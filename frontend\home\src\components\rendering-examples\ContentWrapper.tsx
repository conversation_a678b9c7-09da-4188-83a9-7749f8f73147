'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useTranslation } from '@/components/Providers/i18n-provider';

interface ContentWrapperProps {
  defaultContent: React.ReactNode;
  withDataContent?: React.ReactNode;
  advancedContent?: React.ReactNode;
  useDirectLinks?: boolean; // Add option to use direct links instead of route params
}

// Component to handle route selection with search params
function RouteSelector({
  onRouteChange,
  useDirectLinks: _useDirectLinks,
}: {
  onRouteChange: (route: string) => void;
  useDirectLinks?: boolean;
}) {
  const searchParams = useSearchParams();
  const _router = useRouter();

  useEffect(() => {
    const route = searchParams.get('route');
    if (route === 'with-data') {
      onRouteChange('with-data');
    } else if (route === 'advanced') {
      onRouteChange('advanced');
    } else {
      onRouteChange('basic');
    }
  }, [searchParams, onRouteChange]);

  return null;
}

export function ContentWrapper({
  defaultContent,
  withDataContent,
  advancedContent,
  useDirectLinks: _useDirectLinks = false,
}: ContentWrapperProps) {
  const { t } = useTranslation();
  const [currentRoute, setCurrentRoute] = useState<string>('basic');
  const _router = useRouter();

  // If using direct links, redirect to the appropriate page instead of using params
  useEffect(() => {
    if (_useDirectLinks) {
      if (currentRoute === 'with-data') {
        _router.push('/rendering-examples/ssr/with-data');
      } else if (currentRoute === 'advanced') {
        _router.push('/rendering-examples/ssr/advanced');
      }
    }
  }, [currentRoute, _useDirectLinks, _router]);

  // Render the appropriate content based on the current route
  const renderContent = () => {
    // If using direct links and not on the basic route, don't render content
    // as we'll navigate to the dedicated page
    if (_useDirectLinks && currentRoute !== 'basic') {
      return null;
    }

    switch (currentRoute) {
      case 'with-data':
        return (
          withDataContent || (
            <div className="bg-muted/50 rounded-lg p-6 text-center">
              <h3 className="mb-2 text-lg font-medium">
                {t('rendering.noContent', 'No Content Available')}
              </h3>
              <p>
                {t(
                  'rendering.withDataMissing',
                  'The "With Data" example for this rendering method is not available.',
                )}
              </p>
            </div>
          )
        );
      case 'advanced':
        return (
          advancedContent || (
            <div className="bg-muted/50 rounded-lg p-6 text-center">
              <h3 className="mb-2 text-lg font-medium">
                {t('rendering.noContent', 'No Content Available')}
              </h3>
              <p>
                {t(
                  'rendering.advancedMissing',
                  'The "Advanced" example for this rendering method is not available.',
                )}
              </p>
            </div>
          )
        );
      case 'basic':
      default:
        return defaultContent;
    }
  };

  return (
    <div className="mt-6">
      <Suspense fallback={null}>
        <RouteSelector onRouteChange={setCurrentRoute} useDirectLinks={_useDirectLinks} />
      </Suspense>
      {renderContent()}
    </div>
  );
}
