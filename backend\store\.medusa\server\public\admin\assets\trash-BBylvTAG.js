import{r as n}from"./index-Bwql5Dzz.js";var s=Object.defineProperty,o=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,l=(r,t,e)=>t in r?s(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,v=(r,t)=>{for(var e in t)i.call(t,e)&&l(r,e,t[e]);if(o)for(var e of o(t))f.call(t,e)&&l(r,e,t[e]);return r},_=(r,t)=>{var e={};for(var a in r)i.call(r,a)&&t.indexOf(a)<0&&(e[a]=r[a]);if(r!=null&&o)for(var a of o(r))t.indexOf(a)<0&&f.call(r,a)&&(e[a]=r[a]);return e};const d=n.forwardRef((r,t)=>{var e=r,{color:a="currentColor"}=e,p=_(e,["color"]);return n.createElement("svg",v({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),n.createElement("path",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M1.944 3.278h11.112M5.5 3.278V1.944a.89.89 0 0 1 .889-.888H8.61a.89.89 0 0 1 .889.888v1.334M11.722 5.5v6.667c0 .982-.795 1.777-1.777 1.777h-4.89a1.777 1.777 0 0 1-1.777-1.777V5.5M5.944 7.278v4M9.056 7.278v4"}))});d.displayName="Trash";export{d as T};
