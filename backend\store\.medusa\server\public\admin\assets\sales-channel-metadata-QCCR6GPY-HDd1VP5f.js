import{M as m}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as p,h as d,em as u,j as r}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import{b as h}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var C=()=>{const{id:t}=p(),{sales_channel:a,isPending:e,isError:s,error:o}=d(t),{mutateAsync:n,isPending:i}=u(t);if(s)throw o;return r.jsx(h,{children:r.jsx(m,{isPending:e,isMutating:i,hook:n,metadata:a==null?void 0:a.metadata})})};export{C as Component};
