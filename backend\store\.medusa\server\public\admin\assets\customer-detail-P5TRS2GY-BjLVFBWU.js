import{u as q,a as M,b as H}from"./chunk-ZJRFL6ZN-gHZBUXyR.js";import{u as Q,a as $,b as K}from"./chunk-YG4XFXE6-TB5N21Oh.js";import{ba as R,j as e,R as J,d as U,a as X,dv as Y,q as Z,b as h,u as W,dK as V,H as w,A as T,T as u,r as _,dC as ee,k as G,dL as se,bn as i,L as te,B as re,aR as ae,p as oe,s as ie,t as f,dM as ne}from"./index-Bwql5Dzz.js";import{u as N,_ as O}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as ce}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as k}from"./use-prompt-pbDx0Sfe.js";import{P as z}from"./pencil-square-6wRbnn1C.js";import{T as I}from"./trash-BBylvTAG.js";import{A as le}from"./arrow-path-Bonjx2lP.js";import{C as A}from"./container-Dqi2woPF.js";import{S as de}from"./status-badge-B-sIb9s0.js";import{C as E}from"./checkbox-B4pL6X49.js";import{c as L}from"./index-BxZ1678G.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-WYX5PIA3-DoOUp1ge.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-NNBHHXXN-09hoI4Mn.js";import"./chunk-PDWBYQOW-BedvhUOI.js";import"./chunk-MWVM4TYO-bKUcYSnf.js";import"./chunk-7DXVXBSA-CCclXhoB.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./react-country-flag.esm-BcG425Ss.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var ps=s=>{const{id:t}=s.params||{},{customer:r}=R(t,void 0,{initialData:s.data,enabled:!!t});if(!r)return null;const n=[r.first_name,r.last_name].filter(Boolean).join(" ")||r.email;return e.jsx("span",{children:n})},me=({customer:s})=>{const{t}=h(),r=k(),a=W(),{mutateAsync:n}=V(s.id),l=[s.first_name,s.last_name].filter(Boolean).join(" "),o=s.has_account?"green":"orange",d=s.has_account?t("customers.fields.registered"):t("customers.fields.guest"),p=async()=>{await r({title:t("customers.delete.title"),description:t("customers.delete.description",{email:s.email}),verificationInstruction:t("general.typeToConfirm"),verificationText:s.email,confirmText:t("actions.delete"),cancelText:t("actions.cancel")})&&await n(void 0,{onSuccess:()=>{f.success(t("customers.delete.successToast",{email:s.email})),a("/customers",{replace:!0})},onError:x=>{f.error(x.message)}})};return e.jsxs(A,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(w,{children:s.email}),e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx(de,{color:o,children:d}),e.jsx(T,{groups:[{actions:[{label:t("actions.edit"),icon:e.jsx(z,{}),to:"edit"}]},{actions:[{label:t("actions.delete"),icon:e.jsx(I,{}),onClick:p}]}]})]})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[e.jsx(u,{size:"small",leading:"compact",weight:"plus",children:t("fields.name")}),e.jsx(u,{size:"small",leading:"compact",children:l||"-"})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[e.jsx(u,{size:"small",leading:"compact",weight:"plus",children:t("fields.company")}),e.jsx(u,{size:"small",leading:"compact",children:s.company_name||"-"})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[e.jsx(u,{size:"small",leading:"compact",weight:"plus",children:t("fields.phone")}),e.jsx(u,{size:"small",leading:"compact",children:s.phone||"-"})]})]})},v=10,b="cusgr",ue=({customer:s})=>{const t=k(),[r,a]=_.useState({}),{raw:n,searchParams:l}=q({pageSize:v,prefix:b}),{customer_groups:o,count:d,isLoading:p,isError:g,error:x}=ee({...l,fields:"+customers.id",customers:{id:s.id}},{placeholderData:G}),{mutateAsync:y}=se(s.id),j=M(),P=ge(s.id),{table:B}=N({data:o??[],columns:P,count:d,getRowId:m=>m.id,enablePagination:!0,enableRowSelection:!0,pageSize:v,prefix:b,rowSelection:{state:r,updater:a}}),F=async()=>{const m=Object.keys(r);await t({title:i("general.areYouSure"),description:i("customers.groups.removeMany",{groups:o==null?void 0:o.filter(c=>m.includes(c.id)).map(c=>c.name).join(",")}),confirmText:i("actions.remove"),cancelText:i("actions.cancel")})&&await y({remove:m},{onSuccess:()=>{f.success(i("customers.groups.removed.success",{groups:o.filter(c=>m.includes(c.id)).map(c=>c==null?void 0:c.name)}))},onError:c=>{f.error(c.message)}})};if(g)throw x;return e.jsxs(A,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(w,{level:"h2",children:i("customerGroups.domain")}),e.jsx(te,{to:`/customers/${s.id}/add-customer-groups`,children:e.jsx(re,{variant:"secondary",size:"small",children:i("general.add")})})]}),e.jsx(O,{table:B,columns:P,pageSize:v,isLoading:p,count:d,prefix:b,navigateTo:m=>`/customer-groups/${m.id}`,filters:j,search:!0,pagination:!0,orderBy:[{key:"name",label:i("fields.name")},{key:"created_at",label:i("fields.createdAt")},{key:"updated_at",label:i("fields.updatedAt")}],commands:[{action:F,label:i("actions.remove"),shortcut:"r"}],queryObject:n,noRecords:{message:i("customers.groups.list.noRecordsMessage")}})]})},pe=({group:s,customerId:t})=>{const r=k(),{t:a}=h(),{mutateAsync:n}=ne(s.id),l=async()=>{await r({title:a("general.areYouSure"),description:a("customers.groups.remove",{name:s.name}),confirmText:a("actions.remove"),cancelText:a("actions.cancel")})&&await n([t],{onError:d=>{f.error(d.message)}})};return e.jsx(T,{groups:[{actions:[{label:a("actions.edit"),icon:e.jsx(z,{}),to:`/customer-groups/${s.id}/edit`},{label:a("actions.remove"),onClick:l,icon:e.jsx(I,{})}]}]})},D=L(),ge=s=>{const t=H();return _.useMemo(()=>[D.display({id:"select",header:({table:r})=>e.jsx(E,{checked:r.getIsSomePageRowsSelected()?"indeterminate":r.getIsAllPageRowsSelected(),onCheckedChange:a=>r.toggleAllPageRowsSelected(!!a)}),cell:({row:r})=>e.jsx(E,{checked:r.getIsSelected(),onCheckedChange:a=>r.toggleSelected(!!a),onClick:a=>{a.stopPropagation()}})}),...t,D.display({id:"actions",cell:({row:r})=>e.jsx(pe,{group:r.original,customerId:s})})],[t,s])},C="cusord",S=10,xe="*customer,*items,*sales_channel",fe="id,status,display_id,created_at,email,fulfillment_status,payment_status,total,currency_code",he=({customer:s})=>{const{t}=h(),{searchParams:r,raw:a}=Q({pageSize:S,prefix:C}),{orders:n,count:l,isLoading:o,isError:d,error:p}=ae({customer_id:s.id,fields:fe+","+xe,...r},{placeholderData:G}),g=ve(),x=$(),{table:y}=N({data:n??[],columns:g,enablePagination:!0,count:l,pageSize:S,prefix:C});if(d)throw p;return e.jsxs(A,{className:"divide-y p-0",children:[e.jsx("div",{className:"flex items-center justify-between px-6 py-4",children:e.jsx(w,{level:"h2",children:t("orders.domain")})}),e.jsx(O,{columns:g,table:y,pagination:!0,navigateTo:j=>`/orders/${j.original.id}`,filters:x,count:l,isLoading:o,pageSize:S,orderBy:[{key:"display_id",label:t("orders.fields.displayId")},{key:"created_at",label:t("fields.createdAt")},{key:"updated_at",label:t("fields.updatedAt")}],search:!0,queryObject:a,prefix:C})]})},ye=({order:s})=>{const{t}=h();return e.jsx(T,{groups:[{actions:[{label:t("transferOwnership.label"),to:`${s.id}/transfer`,icon:e.jsx(le,{})}]}]})},je=L(),ve=()=>{const s=K({exclude:["customer"]});return _.useMemo(()=>[...s,je.display({id:"actions",cell:({row:t})=>e.jsx(ye,{order:t.original})})],[s])},gs=()=>{const{id:s}=J(),t=U(),{customer:r,isLoading:a,isError:n,error:l}=R(s,void 0,{initialData:t}),{getWidgets:o}=X();if(a||!r)return e.jsx(Y,{sections:2,showJSON:!0,showMetadata:!0});if(n)throw l;return e.jsxs(ce,{widgets:{before:o("customer.details.before"),after:o("customer.details.after")},data:r,hasOutlet:!0,showJSON:!0,showMetadata:!0,children:[e.jsx(me,{customer:r}),e.jsx(he,{customer:r}),e.jsx(ue,{customer:r})]})},be=s=>({queryKey:oe.detail(s),queryFn:async()=>ie.admin.customer.retrieve(s)}),xs=async({params:s})=>{const t=s.id,r=be(t);return Z.ensureQueryData(r)};export{ps as Breadcrumb,gs as Component,xs as loader};
