'use client';

import * as React from 'react';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useLanguageChange } from '@/hooks/useLanguageChange';
import { cn } from '@/lib/utils';
import { DottedUnderline } from './DottedUnderline';
import { TimeConversionTooltipProps } from './types';

/**
 * TimeConversionTooltip - Display time conversion info on hover
 *
 * Shows local and UTC time information when hovering over dates/times
 * with a subtle dotted underline to indicate interactivity
 *
 * Supports internationalization through i18next and Luxon
 *
 * @example Basic Usage
 * ```tsx
 * <TimeConversionTooltip data={timeData}>
 *   March 15, 2023
 * </TimeConversionTooltip>
 * ```
 *
 * @example With Language Prop
 * ```tsx
 * <TimeConversionTooltip data={timeData} language="fr">
 *   March 15, 2023
 * </TimeConversionTooltip>
 * ```
 *
 * @example With Translation Function
 * ```tsx
 * <TimeConversionTooltip data={timeData}>
 *   {(t) => t('myComponent.date')}
 * </TimeConversionTooltip>
 * ```
 */
export const TimeConversionTooltip = React.forwardRef<HTMLSpanElement, TimeConversionTooltipProps>(
  (
    {
      data,
      children,
      enabled = true,
      className,
      openDelay = 100,
      closeDelay = 100,
      zIndex = 60,
      i18nNamespace = 'common',
      i18nPrefix = 'timeConversion',
      language,
      ...props
    },
    ref,
  ) => {
    const { t, i18n } = useTranslation(i18nNamespace);
    const currentLang = useLanguageChange();
    const [isHovered, setIsHovered] = React.useState(false);

    // Set the language for i18n if specified explicitly, otherwise use current language
    React.useEffect(() => {
      const targetLang = language || currentLang;
      if (targetLang && targetLang !== i18n.language) {
        i18n.changeLanguage(targetLang);
      }
    }, [language, currentLang, i18n]);

    // Render children based on whether it's a function or direct content
    const renderChildren = () => {
      if (typeof children === 'function') {
        // Pass the properly configured translation function that uses the specified namespace
        return children(t);
      }
      return children;
    };

    // If tooltip is disabled, still show the dotted underline on hover
    if (!enabled) {
      return (
        <span
          ref={ref}
          className={cn('relative', className)}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          {...props}
        >
          {renderChildren()}
          <DottedUnderline
            isVisible={isHovered}
            className="text-muted-foreground"
            zIndex={zIndex}
          />
        </span>
      );
    }

    // Get the timezone to display - either from localTimezone or offset
    const displayTimezone = data.localTimezone || data.timezone;

    return (
      <HoverCard openDelay={openDelay} closeDelay={closeDelay}>
        <HoverCardTrigger asChild>
          <span
            ref={ref}
            className={cn('relative inline-block', className)}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            {...props}
          >
            {renderChildren()}
            <DottedUnderline
              isVisible={isHovered}
              className="text-muted-foreground"
              zIndex={zIndex}
            />
          </span>
        </HoverCardTrigger>
        <HoverCardContent
          className="w-auto p-3"
          align="center"
          side="top"
          sideOffset={5}
          style={{ zIndex: zIndex + 1 }}
        >
          <h3 className="text-foreground mb-2 border-b pb-1 text-sm font-medium">
            {t(`${i18nPrefix}.title`, 'Time conversion')}
          </h3>

          <div className="space-y-2">
            <div className="grid grid-cols-[minmax(0,1fr)_auto] items-center gap-3">
              <span
                className="text-muted-foreground truncate text-xs font-medium"
                title={displayTimezone}
              >
                {displayTimezone}
              </span>
              <span
                className="shrink-0 text-right text-xs"
                title={`${data.localTime} ${data.date}`}
              >
                {data.localTime} {data.date}
              </span>
            </div>
            <div className="grid grid-cols-[minmax(0,1fr)_auto] items-center gap-3">
              <span className="text-muted-foreground text-xs font-medium">
                {t(`${i18nPrefix}.utc`, 'UTC')}
              </span>
              <span className="shrink-0 text-right text-xs" title={`${data.utcTime} ${data.date}`}>
                {data.utcTime} {data.date}
              </span>
            </div>
          </div>
        </HoverCardContent>
      </HoverCard>
    );
  },
);

TimeConversionTooltip.displayName = 'TimeConversionTooltip';
