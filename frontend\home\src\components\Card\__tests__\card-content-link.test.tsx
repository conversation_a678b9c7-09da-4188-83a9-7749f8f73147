import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import CardContentLink from '../card-content-link';
import { 
  lightingFixture, 
  minimalFixture,
  noImageFixture,
  noDescriptionFixture,
  primaryImageOnlyFixture,
  secondaryImageOnlyFixture,
} from '../__fixtures__/card-content-link.fixtures';

// Mock Next.js components
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />;
  },
}));

jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ href, children, onClick, ...props }: any) => {
    return (
      <a href={href} onClick={onClick} {...props}>
        {children}
      </a>
    );
  },
}));

describe('CardContentLink', () => {
  it('renders correctly with all props', () => {
    render(<CardContentLink {...lightingFixture} />);
    
    // Check title and description
    expect(screen.getByTestId('card-title')).toHaveTextContent(lightingFixture.title);
    expect(screen.getByTestId('card-description')).toHaveTextContent(lightingFixture.description as string);
    
    // Check links
    const linksList = screen.getByTestId('card-links-list');
    expect(linksList.children.length).toBe(lightingFixture.links.length);
    
    // Check primary image
    const image = screen.getByTestId('card-image');
    expect(image).toHaveAttribute('src', lightingFixture.image?.src);
    expect(image).toHaveAttribute('alt', lightingFixture.image?.alt as string);
    
    // Check secondary image
    const secondaryImage = screen.getByTestId('secondary-image');
    expect(secondaryImage).toHaveAttribute('src', lightingFixture.secondaryImage?.src);
    expect(secondaryImage).toHaveAttribute('alt', lightingFixture.secondaryImage?.alt as string);
  });

  it('renders with minimal props', () => {
    render(<CardContentLink {...minimalFixture} />);
    
    // Check title
    expect(screen.getByTestId('card-title')).toHaveTextContent(minimalFixture.title);
    
    // Check for no description
    expect(screen.queryByTestId('card-description')).not.toBeInTheDocument();
    
    // Check links
    const linksList = screen.getByTestId('card-links-list');
    expect(linksList.children.length).toBe(minimalFixture.links.length);
    
    // Check for no images
    expect(screen.queryByTestId('card-image')).not.toBeInTheDocument();
    expect(screen.queryByTestId('secondary-image')).not.toBeInTheDocument();
  });

  it('renders without description when not provided', () => {
    render(<CardContentLink {...noDescriptionFixture} />);
    
    expect(screen.getByTestId('card-title')).toHaveTextContent(noDescriptionFixture.title);
    expect(screen.queryByTestId('card-description')).not.toBeInTheDocument();
  });

  it('renders without images when not provided', () => {
    render(<CardContentLink {...noImageFixture} />);
    
    expect(screen.queryByTestId('card-image')).not.toBeInTheDocument();
    expect(screen.queryByTestId('card-image-wrapper')).not.toBeInTheDocument();
    expect(screen.queryByTestId('secondary-image')).not.toBeInTheDocument();
    expect(screen.queryByTestId('secondary-image-wrapper')).not.toBeInTheDocument();
  });

  it('renders with only primary image', () => {
    render(<CardContentLink {...primaryImageOnlyFixture} />);
    
    expect(screen.getByTestId('card-image')).toBeInTheDocument();
    expect(screen.queryByTestId('secondary-image')).not.toBeInTheDocument();
  });

  it('renders with only secondary image', () => {
    render(<CardContentLink {...secondaryImageOnlyFixture} />);
    
    expect(screen.queryByTestId('card-image')).not.toBeInTheDocument();
    expect(screen.getByTestId('secondary-image')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<CardContentLink {...lightingFixture} className="custom-test-class" />);
    expect(screen.getByTestId('card-content-link')).toHaveClass('custom-test-class');
  });

  it('calls onClick handler when a link is clicked', () => {
    const onClickMock = jest.fn();
    render(<CardContentLink {...lightingFixture} onClick={onClickMock} />);
    
    const firstLink = screen.getByTestId('card-link-0');
    fireEvent.click(firstLink);
    
    expect(onClickMock).toHaveBeenCalledTimes(1);
    expect(onClickMock).toHaveBeenCalledWith(lightingFixture.links[0].href);
  });

  it('has the correct number of links', () => {
    render(<CardContentLink {...lightingFixture} />);
    
    lightingFixture.links.forEach((link: { label: string; href: string }, index: number) => {
      const linkElement = screen.getByTestId(`card-link-${index}`);
      expect(linkElement).toHaveTextContent(link.label);
      expect(linkElement).toHaveAttribute('href', link.href);
    });
  });

  it('renders links with correct href attributes', () => {
    render(<CardContentLink {...lightingFixture} />);
    
    lightingFixture.links.forEach((link: { label: string; href: string }, index: number) => {
      const linkElement = screen.getByTestId(`card-link-${index}`);
      expect(linkElement).toHaveAttribute('href', link.href);
    });
  });

  it('has accessible elements', () => {
    render(<CardContentLink {...lightingFixture} />);
    
    // Images should have alt text
    const primaryImage = screen.getByTestId('card-image');
    expect(primaryImage).toHaveAttribute('alt', lightingFixture.image?.alt as string);
    
    const secondaryImage = screen.getByTestId('secondary-image');
    expect(secondaryImage).toHaveAttribute('alt', lightingFixture.secondaryImage?.alt as string);
    
    // Links should be accessible
    lightingFixture.links.forEach((_: any, index: number) => {
      const link = screen.getByTestId(`card-link-${index}`);
      expect(link).toHaveAttribute('href');
      expect(link.textContent).not.toBe('');
    });
  });

  it('has the correct layout structure', () => {
    render(<CardContentLink {...lightingFixture} />);
    
    // Card should have rounded corners and padding
    const card = screen.getByTestId('card-content-link');
    expect(card).toHaveClass('rounded-xl');
    expect(card).toHaveClass('p-6');
    
    // Content should be in a flex container
    const contentSection = screen.getByTestId('card-links-list').closest('div')?.parentElement;
    expect(contentSection).not.toBeNull();
    expect(contentSection).toHaveClass('flex');
    
    // Links section should take half width
    const linksSection = screen.getByTestId('card-links-list').parentElement;
    expect(linksSection).toHaveClass('w-1/2');
    
    // Image section should take half width
    const imageSection = screen.getByTestId('card-image-wrapper');
    expect(imageSection).toHaveClass('w-1/2');
    
    // Secondary image should be at the bottom
    const secondaryImageSection = screen.getByTestId('secondary-image-wrapper');
    expect(secondaryImageSection).toHaveClass('mt-6');
  });
});