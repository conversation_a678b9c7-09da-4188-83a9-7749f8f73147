import * as React from 'react';

/**
 * Animation configuration
 */
export const ANIMATION_CONFIG = {
  defaultDelay: 0.05,
  staggerChildren: 0.03,
  duration: 0.3,
  variants: {
    container: {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { staggerChildren: 0.03 } },
    },
    item: {
      hidden: { opacity: 0, y: 10 },
      visible: { opacity: 1, y: 0 },
    },
  },
};

/**
 * Virtualization configuration
 */
export const VIRTUALIZATION_CONFIG = {
  defaultHeight: 500,
  defaultWidth: '100%',
  defaultOverscan: 10,
  scrollToIndexBehavior: 'auto' as const,
};

/**
 * Row heights for different size variants
 */
export const ROW_HEIGHTS = {
  sm: 40,
  md: 48,
  lg: 56,
};

/**
 * Header heights for different size variants
 */
export const HEADER_HEIGHTS = {
  sm: 44,
  md: 52,
  lg: 60,
};

/**
 * Footer heights for different size variants
 */
export const FOOTER_HEIGHTS = {
  sm: 44,
  md: 52,
  lg: 60,
};

/**
 * Default pagination configuration
 */
export const DEFAULT_PAGINATION = {
  enabled: true,
  pageSizeOptions: [10, 20, 30, 50, 100],
  initialPageSize: 10,
  initialPageIndex: 0,
  showPageSizeSelector: true,
  showPageNavigator: true,
  showPageInfo: true,
};

/**
 * Default sorting configuration
 */
export const DEFAULT_SORTING = {
  enabled: true,
  maxSortColumns: 3,
  defaultSortDirection: 'asc' as const,
};

/**
 * Default filtering configuration
 */
export const DEFAULT_FILTERING = {
  enabled: true,
  globalFilterMode: 'contains' as const,
  showGlobalFilter: true,
  debounce: true,
  debounceTime: 300,
};

/**
 * Default column visibility configuration
 */
export const DEFAULT_COLUMN_VISIBILITY = {
  enabled: true,
  showToggle: true,
};

/**
 * Default header configuration
 */
export const DEFAULT_HEADER = {
  show: true,
  sticky: true,
  showColumnSeparators: true,
  height: undefined as number | string | undefined,
};

/**
 * Default footer configuration
 */
export const DEFAULT_FOOTER = {
  show: true,
  sticky: false,
  showColumnSeparators: true,
  showSummary: false,
  height: undefined as number | string | undefined,
  footerContent: undefined as React.ReactNode | undefined,
};

/**
 * Default row selection configuration
 */
export const DEFAULT_ROW_SELECTION = {
  enabled: false,
  mode: 'multi' as const,
  preserveSelection: true,
};

/**
 * Default row actions configuration
 */
export const DEFAULT_ROW_ACTIONS = {
  enabled: false,
  position: 'end' as const,
  width: 100,
};

/**
 * Default row expansion configuration
 */
export const DEFAULT_ROW_EXPANSION = {
  enabled: false,
  allowMultipleExpanded: true,
};

/**
 * Default highlighting configuration
 */
export const DEFAULT_HIGHLIGHTING = {
  hoverHighlight: true,
  alternateRowHighlight: false,
};

/**
 * Default export configuration
 */
export const DEFAULT_EXPORT = {
  enableCSV: false,
  enableExcel: false,
  enablePDF: false,
};

/**
 * Default column configuration
 */
export const DEFAULT_COLUMN_CONFIG = {
  enableSorting: true,
  enableFiltering: true,
  sticky: false,
  hidden: false,
  enableResizing: true,
  truncate: false,
};
