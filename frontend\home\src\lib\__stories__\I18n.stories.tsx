'use client';

import React, { useState, useEffect } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { expect, userEvent, within } from '@storybook/test';
import { i18n, SupportedLanguage, languageCodes, supportedLngs } from '../index';
import { useTranslation } from '@/components/Providers/i18n-provider';

/**
 * I18n Demonstration Component
 * 
 * Shows how to use the i18n system with examples for both client-side 
 * and server-side usage.
 */
const I18nDemoComponent = ({
  showHelp = false,
  initialLanguage = 'en' as SupportedLanguage,
}: {
  showHelp?: boolean;
  initialLanguage?: SupportedLanguage;
}) => {
  const { t, i18n: clientI18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(initialLanguage);
  const [rtl, setRtl] = useState(false);
  
  // Update language when dropdown changes
  const handleLanguageChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLang = e.target.value as SupportedLanguage;
    await clientI18n.changeLanguage(newLang);
    setCurrentLanguage(newLang);
  };
  
  // Update RTL status when language changes
  useEffect(() => {
    setRtl(supportedLngs[currentLanguage]?.rtl || false);
  }, [currentLanguage]);
  
  return (
    <div className="space-y-8 max-w-xl p-6 bg-card rounded-lg shadow" dir={rtl ? 'rtl' : 'ltr'}>
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">
          {t('i18n.demo.title')}
        </h1>
        
        <div className="flex items-center gap-2">
          <label htmlFor="language-selector" className="text-sm font-medium">
            {t('i18n.demo.language')}:
          </label>
          <select
            id="language-selector"
            value={currentLanguage}
            onChange={handleLanguageChange}
            className="border rounded p-1 text-sm"
          >
            {languageCodes.map(lang => (
              <option key={lang} value={lang}>
                {t(`i18n.languages.${lang}`)} {supportedLngs[lang].rtl ? '(RTL)' : ''}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      <div className="space-y-4 border-t pt-4">
        <h2 className="text-xl font-semibold">
          {t('i18n.demo.translatedContent')}
        </h2>
        
        <div className="space-y-2">
          <p>{t('i18n.demo.greeting')}</p>
          <p>{t('i18n.demo.welcome')}</p>
          <p>{t('i18n.demo.date', { date: new Date() })}</p>
          <p>{t('i18n.demo.number', { number: 1234.56 })}</p>
        </div>
        
        <div className="flex flex-wrap gap-2 mt-4">
          <button className="px-3 py-1 bg-primary text-primary-foreground rounded">
            {t('i18n.demo.buttons.submit')}
          </button>
          <button className="px-3 py-1 bg-secondary text-secondary-foreground rounded">
            {t('i18n.demo.buttons.cancel')}
          </button>
          <button className="px-3 py-1 bg-destructive text-destructive-foreground rounded">
            {t('i18n.demo.buttons.delete')}
          </button>
        </div>
      </div>
      
      <div className="space-y-4 border-t pt-4">
        <h2 className="text-xl font-semibold">
          {t('i18n.demo.rtlSupport')}
        </h2>
        
        <div className="bg-muted p-3 rounded">
          <p>{t('i18n.demo.rtlDescription')}</p>
          <p className="mt-2">{rtl ? t('i18n.demo.rtlEnabled') : t('i18n.demo.rtlDisabled')}</p>
        </div>
      </div>
      
      {showHelp && (
        <div className="space-y-4 border-t pt-4">
          <h2 className="text-xl font-semibold">
            {t('i18n.demo.usage')}
          </h2>
          
          <div className="space-y-2">
            <h3 className="text-lg font-medium">{t('i18n.demo.clientUsage')}</h3>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {`import { useTranslation } from '@/components/Providers/i18n-provider';

function MyComponent() {
  const { t } = useTranslation();
  
  return <p>{t('my.translation.key')}</p>;
}`}
            </pre>
            
            <h3 className="text-lg font-medium mt-4">{t('i18n.demo.serverUsage')}</h3>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
              {`import { createTranslation } from '@/components/Providers/server-i18n';

export default async function ServerComponent() {
  const { t } = await createTranslation('common');
  
  return <p>{t('my.translation.key')}</p>;
}`}
            </pre>
          </div>
        </div>
      )}
      
      <div className="text-xs text-muted-foreground pt-4 border-t">
        {t('i18n.demo.currentLanguage')}: {currentLanguage} 
        <span className="ml-2">{rtl ? '(RTL)' : '(LTR)'}</span>
      </div>
    </div>
  );
};

// Storybook configuration
const meta: Meta<typeof I18nDemoComponent> = {
  title: 'Core/Internationalization',
  component: I18nDemoComponent,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Internationalization (i18n) system demo showcasing language switching, RTL support, and usage examples for both client and server components.'
      }
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#FFFFFF' },
        { name: 'dark', value: '#1F2937' },
      ],
    },
    badges: ['stable', 'tested', 'accessible'],
    a11y: { disable: false },
  },
  argTypes: {
    showHelp: {
      control: 'boolean',
      description: 'Show usage examples',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: false },
      },
    },
    initialLanguage: {
      control: 'select',
      options: languageCodes,
      description: 'Initial language to display',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'en' },
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="story-wrapper">
        <Story />
      </div>
    ),
  ],
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof I18nDemoComponent>;

// Default Story
export const Default: Story = {
  args: {
    showHelp: false,
    initialLanguage: 'en' as SupportedLanguage,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Verify title is rendered
    await expect(canvas.getByText(/i18n demo/i)).toBeInTheDocument();
    
    // Verify the language selector is present
    const selector = canvas.getByLabelText(/language/i);
    await expect(selector).toBeInTheDocument();
  },
};

// Usage Examples Story
export const WithUsageExamples: Story = {
  args: {
    showHelp: true,
    initialLanguage: 'en' as SupportedLanguage,
  },
};

// RTL Language Story
export const RTLLanguage: Story = {
  args: {
    showHelp: false,
    initialLanguage: 'ar' as SupportedLanguage,
  },
};

// Language Switching Test
export const LanguageSwitching: Story = {
  args: {
    showHelp: false,
    initialLanguage: 'en' as SupportedLanguage,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const user = userEvent.setup();
    
    // Find and click the language selector
    const selector = canvas.getByLabelText(/language/i);
    await user.click(selector);
    
    // Select French
    const frenchOption = canvas.getByRole('option', { name: /french|français/i });
    await user.click(frenchOption);
    
    // Verify language changed
    await expect(canvas.getByText(/langue actuelle/i)).toBeInTheDocument();
  },
};

// Individual Language Stories
export const English: Story = {
  args: {
    initialLanguage: 'en' as SupportedLanguage,
  },
  parameters: {
    locale: 'en',
  },
};

export const French: Story = {
  args: {
    initialLanguage: 'fr' as SupportedLanguage,
  },
  parameters: {
    locale: 'fr',
  },
};

export const Japanese: Story = {
  args: {
    initialLanguage: 'ja' as SupportedLanguage,
  },
  parameters: {
    locale: 'ja',
  },
};

export const Arabic: Story = {
  args: {
    initialLanguage: 'ar' as SupportedLanguage,
  },
  parameters: {
    locale: 'ar',
    direction: 'rtl',
  },
}; 