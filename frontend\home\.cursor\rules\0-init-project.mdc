---
description: 
globs: 
alwaysApply: false
---
1. Install dependencies using PNPM:
   - Execute `pnpm install` to ensure all necessary packages are installed.

2. Update all package versions to the latest:
   - Run `pnpm update --latest` to upgrade dependencies to their latest available versions.
   - Confirm that the `package.json` file reflects these updated versions accurately.

3. Implement enterprise-grade Cursor.ai indexing:
   - Detect and analyze the existing project structure.
   - Create a robust `.cursorignore` with comprehensive rules:
     ```
     # Dependencies & Package Managers
     node_modules/
     vendor/
     .pnpm-store/
     package-lock.json
     pnpm-lock.yaml
     yarn.lock

     # Build & Artifacts
     dist/
     build/
     out/
     coverage/
     .cache/
     temp/
     tmp/
     *.log
     logs/

     # IDE & System Files
     .idea/
     .vscode/
     .DS_Store
     *.log

     # Environment & Sensitive Files
     .env
     .env.*
     *.pem
     *.key

     # Version Control & Metadata
     .git/
     .github/
     .gitlab-ci.yml

     # Testing & Coverage
     __tests__/
     __mocks__/
     coverage/
     jest.config.*
     *.test.*
     *.spec.*

     # Deployment & Infrastructure
     Dockerfile*
     docker-compose.*
     terraform.tfstate*
     .terraform/
     serverless.yml
     terraform/
     infra/

     # Monorepo Management
     lerna.json
     turbo.json
     nx.json

     # IDE & System Files
     *.log
     .DS_Store
     .idea/
     .vscode/

     # Cursor-specific
     .cursorignore
     .cursorindexingignore
     *.ai-autosave
     ```
   - Ensure `.cursorignore` is included in your global `.gitignore` to prevent accidental commits.
   - Validate indexing setup via Cursor Settings (`Cursor Settings > Features > Codebase Indexing`).

4. Update all package versions to latest:
   - Execute `pnpm update --latest`.
   - Confirm all changes in `package.json`.

5. Automated validation & recursive testing (Cursor.ai-managed):
   - **Linting (`pnpm run lint`)**: Cursor.ai runs recursively, automatically resolving lint errors until no errors remain.
   - **Development (`pnpm run dev`)**: Cursor.ai verifies the development server starts correctly and runs error-free.
   - **Build (`pnpm run build`)**: Cursor.ai recursively fixes build issues, re-running until a successful build is achieved.
   - Cursor.ai automatically analyzes logs, diagnoses errors, suggests fixes, and retries processes as necessary.

6. Ensure **Cursor indexing validation**:
   - Automatically verify that Cursor indexes only source code, strictly respecting `.cursorignore`.
   - Confirm indexing settings in **Cursor Settings > Features > Codebase Indexing**.

7. Generate detailed logs for each step:
   - Provide real-time logging and tracking to identify and troubleshoot potential issues quickly.

8. Automatic diagnostics:
   - If any step repeatedly fails, provide detailed error analysis, suggestions for manual fixes, or escalation steps for debugging.

9. Enterprise-grade validation:
   - Ensure scalability and performance by consistently validating and optimizing the Cursor indexing strategy, particularly suited for large monorepos and enterprise environments.
