import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { FilterTagGroup } from '../filter-tag-group';
import { sampleTags } from '../__fixtures__/filter-tag-group.fixtures';

describe('FilterTagGroup', () => {
  const mockOnRemoveTag = jest.fn();
  const mockOnClearAll = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with tags', () => {
    render(
      <FilterTagGroup 
        tags={sampleTags.slice(0, 2)}
        onRemoveTag={mockOnRemoveTag}
        onClearAll={mockOnClearAll}
      />
    );

    // Check if all tags are rendered
    expect(screen.getByText(/หลอดไฟ : LED/i)).toBeInTheDocument();
    expect(screen.getByText(/แบรนด์ : Philips/i)).toBeInTheDocument();
    
    // Check if clear all button is rendered
    expect(screen.getByText('ล้างค่า')).toBeInTheDocument();
  });

  it('does not render when there are no tags and no emptyStateComponent', () => {
    const { container } = render(
      <FilterTagGroup 
        tags={[]}
        onRemoveTag={mockOnRemoveTag}
        onClearAll={mockOnClearAll}
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  it('renders emptyStateComponent when there are no tags', () => {
    render(
      <FilterTagGroup 
        tags={[]}
        onRemoveTag={mockOnRemoveTag}
        onClearAll={mockOnClearAll}
        emptyStateComponent={<div data-testid="empty-state">No filters selected</div>}
      />
    );
    
    expect(screen.getByTestId('empty-state')).toBeInTheDocument();
  });

  it('calls onRemoveTag when tag remove button is clicked', () => {
    render(
      <FilterTagGroup 
        tags={sampleTags.slice(0, 2)}
        onRemoveTag={mockOnRemoveTag}
        onClearAll={mockOnClearAll}
      />
    );
    
    // Find and click the remove button for the first tag
    const removeButtons = screen.getAllByRole('button', { name: /ลบตัวกรอง/i });
    fireEvent.click(removeButtons[0]);
    
    expect(mockOnRemoveTag).toHaveBeenCalledWith(sampleTags[0]);
  });

  it('calls onClearAll when clear all button is clicked', () => {
    render(
      <FilterTagGroup 
        tags={sampleTags.slice(0, 2)}
        onRemoveTag={mockOnRemoveTag}
        onClearAll={mockOnClearAll}
      />
    );
    
    const clearAllButton = screen.getByText('ล้างค่า');
    fireEvent.click(clearAllButton);
    
    expect(mockOnClearAll).toHaveBeenCalled();
  });

  it('does not show clear all button when showClearAll is false', () => {
    render(
      <FilterTagGroup 
        tags={sampleTags.slice(0, 2)}
        onRemoveTag={mockOnRemoveTag}
        onClearAll={mockOnClearAll}
        showClearAll={false}
      />
    );
    
    expect(screen.queryByText('ล้างค่า')).not.toBeInTheDocument();
  });

  it('applies custom className and tagClassName', () => {
    render(
      <FilterTagGroup 
        tags={sampleTags.slice(0, 2)}
        onRemoveTag={mockOnRemoveTag}
        onClearAll={mockOnClearAll}
        className="custom-container-class"
        tagClassName="custom-tag-class"
      />
    );
    
    expect(screen.getByRole('region')).toHaveClass('custom-container-class');
    
    const tagElements = screen.getAllByRole('status');
    expect(tagElements[0]).toHaveClass('custom-tag-class');
  });

  it('has proper accessibility attributes', () => {
    render(
      <FilterTagGroup 
        tags={sampleTags.slice(0, 2)}
        onRemoveTag={mockOnRemoveTag}
        onClearAll={mockOnClearAll}
        ariaLabel="Selected filters"
      />
    );
    
    expect(screen.getByRole('region')).toHaveAttribute('aria-label', 'Selected filters');
    
    const removeButtons = screen.getAllByRole('button', { name: /ลบตัวกรอง/i });
    expect(removeButtons[0]).toHaveAccessibleName(/ลบตัวกรอง LED/i);
    
    const clearAllButton = screen.getByText('ล้างค่า');
    expect(clearAllButton).toHaveAccessibleName('ลบตัวกรองทั้งหมด');
  });
});