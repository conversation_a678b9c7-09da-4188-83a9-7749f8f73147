import React from "react";
import { Star } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

export interface RatingDistribution {
  5: number;
  4: number;
  3: number;
  2: number;
  1: number;
}

export interface ProductSummaryRatingProps {
  /**
   * คะแนนเฉลี่ยทั้งหมด
   */
  averageRating: number;
  /**
   * จำนวนรีวิวทั้งหมด
   */
  totalReviews: number;
  /**
   * การกระจายของคะแนนตามจำนวนดาว
   */
  ratingDistribution: RatingDistribution;
  /**
   * รูปแบบการแสดงผล
   */
  variant?: "default" | "compact" | "detailed";
  /**
   * เรียกเมื่อมีการคลิกที่ระดับคะแนน
   */
  onRatingClick?: (rating: number) => void;
  /**
   * CSS class เพิ่มเติม
   */
  className?: string;
  /**
   * สถานะกำลังโหลดข้อมูล
   */
  isLoading?: boolean;
}

export const ProductSummaryRating = ({
  averageRating = 0,
  totalReviews = 0,
  ratingDistribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
  variant = "default",
  onRatingClick,
  className,
  isLoading = false,
}: ProductSummaryRatingProps) => {
  // คำนวณเปอร์เซ็นต์สำหรับแต่ละระดับคะแนน
  const getPercentage = (count: number) => {
    if (totalReviews === 0) return 0;
    return Math.round((count / totalReviews) * 100);
  };

  // คำนวณจำนวนดาวที่จะแสดงใน UI
  const calculateStars = (rating: number, totalStars: number = 5, size: "sm" | "md" | "lg" = "sm") => {
    const stars = [];
    const sizeClass = {
      sm: "w-5 h-5", // สำหรับด้านขวา
      md: "w-6 h-6",
      lg: "w-8 h-8", // สำหรับด้านซ้าย
    }[size];
    
    for (let i = 1; i <= totalStars; i++) {
      stars.push(
        <Star 
          key={i} 
          className={cn(
            sizeClass,
            i <= rating ? "text-yellow-400 fill-yellow-400" : "text-gray-200"
          )} 
        />
      );
    }
    return stars;
  };

  // สร้าง CSS classes ตาม props
  const containerClasses = cn(
    "w-full",
    variant === "compact" ? "gap-2" : "gap-6",
    className
  );

  // ถ้าอยู่ในสถานะโหลด
  if (isLoading) {
    return (
      <div className={containerClasses} data-testid="loading-placeholder">
        <div className="flex flex-col md:flex-row gap-4 animate-pulse">
          {/* ส่วนซ้าย - คะแนนเฉลี่ย (Loading) */}
          <div className="flex flex-col items-center md:w-60">
            <div className="text-center">
              <div className="flex items-baseline justify-center">
                <div className="h-12 w-16 bg-gray-200 rounded"></div>
                <div className="h-12 w-16 bg-gray-200 rounded ml-1"></div>
              </div>
              <div className="flex justify-center my-3 gap-1">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="w-8 h-8 bg-gray-200 rounded-full"></div>
                ))}
              </div>
              <div className="h-4 w-20 bg-gray-200 rounded mx-auto mt-2"></div>
            </div>
          </div>
          
          {/* ส่วนขวา - การกระจายของคะแนน (Loading) */}
          <div className="flex-1">
            {[5, 4, 3, 2, 1].map((rating) => (
              <div key={rating} className="flex items-center gap-3 mb-3">
                <div className="w-8 h-6 bg-gray-200 rounded"></div>
                <div className="flex gap-1">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="w-5 h-5 bg-gray-200 rounded-full"></div>
                  ))}
                </div>
                <div className="h-2 w-full max-w-[200px] bg-gray-200 rounded-full mx-4"></div>
                <div className="w-8 h-6 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // ถ้าไม่มีรีวิว
  if (totalReviews === 0) {
    return (
      <div className={containerClasses}>
        <div className="text-center">
          <p className="text-lg font-medium">No reviews yet</p>
          <p className="text-sm text-gray-500">Be the first to review this product</p>
        </div>
      </div>
    );
  }

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center md:flex-row gap-2">
        {/* ส่วนซ้าย - คะแนนเฉลี่ย */}
        <div className="flex flex-col items-center md:w-60">
          <div className="text-center">
            <div className="flex items-baseline justify-center text-5xl">
              <span className="font-bold leading-none">{averageRating.toFixed(1)}</span>
              <span className="text-gray-300 ml-1">/5</span>
            </div>
            <div className="flex justify-center my-3">
              {calculateStars(Math.round(averageRating), 5, "lg").map((star, index) => (
                <span key={index} className="mx-1">{star}</span>
              ))}
            </div>
            <div className="text-gray-500 mt-1">{totalReviews} รีวิว</div>
          </div>
        </div>
        
        {/* ส่วนขวา - การกระจายของคะแนน */}
        <div className="flex-1">
          {[5, 4, 3, 2, 1].map((rating) => {
            const count = ratingDistribution[rating as keyof RatingDistribution];
            const percentage = getPercentage(count);
            
            return (
              <div 
                key={rating}
                className={cn(
                  "flex items-center gap-3 mb-3", 
                  onRatingClick && "cursor-pointer hover:bg-gray-50"
                )}
                onClick={() => onRatingClick && onRatingClick(rating)}
                role={onRatingClick ? "button" : "presentation"}
                aria-label={onRatingClick ? `Filter by ${rating} star reviews` : undefined}
              >
                {/* จำนวนดาว */}
                <div className="w-8 text-center font-medium">
                  {rating}
                </div>
                
                {/* แสดงดาวตามจำนวน */}
                <div className="flex items-center">
                  {calculateStars(rating, 5, "sm").map((star, index) => (
                    <span key={index}>{star}</span>
                  ))}
                </div>
                
                {/* Progress bar - แก้ไขส่วนนี้ */}
                <Progress 
                  value={percentage} 
                  className="h-2 w-full min-w-[60px] max-w-[120px] md:max-w-[240px] mx-4 bg-gray-200 [&>div]:bg-yellow-400" 
                  aria-label={`${percentage}% of reviews have ${rating} stars`}
                />
                
                {/* จำนวนรีวิว */}
                <div className="min-w-[30px] text-right">
                  <span className={cn(
                    "text-gray-500",
                    count > 0 && "text-gray-500"
                  )}>
                    {count}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};