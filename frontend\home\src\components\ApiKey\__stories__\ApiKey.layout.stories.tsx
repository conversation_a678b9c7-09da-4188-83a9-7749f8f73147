import type { Meta, StoryObj } from '@storybook/react';
import { expect, within } from '@storybook/test';
import { Create, Edit } from '..';
import { sampleProjects, sampleApiKeyRestricted } from '../__fixtures__/ApiKey.fixtures';

const meta = {
  title: 'Components/ApiKey/Layout',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story:
          'Demonstrates how API Key components appear in different layout contexts and real-world usage scenarios.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-gray-100 p-4">
        <Story />
      </div>
    ),
  ],
  tags: ['layout', 'integration'],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Demonstrates the Create API Key component in a dashboard layout.
 */
export const CreateInDashboard: Story = {
  render: () => (
    <div className="mx-auto max-w-7xl">
      <div className="rounded-lg bg-white shadow-sm">
        {/* Dashboard header */}
        <div className="border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold">API Key Management</h1>
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground text-sm">Project: ZoomThai</span>
              <span
                className="inline-block h-2 w-2 rounded-full bg-green-500"
                aria-hidden="true"
              ></span>
            </div>
          </div>
        </div>

        {/* Dashboard content */}
        <div className="p-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <nav className="space-y-1">
                <a
                  href="#"
                  className="bg-primary text-primary-foreground block rounded-md px-3 py-2 font-medium"
                >
                  API Keys
                </a>
                <a
                  href="#"
                  className="text-muted-foreground hover:bg-accent hover:text-accent-foreground block rounded-md px-3 py-2"
                >
                  Usage & Quotas
                </a>
                <a
                  href="#"
                  className="text-muted-foreground hover:bg-accent hover:text-accent-foreground block rounded-md px-3 py-2"
                >
                  Permissions
                </a>
                <a
                  href="#"
                  className="text-muted-foreground hover:bg-accent hover:text-accent-foreground block rounded-md px-3 py-2"
                >
                  Audit Logs
                </a>
              </nav>
            </div>

            {/* Main content */}
            <div className="lg:col-span-3">
              <div className="bg-background border-border rounded-lg border p-4">
                <h2 className="mb-4 text-lg font-medium">Create New API Key</h2>
                <Create
                  onSubmit={() => {}}
                  onCancel={() => {}}
                  availableProjects={sampleProjects}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByText('Create new secret key')).toBeInTheDocument();
  },
};

/**
 * Demonstrates the Edit API Key component in a modal dialog layout.
 */
export const EditInModal: Story = {
  render: () => (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-background w-full max-w-xl rounded-lg p-6 shadow-lg">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold">Edit API Key</h2>
          <button className="text-muted-foreground hover:text-foreground">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />
      </div>
    </div>
  ),
};

/**
 * Shows the Create component in a wizard/stepper layout.
 */
export const CreateInWizard: Story = {
  render: () => (
    <div className="mx-auto max-w-4xl overflow-hidden rounded-lg bg-white shadow-lg">
      <div className="border-b border-gray-200 px-6 py-4">
        <h1 className="text-xl font-semibold">Create Project Access</h1>
      </div>

      {/* Stepper */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-col items-center">
            <div className="bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full font-medium">
              1
            </div>
            <span className="mt-1 text-xs">Project</span>
          </div>
          <div className="bg-primary h-1 w-12"></div>
          <div className="flex flex-col items-center">
            <div className="bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full font-medium">
              2
            </div>
            <span className="mt-1 text-xs">API Key</span>
          </div>
          <div className="h-1 w-12 bg-gray-300"></div>
          <div className="flex flex-col items-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 font-medium text-gray-500">
              3
            </div>
            <span className="mt-1 text-xs">Permissions</span>
          </div>
          <div className="h-1 w-12 bg-gray-300"></div>
          <div className="flex flex-col items-center">
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200 font-medium text-gray-500">
              4
            </div>
            <span className="mt-1 text-xs">Summary</span>
          </div>
        </div>
      </div>

      {/* Create API Key step */}
      <div className="p-6">
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />

        {/* Wizard navigation */}
        <div className="mt-6 flex justify-between">
          <button className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium">
            Back
          </button>
          <button className="bg-primary text-primary-foreground rounded-md px-4 py-2 text-sm font-medium">
            Next
          </button>
        </div>
      </div>
    </div>
  ),
};

/**
 * Shows both Create and Edit components in a tabbed interface.
 */
export const ApiKeysInTabbedInterface: Story = {
  render: () => (
    <div className="mx-auto max-w-4xl overflow-hidden rounded-lg bg-white shadow-lg">
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex">
          <a href="#" className="text-primary-foreground bg-primary px-6 py-3 text-sm font-medium">
            Manage API Keys
          </a>
          <a
            href="#"
            className="text-muted-foreground hover:text-foreground px-6 py-3 text-sm font-medium"
          >
            Usage Statistics
          </a>
          <a
            href="#"
            className="text-muted-foreground hover:text-foreground px-6 py-3 text-sm font-medium"
          >
            Settings
          </a>
        </nav>
      </div>

      <div className="p-6">
        {/* Tab content */}
        <div className="mb-4 border-b border-gray-200 pb-4">
          <h2 className="text-lg font-medium">Your API Keys</h2>
          <p className="text-muted-foreground mt-1 text-sm">
            Manage the API keys you use to access our services.
          </p>
        </div>

        {/* Sub-tabs for API key operations */}
        <div className="mb-4 border-b border-gray-200">
          <nav className="-mb-px flex">
            <a
              href="#"
              className="border-primary text-primary border-b-2 px-4 py-2 text-sm font-medium"
            >
              Create New
            </a>
            <a
              href="#"
              className="text-muted-foreground hover:text-foreground border-b-2 border-transparent px-4 py-2 text-sm font-medium"
            >
              Edit Existing
            </a>
            <a
              href="#"
              className="text-muted-foreground hover:text-foreground border-b-2 border-transparent px-4 py-2 text-sm font-medium"
            >
              Revoke Keys
            </a>
          </nav>
        </div>

        {/* API Key component */}
        <div className="rounded-md bg-gray-50 p-4">
          <Create
            onSubmit={() => {}}
            onCancel={() => {}}
            availableProjects={sampleProjects.slice(0, 3)}
          />
        </div>
      </div>
    </div>
  ),
};

/**
 * Shows the API Key component in a responsive sidebar drawer pattern.
 */
export const ApiKeyInSidebarDrawer: Story = {
  render: () => (
    <div className="mx-auto max-w-7xl">
      <div className="flex">
        {/* Main content */}
        <div className="min-w-0 flex-1 bg-white p-4">
          <h1 className="mb-4 text-xl font-semibold">Dashboard</h1>
          <p className="text-muted-foreground">
            Select &quot;Create API Key&quot; from the sidebar to get started.
          </p>

          {/* Placeholder content */}
          <div className="mt-6 grid grid-cols-1 gap-4 md:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="flex h-40 items-center justify-center rounded-md bg-gray-50 p-4"
              >
                Dashboard Widget {i}
              </div>
            ))}
          </div>
        </div>

        {/* Sidebar drawer */}
        <div className="hidden w-96 border-l border-gray-200 bg-white lg:block">
          <div className="border-b border-gray-200 p-4">
            <h2 className="text-lg font-medium">Create API Key</h2>
            <p className="text-muted-foreground mt-1 text-sm">
              Generate a new API key for your application.
            </p>
          </div>
          <div className="p-4">
            <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
          </div>
        </div>
      </div>
    </div>
  ),
};

/**
 * Shows the Edit API Key component in a nested form context.
 */
export const EditInNestedForm: Story = {
  render: () => (
    <div className="mx-auto max-w-4xl overflow-hidden rounded-lg bg-white shadow-lg">
      <div className="border-b border-gray-200 px-6 py-4">
        <h1 className="text-xl font-semibold">Project Configuration</h1>
      </div>

      <div className="p-6">
        <form>
          {/* Project section */}
          <fieldset className="mb-8">
            <legend className="mb-4 text-lg font-medium">1. Project Details</legend>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-1 block text-sm font-medium">Project Name</label>
                <input
                  type="text"
                  className="w-full rounded-md border border-gray-300 px-3 py-2"
                  defaultValue="ZoomThai"
                />
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium">Organization</label>
                <select className="w-full rounded-md border border-gray-300 px-3 py-2">
                  <option>My Organization</option>
                  <option>Other Organization</option>
                </select>
              </div>
              <div className="md:col-span-2">
                <label className="mb-1 block text-sm font-medium">Description</label>
                <textarea
                  className="w-full rounded-md border border-gray-300 px-3 py-2"
                  rows={3}
                  defaultValue="Thai language model fine-tuning project"
                ></textarea>
              </div>
            </div>
          </fieldset>

          {/* API Key section */}
          <fieldset className="mb-8 border-t border-gray-200 pt-8">
            <legend className="mb-4 text-lg font-medium">2. API Key Configuration</legend>
            <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />
          </fieldset>

          {/* Form actions */}
          <div className="flex justify-end gap-3 border-t border-gray-200 pt-6">
            <button
              type="button"
              className="rounded-md border border-gray-300 px-4 py-2 text-sm font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-primary text-primary-foreground rounded-md px-4 py-2 text-sm font-medium"
            >
              Save Configuration
            </button>
          </div>
        </form>
      </div>
    </div>
  ),
};
