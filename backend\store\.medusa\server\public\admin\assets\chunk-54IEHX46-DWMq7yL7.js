function d(r,n){switch(r){case!0:return{label:n("categories.fields.status.active"),color:"green"};case!1:return{label:n("categories.fields.status.inactive"),color:"red"}}}function h(r,n){switch(r){case!0:return{label:n("categories.fields.visibility.internal"),color:"blue"};case!1:return{label:n("categories.fields.visibility.public"),color:"green"}}}function u(r){if(!r)return[];const n=r.parent_category?u(r.parent_category):[];return n.push({id:r.id,name:r.name}),n}function f(r){return!r||!r.category_children?[]:r.category_children.map(n=>({id:n.id,name:n.name}))}var p=(r,n)=>{const c=new Set,s=(i,a)=>{const e=[...i],l=[];for(;e.length>0;){const t=e.pop();t.id!==a&&(t.category_children&&(t.category_children=s(t.category_children,a)),l.push(t))}return l},o=i=>{const a=[...i];for(;a.length>0;){const e=a.pop();if(!c.has(e.id)){if(c.add(e.id),e.id===n.parent_category_id)return e.category_children||(e.category_children=[]),n.rank===null?e.category_children.push(n):e.category_children.splice(n.rank,0,n),e.category_children.forEach((l,t)=>{l.rank=t}),e.category_children.sort((l,t)=>(l.rank??0)-(t.rank??0)),r;e.category_children&&a.push(...e.category_children)}}return i};return r=s(r,n.id),n.parent_category_id===null&&n.rank===null?(r.unshift(n),r.forEach((i,a)=>{i.rank=a})):n.parent_category_id===null&&n.rank!==null?(r.splice(n.rank,0,n),r.forEach((i,a)=>{i.rank=a})):r=o(r),r.sort((i,a)=>(i.rank??0)-(a.rank??0)),r};export{d as a,h as b,f as c,u as g,p as i};
