// Dropdown.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { expect, userEvent, within } from '@storybook/test';
import { Dropdown } from './dropdown';
import { User, IdCard, Settings, LogOut } from 'lucide-react';

// Options for stories
const fruitOptions = [
  { value: 'apple', label: 'Apple' },
  { value: 'banana', label: 'Banana' },
  { value: 'cherry', label: 'Cherry' },
  { value: 'durian', label: 'Durian' },
  { value: 'elderberry', label: 'Elderberry' },
];

const userOptions = [
  { value: 'user1', label: 'สมาชิกของฉัน', icon: <IdCard className="h-5 w-5 text-blue-900" /> },
  { value: 'user2', label: 'ตั้งค่าบัญชีผู้ใช้', icon: <Settings className="h-5 w-5 text-blue-900" /> },
  { value: 'user3', label: 'ออกจากระบบ', icon: <LogOut className="h-5 w-5 text-blue-900" /> },
];


const meta: Meta<typeof Dropdown> = {
  title: 'UI/Dropdown/Dropdown',
  component: Dropdown,
  parameters: {
    layout: 'centered',
    // a11y testing parameters
    a11y: {
      config: {
        rules: [
          {
            id: 'aria-required-children',
            enabled: true,
          },
        ],
      },
    },
  },
  // ค่าเริ่มต้นที่ใช้กับทุก Story
  args: {
    id: 'dropdown-field',
    options: fruitOptions,
    placeholder: 'Select an option',
  },
  // Mock functions สำหรับใช้ในการทดสอบ
  argTypes: {
    onChange: { action: 'changed' },
    onValueChange: { action: 'valueChanged' },
    variant: {
      control: 'select',
      options: ['default', 'blue', 'white'],
    },
    size: {
      control: 'select',
      options: ['sm', 'default', 'lg'],
    },
  },
  // สร้าง documentation อัตโนมัติ
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Dropdown>;

// Story พื้นฐาน
export const Default: Story = {
  args: {},
};

// Story ขนาดต่างๆ
export const SizeVariants: Story = {
  render: () => (
    <div className="flex flex-col space-y-4 w-64">
      <Dropdown 
        size="sm" 
        options={fruitOptions} 
        placeholder="Small dropdown"
      />
      <Dropdown 
        size="default" 
        options={fruitOptions} 
        placeholder="Default dropdown"
      />
      <Dropdown 
        size="lg" 
        options={fruitOptions} 
        placeholder="Large dropdown"
      />
    </div>
  ),
};

// Story ที่มี label
export const WithLabel: Story = {
  args: {
    label: 'Favorite Fruit',
    placeholder: 'Select a fruit',
  },
};

// Story ที่มีข้อความช่วยเหลือ
export const WithHelperText: Story = {
  args: {
    label: 'Favorite Fruit',
    placeholder: 'Select a fruit',
    helperText: 'Choose your favorite fruit from the list',
  },
};

// Story ที่มีการกำหนดค่าเริ่มต้น
export const WithDefaultValue: Story = {
  args: {
    label: 'Favorite Fruit',
    defaultValue: 'banana',
  },
};

// Story แบบมี Prefix Icon
export const WithPrefix: Story = {
  args: {
    options: userOptions,
    placeholder: 'ธยาดา แสงสว่าง',
    variant: 'blue',
    prefixIcon: <User className="h-5 w-5 text-blue-900" />,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Click to open dropdown
    const dropdown = canvas.getByRole('combobox');
    await userEvent.click(dropdown);
    
    // Check if options are visible
    const option = await canvas.findByText('สมชาย ใจดี');
    expect(option).toBeInTheDocument();
  },
};

// Story แสดงสถานะ disabled
export const Disabled: Story = {
  args: {
    label: 'Favorite Fruit',
    defaultValue: 'apple',
    disabled: true,
  },
};

// Story Dropdown แบบ Searchable
export const Searchable: Story = {
  args: {
    label: 'Fruit',
    options: fruitOptions,
    placeholder: 'Search and select a fruit',
    searchable: true,
  },
};

// Story สำหรับทดสอบ a11y โดยเฉพาะ
export const AccessibilityTest: Story = {
  args: {
    label: 'Fruit',
    options: fruitOptions,
    placeholder: 'Select your favorite fruit',
    required: true,
    helperText: 'Please select a fruit from the dropdown list',
  },
};