import{r as n}from"./index-Bwql5Dzz.js";var _=Object.defineProperty,l=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,o=(r,e,a)=>e in r?_(r,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[e]=a,c=(r,e)=>{for(var a in e)i.call(e,a)&&o(r,a,e[a]);if(l)for(var a of l(e))f.call(e,a)&&o(r,a,e[a]);return r},s=(r,e)=>{var a={};for(var t in r)i.call(r,t)&&e.indexOf(t)<0&&(a[t]=r[t]);if(r!=null&&l)for(var t of l(r))e.indexOf(t)<0&&f.call(r,t)&&(a[t]=r[t]);return a};const v=n.forwardRef((r,e)=>{var a=r,{color:t="currentColor"}=a,p=s(a,["color"]);return n.createElement("svg",c({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:e},p),n.createElement("path",{fill:t,d:"M4.91 5.75c-.163 0-.323-.037-.464-.108a.85.85 0 0 1-.334-.293A.7.7 0 0 1 4 4.952a.7.7 0 0 1 .142-.39l2.59-3.454c.082-.11.195-.2.33-.263a1.04 1.04 0 0 1 .876 0 .9.9 0 0 1 .33.263l2.59 3.455a.7.7 0 0 1 .141.39.7.7 0 0 1-.111.396.85.85 0 0 1-.335.293c-.14.07-.3.108-.464.108zM10.09 9.25c.163 0 .323.037.463.108.14.07.256.172.335.293a.7.7 0 0 1 .111.397.7.7 0 0 1-.141.39l-2.59 3.454a.9.9 0 0 1-.33.263 1.04 1.04 0 0 1-.876 0 .9.9 0 0 1-.33-.263l-2.59-3.455a.7.7 0 0 1-.142-.39.7.7 0 0 1 .112-.396.85.85 0 0 1 .335-.293c.14-.07.3-.108.463-.108z"}))});v.displayName="TrianglesMini";export{v as T};
