import{M as n}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as d,aN as p,aQ as u,j as r}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import{b as c}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var h=()=>{const{id:a}=d(),{product_category:t,isPending:o,isError:s,error:e}=p(a),{mutateAsync:i,isPending:m}=u(a);if(s)throw e;return r.jsx(c,{children:r.jsx(n,{isPending:o,isMutating:m,hook:i,metadata:t==null?void 0:t.metadata})})};export{h as Component};
