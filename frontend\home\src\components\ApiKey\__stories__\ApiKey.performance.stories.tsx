import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Create, Edit, ApiKeyPermissionSection } from '..';
import { FormProvider, useForm } from 'react-hook-form';
import { ApiKeyFormValues } from '../ApiKey-Schema';
import {
  sampleProjects,
  sampleEnvironments,
  sampleApiKeyAll,
  sampleApiKeyRestricted,
} from '../__fixtures__/ApiKey.fixtures';

// Wrapper component to provide form context for performance testing
const ApiKeyPermissionSectionWrapper = () => {
  const methods = useForm({
    defaultValues: {
      permissionType: 'Restricted' as const,
      resourcePermissions: {
        models: 'Read' as const,
        modelCapabilities: 'Write' as const,
        assistants: 'None' as const,
        threads: 'None' as const,
        evals: 'None' as const,
        fineTuning: 'None' as const,
        files: 'Read' as const,
      },
    },
  });

  return (
    <FormProvider {...methods}>
      <div className="rounded-lg bg-white p-4 shadow">
        <ApiKeyPermissionSection permissionType="Restricted" />
      </div>
    </FormProvider>
  );
};

const meta = {
  title: 'Components/ApiKey/Performance',
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Performance testing for ApiKey components',
      },
    },
    chromatic: {
      delay: 300,
      pauseAnimationAtEnd: true,
    },
  },
  decorators: [
    (Story) => (
      <div className="rounded-lg bg-gray-50 p-6">
        <Story />
      </div>
    ),
  ],
  tags: ['performance'],
} as Meta;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Basic performance test for the Create component.
 */
export const CreateBasicPerformance: Story = {
  render: () => (
    <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
  ),
  parameters: {
    performance: {
      allowedJankiness: 2,
      allowedTimeToInteractive: 200,
    },
  },
};

/**
 * Performance test with all advanced options enabled.
 */
export const CreateWithAllOptions: Story = {
  render: () => (
    <Create
      onSubmit={() => {}}
      onCancel={() => {}}
      availableProjects={sampleProjects}
      availableEnvironments={sampleEnvironments}
      showAdvancedOptions={true}
      showTags={true}
      showIpRestrictions={true}
      showDomainRestrictions={true}
      showRateLimit={true}
    />
  ),
  parameters: {
    performance: {
      allowedJankiness: 3,
      allowedTimeToInteractive: 300,
    },
  },
};

/**
 * Performance test for the Edit component with restricted permissions.
 */
export const EditRestrictedPerformance: Story = {
  render: () => <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />,
  parameters: {
    performance: {
      allowedJankiness: 2,
      allowedTimeToInteractive: 200,
    },
  },
};

/**
 * Performance test with usage log and all options enabled.
 */
export const EditWithAllOptionsPerformance: Story = {
  render: () => (
    <Edit
      apiKey={sampleApiKeyRestricted}
      onSubmit={() => {}}
      onCancel={() => {}}
      showAdvancedOptions={true}
      showTags={true}
      showIpRestrictions={true}
      showDomainRestrictions={true}
      showRateLimit={true}
      showUsageLog={true}
    />
  ),
  parameters: {
    performance: {
      allowedJankiness: 3,
      allowedTimeToInteractive: 350,
    },
  },
};

/**
 * Performance test with different animation speeds.
 */
export const AnimationSpeedPerformance: Story = {
  render: () => (
    <div className="space-y-4">
      <div className="rounded-lg bg-white p-4 shadow">
        <h3 className="mb-2 font-medium">Slow Animation</h3>
        <Edit
          apiKey={sampleApiKeyAll}
          onSubmit={() => {}}
          onCancel={() => {}}
          animationSpeed="slow"
        />
      </div>
      <div className="rounded-lg bg-white p-4 shadow">
        <h3 className="mb-2 font-medium">Normal Animation</h3>
        <Edit
          apiKey={sampleApiKeyAll}
          onSubmit={() => {}}
          onCancel={() => {}}
          animationSpeed="normal"
        />
      </div>
      <div className="rounded-lg bg-white p-4 shadow">
        <h3 className="mb-2 font-medium">Fast Animation</h3>
        <Edit
          apiKey={sampleApiKeyAll}
          onSubmit={() => {}}
          onCancel={() => {}}
          animationSpeed="fast"
        />
      </div>
    </div>
  ),
  parameters: {
    performance: {
      allowedJankiness: 4,
      allowedTimeToInteractive: 500,
    },
  },
};

/**
 * Performance test for the Permission Section component in isolation.
 */
export const PermissionSectionPerformance: Story = {
  render: () => <ApiKeyPermissionSectionWrapper />,
  parameters: {
    performance: {
      allowedJankiness: 1,
      allowedTimeToInteractive: 100,
    },
  },
};

/**
 * Multiple instances performance test to measure scaling.
 */
export const MultipleInstancesPerformance: Story = {
  render: () => (
    <div className="space-y-2">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="rounded-lg bg-white p-4 shadow">
          <h3 className="mb-2 font-medium">Instance {i + 1}</h3>
          <Create
            onSubmit={() => {}}
            onCancel={() => {}}
            availableProjects={sampleProjects.slice(0, 2)}
            variant="compact"
          />
        </div>
      ))}
    </div>
  ),
  parameters: {
    performance: {
      allowedJankiness: 5,
      allowedTimeToInteractive: 500,
    },
  },
};

/**
 * Performance test with large data sets.
 */
export const LargeDataSetPerformance: Story = {
  render: () => {
    // Generate a large number of projects
    const manyProjects = Array.from({ length: 100 }).map((_, i) => ({
      value: `project${i}`,
      label: `Project ${i} with a long name to test performance`,
    }));

    return (
      <Create
        onSubmit={() => {}}
        onCancel={() => {}}
        availableProjects={manyProjects}
        availableEnvironments={sampleEnvironments}
        showAdvancedOptions={true}
        showTags={true}
      />
    );
  },
  parameters: {
    performance: {
      allowedJankiness: 4,
      allowedTimeToInteractive: 400,
    },
  },
};

/**
 * Performance test with many custom resources.
 */
export const ManyCustomResourcesPerformance: Story = {
  render: () => {
    // Create a proper React component to use hooks
    const CustomResourcesComponent = () => {
      // Create many custom resources
      const customResources = Array.from({ length: 20 }).map((_, i) => ({
        name: `customResource${i}`,
        label: `Custom Resource ${i}`,
        path: `/v1/custom/resource${i}`,
      }));

      const methods = useForm({
        defaultValues: {
          permissionType: 'Restricted' as const,
          resourcePermissions: Object.fromEntries(customResources.map((r) => [r.name, 'None'])),
        },
      });

      return (
        <FormProvider {...methods}>
          <div className="rounded-lg bg-white p-4 shadow">
            <ApiKeyPermissionSection
              permissionType="Restricted"
              customResources={customResources}
            />
          </div>
        </FormProvider>
      );
    };

    return <CustomResourcesComponent />;
  },
  parameters: {
    performance: {
      allowedJankiness: 3,
      allowedTimeToInteractive: 300,
    },
  },
};

/**
 * Performance test with complex event handling.
 */
export const ComplexEventHandlingPerformance: Story = {
  render: () => {
    const handleSubmit = (data: ApiKeyFormValues) => {
      // Simulate complex processing
      console.warn('Processing form data:', data);
      // Simulate network delay
      setTimeout(() => {
        console.warn('Form submission complete');
      }, 500);
    };

    return (
      <Create
        onSubmit={handleSubmit}
        onCancel={() => console.warn('Cancelled')}
        availableProjects={sampleProjects}
        validateForm={(data) => {
          // Simulate complex validation
          const errors: Record<string, string> = {};

          if (data.name && data.name.length < 3) {
            errors.name = 'Name must be at least 3 characters long';
          }

          if (data.permissionType === 'Restricted') {
            const resourcePermissions = data.resourcePermissions || {};
            const hasAnyPermission = Object.values(resourcePermissions).some(
              (value) => value !== 'None',
            );

            if (!hasAnyPermission) {
              errors['resourcePermissions'] = 'At least one resource must have permissions';
            }
          }

          return Object.keys(errors).length > 0 ? errors : null;
        }}
      />
    );
  },
  parameters: {
    performance: {
      allowedJankiness: 2,
      allowedTimeToInteractive: 250,
    },
  },
};
