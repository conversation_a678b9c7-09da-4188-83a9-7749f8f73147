import type { Meta, StoryObj } from '@storybook/react';
import { Globe, Languages, Flag } from 'lucide-react';
import { StorybookI18nButton } from '../storybook-wrapper';

const meta: Meta<typeof StorybookI18nButton> = {
  title: 'Components/I18nButton',
  component: StorybookI18nButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A button that allows users to switch between different languages with full internationalization support.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof StorybookI18nButton>;

// Default language button
export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'The default language button with ghost variant and icon size.',
      },
    },
  },
};

// Different language variants
export const English: Story = {
  args: {
    showLanguageCode: true
  },
  parameters: {
    locale: 'en',
    docs: {
      description: {
        story: 'Language button with English locale set.',
      },
    },
  },
};

export const French: Story = {
  args: {
    showLanguageCode: true
  },
  parameters: {
    locale: 'fr',
    docs: {
      description: {
        story: 'Language button with French locale set.',
      },
    },
  },
};

export const Japanese: Story = {
  args: {
    showLanguageCode: true
  },
  parameters: {
    locale: 'ja',
    docs: {
      description: {
        story: 'Language button with Japanese locale set.',
      },
    },
  },
};

export const Arabic: Story = {
  args: {
    showLanguageCode: true
  },
  parameters: {
    locale: 'ar',
    docs: {
      description: {
        story: 'Language button with Arabic locale set (RTL language).',
      },
    },
  },
};

// Different button variants
export const OutlineVariant: Story = {
  args: {
    variant: 'outline',
    size: 'default',
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button with outline variant.',
      },
    },
  },
};

export const SecondaryVariant: Story = {
  args: {
    variant: 'secondary',
    size: 'default',
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button with secondary variant.',
      },
    },
  },
};

// Different sizes
export const SmallSize: Story = {
  args: {
    size: 'sm',
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button with small size.',
      },
    },
  },
};

export const DefaultSize: Story = {
  args: {
    size: 'default',
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button with default size.',
      },
    },
  },
};

export const LargeSize: Story = {
  args: {
    size: 'lg',
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button with large size.',
      },
    },
  },
};

// States
export const WithLoading: Story = {
  args: {
    loading: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button in loading state.',
      },
    },
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Disabled language button.',
      },
    },
  },
};

// With custom icon
export const CustomIcon: Story = {
  args: {
    icon: <Languages className="h-[1.2rem] w-[1.2rem]" />,
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button with custom icon.',
      },
    },
  },
};

// With custom languages
export const CustomLanguages: Story = {
  args: {
    languages: [
      { code: 'en', label: 'English', icon: <Flag className="h-4 w-4" /> },
      { code: 'fr', label: 'French', icon: <Flag className="h-4 w-4" /> },
      { code: 'es', label: 'Spanish', icon: <Flag className="h-4 w-4" /> },
    ],
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button with custom languages list.',
      },
    },
  },
};

// With native names
export const NativeNames: Story = {
  args: {
    showNativeNames: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button showing language names in their native language.',
      },
    },
  },
};

// With language code
export const ShowLanguageCode: Story = {
  args: {
    showLanguageCode: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Language button showing the current language code next to the icon.',
      },
    },
  },
}; 