import{V as _}from"./chunk-F6ZOHZVB-CmkCPNYI.js";import{a2 as C,a4 as m,df as j,b as f,R as y,dm as b,j as e,H as E,a8 as F,a9 as v,dq as S,t as p,w as s,x,B as u}from"./index-Bwql5Dzz.js";import{K as L}from"./chunk-6HTZNHPT-N4svn6ad.js";import{b as t,u as w}from"./chunk-JGQGO74V-DtHO1ucg.js";import{D as h}from"./date-picker-C167G6yz.js";import"./prompt-BsR9zKsn.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./index-DP5bcQyU.js";import"./x-mark-mini-DvSTI7zK.js";import"./triangle-left-mini-Bu6679Aa.js";var D=C({name:m(),description:m().optional(),campaign_identifier:m().optional(),starts_at:j().optional(),ends_at:j().optional()}),I=({campaign:n})=>{const{t:a}=f(),{handleSuccess:o}=w(),i=F({defaultValues:{name:n.name||"",description:n.description||"",campaign_identifier:n.campaign_identifier||"",starts_at:n.starts_at?new Date(n.starts_at):void 0,ends_at:n.ends_at?new Date(n.ends_at):void 0},resolver:v(D)}),{mutateAsync:d,isPending:l}=S(n.id),g=i.handleSubmit(async r=>{await d({name:r.name,description:r.description,campaign_identifier:r.campaign_identifier,starts_at:r.starts_at,ends_at:r.ends_at},{onSuccess:({campaign:c})=>{p.success(a("campaigns.edit.successToast",{name:c.name})),o()},onError:c=>{p.error(c.message)}})});return e.jsx(t.Form,{form:i,children:e.jsxs(L,{onSubmit:g,className:"flex flex-1 flex-col",children:[e.jsx(t.Body,{children:e.jsxs("div",{className:"flex flex-col gap-y-4",children:[e.jsx(s.Field,{control:i.control,name:"name",render:({field:r})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:a("fields.name")}),e.jsx(s.Control,{children:e.jsx(x,{...r})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:i.control,name:"description",render:({field:r})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:a("fields.description")}),e.jsx(s.Control,{children:e.jsx(x,{...r})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:i.control,name:"campaign_identifier",render:({field:r})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:a("campaigns.fields.identifier")}),e.jsx(s.Control,{children:e.jsx(x,{...r})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:i.control,name:"starts_at",render:({field:r})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:a("campaigns.fields.start_date")}),e.jsx(s.Control,{children:e.jsx(h,{granularity:"minute",hourCycle:12,shouldCloseOnSelect:!1,...r})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:i.control,name:"ends_at",render:({field:r})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:a("campaigns.fields.end_date")}),e.jsx(s.Control,{children:e.jsx(h,{granularity:"minute",shouldCloseOnSelect:!1,...r})}),e.jsx(s.ErrorMessage,{})]})})]})}),e.jsx(t.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(t.Close,{asChild:!0,children:e.jsx(u,{variant:"secondary",size:"small",children:a("actions.cancel")})}),e.jsx(u,{isLoading:l,type:"submit",variant:"primary",size:"small",children:a("actions.save")})]})})]})})},k=()=>{const{t:n}=f(),{id:a}=y(),{campaign:o,isLoading:i,isError:d,error:l}=b(a);if(d)throw l;return e.jsxs(t,{children:[e.jsxs(t.Header,{children:[e.jsx(t.Title,{asChild:!0,children:e.jsx(E,{children:n("campaigns.edit.header")})}),e.jsx(t.Description,{asChild:!0,children:e.jsx(_,{children:n("campaigns.edit.description")})})]}),!i&&o&&e.jsx(I,{campaign:o})]})};export{k as Component};
