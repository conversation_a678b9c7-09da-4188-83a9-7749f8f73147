// card-product.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { CardProduct, CardProductProps } from '../card-product';
import { 
  wireProduct, 
  lightFixtureProduct, 
  deskLampProduct 
} from '../__fixtures__/card-product.fixtures';

const meta: Meta<typeof CardProduct> = {
  title: 'UI/Card/CardProduct',
  component: CardProduct,
  parameters: {
    layout: 'centered',
    // Add a11y parameter for accessibility testing
    a11y: {
      config: {
        rules: [
          {
            // Ensure all images have alt text
            id: 'image-alt',
            enabled: true
          }
        ]
      }
    },
  },
  // Define argTypes for Storybook controls
  argTypes: {
    id: { 
      control: 'text',
      description: 'Unique product identifier',
    },
    imageUrl: { 
      control: 'text',
      description: 'URL to the product image' 
    },
    brand: { 
      control: 'text',
      description: 'Product brand name' 
    },
    name: { 
      control: 'text',
      description: 'Product name/title' 
    },
    price: { 
      control: { type: 'number', min: 0 },
      description: 'Current product price' 
    },
    originalPrice: { 
      control: { type: 'number', min: 0 },
      description: 'Original price before discount' 
    },
    discountPercentage: { 
      control: { type: 'number', min: 0, max: 100 },
      description: 'Discount percentage (0-100)' 
    },
    unit: { 
      control: 'text',
      description: 'Price unit (e.g., บาท/ชิ้น)' 
    },
    soldCount: { 
      control: 'number',
      description: 'Number of items sold' 
    },
    onAddToCart: { 
      action: 'addToCart',
      description: 'Function called when add button is clicked' 
    },
    isAddingToCart: { 
      control: 'boolean',
      description: 'Whether the item is currently being added to cart' 
    },
  },
  // Default values for the stories
  args: {
    onAddToCart: action('onAddToCart'),
  },
  // Add decorators if needed
  decorators: [
    (Story) => (
      <div style={{ maxWidth: '300px' }}>
        <Story />
      </div>
    ),
  ],
  // Add tags for filtering in Storybook
  tags: ['autodocs', 'product', 'card'],
};

export default meta;
type Story = StoryObj<typeof CardProduct>;

// Default story with the wire product
export const WireProduct: Story = {
  args: {
    ...wireProduct,
  },
};

// Light fixture product story
export const LightFixture: Story = {
  args: {
    ...lightFixtureProduct,
  },
};

// Desk lamp product story (no discount)
export const DeskLamp: Story = {
  args: {
    ...deskLampProduct,
  },
};

// Story with long product name to test line clamping
export const LongProductName: Story = {
  args: {
    ...wireProduct,
    name: 'สายไฟ IEC(IV) 1x1 ตร.มม. ยาว 100เมตร สีเขียว คุณภาพสูง มาตรฐาน IEC รับประกัน 5 ปี ทนความร้อนได้ดี ปลอดภัย 100%',
  },
};