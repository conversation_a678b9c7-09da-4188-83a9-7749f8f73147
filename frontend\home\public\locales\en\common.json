{"app": {"title": "Timeline", "description": "A customizable timeline component for displaying chronological events."}, "nav": {"home": "Home", "about": "About", "settings": "Settings", "installation": "Installation", "usage": "Usage", "examples": "Examples", "rendering_examples": "Rendering Examples"}, "buttons": {"submit": "Submit", "cancel": "Cancel", "save": "Save", "delete": "Delete", "clickMe": "ClickMe"}, "language": {"en": "English", "fr": "French", "ja": "Japanese", "ar": "Arabic", "switchLanguage": "Switch Language", "current": "Current language"}, "theme": {"current": "Current theme"}, "rendering": {"backToExamples": "Back to Examples", "backToCSR": "Back to CSR Example", "backToSPA": "Back to SPA Example", "backToSSR": "Back to SSR Example", "viewRouterExample": "View Router Example with Theme & Language Support", "viewFullSPAExample": "View Full SPA Router Example with Theme & Language Support", "basicExample": "Basic Example", "withDataExample": "With Data Example", "advancedExample": "Advanced Example", "ssr": "SSR", "ssg": "SSG", "csr": "CSR", "isr": "ISR", "spa": "SPA", "routes": {"basic": "Basic", "withData": "With Data", "advanced": "Advanced", "home": "Home", "about": "About", "products": "Products", "contact": "Contact"}, "ssrTitle": "Server-Side Rendering (SSR)", "ssrHowWorks": "How SSR Works", "ssrDescription": "With Server-Side Rendering, the HTML for this page is generated on the server for each request. This means the content is always up-to-date, as it's generated when the user requests the page.", "ssrNote": "This is useful for pages where the content changes frequently or needs to be personalized for each user. SSR is also great for SEO since search engines can see the fully rendered content.", "ssrServerData": "Server-Generated Data", "ssrServerDataDescription": "This data was generated on the server at request time:", "ssrRefreshNote": "Refresh the page to see these values change, confirming that the page is rendered on each request.", "ssrImplementation": "Implementation", "ssrImplementationDescription": "In Next.js 15, SSR is the default for pages in the App Router. Simply export a server component that fetches data.", "ssrWithData": "SSR with Detailed Data", "ssrDataFetch": "Dynamic User Data", "ssrDataFetchDescription": "This example demonstrates fetching more complex data on the server for each request. Below you can see user data that is freshly generated for each page load.", "userData": "User Data", "userId": "ID", "userName": "Name", "userRole": "Role", "userLastActive": "Last Active", "systemStatus": "System Status", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "uptime": "Uptime", "generatedAt": "Generated at", "ssrAdvanced": "Advanced SSR Analytics", "realTimeAnalytics": "Real-time Analytics Dashboard", "ssrAdvancedDescription": "This advanced example demonstrates how SSR can be used to generate complex, data-heavy dashboards with the latest information on every request.", "pageViews": "Page Views", "today": "Today", "weekly": "Weekly", "monthly": "Monthly", "userSessions": "User Sessions", "avgDuration": "Avg. <PERSON>", "bounceRate": "Bounce Rate", "newUsers": "New Users", "serverLoad": "Server Load", "current": "Current", "average": "Average", "peak": "Peak", "ssrAdvantages": "SSR Advantages", "ssrAdvantage1": "Always fresh data on every request", "ssrAdvantage2": "Excellent SEO as search engines see complete HTML", "ssrAdvantage3": "Fast time-to-content for users", "ssrAdvantage4": "Works well even with JavaScript disabled", "ssrRouterTitle": "Server-Side Rendering (SSR) Router Example", "ssrRouterHowWorks": "How SSR Routing Works", "ssrRouterDescription": "With Server-Side Rendering, each route change triggers a new request to the server, which generates the HTML for that route. This means that each page is freshly rendered on the server with the latest data.", "ssrRouterNote": "While this gives you always-fresh content, you'll notice full page refreshes during navigation. Theme and language settings are maintained through cookies or local storage.", "csrTitle": "Client-Side Rendering (CSR)", "csrHowWorks": "How CSR Works", "csrDescription": "With Client-Side Rendering, the server sends a minimal HTML page, and JavaScript builds the page in the browser. This is visible in how this page starts with minimal content and then 'hydrates' with the full UI once JavaScript loads.", "csrNote": "CSR is good for highly interactive pages where most content is user-specific and SEO is less important.", "clientState": "Client-Side State", "clientStateDescription": "These values are only available after JavaScript runs in the browser:", "clientStateLoading": "Loading client-side data...", "clientTime": "Browser time", "windowSize": "Window size", "refreshNote": "Refresh the page to see how CSR causes a flash of unstyled/minimal content.", "csrWithData": "CSR with Data Fetching", "csrDataFetch": "Client-Side Data Fetching", "csrDataFetchDescription": "In CSR, data is fetched by the browser after JavaScript loads. This means there's always a delay before data appears.", "fetchedData": "Fetched Data", "loadingData": "Loading data...", "dataMessage": "Message", "dataTimestamp": "Timestamp", "csrAdvanced": "Advanced CSR Techniques", "csrAdvancedTips": "Optimizing CSR Applications", "csrAdvancedDescription": "Advanced CSR applications use techniques like code splitting, lazy loading, and client-side caching to improve performance.", "csrBestPractices": "Best Practices", "csrBestPractice1": "Use loading states to indicate content is being fetched", "csrBestPractice2": "Implement error boundaries to handle failures gracefully", "csrBestPractice3": "Consider using a service worker for offline capabilities", "csrBestPractice4": "Pre-load critical data where possible", "csrRouterTitle": "How CSR Routing Works", "csrRouterDescription": "With Client-Side Rendering, the initial HTML is minimal, and JavaScript builds the page in the browser. Navigation between routes happens entirely in the browser without full page reloads.", "csrRouterNote": "This approach provides a smooth, app-like experience with preserved state between route changes. Theme and language preferences persist naturally during navigation since the page never fully reloads.", "spaTitle": "Single Page Application (SPA)", "spaHowWorks": "How SPAs Work", "spaDescription": "Single Page Applications load a single HTML page and then dynamically update the content as the user interacts with the app. Navigation between \"pages\" happens entirely on the client-side without refreshing the browser.", "spaNote": "SPAs provide a smoother user experience since they don't require page reloads, maintaining application state and allowing for more app-like interactions. The current URL is managed using the browser's History API.", "currentUrl": "Current URL", "loadedAt": "Loaded at", "spaFeatures": "Key SPA Features", "spaFeature1": "Client-side routing without page reloads", "spaFeature2": "Persistent application state between \"page\" transitions", "spaFeature3": "Ability to work offline with service workers", "spaFeature4": "Smooth transitions and animations between views", "spaRouterTitle": "How SPA Routing Works", "spaRouterDescription": "In a Single Page Application, all routing happens client-side without refreshing the page. The app intercepts URL changes and renders the appropriate content without requesting new HTML from the server.", "spaRouterNote": "This provides the smoothest user experience with persistent state and no page flashes. Current URL:"}, "timeConversion": {"title": "Time conversion", "utc": "UTC", "local": "Local", "timezone": "Timezone", "date": "Date", "time": "Time"}, "tableDynamic": {"empty": {"title": "No data available", "description": "There are no items to display."}, "error": {"generic": "An error occurred while loading the data."}, "filter": {"searchPlaceholder": "Search...", "clearSearch": "Clear search", "filters": "Filters", "filterColumns": "Filter columns", "clearAllFilters": "Clear all filters"}, "footer": {"rowsPerPage": "Rows per page:", "pageInfo": "Page {{current}} of {{total}} ({{from}}-{{to}} of {{totalRows}} items)", "firstPage": "First page", "previousPage": "Previous page", "nextPage": "Next page", "lastPage": "Last page"}, "skeleton": {"ariaLabel": "Loading table content"}}, "userTable": {"headers": {"name": "Name", "email": "Email", "role": "Role", "status": "Status", "lastLogin": "Last Login"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending"}, "empty": {"title": "No Users Found", "description": "There are no users to display."}, "error": {"title": "Error", "retry": "Retry"}, "footer": {"total": "Total: {{count}} users"}}, "apiKey": {"name": {"label": "Name", "placeholder": "Optional", "serviceKeyLabel": "Service Key Name", "serviceKeyPlaceholder": "My Service Account Key", "userKeyPlaceholder": "My Test Key"}, "ownerType": {"label": "Owned by", "you": "You", "serviceAccount": "Service account"}, "permissionType": {"label": "Permissions", "all": "All", "restricted": "Restricted", "readOnly": "Read only"}, "project": {"label": "Project", "placeholder": "Select a project"}, "resources": {"title": "Resources", "permissions": "Permissions", "models": "Models", "modelsPath": "/v1/models", "modelCapabilities": "Model capabilities", "modelCapabilitiesPath": "/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations", "assistants": "Assistants", "assistantsPath": "/v1/assistants", "assistantsModelsPath": "/v1/models (required for Assistants)", "threads": "Threads", "threadsPath": "/v1/threads", "threadsModelsPath": "/v1/models (required for Threads)", "evals": "Evals", "evalsPath": "/v1/evals", "fineTuning": "Fine-tuning", "fineTuningPath": "/v1/fine_tuning", "files": "Files", "filesPath": "/v1/files"}, "resourcePermissions": {"none": "None", "read": "Read", "write": "Write"}, "descriptions": {"serviceAccount": "A new bot member (service account) will be added to your project, and an API key will be created.", "userAccount": "This API key is tied to your user and can make requests against the selected project. If you are removed from the organization or project, this key will be disabled.", "personal": "This API key is tied to your user and can make requests against the selected project. If you are removed from the organization or project, this key will be disabled.", "permissionChange": "Permission changes may take a few minutes to take effect."}, "actions": {"cancel": "Cancel", "save": "Save", "createKey": "Create secret key", "create": "Create secret key", "submitting": "Submitting..."}, "titles": {"create": "Create new secret key", "edit": "Edit secret key"}, "validation": {"required": "This field is required", "name": "Name must contain only letters, numbers, hyphens, and underscores"}}, "batchDetailsCard": {"title": "BATCH", "status": {"label": "Status", "completed": "Completed", "failed": "Failed", "pending": "Pending"}, "createdAt": {"label": "Created at"}, "endpoint": {"label": "Endpoint"}, "completionWindow": {"label": "Completion window"}, "completionTime": {"label": "Completion time"}, "requestCounts": {"label": "Request counts", "info": "{{completed}} completed, {{failed}} failed of {{total}} total requests"}, "files": {"title": "Files", "input": {"label": "Input file"}, "output": {"label": "Output file"}, "error": {"label": "Error file"}, "download": "Download {{file}}"}, "error": {"title": "Error Loading Batch", "generic": "Failed to load batch details"}, "tooltip": {"timeConversion": "Time Conversion", "utc": "UTC", "local": "Local", "relative": "Relative"}, "skeleton": {"ariaLabel": "Loading batch details"}, "demo": {"title": "Batch Details Card", "description": "A comprehensive card displaying detailed information about a batch process", "showLoading": "Show Loading State", "hideLoading": "Hide Loading State", "sizes": "<PERSON><PERSON>", "smallSize": "Small Size", "mediumSize": "Medium Size", "largeSize": "Large Size", "variants": "Style Variants", "primaryVariant": "Primary Variant", "secondaryVariant": "Secondary Variant", "outlineVariant": "Outline Variant", "states": "Component States", "disabledState": "Disabled State", "errorState": "Error State", "customization": "Custom Configurations", "noTimeline": "Without Timeline", "customTimeline": "Custom Timeline", "fullWidth": "Full Width", "languages": "Language Variants"}}, "rtlDemo": {"title": "RTL Demonstration", "currentLanguage": "Current Language", "direction": "Text Direction", "paragraph1": "This component demonstrates how text direction changes based on the selected language. English is a left-to-right (LTR) language.", "paragraph2": "Notice how the buttons at the bottom maintain their positions relative to the text direction. In LTR mode, 'Previous' is on the left, while in RTL mode, it appears on the right.", "previousButton": "Previous", "nextButton": "Next", "formTitle": "Form Elements Demo", "nameLabel": "Name", "namePlaceholder": "Enter your name", "emailLabel": "Email", "emailPlaceholder": "<EMAIL>", "subscribeLabel": "Subscribe to newsletter", "submitButton": "Submit", "textAlignmentTitle": "Text Alignment Demo", "textAlignStart": "This text is aligned to the start (left in LTR, right in RTL).", "textAlignEnd": "This text is aligned to the end (right in LTR, left in RTL).", "textAlignCenter": "This text is centered regardless of direction."}}