// _fixtures_/inputpassword-items.tsx
import { InputPassword } from '../input-password';

// Basic password input
export const DefaultPasswordInput = (
  <InputPassword 
    id="default-password"
    placeholder="Enter password"
  />
);

// Password input with label
export const PasswordInputWithLabel = (
  <InputPassword 
    id="labeled-password"
    label="Password"
    placeholder="Enter your password"
  />
);

// Password input with helper text
export const PasswordInputWithHelper = (
  <InputPassword 
    id="helper-password"
    label="Password"
    placeholder="Enter your password"
    helperText="Password must be at least 8 characters"
  />
);

// Password input with error
export const PasswordInputWithError = (
  <InputPassword 
    id="error-password"
    label="Password"
    placeholder="Enter password"
    error={true}
    errorMessage="Password must contain at least one uppercase letter"
  />
);

// Password input without toggle
export const PasswordInputWithoutToggle = (
  <InputPassword 
    id="no-toggle-password"
    label="Password"
    placeholder="Enter password"
    showPasswordToggle={false}
  />
);

// New password input
export const NewPasswordInput = (
  <InputPassword 
    id="new-password"
    label="รหัสผ่านใหม่"
    placeholder="************"
  />
);

// Confirm password input
export const ConfirmPasswordInput = (
  <InputPassword 
    id="confirm-password"
    label="ยืนยันรหัสผ่าน"
    placeholder="Qw1098304"
    readOnly
  />
);

// Different sized password inputs
export const SmallPasswordInput = (
  <InputPassword 
    id="small-password"
    size="sm"
    placeholder="Small password field"
  />
);

export const LargePasswordInput = (
  <InputPassword 
    id="large-password"
    size="lg"
    placeholder="Large password field"
  />
);

// Disabled password input
export const DisabledPasswordInput = (
  <InputPassword 
    id="disabled-password"
    label="Password"
    placeholder="Cannot edit this password"
    disabled
  />
);