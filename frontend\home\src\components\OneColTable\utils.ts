import { format, isValid } from 'date-fns';
import { TableItem } from './types';
import { DATE_FORMATS, ITEM_STATUS } from './constants';

/**
 * Format a timestamp for display in the table
 * @param timestamp Timestamp to format as Date object or string
 * @param formatPattern Optional custom date format pattern
 * @returns Formatted date string
 */
export const formatTimestamp = (
  timestamp: string | Date | undefined,
  formatPattern = DATE_FORMATS.DEFAULT,
): string => {
  if (!timestamp) return '';

  try {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;

    if (!isValid(date)) {
      return String(timestamp);
    }

    return format(date, formatPattern);
  } catch {
    // Silently handle errors and return the original timestamp
    return String(timestamp);
  }
};

/**
 * Filter table items based on a search term
 * @param items Array of TableItem objects
 * @param searchTerm Term to search for
 * @returns Filtered array of TableItem objects
 */
export const filterItems = (items: TableItem[], searchTerm: string): TableItem[] => {
  if (!searchTerm?.trim()) {
    return items;
  }

  const lowercaseTerm = searchTerm.toLowerCase().trim();

  return items.filter((item) => {
    // Search in content
    if (typeof item.content === 'string' && item.content.toLowerCase().includes(lowercaseTerm)) {
      return true;
    }

    // Search in secondaryContent
    if (
      typeof item.secondaryContent === 'string' &&
      item.secondaryContent.toLowerCase().includes(lowercaseTerm)
    ) {
      return true;
    }

    // Search in other string properties
    for (const key in item) {
      if (
        typeof item[key] === 'string' &&
        key !== 'id' &&
        key !== 'content' &&
        key !== 'secondaryContent' &&
        (item[key] as string).toLowerCase().includes(lowercaseTerm)
      ) {
        return true;
      }
    }

    return false;
  });
};

/**
 * Sort table items by timestamp or other property
 * @param items Array of TableItem objects
 * @param sortBy Property to sort by
 * @param sortOrder Sort order (asc or desc)
 * @returns Sorted array of TableItem objects
 */
export const sortItems = (
  items: TableItem[],
  sortBy: keyof TableItem = 'timestamp',
  sortOrder: 'asc' | 'desc' = 'desc',
): TableItem[] => {
  return [...items].sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];

    // Handle timestamps specially
    if (sortBy === 'timestamp') {
      const aDate = aValue ? new Date(aValue as string | Date).getTime() : 0;
      const bDate = bValue ? new Date(bValue as string | Date).getTime() : 0;

      return sortOrder === 'asc' ? aDate - bDate : bDate - aDate;
    }

    // Handle strings
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    }

    // Handle numbers
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    }

    // Default case
    return 0;
  });
};

/**
 * Get color class based on item status
 * @param status Status string
 * @returns Tailwind color class
 */
export const getStatusColorClass = (status?: string): string => {
  switch (status) {
    case ITEM_STATUS.COMPLETED:
      return 'text-green-500';
    case ITEM_STATUS.IN_PROGRESS:
      return 'text-blue-500';
    case ITEM_STATUS.PENDING:
      return 'text-amber-500';
    case ITEM_STATUS.WARNING:
      return 'text-orange-500';
    case ITEM_STATUS.ERROR:
      return 'text-red-500';
    default:
      return 'text-muted-foreground';
  }
};

/**
 * Create a paginated subset of items
 * @param items Full array of items
 * @param page Page number (1-based)
 * @param pageSize Number of items per page
 * @returns Paginated array of items
 */
export const paginateItems = (
  items: TableItem[],
  page: number = 1,
  pageSize: number = 10,
): TableItem[] => {
  const startIdx = (page - 1) * pageSize;
  return items.slice(startIdx, startIdx + pageSize);
};
