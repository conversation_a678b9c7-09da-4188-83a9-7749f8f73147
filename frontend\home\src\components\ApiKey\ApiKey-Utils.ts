import type { ResourcePermissionValue } from './Api<PERSON>ey-Schema';

/**
 * Default resource permissions object with all permissions set to None
 */
export const DEFAULT_RESOURCE_PERMISSIONS = {
  models: 'None' as ResourcePermissionValue,
  modelCapabilities: 'None' as ResourcePermissionValue,
  assistants: 'None' as ResourcePermissionValue,
  threads: 'None' as ResourcePermissionValue,
  evals: 'None' as ResourcePermissionValue,
  fineTuning: 'None' as ResourcePermissionValue,
  files: 'None' as ResourcePermissionValue,
};

/**
 * Ensures that a resource permissions object has all required fields
 * @param permissions Partial resource permissions object
 * @returns Complete resource permissions object with all required fields
 */
export const ensureCompleteResourcePermissions = (
  permissions?: Record<string, ResourcePermissionValue | undefined> | null,
): Record<string, ResourcePermissionValue> => {
  if (!permissions) {
    return DEFAULT_RESOURCE_PERMISSIONS;
  }

  // Filter out any undefined values and merge with defaults
  const filteredPermissions: Record<string, ResourcePermissionValue> = {};

  // Copy only defined values
  Object.entries(permissions).forEach(([key, value]) => {
    if (value !== undefined) {
      filteredPermissions[key] = value;
    }
  });

  // Start with default permissions and override with provided values
  return {
    ...DEFAULT_RESOURCE_PERMISSIONS,
    ...filteredPermissions,
  };
};

/**
 * Converts a permission type to a string label
 * @param permissionType The permission type to convert
 * @returns The label for the permission type
 */
export const getPermissionTypeLabel = (permissionType: string): string => {
  switch (permissionType) {
    case 'All':
      return 'All';
    case 'Restricted':
      return 'Restricted';
    case 'Read only':
      return 'Read only';
    default:
      return permissionType;
  }
};
