/**
 * Default configuration values for OneColTable component
 */

// Default row heights for different size variants
export const ROW_HEIGHTS = {
  sm: 48,
  md: 56,
  lg: 64,
};

// Default animation configuration
export const ANIMATION_CONFIG = {
  duration: 0.2,
  defaultDelay: 0.05,
};

// Default virtualization configuration
export const VIRTUALIZATION_CONFIG = {
  defaultHeight: 400,
  defaultWidth: '100%',
  defaultOverscan: 5,
};

// Status variants for table items
export const ITEM_STATUS = {
  COMPLETED: 'completed',
  IN_PROGRESS: 'in-progress',
  PENDING: 'pending',
  WARNING: 'warning',
  ERROR: 'error',
};

// Date format patterns
export const DATE_FORMATS = {
  DEFAULT: 'MMM d, yyyy, h:mm a',
  SHORT: 'MMM d, yyyy',
  TIME_ONLY: 'h:mm a',
};

// Default number of skeleton rows
export const DEFAULT_SKELETON_ROWS = 5;

// Default limits
export const DEFAULT_LIMITS = {
  INITIAL_ITEMS: 10,
  PAGE_SIZE: 5,
  MAX_VISIBLE: 100,
};
