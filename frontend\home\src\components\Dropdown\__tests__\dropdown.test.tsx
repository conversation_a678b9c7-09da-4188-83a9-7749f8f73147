// _tests_/dropdown.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Dropdown } from '../dropdown';
import { User, Package } from 'lucide-react';

// Mock options for testing
const mockOptions = [
  { value: 'apple', label: 'Apple' },
  { value: 'banana', label: 'Banana' },
  { value: 'cherry', label: 'Cherry' },
];

const mockOptionsWithIcons = [
  { value: 'user1', label: 'User One', icon: <User data-testid="user-icon" /> },
  { value: 'user2', label: 'User Two', icon: <User /> },
  { value: 'package1', label: 'Package One', icon: <Package /> },
];

describe('Dropdown Component', () => {
  // Test basic rendering
  test('renders dropdown button correctly', () => {
    render(<Dropdown options={mockOptions} placeholder="Select fruit" />);
    
    const dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toBeInTheDocument();
    expect(dropdownButton).toHaveTextContent('Select fruit');
  });
  
  // Test label rendering
  test('renders label when provided', () => {
    render(<Dropdown id="test-dropdown" options={mockOptions} label="Test Label" />);
    
    const labelElement = screen.getByText('Test Label');
    expect(labelElement).toBeInTheDocument();
  });
  
  // Test helper text
  test('renders helper text when provided', () => {
    render(<Dropdown id="test-dropdown" options={mockOptions} helperText="This is a helper text" />);
    
    const helperTextElement = screen.getByText('This is a helper text');
    expect(helperTextElement).toBeInTheDocument();
  });
  
  // Test error state
  test('renders error message when error is true', () => {
    render(
      <Dropdown 
        id="test-dropdown" 
        options={mockOptions} 
        error={true} 
        errorMessage="This is an error message" 
      />
    );
    
    const errorMessageElement = screen.getByText('This is an error message');
    expect(errorMessageElement).toBeInTheDocument();
  });
  
  // Test dropdown opens on click
  test('opens dropdown menu when clicked', async () => {
    render(<Dropdown options={mockOptions} />);
    
    const dropdownButton = screen.getByRole('combobox');
    fireEvent.click(dropdownButton);
    
    // Wait for dropdown content to appear
    await waitFor(() => {
      mockOptions.forEach(option => {
        expect(screen.getByText(option.label)).toBeInTheDocument();
      });
    });
  });
  
  // Test option selection
  test('selects an option when clicked', async () => {
    const handleValueChange = jest.fn();
    
    render(
      <Dropdown 
        options={mockOptions} 
        onValueChange={handleValueChange} 
      />
    );
    
    // Open dropdown
    const dropdownButton = screen.getByRole('combobox');
    fireEvent.click(dropdownButton);
    
    // Wait for dropdown content
    await waitFor(() => {
      expect(screen.getByText('Banana')).toBeInTheDocument();
    });
    
    // Select an option
    fireEvent.click(screen.getByText('Banana'));
    
    // Check if the dropdown button now shows the selected option
    expect(screen.getByRole('combobox')).toHaveTextContent('Banana');
    
    // Check if the onValueChange callback was called with the correct value
    expect(handleValueChange).toHaveBeenCalledWith('banana');
  });
  
  // Test default value
  test('displays defaultValue when provided', () => {
    render(
      <Dropdown 
        options={mockOptions} 
        defaultValue="banana" 
      />
    );
    
    expect(screen.getByRole('combobox')).toHaveTextContent('Banana');
  });
  
  // Test disabled state
  test('disables dropdown when disabled prop is true', () => {
    render(
      <Dropdown 
        options={mockOptions} 
        disabled={true} 
      />
    );
    
    const dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toBeDisabled();
  });
  
  // Test required attribute
  test('adds required indicator when required prop is true', () => {
    render(
      <Dropdown 
        id="test-dropdown" 
        options={mockOptions} 
        label="Test Label"
        required={true} 
      />
    );
    
    const dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toHaveAttribute('aria-required', 'true');
  });
  
  // Test searchable functionality
  test('shows search input when searchable is true', async () => {
    render(
      <Dropdown 
        options={mockOptions} 
        searchable={true} 
      />
    );
    
    // Open dropdown
    fireEvent.click(screen.getByRole('combobox'));
    
    // Check if search input appears
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search options...')).toBeInTheDocument();
    });
  });
  
  // Test clearable functionality
  test('clears selection when clearable is true and selected option is clicked again', async () => {
    const handleValueChange = jest.fn();
    
    render(
      <Dropdown 
        options={mockOptions} 
        defaultValue="banana"
        clearable={true}
        onValueChange={handleValueChange}
      />
    );
    
    // Initially the dropdown should show "Banana"
    expect(screen.getByRole('combobox')).toHaveTextContent('Banana');
    
    // Open dropdown
    fireEvent.click(screen.getByRole('combobox'));
    
    // Wait for dropdown content
    await waitFor(() => {
      expect(screen.getByText('Banana')).toBeInTheDocument();
    });
    
    // Click on "Banana" again to clear it
    fireEvent.click(screen.getByText('Banana'));
    
    // Check if the placeholder is shown again
    expect(handleValueChange).toHaveBeenCalledWith('');
  });
  
  // Test prefix icon
  test('renders prefix icon when provided', () => {
    render(
      <Dropdown 
        options={mockOptions}
        prefixIcon={<User data-testid="prefix-icon" />}
      />
    );
    
    const prefixIcon = screen.getByTestId('prefix-icon');
    expect(prefixIcon).toBeInTheDocument();
  });
  
  // Test option icons rendering
  test('renders option icons when showIcon is true', async () => {
    render(
      <Dropdown 
        options={mockOptionsWithIcons}
        defaultValue="user1"
        showIcon={true}
      />
    );
    
    // Check if the icon is rendered in the button
    expect(screen.getByTestId('user-icon')).toBeInTheDocument();
    
    // Open dropdown
    fireEvent.click(screen.getByRole('combobox'));
    
    // Check if icons are rendered in the dropdown
    await waitFor(() => {
      expect(screen.getAllByTestId('user-icon')).toHaveLength(2); // One in button, one in option
    });
  });
  
  // Test size variants
  test('applies correct size classes', () => {
    const { rerender } = render(
      <Dropdown 
        options={mockOptions} 
        size="sm"
      />
    );
    
    let dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toHaveClass('h-8');
    
    rerender(
      <Dropdown 
        options={mockOptions} 
        size="default"
      />
    );
    
    dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toHaveClass('h-10');
    
    rerender(
      <Dropdown 
        options={mockOptions} 
        size="lg"
      />
    );
    
    dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toHaveClass('h-12');
  });
  
  // Test variants
  test('applies correct variant classes', () => {
    const { rerender } = render(
      <Dropdown 
        options={mockOptions} 
        variant="default"
      />
    );
    
    let dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toHaveClass('bg-transparent');
    
    rerender(
      <Dropdown 
        options={mockOptions} 
        variant="blue"
      />
    );
    
    dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toHaveClass('bg-[#f8fafc]');
    expect(dropdownButton).toHaveClass('border-[#1E3A8A]');
    
    rerender(
      <Dropdown 
        options={mockOptions} 
        variant="white"
      />
    );
    
    dropdownButton = screen.getByRole('combobox');
    expect(dropdownButton).toHaveClass('bg-white');
    expect(dropdownButton).toHaveClass('border-[#e2e8f0]');
  });
  
  // Test custom classNames
  test('applies custom classNames to appropriate elements', () => {
    render(
      <Dropdown 
        options={mockOptions}
        className="test-class"
        triggerClassName="trigger-class"
        contentClassName="content-class"
      />
    );
    
    const button = screen.getByRole('combobox');
    expect(button).toHaveClass('test-class');
    expect(button).toHaveClass('trigger-class');
  });
});