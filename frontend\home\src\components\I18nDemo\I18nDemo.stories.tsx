import type { Meta, StoryObj } from '@storybook/react';
import { I18nDemo } from './index';

const meta: Meta<typeof I18nDemo> = {
  title: 'Components/I18n/I18nDemo',
  component: I18nDemo,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A demonstration component showing how i18n language switching works with the "Click Me" button. Use the global language switcher in the Storybook toolbar to change language.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof I18nDemo>;

export const English: Story = {
  parameters: {
    locale: 'en',
  },
};

export const French: Story = {
  parameters: {
    locale: 'fr',
  },
};

export const Japanese: Story = {
  parameters: {
    locale: 'ja',
  },
};
