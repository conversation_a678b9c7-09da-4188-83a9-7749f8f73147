import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FormProvider, useForm } from 'react-hook-form';
import { Create, Edit, ApiKeyPermissionSection } from '../../index';
import { sampleProjects, sampleApiKeyRestricted } from '../../__fixtures__/ApiKey.fixtures';

// Wrapper component to provide form context for ApiKeyPermissionSection
const PermissionSectionWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      permissionType: 'Restricted' as const,
      resourcePermissions: {
        models: 'Read' as const,
        modelCapabilities: 'Write' as const,
        assistants: 'None' as const,
        threads: 'None' as const,
        evals: 'None' as const,
        fineTuning: 'None' as const,
        files: 'Read' as const,
      },
    },
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('ApiKey Components - Keyboard Accessibility', () => {
  describe('Create Component Keyboard Navigation', () => {
    it('should allow complete navigation through the form using Tab key', async () => {
      const user = userEvent.setup();

      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Starting point - no element focused
      expect(document.activeElement).toBe(document.body);

      // Get all focusable elements for verification
      const focusableElements = Array.from(
        document.querySelectorAll<HTMLElement>(
          'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])',
        ),
      ).filter((el) => {
        // Filter out hidden elements
        const style = window.getComputedStyle(el);
        return !(style.display === 'none' || style.visibility === 'hidden');
      });

      // Ensure there are focusable elements
      expect(focusableElements.length).toBeGreaterThan(0);

      // Tab through all elements
      for (let i = 0; i < focusableElements.length; i++) {
        await user.tab();
        // Check each element gets focus in expected order
        expect(document.activeElement).toBe(focusableElements[i]);
      }

      // Tab once more should cycle back to first element
      await user.tab();
      expect(document.activeElement).toBe(focusableElements[0]);
    });

    it('should allow interaction with permission buttons using keyboard', async () => {
      const user = userEvent.setup();

      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // First find and click the Restricted permission type button to show resource permissions
      const restrictedButton = screen.getByText('Restricted');
      await user.click(restrictedButton);

      // Find a permission button (e.g., "Read" for Models)
      const permissionButtons = screen.getAllByText(/None|Read|Write/);
      expect(permissionButtons.length).toBeGreaterThan(0);

      // Tab until we reach the first permission button
      let currentButton = null;
      let attempts = 0;
      const maxAttempts = 50; // Prevent infinite loop

      while (!currentButton && attempts < maxAttempts) {
        await user.tab();
        for (const button of permissionButtons) {
          if (document.activeElement === button) {
            currentButton = button;
            break;
          }
        }
        attempts++;
      }

      // Ensure we found one of the permission buttons
      expect(currentButton).not.toBeNull();

      // Activate using Space key
      await user.keyboard(' ');

      // Verify the button was activated (should have primary class now)
      expect(currentButton).toHaveClass('bg-primary');
    });

    it('should allow form submission using keyboard', async () => {
      const onSubmitMock = jest.fn();
      const user = userEvent.setup();

      render(
        <Create onSubmit={onSubmitMock} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Find submit button
      const submitButton = screen.getByRole('button', { name: /Create/i });

      // Tab to submit button
      let found = false;
      let attempts = 0;
      const maxAttempts = 50; // Prevent infinite loop

      while (!found && attempts < maxAttempts) {
        await user.tab();
        if (document.activeElement === submitButton) {
          found = true;
        }
        attempts++;
      }

      // Submit form using Enter key
      await user.keyboard('{Enter}');

      // Verify submission was triggered
      expect(onSubmitMock).toHaveBeenCalled();
    });
  });

  describe('Edit Component Keyboard Navigation', () => {
    it('should allow complete navigation through the edit form using Tab key', async () => {
      const user = userEvent.setup();

      render(<Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />);

      // Starting point - no element focused
      expect(document.activeElement).toBe(document.body);

      // Get all focusable elements
      const focusableElements = Array.from(
        document.querySelectorAll<HTMLElement>(
          'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])',
        ),
      ).filter((el) => {
        // Filter out hidden elements
        const style = window.getComputedStyle(el);
        return !(style.display === 'none' || style.visibility === 'hidden');
      });

      // Ensure there are focusable elements
      expect(focusableElements.length).toBeGreaterThan(0);

      // Tab through all elements
      for (let i = 0; i < focusableElements.length; i++) {
        await user.tab();
        // Check each element gets focus in expected order
        expect(document.activeElement).toBe(focusableElements[i]);
      }
    });
  });

  describe('ApiKeyPermissionSection Keyboard Accessibility', () => {
    it('should allow keyboard navigation through permission options', async () => {
      const user = userEvent.setup();

      render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // Find permission buttons
      const permissionButtons = screen.getAllByText(/None|Read|Write/);
      expect(permissionButtons.length).toBeGreaterThan(0);

      // Tab through all buttons
      let buttonsFound = 0;
      let attempts = 0;
      const maxAttempts = 100; // Prevent infinite loop

      while (buttonsFound < permissionButtons.length && attempts < maxAttempts) {
        await user.tab();

        if (permissionButtons.includes(document.activeElement as HTMLElement)) {
          buttonsFound++;

          // Test activation
          await user.keyboard('{Enter}');
          expect(document.activeElement).toHaveClass('bg-primary');
        }

        attempts++;
      }

      // Ensure we found and activated at least some buttons
      expect(buttonsFound).toBeGreaterThan(0);
    });
  });

  describe('Focus Management', () => {
    it('should trap focus in modal components when present', async () => {
      // This would test a modal component if one exists in the API Key components
      // For demonstration, we're showing the pattern
      /* Example modal focus trap test
      const user = userEvent.setup();
      
      render(
        <div>
          <button>Outside Button</button>
          <div role="dialog" aria-modal="true">
            <button>Modal Button 1</button>
            <button>Modal Button 2</button>
            <button>Close</button>
          </div>
        </div>
      );
      
      // Focus should be trapped inside modal
      const modalButtons = screen.getAllByRole('button', { 
        name: /Modal Button|Close/i 
      });
      
      // Initial focus should be on first element in modal
      expect(document.activeElement).toBe(modalButtons[0]);
      
      // Tab through all modal buttons
      await user.tab();
      expect(document.activeElement).toBe(modalButtons[1]);
      await user.tab();
      expect(document.activeElement).toBe(modalButtons[2]);
      
      // Tab should cycle back to first modal button, not go outside
      await user.tab();
      expect(document.activeElement).toBe(modalButtons[0]);
      */
    });

    it('should maintain focus after actions that change content', async () => {
      const user = userEvent.setup();

      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Find permission type buttons
      const allButton = screen.getByText('All');
      const restrictedButton = screen.getByText('Restricted');

      // Click on Restricted to show permissions
      await user.click(restrictedButton);

      // Focus should be maintained on restricted button after click
      expect(document.activeElement).toBe(restrictedButton);

      // Click back to All to hide permissions
      await user.click(allButton);

      // Focus should be maintained on all button after click
      expect(document.activeElement).toBe(allButton);
    });
  });

  describe('Keyboard Shortcuts', () => {
    it('should not interfere with browser keyboard shortcuts', async () => {
      const user = userEvent.setup();

      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Mock preventDefault to track if it's called during keyboard events
      const preventDefaultMock = jest.fn();

      // Listen for keydown events
      const handleKeyDown = jest.fn((e) => {
        if (e.key === 'F5' || (e.key === 'r' && e.ctrlKey)) {
          e.preventDefault = preventDefaultMock;
        }
      });

      document.addEventListener('keydown', handleKeyDown);

      // Simulate refresh shortcuts
      await user.keyboard('{F5}');
      await user.keyboard('{Control>}r{/Control}');

      // Check that preventDefault was not called for browser shortcuts
      expect(preventDefaultMock).not.toHaveBeenCalled();

      // Cleanup
      document.removeEventListener('keydown', handleKeyDown);
    });
  });
});
