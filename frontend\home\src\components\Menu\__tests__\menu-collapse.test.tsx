import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MenuCollapse } from '../menu-collapse';
import { X } from 'lucide-react';

describe('MenuCollapse', () => {
  const mockOnOpenChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with the correct title', () => {
    render(
      <MenuCollapse title="Test Menu">
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByText('Test Menu')).toBeInTheDocument();
  });

  it('is collapsed by default when defaultOpen is not provided', () => {
    render(
      <MenuCollapse title="Test Menu">
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.queryByText('Test content')).not.toBeInTheDocument();
  });

  it('is expanded by default when defaultOpen is true', () => {
    render(
      <MenuCollapse title="Test Menu" defaultOpen={true}>
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('expands when the header is clicked', () => {
    render(
      <MenuCollapse title="Test Menu">
        <p>Test content</p>
      </MenuCollapse>
    );

    fireEvent.click(screen.getByText('Test Menu'));
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('collapses when the header is clicked while expanded', () => {
    render(
      <MenuCollapse title="Test Menu" defaultOpen={true}>
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Test Menu'));
    expect(screen.queryByText('Test content')).not.toBeInTheDocument();
  });

  it('renders header action when provided', () => {
    render(
      <MenuCollapse 
        title="Test Menu" 
        headerAction={<button aria-label="Clear">Clear</button>}
      >
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByText('Clear')).toBeInTheDocument();
  });

  it('calls onOpenChange when toggled in controlled mode', () => {
    render(
      <MenuCollapse 
        title="Test Menu" 
        open={false}
        onOpenChange={mockOnOpenChange}
      >
        <p>Test content</p>
      </MenuCollapse>
    );

    fireEvent.click(screen.getByText('Test Menu'));
    expect(mockOnOpenChange).toHaveBeenCalledWith(true);
  });

  it('respects the controlled open state', () => {
    const { rerender } = render(
      <MenuCollapse 
        title="Test Menu" 
        open={false}
        onOpenChange={mockOnOpenChange}
      >
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.queryByText('Test content')).not.toBeInTheDocument();

    rerender(
      <MenuCollapse 
        title="Test Menu" 
        open={true}
        onOpenChange={mockOnOpenChange}
      >
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies custom classNames correctly', () => {
    render(
      <MenuCollapse 
        title="Test Menu" 
        defaultOpen={true}
        className="custom-container"
        headerClassName="custom-header"
        contentClassName="custom-content"
      >
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByText('Test Menu').closest('div')?.parentElement).toHaveClass('custom-container');
    expect(screen.getByText('Test Menu').closest('div')).toHaveClass('custom-header');
    expect(screen.getByText('Test content').parentElement).toHaveClass('custom-content');
  });

  it('uses custom aria-label when provided', () => {
    render(
      <MenuCollapse 
        title="Test Menu" 
        ariaLabel="Custom menu label"
      >
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByLabelText('Custom menu label')).toBeInTheDocument();
  });

  it('uses custom icons when provided', () => {
    const CustomCollapsedIcon = () => <div data-testid="custom-collapsed-icon">▼</div>;
    const CustomExpandedIcon = () => <div data-testid="custom-expanded-icon">▲</div>;

    const { rerender } = render(
      <MenuCollapse 
        title="Test Menu" 
        collapsedIcon={<CustomCollapsedIcon />}
        expandedIcon={<CustomExpandedIcon />}
      >
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByTestId('custom-collapsed-icon')).toBeInTheDocument();

    rerender(
      <MenuCollapse 
        title="Test Menu" 
        defaultOpen={true}
        collapsedIcon={<CustomCollapsedIcon />}
        expandedIcon={<CustomExpandedIcon />}
      >
        <p>Test content</p>
      </MenuCollapse>
    );

    expect(screen.getByTestId('custom-expanded-icon')).toBeInTheDocument();
  });
});