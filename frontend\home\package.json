{"dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@faker-js/faker": "^9.6.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/postcss": "^4.0.14", "@tanstack/react-query": "^5.69.0", "@tanstack/react-table": "^8.21.2", "@tanstack/react-virtual": "^3.13.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.5.0", "i18next": "23.10.1", "i18next-browser-languagedetector": "7.2.0", "i18next-http-backend": "2.5.0", "input-otp": "^1.4.2", "js-beautify": "^1.15.4", "lucide-react": "^0.483.0", "luxon": "^3.5.0", "next": "^15.2.3", "next-themes": "^0.4.6", "prism-react-renderer": "^2.4.1", "react": "^19.0.0", "react-content-loader": "^7.0.2", "react-day-picker": "9.6.3", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "14.1.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "sonner": "^2.0.1", "storybook-addon-react-docgen": "^1.2.44", "swiper": "^11.2.6", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@chromatic-com/storybook": "^3.2.6", "@next/bundle-analyzer": "^15.2.3", "@storybook/addon-a11y": "^8.6.7", "@storybook/addon-actions": "^8.6.7", "@storybook/addon-backgrounds": "^8.6.7", "@storybook/addon-console": "^3.0.0", "@storybook/addon-controls": "^8.6.7", "@storybook/addon-coverage": "^1.0.5", "@storybook/addon-essentials": "^8.6.7", "@storybook/addon-interactions": "^8.6.7", "@storybook/addon-links": "^8.6.7", "@storybook/addon-measure": "^8.6.7", "@storybook/addon-onboarding": "^8.6.7", "@storybook/addon-outline": "^8.6.7", "@storybook/addon-storysource": "^8.6.7", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/addon-themes": "^8.6.7", "@storybook/addon-viewport": "^8.6.7", "@storybook/blocks": "^8.6.7", "@storybook/experimental-addon-test": "^8.6.7", "@storybook/nextjs": "^8.6.7", "@storybook/react": "^8.6.7", "@storybook/test": "^8.6.7", "@storybook/test-runner": "^0.22.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/i18next": "^13.0.0", "@types/jest": "^29.5.14", "@types/jest-axe": "^3.5.9", "@types/js-beautify": "^1.14.3", "@types/luxon": "^3.4.2", "@types/node": "^22.13.10", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-i18next": "^8.1.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@whitespace/storybook-addon-html": "^6.1.1", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "chromatic": "^11.27.0", "critters": "^0.0.25", "eslint": "^9.22.0", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^0.11.6", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.7.3", "msw-storybook-addon": "^2.0.4", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "serve": "^14.2.4", "storybook": "^8.6.7", "storybook-addon-pseudo-states": "^4.0.2", "storybook-addon-rtl": "^1.0.1", "storybook-css-modules": "^1.0.8", "storybook-react-i18next": "^3.2.1", "tailwindcss": "^4.0.14", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1"}, "msw": {"workerDirectory": ["public"]}, "name": "shadcn-timeline", "private": true, "scripts": {"analyze": "ANALYZE=true next build", "build": "next build", "build-storybook": "storybook build", "build:ssr": "rm -rf .next && next build", "chromatic": "npx chromatic --project-token=chpt_e81fb51e653743f", "dev": "next dev --turbo", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "lint": "next lint --fix", "lint:check": "eslint . --ext .js,.jsx,.ts,.tsx", "start": "next start", "storybook": "storybook dev -p 6006", "test": "jest", "test-storybook": "test-storybook", "test-storybook:coverage": "test-storybook --coverage"}, "type": "module", "version": "0.1.0"}