'use client';

import React, { useState } from 'react';
import { ProfileTableWithDetail } from '@/components/ProfileTableWithDetail';
import { mockProfiles } from '@/components/ProfileTableWithDetail/__fixtures__/ProfileTableWithDetail.mockData';

export default function ProfilesPage() {
  const [view, setView] = useState<'table' | 'detail'>('table');
  const [selectedProfileId, setSelectedProfileId] = useState<string | undefined>();
  
  // Demo profiles from mock data
  const [profiles, setProfiles] = useState(mockProfiles.slice(0, 20));
  
  // Handle view change
  const handleViewChange = (newView: 'table' | 'detail', profileId?: string) => {
    setView(newView);
    setSelectedProfileId(profileId);
  };
  
  // Handle profile deletion
  const handleProfileDelete = (profileId: string) => {
    setProfiles(prev => prev.filter(p => p.id !== profileId));
    if (selectedProfileId === profileId) {
      setView('table');
      setSelectedProfileId(undefined);
    }
  };
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Profiles</h1>
      
      <ProfileTableWithDetail
        initialData={profiles}
        view={view}
        selectedProfileId={selectedProfileId}
        onViewChange={handleViewChange}
        onProfileDelete={handleProfileDelete}
        pagination={{
          defaultPage: 1,
          defaultPageSize: 10,
          pageSizeOptions: [5, 10, 20, 50],
        }}
      />
    </div>
  );
} 