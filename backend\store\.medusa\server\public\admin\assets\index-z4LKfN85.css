html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:Inter,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}[hidden]{display:none}@font-face{font-family:Inter;font-weight:400;src:url(/app/assets/Inter-Regular-CKBOXRQ3-DYjygwQm.ttf) format("truetype")}@font-face{font-family:Inter;font-weight:500;src:url(/app/assets/Inter-Medium-RUQTUVA3-CKLJZXR2.ttf) format("truetype")}@font-face{font-family:Roboto Mono;font-weight:400;src:url(/app/assets/RobotoMono-Regular-27MA6W2G-44XoGH_Y.ttf) format("truetype")}@font-face{font-family:Roboto Mono;font-weight:500;src:url(/app/assets/RobotoMono-Medium-LUNBMOOH-B0UKl2Zn.ttf) format("truetype")}:root{background-color:var(--bg-subtle);color:var(--fg-base);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility}.size-1{width:.25rem;height:.25rem}.size-fit{width:-moz-fit-content;width:fit-content;height:-moz-fit-content;height:fit-content}.h-0{height:0px}.h-fit{height:-moz-fit-content;height:fit-content}.w-fit{width:-moz-fit-content;width:fit-content}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.resize-none{resize:none}.gap-x-0{-moz-column-gap:0px;column-gap:0px}.gap-x-0\.5{-moz-column-gap:.125rem;column-gap:.125rem}.gap-x-1{-moz-column-gap:.25rem;column-gap:.25rem}.gap-x-1\.5{-moz-column-gap:.375rem;column-gap:.375rem}.gap-x-2{-moz-column-gap:.5rem;column-gap:.5rem}.gap-x-2\.5{-moz-column-gap:.625rem;column-gap:.625rem}.gap-x-3{-moz-column-gap:.75rem;column-gap:.75rem}.gap-x-4{-moz-column-gap:1rem;column-gap:1rem}.gap-x-\[3px\]{-moz-column-gap:3px;column-gap:3px}.gap-y-0{row-gap:0px}.rounded-r-2xl{border-top-right-radius:1rem;border-bottom-right-radius:1rem}.rounded-bl-md{border-bottom-left-radius:.375rem}.rounded-tl-xl{border-top-left-radius:.75rem}.bg-\[\#3B82F6\]{--tw-bg-opacity: 1;background-color:rgb(59 130 246 / var(--tw-bg-opacity))}.bg-gray-200{--tw-bg-opacity: 1;background-color:rgb(229 231 235 / var(--tw-bg-opacity))}.object-contain{-o-object-fit:contain;object-fit:contain}.object-cover{-o-object-fit:cover;object-fit:cover}.object-center{-o-object-position:center;object-position:center}.text-blue-500{--tw-text-opacity: 1;color:rgb(59 130 246 / var(--tw-text-opacity))}.text-red-500{--tw-text-opacity: 1;color:rgb(239 68 68 / var(--tw-text-opacity))}.underline{text-decoration-line:underline}.\!placeholder-ui-fg-disabled::-moz-placeholder{color:var(--fg-disabled)!important}.placeholder-ui-fg-muted::-moz-placeholder{color:var(--fg-muted)}.shadow-none{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.fade-in-0{--tw-enter-opacity: 0}.worfklow-grid{background-image:radial-gradient(black 1px,transparent 0);background-size:40px 40px;background:repeat}.placeholder\:text-ui-fg-muted::-moz-placeholder{color:var(--fg-muted)}.hover\:text-blue-400:hover{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity))}.focus\:shadow-none:focus{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.disabled\:placeholder-ui-fg-disabled:disabled::-moz-placeholder{color:var(--fg-disabled)}.data-\[state\=open\]\:\!shadow-none[data-state=open]{--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}@media (min-width: 768px){.md\:flex{display:flex}.md\:hidden{display:none}.md\:w-\[720px\]{width:720px}.md\:w-auto{width:auto}.md\:w-fit{width:-moz-fit-content;width:fit-content}.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\:flex-row{flex-direction:row}.md\:flex-wrap{flex-wrap:wrap}.md\:items-center{align-items:center}.md\:justify-end{justify-content:flex-end}.md\:gap-y-0{row-gap:0px}.md\:p-0{padding:0}.md\:pt-24{padding-top:6rem}}*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:Inter,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:Roboto Mono,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}*{border-color:var(--border-base)}:root{--tag-neutral-border: rgba(228, 228, 231, 1);--tag-neutral-icon: rgba(161, 161, 170, 1);--bg-switch-off-hover: rgba(212, 212, 216, 1);--border-menu-bot: rgba(255, 255, 255, 1);--border-menu-top: rgba(228, 228, 231, 1);--bg-subtle-hover: rgba(244, 244, 245, 1);--contrast-fg-primary: rgba(255, 255, 255, .88);--bg-switch-off: rgba(228, 228, 231, 1);--contrast-bg-base-pressed: rgba(63, 63, 70, 1);--bg-field-component-hover: rgba(250, 250, 250, 1);--bg-base-pressed: rgba(228, 228, 231, 1);--tag-neutral-text: rgba(82, 82, 91, 1);--tag-red-text: rgba(159, 18, 57, 1);--contrast-bg-base: rgba(24, 24, 27, 1);--border-strong: rgba(212, 212, 216, 1);--contrast-border-base: rgba(255, 255, 255, .15);--bg-field: rgba(250, 250, 250, 1);--tag-blue-text: rgba(30, 64, 175, 1);--button-inverted-pressed: rgba(82, 82, 91, 1);--border-interactive: rgba(59, 130, 246, 1);--bg-base-hover: rgba(244, 244, 245, 1);--contrast-bg-subtle: rgba(39, 39, 42, 1);--bg-highlight: rgba(239, 246, 255, 1);--contrast-fg-secondary: rgba(255, 255, 255, .56);--tag-red-bg: rgba(255, 228, 230, 1);--button-transparent: rgba(255, 255, 255, 0);--button-danger-pressed: rgba(159, 18, 57, 1);--fg-on-color: rgba(255, 255, 255, 1);--button-inverted-hover: rgba(63, 63, 70, 1);--bg-field-component: rgba(255, 255, 255, 1);--tag-orange-text: rgba(154, 52, 18, 1);--tag-green-icon: rgba(16, 185, 129, 1);--border-base: rgba(228, 228, 231, 1);--bg-base: rgba(255, 255, 255, 1);--tag-orange-border: rgba(254, 215, 170, 1);--tag-red-border: rgba(254, 205, 211, 1);--tag-green-border: rgba(167, 243, 208, 1);--tag-green-text: rgba(6, 95, 70, 1);--button-neutral: rgba(255, 255, 255, 1);--tag-blue-border: rgba(191, 219, 254, 1);--fg-interactive-hover: rgba(37, 99, 235, 1);--tag-orange-icon: rgba(249, 115, 22, 1);--button-neutral-hover: rgba(244, 244, 245, 1);--fg-interactive: rgba(59, 130, 246, 1);--bg-component-pressed: rgba(228, 228, 231, 1);--tag-purple-bg: rgba(237, 233, 254, 1);--contrast-bg-base-hover: rgba(39, 39, 42, 1);--bg-component: rgba(250, 250, 250, 1);--bg-subtle: rgba(250, 250, 250, 1);--tag-purple-text: rgba(91, 33, 182, 1);--contrast-border-bot: rgba(255, 255, 255, .1);--button-inverted: rgba(39, 39, 42, 1);--tag-red-icon: rgba(244, 63, 94, 1);--button-transparent-hover: rgba(244, 244, 245, 1);--button-neutral-pressed: rgba(228, 228, 231, 1);--tag-purple-icon: rgba(167, 139, 250, 1);--bg-field-hover: rgba(244, 244, 245, 1);--fg-on-inverted: rgba(255, 255, 255, 1);--bg-interactive: rgba(59, 130, 246, 1);--border-danger: rgba(190, 18, 60, 1);--button-transparent-pressed: rgba(228, 228, 231, 1);--tag-purple-border: rgba(221, 214, 254, 1);--bg-highlight-hover: rgba(219, 234, 254, 1);--border-error: rgba(225, 29, 72, 1);--button-danger: rgba(225, 29, 72, 1);--tag-blue-bg: rgba(219, 234, 254, 1);--border-transparent: rgba(255, 255, 255, 0);--button-danger-hover: rgba(190, 18, 60, 1);--bg-subtle-pressed: rgba(228, 228, 231, 1);--fg-error: rgba(225, 29, 72, 1);--bg-component-hover: rgba(244, 244, 245, 1);--bg-disabled: rgba(244, 244, 245, 1);--tag-blue-icon: rgba(96, 165, 250, 1);--fg-subtle: rgba(82, 82, 91, 1);--tag-orange-bg-hover: rgba(254, 215, 170, 1);--tag-green-bg-hover: rgba(167, 243, 208, 1);--tag-red-bg-hover: rgba(254, 205, 211, 1);--tag-purple-bg-hover: rgba(221, 214, 254, 1);--tag-neutral-bg-hover: rgba(228, 228, 231, 1);--tag-blue-bg-hover: rgba(191, 219, 254, 1);--tag-green-bg: rgba(209, 250, 229, 1);--tag-neutral-bg: rgba(244, 244, 245, 1);--tag-orange-bg: rgba(255, 237, 213, 1);--fg-base: rgba(24, 24, 27, 1);--contrast-border-top: rgba(24, 24, 27, 1);--bg-overlay: rgba(24, 24, 27, .4);--fg-disabled: rgba(161, 161, 170, 1);--fg-muted: rgba(113, 113, 122, 1);--borders-interactive-with-active: 0px 0px 0px 1px rgba(59, 130, 246, 1), 0px 0px 0px 4px rgba(59, 130, 246, .2);--buttons-danger-focus: 0px .75px 0px 0px rgba(255, 255, 255, .2) inset, 0px 1px 2px 0px rgba(190, 18, 60, .4), 0px 0px 0px 1px rgba(190, 18, 60, 1), 0px 0px 0px 2px rgba(255, 255, 255, 1), 0px 0px 0px 4px rgba(59, 130, 246, .6);--details-contrast-on-bg-interactive: 0px 1px 2px 0px rgba(30, 58, 138, .6);--borders-interactive-with-focus: 0px 1px 2px 0px rgba(30, 58, 138, .5), 0px 0px 0px 1px rgba(59, 130, 246, 1), 0px 0px 0px 2px rgba(255, 255, 255, 1), 0px 0px 0px 4px rgba(59, 130, 246, .6);--borders-error: 0px 0px 0px 1px rgba(225, 29, 72, 1), 0px 0px 0px 3px rgba(225, 29, 72, .15);--borders-focus: 0px 0px 0px 1px rgba(255, 255, 255, 1), 0px 0px 0px 3px rgba(59, 130, 246, .6);--borders-interactive-with-shadow: 0px 1px 2px 0px rgba(30, 58, 138, .5), 0px 0px 0px 1px rgba(59, 130, 246, 1);--buttons-danger: 0px .75px 0px 0px rgba(255, 255, 255, .2) inset, 0px 1px 2px 0px rgba(190, 18, 60, .4), 0px 0px 0px 1px rgba(190, 18, 60, 1);--buttons-inverted-focus: 0px .75px 0px 0px rgba(255, 255, 255, .2) inset, 0px 1px 2px 0px rgba(0, 0, 0, .4), 0px 0px 0px 1px rgba(24, 24, 27, 1), 0px 0px 0px 2px rgba(255, 255, 255, 1), 0px 0px 0px 4px rgba(59, 130, 246, .6);--elevation-card-hover: 0px 0px 0px 1px rgba(0, 0, 0, .08), 0px 1px 2px -1px rgba(0, 0, 0, .08), 0px 2px 8px 0px rgba(0, 0, 0, .1);--details-switch-handle: 0px 0px 2px 1px rgba(255, 255, 255, 1) inset, 0px 1px 0px 0px rgba(255, 255, 255, 1) inset, 0px 0px 0px .5px rgba(0, 0, 0, .02), 0px 5px 4px 0px rgba(0, 0, 0, .02), 0px 3px 3px 0px rgba(0, 0, 0, .04), 0px 1px 2px 0px rgba(0, 0, 0, .12), 0px 0px 1px 0px rgba(0, 0, 0, .08);--buttons-neutral: 0px 1px 2px 0px rgba(0, 0, 0, .12), 0px 0px 0px 1px rgba(0, 0, 0, .08);--borders-base: 0px 1px 2px 0px rgba(0, 0, 0, .12), 0px 0px 0px 1px rgba(0, 0, 0, .08);--elevation-card-rest: 0px 0px 0px 1px rgba(0, 0, 0, .08), 0px 1px 2px -1px rgba(0, 0, 0, .08), 0px 2px 4px 0px rgba(0, 0, 0, .04);--buttons-neutral-focus: 0px 1px 2px 0px rgba(0, 0, 0, .12), 0px 0px 0px 1px rgba(0, 0, 0, .08), 0px 0px 0px 2px rgba(255, 255, 255, 1), 0px 0px 0px 4px rgba(59, 130, 246, .6);--details-switch-background-focus: 0px 0px 0px 1px rgba(255, 255, 255, 1), 0px 0px 0px 3px rgba(59, 130, 246, .6), 0px 1px 1px 0px rgba(0, 0, 0, .04) inset, 0px 2px 4px 0px rgba(0, 0, 0, .04) inset, 0px 0px 0px .75px rgba(0, 0, 0, .06) inset, 0px 0px 8px 0px rgba(0, 0, 0, .02) inset, 0px 2px 4px 0px rgba(0, 0, 0, .04);--details-switch-background: 0px 1px 1px 0px rgba(0, 0, 0, .04) inset, 0px 2px 4px 0px rgba(0, 0, 0, .04) inset, 0px 0px 0px .75px rgba(0, 0, 0, .06) inset, 0px 0px 8px 0px rgba(0, 0, 0, .02) inset, 0px 2px 4px 0px rgba(0, 0, 0, .04);--elevation-flyout: 0px 0px 0px 1px rgba(0, 0, 0, .08), 0px 4px 8px 0px rgba(0, 0, 0, .08), 0px 8px 16px 0px rgba(0, 0, 0, .08);--elevation-tooltip: 0px 0px 0px 1px rgba(0, 0, 0, .08), 0px 2px 4px 0px rgba(0, 0, 0, .08), 0px 4px 8px 0px rgba(0, 0, 0, .08);--elevation-modal: 0px 0px 0px 1px rgba(255, 255, 255, 1) inset, 0px 0px 0px 1.5px rgba(228, 228, 231, .6) inset, 0px 0px 0px 1px rgba(0, 0, 0, .08), 0px 8px 16px 0px rgba(0, 0, 0, .08), 0px 16px 32px 0px rgba(0, 0, 0, .08);--elevation-commandbar: 0px 0px 0px 1px rgba(39, 39, 42, 1) inset, 0px 0px 0px 1.5px rgba(255, 255, 255, .3) inset, 0px 8px 16px 0px rgba(0, 0, 0, .08), 0px 16px 32px 0px rgba(0, 0, 0, .08);--elevation-code-block: 0px 0px 0px 1px rgba(24, 24, 27, 1) inset, 0px 0px 0px 1.5px rgba(255, 255, 255, .2) inset;--buttons-inverted: 0px .75px 0px 0px rgba(255, 255, 255, .2) inset, 0px 1px 2px 0px rgba(0, 0, 0, .4), 0px 0px 0px 1px rgba(24, 24, 27, 1)}.dark{--button-danger-pressed: rgba(225, 29, 72, 1);--bg-base-pressed: rgba(63, 63, 70, 1);--bg-component-hover: rgba(255, 255, 255, .1);--border-interactive: rgba(96, 165, 250, 1);--button-neutral: rgba(255, 255, 255, .04);--tag-orange-border: rgba(124, 45, 18, 1);--tag-blue-text: rgba(147, 197, 253, 1);--bg-highlight: rgba(23, 37, 84, 1);--tag-neutral-icon: rgba(113, 113, 122, 1);--bg-switch-off-hover: rgba(82, 82, 91, 1);--fg-on-color: rgba(255, 255, 255, 1);--button-inverted-pressed: rgba(161, 161, 170, 1);--fg-interactive-hover: rgba(147, 197, 253, 1);--fg-error: rgba(251, 113, 133, 1);--bg-switch-off: rgba(63, 63, 70, 1);--border-strong: rgba(255, 255, 255, .16);--border-error: rgba(251, 113, 133, 1);--fg-subtle: rgba(161, 161, 170, 1);--bg-highlight-hover: rgba(30, 58, 138, 1);--button-inverted: rgba(82, 82, 91, 1);--tag-orange-text: rgba(253, 186, 116, 1);--fg-base: rgba(244, 244, 245, 1);--fg-disabled: rgba(82, 82, 91, 1);--button-danger: rgba(159, 18, 57, 1);--tag-neutral-border: rgba(255, 255, 255, .06);--tag-blue-border: rgba(30, 58, 138, 1);--tag-neutral-text: rgba(212, 212, 216, 1);--tag-purple-border: rgba(91, 33, 182, 1);--tag-green-text: rgba(52, 211, 153, 1);--button-inverted-hover: rgba(113, 113, 122, 1);--bg-component-pressed: rgba(255, 255, 255, .16);--contrast-border-bot: rgba(255, 255, 255, .08);--tag-blue-icon: rgba(96, 165, 250, 1);--bg-field: rgba(255, 255, 255, .04);--tag-neutral-bg: rgba(255, 255, 255, .08);--tag-green-border: rgba(6, 78, 59, 1);--tag-red-icon: rgba(251, 113, 133, 1);--tag-red-text: rgba(253, 164, 175, 1);--tag-purple-icon: rgba(167, 139, 250, 1);--bg-interactive: rgba(96, 165, 250, 1);--bg-field-hover: rgba(255, 255, 255, .08);--border-transparent: rgba(255, 255, 255, 0);--tag-orange-icon: rgba(251, 146, 60, 1);--tag-purple-bg: rgba(46, 16, 101, 1);--bg-base-hover: rgba(39, 39, 42, 1);--tag-blue-bg: rgba(23, 37, 84, 1);--tag-green-bg: rgba(2, 44, 34, 1);--tag-purple-text: rgba(196, 181, 253, 1);--tag-red-border: rgba(136, 19, 55, 1);--border-danger: rgba(190, 18, 60, 1);--tag-green-icon: rgba(16, 185, 129, 1);--tag-red-bg: rgba(76, 5, 25, 1);--fg-interactive: rgba(96, 165, 250, 1);--tag-orange-bg: rgba(67, 20, 7, 1);--button-danger-hover: rgba(190, 18, 60, 1);--bg-component: rgba(39, 39, 42, 1);--bg-disabled: rgba(39, 39, 42, 1);--button-transparent: rgba(255, 255, 255, 0);--border-menu-bot: rgba(255, 255, 255, .08);--tag-purple-bg-hover: rgba(91, 33, 182, 1);--tag-orange-bg-hover: rgba(124, 45, 18, 1);--tag-blue-bg-hover: rgba(30, 58, 138, 1);--tag-red-bg-hover: rgba(136, 19, 55, 1);--tag-green-bg-hover: rgba(6, 78, 59, 1);--border-menu-top: rgba(33, 33, 36, 1);--bg-base: rgba(33, 33, 36, 1);--contrast-border-top: rgba(33, 33, 36, 1);--bg-field-component: rgba(33, 33, 36, 1);--bg-subtle-hover: rgba(33, 33, 36, 1);--bg-subtle: rgba(24, 24, 27, 1);--fg-on-inverted: rgba(24, 24, 27, 1);--bg-overlay: rgba(24, 24, 27, .72);--button-transparent-hover: rgba(255, 255, 255, .08);--contrast-fg-secondary: rgba(255, 255, 255, .56);--contrast-border-base: rgba(255, 255, 255, .16);--contrast-bg-base-pressed: rgba(82, 82, 91, 1);--button-neutral-pressed: rgba(255, 255, 255, .12);--border-base: rgba(255, 255, 255, .08);--contrast-fg-primary: rgba(255, 255, 255, .88);--button-neutral-hover: rgba(255, 255, 255, .08);--contrast-bg-base: rgba(39, 39, 42, 1);--tag-neutral-bg-hover: rgba(255, 255, 255, .12);--contrast-bg-subtle: rgba(255, 255, 255, .04);--contrast-bg-base-hover: rgba(63, 63, 70, 1);--bg-field-component-hover: rgba(39, 39, 42, 1);--bg-subtle-pressed: rgba(39, 39, 42, 1);--button-transparent-pressed: rgba(255, 255, 255, .12);--fg-muted: rgba(113, 113, 122, 1);--borders-interactive-with-shadow: 0px 1px 2px 0px rgba(219, 234, 254, .5), 0px 0px 0px 1px rgba(96, 165, 250, 1);--details-contrast-on-bg-interactive: 0px 1px 2px 0px rgba(30, 58, 138, .6);--details-switch-handle: 0px 0px 2px 1px rgba(255, 255, 255, 1) inset, 0px 1px 0px 0px rgba(255, 255, 255, 1) inset, 0px 0px 0px .5px rgba(0, 0, 0, .16), 0px 5px 4px 0px rgba(0, 0, 0, .1), 0px 3px 3px 0px rgba(0, 0, 0, .1), 0px 1px 2px 0px rgba(0, 0, 0, .1), 0px 0px 1px 0px rgba(0, 0, 0, .1);--borders-interactive-with-active: 0px 0px 0px 1px rgba(96, 165, 250, 1), 0px 0px 0px 4px rgba(59, 130, 246, .25);--borders-focus: 0px 0px 0px 1px rgba(24, 24, 27, 1), 0px 0px 0px 3px rgba(96, 165, 250, .8);--borders-interactive-with-focus: 0px 1px 2px 0px rgba(219, 234, 254, .5), 0px 0px 0px 1px rgba(96, 165, 250, 1), 0px 0px 0px 2px rgba(24, 24, 27, 1), 0px 0px 0px 4px rgba(96, 165, 250, .8);--details-switch-background-focus: 0px 0px 0px 1px rgba(24, 24, 27, 1), 0px 0px 0px 3px rgba(96, 165, 250, .8), 0px 1px 1px 0px rgba(0, 0, 0, .1) inset, 0px 2px 4px 0px rgba(0, 0, 0, .1) inset, 0px 0px 0px .75px rgba(255, 255, 255, .12) inset, 0px 0px 8px 0px rgba(0, 0, 0, .1) inset;--buttons-danger: 0px -1px 0px 0px rgba(255, 255, 255, .16), 0px 0px 0px 1px rgba(255, 255, 255, .12), 0px 0px 0px 1px rgba(159, 18, 57, 1), 0px 0px 1px 1.5px rgba(0, 0, 0, .24), 0px 2px 2px 0px rgba(0, 0, 0, .24);--buttons-danger-focus: 0px -1px 0px 0px rgba(255, 255, 255, .16), 0px 0px 0px 1px rgba(255, 255, 255, .12), 0px 0px 0px 1px rgba(159, 18, 57, 1), 0px 0px 0px 2px rgba(24, 24, 27, 1), 0px 0px 0px 4px rgba(96, 165, 250, .8);--details-switch-background: 0px 1px 1px 0px rgba(0, 0, 0, .1) inset, 0px 2px 4px 0px rgba(0, 0, 0, .1) inset, 0px 0px 0px .75px rgba(255, 255, 255, .12) inset, 0px 0px 8px 0px rgba(0, 0, 0, .1) inset;--buttons-inverted-focus: 0px -1px 0px 0px rgba(255, 255, 255, .12), 0px 0px 0px 1px rgba(255, 255, 255, .12), 0px 0px 0px 1px rgba(82, 82, 91, 1), 0px 0px 0px 2px rgba(24, 24, 27, 1), 0px 0px 0px 4px rgba(96, 165, 250, .8);--elevation-tooltip: 0px -1px 0px 0px rgba(255, 255, 255, .04), 0px 0px 0px 1px rgba(255, 255, 255, .1), 0px 2px 4px 0px rgba(0, 0, 0, .32), 0px 4px 8px 0px rgba(0, 0, 0, .32);--elevation-flyout: 0px -1px 0px 0px rgba(255, 255, 255, .04), 0px 0px 0px 1px rgba(255, 255, 255, .1), 0px 4px 8px 0px rgba(0, 0, 0, .32), 0px 8px 16px 0px rgba(0, 0, 0, .32);--borders-error: 0px 0px 0px 1px rgba(244, 63, 94, 1), 0px 0px 0px 3px rgba(225, 29, 72, .25);--buttons-inverted: 0px -1px 0px 0px rgba(255, 255, 255, .12), 0px 0px 0px 1px rgba(255, 255, 255, .1), 0px 0px 0px 1px rgba(82, 82, 91, 1), 0px 0px 1px 1.5px rgba(0, 0, 0, .24), 0px 2px 2px 0px rgba(0, 0, 0, .24);--borders-base: 0px -1px 0px 0px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(39, 39, 42, 1), 0px 0px 1px 1.5px rgba(0, 0, 0, .24), 0px 2px 2px 0px rgba(0, 0, 0, .24);--elevation-card-hover: 0px -1px 0px 0px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(39, 39, 42, 1), 0px 1px 4px 0px rgba(0, 0, 0, .48), 0px 2px 8px 0px rgba(0, 0, 0, .48);--elevation-card-rest: 0px -1px 0px 0px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(39, 39, 42, 1), 0px 1px 2px 0px rgba(0, 0, 0, .32), 0px 2px 4px 0px rgba(0, 0, 0, .32);--buttons-neutral: 0px -1px 0px 0px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(39, 39, 42, 1), 0px 0px 1px 1.5px rgba(0, 0, 0, .24), 0px 2px 2px 0px rgba(0, 0, 0, .24);--elevation-code-block: 0px -1px 0px 0px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(39, 39, 42, 1), 0px 1px 2px 0px rgba(0, 0, 0, .32), 0px 2px 4px 0px rgba(0, 0, 0, .32);--buttons-neutral-focus: 0px -1px 0px 0px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(255, 255, 255, .06), 0px 0px 0px 1px rgba(39, 39, 42, 1), 0px 0px 0px 2px rgba(24, 24, 27, 1), 0px 0px 0px 4px rgba(96, 165, 250, .8);--elevation-commandbar: 0px 0px 0px 1px rgba(39, 39, 42, 1) inset, 0px 0px 0px 1.5px rgba(255, 255, 255, .1) inset, 0px 4px 8px 0px rgba(0, 0, 0, .32), 0px 8px 16px 0px rgba(0, 0, 0, .32);--elevation-modal: 0px 0px 0px 1px rgba(24, 24, 27, 1) inset, 0px 0px 0px 1.5px rgba(255, 255, 255, .06) inset, 0px -1px 0px 0px rgba(255, 255, 255, .04), 0px 0px 0px 1px rgba(255, 255, 255, .1), 0px 4px 8px 0px rgba(0, 0, 0, .32), 0px 8px 16px 0px rgba(0, 0, 0, .32)}.\!container{width:100%!important}.container{width:100%}@media (min-width: 640px){.\!container{max-width:640px!important}.container{max-width:640px}}@media (min-width: 768px){.\!container{max-width:768px!important}.container{max-width:768px}}@media (min-width: 1024px){.\!container{max-width:1024px!important}.container{max-width:1024px}}@media (min-width: 1280px){.\!container{max-width:1280px!important}.container{max-width:1280px}}@media (min-width: 1536px){.\!container{max-width:1536px!important}.container{max-width:1536px}}.h1-webs{font-size:4rem;line-height:4.400000095367432rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.h2-webs{font-size:3.5rem;line-height:3.8500001430511475rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.h3-webs{font-size:2.5rem;line-height:2.75rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-xlarge-plus{font-size:1.125rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-xsmall{font-size:.75rem;line-height:1.25rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-xsmall-plus{font-size:.75rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-xlarge{font-size:1.125rem;line-height:1.6875rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.\!txt-compact-small-plus{font-size:.8125rem!important;line-height:1.25rem!important;font-weight:500!important;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji!important}.txt-compact-small-plus{font-size:.8125rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-medium{font-size:.875rem;line-height:1.25rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-large-plus{font-size:1rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-medium{font-size:.875rem;line-height:1.3125rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-large{font-size:1rem;line-height:1.25rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-medium-plus{font-size:.875rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-xlarge{font-size:1.125rem;line-height:1.25rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-compact-small{font-size:.8125rem;line-height:1.25rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-xsmall-plus{font-size:.75rem;line-height:1.125rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-small{font-size:.8125rem;line-height:1.21875rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-small-plus{font-size:.8125rem;line-height:1.21875rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-large{font-size:1rem;line-height:1.5rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-medium-plus{font-size:.875rem;line-height:1.3125rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-xsmall{font-size:.75rem;line-height:1.125rem;font-weight:400;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-xlarge-plus{font-size:1.125rem;line-height:1.6875rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.txt-large-plus{font-size:1rem;line-height:1.5rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.code-label{font-size:.75rem;line-height:1.25rem;font-weight:400;font-family:Roboto Mono,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.h4-webs{font-size:1.5rem;line-height:1.875rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.code-body{font-size:.75rem;line-height:1.125rem;font-weight:400;font-family:Roboto Mono,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.h2-core{font-size:1rem;line-height:1.5rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.h3-core{font-size:.875rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.h1-core{font-size:1.125rem;line-height:1.75rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.h1-docs{font-size:1.5rem;line-height:1.875rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.h2-docs{font-size:1.125rem;line-height:1.6875rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.h3-docs{font-size:1rem;line-height:1.5rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.pointer-events-none{pointer-events:none}.visible{visibility:visible}.invisible{visibility:hidden}.static{position:static}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.sticky{position:sticky}.inset-0{top:0;right:0;bottom:0;left:0}.inset-2{top:.5rem;right:.5rem;bottom:.5rem;left:.5rem}.\!inset-x-5{left:1.25rem!important;right:1.25rem!important}.\!inset-y-3{top:.75rem!important;bottom:.75rem!important}.\!inset-y-5{top:1.25rem!important;bottom:1.25rem!important}.inset-x-0{left:0;right:0}.inset-y-0{top:0;bottom:0}.inset-y-2{top:.5rem;bottom:.5rem}.\!right-5{right:1.25rem!important}.\!top-6{top:1.5rem!important}.-left-2{left:-.5rem}.-right-2{right:-.5rem}.-right-2\.5{right:-.625rem}.-right-\[5px\]{right:-5px}.-top-1{top:-.25rem}.-top-2{top:-.5rem}.bottom-0{bottom:0}.bottom-4{bottom:1rem}.bottom-8{bottom:2rem}.bottom-\[3px\]{bottom:3px}.left-0{left:0}.left-0\.5{left:.125rem}.left-1\/2{left:50%}.left-2{left:.5rem}.left-4{left:1rem}.left-6{left:1.5rem}.left-\[1100px\]{left:1100px}.left-\[40px\]{left:40px}.left-\[50\%\]{left:50%}.left-\[68px\],.left-\[calc\(20px\+24px\+24px\)\]{left:68px}.left-\[calc\(var\(--tag-width\)\+8px\)\]{left:calc(var(--tag-width) + 8px)}.right-0{right:0}.right-2{right:.5rem}.right-4{right:1rem}.right-\[28px\]{right:28px}.top-0{top:0}.top-0\.5{top:.125rem}.top-1\/2{top:50%}.top-2{top:.5rem}.top-\[1100px\]{top:1100px}.top-\[50\%\]{top:50%}.isolate{isolation:isolate}.z-10{z-index:10}.z-50{z-index:50}.z-\[-1\]{z-index:-1}.z-\[1\]{z-index:1}.z-\[2\]{z-index:2}.z-\[3\]{z-index:3}.float-left{float:left}.-m-4{margin:-1rem}.m-4{margin:1rem}.-mx-1{margin-left:-.25rem;margin-right:-.25rem}.-my-2{margin-top:-.5rem;margin-bottom:-.5rem}.mx-1{margin-left:.25rem;margin-right:.25rem}.mx-2{margin-left:.5rem;margin-right:.5rem}.mx-auto{margin-left:auto;margin-right:auto}.my-1{margin-top:.25rem;margin-bottom:.25rem}.my-2{margin-top:.5rem;margin-bottom:.5rem}.my-6{margin-top:1.5rem;margin-bottom:1.5rem}.my-8{margin-top:2rem;margin-bottom:2rem}.my-auto{margin-top:auto;margin-bottom:auto}.\!mt-1{margin-top:.25rem!important}.-mb-px{margin-bottom:-1px}.-ml-\[0\.5px\]{margin-left:-.5px}.-ml-\[5px\]{margin-left:-5px}.-mr-3{margin-right:-.75rem}.-mr-\[7px\]{margin-right:-7px}.-mt-\[1px\]{margin-top:-1px}.-mt-\[3px\]{margin-top:-3px}.-mt-\[6px\]{margin-top:-6px}.mb-1{margin-bottom:.25rem}.mb-2{margin-bottom:.5rem}.mb-3{margin-bottom:.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-\[-6px\]{margin-bottom:-6px}.ml-1{margin-left:.25rem}.ml-10{margin-left:2.5rem}.ml-2{margin-left:.5rem}.ml-4{margin-left:1rem}.ml-auto{margin-left:auto}.mr-1{margin-right:.25rem}.mr-2{margin-right:.5rem}.mr-4{margin-right:1rem}.mt-0{margin-top:0}.mt-1{margin-top:.25rem}.mt-16{margin-top:4rem}.mt-2{margin-top:.5rem}.mt-3{margin-top:.75rem}.mt-4{margin-top:1rem}.mt-6{margin-top:1.5rem}.mt-8{margin-top:2rem}.mt-\[1\.5px\]{margin-top:1.5px}.mt-\[2px\]{margin-top:2px}.box-border{box-sizing:border-box}.block{display:block}.inline-block{display:inline-block}.\!inline{display:inline!important}.inline{display:inline}.flex{display:flex}.inline-flex{display:inline-flex}.table{display:table}.grid{display:grid}.contents{display:contents}.hidden{display:none}.aspect-square{aspect-ratio:1 / 1}.\!size-5{width:1.25rem!important;height:1.25rem!important}.size-1\.5{width:.375rem;height:.375rem}.size-10{width:2.5rem;height:2.5rem}.size-11{width:2.75rem;height:2.75rem}.size-12{width:3rem;height:3rem}.size-14{width:3.5rem;height:3.5rem}.size-2{width:.5rem;height:.5rem}.size-2\.5{width:.625rem;height:.625rem}.size-4{width:1rem;height:1rem}.size-5{width:1.25rem;height:1.25rem}.size-6{width:1.5rem;height:1.5rem}.size-7{width:1.75rem;height:1.75rem}.size-8{width:2rem;height:2rem}.size-9{width:2.25rem;height:2.25rem}.size-\[15px\]{width:15px;height:15px}.size-\[3px\]{width:3px;height:3px}.size-\[500rem\]{width:500rem;height:500rem}.size-fit{width:fit-content;height:fit-content}.size-full{width:100%;height:100%}.\!h-5{height:1.25rem!important}.\!h-6{height:1.5rem!important}.h-0\.5{height:.125rem}.h-1{height:.25rem}.h-1\.5{height:.375rem}.h-10{height:2.5rem}.h-12{height:3rem}.h-2{height:.5rem}.h-3{height:.75rem}.h-3\.5{height:.875rem}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-60{height:15rem}.h-7{height:1.75rem}.h-8{height:2rem}.h-\[120px\]{height:120px}.h-\[12px\]{height:12px}.h-\[148ox\]{height:148ox}.h-\[14px\]{height:14px}.h-\[15px\]{height:15px}.h-\[16px\]{height:16px}.h-\[180px\]{height:180px}.h-\[18px\]{height:18px}.h-\[1px\]{height:1px}.h-\[28px\]{height:28px}.h-\[300px\]{height:300px}.h-\[400px\]{height:400px}.h-\[50px\]{height:50px}.h-\[52px\]{height:52px}.h-\[56px\]{height:56px}.h-\[60px\]{height:60px}.h-\[9px\]{height:9px}.h-\[var\(--radix-select-trigger-height\)\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-fit{height:fit-content}.h-full{height:100%}.h-px{height:1px}.h-screen{height:100vh}.max-h-\[140px\]{max-height:140px}.max-h-\[163px\]{max-height:163px}.max-h-\[200px\]{max-height:200px}.max-h-\[219px\]{max-height:219px}.max-h-\[300px\]{max-height:300px}.max-h-\[320px\]{max-height:320px}.max-h-\[360px\]{max-height:360px}.max-h-\[460px\]{max-height:460px}.max-h-\[557px\]{max-height:557px}.max-h-\[612px\]{max-height:612px}.max-h-\[calc\(100\%-16px\)\]{max-height:calc(100% - 16px)}.max-h-\[calc\(100vh-200px\)\]{max-height:calc(100vh - 200px)}.max-h-\[var\(--radix-popper-available-height\)\]{max-height:var(--radix-popper-available-height)}.max-h-full{max-height:100%}.min-h-0{min-height:0px}.min-h-10{min-height:2.5rem}.min-h-7{min-height:1.75rem}.min-h-8{min-height:2rem}.min-h-\[0\]{min-height:0}.min-h-\[14px\]{min-height:14px}.min-h-\[236px\]{min-height:236px}.min-h-\[24px\]{min-height:24px}.min-h-\[250px\]{min-height:250px}.min-h-\[60px\]{min-height:60px}.min-h-\[calc\(100vh-57px-24px\)\]{min-height:calc(100vh - 81px)}.min-h-dvh{min-height:100dvh}.min-h-screen{min-height:100vh}.w-1{width:.25rem}.w-1\.5{width:.375rem}.w-10{width:2.5rem}.w-11{width:2.75rem}.w-12{width:3rem}.w-14{width:3.5rem}.w-16{width:4rem}.w-2{width:.5rem}.w-2\.5{width:.625rem}.w-24{width:6rem}.w-3{width:.75rem}.w-3\.5{width:.875rem}.w-4{width:1rem}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-7{width:1.75rem}.w-8{width:2rem}.w-\[116px\]{width:116px}.w-\[120px\]{width:120px}.w-\[128px\]{width:128px}.w-\[12px\]{width:12px}.w-\[138px\]{width:138px}.w-\[14px\]{width:14px}.w-\[15px\]{width:15px}.w-\[160px\]{width:160px}.w-\[164px\]{width:164px}.w-\[180px\]{width:180px}.w-\[1px\]{width:1px}.w-\[200px\]{width:200px}.w-\[220px\]{width:220px}.w-\[28px\]{width:28px}.w-\[2px\]{width:2px}.w-\[300px\]{width:300px}.w-\[30px\]{width:30px}.w-\[32px\]{width:32px}.w-\[46px\]{width:46px}.w-\[50px\]{width:50px}.w-\[59px\]{width:59px}.w-\[60px\]{width:60px}.w-\[66px\]{width:66px}.w-\[67px\]{width:67px}.w-\[70px\]{width:70px}.w-\[720px\]{width:720px}.w-\[calc\(100\%-16px\)\]{width:calc(100% - 16px)}.w-\[calc\(20px\+24px\+24px\)\]{width:68px}.w-\[calc\(28px\+24px\+4px\)\]{width:56px}.w-\[var\(--radix-dropdown-menu-trigger-width\)\]{width:var(--radix-dropdown-menu-trigger-width)}.w-\[var\(--radix-popper-anchor-width\)\]{width:var(--radix-popper-anchor-width)}.w-auto{width:auto}.w-dvw{width:100dvw}.w-fit{width:fit-content}.w-full{width:100%}.w-px{width:1px}.min-w-0{min-width:0px}.min-w-4{min-width:1rem}.min-w-\[120px\]{min-width:120px}.min-w-\[20px\]{min-width:20px}.min-w-\[220px\]{min-width:220px}.min-w-\[27px\]{min-width:27px}.min-w-\[32px\]{min-width:32px}.min-w-\[360px\]{min-width:360px}.min-w-\[60px\]{min-width:60px}.min-w-\[720px\]{min-width:720px}.min-w-\[calc\(20px\+24px\+24px\)\]{min-width:68px}.min-w-\[calc\(28px\+24px\+4px\)\]{min-width:56px}.min-w-\[var\(--radix-dropdown-menu-trigger-width\)\]{min-width:var(--radix-dropdown-menu-trigger-width)}.min-w-\[var\(--radix-select-trigger-width\)\]{min-width:var(--radix-select-trigger-width)}.max-w-2xl{max-width:42rem}.max-w-48{max-width:12rem}.max-w-\[100\%\]{max-width:100%}.max-w-\[135px\]{max-width:135px}.max-w-\[140px\]{max-width:140px}.max-w-\[1600px\]{max-width:1600px}.max-w-\[187px\]{max-width:187px}.max-w-\[192px\]{max-width:192px}.max-w-\[200px\]{max-width:200px}.max-w-\[220px\]{max-width:220px}.max-w-\[250px\]{max-width:250px}.max-w-\[260px\]{max-width:260px}.max-w-\[280px\]{max-width:280px}.max-w-\[294px\]{max-width:294px}.max-w-\[300px\]{max-width:300px}.max-w-\[304px\]{max-width:304px}.max-w-\[360px\]{max-width:360px}.max-w-\[400px\]{max-width:400px}.max-w-\[42\.5\%\]{max-width:42.5%}.max-w-\[440px\]{max-width:440px}.max-w-\[560px\]{max-width:560px}.max-w-\[60\%\]{max-width:60%}.max-w-\[600px\]{max-width:600px}.max-w-\[720px\]{max-width:720px}.max-w-\[736px\]{max-width:736px}.max-w-\[90\%\]{max-width:90%}.max-w-\[calc\(20px\+24px\+24px\)\]{max-width:68px}.max-w-\[calc\(28px\+24px\+4px\)\]{max-width:56px}.max-w-\[var\(--radix-dropdown-menu-trigger-width\)\]{max-width:var(--radix-dropdown-menu-trigger-width)}.max-w-full{max-width:100%}.flex-1{flex:1 1 0%}.flex-none{flex:none}.flex-shrink{flex-shrink:1}.flex-shrink-0,.shrink-0{flex-shrink:0}.flex-grow,.grow{flex-grow:1}.grow-0{flex-grow:0}.basis-1\/2{flex-basis:50%}.origin-top-left{transform-origin:top left}.-translate-x-1\/2{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1{--tw-translate-y: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.-translate-y-1\/2{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.translate-x-\[-50\%\]{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.translate-y-\[-50\%\]{--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.rotate-90{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.\!transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))!important}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes pulse{50%{opacity:.5}}.animate-pulse{animation:pulse 2s cubic-bezier(.4,0,.6,1) infinite}@keyframes spin{to{transform:rotate(360deg)}}.animate-spin{animation:spin 1s linear infinite}.cursor-auto{cursor:auto}.cursor-default{cursor:default}.cursor-grab{cursor:grab}.cursor-grabbing{cursor:grabbing}.cursor-not-allowed{cursor:not-allowed}.cursor-ns-resize{cursor:ns-resize}.cursor-nwse-resize{cursor:nwse-resize}.cursor-pointer{cursor:pointer}.cursor-text{cursor:text}.touch-none{touch-action:none}.select-none{-webkit-user-select:none;user-select:none}.list-inside{list-style-position:inside}.list-none{list-style-type:none}.list-image-none{list-style-image:none}.appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.auto-rows-auto{grid-auto-rows:auto}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-\[15px_1fr\]{grid-template-columns:15px 1fr}.grid-cols-\[1fr_1fr_1fr_20px\]{grid-template-columns:1fr 1fr 1fr 20px}.grid-cols-\[1fr_1fr_28px\]{grid-template-columns:1fr 1fr 28px}.grid-cols-\[1fr_1fr_auto\]{grid-template-columns:1fr 1fr auto}.grid-cols-\[1fr_20px\]{grid-template-columns:1fr 20px}.grid-cols-\[1fr_28px\]{grid-template-columns:1fr 28px}.grid-cols-\[1fr_32px\]{grid-template-columns:1fr 32px}.grid-cols-\[20px_1fr\]{grid-template-columns:20px 1fr}.grid-cols-\[20px_1fr_20px\]{grid-template-columns:20px 1fr 20px}.grid-cols-\[24px_1fr_15px\]{grid-template-columns:24px 1fr 15px}.grid-cols-\[28px_1fr\]{grid-template-columns:28px 1fr}.grid-cols-\[28px_1fr_28px\]{grid-template-columns:28px 1fr 28px}.grid-cols-\[32px_1fr\]{grid-template-columns:32px 1fr}.grid-cols-\[32px_1fr_32px\]{grid-template-columns:32px 1fr 32px}.grid-cols-\[4px_1fr\]{grid-template-columns:4px 1fr}.grid-cols-\[auto\,1fr\]{grid-template-columns:auto 1fr}.grid-cols-\[min-content\,1fr\]{grid-template-columns:min-content 1fr}.grid-cols-\[repeat\(auto-fill\,minmax\(96px\,1fr\)\)\]{grid-template-columns:repeat(auto-fill,minmax(96px,1fr))}.grid-rows-2{grid-template-rows:repeat(2,minmax(0,1fr))}.grid-rows-4{grid-template-rows:repeat(4,minmax(0,1fr))}.grid-rows-\[20px_1fr\]{grid-template-rows:20px 1fr}.flex-row{flex-direction:row}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-wrap{flex-wrap:wrap}.flex-nowrap{flex-wrap:nowrap}.items-start{align-items:flex-start}.items-end{align-items:flex-end}.items-center{align-items:center}.items-stretch{align-items:stretch}.justify-start{justify-content:flex-start}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.justify-around{justify-content:space-around}.gap-1{gap:.25rem}.gap-1\.5{gap:.375rem}.gap-2{gap:.5rem}.gap-3{gap:.75rem}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.gap-8{gap:2rem}.gap-x-0\.5{column-gap:.125rem}.gap-x-1{column-gap:.25rem}.gap-x-1\.5{column-gap:.375rem}.gap-x-2{column-gap:.5rem}.gap-x-2\.5{column-gap:.625rem}.gap-x-3{column-gap:.75rem}.gap-x-4{column-gap:1rem}.gap-x-\[3px\]{column-gap:3px}.gap-y-0\.5{row-gap:.125rem}.gap-y-1{row-gap:.25rem}.gap-y-1\.5{row-gap:.375rem}.gap-y-2{row-gap:.5rem}.gap-y-3{row-gap:.75rem}.gap-y-4{row-gap:1rem}.gap-y-6{row-gap:1.5rem}.gap-y-8{row-gap:2rem}.space-x-2>:not([hidden])~:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-0>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(0px * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0px * var(--tw-space-y-reverse))}.space-y-2>:not([hidden])~:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.divide-x>:not([hidden])~:not([hidden]){--tw-divide-x-reverse: 0;border-right-width:calc(1px * var(--tw-divide-x-reverse));border-left-width:calc(1px * calc(1 - var(--tw-divide-x-reverse)))}.divide-y>:not([hidden])~:not([hidden]){--tw-divide-y-reverse: 0;border-top-width:calc(1px * calc(1 - var(--tw-divide-y-reverse)));border-bottom-width:calc(1px * var(--tw-divide-y-reverse))}.divide-dashed>:not([hidden])~:not([hidden]){border-style:dashed}.self-start{align-self:flex-start}.self-end{align-self:flex-end}.self-center{align-self:center}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-visible{overflow:visible}.overflow-x-auto{overflow-x:auto}.overflow-y-auto{overflow-y:auto}.overflow-x-hidden{overflow-x:hidden}.overscroll-none{overscroll-behavior:none}.truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.text-ellipsis{text-overflow:ellipsis}.whitespace-nowrap{white-space:nowrap}.whitespace-pre-line{white-space:pre-line}.whitespace-pre-wrap{white-space:pre-wrap}.text-nowrap{text-wrap:nowrap}.text-balance{text-wrap:balance}.text-pretty{text-wrap:pretty}.break-words{overflow-wrap:break-word}.break-all{word-break:break-all}.rounded{border-radius:.25rem}.rounded-\[10px\]{border-radius:10px}.rounded-\[1px\]{border-radius:1px}.rounded-\[3px\]{border-radius:3px}.rounded-\[4px\]{border-radius:4px}.rounded-\[8px\]{border-radius:8px}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:.5rem}.rounded-md{border-radius:.375rem}.rounded-none{border-radius:0}.rounded-sm{border-radius:.125rem}.rounded-xl{border-radius:.75rem}.rounded-b-\[4px\]{border-bottom-right-radius:4px;border-bottom-left-radius:4px}.rounded-b-lg{border-bottom-right-radius:.5rem;border-bottom-left-radius:.5rem}.rounded-b-xl{border-bottom-right-radius:.75rem;border-bottom-left-radius:.75rem}.rounded-r{border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}.rounded-t-lg{border-top-left-radius:.5rem;border-top-right-radius:.5rem}.border{border-width:1px}.border-\[0\.5px\]{border-width:.5px}.border-y{border-top-width:1px;border-bottom-width:1px}.border-b{border-bottom-width:1px}.border-b-0{border-bottom-width:0px}.border-b-2{border-bottom-width:2px}.border-l{border-left-width:1px}.border-l-4{border-left-width:4px}.border-r{border-right-width:1px}.border-t{border-top-width:1px}.border-t-0{border-top-width:0px}.border-t-2{border-top-width:2px}.border-dashed{border-style:dashed}.border-dotted{border-style:dotted}.border-none{border-style:none}.\!border-ui-border-error{border-color:var(--border-error)!important}.\!border-ui-border-interactive{border-color:var(--border-interactive)!important}.border-\[rgba\(3\,7\,18\,0\.2\)\]{border-color:#03071233}.border-transparent{border-color:transparent}.border-ui-border-base{border-color:var(--border-base)}.border-ui-border-interactive{border-color:var(--border-interactive)}.border-ui-border-strong{border-color:var(--border-strong)}.border-ui-contrast-border-base{border-color:var(--contrast-border-base)}.border-ui-contrast-border-bot{border-color:var(--contrast-border-bot)}.border-ui-tag-blue-border{border-color:var(--tag-blue-border)}.border-ui-tag-green-border{border-color:var(--tag-green-border)}.border-ui-tag-neutral-border{border-color:var(--tag-neutral-border)}.border-ui-tag-orange-border{border-color:var(--tag-orange-border)}.border-ui-tag-purple-border{border-color:var(--tag-purple-border)}.border-ui-tag-red-border{border-color:var(--tag-red-border)}.border-b-ui-border-menu-bot{border-bottom-color:var(--border-menu-bot)}.border-r-ui-border-base{border-right-color:var(--border-base)}.border-t-ui-border-menu-top{border-top-color:var(--border-menu-top)}.\!bg-ui-bg-disabled{background-color:var(--bg-disabled)!important}.\!bg-ui-bg-interactive{background-color:var(--bg-interactive)!important}.bg-\[\#3B82F6\]{--tw-bg-opacity: 1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1))}.bg-gray-200{--tw-bg-opacity: 1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-transparent{background-color:transparent}.bg-ui-bg-base{background-color:var(--bg-base)}.bg-ui-bg-base-hover{background-color:var(--bg-base-hover)}.bg-ui-bg-base-pressed{background-color:var(--bg-base-pressed)}.bg-ui-bg-component{background-color:var(--bg-component)}.bg-ui-bg-component-hover{background-color:var(--bg-component-hover)}.bg-ui-bg-component-pressed{background-color:var(--bg-component-pressed)}.bg-ui-bg-disabled{background-color:var(--bg-disabled)}.bg-ui-bg-field{background-color:var(--bg-field)}.bg-ui-bg-field-component{background-color:var(--bg-field-component)}.bg-ui-bg-field-component-hover{background-color:var(--bg-field-component-hover)}.bg-ui-bg-field-hover{background-color:var(--bg-field-hover)}.bg-ui-bg-highlight{background-color:var(--bg-highlight)}.bg-ui-bg-highlight-hover{background-color:var(--bg-highlight-hover)}.bg-ui-bg-interactive{background-color:var(--bg-interactive)}.bg-ui-bg-overlay{background-color:var(--bg-overlay)}.bg-ui-bg-subtle{background-color:var(--bg-subtle)}.bg-ui-bg-subtle-hover{background-color:var(--bg-subtle-hover)}.bg-ui-bg-switch-off{background-color:var(--bg-switch-off)}.bg-ui-border-base{background-color:var(--border-base)}.bg-ui-border-menu-bot{background-color:var(--border-menu-bot)}.bg-ui-border-menu-top{background-color:var(--border-menu-top)}.bg-ui-border-strong{background-color:var(--border-strong)}.bg-ui-button-danger{background-color:var(--button-danger)}.bg-ui-button-inverted{background-color:var(--button-inverted)}.bg-ui-button-neutral{background-color:var(--button-neutral)}.bg-ui-button-neutral-pressed{background-color:var(--button-neutral-pressed)}.bg-ui-button-transparent{background-color:var(--button-transparent)}.bg-ui-contrast-bg-base{background-color:var(--contrast-bg-base)}.bg-ui-contrast-bg-subtle{background-color:var(--contrast-bg-subtle)}.bg-ui-contrast-border-base{background-color:var(--contrast-border-base)}.bg-ui-contrast-border-top{background-color:var(--contrast-border-top)}.bg-ui-contrast-fg-primary{background-color:var(--contrast-fg-primary)}.bg-ui-fg-interactive{background-color:var(--fg-interactive)}.bg-ui-fg-muted{background-color:var(--fg-muted)}.bg-ui-fg-on-color{background-color:var(--fg-on-color)}.bg-ui-fg-subtle{background-color:var(--fg-subtle)}.bg-ui-tag-blue-bg{background-color:var(--tag-blue-bg)}.bg-ui-tag-blue-icon{background-color:var(--tag-blue-icon)}.bg-ui-tag-green-bg{background-color:var(--tag-green-bg)}.bg-ui-tag-green-icon{background-color:var(--tag-green-icon)}.bg-ui-tag-neutral-bg{background-color:var(--tag-neutral-bg)}.bg-ui-tag-neutral-icon{background-color:var(--tag-neutral-icon)}.bg-ui-tag-orange-bg{background-color:var(--tag-orange-bg)}.bg-ui-tag-orange-icon{background-color:var(--tag-orange-icon)}.bg-ui-tag-purple-bg{background-color:var(--tag-purple-bg)}.bg-ui-tag-purple-icon{background-color:var(--tag-purple-icon)}.bg-ui-tag-red-bg{background-color:var(--tag-red-bg)}.bg-ui-tag-red-icon{background-color:var(--tag-red-icon)}.bg-\[linear-gradient\(0deg\,var\(--border-strong\)_1px\,transparent_1px\)\]{background-image:linear-gradient(0deg,var(--border-strong) 1px,transparent 1px)}.bg-\[linear-gradient\(90deg\,var\(--border-strong\)_1px\,transparent_1px\)\]{background-image:linear-gradient(90deg,var(--border-strong) 1px,transparent 1px)}.bg-\[linear-gradient\(var\(--border-strong\)_33\%\,rgba\(255\,255\,255\,0\)_0\%\)\]{background-image:linear-gradient(var(--border-strong) 33%,rgba(255,255,255,0) 0%)}.bg-\[radial-gradient\(var\(--border-base\)_1\.5px\,transparent_0\)\]{background-image:radial-gradient(var(--border-base) 1.5px,transparent 0)}.bg-gradient-to-b{background-image:linear-gradient(to bottom,var(--tw-gradient-stops))}.from-white\/0{--tw-gradient-from: rgb(255 255 255 / 0) var(--tw-gradient-from-position);--tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)}.to-white\/20{--tw-gradient-to: rgb(255 255 255 / .2) var(--tw-gradient-to-position)}.bg-\[length\:1px_3px\]{background-size:1px 3px}.bg-\[length\:1px_4px\]{background-size:1px 4px}.bg-\[length\:20px_20px\]{background-size:20px 20px}.bg-\[length\:4px_1px\]{background-size:4px 1px}.bg-right{background-position:right}.bg-repeat{background-repeat:repeat}.bg-repeat-y{background-repeat:repeat-y}.fill-ui-bg-base{fill:var(--bg-base)}.fill-ui-button-inverted{fill:var(--button-inverted)}.fill-ui-fg-muted{fill:var(--fg-muted)}.fill-ui-fg-subtle{fill:var(--fg-subtle)}.stroke-ui-fg-subtle{stroke:var(--fg-subtle)}.object-contain{object-fit:contain}.object-cover{object-fit:cover}.object-center{object-position:center}.p-0{padding:0}.p-0\.5{padding:.125rem}.p-1{padding:.25rem}.p-1\.5{padding:.375rem}.p-16{padding:4rem}.p-2{padding:.5rem}.p-2\.5{padding:.625rem}.p-3{padding:.75rem}.p-3\.5{padding:.875rem}.p-4{padding:1rem}.p-5{padding:1.25rem}.p-6{padding:1.5rem}.p-8{padding:2rem}.px-0{padding-left:0;padding-right:0}.px-0\.5{padding-left:.125rem;padding-right:.125rem}.px-1{padding-left:.25rem;padding-right:.25rem}.px-1\.5{padding-left:.375rem;padding-right:.375rem}.px-2{padding-left:.5rem;padding-right:.5rem}.px-2\.5{padding-left:.625rem;padding-right:.625rem}.px-3{padding-left:.75rem;padding-right:.75rem}.px-3\.5{padding-left:.875rem;padding-right:.875rem}.px-4{padding-left:1rem;padding-right:1rem}.px-5{padding-left:1.25rem;padding-right:1.25rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.px-8{padding-left:2rem;padding-right:2rem}.px-\[5px\]{padding-left:5px;padding-right:5px}.px-\[6px\]{padding-left:6px;padding-right:6px}.px-px{padding-left:1px;padding-right:1px}.py-0{padding-top:0;padding-bottom:0}.py-0\.5{padding-top:.125rem;padding-bottom:.125rem}.py-1{padding-top:.25rem;padding-bottom:.25rem}.py-1\.5{padding-top:.375rem;padding-bottom:.375rem}.py-16{padding-top:4rem;padding-bottom:4rem}.py-2{padding-top:.5rem;padding-bottom:.5rem}.py-2\.5{padding-top:.625rem;padding-bottom:.625rem}.py-3{padding-top:.75rem;padding-bottom:.75rem}.py-3\.5{padding-top:.875rem;padding-bottom:.875rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-6{padding-top:1.5rem;padding-bottom:1.5rem}.py-8{padding-top:2rem;padding-bottom:2rem}.py-\[2px\]{padding-top:2px;padding-bottom:2px}.py-\[3px\]{padding-top:3px;padding-bottom:3px}.py-\[5px\]{padding-top:5px;padding-bottom:5px}.py-\[7px\]{padding-top:7px;padding-bottom:7px}.py-\[9px\]{padding-top:9px;padding-bottom:9px}.py-px{padding-top:1px;padding-bottom:1px}.\!pl-0{padding-left:0!important}.\!pr-0{padding-right:0!important}.pb-1{padding-bottom:.25rem}.pb-2{padding-bottom:.5rem}.pb-3{padding-bottom:.75rem}.pb-4{padding-bottom:1rem}.pb-8{padding-bottom:2rem}.pb-\[5px\]{padding-bottom:5px}.pb-\[9px\]{padding-bottom:9px}.pl-0{padding-left:0}.pl-0\.5{padding-left:.125rem}.pl-1{padding-left:.25rem}.pl-1\.5{padding-left:.375rem}.pl-10{padding-left:2.5rem}.pl-2{padding-left:.5rem}.pl-3{padding-left:.75rem}.pl-5{padding-left:1.25rem}.pl-6{padding-left:1.5rem}.pl-7{padding-left:1.75rem}.pl-8{padding-left:2rem}.pl-\[31px\]{padding-left:31px}.pl-\[34px\]{padding-left:34px}.pl-\[60px\]{padding-left:60px}.pl-\[88px\]{padding-left:88px}.pl-\[calc\(var\(--tag-width\)\+8px\)\]{padding-left:calc(var(--tag-width) + 8px)}.pr-1{padding-right:.25rem}.pr-1\.5{padding-right:.375rem}.pr-10{padding-right:2.5rem}.pr-2{padding-right:.5rem}.pr-2\.5{padding-right:.625rem}.pr-3{padding-right:.75rem}.pr-4{padding-right:1rem}.pr-6{padding-right:1.5rem}.pr-7{padding-right:1.75rem}.pr-8{padding-right:2rem}.pr-\[7px\]{padding-right:7px}.pr-\[calc\(15px\+2px\+8px\)\]{padding-right:25px}.pt-0{padding-top:0}.pt-0\.5{padding-top:.125rem}.pt-1{padding-top:.25rem}.pt-1\.5{padding-top:.375rem}.pt-12{padding-top:3rem}.pt-2{padding-top:.5rem}.pt-2\.5{padding-top:.625rem}.pt-3{padding-top:.75rem}.pt-4{padding-top:1rem}.pt-6{padding-top:1.5rem}.pt-8{padding-top:2rem}.pt-\[1px\]{padding-top:1px}.pt-\[2px\]{padding-top:2px}.pt-\[72px\]{padding-top:72px}.text-left{text-align:left}.text-center{text-align:center}.text-right{text-align:right}.text-start{text-align:start}.text-end{text-align:end}.align-middle{vertical-align:middle}.align-sub{vertical-align:sub}.font-mono{font-family:Roboto Mono,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.font-sans{font-family:Inter,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji"}.text-\[13px\]{font-size:13px}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.font-medium{font-weight:500}.font-normal{font-weight:400}.uppercase{text-transform:uppercase}.capitalize{text-transform:capitalize}.tabular-nums{--tw-numeric-spacing: tabular-nums;font-variant-numeric:var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction)}.leading-\[20px\]{line-height:20px}.leading-none{line-height:1}.leading-normal{line-height:1.5}.tracking-widest{letter-spacing:.1em}.\!text-ui-contrast-fg-secondary{color:var(--contrast-fg-secondary)!important}.\!text-ui-fg-on-color{color:var(--fg-on-color)!important}.text-blue-500{--tw-text-opacity: 1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-red-500{--tw-text-opacity: 1;color:rgb(239 68 68 / var(--tw-text-opacity, 1))}.text-ui-contrast-fg-primary{color:var(--contrast-fg-primary)}.text-ui-contrast-fg-secondary{color:var(--contrast-fg-secondary)}.text-ui-fg-base{color:var(--fg-base)}.text-ui-fg-disabled{color:var(--fg-disabled)}.text-ui-fg-error{color:var(--fg-error)}.text-ui-fg-interactive{color:var(--fg-interactive)}.text-ui-fg-interactive-hover{color:var(--fg-interactive-hover)}.text-ui-fg-muted{color:var(--fg-muted)}.text-ui-fg-on-color{color:var(--fg-on-color)}.text-ui-fg-on-inverted{color:var(--fg-on-inverted)}.text-ui-fg-subtle{color:var(--fg-subtle)}.text-ui-tag-blue-icon{color:var(--tag-blue-icon)}.text-ui-tag-blue-text{color:var(--tag-blue-text)}.text-ui-tag-green-icon{color:var(--tag-green-icon)}.text-ui-tag-green-text{color:var(--tag-green-text)}.text-ui-tag-neutral-icon{color:var(--tag-neutral-icon)}.text-ui-tag-neutral-text{color:var(--tag-neutral-text)}.text-ui-tag-orange-icon{color:var(--tag-orange-icon)}.text-ui-tag-orange-text{color:var(--tag-orange-text)}.text-ui-tag-purple-text{color:var(--tag-purple-text)}.text-ui-tag-red-icon{color:var(--tag-red-icon)}.text-ui-tag-red-text{color:var(--tag-red-text)}.\!placeholder-ui-fg-disabled::placeholder{color:var(--fg-disabled)!important}.placeholder-ui-fg-muted::placeholder{color:var(--fg-muted)}.caret-ui-fg-base{caret-color:var(--fg-base)}.opacity-0{opacity:0}.opacity-100{opacity:1}.opacity-25{opacity:.25}.opacity-50{opacity:.5}.opacity-80{opacity:.8}.\!shadow-borders-error{--tw-shadow: var(--borders-error) !important;--tw-shadow-colored: var(--borders-error) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.\!shadow-buttons-neutral{--tw-shadow: var(--buttons-neutral) !important;--tw-shadow-colored: var(--buttons-neutral) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.\!shadow-elevation-commandbar{--tw-shadow: var(--elevation-commandbar) !important;--tw-shadow-colored: var(--elevation-commandbar) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.shadow-\[0_1px_1px_0\]{--tw-shadow: 0 1px 1px 0;--tw-shadow-colored: 0 1px 1px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-\[0px_0px_0px_1px_rgba\(0\,0\,0\,0\.12\)_inset\]{--tw-shadow: 0px 0px 0px 1px rgba(0,0,0,.12) inset;--tw-shadow-colored: inset 0px 0px 0px 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-\[0px_1px_2px_0px_rgba\(3\,7\,18\,0\.12\)\,0px_1px_2px_0px_rgba\(255\,255\,255\,0\.10\)_inset\,0px_-1px_5px_0px_rgba\(255\,255\,255\,0\.10\)_inset\,0px_0px_0px_0px_rgba\(3\,7\,18\,0\.06\)_inset\]{--tw-shadow: 0px 1px 2px 0px rgba(3,7,18,.12),0px 1px 2px 0px rgba(255,255,255,.1) inset,0px -1px 5px 0px rgba(255,255,255,.1) inset,0px 0px 0px 0px rgba(3,7,18,.06) inset;--tw-shadow-colored: 0px 1px 2px 0px var(--tw-shadow-color), inset 0px 1px 2px 0px var(--tw-shadow-color), inset 0px -1px 5px 0px var(--tw-shadow-color), inset 0px 0px 0px 0px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-\[inset_0_0_0_1px_rgba\(0\,0\,0\,0\.12\)\]{--tw-shadow: inset 0 0 0 1px rgba(0,0,0,.12);--tw-shadow-colored: inset 0 0 0 1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-borders-base{--tw-shadow: var(--borders-base);--tw-shadow-colored: var(--borders-base);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-borders-error{--tw-shadow: var(--borders-error);--tw-shadow-colored: var(--borders-error);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-borders-focus{--tw-shadow: var(--borders-focus);--tw-shadow-colored: var(--borders-focus);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-borders-interactive-with-active{--tw-shadow: var(--borders-interactive-with-active);--tw-shadow-colored: var(--borders-interactive-with-active);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-buttons-danger{--tw-shadow: var(--buttons-danger);--tw-shadow-colored: var(--buttons-danger);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-buttons-inverted{--tw-shadow: var(--buttons-inverted);--tw-shadow-colored: var(--buttons-inverted);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-buttons-neutral{--tw-shadow: var(--buttons-neutral);--tw-shadow-colored: var(--buttons-neutral);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-details-contrast-on-bg-interactive{--tw-shadow: var(--details-contrast-on-bg-interactive);--tw-shadow-colored: var(--details-contrast-on-bg-interactive);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-details-switch-background{--tw-shadow: var(--details-switch-background);--tw-shadow-colored: var(--details-switch-background);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-details-switch-handle{--tw-shadow: var(--details-switch-handle);--tw-shadow-colored: var(--details-switch-handle);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-elevation-card-hover{--tw-shadow: var(--elevation-card-hover);--tw-shadow-colored: var(--elevation-card-hover);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-elevation-card-rest{--tw-shadow: var(--elevation-card-rest);--tw-shadow-colored: var(--elevation-card-rest);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-elevation-code-block{--tw-shadow: var(--elevation-code-block);--tw-shadow-colored: var(--elevation-code-block);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-elevation-flyout{--tw-shadow: var(--elevation-flyout);--tw-shadow-colored: var(--elevation-flyout);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-elevation-modal{--tw-shadow: var(--elevation-modal);--tw-shadow-colored: var(--elevation-modal);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-elevation-tooltip{--tw-shadow: var(--elevation-tooltip);--tw-shadow-colored: var(--elevation-tooltip);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-ui-border-base{--tw-shadow-color: var(--border-base);--tw-shadow: var(--tw-shadow-colored)}.outline-none{outline:2px solid transparent;outline-offset:2px}.ring-2{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.ring-inset{--tw-ring-inset: inset}.ring-ui-bg-interactive{--tw-ring-color: var(--bg-interactive)}.blur{--tw-blur: blur(8px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.drop-shadow-sm{--tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / .05));filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.invert{--tw-invert: invert(100%);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.\!filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)!important}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-fg{transition-property:color,background-color,border-color,box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-opacity{transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-shadow{transition-property:box-shadow;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.delay-200{transition-delay:.2s}.duration-150{transition-duration:.15s}.duration-200{transition-duration:.2s}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.ease-linear{transition-timing-function:linear}.ease-out{transition-timing-function:cubic-bezier(0,0,.2,1)}.will-change-contents{will-change:contents}.will-change-transform{will-change:transform}@keyframes enter{0%{opacity:var(--tw-enter-opacity, 1);transform:translate3d(var(--tw-enter-translate-x, 0),var(--tw-enter-translate-y, 0),0) scale3d(var(--tw-enter-scale, 1),var(--tw-enter-scale, 1),var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity, 1);transform:translate3d(var(--tw-exit-translate-x, 0),var(--tw-exit-translate-y, 0),0) scale3d(var(--tw-exit-scale, 1),var(--tw-exit-scale, 1),var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))}}.animate-in{animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial}.animate-out{animation-name:exit;animation-duration:.15s;--tw-exit-opacity: initial;--tw-exit-scale: initial;--tw-exit-rotate: initial;--tw-exit-translate-x: initial;--tw-exit-translate-y: initial}.fade-in,.fade-in-0{--tw-enter-opacity: 0}.fade-out{--tw-exit-opacity: 0}.zoom-in{--tw-enter-scale: 0}.zoom-in-95{--tw-enter-scale: .95}.zoom-out{--tw-exit-scale: 0}.spin-in{--tw-enter-rotate: 30deg}.spin-out{--tw-exit-rotate: 30deg}.slide-in-from-bottom{--tw-enter-translate-y: 100%}.slide-in-from-left{--tw-enter-translate-x: -100%}.slide-in-from-right{--tw-enter-translate-x: 100%}.slide-in-from-top{--tw-enter-translate-y: -100%}.slide-out-to-bottom{--tw-exit-translate-y: 100%}.slide-out-to-left{--tw-exit-translate-x: -100%}.slide-out-to-right{--tw-exit-translate-x: 100%}.slide-out-to-top{--tw-exit-translate-y: -100%}.duration-150{animation-duration:.15s}.duration-200{animation-duration:.2s}.delay-200{animation-delay:.2s}.ease-in-out{animation-timing-function:cubic-bezier(.4,0,.2,1)}.ease-linear{animation-timing-function:linear}.ease-out{animation-timing-function:cubic-bezier(0,0,.2,1)}.running{animation-play-state:running}.paused{animation-play-state:paused}.\[appearance\:textfield\]{-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.data-\[state\=checked\]\:txt-compact-small-plus[data-state=checked]{font-size:.8125rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.\[\&\>\*\]\:txt-compact-small-plus>*{font-size:.8125rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.\[\&\>code\]\:code-body>code{font-size:.75rem;line-height:1.125rem;font-weight:400;font-family:Roboto Mono,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace}.\[\&_\[cmdk-group-heading\]\]\:txt-compact-xsmall-plus [cmdk-group-heading]{font-size:.75rem;line-height:1.25rem;font-weight:500;font-family:Inter,ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.placeholder\:text-ui-fg-muted::placeholder{color:var(--fg-muted)}.before\:absolute:before{content:var(--tw-content);position:absolute}.before\:inset-0:before{content:var(--tw-content);top:0;right:0;bottom:0;left:0}.before\:rounded-full:before{content:var(--tw-content);border-radius:9999px}.before\:shadow-details-switch-background:before{content:var(--tw-content);--tw-shadow: var(--details-switch-background);--tw-shadow-colored: var(--details-switch-background);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.before\:content-\[\'\'\]:before{--tw-content: "";content:var(--tw-content)}.after\:pointer-events-none:after{content:var(--tw-content);pointer-events:none}.after\:absolute:after{content:var(--tw-content);position:absolute}.after\:inset-0:after{content:var(--tw-content);top:0;right:0;bottom:0;left:0}.after\:inset-y-0:after{content:var(--tw-content);top:0;bottom:0}.after\:right-0:after{content:var(--tw-content);right:0}.after\:hidden:after{content:var(--tw-content);display:none}.after\:h-full:after{content:var(--tw-content);height:100%}.after\:w-px:after{content:var(--tw-content);width:1px}.after\:rounded-full:after{content:var(--tw-content);border-radius:9999px}.after\:bg-transparent:after{content:var(--tw-content);background-color:transparent}.after\:bg-ui-border-base:after{content:var(--tw-content);background-color:var(--border-base)}.after\:shadow-elevation-flyout:after{content:var(--tw-content);--tw-shadow: var(--elevation-flyout);--tw-shadow-colored: var(--elevation-flyout);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.after\:transition-fg:after{content:var(--tw-content);transition-property:color,background-color,border-color,box-shadow,opacity;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.after\:content-\[\'\'\]:after{--tw-content: "";content:var(--tw-content)}.last\:border-r-0:last-child{border-right-width:0px}.last-of-type\:-mr-1:last-of-type{margin-right:-.25rem}.last-of-type\:\!border-b:last-of-type{border-bottom-width:1px!important}.last-of-type\:border-b-0:last-of-type{border-bottom-width:0px}.last-of-type\:pr-4:last-of-type{padding-right:1rem}.invalid\:border-ui-border-error:invalid{border-color:var(--border-error)}.invalid\:\!shadow-borders-error:invalid{--tw-shadow: var(--borders-error) !important;--tw-shadow-colored: var(--borders-error) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.invalid\:shadow-borders-error:invalid{--tw-shadow: var(--borders-error);--tw-shadow-colored: var(--borders-error);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus-within\:border-ui-fg-interactive:focus-within{border-color:var(--fg-interactive)}.focus-within\:shadow-borders-interactive-with-active:focus-within{--tw-shadow: var(--borders-interactive-with-active);--tw-shadow-colored: var(--borders-interactive-with-active);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus-within\:shadow-borders-interactive-with-focus:focus-within{--tw-shadow: var(--borders-interactive-with-focus);--tw-shadow-colored: var(--borders-interactive-with-focus);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.hover\:border-ui-border-interactive:hover{border-color:var(--border-interactive)}.hover\:\!bg-ui-bg-base:hover{background-color:var(--bg-base)!important}.hover\:bg-transparent:hover{background-color:transparent}.hover\:bg-ui-bg-base:hover{background-color:var(--bg-base)}.hover\:bg-ui-bg-base-hover:hover{background-color:var(--bg-base-hover)}.hover\:bg-ui-bg-base-pressed:hover{background-color:var(--bg-base-pressed)}.hover\:bg-ui-bg-component-hover:hover{background-color:var(--bg-component-hover)}.hover\:bg-ui-bg-field-component-hover:hover{background-color:var(--bg-field-component-hover)}.hover\:bg-ui-bg-field-hover:hover{background-color:var(--bg-field-hover)}.hover\:bg-ui-bg-highlight-hover:hover{background-color:var(--bg-highlight-hover)}.hover\:bg-ui-bg-subtle-hover:hover{background-color:var(--bg-subtle-hover)}.hover\:bg-ui-bg-switch-off-hover:hover{background-color:var(--bg-switch-off-hover)}.hover\:bg-ui-button-danger-hover:hover{background-color:var(--button-danger-hover)}.hover\:bg-ui-button-inverted-hover:hover{background-color:var(--button-inverted-hover)}.hover\:bg-ui-button-neutral-hover:hover{background-color:var(--button-neutral-hover)}.hover\:bg-ui-button-transparent-hover:hover{background-color:var(--button-transparent-hover)}.hover\:bg-ui-contrast-bg-base-hover:hover{background-color:var(--contrast-bg-base-hover)}.hover\:text-blue-400:hover{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity, 1))}.hover\:text-ui-contrast-fg-primary:hover{color:var(--contrast-fg-primary)}.hover\:text-ui-fg-base:hover{color:var(--fg-base)}.hover\:text-ui-fg-interactive-hover:hover{color:var(--fg-interactive-hover)}.hover\:text-ui-fg-subtle:hover{color:var(--fg-subtle)}.hover\:shadow-elevation-card-hover:hover{--tw-shadow: var(--elevation-card-hover);--tw-shadow-colored: var(--elevation-card-hover);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus\:z-\[1\]:focus{z-index:1}.focus\:cursor-text:focus{cursor:text}.focus\:border-solid:focus{border-style:solid}.focus\:border-ui-border-interactive:focus{border-color:var(--border-interactive)}.focus\:bg-ui-bg-component-hover:focus{background-color:var(--bg-component-hover)}.focus\:shadow-borders-focus:focus{--tw-shadow: var(--borders-focus);--tw-shadow-colored: var(--borders-focus);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus-visible\:border-ui-border-interactive:focus-visible{border-color:var(--border-interactive)}.focus-visible\:bg-ui-bg-base:focus-visible{background-color:var(--bg-base)}.focus-visible\:bg-ui-bg-base-hover:focus-visible{background-color:var(--bg-base-hover)}.focus-visible\:bg-ui-bg-base-pressed:focus-visible{background-color:var(--bg-base-pressed)}.focus-visible\:bg-ui-bg-component-hover:focus-visible{background-color:var(--bg-component-hover)}.focus-visible\:bg-ui-bg-field-component-hover:focus-visible{background-color:var(--bg-field-component-hover)}.focus-visible\:bg-ui-bg-field-hover:focus-visible{background-color:var(--bg-field-hover)}.focus-visible\:bg-ui-bg-interactive:focus-visible{background-color:var(--bg-interactive)}.focus-visible\:bg-ui-contrast-bg-base-hover:focus-visible{background-color:var(--contrast-bg-base-hover)}.focus-visible\:text-ui-fg-base:focus-visible{color:var(--fg-base)}.focus-visible\:text-ui-fg-interactive-hover:focus-visible{color:var(--fg-interactive-hover)}.focus-visible\:text-ui-fg-on-color:focus-visible{color:var(--fg-on-color)}.focus-visible\:text-ui-fg-subtle:focus-visible{color:var(--fg-subtle)}.focus-visible\:\!shadow-borders-focus:focus-visible{--tw-shadow: var(--borders-focus) !important;--tw-shadow-colored: var(--borders-focus) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.focus-visible\:\!shadow-buttons-inverted-focus:focus-visible{--tw-shadow: var(--buttons-inverted-focus) !important;--tw-shadow-colored: var(--buttons-inverted-focus) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.focus-visible\:shadow-borders-focus:focus-visible{--tw-shadow: var(--borders-focus);--tw-shadow-colored: var(--borders-focus);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus-visible\:shadow-borders-interactive-with-active:focus-visible{--tw-shadow: var(--borders-interactive-with-active);--tw-shadow-colored: var(--borders-interactive-with-active);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus-visible\:shadow-borders-interactive-with-focus:focus-visible{--tw-shadow: var(--borders-interactive-with-focus);--tw-shadow-colored: var(--borders-interactive-with-focus);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus-visible\:shadow-buttons-danger-focus:focus-visible{--tw-shadow: var(--buttons-danger-focus);--tw-shadow-colored: var(--buttons-danger-focus);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus-visible\:shadow-buttons-neutral-focus:focus-visible{--tw-shadow: var(--buttons-neutral-focus);--tw-shadow-colored: var(--buttons-neutral-focus);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus-visible\:shadow-details-switch-background-focus:focus-visible{--tw-shadow: var(--details-switch-background-focus);--tw-shadow-colored: var(--details-switch-background-focus);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus-visible\:outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.focus-visible\:hover\:bg-ui-contrast-bg-base-hover:hover:focus-visible{background-color:var(--contrast-bg-base-hover)}.active\:cursor-grabbing:active{cursor:grabbing}.active\:bg-ui-bg-base-pressed:active{background-color:var(--bg-base-pressed)}.active\:bg-ui-bg-component-hover:active{background-color:var(--bg-component-hover)}.active\:bg-ui-bg-component-pressed:active{background-color:var(--bg-component-pressed)}.active\:bg-ui-bg-subtle-pressed:active{background-color:var(--bg-subtle-pressed)}.active\:bg-ui-button-danger-pressed:active{background-color:var(--button-danger-pressed)}.active\:bg-ui-button-inverted-pressed:active{background-color:var(--button-inverted-pressed)}.active\:bg-ui-button-neutral-pressed:active{background-color:var(--button-neutral-pressed)}.active\:bg-ui-button-transparent-pressed:active{background-color:var(--button-transparent-pressed)}.active\:bg-ui-contrast-bg-base-pressed:active{background-color:var(--contrast-bg-base-pressed)}.active\:text-ui-fg-base:active{color:var(--fg-base)}.active\:text-ui-fg-subtle:active{color:var(--fg-subtle)}.focus-visible\:active\:bg-ui-contrast-bg-base-pressed:active:focus-visible{background-color:var(--contrast-bg-base-pressed)}.hover\:enabled\:bg-ui-bg-base-hover:enabled:hover{background-color:var(--bg-base-hover)}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:border-ui-border-base:disabled{border-color:var(--border-base)}.disabled\:\!bg-transparent:disabled{background-color:transparent!important}.disabled\:\!bg-ui-bg-disabled:disabled{background-color:var(--bg-disabled)!important}.disabled\:bg-ui-bg-base:disabled{background-color:var(--bg-base)}.disabled\:bg-ui-bg-disabled:disabled{background-color:var(--bg-disabled)}.disabled\:\!text-ui-fg-disabled:disabled{color:var(--fg-disabled)!important}.disabled\:text-ui-fg-disabled:disabled{color:var(--fg-disabled)}.disabled\:text-ui-fg-muted:disabled{color:var(--fg-muted)}.disabled\:placeholder-ui-fg-disabled:disabled::placeholder{color:var(--fg-disabled)}.disabled\:opacity-50:disabled{opacity:.5}.disabled\:\!shadow-none:disabled{--tw-shadow: 0 0 #0000 !important;--tw-shadow-colored: 0 0 #0000 !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.disabled\:shadow-buttons-neutral:disabled{--tw-shadow: var(--buttons-neutral);--tw-shadow-colored: var(--buttons-neutral);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.disabled\:after\:hidden:disabled:after{content:var(--tw-content);display:none}.group:focus-within .group-focus-within\:opacity-100{opacity:1}.group\/table:hover .group-hover\/table\:visible,.group:hover .group-hover\:visible{visibility:visible}.group\/container:hover .group-hover\/container\:flex{display:flex}.group\/container:hover .group-hover\/container\:hidden{display:none}.group\/row:hover .group-hover\/row\:bg-ui-bg-base-hover{background-color:var(--bg-base-hover)}.group\/row:hover .group-hover\/row\:bg-ui-bg-subtle-hover{background-color:var(--bg-subtle-hover)}.group:hover .group-hover\:opacity-100{opacity:1}.group:focus .group-focus\:opacity-100{opacity:1}.group:focus-visible .group-focus-visible\:\!shadow-borders-interactive-with-focus{--tw-shadow: var(--borders-interactive-with-focus) !important;--tw-shadow-colored: var(--borders-interactive-with-focus) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.group:focus-visible .group-focus-visible\:shadow-borders-interactive-with-active{--tw-shadow: var(--borders-interactive-with-active);--tw-shadow-colored: var(--borders-interactive-with-active);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.group:disabled .group-disabled\:cursor-not-allowed{cursor:not-allowed}.group\/trigger:disabled .group-disabled\/trigger\:text-ui-fg-disabled{color:var(--fg-disabled)}.group:disabled .group-disabled\:text-ui-fg-disabled{color:var(--fg-disabled)}.group:disabled .group-disabled\:opacity-50{opacity:.5}.has-\[\:disabled\]\:cursor-not-allowed:has(:disabled){cursor:not-allowed}.has-\[input\:disabled\]\:cursor-not-allowed:has(input:disabled){cursor:not-allowed}.has-\[\:disabled\]\:bg-ui-bg-disabled:has(:disabled){background-color:var(--bg-disabled)}.has-\[\[data-row-link\]\:focus-visible\]\:bg-ui-bg-base-hover:has([data-row-link]:focus-visible){background-color:var(--bg-base-hover)}.has-\[input\:disabled\]\:bg-ui-bg-disabled:has(input:disabled){background-color:var(--bg-disabled)}.has-\[\:disabled\]\:text-ui-fg-disabled:has(:disabled){color:var(--fg-disabled)}.has-\[input\:disabled\]\:text-ui-fg-disabled:has(input:disabled){color:var(--fg-disabled)}.has-\[\:invalid\]\:shadow-borders-error:has(:invalid){--tw-shadow: var(--borders-error);--tw-shadow-colored: var(--borders-error);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.has-\[\[aria-invalid\=true\]\]\:shadow-borders-error:has([aria-invalid=true]){--tw-shadow: var(--borders-error);--tw-shadow-colored: var(--borders-error);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.has-\[input\:focus\]\:shadow-borders-interactive-with-active:has(input:focus){--tw-shadow: var(--borders-interactive-with-active);--tw-shadow-colored: var(--borders-interactive-with-active);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.group:has([data-row-link]:focus-visible) .group-has-\[\[data-row-link\]\:focus-visible\]\:bg-ui-bg-base-hover{background-color:var(--bg-base-hover)}.aria-selected\:bg-ui-bg-base-hover[aria-selected=true]{background-color:var(--bg-base-hover)}.aria-selected\:bg-ui-bg-base-pressed[aria-selected=true]{background-color:var(--bg-base-pressed)}.aria-\[invalid\=true\]\:border-ui-border-error[aria-invalid=true]{border-color:var(--border-error)}.aria-\[invalid\=\'true\'\]\:shadow-borders-error[aria-invalid=true]{--tw-shadow: var(--borders-error);--tw-shadow-colored: var(--borders-error);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.aria-\[invalid\=true\]\:\!shadow-borders-error[aria-invalid=true]{--tw-shadow: var(--borders-error) !important;--tw-shadow-colored: var(--borders-error) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.aria-\[invalid\=true\]\:shadow-borders-error[aria-invalid=true]{--tw-shadow: var(--borders-error);--tw-shadow-colored: var(--borders-error);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.data-\[disabled\]\:pointer-events-none[data-disabled]{pointer-events:none}.data-\[state\=\'open\'\]\:visible[data-state=open]{visibility:visible}.data-\[side\=bottom\]\:translate-y-1[data-side=bottom]{--tw-translate-y: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[side\=left\]\:-translate-x-1[data-side=left]{--tw-translate-x: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[side\=right\]\:translate-x-1[data-side=right]{--tw-translate-x: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[side\=top\]\:-translate-y-1[data-side=top]{--tw-translate-y: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[state\=checked\]\:translate-x-3\.5[data-state=checked]{--tw-translate-x: .875rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[state\=checked\]\:translate-x-4[data-state=checked]{--tw-translate-x: 1rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.data-\[state\=unchecked\]\:translate-x-0\.5[data-state=unchecked]{--tw-translate-x: .125rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}@keyframes accordion-up{0%{height:var(--radix-accordion-content-height)}to{height:0px}}.data-\[state\=closed\]\:animate-accordion-up[data-state=closed]{animation:accordion-up .2s ease-out}@keyframes accordion-down{0%{height:0px}to{height:var(--radix-accordion-content-height)}}.data-\[state\=open\]\:animate-accordion-down[data-state=open]{animation:accordion-down .2s ease-out}.data-\[disabled\]\:cursor-not-allowed[data-disabled]{cursor:not-allowed}.data-\[active-item\=true\]\:bg-ui-bg-base-hover[data-active-item=true]{background-color:var(--bg-base-hover)}.data-\[active\=true\]\:bg-ui-bg-field-hover[data-active=true]{background-color:var(--bg-field-hover)}.data-\[state\=active\]\:bg-ui-bg-base[data-state=active]{background-color:var(--bg-base)}.data-\[state\=checked\]\:bg-ui-bg-interactive[data-state=checked]{background-color:var(--bg-interactive)}.data-\[state\=open\]\:\!bg-ui-bg-component-hover[data-state=open]{background-color:var(--bg-component-hover)!important}.data-\[state\=open\]\:bg-ui-bg-field-hover[data-state=open]{background-color:var(--bg-field-hover)}.data-\[state\=open\]\:bg-ui-bg-subtle-hover[data-state=open]{background-color:var(--bg-subtle-hover)}.data-\[disabled\]\:text-ui-fg-disabled[data-disabled]{color:var(--fg-disabled)}.data-\[placeholder\]\:text-ui-fg-muted[data-placeholder]{color:var(--fg-muted)}.data-\[state\=active\]\:text-ui-fg-base[data-state=active]{color:var(--fg-base)}.data-\[disabled\]\:opacity-50[data-disabled]{opacity:.5}.data-\[state\=active\]\:shadow-elevation-card-rest[data-state=active]{--tw-shadow: var(--elevation-card-rest);--tw-shadow-colored: var(--elevation-card-rest);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.data-\[state\=checked\]\:shadow-borders-interactive-with-shadow[data-state=checked]{--tw-shadow: var(--borders-interactive-with-shadow);--tw-shadow-colored: var(--borders-interactive-with-shadow);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.data-\[state\=open\]\:\!shadow-borders-interactive-with-active[data-state=open]{--tw-shadow: var(--borders-interactive-with-active) !important;--tw-shadow-colored: var(--borders-interactive-with-active) !important;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)!important}.data-\[state\=open\]\:animate-in[data-state=open]{animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial}.data-\[state\=closed\]\:animate-out[data-state=closed]{animation-name:exit;animation-duration:.15s;--tw-exit-opacity: initial;--tw-exit-scale: initial;--tw-exit-rotate: initial;--tw-exit-translate-x: initial;--tw-exit-translate-y: initial}.data-\[state\=closed\]\:fade-out-0[data-state=closed]{--tw-exit-opacity: 0}.data-\[state\=open\]\:fade-in-0[data-state=open]{--tw-enter-opacity: 0}.data-\[state\=closed\]\:zoom-out-95[data-state=closed]{--tw-exit-scale: .95}.data-\[state\=open\]\:zoom-in-95[data-state=open]{--tw-enter-scale: .95}.data-\[side\=bottom\]\:slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y: -.5rem}.data-\[side\=left\]\:slide-in-from-right-2[data-side=left]{--tw-enter-translate-x: .5rem}.data-\[side\=right\]\:slide-in-from-left-2[data-side=right]{--tw-enter-translate-x: -.5rem}.data-\[side\=top\]\:slide-in-from-bottom-2[data-side=top],.data-\[state\=closed\]\:slide-in-from-bottom-2[data-state=closed]{--tw-enter-translate-y: .5rem}.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state=closed]{--tw-exit-translate-x: -50%}.data-\[state\=closed\]\:slide-out-to-right-1\/2[data-state=closed]{--tw-exit-translate-x: 50%}.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state=closed]{--tw-exit-translate-y: -48%}.data-\[state\=open\]\:slide-in-from-bottom-0[data-state=open]{--tw-enter-translate-y: 0px}.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state=open]{--tw-enter-translate-x: -50%}.data-\[state\=open\]\:slide-in-from-right-1\/2[data-state=open]{--tw-enter-translate-x: 50%}.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state=open]{--tw-enter-translate-y: -48%}.group\/trigger[data-state=closed] .group-data-\[state\=closed\]\/trigger\:hidden,.group\/trigger[data-state=open] .group-data-\[state\=open\]\/trigger\:hidden{display:none}.group[data-state=open] .group-data-\[state\=\'open\'\]\:rotate-90{--tw-rotate: 90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group\/trigger[data-state=open] .group-data-\[state\=open\]\/trigger\:rotate-180,.group[data-state=open] .group-data-\[state\=open\]\:rotate-180{--tw-rotate: 180deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group[data-state=open] .group-data-\[state\=open\]\:rotate-45{--tw-rotate: 45deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.group\/row[data-selected=true] .group-data-\[selected\=true\]\/row\:bg-ui-bg-highlight{background-color:var(--bg-highlight)}.group[data-state=checked] .group-data-\[state\=checked\]\:bg-ui-bg-interactive,.group[data-state=indeterminate] .group-data-\[state\=indeterminate\]\:bg-ui-bg-interactive{background-color:var(--bg-interactive)}.group\/trigger[data-state=active] .group-data-\[state\=active\]\/trigger\:text-ui-fg-interactive,.group[data-state=open] .group-data-\[state\=open\]\:text-ui-fg-interactive{color:var(--fg-interactive)}.group[data-state=checked] .group-data-\[state\=checked\]\:shadow-borders-interactive-with-shadow,.group[data-state=indeterminate] .group-data-\[state\=indeterminate\]\:shadow-borders-interactive-with-shadow{--tw-shadow: var(--borders-interactive-with-shadow);--tw-shadow-colored: var(--borders-interactive-with-shadow);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.group[data-link=true] .group-data-\[link\=true\]\:hover\:bg-ui-bg-base-hover:hover{background-color:var(--bg-base-hover)}.group\/row[data-selected=true]:hover .group-data-\[selected\=true\]\/row\:group-hover\/row\:bg-ui-bg-highlight-hover{background-color:var(--bg-highlight-hover)}.group:hover:enabled[data-state=unchecked] .group-hover\:group-enabled\:group-data-\[state\=unchecked\]\:bg-ui-bg-base-hover{background-color:var(--bg-base-hover)}@media (prefers-reduced-motion: reduce){.motion-reduce\:transition-none{transition-property:none}}@media not all and (min-width: 1024px){.max-lg\:flex{display:flex}.max-lg\:hidden{display:none}}@media not all and (min-width: 768px){.max-md\:inset-x-2{left:.5rem;right:.5rem}.max-md\:max-w-\[calc\(100\%-16px\)\]{max-width:calc(100% - 16px)}}@media not all and (min-width: 640px){.max-sm\:inset-x-2{left:.5rem;right:.5rem}.max-sm\:w-\[calc\(100\%-16px\)\]{width:calc(100% - 16px)}}@media (min-width: 640px){.sm\:right-2{right:.5rem}.sm\:block{display:block}.sm\:flex{display:flex}.sm\:hidden{display:none}.sm\:max-w-\[560px\]{max-width:560px}.sm\:grid-cols-\[1fr_1fr_1fr_1fr_20px\]{grid-template-columns:1fr 1fr 1fr 1fr 20px}.sm\:flex-row{flex-direction:row}}@media (min-width: 768px){.md\:flex{display:flex}.md\:hidden{display:none}.md\:w-\[720px\]{width:720px}.md\:w-auto{width:auto}.md\:w-fit{width:fit-content}.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\:flex-row{flex-direction:row}.md\:flex-wrap{flex-wrap:wrap}.md\:items-center{align-items:center}.md\:justify-end{justify-content:flex-end}.md\:gap-y-0{row-gap:0px}.md\:p-0{padding:0}.md\:pt-24{padding-top:6rem}}@media (min-width: 1024px){.lg\:col-span-2{grid-column:span 2 / span 2}.lg\:block{display:block}.lg\:flex{display:flex}.lg\:grid{display:grid}.lg\:hidden{display:none}.lg\:w-1\/2{width:50%}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:grid-cols-\[1fr_560px\]{grid-template-columns:1fr 560px}.lg\:flex-row{flex-direction:row}.lg\:gap-y-8{row-gap:2rem}.lg\:border-b-0{border-bottom-width:0px}.lg\:border-l{border-left-width:1px}}@media (min-width: 1280px){.xl\:mt-0{margin-top:0}.xl\:flex{display:flex}.xl\:grid{display:grid}.xl\:hidden{display:none}.xl\:max-w-\[440px\]{max-width:440px}.xl\:grid-cols-\[minmax\(0\,_1fr\)_440px\]{grid-template-columns:minmax(0,1fr) 440px}.xl\:flex-row{flex-direction:row}.xl\:items-start{align-items:flex-start}.xl\:items-center{align-items:center}}.\[\&\:\:--webkit-search-cancel-button\]\:hidden::--webkit-search-cancel-button{display:none}.\[\&\:\:-webkit-inner-spin-button\]\:appearance-none::-webkit-inner-spin-button{-webkit-appearance:none;-moz-appearance:none;appearance:none}.\[\&\:\:-webkit-outer-spin-button\]\:appearance-none::-webkit-outer-spin-button{-webkit-appearance:none;-moz-appearance:none;appearance:none}.\[\&\:\:-webkit-search-cancel-button\]\:hidden::-webkit-search-cancel-button{display:none}.\[\&\:\:-webkit-search-decoration\]\:hidden::-webkit-search-decoration{display:none}.\[\&\:first-of-type\>div\]\:border-t-0:first-of-type>div{border-top-width:0px}.\[\&\:has\(\[data-field\]\:focus\)\]\:bg-ui-bg-base:has([data-field]:focus){background-color:var(--bg-base)}.\[\&\:hover\>div\]\:bg-ui-bg-component-hover:hover>div{background-color:var(--bg-component-hover)}.\[\&\>\*\]\:flex>*{display:flex}.\[\&\>\*\]\:items-center>*{align-items:center}.\[\&\>\*\]\:justify-center>*{justify-content:center}.\[\&\>code\]\:mx-2>code{margin-left:.5rem;margin-right:.5rem}.\[\&\>code\]\:text-ui-contrast-fg-primary>code{color:var(--contrast-fg-primary)}.\[\&\>div\]\:flex>div{display:flex}.\[\&\>div\]\:size-11>div{width:2.75rem;height:2.75rem}.\[\&\>div\]\:size-6>div{width:1.5rem;height:1.5rem}.\[\&\>div\]\:size-9>div{width:2.25rem;height:2.25rem}.\[\&\>div\]\:items-center>div{align-items:center}.\[\&\>div\]\:justify-center>div{justify-content:center}.\[\&\>div\]\:rounded-\[10px\]>div{border-radius:10px}.\[\&\>div\]\:rounded-\[4px\]>div{border-radius:4px}.\[\&\>div\]\:rounded-\[6px\]>div{border-radius:6px}.\[\&\>div\]\:bg-ui-bg-field>div{background-color:var(--bg-field)}.\[\&\>div\]\:text-ui-fg-subtle>div{color:var(--fg-subtle)}.\[\&\>li\]\:border-b-0>li{border-bottom-width:0px}.\[\&\>svg\]\:text-ui-fg-subtle>svg{color:var(--fg-subtle)}.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading]{padding-left:.5rem;padding-right:.5rem}.\[\&_\[cmdk-group-heading\]\]\:pb-1 [cmdk-group-heading]{padding-bottom:.25rem}.\[\&_\[cmdk-group-heading\]\]\:pt-3 [cmdk-group-heading]{padding-top:.75rem}.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading]{font-weight:500}.\[\&_\[cmdk-group-heading\]\]\:text-ui-fg-muted [cmdk-group-heading]{color:var(--fg-muted)}.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden])~[cmdk-group]{padding-top:0}.\[\&_\[cmdk-item\]\]\:py-2 [cmdk-item]{padding-top:.5rem;padding-bottom:.5rem}.\[\&_div\]\:h-2 div{height:.5rem}.\[\&_div\]\:w-2 div{width:.5rem}.\[\&_div\]\:rounded-sm div{border-radius:.125rem}.\[\&_div\]\:bg-ui-tag-blue-icon div{background-color:var(--tag-blue-icon)}.\[\&_div\]\:bg-ui-tag-green-icon div{background-color:var(--tag-green-icon)}.\[\&_div\]\:bg-ui-tag-neutral-icon div{background-color:var(--tag-neutral-icon)}.\[\&_div\]\:bg-ui-tag-orange-icon div{background-color:var(--tag-orange-icon)}.\[\&_div\]\:bg-ui-tag-purple-icon div{background-color:var(--tag-purple-icon)}.\[\&_div\]\:bg-ui-tag-red-icon div{background-color:var(--tag-red-icon)}.\[\&_path\]\:shadow-details-contrast-on-bg-interactive path{--tw-shadow: var(--details-contrast-on-bg-interactive);--tw-shadow-colored: var(--details-contrast-on-bg-interactive);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.\[\&_svg\]\:invisible svg{visibility:hidden}.\[\&_svg\]\:text-ui-fg-disabled svg{color:var(--fg-disabled)}.\[\&_svg\]\:text-ui-fg-subtle svg{color:var(--fg-subtle)}.\[\&_svg\]\:text-ui-tag-blue-icon svg{color:var(--tag-blue-icon)}.\[\&_svg\]\:text-ui-tag-green-icon svg{color:var(--tag-green-icon)}.\[\&_svg\]\:text-ui-tag-neutral-icon svg{color:var(--tag-neutral-icon)}.\[\&_svg\]\:text-ui-tag-orange-icon svg{color:var(--tag-orange-icon)}.\[\&_svg\]\:text-ui-tag-purple-icon svg{color:var(--tag-purple-icon)}.\[\&_svg\]\:text-ui-tag-red-icon svg{color:var(--tag-red-icon)}.\[\&_td\:first-child\]\:pl-6 td:first-child{padding-left:1.5rem}.\[\&_td\:last-child\]\:pr-6 td:last-child{padding-right:1.5rem}.\[\&_td\:last-of-type\]\:w-\[1\%\] td:last-of-type{width:1%}.\[\&_td\:last-of-type\]\:whitespace-nowrap td:last-of-type{white-space:nowrap}.\[\&_th\:first-child\]\:pl-6 th:first-child{padding-left:1.5rem}.\[\&_th\:first-of-type\]\:w-\[1\%\] th:first-of-type{width:1%}.\[\&_th\:first-of-type\]\:whitespace-nowrap th:first-of-type{white-space:nowrap}.\[\&_th\:last-child\]\:pr-6 th:last-child{padding-right:1.5rem}.\[\&_th\:last-of-type\]\:w-\[1\%\] th:last-of-type{width:1%}.\[\&_th\:last-of-type\]\:whitespace-nowrap th:last-of-type{white-space:nowrap}.\[\&_tr\]\:bg-ui-bg-subtle tr{background-color:var(--bg-subtle)}.\[\&_tr\]\:last-of-type\:\!border-b:last-of-type tr{border-bottom-width:1px!important}.\[\&_tr\]\:hover\:bg-ui-bg-subtle:hover tr{background-color:var(--bg-subtle)}
