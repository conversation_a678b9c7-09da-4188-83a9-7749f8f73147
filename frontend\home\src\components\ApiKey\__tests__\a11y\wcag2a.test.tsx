import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { FormProvider, useForm } from 'react-hook-form';
import { Create, Edit, ApiKeyPermissionSection, PermissionNotification } from '../../index';
import { sampleProjects, sampleApiKeyRestricted } from '../../__fixtures__/ApiKey.fixtures';

// Add jest-axe matcher
expect.extend(toHaveNoViolations);

// Wrapper component to provide form context for ApiKeyPermissionSection
const PermissionSectionWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      permissionType: 'Restricted' as const,
      resourcePermissions: {
        models: 'Read' as const,
        modelCapabilities: 'Write' as const,
        assistants: 'None' as const,
        threads: 'None' as const,
        evals: 'None' as const,
        fineTuning: 'None' as const,
        files: 'Read' as const,
      },
    },
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('ApiKey Components - WCAG 2.1 Level A Compliance', () => {
  // Test Create component
  describe('Create Component', () => {
    it('should have no accessibility violations', async () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have all form controls with associated labels', () => {
      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Check that all input fields have associated labels
      const inputElements = screen.getAllByRole('textbox');
      inputElements.forEach((input) => {
        expect(input).toHaveAccessibleName();
      });

      // Check select elements
      const selectElements = screen.getAllByRole('combobox');
      selectElements.forEach((select) => {
        expect(select).toHaveAccessibleName();
      });

      // Check buttons
      const buttons = screen.getAllByRole('button');
      buttons.forEach((button) => {
        expect(button).toHaveAccessibleName();
      });
    });

    it('should maintain proper heading hierarchy', () => {
      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Check for proper heading hierarchy
      const headings = screen.getAllByRole('heading');

      // Extract heading levels
      const headingLevels = headings.map((heading) => {
        const headingLevel = parseInt(
          heading.getAttribute('aria-level') || heading.tagName.substring(1),
          10,
        );
        return headingLevel;
      });

      // Check if heading levels increase by at most 1
      for (let i = 1; i < headingLevels.length; i++) {
        expect(headingLevels[i] - headingLevels[i - 1]).toBeLessThanOrEqual(1);
      }
    });
  });

  // Test Edit component
  describe('Edit Component', () => {
    it('should have no accessibility violations', async () => {
      const { container } = render(
        <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have all interactive elements focusable and with accessible names', () => {
      render(<Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />);

      // Check all interactive elements
      const interactiveElements = [
        ...screen.getAllByRole('button'),
        ...screen.getAllByRole('textbox'),
        ...screen.getAllByRole('combobox'),
      ];

      interactiveElements.forEach((element) => {
        expect(element).toHaveAttribute('tabIndex', expect.not.stringMatching('-1'));
        expect(element).toHaveAccessibleName();
      });
    });
  });

  // Test ApiKeyPermissionSection component
  describe('ApiKeyPermissionSection Component', () => {
    it('should have no accessibility violations', async () => {
      const { container } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have all permission buttons with accessible names', () => {
      render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // Check all buttons (None, Read, Write) have accessible names
      const buttons = screen.getAllByRole('button');
      buttons.forEach((button) => {
        expect(button).toHaveAccessibleName();
      });
    });

    it('should have distinguishable text for resource paths', () => {
      render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // Check paths have proper color contrast by their class name
      const pathElements = document.querySelectorAll('.text-muted-foreground');
      expect(pathElements.length).toBeGreaterThan(0);
    });
  });

  // Test PermissionNotification component
  describe('PermissionNotification Component', () => {
    it('should have no accessibility violations', async () => {
      const { container } = render(<PermissionNotification />);

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should maintain proper color contrast in all variants', async () => {
      const { rerender, container } = render(<PermissionNotification variant="default" />);
      let results = await axe(container);
      expect(results).toHaveNoViolations();

      rerender(<PermissionNotification variant="info" />);
      results = await axe(container);
      expect(results).toHaveNoViolations();

      rerender(<PermissionNotification variant="warning" />);
      results = await axe(container);
      expect(results).toHaveNoViolations();

      rerender(<PermissionNotification variant="error" />);
      results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should have accessible text content', () => {
      render(<PermissionNotification />);

      // Check notification content is accessible
      const notificationText = screen.getByText(
        'Permission changes may take a few minutes to take effect.',
      );
      expect(notificationText).toBeVisible();

      // If there's an icon, it should have proper aria-hidden
      const icons = document.querySelectorAll('svg');
      icons.forEach((icon) => {
        expect(icon).toHaveAttribute('aria-hidden', 'true');
      });
    });
  });
});
