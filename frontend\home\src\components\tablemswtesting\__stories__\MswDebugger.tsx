import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

interface MswDebuggerProps {
  endpoint: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  buttonLabel?: string;
  payload?: Record<string, any>;
  autoFetch?: boolean;
  simulationOptions?: string[];
}

/**
 * Debug component for testing MSW handlers in Storybook
 */
export const MswDebugger: React.FC<MswDebuggerProps> = ({
  endpoint,
  method = 'GET',
  buttonLabel = 'Test API Connection',
  payload,
  autoFetch = false,
  simulationOptions = ['none', 'error', 'slow', 'empty', 'not-found', 'validation-error']
}) => {
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [requestCount, setRequestCount] = useState(0);
  const [selectedSimulation, setSelectedSimulation] = useState('none');

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log(`[MswDebugger] Sending ${method} request to ${endpoint}`);
      
      // Build URL with simulation parameter if not 'none'
      const url = new URL(endpoint, window.location.origin);
      if (selectedSimulation !== 'none') {
        url.searchParams.append('simulate', selectedSimulation);
      }
      
      const fetchOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };
      
      if ((method === 'POST' || method === 'PUT') && payload) {
        fetchOptions.body = JSON.stringify(payload);
      }
      
      const res = await fetch(url.toString(), fetchOptions);
      console.log(`[MswDebugger] Received response with status: ${res.status}`);
      
      // Try to parse as JSON, but fallback to text if not JSON
      let data;
      const contentType = res.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await res.json();
      } else {
        data = { text: await res.text() };
      }
      
      console.log(`[MswDebugger] Response data:`, data);
      setResponse({
        status: res.status,
        statusText: res.statusText,
        ok: res.ok,
        headers: Object.fromEntries(res.headers.entries()),
        data
      });
      setRequestCount(prev => prev + 1);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error(`[MswDebugger] Error:`, err);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [autoFetch]);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>MSW API Debugger</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Endpoint</Label>
              <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded">{endpoint}</div>
            </div>
            <div>
              <Label>Method</Label>
              <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded">{method}</div>
            </div>
          </div>
          
          <div>
            <Label>Simulate Condition</Label>
            <Select
              value={selectedSimulation}
              onValueChange={setSelectedSimulation}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a simulation condition" />
              </SelectTrigger>
              <SelectContent>
                {simulationOptions.map(option => (
                  <SelectItem key={option} value={option}>
                    {option === 'none' ? 'No Simulation' : option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {payload && (
            <div>
              <Label>Request Payload</Label>
              <pre className="p-2 bg-gray-100 dark:bg-gray-800 rounded overflow-auto text-xs">
                {JSON.stringify(payload, null, 2)}
              </pre>
            </div>
          )}
          
          {error && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <h4 className="text-red-700 dark:text-red-400 font-medium">Error</h4>
              <p className="text-red-600 dark:text-red-300 text-sm">{error}</p>
            </div>
          )}
          
          {response && (
            <div>
              <Label>Response (Request #{requestCount})</Label>
              <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-md space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Status:</span>
                  <span className={response.ok ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                    {response.status} {response.statusText}
                  </span>
                </div>
                <div>
                  <span className="text-sm font-medium">Response Body:</span>
                  <pre className="mt-1 p-2 bg-gray-200 dark:bg-gray-700 rounded overflow-auto text-xs">
                    {JSON.stringify(response.data, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={fetchData} 
          disabled={isLoading} 
          className="w-full"
        >
          {isLoading ? 'Loading...' : buttonLabel}
        </Button>
      </CardFooter>
    </Card>
  );
}; 