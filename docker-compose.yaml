version: '3.8'

services:
  # medusa:
  #   image: medusajs/medusa:latest
  #   container_name: medusa-server
  #   ports:
  #     - "9000:9000"
  #   environment:
  #     DATABASE_URL: ****************************************************/medusa_db
  #     REDIS_URL: redis://redis:6379
  #   depends_on:
  #     - postgres
  #     - redis
  #   volumes:
  #     - ./medusa:/app
  #   working_dir: /app
  #   command: npm run start
  verdaccio:
    image: verdaccio/verdaccio:latest
    container_name: verdaccio
    ports:
      - "4873:4873"
    volumes:
      - verdaccio_storage:/verdaccio/storage
      - verdaccio_config:/verdaccio/conf
      - ./htpasswd:/verdaccio/conf/htpasswd
    restart: unless-stopped



  postgres:
    image: postgres:15
    container_name: medusa-postgres
    restart: always
    environment:
      POSTGRES_USER: medusa_user
      POSTGRES_PASSWORD: medusa_password
      POSTGRES_DB: medusa_db
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data

  redis:
    image: redis:6
    container_name: medusa-redis
    restart: always
    ports:
      - "6379:6379"

volumes:
  pgdata:
  verdaccio_storage:
  verdaccio_config:
