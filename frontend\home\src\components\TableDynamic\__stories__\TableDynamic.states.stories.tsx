'use client';

import * as React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import TableDynamic from '../table-dynamic';
import { simpleData, simpleColumns } from '../__fixtures__/TableDynamic.fixtures';
import type { ColumnConfig } from '../types';

// Enhanced simple columns for stories
const getEnhancedSimpleColumns = (): ColumnConfig[] => {
  return simpleColumns.map((col) => {
    if (col.id === 'price') {
      return {
        ...col,
        cell: ({ row }) => <div className="text-right">${row.getValue('price')}</div>,
      } as ColumnConfig;
    }
    if (col.id === 'name') {
      return {
        ...col,
        cell: ({ row }) => <div className="font-medium">{row.getValue('name')}</div>,
      } as ColumnConfig;
    }
    return col as ColumnConfig;
  });
};

const meta = {
  title: 'Components/TableDynamic/States',
  component: TableDynamic,
  parameters: {
    docs: {
      description: {
        story:
          'Demonstration of different states of the TableDynamic component, including loading, error, and empty states.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="max-w-full p-4">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof TableDynamic>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shows the default state with data.
 */
export const Default: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
  },
};

/**
 * Shows the loading state with a skeleton loader.
 */
export const Loading: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    loading: true,
  },
};

/**
 * Shows the loading state with a simplified skeleton.
 */
export const LoadingSimplified: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    loading: true,
    skeletonProps: {
      simplified: true,
    },
  },
};

/**
 * Shows the error state.
 */
export const Error: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    error: 'Failed to load table data. Please try again later.',
  },
};

/**
 * Shows the empty state when there is no data.
 */
export const Empty: Story = {
  args: {
    data: [],
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
  },
};

/**
 * Shows a custom empty state.
 */
export const CustomEmptyState: Story = {
  args: {
    data: [],
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    emptyState: (
      <div className="py-8 text-center">
        <div className="text-xl font-bold">No products found</div>
        <div className="text-muted-foreground mt-2">Try changing your filters or search term</div>
        <button className="bg-primary text-primary-foreground mt-4 rounded px-4 py-2">
          Add New Product
        </button>
      </div>
    ),
  },
};

/**
 * Shows the disabled state.
 */
export const Disabled: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    disabled: true,
  },
};
