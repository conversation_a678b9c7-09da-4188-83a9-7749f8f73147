import React from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { ProductDetail } from '../product-detail';
import { 
  productWithBadgeFixture, 
  productWithoutBadgeFixture,
  productPromotionFixture,
  productSpecialDiscountFixture,
  productNewArrivalFixture
} from '../__fixtures__/product-detail.fixtures';

const meta: Meta<typeof ProductDetail> = {
  title: 'UI/Product/ProductDetail',
  component: ProductDetail,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'select', 
      options: ['default', 'bordered', 'compact', 'expanded'],
    },
    layout: {
      control: 'select',
      options: ['standard', 'horizontal', 'mobile', 'catalog'],
    },
    showRating: { control: 'boolean' },
    showSalesCount: { control: 'boolean' },
    showSpecifications: { control: 'boolean' },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ProductDetail>;

// Default: มี badge tag แบบ "3 แต้ม 1"
export const WithBadge: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'default',
    layout: 'standard',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'With Badge Tag (3 แต้ม 1)',
};

// ไม่มี badge tag
export const WithoutBadge: Story = {
  args: {
    product: productWithoutBadgeFixture,
    variant: 'default',
    layout: 'standard',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Without Badge Tag',
};

// มี badge tag แบบ "ซื้อ 1 แถม 1"
export const PromotionBadge: Story = {
  args: {
    product: productPromotionFixture,
    variant: 'default',
    layout: 'standard',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Promotion Badge (ซื้อ 1 แถม 1)',
};

// มี badge tag แบบ "ลดพิเศษ 56%"
export const DiscountBadge: Story = {
  args: {
    product: productSpecialDiscountFixture,
    variant: 'default',
    layout: 'standard',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Discount Badge (ลดพิเศษ 56%)',
};

// มี badge tag แบบ "สินค้าใหม่"
export const NewArrivalBadge: Story = {
  args: {
    product: productNewArrivalFixture,
    variant: 'default',
    layout: 'standard',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'New Arrival Badge (สินค้าใหม่)',
};

// Variant: bordered
export const BorderedVariant: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'bordered',
    layout: 'standard',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Bordered Variant',
};

// Variant: compact
export const CompactVariant: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'compact',
    layout: 'mobile',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Compact Variant',
};

// Variant: expanded
export const ExpandedVariant: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'expanded',
    layout: 'standard',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Expanded Variant',
};

// Layout: horizontal
export const HorizontalLayout: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'default',
    layout: 'horizontal',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Horizontal Layout',
};

// Layout: mobile
export const MobileLayout: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'default',
    layout: 'mobile',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Mobile Layout',
};

// Layout: catalog
export const CatalogLayout: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'default',
    layout: 'catalog',
    showRating: true,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Catalog Layout',
};

// ไม่แสดงคะแนน
export const WithoutRating: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'default',
    layout: 'standard',
    showRating: false,
    showSalesCount: true,
    showSpecifications: true,
  },
  name: 'Without Rating',
};

// ไม่แสดงยอดขาย
export const WithoutSalesCount: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'default',
    layout: 'standard',
    showRating: true,
    showSalesCount: false,
    showSpecifications: true,
  },
  name: 'Without Sales Count',
};

// ไม่แสดงรายละเอียด
export const WithoutSpecifications: Story = {
  args: {
    product: productWithBadgeFixture,
    variant: 'default',
    layout: 'standard',
    showRating: true,
    showSalesCount: true,
    showSpecifications: false,
  },
  name: 'Without Specifications',
};