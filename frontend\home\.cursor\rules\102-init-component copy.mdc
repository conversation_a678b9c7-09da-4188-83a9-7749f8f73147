---
description: 
globs: src/components/**,.storybook/mswHandlers/**,src/lib/**
alwaysApply: false
---
# Production-Ready React Component Design System

## Core Design Principles

### Visual Hierarchy
- Group related information in cards and sections
- Use headings, icons, and color-coding for structure
- Consistent spacing for visual separation
- Strategic elevation (shadows) for depth

### Accessibility (WCAG 2.1 AAA)
- 7:1 contrast ratio for normal text, 4.5:1 for large text
- ARIA labels for interactive elements
- Keyboard navigation with visible focus states (3:1 min contrast)
- Screen reader compatibility with semantic HTML
- Respect `prefers-reduced-motion` settings

### Typography
- Consistent type scale with clear hierarchy
- 16px minimum font size with 1.5 line height
- 80 character maximum line length
- Left-aligned text for LTR languages
- Text resizable to 200% without loss of functionality

### Interactive Elements
- Visually distinct with hover/focus states
- Clear affordances for clickable elements
- Immediate feedback for interactions
- Tooltips for supplementary information
- Confirmation for destructive actions

### Status Indicators
- Consistent color-coding with adequate contrast
- Multiple cues beyond color (icons, patterns, text)
- Screen reader accessible descriptions

### Responsive Design
- Support all breakpoints from '3xs' (256px) to '7xl' (1280px)
- Mobile-first approach with progressive enhancement
- 44×44px minimum touch targets with adequate spacing
- No horizontal scrolling at widths ≥320px

### Implementation Workflow
1. Define types and interfaces first
2. Create accessible loading skeletons
3. Implement animations with reduced-motion alternatives
4. Use CSS variables and Tailwind utilities
5. Add error handling and empty states
6. Ensure keyboard and screen reader support
7. Test across all breakpoints

### Component Architecture
- Use compound component pattern for complex UI
- Forward refs for all components
- Set explicit displayNames for debugging
- Use proper HTML semantics
- Separate presentation, logic, and data fetching

## UI Component Implementation

### Mandatory Workflow

1. **MSW Handlers First**
   ```
   src/.storybook/mswHandlers/{ComponentName}.handlers.ts
   ```

2. **Mock Data with faker-js**
   ```
   src/components/{ComponentName}/__fixtures__/{ComponentName}.mockData.ts
   ```
   
3. **Type Definitions and Skeletons**
   ```
   src/components/{ComponentName}/{ComponentName}-Types.ts
   src/components/{ComponentName}/{ComponentName}-Skeleton.tsx
   ```

4. **Animation Variants**
   ```
   src/components/{ComponentName}/{ComponentName}-Animations.ts
   ```

5. **Style Variants**
   ```
   src/components/{ComponentName}/{ComponentName}-Variants.ts
   ```

6. **Component Implementation**
   ```
   src/components/{ComponentName}/{ComponentName}.tsx
   ```

7. **Storybook Stories**
   ```
   src/components/{ComponentName}/__stories__/{ComponentName}.stories.tsx
   ```

### Global Theme Integration

- Use CSS variables from `src/styles/globals.css`
- Never implement theme switching at component level
- Use Tailwind classes referencing CSS variables
- Support automatic dark mode via `.dark` class

### CSS Variables Example

```css
:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --primary: 142.1 76.2% 36.3%;
  --primary-foreground: 355.7 100% 97.3%;
  /* More variables... */
}

.dark {
  --background: 20 14.3% 4.1%;
  --foreground: 0 0% 95%;
  /* Dark mode variables... */
}
```

### Common Tailwind Classes

| CSS Variable | Tailwind Class | Usage |
|--------------|----------------|-------|
| --background | `bg-background` | Main backgrounds |
| --foreground | `text-foreground` | Main text color |
| --primary | `bg-primary` | Primary actions |
| --primary-foreground | `text-primary-foreground` | Text on primary backgrounds |
| --secondary | `bg-secondary` | Secondary elements |
| --muted | `bg-muted` | Subtle backgrounds |
| --accent | `bg-accent` | Accent backgrounds |
| --destructive | `bg-destructive` | Error states |
| --border | `border-border` | Border colors |
| --ring | `ring-ring` | Focus rings |

## Component Architecture with Compound Pattern

```typescript
// Factory approach for compound components
function createCompoundPart<T, P extends React.HTMLAttributes<T>>(
  displayName: string,
  Component: React.ForwardRefExoticComponent<P & React.RefAttributes<T>> | keyof JSX.IntrinsicElements,
  defaultProps?: Partial<P>
) {
  const CompoundPart = React.forwardRef<T, P>((props, ref) => {
    const combinedProps = { ...defaultProps, ...props, ref } as ComponentProps;
    return React.createElement(Component, combinedProps);
  });
  
  CompoundPart.displayName = displayName;
  return CompoundPart;
}

// Main component with compound structure
const {ComponentName} = React.forwardRef<HTMLElement, Props>((props, ref) => {
  // Implementation
});

// Create subcomponents
const {ComponentName}Header = createCompoundPart('{ComponentName}Header', 'div');
const {ComponentName}Title = createCompoundPart('{ComponentName}Title', 'h3');
const {ComponentName}Content = createCompoundPart('{ComponentName}Content', 'div');

// Attach subcomponents
{ComponentName}.Header = {ComponentName}Header;
{ComponentName}.Title = {ComponentName}Title;
{ComponentName}.Content = {ComponentName}Content;
```

## TanStack Table Requirements

Always use TanStack Table with these features:
- Strongly typed column definitions
- Virtual scrolling for large datasets
- Sorting & filtering capabilities
- Controlled state management
- WCAG compliance
- Visual enhancements (badges, avatars, tooltips)

```typescript
// Column definition example
const columns = [
  columnHelper.accessor('avatar', {
    header: 'Profile',
    cell: info => (
      <Avatar>
        <AvatarImage src={info.row.original.avatarUrl} alt={`${info.row.original.name}'s profile`} />
        <AvatarFallback>{info.row.original.name.charAt(0).toUpperCase()}</AvatarFallback>
      </Avatar>
    ),
  }),
  
  columnHelper.accessor('status', {
    header: 'Status',
    cell: info => (
      <Badge className={statusStyles[info.getValue()]} aria-label={`Status: ${info.getValue()}`}>
        {info.getValue()}
      </Badge>
    ),
  }),
  
  // Actions column
  columnHelper.display({
    id: 'actions',
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => (
      <div className="flex items-center gap-2 justify-end">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="sm" aria-label={`View details for ${row.original.name}`}>
              <EyeIcon className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>View details</TooltipContent>
        </Tooltip>
        {/* More actions... */}
      </div>
    ),
  }),
];

// TanStack Table implementation
function DataTable({ data }) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });
  
  // Virtual scrolling
  const { rows } = table.getRowModel();
  const virtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 56,
  });
  
  return (
    <div role="region" aria-label="Data table">
      {/* Table implementation */}
    </div>
  );
}
```

## Animation System with Framer Motion

All animations must:
- Respect `prefers-reduced-motion`
- Be pausable/stoppable/hideable
- Have maximum duration of 5 seconds unless controllable
- Use variants for coordinated animations
- Enhance understanding rather than distract
- Optimize performance to maintain 60fps

```typescript
// Animation variants
export const transitions = {
  quick: { duration: 0.2 },
  normal: { duration: 0.3 },
  slow: { duration: 0.5 },
  spring: { type: 'spring', stiffness: 300, damping: 30 },
  stagger: (delay = 0.05) => ({ 
    staggerChildren: delay,
    delayChildren: delay * 2
  }),
};

export const states = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  fadeIn: { opacity: 1 },
  fadeInUp: { opacity: 1 },
  fadeOut: { opacity: 0 },
  hover: { scale: 1.02, y: -2 },
  tap: { scale: 0.98 },
};

// Reduced motion alternatives
export const reducedMotionStates = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  fadeIn: { opacity: 1 },
  fadeInUp: { opacity: 1 }, // No movement
  fadeOut: { opacity: 0 },
  hover: { boxShadow: '0 0 0 2px currentColor' },
  tap: { opacity: 0.9 },
};

// Hook for respecting reduced motion
export function useAnimationVariants(variants, disableAnimations = false) {
  const prefersReducedMotion = useReducedMotion();
  
  if (prefersReducedMotion || disableAnimations) {
    // Return simplified variants
    return {...};
  }
  
  return variants;
}

// Usage example
const animationVariants = useAnimationVariants(variants);

return (
  <motion.div
    variants={animationVariants.container}
    initial="initial"
    animate="animate"
    exit="exit"
  >
    {items.map(item => (
      <motion.div 
        key={item.id}
        variants={animationVariants.item}
        whileHover="hover"
        whileTap="tap"
      >
        {/* Item content */}
      </motion.div>
    ))}
  </motion.div>
);
```

## Styling with class-variance-authority (cva)

All components must use cva for style variants:
- Comprehensive variants (size, color, density)
- Sensible defaults
- Tailwind CSS integration
- Class name merging with cn utility

```typescript
// Variant definition
const buttonVariants = cva(
  'rounded-md shadow transition-colors focus:outline-none focus:ring-2',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        outline: 'border border-input bg-background hover:bg-accent',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        destructive: 'bg-destructive text-destructive-foreground',
      },
      size: {
        sm: 'h-8 px-2 py-1 text-sm',
        md: 'h-10 px-4 py-2',
        lg: 'h-12 px-6 py-3 text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

// Component using variants
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size }), className)}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';
```

## Component States

Every component must implement:
1. **Loading State** with skeletons
2. **Empty State** with helpful messaging
3. **Error State** with clear visualization
4. **Success State** with appropriate feedback

```typescript
// Component with state handling
const Component = ({ loading, error, data, emptyMessage = 'No items' }) => {
  if (loading) {
    return <ComponentSkeleton />;
  }
  
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error.message || 'An error occurred'}</AlertDescription>
      </Alert>
    );
  }
  
  if (!data || data.length === 0) {
    return (
      <EmptyState
        icon={<InboxIcon className="h-8 w-8" />}
        title="No items found"
        description={emptyMessage}
      />
    );
  }
  
  // Success state (main component rendering)
  return (
    <Card>
      {/* Component implementation */}
    </Card>
  );
};
```

## MSW Handler Implementation

```typescript
// Factory for MSW handlers
function createApiHandlers(config) {
  const { endpoint, mockData } = config;
  
  function parseQueryParams(url) {
    const page = Number(url.searchParams.get('page') || 1);
    const pageSize = Number(url.searchParams.get('perPage') || 25);
    const search = url.searchParams.get('search') || '';
    const sortBy = url.searchParams.get('sortBy') || 'id';
    const sortOrder = url.searchParams.get('sortOrder') || 'asc';
    const simulate = url.searchParams.get('simulate') || '';
    
    return { page, pageSize, search, sortBy, sortOrder, simulate };
  }
  
  return [
    http.get(endpoint, async ({ request }) => {
      // Implementation for GET collection
    }),
    
    http.get(`${endpoint}/:id`, async ({ params, request }) => {
      // Implementation for GET single item
    }),
    
    http.post(endpoint, async ({ request }) => {
      // Implementation for POST create
    }),
    
    http.put(`${endpoint}/:id`, async ({ params, request }) => {
      // Implementation for PUT update
    }),
    
    http.delete(`${endpoint}/:id`, async ({ params, request }) => {
      // Implementation for DELETE
    })
  ];
}
```

## Mock Data with faker-js

```typescript
// Generate 500-2000 records
const RECORD_COUNT = faker.number.int({ min: 500, max: 2000 });

// Factory functions for complex nested data
const mockDataFactories = {
  createContact: () => ({
    email: faker.internet.email(),
    phone: faker.phone.number(),
    address: {
      street: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      country: faker.location.country(),
      zipCode: faker.location.zipCode(),
    }
  }),
  
  createStatistics: () => ({
    views: faker.number.int({ min: 0, max: 10000 }),
    likes: faker.number.int({ min: 0, max: 1000 }),
    shares: faker.number.int({ min: 0, max: 500 }),
    rating: faker.number.float({ min: 0, max: 5, precision: 0.1 }),
  }),
};

// Generate mock items
function generateMockItem() {
  return {
    id: faker.string.uuid(),
    name: faker.person.fullName(),
    status: faker.helpers.arrayElement(['draft', 'published', 'archived']),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    avatarUrl: faker.image.avatar(),
    description: faker.helpers.maybe(() => faker.lorem.paragraph(), { probability: 0.7 }),
    tags: Array.from(
      { length: faker.number.int({ min: 0, max: 5 }) },
      () => faker.word.sample()
    ),
    contact: mockDataFactories.createContact(),
    statistics: mockDataFactories.createStatistics(),
  };
}

export const mockData = Array.from(
  { length: RECORD_COUNT },
  () => generateMockItem()
);
```

## Skeleton Implementation

```typescript
// Skeleton component
export const ComponentSkeleton = ({
  count = 5,
  className = '',
  disableAnimation = false,
}) => {
  const WrappedSkeleton = ({ children }) => {
    if (disableAnimation) {
      return <>{children}</>;
    }
    
    return (
      <motion.div
        initial="initial"
        animate="animate"
        variants={skeletonAnimationVariants}
      >
        {children}
      </motion.div>
    );
  };

  return (
    <div className={className}>
      {Array.from({ length: count }).map((_, index) => (
        <WrappedSkeleton key={index}>
          <div className="flex items-start gap-4 mb-4 p-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-3 w-[180px]" />
            </div>
          </div>
        </WrappedSkeleton>
      ))}
    </div>
  );
};
```

## Type Definitions

```typescript
export interface ComponentData {
  /** Unique identifier */
  id: string;
  
  /** Display name */
  name: string;
  
  /** Current status */
  status: ComponentStatus;
  
  /** Creation timestamp */
  createdAt: string;
  
  /** Last update timestamp */
  updatedAt: string;
  
  /** Optional description */
  description?: string;
  
  /** Associated tags */
  tags: string[];
  
  /** Configuration settings */
  settings: ComponentSettings;
}

export enum ComponentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

export interface ComponentSettings {
  /** Visibility setting */
  isVisible: boolean;
  
  /** Feature flags */
  features: {
    /** Enable advanced features */
    advanced: boolean;
    /** Enable experimental features */
    experimental: boolean;
  };
}

export interface ComponentProps {
  /** Initial data to display */
  initialData?: ComponentData[];
  
  /** Loading state */
  isLoading?: boolean;
  
  /** Error state */
  error?: Error;
  
  /** Display variant */
  variant?: 'default' | 'compact' | 'expanded';
  
  /** Custom CSS class */
  className?: string;
}
```

## Network State Awareness

```typescript
// Network status hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(onlineManager.isOnline());
  
  useEffect(() => {
    const unsubscribe = onlineManager.setEventListener(online => {
      setIsOnline(online);
    });
    
    return unsubscribe;
  }, []);
  
  return { isOnline };
}

// Component with network awareness
function useComponentData(options) {
  const { isOnline } = useNetworkStatus();
  
  const query = useQuery(
    createNetworkAwareQueryOptions({
      queryKey: options.queryKey,
      queryFn: async () => {
        if (!isOnline) {
          throw new Error('You are offline');
        }
        
        const response = await fetch(options.endpoint);
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        
        return response.json();
      },
      retry: isOnline ? 3 : false,
      initialData: options.initialData,
      offlineFallbackData: options.offlineFallbackData,
      refetchOnReconnect: true,
    })
  );
  
  return {
    ...query,
    isOnline,
  };
}
```

## Storybook Stories

```typescript
// stories.tsx
const meta: Meta<typeof Component> = {
  title: 'Components/Component',
  component: Component,
  parameters: {
    msw: {
      handlers: ComponentHandlers,
    },
    layout: 'padded',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'compact', 'expanded'],
    },
  },
  args: {
    variant: 'default',
  },
  tags: ['autodocs']
};

export default meta;
type Story = StoryObj<typeof Component>;

// Default story
export const Default: Story = {
  name: 'Default',
  args: {}
};

// Loading state
export const Loading: Story = {
  name: 'Loading State',
  args: {
    isLoading: true
  }
};

// Error state
export const Error: Story = {
  name: 'Error State',
  args: {
    error: new Error('Failed to load data')
  }
};

// Empty state
export const Empty: Story = {
  name: 'Empty State',
  args: {
    initialData: []
  }
};
```

## Accessibility Guidelines

### Table Accessibility

```typescript
<div
  role="table"
  aria-label="User Profiles"
  aria-rowcount={totalRowCount}
  aria-colcount={columns.length}
  className="tableContainer"
>
  <div role="rowgroup" aria-label="Table headers">
    <div role="row">
      {headers.map(header => (
        <div
          role="columnheader"
          aria-sort={header.column.getIsSorted() || 'none'}
          aria-colindex={header.index + 1}
          tabIndex={0}
          className="focusable-header"
        >
          {/* Header content */}
        </div>
      ))}
    </div>
  </div>
  
  <div role="rowgroup" aria-label="Table data">
    {rows.map((row, rowIndex) => (
      <div
        role="row"
        aria-rowindex={rowIndex + 1 + (page * pageSize)}
        tabIndex={0}
        aria-selected={row.getIsSelected() || undefined}
      >
        {row.getVisibleCells().map((cell, cellIndex) => (
          <div
            role="cell"
            aria-colindex={cellIndex + 1}
            data-column-id={cell.column.id}
          >
            {/* Cell content */}
          </div>
        ))}
      </div>
    ))}
  </div>
</div>
```

### Form Accessibility

```typescript
<form 
  aria-labelledby="form-title"
  aria-describedby="form-description"
  className="space-y-6"
  noValidate
>
  <h2 id="form-title" className="text-2xl font-semibold">Create Account</h2>
  
  <p id="form-description" className="text-muted-foreground">
    All fields marked with an asterisk (*) are required.
  </p>
  
  <fieldset className="space-y-4 border rounded-md p-4">
    <legend className="text-lg font-medium px-2">Personal Information</legend>
    
    <div className="space-y-2">
      <Label htmlFor="full-name" className="flex items-center gap-1">
        Full Name <span className="text-destructive" aria-hidden="true">*</span>
        <span className="sr-only">(required)</span>
      </Label>
      <Input
        id="full-name"
        name="fullName"
        type="text"
        autoComplete="name"
        aria-required="true"
        aria-describedby="name-hint name-error"
      />
      <p id="name-hint" className="text-sm text-muted-foreground">
        Enter your legal full name as it appears on official documents.
      </p>
      {errors.fullName && (
        <p id="name-error" className="text-sm text-destructive font-medium" aria-live="polite">
          {errors.fullName}
        </p>
      )}
    </div>
  </fieldset>
</form>
```

## Responsive Design System

All components must be responsive across these breakpoints:

| Breakpoint | Size (pixels) | Target Devices            |
|------------|---------------|---------------------------|
| '3xs'      | 256px         | Tiny displays, watches    |
| '2xs'      | 288px         | Extra small displays      |
| 'xs'       | 320px         | Small phones              |
| 'sm'       | 384px         | Phones                    |
| 'md'       | 448px         | Large phones              |
| 'lg'       | 512px         | Small tablets             |
| 'xl'       | 576px         | Tablets                   |
| '2xl'      | 672px         | Large tablets             |
| '3xl'      | 768px         | Small laptops             |
| '4xl'      | 896px         | Laptops                   |
| '5xl'      | 1024px        | Desktops                  |
| '6xl'      | 1152px        | Large desktops            |
| '7xl'      | 1280px        | Extra large desktops      |

```typescript
// Responsive hooks
function useResponsive() {
  const [breakpoint, setBreakpoint] = useState<BreakpointKey>('md');
  
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width < 256) setBreakpoint('3xs');
      else if (width < 288) setBreakpoint('2xs');
      else if (width < 320) setBreakpoint('xs');
      else if (width < 384) setBreakpoint('sm');
      else if (width < 448) setBreakpoint('md');
      else if (width < 512) setBreakpoint('lg');
      else if (width < 576) setBreakpoint('xl');
      else if (width < 672) setBreakpoint('2xl');
      else if (width < 768) setBreakpoint('3xl');
      else if (width < 896) setBreakpoint('4xl');
      else if (width < 1024) setBreakpoint('5xl');
      else if (width < 1152) setBreakpoint('6xl');
      else setBreakpoint('7xl');
    };
    
    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);
  
  return breakpoint;
}

// Component with responsive props
interface ResponsiveComponentProps {
  responsiveVariants?: Partial<Record<BreakpointKey, {
    variant?: string;
    size?: string;
    layout?: 'stack' | 'grid' | 'row';
  }>>;
  hideOnBreakpoint?: BreakpointKey[];
}

function ResponsiveComponent({ 
  responsiveVariants,
  hideOnBreakpoint,
  ...defaultProps 
}: ResponsiveComponentProps) {
  const breakpoint = useResponsive();
  
  if (hideOnBreakpoint?.includes(breakpoint)) {
    return null;
  }
  
  const propsForBreakpoint = {
    ...defaultProps,
    ...(responsiveVariants?.[breakpoint] || {})
  };
  
  return (
    <div className={`responsive-component size-${propsForBreakpoint.size}`}>
      {/* Component implementation */}
    </div>
  );
}
```

## Reuse Strategy

Before implementing any component:

1. Check `@/components/ui/index.ts` first for existing components
2. Only create custom components when:
   - The component has unique business logic
   - It requires specialized behavior not in the UI library
   - It's specifically requested to be implemented from scratch
3. Tables are special - always use TanStack Table directly

## Core Principles Summary

1. **Production Parity**: Identical behavior in development and production
2. **Full Optimization**: Performance-first with virtualization
3. **Comprehensive Design**: Mobile-first, accessible, with global theming
4. **Framework Integration**: Works with TanStack, shadcn/ui, Framer Motion
5. **Developer Experience**: Consistent patterns and comprehensive testing 