import type { <PERSON>a, StoryObj } from '@storybook/react';
import { LinkList } from '../navigation-link-list';

const meta: Meta<typeof LinkList> = {
  title: 'UI/Navigation/LinkList',
  component: LinkList,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    title: { 
      control: 'text',
      description: 'Menu title'
    },
    items: { 
      control: 'object',
      description: 'List of menu items'
    },
    className: { 
      control: 'text',
      description: 'Additional CSS class for container'
    },
    titleClassName: { 
      control: 'text',
      description: 'Additional CSS class for title'
    },
    itemClassName: { 
      control: 'text',
      description: 'Additional CSS class for items'
    },
    listClassName: { 
      control: 'text',
      description: 'Additional CSS class for list container'
    },
  },
};

export default meta;
type Story = StoryObj<typeof LinkList>;

// สร้างข้อมูลตัวอย่าง - ข้อมูลบริษัท
const companyInfoItems = [
  {
    label: 'เกี่ยวกับเรา',
    href: '/about',
    external: false,
  },
  {
    label: 'ข่าวสารและกิจกรรม',
    href: '/news',
    external: false,
  },
  {
    label: 'ติดต่อเรา',
    href: '/contact',
    external: false,
  },
];

// สร้างข้อมูลตัวอย่าง - บริการ
const serviceItems = [
  {
    label: 'บริการทั้งหมด',
    href: '/services',
    external: false,
  },
  {
    label: 'การสั่งซื้อ',
    href: '/order',
    external: false,
  },
  {
    label: 'การจัดส่ง',
    href: '/shipping',
    external: false,
  },
];

// สร้างข้อมูลตัวอย่าง - ลิงก์ภายนอก
const externalLinkItems = [
  {
    label: 'Facebook',
    href: 'https://facebook.com/company',
    external: true,
  },
  {
    label: 'Instagram',
    href: 'https://instagram.com/company',
    external: true,
  },
  {
    label: 'LinkedIn',
    href: 'https://linkedin.com/company',
    external: true,
  },
];

// สร้างข้อมูลตัวอย่าง - บริการ
const helpCenterItems = [
    {
      label: 'คำถามที่พบบ่อย',
      href: '/howto',
      external: false,
    },
    {
      label: 'ข้อมูลการติดต่อ',
      href: '/faq',
      external: false,
    },
  ];

export const CompanyInfo: Story = {
  args: {
    title: 'ข้อมูลบริษัท',
    items: companyInfoItems,
  },
};

export const ExternalLinks: Story = {
  args: {
    title: 'ติดตามเรา',
    items: externalLinkItems,
  },
};

export const CustomTheme: Story = {
  args: {
    title: 'ข้อมูลบริษัท',
    items: companyInfoItems,
    className: 'p-4 bg-slate-100 rounded-lg',
    titleClassName: 'text-xl font-bold text-blue-600',
    itemClassName: 'text-gray-700 hover:text-blue-500',
    listClassName: 'list-none pl-0',
  },
};

export const FooterGroup: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-y-6 gap-x-16 bg-gray-100 p-6 rounded-lg w-full max-w-4xl">
      <LinkList
        title="ข้อมูลบริษัท"
        items={companyInfoItems}
      />
      <LinkList
        title="บริการ"
        items={serviceItems}
      />
      <LinkList
        title="ติดตามเรา"
        items={externalLinkItems}
      />
      <LinkList
        title="ศูนย์ช่วยเหลือ"
        items={helpCenterItems}
      />
    </div>
  ),
};