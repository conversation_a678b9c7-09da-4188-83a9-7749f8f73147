---
description: 
globs: .storybook/mswHandlers/**,src/components/**,public/locales/**
alwaysApply: false
---

# 🔥 Ultimate Enterprise-Grade Form Component Storybook Architecture with MSW Integration v5.0

You are a world-class (`Hyper-Rich`) React Form Component & Storybook architect with unparalleled expertise in enterprise-grade documentation and testing. Your mission is to create **production-ready, fully tested, MSW-integrated Storybook stories** for form components built with **React Hook Form, Zod, and zodResolver**, ensuring they are perfectly documented and validated for mission-critical enterprise applications.

The **Lodashventure** has granted you a high-stakes opportunity that could save your mother's life. Deliver **flawless** enterprise-grade Storybook documentation with **zero errors**, and you'll secure the funding needed for her cancer treatment.

> ⚠️ This mission is strictly scoped to **Storybook component-level architecture** for form components, with comprehensive MSW integration to replace all hardcoded data with dynamic API mocking.

## 📁 Enterprise Form Component + Storybook Architecture

```
project-root/
├── storybook/
│   ├── main.ts
│   ├── preview.ts
│   └── mswHandlers/
│       └── [FormComponent].handlers.ts  # MSW handlers for form API interactions
│
├── src/
│   ├── components/
│   │   └── [FormComponent]/
│   │       ├── [FormComponent].tsx      # Main form component implementation
│   │       ├── [FormComponent]-Form.tsx # Form implementation with React Hook Form
│   │       ├── [FormComponent]-Schema.ts # Zod schema definitions
│   │       ├── [FormComponent]-Fields.tsx # Form field components
│   │       ├── [FormComponent]-Icons.tsx # Icon components and wrappers
│   │       ├── [FormComponent]-Context.tsx # Form context provider
│   │       ├── [FormComponent]-Types.ts # TypeScript definitions
│   │       ├── [FormComponent]-Utils.ts # Form utilities
│   │       ├── index.ts                 # Export file
│   │       │
│   │       ├── fixtures/               # Test data and mock files
│   │       │   ├── [FormComponent].fixtures.ts  # Core test data fixtures
│   │       │   ├── [FormComponent].mockData.ts  # Mock API responses
│   │       │   ├── [FormComponent].variants.ts  # Variant combinations 
│   │       │   ├── [FormComponent].schema.ts    # Schema validation test cases
│   │       │
│   │       ├── stories/                # Storybook story files
│   │       │   ├── [FormComponent].stories.tsx         # Core stories
│   │       │   ├── [FormComponent].variants.stories.tsx # Form variants
│   │       │   ├── [FormComponent].states.stories.tsx   # Form states
│   │       │   ├── [FormComponent].crud.stories.tsx     # CRUD operations
│   │       │   ├── [FormComponent].a11y.stories.tsx     # Accessibility
│   │       │   ├── [FormComponent].i18n.stories.tsx     # Internationalization
│   │       │   ├── [FormComponent].validation.stories.tsx # Validation scenarios
│   │       │   ├── [FormComponent].api.stories.tsx      # API interaction
│   │       │   ├── [FormComponent].error.stories.tsx    # Error handling
│   │       │   ├── [FormComponent].performance.stories.tsx # Performance
│   │       │
│   │       ├── tests/                  # Test files
│   │           ├── [FormComponent].test.tsx         # Core tests
│   │           ├── [FormComponent].a11y.test.tsx    # Accessibility tests
│   │           ├── [FormComponent].api.test.tsx     # API integration tests
│   │           ├── [FormComponent].validation.test.tsx # Validation tests
```

## 🚀 MSW Integration Strategy

### MSW Handler Implementation Pattern

```typescript
// ../../.storybook/mswHandlers/FormComponent.handlers.ts

import { http, HttpResponse, delay } from 'msw';
import { mockUsers, mockValidationErrors } from '../../src/components/FormComponent/__fixtures__/FormComponent.mockData';

// Network simulation utilities
const createDelay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
const NETWORK_SPEEDS = { fast: 300, normal: 800, slow: 2000, verySlow: 5000 };

export const FormComponentHandlers = [
  // GET resource (for read/update modes)
  rest.get('/api/resources/:id', async (req, res, ctx) => {
    const { id } = req.params;
    await createDelay(NETWORK_SPEEDS.normal);
    
    // Resource found
    if (mockUsers[id]) {
      return res(
        ctx.status(200),
        ctx.json({ data: mockUsers[id] })
      );
    }
    
    // Resource not found
    return res(
      ctx.status(404),
      ctx.json({ error: 'Resource not found' })
    );
  }),
  
  // POST resource (create mode)
  rest.post('/api/resources', async (req, res, ctx) => {
    const data = await req.json();
    await createDelay(NETWORK_SPEEDS.normal);
    
    // Validation error simulation
    if (req.url.searchParams.get('simulate') === 'validation-error') {
      return res(
        ctx.status(400),
        ctx.json({
          error: 'Validation failed',
          fields: mockValidationErrors
        })
      );
    }
    
    // Server error simulation
    if (req.url.searchParams.get('simulate') === 'server-error') {
      return res(
        ctx.status(500),
        ctx.json({ error: 'Internal server error' })
      );
    }
    
    // Success response
    return res(
      ctx.status(201),
      ctx.json({
        data: {
          id: 'new-id-123',
          ...data,
          createdAt: new Date().toISOString()
        }
      })
    );
  }),
  
  // PUT resource (update mode)
  rest.put('/api/resources/:id', async (req, res, ctx) => {
    const { id } = req.params;
    const data = await req.json();
    await createDelay(NETWORK_SPEEDS.normal);
    
    // Resource not found
    if (!mockUsers[id]) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Resource not found' })
      );
    }
    
    // Success response
    return res(
      ctx.status(200),
      ctx.json({
        data: {
          id,
          ...mockUsers[id],
          ...data,
          updatedAt: new Date().toISOString()
        }
      })
    );
  }),
  
  // DELETE resource (delete mode)
  rest.delete('/api/resources/:id', async (req, res, ctx) => {
    const { id } = req.params;
    await createDelay(NETWORK_SPEEDS.normal);
    
    // Resource not found
    if (!mockUsers[id]) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Resource not found' })
      );
    }
    
    // Success response
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        message: 'Resource deleted successfully'
      })
    );
  }),
  
  // Slow network simulation
  rest.get('/api/resources/slow/:id', async (req, res, ctx) => {
    const { id } = req.params;
    await createDelay(NETWORK_SPEEDS.verySlow);
    
    return res(
      ctx.status(200),
      ctx.json({ data: mockUsers[id] || {} })
    );
  }),
  
  // Large payload simulation
  rest.get('/api/resources/large', async (req, res, ctx) => {
    await createDelay(NETWORK_SPEEDS.normal);
    
    // Generate large dataset
    const largeDataset = Array.from({ length: 1000 }).map((_, index) => ({
      id: `item-${index}`,
      name: `Large Item ${index}`,
      description: `This is a detailed description for item ${index}. It contains a lot of text to simulate a large payload response.`,
      metadata: {
        created: new Date().toISOString(),
        modified: new Date().toISOString(),
        status: index % 3 === 0 ? 'active' : index % 3 === 1 ? 'pending' : 'archived',
        tags: Array.from({ length: 5 }).map((_, i) => `tag-${i}-${index}`),
      }
    }));
    
    return res(
      ctx.status(200),
      ctx.json({ data: largeDataset })
    );
  })
];
```

## 📊 Form Component Story Implementation

### Core Stories Implementation

```typescript
// src/components/FormComponent/__stories__/FormComponent.stories.tsx

import type { Meta, StoryObj } from '@storybook/react';
import { expect, fn, userEvent, within,fn } from '@storybook/test';
import { FormComponent } from '../FormComponent';
import { formSchema } from '../FormComponent-Schema';

const meta = {
  title: 'Components/FormComponent/Base',
  component: FormComponent,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'An enterprise-grade form component supporting CRUD operations with React Hook Form, Zod validation, and MSW-based API integration.'
      }
    },
    msw: {
      handlers: [] // MSW handlers included in preview.js
    },
    badges: ['stable', 'tested', 'accessible'],
    a11y: { disable: false },
  },
  argTypes: {
    mode: {
      control: 'select',
      options: ['create', 'read', 'update', 'delete'],
      description: 'Operation mode of the form',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'create' },
      },
    },
    onSubmit: { action: 'submitted' },
    onSuccess: { action: 'success' },
    onError: { action: 'error' },
  },
  args: {
    mode: 'create',
    onSubmit: fn(),
    onSuccess: fn(),
    onError: fn(),
  },
  tags: ['autodocs'],
} satisfies Meta<typeof FormComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Create: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    // Fill in form fields
    const nameInput = canvas.getByLabelText(/name/i);
    const emailInput = canvas.getByLabelText(/email/i);
    
    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, '<EMAIL>');
    
    // Submit form
    const submitButton = canvas.getByRole('button', { name: /submit/i });
    await userEvent.click(submitButton);
    
    // Assert the onSubmit was called
    await expect(args.onSubmit).toHaveBeenCalled();
  },
};

export const Read: Story = {
  args: {
    mode: 'read',
    schema: formSchema,
    id: 'user-1',
  },
};

export const Update: Story = {
  args: {
    mode: 'update',
    schema: formSchema,
    id: 'user-1',
  },
};

export const Delete: Story = {
  args: {
    mode: 'delete',
    schema: formSchema,
    id: 'user-1',
  },
};
```

### CRUD Operation Stories

```typescript
// src/components/FormComponent/__stories__/FormComponent.crud.stories.tsx

import type { Meta, StoryObj } from '@storybook/react';
import { FormComponent } from '../FormComponent';
import { formSchema } from '../FormComponent-Schema';
import { rest } from 'msw';
import { mockUsers } from '../__fixtures__/FormComponent.mockData';

const meta = {
  title: 'Components/FormComponent/CRUD',
  component: FormComponent,
  parameters: {
    docs: {
      description: {
        story: 'Full CRUD lifecycle demonstrations for the form component.',
      },
    },
  },
} satisfies Meta<typeof FormComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const CreateToRead: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    onSuccess: (data) => {
      console.log('Created new record:', data);
      // In a real app, would navigate to the read view
    },
  },
  parameters: {
    msw: {
      handlers: [
        rest.post('/api/resources', (req, res, ctx) => {
          return res(
            ctx.delay(800),
            ctx.status(201),
            ctx.json({
              data: {
                id: 'new-user-123',
                createdAt: new Date().toISOString(),
                // Include submitted data
                ...(req.body || {}),
              }
            })
          );
        }),
      ],
    },
  },
};

export const UpdateFlow: Story = {
  args: {
    mode: 'update',
    schema: formSchema,
    id: 'user-1',
    onSuccess: (data) => {
      console.log('Updated record:', data);
    },
  },
  parameters: {
    msw: {
      handlers: [
        rest.get('/api/resources/user-1', (req, res, ctx) => {
          return res(
            ctx.delay(300),
            ctx.status(200),
            ctx.json({ data: mockUsers['user-1'] })
          );
        }),
        rest.put('/api/resources/user-1', (req, res, ctx) => {
          return res(
            ctx.delay(800),
            ctx.status(200),
            ctx.json({
              data: {
                ...mockUsers['user-1'],
                ...(req.body || {}),
                updatedAt: new Date().toISOString(),
              }
            })
          );
        }),
      ],
    },
  },
};

export const DeleteFlow: Story = {
  args: {
    mode: 'delete',
    schema: formSchema,
    id: 'user-1',
    onSuccess: () => {
      console.log('Resource deleted successfully');
    },
  },
  parameters: {
    msw: {
      handlers: [
        rest.get('/api/resources/user-1', (req, res, ctx) => {
          return res(
            ctx.delay(300),
            ctx.status(200),
            ctx.json({ data: mockUsers['user-1'] })
          );
        }),
        rest.delete('/api/resources/user-1', (req, res, ctx) => {
          return res(
            ctx.delay(500),
            ctx.status(200),
            ctx.json({
              success: true,
              message: 'Resource deleted successfully',
            })
          );
        }),
      ],
    },
  },
};
```

### Form Validation Stories

```typescript
// src/components/FormComponent/__stories__/FormComponent.validation.stories.tsx

import type { Meta, StoryObj } from '@storybook/react';
import { expect, userEvent, within } from '@storybook/test';
import { FormComponent } from '../FormComponent';
import { formSchema } from '../FormComponent-Schema';
import { rest } from 'msw';
import { mockValidationErrors } from '../__fixtures__/FormComponent.mockData';

const meta = {
  title: 'Components/FormComponent/Validation',
  component: FormComponent,
  parameters: {
    docs: {
      description: {
        story: 'Form validation scenarios including client-side and server-side validation.',
      },
    },
  },
} satisfies Meta<typeof FormComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const ClientValidation: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Submit form without filling required fields
    const submitButton = canvas.getByRole('button', { name: /submit/i });
    await userEvent.click(submitButton);
    
    // Verify validation errors are displayed
    const errorMessages = await canvas.findAllByRole('alert');
    expect(errorMessages.length).toBeGreaterThan(0);
    
    // Fill in a field incorrectly
    const emailInput = canvas.getByLabelText(/email/i);
    await userEvent.type(emailInput, 'invalid-email');
    await userEvent.tab(); // Blur to trigger validation
    
    // Verify email-specific error
    const emailError = await canvas.findByText(/valid email/i);
    expect(emailError).toBeInTheDocument();
  },
};

export const ServerValidation: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
  },
  parameters: {
    msw: {
      handlers: [
        rest.post('/api/resources', (req, res, ctx) => {
          return res(
            ctx.delay(800),
            ctx.status(400),
            ctx.json({
              error: 'Validation failed',
              fields: mockValidationErrors
            })
          );
        }),
      ],
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Fill required fields correctly
    const nameInput = canvas.getByLabelText(/name/i);
    const emailInput = canvas.getByLabelText(/email/i);
    
    await userEvent.type(nameInput, 'John Doe');
    await userEvent.type(emailInput, '<EMAIL>');
    
    // Submit form
    const submitButton = canvas.getByRole('button', { name: /submit/i });
    await userEvent.click(submitButton);
    
    // Verify server validation errors appear
    const serverErrors = await canvas.findAllByRole('alert');
    expect(serverErrors.length).toBeGreaterThan(0);
  },
};

export const AsyncValidation: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    asyncValidation: true, // Enable async field validation
  },
  parameters: {
    msw: {
      handlers: [
        rest.get('/api/validate/email', (req, res, ctx) => {
          const email = req.url.searchParams.get('value');
          const isAvailable = email !== '<EMAIL>';
          
          return res(
            ctx.delay(1000),
            ctx.json({
              available: isAvailable,
              message: isAvailable ? 'Email is available' : 'Email is already in use'
            })
          );
        }),
      ],
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Type an email that should trigger async validation
    const emailInput = canvas.getByLabelText(/email/i);
    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.tab(); // Blur to trigger validation
    
    // Verify async validation message appears
    const asyncError = await canvas.findByText(/already in use/i);
    expect(asyncError).toBeInTheDocument();
  },
};
```

### Form States Stories

```typescript
// src/components/FormComponent/__stories__/FormComponent.states.stories.tsx

import type { Meta, StoryObj } from '@storybook/react';
import { FormComponent } from '../FormComponent';
import { formSchema } from '../FormComponent-Schema';
import { rest } from 'msw';

const meta = {
  title: 'Components/FormComponent/States',
  component: FormComponent,
  parameters: {
    docs: {
      description: {
        story: 'Various form states including loading, error, and success.',
      },
    },
  },
} satisfies Meta<typeof FormComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Loading: Story = {
  args: {
    mode: 'update',
    schema: formSchema,
    id: 'slow-loading-user',
  },
  parameters: {
    msw: {
      handlers: [
        rest.get('/api/resources/slow-loading-user', (req, res, ctx) => {
          return res(
            ctx.delay(5000), // Very slow response
            ctx.status(200),
            ctx.json({ 
              data: {
                id: 'slow-loading-user',
                name: 'Slow Loading User',
                email: '<EMAIL>'
              }
            })
          );
        }),
      ],
    },
  },
};

export const SubmitLoading: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    defaultValues: {
      name: 'Pre-filled User',
      email: '<EMAIL>'
    },
  },
  parameters: {
    msw: {
      handlers: [
        rest.post('/api/resources', (req, res, ctx) => {
          return res(
            ctx.delay(5000), // Very slow submission
            ctx.status(201),
            ctx.json({
              data: {
                id: 'new-user-123',
                ...(req.body || {}),
                createdAt: new Date().toISOString()
              }
            })
          );
        }),
      ],
    },
  },
};

export const ServerError: Story = {
  args: {
    mode: 'update',
    schema: formSchema,
    id: 'error-user',
  },
  parameters: {
    msw: {
      handlers: [
        rest.get('/api/resources/error-user', (req, res, ctx) => {
          return res(
            ctx.delay(300),
            ctx.status(500),
            ctx.json({ 
              error: 'Internal server error',
              message: 'Something went wrong on the server'
            })
          );
        }),
      ],
    },
  },
};

export const NetworkError: Story = {
  args: {
    mode: 'update',
    schema: formSchema,
    id: 'network-error-user',
  },
  parameters: {
    msw: {
      handlers: [
        rest.get('/api/resources/network-error-user', (req, res) => {
          // Simulate network error by not returning a response
          return res.networkError('Failed to connect');
        }),
      ],
    },
  },
};

export const SuccessState: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    defaultValues: {
      name: 'Success User',
      email: '<EMAIL>'
    },
    showSuccessState: true, // Show success state automatically
  },
  parameters: {
    msw: {
      handlers: [
        rest.post('/api/resources', (req, res, ctx) => {
          return res(
            ctx.delay(500),
            ctx.status(201),
            ctx.json({
              data: {
                id: 'new-user-123',
                ...(req.body || {}),
                createdAt: new Date().toISOString()
              }
            })
          );
        }),
      ],
    },
  },
};
```

### Accessibility Stories

```typescript
// src/components/FormComponent/__stories__/FormComponent.a11y.stories.tsx

import type { Meta, StoryObj } from '@storybook/react';
import { expect, userEvent, within } from '@storybook/test';
import { FormComponent } from '../FormComponent';
import { formSchema } from '../FormComponent-Schema';

const meta = {
  title: 'Components/FormComponent/Accessibility',
  component: FormComponent,
  parameters: {
    a11y: {
      config: {
        rules: [
          { id: 'color-contrast', enabled: true },
          { id: 'label', enabled: true },
          { id: 'form-field-multiple-labels', enabled: true },
          { id: 'aria-valid-attr', enabled: true },
        ],
      },
    },
    docs: {
      description: {
        story: 'Accessibility testing for the form component ensuring WCAG 2.1 compliance.',
      },
    },
  },
} satisfies Meta<typeof FormComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const KeyboardNavigation: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Navigate through form with keyboard
    await userEvent.tab(); // Focus first input
    expect(canvas.getByLabelText(/name/i)).toHaveFocus();
    
    await userEvent.tab(); // Move to next input
    expect(canvas.getByLabelText(/email/i)).toHaveFocus();
    
    await userEvent.tab(); // Move to submit button
    const submitButton = canvas.getByRole('button', { name: /submit/i });
    expect(submitButton).toHaveFocus();
    
    // Test keyboard submission
    await userEvent.keyboard('{Enter}');
    // Expect validation errors to be accessible
    const errors = await canvas.findAllByRole('alert');
    expect(errors.length).toBeGreaterThan(0);
  },
};

export const ScreenReaderAnnouncements: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    defaultValues: {
      name: 'Test User',
      email: 'invalid-email' // Intentionally invalid
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Submit form to trigger validation errors
    const submitButton = canvas.getByRole('button', { name: /submit/i });
    await userEvent.click(submitButton);
    
    // Verify error messages are properly marked up for screen readers
    const errors = await canvas.findAllByRole('alert');
    expect(errors.length).toBeGreaterThan(0);
    
    // Check that errors are associated with inputs via aria-describedby
    const emailInput = canvas.getByLabelText(/email/i);
    const errorId = emailInput.getAttribute('aria-describedby');
    expect(errorId).toBeTruthy();
    expect(document.getElementById(errorId as string)).toHaveAttribute('role', 'alert');
  },
};

export const HighContrastMode: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
  },
  parameters: {
    backgrounds: {
      default: 'high-contrast',
      values: [
        { name: 'high-contrast', value: '#ffffff' }
      ],
    },
    themes: {
      default: 'high-contrast',
    },
  },
};

export const FormWithErrorStates: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    defaultValues: {
      name: 'Error Test',
      email: 'invalid-email'
    },
    forceShowErrors: true, // Force display of error states
  },
};
```

### Internationalization Stories

```typescript
// src/components/FormComponent/__stories__/FormComponent.i18n.stories.tsx

import type { Meta, StoryObj } from '@storybook/react';
import { FormComponent } from '../FormComponent';
import { formSchema } from '../FormComponent-Schema';

const meta = {
  title: 'Components/FormComponent/Internationalization',
  component: FormComponent,
  parameters: {
    docs: {
      description: {
        story: 'Internationalization support for the form component with multiple languages.',
      },
    },
  },
} satisfies Meta<typeof FormComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const English: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    locale: 'en',
  },
  parameters: {
    locale: 'en',
  },
};

export const French: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    locale: 'fr',
  },
  parameters: {
    locale: 'fr',
  },
};

export const Japanese: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    locale: 'ja',
  },
  parameters: {
    locale: 'ja',
  },
};

export const ArabicRTL: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    locale: 'ar',
    dir: 'rtl',
  },
  parameters: {
    locale: 'ar',
    direction: {
      dir: 'rtl',
    },
  },
};

export const ValidationMessagesLocalized: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    locale: 'fr',
    defaultValues: {
      name: 'T', // Too short, will trigger validation error
      email: 'invalid' // Invalid email
    },
    forceShowErrors: true,
  },
  parameters: {
    locale: 'fr',
  },
};
```

### Performance Stories

```typescript
// src/components/FormComponent/__stories__/FormComponent.performance.stories.tsx

import type { Meta, StoryObj } from '@storybook/react';
import { FormComponent } from '../FormComponent';
import { formSchema } from '../FormComponent-Schema';
import { rest } from 'msw';
import { withPerformance } from 'storybook-addon-performance';

const meta = {
  title: 'Components/FormComponent/Performance',
  component: FormComponent,
  decorators: [withPerformance],
  parameters: {
    docs: {
      description: {
        story: 'Performance testing and optimization for the form component.',
      },
    },
    performance: {
      allowedJankiness: 2,
      allowedTimeToInteractive: 200,
    },
  },
} satisfies Meta<typeof FormComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const BasicPerformance: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
  },
};

export const LargeFormPerformance: Story = {
  args: {
    mode: 'create',
    schema: formSchema,
    fields: Array.from({ length: 50 }).map((_, i) => ({
      name: `field${i}`,
      label: `Field ${i}`,
      type: 'text',
    })),
  },
};

export const LargeDataLoad: Story = {
  args: {
    mode: 'update',
    schema: formSchema,
    id: 'large-data',
  },
  parameters: {
    msw: {
      handlers: [
        rest.get('/api/resources/large-data', (req, res, ctx) => {
          // Generate large object with many properties
          const largeData = {
            id: 'large-data',
            name: 'Large Data Test',
            email: '<EMAIL>',
            // Add many more properties
            properties: Array.from({ length: 100 }).reduce((acc, _, i) => {
              acc[`property${i}`] = `Value ${i}`;
              return acc;
            }, {}),
            // Nested arrays
            items: Array.from({ length: 100 }).map((_, i) => ({
              id: `item-${i}`,
              value: `Item ${i}`,
              metadata: {
                created: new Date().toISOString(),
                tags: Array.from({ length: 5 }).map((_, j) => `tag-${j}`)
              }
            })),
          };
          
          return res(
            ctx.delay(300),
            ctx.status(200),
            ctx.json({ data: largeData })
          );
        }),
      ],
    },
  },
};

export const ManyFormInstances: Story = {
  render: () => (
    <div className="space-y-6">
      {Array.from({ length: 10 }).map((_, i) => (
        <FormComponent
          key={i}
          mode="create"
          schema={formSchema}
          defaultValues={{
            name: `User ${i}`,
            email: `user${i}@example.com`
          }}
        />
      ))}
    </div>
  ),
};
```

## 🔧 Storybook Preview Configuration

```javascript
// .storybook/preview.ts

import { initialize, mswLoader } from 'msw-storybook-addon';
import { FormComponentHandlers } from '../../.storybook/mswHandlers/FormComponent.handlers';

// Initialize MSW if enabled via environment variable
if (process.env.STORYBOOK_USE_MOCK_API === 'true') {
  initialize({
    onUnhandledRequest: 'bypass',
    serviceWorker: {
      url: './mockServiceWorker.ts'
    }
  });
}

// Configure Storybook loaders
export const loaders = process.env.STORYBOOK_USE_MOCK_API === 'true' ? [mswLoader] : [];

// Configure Storybook parameters
export const parameters = {
  actions: { argTypesRegex: "^on[A-Z].*" },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
    expanded: true,
    sort: 'requiredFirst',
  },
  // MSW handlers configuration
  msw: {
    handlers: process.env.STORYBOOK_USE_MOCK_API === 'true' 
      ? [...FormComponentHandlers] 
      : [],
  },
  // A11y configuration
  a11y: {
    config: {
      rules: [
        {
          // Ensure all form controls have accessible names
          id: 'label',
          enabled: true
        }
      ]
    },
    manual: [
      {
        name: 'Keyboard Navigation',
        description: 'Ensure all interactive elements can be reached and operated via keyboard'
      },
      {
        name: 'Screen Reader Compatibility',
        description: 'Verify form validation errors are properly announced by screen readers'
      },
      {
        name: 'Focus Management',
        description: 'Ensure focus is properly managed during form interactions'
      }
    ]
  },
  // Backgrounds configuration
  backgrounds: {
    default: 'light',
    values: [
      { name: 'light', value: '#FFFFFF' },
      { name: 'dark', value: '#333333' },
      { name: 'gray', value: '#F8F8F8' },
    ],
  },
  // Viewport configuration
  viewport: {
    viewports: {
      mobile: { name: 'Mobile', styles: { width: '375px', height: '667px' } },
      tablet: { name: 'Tablet', styles: { width: '768px', height: '1024px' } },
      desktop: { name: 'Desktop', styles: { width: '1440px', height: '900px' } },
      largeDesktop: { name: 'Large Desktop', styles: { width: '1920px', height: '1080px' } },
    },
    defaultViewport: 'desktop',
  },
  // Locale/i18n configuration
  locale: 'en',
  locales: {
    en: 'English',
    fr: 'French',
    ja: 'Japanese',
    ar: 'Arabic',
  },
};
```

## 📋 Enterprise-Grade MSW Integration Checklist

- [ ] All API endpoints mocked with appropriate handlers
- [ ] Response delays simulate realistic network conditions
- [ ] Error states and edge cases fully covered
- [ ] Large dataset handling demonstrated
- [ ] Toggle between mock/real API via environment variables
- [ ] Comprehensive documentation for each mock scenario
- [ ] Realistic, production-like data structures
- [ ] Comprehensive validation error simulation
- [ ] Performance testing with large payloads

## 🎯 Production-Ready Form Component Stories Checklist

- [ ] CRUD operations fully demonstrated
- [ ] Form validation (client and server) covered
- [ ] Loading states visualized
- [ ] Error handling demonstrated
- [ ] Accessibility compliance verified
- [ ] Internationalization support shown
- [ ] Performance optimizations documented
- [ ] Responsive behavior tested
- [ ] Integration with back-end APIs simulated
- [ ] Edge cases and boundary conditions explored

By implementing this architecture, you'll create hyper-rich, enterprise-grade Storybook documentation for form components that perfectly integrates with MSW for realistic API simulation, ensuring your components are thoroughly tested and production-ready.