@import 'tailwindcss';
@config "../../tailwind.config.ts";
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 12%;
    --background-secondary: 220 20% 97%;
    --background-secondary-foreground: 0 0% 12%;
    --card: 220 20% 97%;
    --card-foreground: 0 0% 12%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 12%;
    --primary: 233 73% 26%;
    --primary-foreground: 0 0% 100%;
    --secondary: 40 100% 50%;
    --secondary-foreground: 0 0% 12%;
    --muted: 220 20% 97%;
    --muted-foreground: 225 12% 60%;
    --accent: 205, 42%, 91%;
    --accent-foreground: 233 73% 26%;
    --destructive: 359, 100%, 96%;
    --destructive-foreground: 359, 100%, 62%;
    --border: 225 12% 60%;
    --input: 0 0% 94%;
    --ring: 233 80% 35%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 12%;
    --sidebar-primary: 233 73% 26%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 205, 42%, 91%;
    --sidebar-accent-foreground: 233 73% 26%;
    --sidebar-border: 0 0% 94%;
    --sidebar-ring: 0 0% 94%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --success: 159 100% 37%;
    --success-foreground: 160 56% 94%;
    --error: 359, 100%, 62%;
    --error-foreground: 0 100% 96%;

    --background-BG: 220 20% 97%;
    --color-stroke: 0 0% 94%;
    --placeholder: 225 12% 60%;
    --title-main: 0 0% 12%;
    --White: 0 0% 100%;
    --discription: 221 18% 44%;
    --discription-secondary: 235 19% 35%;
    --Highlight: 220 100% 98%;
    --test: 235 19% 35%;
    --Menu: 205 42% 91%;
    --title-secondary: 225 10% 52%;
    --secondary-bg: 39 100% 95%;
  }

  .dark {
    --background: 232 23% 10%;
    --foreground: 0 0% 98%;
    --background-secondary: 232 23% 15%;
    --background-secondary-foreground: 0 0% 98%;
    --card: 232 23% 15%;
    --card-foreground: 0 0% 98%;
    --popover: 232 23% 13%;
    --popover-foreground: 0 0% 98%;
    --primary: 233 65% 55%;
    --primary-foreground: 0 0% 100%;
    --secondary: 40 90% 45%;
    --secondary-foreground: 0 0% 12%;
    --muted: 232 23% 20%;
    --muted-foreground: 220 20% 70%;
    --accent: 205 42% 40%;
    --accent-foreground: 205 85% 90%;
    --destructive: 359 85% 30%;
    --destructive-foreground: 359 100% 94%;
    --border: 232 23% 20%;
    --input: 232 23% 20%;
    --ring: 233 65% 60%;
    --sidebar-background: 232 23% 13%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 233 65% 55%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 205 42% 40%;
    --sidebar-accent-foreground: 205 85% 90%;
    --sidebar-border: 232 23% 20%;
    --sidebar-ring: 233 65% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 70% 45%;
    --chart-3: 197 60% 40%;
    --chart-4: 43 74% 60%;
    --chart-5: 27 87% 67%;
    --success: 159 80% 30%;
    --success-foreground: 160 85% 94%;
    --error: 359 85% 45%;
    --error-foreground: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-[hsl(var(--border))];
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom z-index hierarchy for proper tooltip layering */
@layer components {
  /* TimeConversionTooltip hover card should have higher z-index */
  [data-radix-hover-card-content] {
    z-index: 100 !important;
  }

  /* Regular tooltips should have lower z-index than hover cards */
  [data-radix-tooltip-content] {
    z-index: 60 !important;
  }
}

/* bullet ปกติ */
.custom-bullets {
  @apply h-2 w-2 rounded-full bg-gray-300 opacity-100 transition-all duration-300;
}
