import { HTMLAttributes, ReactNode } from 'react';
import {
  ColumnDef,
  SortingState,
  VisibilityState,
  FilterFn,
  Row,
  ColumnFiltersState,
  Column,
} from '@tanstack/react-table';

/**
 * Generic record type with unknown values to replace any
 */
export type DataRecord = Record<string, unknown>;

/**
 * Base props extending HTML attributes
 */
export interface BaseProps extends HTMLAttributes<HTMLDivElement> {
  /** Optional CSS class to be merged */
  className?: string;
  /** Children components/elements */
  children?: ReactNode;
}

/**
 * Component variant props
 */
export interface TableVariantProps {
  /** Table size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Table style variant */
  variant?: 'primary' | 'secondary' | 'outline';
}

/**
 * State-related props
 */
export interface StateProps {
  /** Whether the component is in a loading state */
  loading?: boolean;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Error message or component if any */
  error?: string | ReactNode;
}

/**
 * Animation-related props
 */
export interface AnimationProps {
  /** Whether to animate the component */
  animate?: boolean;
  /** Animation delay in seconds */
  animationDelay?: number;
}

/**
 * Virtualization props
 */
export interface VirtualizationProps {
  /** Enable virtualization for large datasets */
  virtualized?: boolean;
  /** Height for virtualized table container */
  height?: number;
  /** Width for virtualized table container */
  width?: number | string;
  /** Number of items to overscan (render off-screen) */
  overscan?: number;
}

/**
 * Skeleton loader props
 */
export interface SkeletonProps {
  /** Props to pass to the skeleton component */
  skeletonProps?: {
    /** Whether to use simplified skeleton version */
    simplified?: boolean;
    /** Number of rows to show in skeleton */
    rowCount?: number;
    /** Number of columns to show in skeleton */
    columnCount?: number;
  };
}

/**
 * Internationalization props
 */
export interface I18nProps {
  /** Custom translation namespace */
  i18nNamespace?: string;
  /** Translation prefix for component keys */
  i18nPrefix?: string;
}

/**
 * Table column configuration
 */
export interface ColumnConfig<T extends DataRecord = DataRecord> {
  /** Unique column id */
  id: string;
  /** Column header text or ReactNode */
  header: string | ReactNode;
  /** Accessor key for data in the row */
  accessorKey: string;
  /** Whether the column can be sorted */
  enableSorting?: boolean;
  /** Whether the column can be filtered */
  enableFiltering?: boolean;
  /** Whether the column is sticky */
  sticky?: boolean;
  /** Whether to hide the column by default */
  hidden?: boolean;
  /** Whether the column is pinned to the left or right */
  pinned?: 'left' | 'right';
  /** Width of the column */
  width?: number | string;
  /** Minimum width of the column */
  minWidth?: number;
  /** Maximum width of the column */
  maxWidth?: number;
  /** Whether the column can be resized */
  enableResizing?: boolean;
  /** Custom cell renderer */
  cell?: (info: { row: Row<T>; column: Column<T>; value: unknown }) => ReactNode;
  /** Custom aggregation function */
  aggregationFn?: (columnValues: unknown[]) => unknown;
  /** Custom aggregation formatter */
  aggregationFormatter?: (value: unknown) => ReactNode;
  /** Custom filter function */
  filterFn?: FilterFn<T>;
  /** Initial filter value */
  initialFilterValue?: unknown;
  /** Custom filter component */
  Filter?: ReactNode;
  /** Header tooltip content */
  headerTooltip?: ReactNode;
  /** Additional cell classes */
  cellClassName?: string;
  /** Additional header classes */
  headerClassName?: string;
  /** Whether cell content should be truncated with ellipsis */
  truncate?: boolean;
  /** Meta information for the column */
  meta?: ColumnMeta;
}

/**
 * Column metadata interface to replace any in column definitions
 */
export interface ColumnMeta {
  /** Custom cell class name */
  className?: string;
  /** Text alignment */
  align?: 'left' | 'center' | 'right';
  /** Whether to truncate text */
  truncate?: boolean;
  /** Aggregation function */
  aggregationFn?: (values: unknown[]) => unknown;
  /** Aggregation formatter */
  aggregationFormatter?: (value: unknown) => ReactNode;
  /** Cell class name */
  cellClassName?: string;
  /** Other metadata properties */
  [key: string]: unknown;
}

/**
 * Table pagination configuration
 */
export interface PaginationConfig {
  /** Whether to enable pagination */
  enabled?: boolean;
  /** Page size options */
  pageSizeOptions?: number[];
  /** Initial page size */
  initialPageSize?: number;
  /** Initial page index */
  initialPageIndex?: number;
  /** Whether to show the page size selector */
  showPageSizeSelector?: boolean;
  /** Whether to show the page navigator */
  showPageNavigator?: boolean;
  /** Whether to show the page info */
  showPageInfo?: boolean;
  /** Total number of items (required for server-side pagination) */
  totalItems?: number;
  /** Custom page size change handler */
  onPageSizeChange?: (pageSize: number) => void;
  /** Custom page change handler */
  onPageChange?: (pageIndex: number) => void;
}

/**
 * Table sorting configuration
 */
export interface SortingConfig {
  /** Whether to enable sorting */
  enabled?: boolean;
  /** Initial sorting state */
  initialSorting?: SortingState;
  /** Maximum number of columns that can be sorted at once */
  maxSortColumns?: number;
  /** Default sort direction for columns */
  defaultSortDirection?: 'asc' | 'desc';
  /** Custom sort change handler */
  onSortingChange?: (sorting: SortingState) => void;
}

/**
 * Table filtering configuration
 */
export interface FilteringConfig {
  /** Whether to enable filtering */
  enabled?: boolean;
  /** Global filter mode */
  globalFilterMode?: 'startsWith' | 'contains' | 'endsWith' | 'equals';
  /** Initial column filters state */
  initialColumnFilters?: ColumnFiltersState;
  /** Initial global filter value */
  initialGlobalFilter?: string;
  /** Whether to show the global filter input */
  showGlobalFilter?: boolean;
  /** Custom global filter change handler */
  onGlobalFilterChange?: (globalFilter: string) => void;
  /** Custom column filters change handler */
  onColumnFiltersChange?: (columnFilters: ColumnFiltersState) => void;
  /** Whether to debounce filter input */
  debounce?: boolean;
  /** Debounce time in ms */
  debounceTime?: number;
}

/**
 * Table column visibility configuration
 */
export interface ColumnVisibilityConfig {
  /** Whether to enable column visibility */
  enabled?: boolean;
  /** Initial column visibility state */
  initialVisibility?: VisibilityState;
  /** Whether to show the column visibility toggle */
  showToggle?: boolean;
  /** Custom visibility change handler */
  onVisibilityChange?: (visibility: VisibilityState) => void;
}

/**
 * Table header configuration
 */
export interface HeaderConfig {
  /** Whether to show the table header */
  show?: boolean;
  /** Whether the header is sticky */
  sticky?: boolean;
  /** Custom header row height */
  height?: number | string;
  /** Whether to show column separators */
  showColumnSeparators?: boolean;
  /** Custom header cell renderer */
  renderHeaderCell?: (column: ColumnDef<DataRecord>) => ReactNode;
}

/**
 * Table footer configuration
 */
export interface FooterConfig {
  /** Whether to show the table footer */
  show?: boolean;
  /** Whether the footer is sticky */
  sticky?: boolean;
  /** Custom footer row height */
  height?: number | string;
  /** Whether to show column separators */
  showColumnSeparators?: boolean;
  /** Custom footer content */
  footerContent?: ReactNode;
  /** Whether to show summary row in footer */
  showSummary?: boolean;
  /** Custom summary row renderer */
  renderSummaryRow?: (data: DataRecord[]) => ReactNode;
}

/**
 * Row selection configuration
 */
export interface RowSelectionConfig {
  /** Whether to enable row selection */
  enabled?: boolean;
  /** Selection mode */
  mode?: 'single' | 'multi';
  /** Preserve selection on page change */
  preserveSelection?: boolean;
  /** Initial selected row ids */
  initialRowSelection?: Record<string, boolean>;
  /** Custom row selection change handler */
  onRowSelectionChange?: (selection: Record<string, boolean>) => void;
}

/**
 * Row actions configuration
 */
export interface RowActionsConfig {
  /** Whether to enable row actions */
  enabled?: boolean;
  /** Width of the actions column */
  width?: number | string;
  /** Position of the actions column */
  position?: 'start' | 'end';
  /** Custom row actions renderer */
  renderRowActions?: (row: Row<DataRecord>) => ReactNode;
}

/**
 * Row expansion configuration
 */
export interface RowExpansionConfig {
  /** Whether to enable row expansion */
  enabled?: boolean;
  /** Custom row expansion renderer */
  renderRowExpansion?: (row: Row<DataRecord>) => ReactNode;
  /** Whether multiple rows can be expanded at once */
  allowMultipleExpanded?: boolean;
  /** Initial expanded row ids */
  initialExpandedRowIds?: Record<string, boolean>;
  /** Custom row expansion change handler */
  onRowExpansionChange?: (expanded: Record<string, boolean>) => void;
}

/**
 * Table highlighting configuration
 */
export interface HighlightConfig {
  /** Whether to highlight rows on hover */
  hoverHighlight?: boolean;
  /** Whether to highlight alternate rows */
  alternateRowHighlight?: boolean;
  /** Custom row highlighting function */
  rowHighlightFn?: (row: Row<DataRecord>) => string | null;
  /** Custom cell highlighting function */
  cellHighlightFn?: (row: Row<DataRecord>, columnId: string) => string | null;
}

/**
 * Table export configuration
 */
export interface ExportConfig {
  /** Whether to enable CSV export */
  enableCSV?: boolean;
  /** Whether to enable Excel export */
  enableExcel?: boolean;
  /** Whether to enable PDF export */
  enablePDF?: boolean;
  /** Custom export filename */
  filename?: string;
  /** Custom export handler */
  onExport?: (type: 'csv' | 'excel' | 'pdf') => void;
}

/**
 * Combined props interface for the main component
 */
export interface TableDynamicProps<T extends DataRecord = DataRecord>
  extends BaseProps,
    TableVariantProps,
    StateProps,
    AnimationProps,
    VirtualizationProps,
    SkeletonProps,
    I18nProps {
  /** Table data */
  data: T[];
  /** Table column configurations */
  columns: ColumnConfig<T>[];
  /** Pagination configuration */
  pagination?: PaginationConfig;
  /** Sorting configuration */
  sorting?: SortingConfig;
  /** Filtering configuration */
  filtering?: FilteringConfig;
  /** Column visibility configuration */
  columnVisibility?: ColumnVisibilityConfig;
  /** Header configuration */
  header?: HeaderConfig;
  /** Footer configuration */
  footer?: FooterConfig;
  /** Row selection configuration */
  rowSelection?: RowSelectionConfig;
  /** Row actions configuration */
  rowActions?: RowActionsConfig;
  /** Row expansion configuration */
  rowExpansion?: RowExpansionConfig;
  /** Highlighting configuration */
  highlighting?: HighlightConfig;
  /** Export configuration */
  export?: ExportConfig;
  /** Event handler for row click */
  onRowClick?: (row: Row<T>) => void;
  /** Event handler for cell click */
  onCellClick?: (row: Row<T>, columnId: string) => void;
  /** Custom empty state */
  emptyState?: ReactNode;
  /** Whether to show border */
  showBorder?: boolean;
  /** Whether to stripe rows */
  striped?: boolean;
  /** Whether to show table header */
  showHeader?: boolean;
  /** Whether to show table footer */
  showFooter?: boolean;
  /** Whether the table is loading data */
  isLoadingData?: boolean;
  /** Whether the table is compact */
  compact?: boolean;
  /** Custom ref for the table container */
  tableRef?: React.RefObject<HTMLDivElement>;
}
