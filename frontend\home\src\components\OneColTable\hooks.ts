import { useState, useCallback, useEffect, useMemo } from 'react';
import { TableItem } from './types';
import { filterItems, sortItems, paginateItems } from './utils';
import { DEFAULT_LIMITS } from './constants';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  createColumnHelper,
} from '@tanstack/react-table';

/**
 * Hook for pagination logic in OneColTable
 * @param allItems All available items
 * @param initialPage Initial page number
 * @param pageSize Number of items per page
 * @returns Pagination state and handlers
 */
export const useTablePagination = (
  allItems: TableItem[],
  initialPage: number = 1,
  pageSize: number = DEFAULT_LIMITS.PAGE_SIZE,
) => {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [visibleCount, setVisibleCount] = useState(pageSize);

  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.ceil(allItems.length / pageSize);
  }, [allItems.length, pageSize]);

  // Get current page items
  const currentItems = useMemo(() => {
    return paginateItems(allItems, currentPage, pageSize);
  }, [allItems, currentPage, pageSize]);

  // Get all visible items (for load more pagination)
  const visibleItems = useMemo(() => {
    return allItems.slice(0, visibleCount);
  }, [allItems, visibleCount]);

  // Determine if there are more items to load
  const hasMoreItems = useMemo(() => {
    return visibleCount < allItems.length;
  }, [visibleCount, allItems.length]);

  // Go to next page
  const nextPage = useCallback(() => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  }, [totalPages]);

  // Go to previous page
  const previousPage = useCallback(() => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  }, []);

  // Go to specific page
  const goToPage = useCallback(
    (page: number) => {
      setCurrentPage(Math.max(1, Math.min(page, totalPages)));
    },
    [totalPages],
  );

  // Load more items
  const loadMore = useCallback(
    (increment: number = pageSize) => {
      setVisibleCount((prev) => Math.min(prev + increment, allItems.length));
    },
    [pageSize, allItems.length],
  );

  // Reset pagination when allItems changes
  useEffect(() => {
    setCurrentPage(1);
    setVisibleCount(pageSize);
  }, [allItems, pageSize]);

  return {
    currentPage,
    totalPages,
    currentItems,
    visibleItems,
    hasMoreItems,
    visibleCount,
    nextPage,
    previousPage,
    goToPage,
    loadMore,
  };
};

/**
 * Hook for sorting and filtering logic in OneColTable
 * @param items Items to sort and filter
 * @param initialSortBy Initial property to sort by
 * @param initialSortOrder Initial sort order
 * @returns Sort and filter state and handlers
 */
export const useTableSortAndFilter = <T extends keyof TableItem>(
  items: TableItem[],
  initialSortBy?: T,
  initialSortOrder: 'asc' | 'desc' = 'desc',
) => {
  const [sortBy, setSortBy] = useState<T | undefined>(initialSortBy);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(initialSortOrder);
  const [filterTerm, setFilterTerm] = useState('');

  // Apply sorting and filtering
  const processedItems = useMemo(() => {
    let result = items;

    // Apply filtering
    if (filterTerm) {
      result = filterItems(result, filterTerm);
    }

    // Apply sorting
    if (sortBy) {
      result = sortItems(result, sortBy, sortOrder);
    }

    return result;
  }, [items, sortBy, sortOrder, filterTerm]);

  // Toggle sort order
  const toggleSortOrder = useCallback(() => {
    setSortOrder((prev) => (prev === 'asc' ? 'desc' : 'asc'));
  }, []);

  // Update sort field
  const updateSortBy = useCallback(
    (field: T) => {
      setSortBy((prev) => {
        // If clicking the same field, toggle the sort order
        if (prev === field) {
          toggleSortOrder();
          return prev;
        }
        // Otherwise update the field and set to default sort order
        return field;
      });
    },
    [toggleSortOrder],
  );

  // Update filter term
  const updateFilterTerm = useCallback((term: string) => {
    setFilterTerm(term);
  }, []);

  // Clear filters
  const clearFilters = useCallback(() => {
    setFilterTerm('');
  }, []);

  return {
    sortBy,
    sortOrder,
    filterTerm,
    processedItems,
    updateSortBy,
    toggleSortOrder,
    updateFilterTerm,
    clearFilters,
  };
};

/**
 * Hook for managing hovering and selection state in OneColTable
 * @returns Hover and selection state and handlers
 */
export const useTableRowInteraction = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [hoveredItem, setHoveredItem] = useState<TableItem | null>(null);
  const [selectedItems, setSelectedItems] = useState<TableItem[]>([]);

  // Handle row hover
  const handleRowHover = useCallback((item: TableItem, index: number) => {
    setHoveredIndex(index);
    setHoveredItem(item);
  }, []);

  // Handle row hover exit
  const handleRowLeave = useCallback(() => {
    setHoveredIndex(null);
    setHoveredItem(null);
  }, []);

  // Toggle item selection
  const toggleItemSelection = useCallback((item: TableItem) => {
    setSelectedItems((prev) => {
      const isSelected = prev.some((i) => i.id === item.id);
      if (isSelected) {
        return prev.filter((i) => i.id !== item.id);
      } else {
        return [...prev, item];
      }
    });
  }, []);

  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedItems([]);
  }, []);

  // Check if an item is selected
  const isItemSelected = useCallback(
    (item: TableItem) => {
      return selectedItems.some((i) => i.id === item.id);
    },
    [selectedItems],
  );

  return {
    hoveredIndex,
    hoveredItem,
    selectedItems,
    handleRowHover,
    handleRowLeave,
    toggleItemSelection,
    clearSelection,
    isItemSelected,
  };
};

/**
 * Hook for enhanced OneColTable functionality
 *
 * @param items The table data items
 * @param initialSorting Initial sorting configuration
 * @param initialSearchTerm Initial search term
 * @returns Table related props and functions
 */
export const useOneColTable = (
  items: TableItem[],
  initialSorting: SortingState = [],
  initialSearchTerm: string = '',
) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [sorting, setSorting] = useState<SortingState>(initialSorting);

  // Create column helper
  const columnHelper = createColumnHelper<TableItem>();

  // Define default columns
  const defaultColumns = useMemo(
    () => [
      columnHelper.accessor('content', {
        id: 'content',
        header: 'Content',
      }),
      columnHelper.accessor('timestamp', {
        id: 'timestamp',
        header: 'Date',
        sortingFn: 'datetime',
      }),
    ],
    [columnHelper],
  );

  // Filter items based on search term
  const filteredItems = useMemo(
    () => (searchTerm ? filterItems(items, searchTerm) : items),
    [items, searchTerm],
  );

  // Create the table instance
  const table = useReactTable({
    data: filteredItems,
    columns: defaultColumns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  return {
    table,
    items: filteredItems,
    searchTerm,
    setSearchTerm,
    sorting,
    setSorting,
    columnHelper,
  };
};

/**
 * Hook for handling pagination in OneColTable
 *
 * @param totalItems Total number of items
 * @param initialPage Initial page (1-based)
 * @param initialPageSize Initial page size
 * @returns Pagination related props and functions
 */
export const useOneColTablePagination = (
  totalItems: number,
  initialPage: number = 1,
  initialPageSize: number = 10,
) => {
  const [page, setPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);

  const totalPages = Math.ceil(totalItems / pageSize);
  const hasPreviousPage = page > 1;
  const hasNextPage = page < totalPages;

  const goToPage = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  const goToNextPage = () => {
    if (hasNextPage) {
      setPage((prev) => prev + 1);
    }
  };

  const goToPreviousPage = () => {
    if (hasPreviousPage) {
      setPage((prev) => prev - 1);
    }
  };

  return {
    page,
    setPage,
    pageSize,
    setPageSize,
    totalPages,
    hasPreviousPage,
    hasNextPage,
    goToPage,
    goToNextPage,
    goToPreviousPage,
  };
};
