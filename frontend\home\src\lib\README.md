# Internationalization (i18n) System

This directory contains the implementation for our application's internationalization (i18n) system, which supports both client-side and server-side components in Next.js.

## Architecture

The i18n system follows a clean architecture pattern with clear separation of concerns:

```
src/lib/
├── index.ts                      # Public API (clean facade)
├── unified-i18n.ts              # Unified API integrator
├── _private-shared-i18n.ts      # Shared types and constants
├── _private-i18n.ts             # Client-side implementation
└── _private-server-i18n.ts      # Server-side implementation
```

### Core Principles

1. **Public API Isolation**: Only `index.ts` should be imported by application code
2. **Implementation Hiding**: All implementation details are in private modules
3. **Dynamic Server Imports**: Server code is loaded dynamically to prevent client-side bundling
4. **Type Safety**: All interfaces and translations are fully typed
5. **Unified Interface**: Provides consistent API regardless of environment (client/server)
6. **Flexible Usage**: Supports both object-based and direct function imports

### File Responsibilities

- **index.ts**: Public facade that exports all API elements with flexible usage patterns
- **unified-i18n.ts**: Integration layer that combines client and server implementations
- **_private-shared-i18n.ts**: Shared types, constants, and utilities used by both implementations
- **_private-i18n.ts**: Client-side implementation using i18next and related libraries
- **_private-server-i18n.ts**: Server-side implementation using file system and dynamic imports

## How to Use

Our i18n system supports two usage patterns for flexibility:

### Pattern 1: Using the i18n Object

```tsx
import { i18n, type SupportedLanguage } from '@/lib';

// Access configuration
const supportedLanguages = i18n.shared.languageCodes;

// Use functions
i18n.client.changeLanguage('fr');
const isRightToLeft = i18n.shared.isRTL('ar');
```

### Pattern 2: Using Direct Function Imports

```tsx
import { 
  languageCodes, 
  changeLanguage,
  isRTL,
  type SupportedLanguage 
} from '@/lib';

// Access configuration directly
const supportedLanguages = languageCodes;

// Use functions directly
changeLanguage('fr');
const isRightToLeft = isRTL('ar');
```

Both patterns are supported and work equivalently. Choose the style that best fits your codebase's conventions.

### In Client Components

```tsx
'use client';

import { useTranslation } from '@/components/Providers/i18n-provider';

export default function MyClientComponent() {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('page.title')}</h1>
      <p>{t('page.description')}</p>
    </div>
  );
}
```

### In Server Components

```tsx
import { createTranslation } from '@/components/Providers/server-i18n';

export default async function MyServerComponent() {
  const { t } = await createTranslation('common');

  return (
    <div>
      <h1>{t('page.title')}</h1>
      <p>{t('page.description')}</p>
    </div>
  );
}
```

### Changing Language (Client-side)

```tsx
'use client';

import { Button } from '@/components/ui/button';
import { changeLanguage, type SupportedLanguage } from '@/lib';

export default function LanguageSwitcher() {
  const handleChangeLanguage = (language: SupportedLanguage) => {
    changeLanguage(language);
  };

  return (
    <div>
      <Button onClick={() => handleChangeLanguage('en')}>English</Button>
      <Button onClick={() => handleChangeLanguage('fr')}>Français</Button>
      <Button onClick={() => handleChangeLanguage('ja')}>日本語</Button>
      <Button onClick={() => handleChangeLanguage('ar')}>العربية</Button>
    </div>
  );
}
```

### Accessing i18n Configuration

**Using the i18n object:**
```tsx
import { i18n, type SupportedLanguage } from '@/lib';

// Access i18n configuration
const supportedLanguages = i18n.shared.languageCodes; // ['en', 'fr', 'ja', 'ar']
const isRightToLeft = i18n.shared.isRTL('ar'); // true

// Check environment
if (i18n.isServer) {
  // Server-side specific code
}

// Get translation function (works in both client and server)
const { t } = await i18n.getTranslationFunction('common');
```

**Using direct imports:**
```tsx
import { 
  languageCodes, 
  isRTL, 
  getServerTranslation,
  type SupportedLanguage 
} from '@/lib';

// Access configuration directly
const supportedLanguages = languageCodes; // ['en', 'fr', 'ja', 'ar']
const isRightToLeft = isRTL('ar'); // true

// Get server translation (server-side only)
const { t } = await getServerTranslation('common');
```

## Translation Files

Translation files are stored in `public/locales/{language}/{namespace}.json`:

```
public/
└── locales/
    ├── en/
    │   └── common.json
    ├── fr/
    │   └── common.json
    ├── ja/
    │   └── common.json
    └── ar/
        └── common.json
```

### Translation File Format

Each translation file uses a nested JSON structure:

```json
{
  "page": {
    "title": "Welcome to our application",
    "description": "This is a sample description"
  },
  "auth": {
    "login": "Log In",
    "signup": "Sign Up",
    "logout": "Log Out"
  }
}
```

## Supported Languages

The i18n system currently supports the following languages:

- English (en) - Default
- French (fr)
- Japanese (ja)
- Arabic (ar) - Right-to-left (RTL)

To add a new language:

1. Update `_private-shared-i18n.ts` to include the new language
2. Create translation files for the new language in `public/locales/{language}/`
3. Update any language selection UI components

## Production-Ready Features

- **Performance Optimized**:
  - Translation caching to minimize file system access
  - Environment-specific code splitting
  - Lazy loading for server-side functionality
  
- **Type Safety**:
  - Full TypeScript support for all APIs
  - Type inference from Zod schemas
  - Exhaustive language code typing
  
- **Robust Error Handling**:
  - Safe fallbacks for missing translations
  - Graceful error handling for file system errors
  - Runtime environment checks for server/client code

- **Security**:
  - Server-side code not exposed to client bundles
  - Safe dynamic imports for environment-specific code
  - Protected internal implementation details

## Testing

Tests for the i18n system are located in `src/lib/__tests__/` directory. Run them with:

```bash
pnpm test
```