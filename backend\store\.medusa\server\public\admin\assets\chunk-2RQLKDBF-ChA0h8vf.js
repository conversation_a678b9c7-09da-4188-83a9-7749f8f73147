import{r as y,j as t,bq as l,O as ve,m as ue,b as pe,H as z,Y as ye,br as $,I as Z,bs as Ar,b4 as Kr,L as Fr}from"./index-Bwql5Dzz.js";import{C as me}from"./container-Dqi2woPF.js";import{T as Ur}from"./Trans-VWqfqpAH.js";import{X as Qr}from"./x-mark-mini-DvSTI7zK.js";import{C as zr}from"./check-BGSYwiWc.js";var Zr=Object.defineProperty,K=Object.getOwnPropertySymbols,fe=Object.prototype.hasOwnProperty,xe=Object.prototype.propertyIsEnumerable,de=(r,e,a)=>e in r?Zr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[e]=a,qr=(r,e)=>{for(var a in e)fe.call(e,a)&&de(r,a,e[a]);if(K)for(var a of K(e))xe.call(e,a)&&de(r,a,e[a]);return r},Hr=(r,e)=>{var a={};for(var s in r)fe.call(r,s)&&e.indexOf(s)<0&&(a[s]=r[s]);if(r!=null&&K)for(var s of K(r))e.indexOf(s)<0&&xe.call(r,s)&&(a[s]=r[s]);return a};const Y=y.forwardRef((r,e)=>{var a=r,{color:s="currentColor"}=a,o=Hr(a,["color"]);return y.createElement("svg",qr({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:e},o),y.createElement("path",{stroke:s,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M8.833 1.944h4.223v4.223M13.056 1.944 7.5 7.5M11.278 8.833v2.445c0 .982-.796 1.778-1.778 1.778H3.722a1.777 1.777 0 0 1-1.778-1.778V5.5c0-.982.796-1.778 1.778-1.778h2.445"}))});Y.displayName="ArrowUpRightOnBox";var Wr=Object.defineProperty,F=Object.getOwnPropertySymbols,he=Object.prototype.hasOwnProperty,je=Object.prototype.propertyIsEnumerable,ce=(r,e,a)=>e in r?Wr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[e]=a,Xr=(r,e)=>{for(var a in e)he.call(e,a)&&ce(r,a,e[a]);if(F)for(var a of F(e))je.call(e,a)&&ce(r,a,e[a]);return r},Yr=(r,e)=>{var a={};for(var s in r)he.call(r,s)&&e.indexOf(s)<0&&(a[s]=r[s]);if(r!=null&&F)for(var s of F(r))e.indexOf(s)<0&&je.call(r,s)&&(a[s]=r[s]);return a};const Ne=y.forwardRef((r,e)=>{var a=r,{color:s="currentColor"}=a,o=Yr(a,["color"]);return y.createElement("svg",Xr({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:e},o),y.createElement("g",{stroke:s,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},y.createElement("path",{d:"M12.167 4.167H5.944c-.981 0-1.777.796-1.777 1.777v6.223c0 .982.796 1.777 1.777 1.777h6.223c.982 0 1.777-.796 1.777-1.777V5.944c0-.981-.796-1.777-1.777-1.777"}),y.createElement("path",{d:"M1.99 10.165 1.075 4.01a1.78 1.78 0 0 1 1.497-2.02l6.155-.914a1.78 1.78 0 0 1 1.909 1.092"})),y.createElement("defs",null,y.createElement("clipPath",{id:"a"},y.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});Ne.displayName="SquareTwoStack";function j(r,e){if(r==null)return{};var a={};for(var s in r)if({}.hasOwnProperty.call(r,s)){if(e.indexOf(s)!==-1)continue;a[s]=r[s]}return a}var ge={},we=y.createContext(ge),Gr=(r,e)=>l({},r,e),ea=()=>y.useContext(we),G=y.createContext(()=>{});G.displayName="JVR.DispatchShowTools";function ra(){return y.useReducer(Gr,ge)}function Ce(){return y.useContext(G)}var be=r=>{var{initial:e,dispatch:a,children:s}=r;return t.jsx(we.Provider,{value:e,children:t.jsx(G.Provider,{value:a,children:s})})};be.displayName="JVR.ShowTools";var ke={},Re=y.createContext(ke),aa=(r,e)=>l({},r,e),U=()=>y.useContext(Re),ee=y.createContext(()=>{});ee.displayName="JVR.DispatchExpands";function ta(){return y.useReducer(aa,ke)}function sa(){return y.useContext(ee)}var Ve=r=>{var{initial:e,dispatch:a,children:s}=r;return t.jsx(Re.Provider,{value:e,children:t.jsx(ee.Provider,{value:a,children:s})})};Ve.displayName="JVR.Expands";var Se={Str:{as:"span","data-type":"string",style:{color:"var(--w-rjv-type-string-color, #cb4b16)"},className:"w-rjv-type",children:"string"},Url:{as:"a",style:{color:"var(--w-rjv-type-url-color, #0969da)"},"data-type":"url",className:"w-rjv-type",children:"url"},Undefined:{style:{color:"var(--w-rjv-type-undefined-color, #586e75)"},as:"span","data-type":"undefined",className:"w-rjv-type",children:"undefined"},Null:{style:{color:"var(--w-rjv-type-null-color, #d33682)"},as:"span","data-type":"null",className:"w-rjv-type",children:"null"},Map:{style:{color:"var(--w-rjv-type-map-color, #268bd2)",marginRight:3},as:"span","data-type":"map",className:"w-rjv-type",children:"Map"},Nan:{style:{color:"var(--w-rjv-type-nan-color, #859900)"},as:"span","data-type":"nan",className:"w-rjv-type",children:"NaN"},Bigint:{style:{color:"var(--w-rjv-type-bigint-color, #268bd2)"},as:"span","data-type":"bigint",className:"w-rjv-type",children:"bigint"},Int:{style:{color:"var(--w-rjv-type-int-color, #268bd2)"},as:"span","data-type":"int",className:"w-rjv-type",children:"int"},Set:{style:{color:"var(--w-rjv-type-set-color, #268bd2)",marginRight:3},as:"span","data-type":"set",className:"w-rjv-type",children:"Set"},Float:{style:{color:"var(--w-rjv-type-float-color, #859900)"},as:"span","data-type":"float",className:"w-rjv-type",children:"float"},True:{style:{color:"var(--w-rjv-type-boolean-color, #2aa198)"},as:"span","data-type":"bool",className:"w-rjv-type",children:"bool"},False:{style:{color:"var(--w-rjv-type-boolean-color, #2aa198)"},as:"span","data-type":"bool",className:"w-rjv-type",children:"bool"},Date:{style:{color:"var(--w-rjv-type-date-color, #268bd2)"},as:"span","data-type":"date",className:"w-rjv-type",children:"date"}},Le=y.createContext(Se),la=(r,e)=>l({},r,e),C=()=>y.useContext(Le),re=y.createContext(()=>{});re.displayName="JVR.DispatchTypes";function na(){return y.useReducer(la,Se)}function ia(){return y.useContext(re)}function Te(r){var{initial:e,dispatch:a,children:s}=r;return t.jsx(Le.Provider,{value:e,children:t.jsx(re.Provider,{value:a,children:s})})}Te.displayName="JVR.Types";var oa=["style"];function Ee(r){var{style:e}=r,a=j(r,oa),s=l({cursor:"pointer",height:"1em",width:"1em",userSelect:"none",display:"inline-flex"},e);return t.jsx("svg",l({viewBox:"0 0 24 24",fill:"var(--w-rjv-arrow-color, currentColor)",style:s},a,{children:t.jsx("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"})}))}Ee.displayName="JVR.TriangleArrow";var Je={Arrow:{as:"span",className:"w-rjv-arrow",style:{transform:"rotate(0deg)",transition:"all 0.3s"},children:t.jsx(Ee,{})},Colon:{as:"span",style:{color:"var(--w-rjv-colon-color, var(--w-rjv-color))",marginLeft:0,marginRight:2},className:"w-rjv-colon",children:":"},Quote:{as:"span",style:{color:"var(--w-rjv-quotes-color, #236a7c)"},className:"w-rjv-quotes",children:'"'},ValueQuote:{as:"span",style:{color:"var(--w-rjv-quotes-string-color, #cb4b16)"},className:"w-rjv-quotes",children:'"'},BracketsLeft:{as:"span",style:{color:"var(--w-rjv-brackets-color, #236a7c)"},className:"w-rjv-brackets-start",children:"["},BracketsRight:{as:"span",style:{color:"var(--w-rjv-brackets-color, #236a7c)"},className:"w-rjv-brackets-end",children:"]"},BraceLeft:{as:"span",style:{color:"var(--w-rjv-curlybraces-color, #236a7c)"},className:"w-rjv-curlybraces-start",children:"{"},BraceRight:{as:"span",style:{color:"var(--w-rjv-curlybraces-color, #236a7c)"},className:"w-rjv-curlybraces-end",children:"}"}},_e=y.createContext(Je),da=(r,e)=>l({},r,e),R=()=>y.useContext(_e),ae=y.createContext(()=>{});ae.displayName="JVR.DispatchSymbols";function ca(){return y.useReducer(da,Je)}function va(){return y.useContext(ae)}var Be=r=>{var{initial:e,dispatch:a,children:s}=r;return t.jsx(_e.Provider,{value:e,children:t.jsx(ae.Provider,{value:a,children:s})})};Be.displayName="JVR.Symbols";var De={Copied:{className:"w-rjv-copied",style:{height:"1em",width:"1em",cursor:"pointer",verticalAlign:"middle",marginLeft:5}},CountInfo:{as:"span",className:"w-rjv-object-size",style:{color:"var(--w-rjv-info-color, #0000004d)",paddingLeft:8,fontStyle:"italic"}},CountInfoExtra:{as:"span",className:"w-rjv-object-extra",style:{paddingLeft:8}},Ellipsis:{as:"span",style:{cursor:"pointer",color:"var(--w-rjv-ellipsis-color, #cb4b16)",userSelect:"none"},className:"w-rjv-ellipsis",children:"..."},Row:{as:"div",className:"w-rjv-line"},KeyName:{as:"span",className:"w-rjv-object-key"}},$e=y.createContext(De),ua=(r,e)=>l({},r,e),S=()=>y.useContext($e),te=y.createContext(()=>{});te.displayName="JVR.DispatchSection";function pa(){return y.useReducer(ua,De)}function ya(){return y.useContext(te)}var Oe=r=>{var{initial:e,dispatch:a,children:s}=r;return t.jsx($e.Provider,{value:e,children:t.jsx(te.Provider,{value:a,children:s})})};Oe.displayName="JVR.Section";var Me={objectSortKeys:!1,indentWidth:15},se=y.createContext(Me);se.displayName="JVR.Context";var Ie=y.createContext(()=>{});Ie.displayName="JVR.DispatchContext";function ma(r,e){return l({},r,e)}var O=()=>y.useContext(se),Pe=r=>{var{children:e,initialState:a,initialTypes:s}=r,[o,d]=y.useReducer(ma,Object.assign({},Me,a)),[n,i]=ra(),[c,v]=ta(),[u,p]=na(),[m,f]=ca(),[N,g]=pa();return y.useEffect(()=>d(l({},a)),[a]),t.jsx(se.Provider,{value:o,children:t.jsx(Ie.Provider,{value:d,children:t.jsx(be,{initial:n,dispatch:i,children:t.jsx(Ve,{initial:c,dispatch:v,children:t.jsx(Te,{initial:l({},u,s),dispatch:p,children:t.jsx(Be,{initial:m,dispatch:f,children:t.jsx(Oe,{initial:N,dispatch:g,children:e})})})})})})})};Pe.displayName="JVR.Provider";function fa(r){if(r==null)throw new TypeError("Cannot destructure "+r)}var xa=["isNumber","value","parentValue","keyName","keys"],ha=["as","render"],ja=["as","render"],Na=["as","render"],ga=["as","style","render"],wa=["as","render"],Ca=["as","render"],ba=["as","render"],ka=["as","render"],q=r=>{var{Quote:e={}}=R(),{isNumber:a,value:s,parentValue:o,keyName:d,keys:n}=r,i=j(r,xa);if(a)return null;var{as:c,render:v}=e,u=j(e,ha),p=c||"span",m=l({},i,u),f={value:s,parentValue:o,keyName:d,keys:n||(d?[d]:[])},N=v&&typeof v=="function"&&v(m,f);return N||t.jsx(p,l({},m))};q.displayName="JVR.Quote";var A=r=>{var{ValueQuote:e={}}=R(),a=l({},(fa(r),r)),{as:s,render:o}=e,d=j(e,ja),n=s||"span",i=l({},a,d),c=o&&typeof o=="function"&&o(i,{});return c||t.jsx(n,l({},i))};A.displayName="JVR.ValueQuote";var Ae=r=>{var{value:e,parentValue:a,keyName:s,keys:o}=r,{Colon:d={}}=R(),{as:n,render:i}=d,c=j(d,Na),v=n||"span",u=i&&typeof i=="function"&&i(c,{value:e,parentValue:a,keyName:s,keys:o||(s?[s]:[])});return u||t.jsx(v,l({},c))};Ae.displayName="JVR.Colon";var Ke=r=>{var{Arrow:e={}}=R(),a=U(),{expandKey:s,style:o,value:d,parentValue:n,keyName:i,keys:c}=r,v=!!a[s],{as:u,style:p,render:m}=e,f=j(e,ga),N=u||"span",g=m&&typeof m=="function",x=l({},f,{"data-expanded":v,style:l({},p,o)}),b={value:d,parentValue:n,keyName:i,keys:c||(i?[i]:[])},w=g&&m(x,b);return w||t.jsx(N,l({},f,{style:l({},p,o)}))};Ke.displayName="JVR.Arrow";var Fe=r=>{var{isBrackets:e,value:a,parentValue:s,keyName:o,keys:d}=r,{BracketsLeft:n={},BraceLeft:i={}}=R(),c={value:a,parentValue:s,keyName:o,keys:d||(o?[o]:[])};if(e){var{as:v,render:u}=n,p=j(n,wa),m=v||"span",f=u&&typeof u=="function"&&u(p,c);return f||t.jsx(m,l({},p))}var{as:N,render:g}=i,x=j(i,Ca),b=N||"span",w=g&&typeof g=="function"&&g(x,c);return w||t.jsx(b,l({},x))};Fe.displayName="JVR.BracketsOpen";var le=r=>{var{isBrackets:e,isVisiable:a,value:s,parentValue:o,keyName:d,keys:n}=r,i={value:s,parentValue:o,keyName:d,keys:n||(d?[d]:[])};if(!a)return null;var{BracketsRight:c={},BraceRight:v={}}=R();if(e){var{as:u,render:p}=c,m=j(c,ba),f=u||"span",N=p&&typeof p=="function"&&p(m,i);return N||t.jsx(f,l({},m))}var{as:g,render:x}=v,b=j(v,ka),w=g||"span",k=x&&typeof x=="function"&&x(b,i);return k||t.jsx(w,l({},b))};le.displayName="JVR.BracketsClose";var Ue=r=>{var e,{value:a,expandKey:s,level:o,keys:d=[]}=r,n=U(),i=Array.isArray(a),{collapsed:c,shouldExpandNodeInitially:v}=O(),u=a instanceof Set,p=typeof c=="boolean"?c:typeof c=="number"?o>c:!1,m=(e=n[s])!=null?e:p,f=Object.keys(a).length;if(n[s]===void 0&&v&&v(m,{value:a,keys:d,level:o})||m||f===0)return null;var N={paddingLeft:4};return t.jsx("div",{style:N,children:t.jsx(le,{isBrackets:i||u,isVisiable:!0})})};Ue.displayName="JVR.NestedClose";var Ra=["as","render"],Va=["as","render"],Sa=["as","render"],La=["as","render"],Ta=["as","render"],Ea=["as","render"],Ja=["as","render"],_a=["as","render"],Ba=["as","render"],Da=["as","render"],$a=["as","render"],Oa=["as","render"],Ma=["as","render"],H=r=>{if(r===void 0)return"0n";if(typeof r=="string")try{r=BigInt(r)}catch{return"0n"}return r?r.toString()+"n":"0n"},Qe=r=>{var{value:e,keyName:a}=r,{Set:s={},displayDataTypes:o}=C(),d=e instanceof Set;if(!d||!o)return null;var{as:n,render:i}=s,c=j(s,Ra),v=i&&typeof i=="function",u=v&&i(c,{type:"type",value:e,keyName:a});if(u)return u;var p=n||"span";return t.jsx(p,l({},c))};Qe.displayName="JVR.SetComp";var ze=r=>{var{value:e,keyName:a}=r,{Map:s={},displayDataTypes:o}=C(),d=e instanceof Map;if(!d||!o)return null;var{as:n,render:i}=s,c=j(s,Va),v=i&&typeof i=="function",u=v&&i(c,{type:"type",value:e,keyName:a});if(u)return u;var p=n||"span";return t.jsx(p,l({},c))};ze.displayName="JVR.MapComp";var L={opacity:.75,paddingRight:4},Ze=r=>{var{children:e="",keyName:a}=r,{Str:s={},displayDataTypes:o}=C(),{shortenTextAfterLength:d=30}=O(),{as:n,render:i}=s,c=j(s,Sa),v=e,[u,p]=y.useState(d&&v.length>d);y.useEffect(()=>p(d&&v.length>d),[d]);var m=n||"span",f=l({},L,s.style||{});d>0&&(c.style=l({},c.style,{cursor:v.length<=d?"initial":"pointer"}),v.length>d&&(c.onClick=()=>{p(!u)}));var N=u?v.slice(0,d)+"...":v,g=i&&typeof i=="function",x=g&&i(l({},c,{style:f}),{type:"type",value:e,keyName:a}),b=g&&i(l({},c,{children:N,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(x||t.jsx(m,l({},c,{style:f}))),b||t.jsxs(y.Fragment,{children:[t.jsx(A,{}),t.jsx(m,l({},c,{className:"w-rjv-value",children:N})),t.jsx(A,{})]})]})};Ze.displayName="JVR.TypeString";var qe=r=>{var{children:e,keyName:a}=r,{True:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,La),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m||t.jsx(c,l({},i,{className:"w-rjv-value",children:e==null?void 0:e.toString()}))]})};qe.displayName="JVR.TypeTrue";var He=r=>{var{children:e,keyName:a}=r,{False:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,Ta),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m||t.jsx(c,l({},i,{className:"w-rjv-value",children:e==null?void 0:e.toString()}))]})};He.displayName="JVR.TypeFalse";var We=r=>{var{children:e,keyName:a}=r,{Float:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,Ea),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m||t.jsx(c,l({},i,{className:"w-rjv-value",children:e==null?void 0:e.toString()}))]})};We.displayName="JVR.TypeFloat";var Xe=r=>{var{children:e,keyName:a}=r,{Int:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,Ja),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m||t.jsx(c,l({},i,{className:"w-rjv-value",children:e==null?void 0:e.toString()}))]})};Xe.displayName="JVR.TypeInt";var Ye=r=>{var{children:e,keyName:a}=r,{Bigint:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,_a),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m||t.jsx(c,l({},i,{className:"w-rjv-value",children:H(e==null?void 0:e.toString())}))]})};Ye.displayName="JVR.TypeFloat";var Ge=r=>{var{children:e,keyName:a}=r,{Url:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,Ba),c=d||"span",v=l({},L,s.style),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e==null?void 0:e.href,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m||t.jsxs("a",l({href:e==null?void 0:e.href,target:"_blank"},i,{className:"w-rjv-value",children:[t.jsx(A,{}),e==null?void 0:e.href,t.jsx(A,{})]}))]})};Ge.displayName="JVR.TypeUrl";var er=r=>{var{children:e,keyName:a}=r,{Date:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,Da),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=e instanceof Date?e.toLocaleString():e,f=u&&n(l({},i,{children:m,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),f||t.jsx(c,l({},i,{className:"w-rjv-value",children:m}))]})};er.displayName="JVR.TypeDate";var rr=r=>{var{children:e,keyName:a}=r,{Undefined:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,$a),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m]})};rr.displayName="JVR.TypeUndefined";var ar=r=>{var{children:e,keyName:a}=r,{Null:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,Oa),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e,className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m]})};ar.displayName="JVR.TypeNull";var tr=r=>{var{children:e,keyName:a}=r,{Nan:s={},displayDataTypes:o}=C(),{as:d,render:n}=s,i=j(s,Ma),c=d||"span",v=l({},L,s.style||{}),u=n&&typeof n=="function",p=u&&n(l({},i,{style:v}),{type:"type",value:e,keyName:a}),m=u&&n(l({},i,{children:e==null?void 0:e.toString(),className:"w-rjv-value"}),{type:"value",value:e,keyName:a});return t.jsxs(y.Fragment,{children:[o&&(p||t.jsx(c,l({},i,{style:v}))),m]})};tr.displayName="JVR.TypeNan";var Ia=r=>Number(r)===r&&r%1!==0||isNaN(r),sr=r=>{var{value:e,keyName:a}=r,s={keyName:a};return e instanceof URL?t.jsx(Ge,l({},s,{children:e})):typeof e=="string"?t.jsx(Ze,l({},s,{children:e})):e===!0?t.jsx(qe,l({},s,{children:e})):e===!1?t.jsx(He,l({},s,{children:e})):e===null?t.jsx(ar,l({},s,{children:e})):e===void 0?t.jsx(rr,l({},s,{children:e})):e instanceof Date?t.jsx(er,l({},s,{children:e})):typeof e=="number"&&isNaN(e)?t.jsx(tr,l({},s,{children:e})):typeof e=="number"&&Ia(e)?t.jsx(We,l({},s,{children:e})):typeof e=="bigint"?t.jsx(Ye,l({},s,{children:e})):typeof e=="number"?t.jsx(Xe,l({},s,{children:e})):null};sr.displayName="JVR.Value";function B(r,e,a){var s=va(),o=[r.className,e.className].filter(Boolean).join(" "),d=l({},r,e,{className:o,style:l({},r.style,e.style),children:e.children||r.children});y.useEffect(()=>s({[a]:d}),[e])}function V(r,e,a){var s=ia(),o=[r.className,e.className].filter(Boolean).join(" "),d=l({},r,e,{className:o,style:l({},r.style,e.style),children:e.children||r.children});y.useEffect(()=>s({[a]:d}),[e])}function I(r,e,a){var s=ya(),o=[r.className,e.className].filter(Boolean).join(" "),d=l({},r,e,{className:o,style:l({},r.style,e.style),children:e.children||r.children});y.useEffect(()=>s({[a]:d}),[e])}var Pa=["as","render"],lr=r=>{var{KeyName:e={}}=S();return I(e,r,"KeyName"),null};lr.displayName="JVR.KeyName";var nr=r=>{var{children:e,value:a,parentValue:s,keyName:o,keys:d}=r,n=typeof e=="number",i={color:n?"var(--w-rjv-key-number, #268bd2)":"var(--w-rjv-key-string, #002b36)"},{KeyName:c={}}=S(),{as:v,render:u}=c,p=j(c,Pa);p.style=l({},p.style,i);var m=v||"span",f=u&&typeof u=="function"&&u(l({},p,{children:e}),{value:a,parentValue:s,keyName:o,keys:d||(o?[o]:[])});return f||t.jsx(m,l({},p,{children:e}))};nr.displayName="JVR.KeyNameComp";var Aa=["children","value","parentValue","keyName","keys"],Ka=["as","render","children"],ir=r=>{var{Row:e={}}=S();return I(e,r,"Row"),null};ir.displayName="JVR.Row";var or=r=>{var{children:e,value:a,parentValue:s,keyName:o,keys:d}=r,n=j(r,Aa),{Row:i={}}=S(),{as:c,render:v}=i,u=j(i,Ka),p=c||"div",m=v&&typeof v=="function"&&v(l({},n,u,{children:e}),{value:a,keyName:o,parentValue:s,keys:d});return m||t.jsx(p,l({},n,u,{children:e}))};or.displayName="JVR.RowComp";function Fa(r){var e=y.useRef();return y.useEffect(()=>{e.current=r}),e.current}function Ua(r){var{value:e,highlightUpdates:a,highlightContainer:s}=r,o=Fa(e),d=y.useMemo(()=>{if(!a||o===void 0)return!1;if(typeof e!=typeof o)return!0;if(typeof e=="number")return isNaN(e)&&isNaN(o)?!1:e!==o;if(Array.isArray(e)!==Array.isArray(o))return!0;if(typeof e=="object"||typeof e=="function")return!1;if(e!==o)return!0},[a,e]);y.useEffect(()=>{s&&s.current&&d&&"animate"in s.current&&s.current.animate([{backgroundColor:"var(--w-rjv-update-color, #ebcb8b)"},{backgroundColor:""}],{duration:1e3,easing:"ease-in"})},[d,e,s])}var Qa=["keyName","value","parentValue","expandKey","keys"],za=["as","render"],ne=r=>{var{keyName:e,value:a,parentValue:s,expandKey:o,keys:d}=r,n=j(r,Qa),{onCopied:i,enableClipboard:c}=O(),v=ea(),u=v[o],[p,m]=y.useState(!1),{Copied:f={}}=S();if(c===!1||!u)return null;var N=P=>{P.stopPropagation();var E="";typeof a=="number"&&a===1/0?E="Infinity":typeof a=="number"&&isNaN(a)?E="NaN":typeof a=="bigint"?E=H(a):a instanceof Date?E=a.toLocaleString():E=JSON.stringify(a,(J,_)=>typeof _=="bigint"?H(_):_,2),i&&i(E,a),m(!0);var Q=navigator.clipboard||{writeText(J){return new Promise((_,M)=>{var T=document.createElement("textarea");T.style.position="absolute",T.style.opacity="0",T.style.left="-99999999px",T.value=J,document.body.appendChild(T),T.select(),document.execCommand("copy")?_():M(),T.remove()})}};Q.writeText(E).then(()=>{var J=setTimeout(()=>{m(!1),clearTimeout(J)},3e3)}).catch(J=>{})},g={style:{display:"inline-flex"},fill:p?"var(--w-rjv-copied-success-color, #28a745)":"var(--w-rjv-copied-color, currentColor)",onClick:N},{render:x}=f,b=j(f,za),w=l({},b,n,g,{style:l({},b.style,n.style,g.style)}),k=x&&typeof x=="function",D=k&&x(l({},w,{"data-copied":p}),{value:a,keyName:e,keys:d,parentValue:s});return D||(p?t.jsx("svg",l({viewBox:"0 0 32 36"},w,{children:t.jsx("path",{d:"M27.5,33 L2.5,33 L2.5,12.5 L27.5,12.5 L27.5,15.2249049 C29.1403264,13.8627542 29.9736597,13.1778155 30,13.1700887 C30,11.9705278 30,10.0804982 30,7.5 C30,6.1 28.9,5 27.5,5 L20,5 C20,2.2 17.8,0 15,0 C12.2,0 10,2.2 10,5 L2.5,5 C1.1,5 0,6.1 0,7.5 L0,33 C0,34.4 1.1,36 2.5,36 L27.5,36 C28.9,36 30,34.4 30,33 L30,26.1114493 L27.5,28.4926435 L27.5,33 Z M7.5,7.5 L10,7.5 C10,7.5 12.5,6.4 12.5,5 C12.5,3.6 13.6,2.5 15,2.5 C16.4,2.5 17.5,3.6 17.5,5 C17.5,6.4 18.8,7.5 20,7.5 L22.5,7.5 C22.5,7.5 25,8.6 25,10 L5,10 C5,8.5 6.1,7.5 7.5,7.5 Z M5,27.5 L10,27.5 L10,25 L5,25 L5,27.5 Z M28.5589286,16 L32,19.6 L21.0160714,30.5382252 L13.5303571,24.2571429 L17.1303571,20.6571429 L21.0160714,24.5428571 L28.5589286,16 Z M17.5,15 L5,15 L5,17.5 L17.5,17.5 L17.5,15 Z M10,20 L5,20 L5,22.5 L10,22.5 L10,20 Z"})})):t.jsx("svg",l({viewBox:"0 0 32 36"},w,{children:t.jsx("path",{d:"M27.5,33 L2.5,33 L2.5,12.5 L27.5,12.5 L27.5,20 L30,20 L30,7.5 C30,6.1 28.9,5 27.5,5 L20,5 C20,2.2 17.8,0 15,0 C12.2,0 10,2.2 10,5 L2.5,5 C1.1,5 0,6.1 0,7.5 L0,33 C0,34.4 1.1,36 2.5,36 L27.5,36 C28.9,36 30,34.4 30,33 L30,29 L27.5,29 L27.5,33 Z M7.5,7.5 L10,7.5 C10,7.5 12.5,6.4 12.5,5 C12.5,3.6 13.6,2.5 15,2.5 C16.4,2.5 17.5,3.6 17.5,5 C17.5,6.4 18.8,7.5 20,7.5 L22.5,7.5 C22.5,7.5 25,8.6 25,10 L5,10 C5,8.5 6.1,7.5 7.5,7.5 Z M5,27.5 L10,27.5 L10,25 L5,25 L5,27.5 Z M22.5,21.5 L22.5,16.5 L12.5,24 L22.5,31.5 L22.5,26.5 L32,26.5 L32,21.5 L22.5,21.5 Z M17.5,15 L5,15 L5,17.5 L17.5,17.5 L17.5,15 Z M10,20 L5,20 L5,22.5 L10,22.5 L10,20 Z"})})))};ne.displayName="JVR.Copied";function dr(){var r=y.useRef(null);return r.current===null&&(r.current="custom-id-"+Math.random().toString(36).substr(2,9)),r.current}var cr=r=>{var e,{value:a,expandKey:s="",level:o,keys:d=[]}=r,n=U(),{objectSortKeys:i,indentWidth:c,collapsed:v,shouldExpandNodeInitially:u}=O(),p=Array.isArray(a),m=typeof v=="boolean"?v:typeof v=="number"?o>v:!1,f=(e=n[s])!=null?e:m;if(n[s]===void 0&&u&&u(f,{value:a,keys:d,level:o})||f)return null;var N=p?Object.entries(a).map(x=>[Number(x[0]),x[1]]):Object.entries(a);i&&(N=i===!0?N.sort((x,b)=>{var[w]=x,[k]=b;return typeof w=="string"&&typeof k=="string"?w.localeCompare(k):0}):N.sort((x,b)=>{var[w,k]=x,[D,P]=b;return typeof w=="string"&&typeof D=="string"?i(w,D,k,P):0}));var g={borderLeft:"var(--w-rjv-border-left-width, 1px) var(--w-rjv-line-style, solid) var(--w-rjv-line-color, #ebebeb)",paddingLeft:c,marginLeft:6};return t.jsx("div",{className:"w-rjv-wrap",style:g,children:N.map((x,b)=>{var[w,k]=x;return t.jsx(vr,{parentValue:a,keyName:w,keys:[...d,w],value:k,level:o},b)})})};cr.displayName="JVR.KeyValues";var ie=r=>{var{keyName:e,parentValue:a,keys:s,value:o}=r,{highlightUpdates:d}=O(),n=typeof e=="number",i=y.useRef(null);Ua({value:o,highlightUpdates:d,highlightContainer:i});var c={keyName:e,value:o,keys:s,parentValue:a};return t.jsxs(y.Fragment,{children:[t.jsxs("span",{ref:i,children:[t.jsx(q,l({isNumber:n,"data-placement":"left"},c)),t.jsx(nr,l({},c,{children:e})),t.jsx(q,l({isNumber:n,"data-placement":"right"},c))]}),t.jsx(Ae,l({},c))]})};ie.displayName="JVR.KayName";var vr=r=>{var{keyName:e,value:a,parentValue:s,level:o=0,keys:d=[]}=r,n=Ce(),i=dr(),c=Array.isArray(a),v=a instanceof Set,u=a instanceof Map,p=a instanceof Date,m=a instanceof URL,f=a&&typeof a=="object"&&!c&&!v&&!u&&!p&&!m,N=f||c||v||u;if(N){var g=v?Array.from(a):u?Object.fromEntries(a):a;return t.jsx(oe,{keyName:e,value:g,parentValue:s,initialValue:a,keys:d,level:o+1})}var x={onMouseEnter:()=>n({[i]:!0}),onMouseLeave:()=>n({[i]:!1})};return t.jsxs(or,l({className:"w-rjv-line",value:a,keyName:e,keys:d,parentValue:s},x,{children:[t.jsx(ie,{keyName:e,value:a,keys:d,parentValue:s}),t.jsx(sr,{keyName:e,value:a}),t.jsx(ne,{keyName:e,value:a,keys:d,parentValue:s,expandKey:i})]}))};vr.displayName="JVR.KeyValuesItem";var Za=["value","keyName"],qa=["as","render"],ur=r=>{var{CountInfoExtra:e={}}=S();return I(e,r,"CountInfoExtra"),null};ur.displayName="JVR.CountInfoExtra";var pr=r=>{var{value:e={},keyName:a}=r,s=j(r,Za),{CountInfoExtra:o={}}=S(),{as:d,render:n}=o,i=j(o,qa);if(!n&&!i.children)return null;var c=d||"span",v=n&&typeof n=="function",u=l({},i,s),p=v&&n(u,{value:e,keyName:a});return p||t.jsx(c,l({},u))};pr.displayName="JVR.CountInfoExtraComps";var Ha=["value","keyName"],Wa=["as","render"],yr=r=>{var{CountInfo:e={}}=S();return I(e,r,"CountInfo"),null};yr.displayName="JVR.CountInfo";var mr=r=>{var{value:e={},keyName:a}=r,s=j(r,Ha),{displayObjectSize:o}=O(),{CountInfo:d={}}=S();if(!o)return null;var{as:n,render:i}=d,c=j(d,Wa),v=n||"span";c.style=l({},c.style,r.style);var u=Object.keys(e).length;c.children||(c.children=u+" item"+(u===1?"":"s"));var p=l({},c,s),m=i&&typeof i=="function",f=m&&i(l({},p,{"data-length":u}),{value:e,keyName:a});return f||t.jsx(v,l({},p))};mr.displayName="JVR.CountInfoComp";var Xa=["as","render"],fr=r=>{var{Ellipsis:e={}}=S();return I(e,r,"Ellipsis"),null};fr.displayName="JVR.Ellipsis";var xr=r=>{var{isExpanded:e,value:a,keyName:s}=r,{Ellipsis:o={}}=S(),{as:d,render:n}=o,i=j(o,Xa),c=d||"span",v=n&&typeof n=="function"&&n(l({},i,{"data-expanded":e}),{value:a,keyName:s});return v||(!e||typeof a=="object"&&Object.keys(a).length==0?null:t.jsx(c,l({},i)))};xr.displayName="JVR.EllipsisComp";var hr=r=>{var e,{keyName:a,expandKey:s,keys:o=[],initialValue:d,value:n,parentValue:i,level:c}=r,v=U(),u=sa(),{onExpand:p,collapsed:m,shouldExpandNodeInitially:f}=O(),N=Array.isArray(n),g=n instanceof Set,x=typeof m=="boolean"?m:typeof m=="number"?c>m:!1,b=typeof n=="object",w=(e=v[s])!=null?e:x,k=f&&f(w,{value:n,keys:o,level:c});v[s]===void 0&&k!==void 0&&(w=k);var D=()=>{var T={expand:!w,value:n,keyid:s,keyName:a};p&&p(T),u({[s]:T.expand})},P={display:"inline-flex",alignItems:"center"},E={transform:"rotate("+(w?"-90":"0")+"deg)",transition:"all 0.3s"},Q=Object.keys(n).length,J=Q!==0&&(N||g||b),_={style:P};J&&(_.onClick=D);var M={keyName:a,value:n,keys:o,parentValue:i};return t.jsxs("span",l({},_,{children:[J&&t.jsx(Ke,l({style:E,expandKey:s},M)),(a||typeof a=="number")&&t.jsx(ie,l({},M)),t.jsx(Qe,{value:d,keyName:a}),t.jsx(ze,{value:d,keyName:a}),t.jsx(Fe,l({isBrackets:N||g},M)),t.jsx(xr,{keyName:a,value:n,isExpanded:w}),t.jsx(le,l({isVisiable:w||!J,isBrackets:N||g},M)),t.jsx(mr,{value:n,keyName:a}),t.jsx(pr,{value:n,keyName:a}),t.jsx(ne,{keyName:a,value:n,expandKey:s,parentValue:i,keys:o})]}))};hr.displayName="JVR.NestedOpen";var Ya=["className","children","parentValue","keyid","level","value","initialValue","keys","keyName"],oe=y.forwardRef((r,e)=>{var{className:a="",parentValue:s,level:o=1,value:d,initialValue:n,keys:i,keyName:c}=r,v=j(r,Ya),u=Ce(),p=dr(),m=[a,"w-rjv-inner"].filter(Boolean).join(" "),f={onMouseEnter:()=>u({[p]:!0}),onMouseLeave:()=>u({[p]:!1})};return t.jsxs("div",l({className:m,ref:e},v,f,{children:[t.jsx(hr,{expandKey:p,value:d,level:o,keys:i,parentValue:s,keyName:c,initialValue:n}),t.jsx(cr,{expandKey:p,value:d,level:o,keys:i,parentValue:s,keyName:c}),t.jsx(Ue,{expandKey:p,value:d,level:o,keys:i})]}))});oe.displayName="JVR.Container";var jr=r=>{var{BraceLeft:e={}}=R();return B(e,r,"BraceLeft"),null};jr.displayName="JVR.BraceLeft";var Nr=r=>{var{BraceRight:e={}}=R();return B(e,r,"BraceRight"),null};Nr.displayName="JVR.BraceRight";var gr=r=>{var{BracketsLeft:e={}}=R();return B(e,r,"BracketsLeft"),null};gr.displayName="JVR.BracketsLeft";var wr=r=>{var{BracketsRight:e={}}=R();return B(e,r,"BracketsRight"),null};wr.displayName="JVR.BracketsRight";var Cr=r=>{var{Arrow:e={}}=R();return B(e,r,"Arrow"),null};Cr.displayName="JVR.Arrow";var br=r=>{var{Colon:e={}}=R();return B(e,r,"Colon"),null};br.displayName="JVR.Colon";var kr=r=>{var{Quote:e={}}=R();return B(e,r,"Quote"),null};kr.displayName="JVR.Quote";var Rr=r=>{var{ValueQuote:e={}}=R();return B(e,r,"ValueQuote"),null};Rr.displayName="JVR.ValueQuote";var Vr=r=>{var{Bigint:e={}}=C();return V(e,r,"Bigint"),null};Vr.displayName="JVR.Bigint";var Sr=r=>{var{Date:e={}}=C();return V(e,r,"Date"),null};Sr.displayName="JVR.Date";var Lr=r=>{var{False:e={}}=C();return V(e,r,"False"),null};Lr.displayName="JVR.False";var Tr=r=>{var{Float:e={}}=C();return V(e,r,"Float"),null};Tr.displayName="JVR.Float";var Er=r=>{var{Int:e={}}=C();return V(e,r,"Int"),null};Er.displayName="JVR.Int";var Jr=r=>{var{Map:e={}}=C();return V(e,r,"Map"),null};Jr.displayName="JVR.Map";var _r=r=>{var{Nan:e={}}=C();return V(e,r,"Nan"),null};_r.displayName="JVR.Nan";var Br=r=>{var{Null:e={}}=C();return V(e,r,"Null"),null};Br.displayName="JVR.Null";var Dr=r=>{var{Set:e={}}=C();return V(e,r,"Set"),null};Dr.displayName="JVR.Set";var $r=r=>{var{Str:e={}}=C();return V(e,r,"Str"),null};$r.displayName="JVR.StringText";var Or=r=>{var{True:e={}}=C();return V(e,r,"True"),null};Or.displayName="JVR.True";var Mr=r=>{var{Undefined:e={}}=C();return V(e,r,"Undefined"),null};Mr.displayName="JVR.Undefined";var Ir=r=>{var{Url:e={}}=C();return V(e,r,"Url"),null};Ir.displayName="JVR.Url";var Pr=r=>{var{Copied:e={}}=S();return I(e,r,"Copied"),null};Pr.displayName="JVR.Copied";var Ga=["className","style","value","children","collapsed","shouldExpandNodeInitially","indentWidth","displayObjectSize","shortenTextAfterLength","highlightUpdates","enableClipboard","displayDataTypes","objectSortKeys","onExpand","onCopied"],h=y.forwardRef((r,e)=>{var{className:a="",style:s,value:o,children:d,collapsed:n,shouldExpandNodeInitially:i,indentWidth:c=15,displayObjectSize:v=!0,shortenTextAfterLength:u=30,highlightUpdates:p=!0,enableClipboard:m=!0,displayDataTypes:f=!0,objectSortKeys:N=!1,onExpand:g,onCopied:x}=r,b=j(r,Ga),w=l({lineHeight:1.4,fontFamily:"var(--w-rjv-font-family, Menlo, monospace)",color:"var(--w-rjv-color, #002b36)",backgroundColor:"var(--w-rjv-background-color, #00000000)",fontSize:13},s),k=["w-json-view-container","w-rjv",a].filter(Boolean).join(" ");return t.jsxs(Pe,{initialState:{value:o,objectSortKeys:N,indentWidth:c,shouldExpandNodeInitially:i,displayObjectSize:v,collapsed:n,enableClipboard:m,shortenTextAfterLength:u,highlightUpdates:p,onCopied:x,onExpand:g},initialTypes:{displayDataTypes:f},children:[t.jsx(oe,l({value:o},b,{ref:e,className:k,style:w})),d]})});h.Bigint=Vr;h.Date=Sr;h.False=Lr;h.Float=Tr;h.Int=Er;h.Map=Jr;h.Nan=_r;h.Null=Br;h.Set=Dr;h.String=$r;h.True=Or;h.Undefined=Mr;h.Url=Ir;h.ValueQuote=Rr;h.Arrow=Cr;h.Colon=br;h.Quote=kr;h.Ellipsis=fr;h.BraceLeft=jr;h.BraceRight=Nr;h.BracketsLeft=gr;h.BracketsRight=wr;h.Copied=Pr;h.CountInfo=yr;h.CountInfoExtra=ur;h.KeyName=lr;h.Row=ir;h.displayName="JVR.JsonView";var W=({data:r})=>{const{t:e}=pe(),a=Object.keys(r).length;return t.jsxs(me,{className:"flex items-center justify-between px-6 py-4",children:[t.jsxs("div",{className:"flex items-center gap-x-4",children:[t.jsx(z,{level:"h2",children:e("json.header")}),t.jsx(ye,{size:"2xsmall",rounded:"full",children:e("json.numberOfKeys",{count:a})})]}),t.jsxs($,{children:[t.jsx($.Trigger,{asChild:!0,children:t.jsx(Z,{size:"small",variant:"transparent",className:"text-ui-fg-muted hover:text-ui-fg-subtle",children:t.jsx(Y,{})})}),t.jsxs($.Content,{className:"bg-ui-contrast-bg-base text-ui-code-fg-subtle !shadow-elevation-commandbar overflow-hidden border border-none max-md:inset-x-2 max-md:max-w-[calc(100%-16px)]",children:[t.jsxs("div",{className:"bg-ui-code-bg-base flex items-center justify-between px-6 py-4",children:[t.jsxs("div",{className:"flex items-center gap-x-4",children:[t.jsx($.Title,{asChild:!0,children:t.jsx(z,{className:"text-ui-contrast-fg-primary",children:t.jsx(Ur,{i18nKey:"json.drawer.header",count:a,components:[t.jsx("span",{className:"text-ui-fg-subtle"},"count-span")]})})}),t.jsx($.Description,{className:"sr-only",children:e("json.drawer.description")})]}),t.jsxs("div",{className:"flex items-center gap-x-2",children:[t.jsx(Ar,{className:"bg-ui-contrast-bg-subtle border-ui-contrast-border-base text-ui-contrast-fg-secondary",children:"esc"}),t.jsx($.Close,{asChild:!0,children:t.jsx(Z,{size:"small",variant:"transparent",className:"text-ui-contrast-fg-secondary hover:text-ui-contrast-fg-primary hover:bg-ui-contrast-bg-base-hover active:bg-ui-contrast-bg-base-pressed focus-visible:bg-ui-contrast-bg-base-hover focus-visible:shadow-borders-interactive-with-active",children:t.jsx(Qr,{})})})]})]}),t.jsx($.Body,{className:"flex flex-1 flex-col overflow-hidden px-[5px] py-0 pb-[5px]",children:t.jsx("div",{className:"bg-ui-contrast-bg-subtle flex-1 overflow-auto rounded-b-[4px] rounded-t-lg p-3",children:t.jsx(y.Suspense,{fallback:t.jsx("div",{className:"flex size-full flex-col"}),children:t.jsxs(h,{value:r,displayDataTypes:!1,style:{"--w-rjv-font-family":"Roboto Mono, monospace","--w-rjv-line-color":"var(--contrast-border-base)","--w-rjv-curlybraces-color":"var(--contrast-fg-secondary)","--w-rjv-brackets-color":"var(--contrast-fg-secondary)","--w-rjv-key-string":"var(--contrast-fg-primary)","--w-rjv-info-color":"var(--contrast-fg-secondary)","--w-rjv-type-string-color":"var(--tag-green-icon)","--w-rjv-quotes-string-color":"var(--tag-green-icon)","--w-rjv-type-boolean-color":"var(--tag-orange-icon)","--w-rjv-type-int-color":"var(--tag-orange-icon)","--w-rjv-type-float-color":"var(--tag-orange-icon)","--w-rjv-type-bigint-color":"var(--tag-orange-icon)","--w-rjv-key-number":"var(--contrast-fg-secondary)","--w-rjv-arrow-color":"var(--contrast-fg-secondary)","--w-rjv-copied-color":"var(--contrast-fg-secondary)","--w-rjv-copied-success-color":"var(--contrast-fg-primary)","--w-rjv-colon-color":"var(--contrast-fg-primary)","--w-rjv-ellipsis-color":"var(--contrast-fg-secondary)"},collapsed:1,children:[t.jsx(h.Quote,{render:()=>t.jsx("span",{})}),t.jsx(h.Null,{render:()=>t.jsx("span",{className:"text-ui-tag-red-icon",children:"null"})}),t.jsx(h.Undefined,{render:()=>t.jsx("span",{className:"text-ui-tag-blue-icon",children:"undefined"})}),t.jsx(h.CountInfo,{render:(s,{value:o})=>t.jsx("span",{className:"text-ui-contrast-fg-secondary ml-2",children:e("general.items",{count:Object.keys(o).length})})}),t.jsx(h.Arrow,{children:t.jsx(Kr,{className:"text-ui-contrast-fg-secondary -ml-[0.5px]"})}),t.jsx(h.Colon,{children:t.jsx("span",{className:"mr-1",children:":"})}),t.jsx(h.Copied,{render:({style:s},{value:o})=>t.jsx(et,{style:s,value:o})})]})})})})]})]})]})},et=({style:r,value:e})=>{const[a,s]=y.useState(!1),o=n=>{if(n.stopPropagation(),s(!0),typeof e=="string")navigator.clipboard.writeText(e);else{const i=JSON.stringify(e,null,2);navigator.clipboard.writeText(i)}setTimeout(()=>{s(!1)},2e3)},d={whiteSpace:"nowrap",width:"20px"};return a?t.jsx("span",{style:{...r,...d},children:t.jsx(zr,{className:"text-ui-contrast-fg-primary"})}):t.jsx("span",{style:{...r,...d},onClick:o,children:t.jsx(Ne,{className:"text-ui-contrast-fg-secondary"})})},X=({data:r,href:e="metadata/edit"})=>{const{t:a}=pe();if(!r||!("metadata"in r))return null;const s=r.metadata?Object.keys(r.metadata).length:0;return t.jsxs(me,{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center gap-x-3",children:[t.jsx(z,{level:"h2",children:a("metadata.header")}),t.jsx(ye,{size:"2xsmall",rounded:"full",children:a("metadata.numberOfKeys",{count:s})})]}),t.jsx(Z,{size:"small",variant:"transparent",className:"text-ui-fg-muted hover:text-ui-fg-subtle",asChild:!0,children:t.jsx(Fr,{to:e,children:t.jsx(Y,{})})})]})},dt=({children:r,widgets:e,data:a,hasOutlet:s=!0,showJSON:o,showMetadata:d})=>{const{before:n,after:i}=e,c={data:a};return o&&!a&&(console.warn("`showJSON` is true but no data is provided. To display JSON, provide data prop."),o=!1),d&&!a&&(console.warn("`showMetadata` is true but no data is provided. To display metadata, provide data prop."),d=!1),t.jsxs("div",{className:"flex flex-col gap-y-3",children:[n.map((v,u)=>y.createElement(v,{...c,key:u})),r,i.map((v,u)=>y.createElement(v,{...c,key:u})),d&&t.jsx(X,{data:a}),o&&t.jsx(W,{data:a}),s&&t.jsx(ve,{})]})},rt=({children:r,widgets:e,data:a,showJSON:s=!1,showMetadata:o=!1,hasOutlet:d=!0})=>{const n={data:a},{before:i,after:c,sideBefore:v,sideAfter:u}=e;s&&!a&&(console.warn("`showJSON` is true but no data is provided. To display JSON, provide data prop."),s=!1),o&&!a&&(console.warn("`showMetadata` is true but no data is provided. To display metadata, provide data prop."),o=!1);const p=y.Children.toArray(r);if(p.length!==2)throw new Error("TwoColumnPage expects exactly two children");const[m,f]=p,N=s||o;return t.jsxs("div",{className:"flex w-full flex-col gap-y-3",children:[i.map((g,x)=>y.createElement(g,{...n,key:x})),t.jsxs("div",{className:"flex w-full flex-col items-start gap-x-4 gap-y-3 xl:grid xl:grid-cols-[minmax(0,_1fr)_440px]",children:[t.jsxs("div",{className:"flex w-full min-w-0 flex-col gap-y-3",children:[m,c.map((g,x)=>y.createElement(g,{...n,key:x})),N&&t.jsxs("div",{className:"hidden flex-col gap-y-3 xl:flex",children:[o&&t.jsx(X,{data:a}),s&&t.jsx(W,{data:a})]})]}),t.jsxs("div",{className:"flex w-full flex-col gap-y-3 xl:mt-0",children:[v.map((g,x)=>y.createElement(g,{...n,key:x})),f,u.map((g,x)=>y.createElement(g,{...n,key:x})),N&&t.jsxs("div",{className:"flex flex-col gap-y-3 xl:hidden",children:[o&&t.jsx(X,{data:a}),s&&t.jsx(W,{data:a})]})]})]}),d&&t.jsx(ve,{})]})},at=({children:r,className:e,...a})=>t.jsx("div",{className:ue("flex w-full flex-col gap-y-3",e),...a,children:r}),tt=({children:r,className:e,...a})=>t.jsx("div",{className:ue("flex w-full max-w-[100%] flex-col gap-y-3 xl:mt-0 xl:max-w-[440px]",e),...a,children:r}),ct=Object.assign(rt,{Main:at,Sidebar:tt});export{Y as A,W as J,dt as S,ct as T,Ne as a};
