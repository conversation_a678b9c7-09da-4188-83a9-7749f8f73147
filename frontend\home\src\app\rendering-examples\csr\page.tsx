'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import { ContentWrapper } from '@/components/rendering-examples/ContentWrapper';
import { useTheme } from 'next-themes';
import { useTranslation } from '@/components/Providers/i18n-provider';

export default function ClientSideRenderedPage() {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [data, setData] = useState<Record<string, string> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [_error, _setError] = useState<string | null>(null);

  // This effect will only run in the browser, not during server-side rendering
  useEffect(() => {
    setMounted(true);

    // Simulated data fetching for basic content
    setTimeout(() => {
      setData({
        message: 'Data loaded client-side',
        timestamp: new Date().toISOString(),
      });
      setIsLoading(false);
    }, 1000);
  }, []);

  // Basic CSR content
  const BasicContent = (
    <>
      <h1 className="mb-6 text-3xl font-bold">
        {t('rendering.csrTitle', 'Client-Side Rendering (CSR)')}
      </h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.csrHowWorks', 'How CSR Works')}
        </h2>
        <p className="mb-4">
          {t(
            'rendering.csrDescription',
            'With Client-Side Rendering, the server sends a minimal HTML page, and JavaScript builds the page in the browser. This is visible in how this page starts with minimal content and then "hydrates" with the full UI once JavaScript loads.',
          )}
        </p>
        <p>
          {t(
            'rendering.csrNote',
            'CSR is good for interactive applications but can have SEO challenges and slower initial loads.',
          )}
        </p>
      </div>

      <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.clientData', 'Client-Generated Data')}
        </h2>

        {!mounted ? (
          <div className="text-center">
            <p>{t('rendering.loadingInitial', 'Initializing...')}</p>
          </div>
        ) : isLoading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="bg-primary/20 h-3 w-3 animate-pulse rounded-full"></div>
            <div className="bg-primary/20 h-3 w-3 animate-pulse rounded-full delay-75"></div>
            <div className="bg-primary/20 h-3 w-3 animate-pulse rounded-full delay-150"></div>
            <span className="ml-2">{t('rendering.loading', 'Loading...')}</span>
          </div>
        ) : (
          <>
            <div className="bg-card mb-4 rounded border p-4">
              <p>
                <strong>{t('rendering.message', 'Message')}:</strong> {data?.message}
              </p>
              <p>
                <strong>{t('rendering.timestamp', 'Timestamp')}:</strong> {data?.timestamp}
              </p>
              <p>
                <strong>{t('rendering.currentTheme', 'Current Theme')}:</strong> {theme}
              </p>
            </div>
            <p className="text-sm">
              {t(
                'rendering.csrRefreshNote',
                'Refresh the page to see the loading state again, confirming this content is rendered client-side.',
              )}
            </p>
          </>
        )}
      </div>

      <div className="bg-muted rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.implementation', 'Implementation')}
        </h2>
        <p className="mb-4">
          {t(
            'rendering.csrImplementation',
            "In Next.js, you can use the 'use client' directive at the top of a component file to indicate it should be rendered client-side.",
          )}
        </p>

        <pre className="bg-card overflow-x-auto rounded-lg p-4 text-sm">
          {`'use client';

// This client component will run in the browser
export default function ClientPage() {
  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Fetch data on the client side after the component mounts
  useEffect(() => {
    fetch('/api/data')
      .then(res => res.json())
      .then(data => {
        setData(data);
        setIsLoading(false);
      });
  }, []);
  
  if (isLoading) return <p>Loading...</p>;
  
  return <div>{data.message}</div>;
}`}
        </pre>
      </div>

      <div className="mt-8 flex flex-col gap-4 sm:flex-row">
        <Link
          href="/rendering-examples/csr/with-data"
          className="bg-primary/5 hover:bg-primary/10 border-primary/20 flex-1 rounded-lg border px-4 py-3 text-center font-medium"
        >
          CSR with Detailed Data →
        </Link>
        <Link
          href="/rendering-examples/csr/advanced"
          className="bg-primary/5 hover:bg-primary/10 border-primary/20 flex-1 rounded-lg border px-4 py-3 text-center font-medium"
        >
          Advanced CSR Example →
        </Link>
        <Link
          href="/rendering-examples/csr/router-example"
          className="bg-primary hover:bg-primary/90 flex-1 rounded-lg px-4 py-3 text-center font-medium text-white"
        >
          CSR Router Example →
        </Link>
      </div>
    </>
  );

  // Content for the "With Data" tab
  const WithDataContent = (
    <>
      <h1 className="mb-6 text-3xl font-bold">
        {t('rendering.csrWithData', 'CSR with Detailed Data')}
      </h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.csrWithDataTitle', 'Client-Side Data Fetching')}
        </h2>
        <p className="mb-4">
          {t(
            'rendering.csrWithDataDescription',
            'This example demonstrates fetching data on the client side after the component has mounted. This is common in SPAs and client-rendered applications.',
          )}
        </p>
      </div>
    </>
  );

  // Advanced Content
  const AdvancedContent = (
    <>
      <h1 className="mb-6 text-3xl font-bold">
        {t('rendering.csrAdvanced', 'Advanced CSR Techniques')}
      </h1>

      <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.csrAdvancedTitle', 'Optimizing Client-Side Applications')}
        </h2>
        <p className="mb-4">
          {t(
            'rendering.csrAdvancedDescription',
            'Advanced CSR applications use techniques like code splitting, lazy loading, and client-side caching to improve performance.',
          )}
        </p>
      </div>

      <div className="bg-card mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.csrBestPractices', 'Best Practices')}
        </h2>
        <ul className="list-disc space-y-2 pl-5">
          <li>
            {t(
              'rendering.csrBestPractice1',
              'Use loading states to indicate content is being fetched',
            )}
          </li>
          <li>
            {t(
              'rendering.csrBestPractice2',
              'Implement error boundaries to handle failures gracefully',
            )}
          </li>
          <li>
            {t(
              'rendering.csrBestPractice3',
              'Consider using a service worker for offline capabilities',
            )}
          </li>
          <li>{t('rendering.csrBestPractice4', 'Pre-load critical data where possible')}</li>
        </ul>
      </div>
    </>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="csr" />

      <main className="container mx-auto px-4 pb-12">
        <ContentWrapper
          defaultContent={BasicContent}
          withDataContent={WithDataContent}
          advancedContent={AdvancedContent}
        />

        <div className="mt-12 flex justify-center">
          <Link
            href="/rendering-examples/csr/router-example"
            className="bg-primary hover:bg-primary/90 rounded-md px-6 py-3 text-white transition-colors"
          >
            {t('rendering.viewRouterExample', 'View Router Example with Theme & Language Support')}
          </Link>
        </div>
      </main>
    </div>
  );
}
