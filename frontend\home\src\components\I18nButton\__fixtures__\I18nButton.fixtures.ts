export const languageFixtures = {
  languageCodes: ['en', 'fr', 'ja', 'ar'],
  translations: {
    'en': {
      'language.en': 'English',
      'language.fr': 'French',
      'language.ja': 'Japanese',
      'language.ar': 'Arabic',
      'language.switchLanguage': 'Switch Language'
    },
    'fr': {
      'language.en': 'Anglais',
      'language.fr': 'Français',
      'language.ja': 'Japonais',
      'language.ar': 'Arabe',
      'language.switchLanguage': 'Changer de Langue'
    },
    'ja': {
      'language.en': '英語',
      'language.fr': 'フランス語',
      'language.ja': '日本語',
      'language.ar': 'アラビア語',
      'language.switchLanguage': '言語を切り替える'
    },
    'ar': {
      'language.en': 'الإنجليزية',
      'language.fr': 'الفرنسية',
      'language.ja': 'اليابانية',
      'language.ar': 'العربية',
      'language.switchLanguage': 'تغيير اللغة'
    }
  }
};

export const i18nMock = {
  language: 'en',
  isInitialized: true,
  changeLanguage: jest.fn((lng: string) => {
    i18nMock.language = lng;
    return Promise.resolve();
  }),
  on: jest.fn(),
  off: jest.fn(),
  t: jest.fn((key: string, fallback?: string) => {
    // Return the translation or fallback
    const translations = languageFixtures.translations.en as Record<string, string>;
    return translations[key] || fallback || key;
  })
};

export const storybookEnvironments = {
  standard: {
    window: {
      parent: window,
      location: {
        href: 'https://example.com'
      }
    }
  },
  storybook: {
    window: {
      parent: {}, // Different from window
      location: {
        href: 'https://example.com/?viewMode=story'
      }
    }
  },
  iframe: {
    window: {
      parent: window,
      location: {
        href: 'https://example.com/iframe.html'
      }
    }
  }
}; 