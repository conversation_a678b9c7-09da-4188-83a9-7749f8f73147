import React from 'react';
import { I18nButton } from './i18n-button';
import type { I18nButtonProps } from './types';

/**
 * A wrapper component for Storybook usage
 * Uses our unified language change hook via the I18nButton component
 * Simply passes props through to the unified I18nButton component
 */
export const StorybookI18nButton: React.FC<I18nButtonProps> = (props) => {
  // Simply pass through all props to I18nButton
  // Uses the unified useLanguageChange hook that works in both contexts
  return <I18nButton {...props} />;
}; 