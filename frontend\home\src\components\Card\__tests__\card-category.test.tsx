import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { CardCategory } from '../card-category';
import { fixtures } from '../__fixtures__/card-category.fixtues';

// Mock Next.js components
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} />;
  },
}));

jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ href, children, ...props }: any) => {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    );
  },
}));

describe('CardCategory', () => {
  it('renders correctly with all props', () => {
    render(<CardCategory {...fixtures.wireCable} />);
    
    const title = screen.getByTestId('card-category-title');
    const image = screen.getByTestId('card-category-image');
    const link = screen.getByRole('link');
    
    expect(title).toHaveTextContent(fixtures.wireCable.title);
    expect(image).toHaveAttribute('src', expect.stringContaining(fixtures.wireCable.imageUrl));
    expect(image).toHaveAttribute('alt', fixtures.wireCable.imageAlt as string);
    expect(link).toHaveAttribute('href', fixtures.wireCable.href);
  });
  
  it('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<CardCategory {...fixtures.wireCable} onClick={handleClick} />);
    
    const link = screen.getByRole('link');
    fireEvent.click(link);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('applies custom className correctly', () => {
    const customClass = 'custom-class-name';
    render(<CardCategory {...fixtures.wireCable} className={customClass} />);
    
    const link = screen.getByRole('link');
    expect(link.className).toContain(customClass);
  });
  
  it('renders with custom testId', () => {
    const testId = 'custom-test-id';
    render(<CardCategory {...fixtures.wireCable} testId={testId} />);
    
    expect(screen.getByTestId(testId)).toBeInTheDocument();
    expect(screen.getByTestId(`${testId}-title`)).toBeInTheDocument();
    expect(screen.getByTestId(`${testId}-image`)).toBeInTheDocument();
  });
  
  it('uses title as alt text when imageAlt is not provided', () => {
    const props = { ...fixtures.wireCable, imageAlt: undefined };
    render(<CardCategory {...props} />);
    
    const image = screen.getByTestId('card-category-image');
    expect(image).toHaveAttribute('alt', props.title);
  });

  it('renders different category types correctly', () => {
    const { rerender } = render(<CardCategory {...fixtures.wireCable} />);
    expect(screen.getByTestId('card-category-title')).toHaveTextContent(fixtures.wireCable.title);
    
    rerender(<CardCategory {...fixtures.plugSocket} />);
    expect(screen.getByTestId('card-category-title')).toHaveTextContent(fixtures.plugSocket.title);
    
    rerender(<CardCategory {...fixtures.breaker} />);
    expect(screen.getByTestId('card-category-title')).toHaveTextContent(fixtures.breaker.title);
    
    rerender(<CardCategory {...fixtures.socket} />);
    expect(screen.getByTestId('card-category-title')).toHaveTextContent(fixtures.socket.title);
    
    rerender(<CardCategory {...fixtures.wiringEquipment} />);
    expect(screen.getByTestId('card-category-title')).toHaveTextContent(fixtures.wiringEquipment.title);
    
    rerender(<CardCategory {...fixtures.powerBackup} />);
    expect(screen.getByTestId('card-category-title')).toHaveTextContent(fixtures.powerBackup.title);
  });
});