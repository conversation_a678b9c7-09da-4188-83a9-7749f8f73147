import{M as s}from"./chunk-HE7M25F2-CIT0st-p.js";import{g as n,ed as p,j as r}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import{b as d}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var h=()=>{const{store:t,isPending:a,isError:o,error:e}=n(),{mutateAsync:i,isPending:m}=p(t==null?void 0:t.id);if(o)throw e;return r.jsx(d,{children:r.jsx(s,{isPending:a,isMutating:m,hook:i,metadata:t==null?void 0:t.metadata})})};export{h as Component};
