import React from "react";

/**
 * Sample category items for product filters
 */
export const productCategories = [
  { id: "1", label: "หลอดไฟ" },
  { id: "2", label: "รางน้ำฝน & ถาวนโน้ต" },
  { id: "3", label: "โคมไฟภายใน" },
  { id: "4", label: "โคมไฟ" },
  { id: "5", label: "โคมไฟภายนอก" },
  { id: "6", label: "โคมไฟโซลาร์เซลล์" },
  { id: "7", label: "อุปกรณ์หลอดไฟ" },
  { id: "8", label: "รางปลั๊กและรแดปเตอร์" },
  { id: "9", label: "เบรกเกอร์และตู้ไฟ" },
  { id: "10", label: "ปลั๊กและสวิตช์ไฟฟ้า" },
  { id: "11", label: "ไฟฉายและไฟฉุกเฉิน" },
  { id: "12", label: "เครื่องสำรองไฟฟ้า" },
  { id: "13", label: "สายไฟ" },
  { id: "14", label: "อุปกรณ์เดินสายไฟ" },
  { id: "15", label: "กริ่ง" },
  { id: "16", label: "ท่อร้อยสายไฟและอุปกรณ์" },
  { id: "17", label: "ระบบโซล่าเซลล์" },
  { id: "18", label: "อุปกรณ์ IoT" },
];

/**
 * Sample brand items for filters
 */
export const brandItems = [
  { id: "1", label: "Philips", checked: true },
  { id: "2", label: "EVE", checked: true },
  { id: "3", label: "LAMPTAN", checked: true },
  { id: "4", label: "PANASONIC", checked: false },
  { id: "5", label: "TOSHIBA", checked: false },
  { id: "6", label: "CARINI", checked: false },
  { id: "7", label: "ECOLINK", checked: false },
  { id: "8", label: "LUMAX", checked: false },
];

/**
 * Sample rating options
 */
export const ratingOptions = [
  { id: "5", label: "5 ดาว", value: 5 },
  { id: "4", label: "4 ดาว", value: 4 },
  { id: "3", label: "3 ดาว", value: 3 },
  { id: "2", label: "2 ดาว", value: 2 },
  { id: "1", label: "1 ดาว", value: 1 },
];

/**
 * Sample price range values
 */
export const priceRangeValues = {
  min: 800,
  max: 5500,
  currentMin: 800,
  currentMax: 5500,
};

/**
 * Sample FAQ items
 */
export const faqItems = [
  {
    id: "1",
    title: "วิธีการสั่งซื้อสินค้า",
    content: `Eget nunc dolor hendrerit felis convallis. In mi in nibh urna pulvinar ipsum. Cursus sapien 
    lectus placerat diam nisi aliquam mauris. Quam nisi nunc nunc sapien leo mauris porttitor 
    consectetur ut. Tincidunt sit at vestibulum massa. Nec malesuada sodales vitae aliquet 
    suscipit sed nisl in. Lectus sed aliquet tortor elementum.`
  },
  {
    id: "2", 
    title: "จะรับสินค้าอย่างไรได้บ้าง",
    content: "สามารถเลือกรับสินค้าได้ผ่านการจัดส่งทางไปรษณีย์ หรือ บริษัทขนส่ง หรือรับที่สาขาของบริษัทที่อยู่ใกล้บ้านคุณ"
  },
  {
    id: "3",
    title: "วิธีการชำระเงิน",
    content: "รองรับการชำระเงินผ่านบัตรเครดิต บัตรเดบิต PromptPay QR Payment หรือโอนผ่านธนาคาร"
  },
  {
    id: "4",
    title: "กรณีของหมดจะทำอย่างไร ?",
    content: "ระบบจะแจ้งให้ทราบทันทีหากสินค้าหมด และคุณสามารถกดติดตามรับการแจ้งเตือนเมื่อสินค้ากลับมาในสต็อกได้"
  },
  {
    id: "5",
    title: "ใบเสนอราคาจะส่งไปที่ใด ?",
    content: "ใบเสนอราคาจะถูกส่งไปยังอีเมลที่คุณได้ลงทะเบียนไว้ หรือคุณสามารถดาวน์โหลดได้จากหน้าประวัติการสั่งซื้อในบัญชีของคุณ"
  }
];