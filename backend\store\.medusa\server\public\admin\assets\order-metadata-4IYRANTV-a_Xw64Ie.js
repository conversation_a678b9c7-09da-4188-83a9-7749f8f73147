import{M as m}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as d,aS as n,d5 as p,j as u}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var P=()=>{const{id:a}=d(),{order:t,isPending:r,isError:s,error:i}=n(a,{fields:"id,metadata"}),{mutateAsync:o,isPending:e}=p(t==null?void 0:t.id);if(s)throw i;return u.jsx(m,{metadata:t==null?void 0:t.metadata,hook:o,isPending:r,isMutating:e})};export{P as Component};
