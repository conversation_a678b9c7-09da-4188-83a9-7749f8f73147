import{u as F,a as z,b as B}from"./chunk-ZJRFL6ZN-gHZBUXyR.js";import{a2 as I,ad as D,a4 as H,R as M,j as e,b as j,r as p,dL as L,a8 as N,a9 as O,dC as V,t as S,E as K,B as x,V as $}from"./index-Bwql5Dzz.js";import{u as q,_ as Q}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{K as Z}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as c,u as J}from"./chunk-JGQGO74V-DtHO1ucg.js";import{C as b}from"./checkbox-B4pL6X49.js";import{c as U}from"./index-BxZ1678G.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./prompt-BsR9zKsn.js";var W=I({customer_group_ids:D(H()).min(1)}),g=10,X=({customerId:a})=>{const{t:o}=j(),{handleSuccess:f}=J(),[r,i]=p.useState(!1),{mutateAsync:h}=L(a),u=N({defaultValues:{customer_group_ids:[]},resolver:O(W)}),{setValue:n}=u,[l,_]=p.useState({});p.useEffect(()=>{n("customer_group_ids",Object.keys(l).filter(t=>l[t]),{shouldDirty:!0,shouldTouch:!0})},[l,n]);const{searchParams:v,raw:R}=F({pageSize:g}),T=z(),{customer_groups:d,count:y,isPending:P,isError:k,error:w}=V({fields:"*customers",...v}),A=t=>{const s=typeof t=="function"?t(l):t,m=Object.keys(s);n("customer_group_ids",m,{shouldDirty:!0,shouldTouch:!0}),_(s)},C=ee(),{table:G}=q({data:d??[],columns:C,count:y,enablePagination:!0,enableRowSelection:t=>{var s;return!((s=t.original.customers)!=null&&s.map(m=>m.id).includes(a))},getRowId:t=>t.id,pageSize:g,rowSelection:{state:l,updater:A}}),E=u.handleSubmit(async t=>{i(!0);try{await h({add:t.customer_group_ids}),S.success(o("customers.groups.add.success",{groups:t.customer_group_ids.map(s=>d==null?void 0:d.find(m=>m.id===s)).filter(Boolean).map(s=>s==null?void 0:s.name)})),f(`/customers/${a}`)}catch(s){S.error(s.message)}finally{i(!1)}});if(k)throw w;return e.jsx(c.Form,{form:u,children:e.jsxs(Z,{className:"flex h-full flex-col overflow-hidden",onSubmit:E,children:[e.jsx(c.Header,{children:e.jsx("div",{className:"flex items-center justify-end gap-x-2",children:u.formState.errors.customer_group_ids&&e.jsx(K,{variant:"error",children:u.formState.errors.customer_group_ids.message})})}),e.jsx(c.Body,{className:"size-full overflow-hidden",children:e.jsx(Q,{table:G,columns:C,pageSize:g,count:y,filters:T,orderBy:[{key:"name",label:o("fields.name")},{key:"created_at",label:o("fields.createdAt")},{key:"updated_at",label:o("fields.updatedAt")}],isLoading:P,layout:"fill",search:"autofocus",queryObject:R,noRecords:{message:o("customers.groups.add.list.noRecordsMessage")}})}),e.jsxs(c.Footer,{children:[e.jsx(c.Close,{asChild:!0,children:e.jsx(x,{variant:"secondary",size:"small",children:o("actions.cancel")})}),e.jsx(x,{type:"submit",variant:"primary",size:"small",isLoading:r,children:o("actions.save")})]})]})})},Y=U(),ee=()=>{const{t:a}=j(),o=B();return p.useMemo(()=>[Y.display({id:"select",header:({table:r})=>e.jsx(b,{checked:r.getIsSomePageRowsSelected()?"indeterminate":r.getIsAllPageRowsSelected(),onCheckedChange:i=>r.toggleAllPageRowsSelected(!!i)}),cell:({row:r})=>{const i=!r.getCanSelect(),h=r.getIsSelected()||i,u=e.jsx(b,{checked:h,disabled:i,onCheckedChange:n=>r.toggleSelected(!!n),onClick:n=>{n.stopPropagation()}});return i?e.jsx($,{content:a("customers.groups.alreadyAddedTooltip"),side:"right",children:u}):u}}),...o],[a,o])},Ae=()=>{const{id:a}=M();return e.jsx(c,{children:e.jsx(X,{customerId:a})})};export{Ae as Component};
