// components/Card/CardBandStats/card-band-stats.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { CardBandStats } from '../card-band-stats';
import { 
  baseFixture, 
  orangeFixture, 
  blueFixture, 
  compactFixture, 
  compactOrangeFixture, 
  compactBlueFixture,
  heroFixture,
  heroOrangeFixture,
  heroBlueFixture,
  interactiveFixture,
  defaultGridFixtures,
  compactGridFixtures,
  heroGridFixtures
} from '../__fixtures__/card-band-stats.fixtures';

const meta: Meta<typeof CardBandStats> = {
  title: 'UI/Card/CardBandStats',
  component: CardBandStats,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A card component for displaying statistics with an icon, value and description.',
      },
    },
    a11y: {
      config: {
        // Custom a11y rules
      },
    },
  },
  argTypes: {
    value: {
      control: 'text',
      description: 'The main value to display',
      table: {
        type: { summary: 'string | number' },
      },
    },
    description: {
      control: 'text',
      description: 'The descriptive text displayed below the value',
      table: {
        type: { summary: 'string' },
      },
    },
    colorScheme: {
      control: 'radio',
      options: ['white', 'orange', 'blue'],
      description: 'The color scheme of the card',
      table: {
        type: { summary: 'white | orange | blue' },
        defaultValue: { summary: 'white' },
      },
    },
    variant: {
      control: 'radio',
      options: ['default', 'compact', 'hero'],
      description: 'Size variant of the card',
      table: {
        type: { summary: 'default | compact' },
        defaultValue: { summary: 'default' },
      },
    },
    onClick: {
      action: 'clicked',
      description: 'Function called when the card is clicked',
      table: {
        type: { summary: '() => void' },
      },
    },
    icon: {
      description: 'Icon displayed at the top of the card',
      table: {
        type: { summary: 'React.ReactNode' },
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof CardBandStats>;

// Default white card
export const Default: Story = {
  args: baseFixture
};

// Orange card
export const Orange: Story = {
  args: orangeFixture
};

// Blue card
export const Blue: Story = {
  args: blueFixture
};

// Compact white card
export const CompactWhite: Story = {
  args: compactFixture
};

// Compact orange card
export const CompactOrange: Story = {
  args: compactOrangeFixture
};

// Compact blue card
export const CompactBlue: Story = {
  args: compactBlueFixture
};

// Interactive card
export const Interactive: Story = {
  args: interactiveFixture
};

// Grid of default cards
export const DefaultCardGrid: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 max-w-3xl">
      {defaultGridFixtures.map((props, index) => (
        <CardBandStats key={index} {...props} />
      ))}
    </div>
  ),
};

// Grid of compact cards
export const CompactCardGrid: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-4 max-w-3xl">
      {compactGridFixtures.map((props, index) => (
        <CardBandStats key={index} {...props} />
      ))}
    </div>
  ),
};

// Hero white card
export const HeroWhite: Story = {
  args: heroFixture
};

// Hero orange card
export const HeroOrange: Story = {
  args: heroOrangeFixture
};

// Hero blue card
export const HeroBlue: Story = {
  args: heroBlueFixture
};

// Grid of hero cards
export const HeroCardGrid: Story = {
  render: () => (
    <div className="grid grid-cols-3 gap-6 max-w-5xl">
      {heroGridFixtures.map((props, index) => (
        <CardBandStats key={index} {...props} />
      ))}
    </div>
  ),
};