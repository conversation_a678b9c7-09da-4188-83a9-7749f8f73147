'use client';

import React, { useEffect, useState } from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';

export function Hero() {
  const { t, i18n } = useTranslation();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Only show translations after i18n has loaded and hydration is complete
    const handleLoaded = () => {
      setIsLoaded(true);
    };

    if (i18n.isInitialized) {
      handleLoaded();
    } else {
      i18n.on('initialized', handleLoaded);
    }

    return () => {
      i18n.off('initialized', handleLoaded);
    };
  }, [i18n]);

  // Default content for SSR and initial client render
  const title = isLoaded ? t('app.title') : 'Timeline';
  const description = isLoaded
    ? t('app.description')
    : 'A customizable timeline component for displaying chronological events.';

  return (
    <div className="space-y-2">
      <h1 className="scroll-m-20 text-4xl font-bold tracking-tight">{title}</h1>
      <p className="text-muted-foreground text-lg">{description}</p>
    </div>
  );
}
