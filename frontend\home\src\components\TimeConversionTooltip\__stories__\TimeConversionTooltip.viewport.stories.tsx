import type { Meta, StoryObj } from '@storybook/react';
import { TimeConversionTooltip } from '../TimeConversionTooltip';
import {
  defaultTooltipData,
  tooltipDataVariants,
} from '../__fixtures__/TimeConversionTooltip.fixtures';

/**
 * Viewport testing for TimeConversionTooltip component
 */
const meta = {
  title: 'Components/TimeConversionTooltip/Viewport',
  component: TimeConversionTooltip,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'TimeConversionTooltip component behavior across different viewport sizes.',
      },
    },
    viewport: {
      viewports: {
        mobile: { name: 'Mobile', styles: { width: '375px', height: '667px' } },
        tablet: { name: 'Tablet', styles: { width: '768px', height: '1024px' } },
        desktop: { name: 'Desktop', styles: { width: '1440px', height: '900px' } },
      },
      defaultViewport: 'desktop',
    },
    chromatic: {
      viewports: [375, 768, 1440],
      delay: 300,
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof TimeConversionTooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Responsive layout example
 */
export const ResponsiveLayout: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Viewport testing',
  },
  render: () => (
    <div className="flex h-screen flex-col items-center justify-center gap-4 p-4 md:flex-row">
      <div className="bg-card w-full rounded-lg p-4 shadow md:w-1/3">
        <h2 className="mb-2 text-lg font-semibold">Event Details</h2>
        <p className="mb-4">The next event is scheduled for:</p>
        <div className="bg-muted rounded p-3">
          <TimeConversionTooltip data={defaultTooltipData}>
            March 15, 2023 - 4:30 PM
          </TimeConversionTooltip>
        </div>
      </div>
    </div>
  ),
};

/**
 * Mobile view example
 */
export const MobileView: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Mobile view',
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
  render: () => (
    <div className="p-4">
      <div className="bg-card rounded-lg p-4 shadow">
        <h2 className="mb-2 text-lg font-semibold">Event Information</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span>Date:</span>
            <TimeConversionTooltip data={defaultTooltipData}>Mobile view</TimeConversionTooltip>
          </div>
          <div className="flex items-center justify-between">
            <span>Location:</span>
            <span>Conference Room A</span>
          </div>
        </div>
      </div>
    </div>
  ),
};

/**
 * Tablet view example
 */
export const TabletView: Story = {
  args: {
    data: tooltipDataVariants[1],
    children: 'Tablet view',
  },
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
  render: () => (
    <div className="p-6">
      <div className="grid grid-cols-2 gap-6">
        <div className="bg-card rounded-lg p-4 shadow">
          <h2 className="mb-2 text-lg font-semibold">Event 1</h2>
          <div className="space-y-3">
            <div>
              <span className="text-muted-foreground block text-sm">Date & Time:</span>
              <TimeConversionTooltip data={tooltipDataVariants[1]}>
                Tablet view
              </TimeConversionTooltip>
            </div>
          </div>
        </div>
        <div className="bg-card rounded-lg p-4 shadow">
          <h2 className="mb-2 text-lg font-semibold">Event 2</h2>
          <div className="space-y-3">
            <div>
              <span className="text-muted-foreground block text-sm">Date & Time:</span>
              <TimeConversionTooltip data={tooltipDataVariants[2]}>
                September 5, 2023 - 5:15 PM
              </TimeConversionTooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
};

/**
 * Desktop view example
 */
export const DesktopView: Story = {
  args: {
    data: tooltipDataVariants[3],
    children: 'Desktop view',
  },
  parameters: {
    viewport: {
      defaultViewport: 'desktop',
    },
  },
  render: () => (
    <div className="p-8">
      <div className="mx-auto max-w-6xl">
        <h1 className="mb-6 text-2xl font-bold">Calendar Events</h1>
        <div className="grid grid-cols-3 gap-8">
          {tooltipDataVariants.map((data, i) => (
            <div key={i} className="bg-card rounded-lg p-4 shadow">
              <h2 className="mb-2 text-lg font-semibold">Event {i + 1}</h2>
              <div className="space-y-3">
                <div>
                  <span className="text-muted-foreground block text-sm">Date & Time:</span>
                  <TimeConversionTooltip data={data}>
                    {`${data.date} - ${data.localTime}`}
                  </TimeConversionTooltip>
                </div>
                <div>
                  <span className="text-muted-foreground block text-sm">Timezone:</span>
                  <span>{data.timezone}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  ),
};
