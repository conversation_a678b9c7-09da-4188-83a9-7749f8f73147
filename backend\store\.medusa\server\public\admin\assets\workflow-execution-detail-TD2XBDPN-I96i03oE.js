import{b as Ee,w as Ve,a as We,g as qe,S as Se,c as _e,d as Ae,e as Ne,f as Fe}from"./chunk-RPAL6FHW-ExF45-x3.js";import{S as Ye,J as Ke}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{r as v,eK as Xe,m as R,eL as le,eM as Je,eN as Qe,eO as et,eP as Te,eQ as fe,eR as tt,j as r,q as at,R as nt,a as rt,dv as st,s as ot,b as U,H as ie,T,Y as ye,c as lt,b7 as it,b8 as ct,I as ut,b4 as pt,b9 as dt,aJ as gt,au as ft,bu as q,cL as yt,L as bt}from"./index-Bwql5Dzz.js";import{c as je}from"./clsx-B-dksMZM.js";import{C as Le}from"./copy-L2SdU4rs.js";import{P as mt}from"./plus-mini-C5sDHkH8.js";import{C as ce}from"./container-Dqi2woPF.js";import{S as ht}from"./status-badge-B-sIb9s0.js";import{f as xt}from"./format-Cpg7FCX8.js";import"./Trans-VWqfqpAH.js";import"./x-mark-mini-DvSTI7zK.js";import"./check-BGSYwiWc.js";var vt=Object.defineProperty,K=Object.getOwnPropertySymbols,Re=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable,be=(e,t,a)=>t in e?vt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,wt=(e,t)=>{for(var a in t)Re.call(t,a)&&be(e,a,t[a]);if(K)for(var a of K(t))Ce.call(t,a)&&be(e,a,t[a]);return e},kt=(e,t)=>{var a={};for(var n in e)Re.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&K)for(var n of K(e))t.indexOf(n)<0&&Ce.call(e,n)&&(a[n]=e[n]);return a};const Oe=v.forwardRef((e,t)=>{var a=e,{color:n="currentColor"}=a,i=kt(a,["color"]);return v.createElement("svg",wt({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},i),v.createElement("g",{stroke:n,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},v.createElement("path",{d:"M4.752 9.616 1.935 8.86l-.755 2.817"}),v.createElement("path",{d:"M13.136 8.53a5.729 5.729 0 0 1-11.196.357M10.248 5.384l2.817.755.755-2.817"}),v.createElement("path",{d:"M1.864 6.469a5.729 5.729 0 0 1 11.184-.403"})),v.createElement("defs",null,v.createElement("clipPath",{id:"a"},v.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});Oe.displayName="ArrowPathMini";var Et=Object.create,J=Object.defineProperty,St=Object.defineProperties,_t=Object.getOwnPropertyDescriptor,At=Object.getOwnPropertyDescriptors,Ie=Object.getOwnPropertyNames,X=Object.getOwnPropertySymbols,Nt=Object.getPrototypeOf,ue=Object.prototype.hasOwnProperty,De=Object.prototype.propertyIsEnumerable,me=(e,t,a)=>t in e?J(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,D=(e,t)=>{for(var a in t||(t={}))ue.call(t,a)&&me(e,a,t[a]);if(X)for(var a of X(t))De.call(t,a)&&me(e,a,t[a]);return e},Q=(e,t)=>St(e,At(t)),Be=(e,t)=>{var a={};for(var n in e)ue.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&X)for(var n of X(e))t.indexOf(n)<0&&De.call(e,n)&&(a[n]=e[n]);return a},Ft=(e,t)=>function(){return t||(0,e[Ie(e)[0]])((t={exports:{}}).exports,t),t.exports},Tt=(e,t)=>{for(var a in t)J(e,a,{get:t[a],enumerable:!0})},jt=(e,t,a,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Ie(t))!ue.call(e,i)&&i!==a&&J(e,i,{get:()=>t[i],enumerable:!(n=_t(t,i))||n.enumerable});return e},Lt=(e,t,a)=>(a=e!=null?Et(Nt(e)):{},jt(!e||!e.__esModule?J(a,"default",{value:e,enumerable:!0}):a,e)),Rt=Ft({"../../node_modules/.pnpm/prismjs@1.29.0_patch_hash=vrxx3pzkik6jpmgpayxfjunetu/node_modules/prismjs/prism.js"(e,t){var a=function(){var n=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,i=0,b={},d={util:{encode:function s(o){return o instanceof h?new h(o.type,s(o.content),o.alias):Array.isArray(o)?o.map(s):o.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(s){return Object.prototype.toString.call(s).slice(8,-1)},objId:function(s){return s.__id||Object.defineProperty(s,"__id",{value:++i}),s.__id},clone:function s(o,u){u=u||{};var m,g;switch(d.util.type(o)){case"Object":if(g=d.util.objId(o),u[g])return u[g];m={},u[g]=m;for(var x in o)o.hasOwnProperty(x)&&(m[x]=s(o[x],u));return m;case"Array":return g=d.util.objId(o),u[g]?u[g]:(m=[],u[g]=m,o.forEach(function(w,E){m[E]=s(w,u)}),m);default:return o}},getLanguage:function(s){for(;s;){var o=n.exec(s.className);if(o)return o[1].toLowerCase();s=s.parentElement}return"none"},setLanguage:function(s,o){s.className=s.className.replace(RegExp(n,"gi"),""),s.classList.add("language-"+o)},isActive:function(s,o,u){for(var m="no-"+o;s;){var g=s.classList;if(g.contains(o))return!0;if(g.contains(m))return!1;s=s.parentElement}return!!u}},languages:{plain:b,plaintext:b,text:b,txt:b,extend:function(s,o){var u=d.util.clone(d.languages[s]);for(var m in o)u[m]=o[m];return u},insertBefore:function(s,o,u,m){m=m||d.languages;var g=m[s],x={};for(var w in g)if(g.hasOwnProperty(w)){if(w==o)for(var E in u)u.hasOwnProperty(E)&&(x[E]=u[E]);u.hasOwnProperty(w)||(x[w]=g[w])}var A=m[s];return m[s]=x,d.languages.DFS(d.languages,function(_,N){N===A&&_!=s&&(this[_]=x)}),x},DFS:function s(o,u,m,g){g=g||{};var x=d.util.objId;for(var w in o)if(o.hasOwnProperty(w)){u.call(o,w,o[w],m||w);var E=o[w],A=d.util.type(E);A==="Object"&&!g[x(E)]?(g[x(E)]=!0,s(E,u,null,g)):A==="Array"&&!g[x(E)]&&(g[x(E)]=!0,s(E,u,w,g))}}},plugins:{},highlight:function(s,o,u){var m={code:s,grammar:o,language:u};if(d.hooks.run("before-tokenize",m),!m.grammar)throw new Error('The language "'+m.language+'" has no grammar.');return m.tokens=d.tokenize(m.code,m.grammar),d.hooks.run("after-tokenize",m),h.stringify(d.util.encode(m.tokens),m.language)},tokenize:function(s,o){var u=o.rest;if(u){for(var m in u)o[m]=u[m];delete o.rest}var g=new f;return y(g,g.head,s),p(s,g,o,g.head,0),S(g)},hooks:{all:{},add:function(s,o){var u=d.hooks.all;u[s]=u[s]||[],u[s].push(o)},run:function(s,o){var u=d.hooks.all[s];if(!(!u||!u.length))for(var m=0,g;g=u[m++];)g(o)}},Token:h};function h(s,o,u,m){this.type=s,this.content=o,this.alias=u,this.length=(m||"").length|0}h.stringify=function s(o,u){if(typeof o=="string")return o;if(Array.isArray(o)){var m="";return o.forEach(function(A){m+=s(A,u)}),m}var g={type:o.type,content:s(o.content,u),tag:"span",classes:["token",o.type],attributes:{},language:u},x=o.alias;x&&(Array.isArray(x)?Array.prototype.push.apply(g.classes,x):g.classes.push(x)),d.hooks.run("wrap",g);var w="";for(var E in g.attributes)w+=" "+E+'="'+(g.attributes[E]||"").replace(/"/g,"&quot;")+'"';return"<"+g.tag+' class="'+g.classes.join(" ")+'"'+w+">"+g.content+"</"+g.tag+">"};function c(s,o,u,m){s.lastIndex=o;var g=s.exec(u);if(g&&m&&g[1]){var x=g[1].length;g.index+=x,g[0]=g[0].slice(x)}return g}function p(s,o,u,m,g,x){for(var w in u)if(!(!u.hasOwnProperty(w)||!u[w])){var E=u[w];E=Array.isArray(E)?E:[E];for(var A=0;A<E.length;++A){if(x&&x.cause==w+","+A)return;var _=E[A],N=_.inside,j=!!_.lookbehind,P=!!_.greedy,B=_.alias;if(P&&!_.pattern.global){var O=_.pattern.toString().match(/[imsuy]*$/)[0];_.pattern=RegExp(_.pattern.source,O+"g")}for(var de=_.pattern||_,F=m.next,I=g;F!==o.tail&&!(x&&I>=x.reach);I+=F.value.length,F=F.next){var M=F.value;if(o.length>s.length)return;if(!(M instanceof h)){var Z=1,C;if(P){if(C=c(de,I,s,j),!C||C.index>=s.length)break;var H=C.index,Ze=C.index+C[0].length,$=I;for($+=F.value.length;H>=$;)F=F.next,$+=F.value.length;if($-=F.value.length,I=$,F.value instanceof h)continue;for(var G=F;G!==o.tail&&($<Ze||typeof G.value=="string");G=G.next)Z++,$+=G.value.length;Z--,M=s.slice(I,$),C.index-=I}else if(C=c(de,0,M,j),!C)continue;var H=C.index,V=C[0],ee=M.slice(0,H),ge=M.slice(H+V.length),te=I+M.length;x&&te>x.reach&&(x.reach=te);var W=F.prev;ee&&(W=y(o,W,ee),I+=ee.length),k(o,W,Z);var He=new h(w,N?d.tokenize(V,N):V,B,V);if(F=y(o,W,He),ge&&y(o,F,ge),Z>1){var ae={cause:w+","+A,reach:te};p(s,o,u,F.prev,I,ae),x&&ae.reach>x.reach&&(x.reach=ae.reach)}}}}}}function f(){var s={value:null,prev:null,next:null},o={value:null,prev:s,next:null};s.next=o,this.head=s,this.tail=o,this.length=0}function y(s,o,u){var m=o.next,g={value:u,prev:o,next:m};return o.next=g,m.prev=g,s.length++,g}function k(s,o,u){for(var m=o.next,g=0;g<u&&m!==s.tail;g++)m=m.next;o.next=m,m.prev=o,s.length-=g}function S(s){for(var o=[],u=s.head.next;u!==s.tail;)o.push(u.value),u=u.next;return o}return d}();t.exports=a,a.default=a}}),l=Lt(Rt());l.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},l.languages.markup.tag.inside["attr-value"].inside.entity=l.languages.markup.entity,l.languages.markup.doctype.inside["internal-subset"].inside=l.languages.markup,l.hooks.add("wrap",function(e){e.type==="entity"&&(e.attributes.title=e.content.replace(/&amp;/,"&"))}),Object.defineProperty(l.languages.markup.tag,"addInlined",{value:function(e,n){var a={},a=(a["language-"+n]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:l.languages[n]},a.cdata=/^<!\[CDATA\[|\]\]>$/i,{"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:a}}),n=(a["language-"+n]={pattern:/[\s\S]+/,inside:l.languages[n]},{});n[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return e}),"i"),lookbehind:!0,greedy:!0,inside:a},l.languages.insertBefore("markup","cdata",n)}}),Object.defineProperty(l.languages.markup.tag,"addAttribute",{value:function(e,t){l.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+e+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[t,"language-"+t],inside:l.languages[t]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),l.languages.html=l.languages.markup,l.languages.mathml=l.languages.markup,l.languages.svg=l.languages.markup,l.languages.xml=l.languages.extend("markup",{}),l.languages.ssml=l.languages.xml,l.languages.atom=l.languages.xml,l.languages.rss=l.languages.xml,function(e){var t={pattern:/\\[\\(){}[\]^$+*?|.]/,alias:"escape"},a=/\\(?:x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]+\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/,n="(?:[^\\\\-]|"+a.source+")",n=RegExp(n+"-"+n),i={pattern:/(<|')[^<>']+(?=[>']$)/,lookbehind:!0,alias:"variable"};e.languages.regex={"char-class":{pattern:/((?:^|[^\\])(?:\\\\)*)\[(?:[^\\\]]|\\[\s\S])*\]/,lookbehind:!0,inside:{"char-class-negation":{pattern:/(^\[)\^/,lookbehind:!0,alias:"operator"},"char-class-punctuation":{pattern:/^\[|\]$/,alias:"punctuation"},range:{pattern:n,inside:{escape:a,"range-punctuation":{pattern:/-/,alias:"operator"}}},"special-escape":t,"char-set":{pattern:/\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},escape:a}},"special-escape":t,"char-set":{pattern:/\.|\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},backreference:[{pattern:/\\(?![123][0-7]{2})[1-9]/,alias:"keyword"},{pattern:/\\k<[^<>']+>/,alias:"keyword",inside:{"group-name":i}}],anchor:{pattern:/[$^]|\\[ABbGZz]/,alias:"function"},escape:a,group:[{pattern:/\((?:\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,alias:"punctuation",inside:{"group-name":i}},{pattern:/\)/,alias:"punctuation"}],quantifier:{pattern:/(?:[+*?]|\{\d+(?:,\d*)?\})[?+]?/,alias:"number"},alternation:{pattern:/\|/,alias:"keyword"}}}(l),l.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},l.languages.javascript=l.languages.extend("clike",{"class-name":[l.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),l.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,l.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:l.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:l.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:l.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:l.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:l.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),l.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:l.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),l.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),l.languages.markup&&(l.languages.markup.tag.addInlined("script","javascript"),l.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),l.languages.js=l.languages.javascript,l.languages.actionscript=l.languages.extend("javascript",{keyword:/\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\b/,operator:/\+\+|--|(?:[+\-*\/%^]|&&?|\|\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/}),l.languages.actionscript["class-name"].alias="function",delete l.languages.actionscript.parameter,delete l.languages.actionscript["literal-property"],l.languages.markup&&l.languages.insertBefore("actionscript","string",{xml:{pattern:/(^|[^.])<\/?\w+(?:\s+[^\s>\/=]+=("|')(?:\\[\s\S]|(?!\2)[^\\])*\2)*\s*\/?>/,lookbehind:!0,inside:l.languages.markup}}),function(e){var t=/#(?!\{).+/,a={pattern:/#\{[^}]+\}/,alias:"variable"};e.languages.coffeescript=e.languages.extend("javascript",{comment:t,string:[{pattern:/'(?:\\[\s\S]|[^\\'])*'/,greedy:!0},{pattern:/"(?:\\[\s\S]|[^\\"])*"/,greedy:!0,inside:{interpolation:a}}],keyword:/\b(?:and|break|by|catch|class|continue|debugger|delete|do|each|else|extend|extends|false|finally|for|if|in|instanceof|is|isnt|let|loop|namespace|new|no|not|null|of|off|on|or|own|return|super|switch|then|this|throw|true|try|typeof|undefined|unless|until|when|while|window|with|yes|yield)\b/,"class-member":{pattern:/@(?!\d)\w+/,alias:"variable"}}),e.languages.insertBefore("coffeescript","comment",{"multiline-comment":{pattern:/###[\s\S]+?###/,alias:"comment"},"block-regex":{pattern:/\/{3}[\s\S]*?\/{3}/,alias:"regex",inside:{comment:t,interpolation:a}}}),e.languages.insertBefore("coffeescript","string",{"inline-javascript":{pattern:/`(?:\\[\s\S]|[^\\`])*`/,inside:{delimiter:{pattern:/^`|`$/,alias:"punctuation"},script:{pattern:/[\s\S]+/,alias:"language-javascript",inside:e.languages.javascript}}},"multiline-string":[{pattern:/'''[\s\S]*?'''/,greedy:!0,alias:"string"},{pattern:/"""[\s\S]*?"""/,greedy:!0,alias:"string",inside:{interpolation:a}}]}),e.languages.insertBefore("coffeescript","keyword",{property:/(?!\d)\w+(?=\s*:(?!:))/}),delete e.languages.coffeescript["template-string"],e.languages.coffee=e.languages.coffeescript}(l),function(e){var t=e.languages.javadoclike={parameter:{pattern:/(^[\t ]*(?:\/{3}|\*|\/\*\*)\s*@(?:arg|arguments|param)\s+)\w+/m,lookbehind:!0},keyword:{pattern:/(^[\t ]*(?:\/{3}|\*|\/\*\*)\s*|\{)@[a-z][a-zA-Z-]+\b/m,lookbehind:!0},punctuation:/[{}]/};Object.defineProperty(t,"addSupport",{value:function(a,n){(a=typeof a=="string"?[a]:a).forEach(function(i){var b=function(y){y.inside||(y.inside={}),y.inside.rest=n},d="doc-comment";if(h=e.languages[i]){var h,c=h[d];if((c=c||(h=e.languages.insertBefore(i,"comment",{"doc-comment":{pattern:/(^|[^\\])\/\*\*[^/][\s\S]*?(?:\*\/|$)/,lookbehind:!0,alias:"comment"}}))[d])instanceof RegExp&&(c=h[d]={pattern:c}),Array.isArray(c))for(var p=0,f=c.length;p<f;p++)c[p]instanceof RegExp&&(c[p]={pattern:c[p]}),b(c[p]);else b(c)}})}}),t.addSupport(["java","javascript","php"],t)}(l),function(e){var t=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/,t=(e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+t.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+t.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+t.source+"$"),alias:"url"}}},selector:{pattern:RegExp(`(^|[{}\\s])[^{}\\s](?:[^{};"'\\s]|\\s+(?![\\s{])|`+t.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:t,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css,e.languages.markup);t&&(t.tag.addInlined("style","css"),t.tag.addAttribute("style","css"))}(l),function(e){var t=/("|')(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,t=(e.languages.css.selector={pattern:e.languages.css.selector.pattern,lookbehind:!0,inside:t={"pseudo-element":/:(?:after|before|first-letter|first-line|selection)|::[-\w]+/,"pseudo-class":/:[-\w]+/,class:/\.[-\w]+/,id:/#[-\w]+/,attribute:{pattern:RegExp(`\\[(?:[^[\\]"']|`+t.source+")*\\]"),greedy:!0,inside:{punctuation:/^\[|\]$/,"case-sensitivity":{pattern:/(\s)[si]$/i,lookbehind:!0,alias:"keyword"},namespace:{pattern:/^(\s*)(?:(?!\s)[-*\w\xA0-\uFFFF])*\|(?!=)/,lookbehind:!0,inside:{punctuation:/\|$/}},"attr-name":{pattern:/^(\s*)(?:(?!\s)[-\w\xA0-\uFFFF])+/,lookbehind:!0},"attr-value":[t,{pattern:/(=\s*)(?:(?!\s)[-\w\xA0-\uFFFF])+(?=\s*$)/,lookbehind:!0}],operator:/[|~*^$]?=/}},"n-th":[{pattern:/(\(\s*)[+-]?\d*[\dn](?:\s*[+-]\s*\d+)?(?=\s*\))/,lookbehind:!0,inside:{number:/[\dn]+/,operator:/[+-]/}},{pattern:/(\(\s*)(?:even|odd)(?=\s*\))/i,lookbehind:!0}],combinator:/>|\+|~|\|\|/,punctuation:/[(),]/}},e.languages.css.atrule.inside["selector-function-argument"].inside=t,e.languages.insertBefore("css","property",{variable:{pattern:/(^|[^-\w\xA0-\uFFFF])--(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*/i,lookbehind:!0}}),{pattern:/(\b\d+)(?:%|[a-z]+(?![\w-]))/,lookbehind:!0}),a={pattern:/(^|[^\w.-])-?(?:\d+(?:\.\d+)?|\.\d+)/,lookbehind:!0};e.languages.insertBefore("css","function",{operator:{pattern:/(\s)[+\-*\/](?=\s)/,lookbehind:!0},hexcode:{pattern:/\B#[\da-f]{3,8}\b/i,alias:"color"},color:[{pattern:/(^|[^\w-])(?:AliceBlue|AntiqueWhite|Aqua|Aquamarine|Azure|Beige|Bisque|Black|BlanchedAlmond|Blue|BlueViolet|Brown|BurlyWood|CadetBlue|Chartreuse|Chocolate|Coral|CornflowerBlue|Cornsilk|Crimson|Cyan|DarkBlue|DarkCyan|DarkGoldenRod|DarkGr[ae]y|DarkGreen|DarkKhaki|DarkMagenta|DarkOliveGreen|DarkOrange|DarkOrchid|DarkRed|DarkSalmon|DarkSeaGreen|DarkSlateBlue|DarkSlateGr[ae]y|DarkTurquoise|DarkViolet|DeepPink|DeepSkyBlue|DimGr[ae]y|DodgerBlue|FireBrick|FloralWhite|ForestGreen|Fuchsia|Gainsboro|GhostWhite|Gold|GoldenRod|Gr[ae]y|Green|GreenYellow|HoneyDew|HotPink|IndianRed|Indigo|Ivory|Khaki|Lavender|LavenderBlush|LawnGreen|LemonChiffon|LightBlue|LightCoral|LightCyan|LightGoldenRodYellow|LightGr[ae]y|LightGreen|LightPink|LightSalmon|LightSeaGreen|LightSkyBlue|LightSlateGr[ae]y|LightSteelBlue|LightYellow|Lime|LimeGreen|Linen|Magenta|Maroon|MediumAquaMarine|MediumBlue|MediumOrchid|MediumPurple|MediumSeaGreen|MediumSlateBlue|MediumSpringGreen|MediumTurquoise|MediumVioletRed|MidnightBlue|MintCream|MistyRose|Moccasin|NavajoWhite|Navy|OldLace|Olive|OliveDrab|Orange|OrangeRed|Orchid|PaleGoldenRod|PaleGreen|PaleTurquoise|PaleVioletRed|PapayaWhip|PeachPuff|Peru|Pink|Plum|PowderBlue|Purple|RebeccaPurple|Red|RosyBrown|RoyalBlue|SaddleBrown|Salmon|SandyBrown|SeaGreen|SeaShell|Sienna|Silver|SkyBlue|SlateBlue|SlateGr[ae]y|Snow|SpringGreen|SteelBlue|Tan|Teal|Thistle|Tomato|Transparent|Turquoise|Violet|Wheat|White|WhiteSmoke|Yellow|YellowGreen)(?![\w-])/i,lookbehind:!0},{pattern:/\b(?:hsl|rgb)\(\s*\d{1,3}\s*,\s*\d{1,3}%?\s*,\s*\d{1,3}%?\s*\)\B|\b(?:hsl|rgb)a\(\s*\d{1,3}\s*,\s*\d{1,3}%?\s*,\s*\d{1,3}%?\s*,\s*(?:0|0?\.\d+|1)\s*\)\B/i,inside:{unit:t,number:a,function:/[\w-]+(?=\()/,punctuation:/[(),]/}}],entity:/\\[\da-f]{1,8}/i,unit:t,number:a})}(l),function(e){var t=/[*&][^\s[\]{},]+/,a=/!(?:<[\w\-%#;/?:@&=+$,.!~*'()[\]]+>|(?:[a-zA-Z\d-]*!)?[\w\-%#;/?:@&=+$.~*'()]+)?/,n="(?:"+a.source+"(?:[ 	]+"+t.source+")?|"+t.source+"(?:[ 	]+"+a.source+")?)",i=/(?:[^\s\x00-\x08\x0e-\x1f!"#%&'*,\-:>?@[\]`{|}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]|[?:-]<PLAIN>)(?:[ \t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g,function(){return/[^\s\x00-\x08\x0e-\x1f,[\]{}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]/.source}),b=/"(?:[^"\\\r\n]|\\.)*"|'(?:[^'\\\r\n]|\\.)*'/.source;function d(h,c){c=(c||"").replace(/m/g,"")+"m";var p=/([:\-,[{]\s*(?:\s<<prop>>[ \t]+)?)(?:<<value>>)(?=[ \t]*(?:$|,|\]|\}|(?:[\r\n]\s*)?#))/.source.replace(/<<prop>>/g,function(){return n}).replace(/<<value>>/g,function(){return h});return RegExp(p,c)}e.languages.yaml={scalar:{pattern:RegExp(/([\-:]\s*(?:\s<<prop>>[ \t]+)?[|>])[ \t]*(?:((?:\r?\n|\r)[ \t]+)\S[^\r\n]*(?:\2[^\r\n]+)*)/.source.replace(/<<prop>>/g,function(){return n})),lookbehind:!0,alias:"string"},comment:/#.*/,key:{pattern:RegExp(/((?:^|[:\-,[{\r\n?])[ \t]*(?:<<prop>>[ \t]+)?)<<key>>(?=\s*:\s)/.source.replace(/<<prop>>/g,function(){return n}).replace(/<<key>>/g,function(){return"(?:"+i+"|"+b+")"})),lookbehind:!0,greedy:!0,alias:"atrule"},directive:{pattern:/(^[ \t]*)%.+/m,lookbehind:!0,alias:"important"},datetime:{pattern:d(/\d{4}-\d\d?-\d\d?(?:[tT]|[ \t]+)\d\d?:\d{2}:\d{2}(?:\.\d*)?(?:[ \t]*(?:Z|[-+]\d\d?(?::\d{2})?))?|\d{4}-\d{2}-\d{2}|\d\d?:\d{2}(?::\d{2}(?:\.\d*)?)?/.source),lookbehind:!0,alias:"number"},boolean:{pattern:d(/false|true/.source,"i"),lookbehind:!0,alias:"important"},null:{pattern:d(/null|~/.source,"i"),lookbehind:!0,alias:"important"},string:{pattern:d(b),lookbehind:!0,greedy:!0},number:{pattern:d(/[+-]?(?:0x[\da-f]+|0o[0-7]+|(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?|\.inf|\.nan)/.source,"i"),lookbehind:!0},tag:a,important:t,punctuation:/---|[:[\]{}\-,|>?]|\.\.\./},e.languages.yml=e.languages.yaml}(l),function(e){var t=/(?:\\.|[^\\\n\r]|(?:\n|\r\n?)(?![\r\n]))/.source;function a(p){return p=p.replace(/<inner>/g,function(){return t}),RegExp(/((?:^|[^\\])(?:\\{2})*)/.source+"(?:"+p+")")}var n=/(?:\\.|``(?:[^`\r\n]|`(?!`))+``|`[^`\r\n]+`|[^\\|\r\n`])+/.source,i=/\|?__(?:\|__)+\|?(?:(?:\n|\r\n?)|(?![\s\S]))/.source.replace(/__/g,function(){return n}),b=/\|?[ \t]*:?-{3,}:?[ \t]*(?:\|[ \t]*:?-{3,}:?[ \t]*)+\|?(?:\n|\r\n?)/.source,d=(e.languages.markdown=e.languages.extend("markup",{}),e.languages.insertBefore("markdown","prolog",{"front-matter-block":{pattern:/(^(?:\s*[\r\n])?)---(?!.)[\s\S]*?[\r\n]---(?!.)/,lookbehind:!0,greedy:!0,inside:{punctuation:/^---|---$/,"front-matter":{pattern:/\S+(?:\s+\S+)*/,alias:["yaml","language-yaml"],inside:e.languages.yaml}}},blockquote:{pattern:/^>(?:[\t ]*>)*/m,alias:"punctuation"},table:{pattern:RegExp("^"+i+b+"(?:"+i+")*","m"),inside:{"table-data-rows":{pattern:RegExp("^("+i+b+")(?:"+i+")*$"),lookbehind:!0,inside:{"table-data":{pattern:RegExp(n),inside:e.languages.markdown},punctuation:/\|/}},"table-line":{pattern:RegExp("^("+i+")"+b+"$"),lookbehind:!0,inside:{punctuation:/\||:?-{3,}:?/}},"table-header-row":{pattern:RegExp("^"+i+"$"),inside:{"table-header":{pattern:RegExp(n),alias:"important",inside:e.languages.markdown},punctuation:/\|/}}}},code:[{pattern:/((?:^|\n)[ \t]*\n|(?:^|\r\n?)[ \t]*\r\n?)(?: {4}|\t).+(?:(?:\n|\r\n?)(?: {4}|\t).+)*/,lookbehind:!0,alias:"keyword"},{pattern:/^```[\s\S]*?^```$/m,greedy:!0,inside:{"code-block":{pattern:/^(```.*(?:\n|\r\n?))[\s\S]+?(?=(?:\n|\r\n?)^```$)/m,lookbehind:!0},"code-language":{pattern:/^(```).+/,lookbehind:!0},punctuation:/```/}}],title:[{pattern:/\S.*(?:\n|\r\n?)(?:==+|--+)(?=[ \t]*$)/m,alias:"important",inside:{punctuation:/==+$|--+$/}},{pattern:/(^\s*)#.+/m,lookbehind:!0,alias:"important",inside:{punctuation:/^#+|#+$/}}],hr:{pattern:/(^\s*)([*-])(?:[\t ]*\2){2,}(?=\s*$)/m,lookbehind:!0,alias:"punctuation"},list:{pattern:/(^\s*)(?:[*+-]|\d+\.)(?=[\t ].)/m,lookbehind:!0,alias:"punctuation"},"url-reference":{pattern:/!?\[[^\]]+\]:[\t ]+(?:\S+|<(?:\\.|[^>\\])+>)(?:[\t ]+(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\)))?/,inside:{variable:{pattern:/^(!?\[)[^\]]+/,lookbehind:!0},string:/(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\))$/,punctuation:/^[\[\]!:]|[<>]/},alias:"url"},bold:{pattern:a(/\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\b|\*\*(?:(?!\*)<inner>|\*(?:(?!\*)<inner>)+\*)+\*\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^..)[\s\S]+(?=..$)/,lookbehind:!0,inside:{}},punctuation:/\*\*|__/}},italic:{pattern:a(/\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\b|\*(?:(?!\*)<inner>|\*\*(?:(?!\*)<inner>)+\*\*)+\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^.)[\s\S]+(?=.$)/,lookbehind:!0,inside:{}},punctuation:/[*_]/}},strike:{pattern:a(/(~~?)(?:(?!~)<inner>)+\2/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^~~?)[\s\S]+(?=\1$)/,lookbehind:!0,inside:{}},punctuation:/~~?/}},"code-snippet":{pattern:/(^|[^\\`])(?:``[^`\r\n]+(?:`[^`\r\n]+)*``(?!`)|`[^`\r\n]+`(?!`))/,lookbehind:!0,greedy:!0,alias:["code","keyword"]},url:{pattern:a(/!?\[(?:(?!\])<inner>)+\](?:\([^\s)]+(?:[\t ]+"(?:\\.|[^"\\])*")?\)|[ \t]?\[(?:(?!\])<inner>)+\])/.source),lookbehind:!0,greedy:!0,inside:{operator:/^!/,content:{pattern:/(^\[)[^\]]+(?=\])/,lookbehind:!0,inside:{}},variable:{pattern:/(^\][ \t]?\[)[^\]]+(?=\]$)/,lookbehind:!0},url:{pattern:/(^\]\()[^\s)]+/,lookbehind:!0},string:{pattern:/(^[ \t]+)"(?:\\.|[^"\\])*"(?=\)$)/,lookbehind:!0}}}}),["url","bold","italic","strike"].forEach(function(p){["url","bold","italic","strike","code-snippet"].forEach(function(f){p!==f&&(e.languages.markdown[p].inside.content.inside[f]=e.languages.markdown[f])})}),e.hooks.add("after-tokenize",function(p){p.language!=="markdown"&&p.language!=="md"||function f(y){if(y&&typeof y!="string")for(var k=0,S=y.length;k<S;k++){var s,o=y[k];o.type!=="code"?f(o.content):(s=o.content[1],o=o.content[3],s&&o&&s.type==="code-language"&&o.type==="code-block"&&typeof s.content=="string"&&(s=s.content.replace(/\b#/g,"sharp").replace(/\b\+\+/g,"pp"),s="language-"+(s=(/[a-z][\w-]*/i.exec(s)||[""])[0].toLowerCase()),o.alias?typeof o.alias=="string"?o.alias=[o.alias,s]:o.alias.push(s):o.alias=[s]))}}(p.tokens)}),e.hooks.add("wrap",function(p){if(p.type==="code-block"){for(var f="",y=0,k=p.classes.length;y<k;y++){var S=p.classes[y],S=/language-(.+)/.exec(S);if(S){f=S[1];break}}var s,o=e.languages[f];o?p.content=e.highlight(function(u){return u=u.replace(d,""),u=u.replace(/&(\w{1,8}|#x?[\da-f]{1,8});/gi,function(m,g){var x;return(g=g.toLowerCase())[0]==="#"?(x=g[1]==="x"?parseInt(g.slice(2),16):Number(g.slice(1)),c(x)):h[g]||m})}(p.content),o,f):f&&f!=="none"&&e.plugins.autoloader&&(s="md-"+new Date().valueOf()+"-"+Math.floor(1e16*Math.random()),p.attributes.id=s,e.plugins.autoloader.loadLanguages(f,function(){var u=document.getElementById(s);u&&(u.innerHTML=e.highlight(u.textContent,e.languages[f],f))}))}}),RegExp(e.languages.markup.tag.pattern.source,"gi")),h={amp:"&",lt:"<",gt:">",quot:'"'},c=String.fromCodePoint||String.fromCharCode;e.languages.md=e.languages.markdown}(l),l.languages.graphql={comment:/#.*/,description:{pattern:/(?:"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*")(?=\s*[a-z_])/i,greedy:!0,alias:"string",inside:{"language-markdown":{pattern:/(^"(?:"")?)(?!\1)[\s\S]+(?=\1$)/,lookbehind:!0,inside:l.languages.markdown}}},string:{pattern:/"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*"/,greedy:!0},number:/(?:\B-|\b)\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,boolean:/\b(?:false|true)\b/,variable:/\$[a-z_]\w*/i,directive:{pattern:/@[a-z_]\w*/i,alias:"function"},"attr-name":{pattern:/\b[a-z_]\w*(?=\s*(?:\((?:[^()"]|"(?:\\.|[^\\"\r\n])*")*\))?:)/i,greedy:!0},"atom-input":{pattern:/\b[A-Z]\w*Input\b/,alias:"class-name"},scalar:/\b(?:Boolean|Float|ID|Int|String)\b/,constant:/\b[A-Z][A-Z_\d]*\b/,"class-name":{pattern:/(\b(?:enum|implements|interface|on|scalar|type|union)\s+|&\s*|:\s*|\[)[A-Z_]\w*/,lookbehind:!0},fragment:{pattern:/(\bfragment\s+|\.{3}\s*(?!on\b))[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-mutation":{pattern:/(\bmutation\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-query":{pattern:/(\bquery\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},keyword:/\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\b/,operator:/[!=|&]|\.{3}/,"property-query":/\w+(?=\s*\()/,object:/\w+(?=\s*\{)/,punctuation:/[!(){}\[\]:=,]/,property:/\w+/},l.hooks.add("after-tokenize",function(e){if(e.language==="graphql")for(var t=e.tokens.filter(function(s){return typeof s!="string"&&s.type!=="comment"&&s.type!=="scalar"}),a=0;a<t.length;){var n=t[a++];if(n.type==="keyword"&&n.content==="mutation"){var i=[];if(y(["definition-mutation","punctuation"])&&f(1).content==="("){a+=2;var b=k(/^\($/,/^\)$/);if(b===-1)continue;for(;a<b;a++){var d=f(0);d.type==="variable"&&(S(d,"variable-input"),i.push(d.content))}a=b+1}if(y(["punctuation","property-query"])&&f(0).content==="{"&&(a++,S(f(0),"property-mutation"),0<i.length)){var h=k(/^\{$/,/^\}$/);if(h!==-1)for(var c=a;c<h;c++){var p=t[c];p.type==="variable"&&0<=i.indexOf(p.content)&&S(p,"variable-input")}}}}function f(s){return t[a+s]}function y(s,o){o=o||0;for(var u=0;u<s.length;u++){var m=f(u+o);if(!m||m.type!==s[u])return}return 1}function k(s,o){for(var u=1,m=a;m<t.length;m++){var g=t[m],x=g.content;if(g.type==="punctuation"&&typeof x=="string"){if(s.test(x))u++;else if(o.test(x)&&--u===0)return m}}return-1}function S(s,o){var u=s.alias;u?Array.isArray(u)||(s.alias=u=[u]):s.alias=u=[],u.push(o)}}),l.languages.sql={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},function(e){var t=e.languages.javascript["template-string"],a=t.pattern.source,n=t.inside.interpolation,i=n.inside["interpolation-punctuation"],b=n.pattern.source;function d(y,k){if(e.languages[y])return{pattern:RegExp("((?:"+k+")\\s*)"+a),lookbehind:!0,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},"embedded-code":{pattern:/[\s\S]+/,alias:y}}}}function h(y,k,S){return y={code:y,grammar:k,language:S},e.hooks.run("before-tokenize",y),y.tokens=e.tokenize(y.code,y.grammar),e.hooks.run("after-tokenize",y),y.tokens}function c(y,k,S){var u=e.tokenize(y,{interpolation:{pattern:RegExp(b),lookbehind:!0}}),s=0,o={},u=h(u.map(function(g){if(typeof g=="string")return g;for(var x,w,g=g.content;y.indexOf((w=s++,x="___"+S.toUpperCase()+"_"+w+"___"))!==-1;);return o[x]=g,x}).join(""),k,S),m=Object.keys(o);return s=0,function g(x){for(var w=0;w<x.length;w++){if(s>=m.length)return;var E,A,_,N,j,P,B,O=x[w];typeof O=="string"||typeof O.content=="string"?(E=m[s],(B=(P=typeof O=="string"?O:O.content).indexOf(E))!==-1&&(++s,A=P.substring(0,B),j=o[E],_=void 0,(N={})["interpolation-punctuation"]=i,(N=e.tokenize(j,N)).length===3&&((_=[1,1]).push.apply(_,h(N[1],e.languages.javascript,"javascript")),N.splice.apply(N,_)),_=new e.Token("interpolation",N,n.alias,j),N=P.substring(B+E.length),j=[],A&&j.push(A),j.push(_),N&&(g(P=[N]),j.push.apply(j,P)),typeof O=="string"?(x.splice.apply(x,[w,1].concat(j)),w+=j.length-1):O.content=j)):(B=O.content,Array.isArray(B)?g(B):g([B]))}}(u),new e.Token(S,u,"language-"+S,y)}e.languages.javascript["template-string"]=[d("css",/\b(?:styled(?:\([^)]*\))?(?:\s*\.\s*\w+(?:\([^)]*\))*)*|css(?:\s*\.\s*(?:global|resolve))?|createGlobalStyle|keyframes)/.source),d("html",/\bhtml|\.\s*(?:inner|outer)HTML\s*\+?=/.source),d("svg",/\bsvg/.source),d("markdown",/\b(?:markdown|md)/.source),d("graphql",/\b(?:gql|graphql(?:\s*\.\s*experimental)?)/.source),d("sql",/\bsql/.source),t].filter(Boolean);var p={javascript:!0,js:!0,typescript:!0,ts:!0,jsx:!0,tsx:!0};function f(y){return typeof y=="string"?y:Array.isArray(y)?y.map(f).join(""):f(y.content)}e.hooks.add("after-tokenize",function(y){y.language in p&&function k(S){for(var s=0,o=S.length;s<o;s++){var u,m,g,x=S[s];typeof x!="string"&&(u=x.content,Array.isArray(u)?x.type==="template-string"?(x=u[1],u.length===3&&typeof x!="string"&&x.type==="embedded-code"&&(m=f(x),x=x.alias,x=Array.isArray(x)?x[0]:x,g=e.languages[x])&&(u[1]=c(m,g,x))):k(u):typeof u!="string"&&k([u]))}}(y.tokens)})}(l),function(e){e.languages.typescript=e.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),e.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete e.languages.typescript.parameter,delete e.languages.typescript["literal-property"];var t=e.languages.extend("typescript",{});delete t["class-name"],e.languages.typescript["class-name"].inside=t,e.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:t}}}}),e.languages.ts=e.languages.typescript}(l),function(e){var t=e.languages.javascript,a=/\{(?:[^{}]|\{(?:[^{}]|\{[^{}]*\})*\})+\}/.source,n="(@(?:arg|argument|param|property)\\s+(?:"+a+"\\s+)?)";e.languages.jsdoc=e.languages.extend("javadoclike",{parameter:{pattern:RegExp(n+/(?:(?!\s)[$\w\xA0-\uFFFF.])+(?=\s|$)/.source),lookbehind:!0,inside:{punctuation:/\./}}}),e.languages.insertBefore("jsdoc","keyword",{"optional-parameter":{pattern:RegExp(n+/\[(?:(?!\s)[$\w\xA0-\uFFFF.])+(?:=[^[\]]+)?\](?=\s|$)/.source),lookbehind:!0,inside:{parameter:{pattern:/(^\[)[$\w\xA0-\uFFFF\.]+/,lookbehind:!0,inside:{punctuation:/\./}},code:{pattern:/(=)[\s\S]*(?=\]$)/,lookbehind:!0,inside:t,alias:"language-javascript"},punctuation:/[=[\]]/}},"class-name":[{pattern:RegExp(/(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\s+(?:<TYPE>\s+)?)[A-Z]\w*(?:\.[A-Z]\w*)*/.source.replace(/<TYPE>/g,function(){return a})),lookbehind:!0,inside:{punctuation:/\./}},{pattern:RegExp("(@[a-z]+\\s+)"+a),lookbehind:!0,inside:{string:t.string,number:t.number,boolean:t.boolean,keyword:e.languages.typescript.keyword,operator:/=>|\.\.\.|[&|?:*]/,punctuation:/[.,;=<>{}()[\]]/}}],example:{pattern:/(@example\s+(?!\s))(?:[^@\s]|\s+(?!\s))+?(?=\s*(?:\*\s*)?(?:@\w|\*\/))/,lookbehind:!0,inside:{code:{pattern:/^([\t ]*(?:\*\s*)?)\S.*$/m,lookbehind:!0,inside:t,alias:"language-javascript"}}}}),e.languages.javadoclike.addSupport("javascript",e.languages.jsdoc)}(l),function(e){e.languages.flow=e.languages.extend("javascript",{}),e.languages.insertBefore("flow","keyword",{type:[{pattern:/\b(?:[Bb]oolean|Function|[Nn]umber|[Ss]tring|[Ss]ymbol|any|mixed|null|void)\b/,alias:"class-name"}]}),e.languages.flow["function-variable"].pattern=/(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=\s*(?:function\b|(?:\([^()]*\)(?:\s*:\s*\w+)?|(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/i,delete e.languages.flow.parameter,e.languages.insertBefore("flow","operator",{"flow-punctuation":{pattern:/\{\||\|\}/,alias:"punctuation"}}),Array.isArray(e.languages.flow.keyword)||(e.languages.flow.keyword=[e.languages.flow.keyword]),e.languages.flow.keyword.unshift({pattern:/(^|[^$]\b)(?:Class|declare|opaque|type)\b(?!\$)/,lookbehind:!0},{pattern:/(^|[^$]\B)\$(?:Diff|Enum|Exact|Keys|ObjMap|PropertyType|Record|Shape|Subtype|Supertype|await)\b(?!\$)/,lookbehind:!0})}(l),l.languages.n4js=l.languages.extend("javascript",{keyword:/\b(?:Array|any|boolean|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|false|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|module|new|null|number|package|private|protected|public|return|set|static|string|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)\b/}),l.languages.insertBefore("n4js","constant",{annotation:{pattern:/@+\w+/,alias:"operator"}}),l.languages.n4jsd=l.languages.n4js,function(e){function t(d,h){return RegExp(d.replace(/<ID>/g,function(){return/(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/.source}),h)}e.languages.insertBefore("javascript","function-variable",{"method-variable":{pattern:RegExp("(\\.\\s*)"+e.languages.javascript["function-variable"].pattern.source),lookbehind:!0,alias:["function-variable","method","function","property-access"]}}),e.languages.insertBefore("javascript","function",{method:{pattern:RegExp("(\\.\\s*)"+e.languages.javascript.function.source),lookbehind:!0,alias:["function","property-access"]}}),e.languages.insertBefore("javascript","constant",{"known-class-name":[{pattern:/\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\b/,alias:"class-name"},{pattern:/\b(?:[A-Z]\w*)Error\b/,alias:"class-name"}]}),e.languages.insertBefore("javascript","keyword",{imports:{pattern:t(/(\bimport\b\s*)(?:<ID>(?:\s*,\s*(?:\*\s*as\s+<ID>|\{[^{}]*\}))?|\*\s*as\s+<ID>|\{[^{}]*\})(?=\s*\bfrom\b)/.source),lookbehind:!0,inside:e.languages.javascript},exports:{pattern:t(/(\bexport\b\s*)(?:\*(?:\s*as\s+<ID>)?(?=\s*\bfrom\b)|\{[^{}]*\})/.source),lookbehind:!0,inside:e.languages.javascript}}),e.languages.javascript.keyword.unshift({pattern:/\b(?:as|default|export|from|import)\b/,alias:"module"},{pattern:/\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\b/,alias:"control-flow"},{pattern:/\bnull\b/,alias:["null","nil"]},{pattern:/\bundefined\b/,alias:"nil"}),e.languages.insertBefore("javascript","operator",{spread:{pattern:/\.{3}/,alias:"operator"},arrow:{pattern:/=>/,alias:"operator"}}),e.languages.insertBefore("javascript","punctuation",{"property-access":{pattern:t(/(\.\s*)#?<ID>/.source),lookbehind:!0},"maybe-class-name":{pattern:/(^|[^$\w\xA0-\uFFFF])[A-Z][$\w\xA0-\uFFFF]+/,lookbehind:!0},dom:{pattern:/\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\b/,alias:"variable"},console:{pattern:/\bconsole(?=\s*\.)/,alias:"class-name"}});for(var a=["function","function-variable","method","method-variable","property-access"],n=0;n<a.length;n++){var b=a[n],i=e.languages.javascript[b],b=(i=e.util.type(i)==="RegExp"?e.languages.javascript[b]={pattern:i}:i).inside||{};(i.inside=b)["maybe-class-name"]=/^[A-Z][\s\S]*/}}(l),function(e){var t=e.util.clone(e.languages.javascript),a=/(?:\s|\/\/.*(?!.)|\/\*(?:[^*]|\*(?!\/))\*\/)/.source,n=/(?:\{(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])*\})/.source,i=/(?:\{<S>*\.{3}(?:[^{}]|<BRACES>)*\})/.source;function b(c,p){return c=c.replace(/<S>/g,function(){return a}).replace(/<BRACES>/g,function(){return n}).replace(/<SPREAD>/g,function(){return i}),RegExp(c,p)}i=b(i).source,e.languages.jsx=e.languages.extend("markup",t),e.languages.jsx.tag.pattern=b(/<\/?(?:[\w.:-]+(?:<S>+(?:[\w.:$-]+(?:=(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s{'"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\/?)?>/.source),e.languages.jsx.tag.inside.tag.pattern=/^<\/?[^\s>\/]*/,e.languages.jsx.tag.inside["attr-value"].pattern=/=(?!\{)(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s'">]+)/,e.languages.jsx.tag.inside.tag.inside["class-name"]=/^[A-Z]\w*(?:\.[A-Z]\w*)*$/,e.languages.jsx.tag.inside.comment=t.comment,e.languages.insertBefore("inside","attr-name",{spread:{pattern:b(/<SPREAD>/.source),inside:e.languages.jsx}},e.languages.jsx.tag),e.languages.insertBefore("inside","special-attr",{script:{pattern:b(/=<BRACES>/.source),alias:"language-javascript",inside:{"script-punctuation":{pattern:/^=(?=\{)/,alias:"punctuation"},rest:e.languages.jsx}}},e.languages.jsx.tag);function d(c){for(var p=[],f=0;f<c.length;f++){var y=c[f],k=!1;typeof y!="string"&&(y.type==="tag"&&y.content[0]&&y.content[0].type==="tag"?y.content[0].content[0].content==="</"?0<p.length&&p[p.length-1].tagName===h(y.content[0].content[1])&&p.pop():y.content[y.content.length-1].content!=="/>"&&p.push({tagName:h(y.content[0].content[1]),openedBraces:0}):0<p.length&&y.type==="punctuation"&&y.content==="{"?p[p.length-1].openedBraces++:0<p.length&&0<p[p.length-1].openedBraces&&y.type==="punctuation"&&y.content==="}"?p[p.length-1].openedBraces--:k=!0),(k||typeof y=="string")&&0<p.length&&p[p.length-1].openedBraces===0&&(k=h(y),f<c.length-1&&(typeof c[f+1]=="string"||c[f+1].type==="plain-text")&&(k+=h(c[f+1]),c.splice(f+1,1)),0<f&&(typeof c[f-1]=="string"||c[f-1].type==="plain-text")&&(k=h(c[f-1])+k,c.splice(f-1,1),f--),c[f]=new e.Token("plain-text",k,null,k)),y.content&&typeof y.content!="string"&&d(y.content)}}var h=function(c){return c?typeof c=="string"?c:typeof c.content=="string"?c.content:c.content.map(h).join(""):""};e.hooks.add("after-tokenize",function(c){c.language!=="jsx"&&c.language!=="tsx"||d(c.tokens)})}(l),function(e){var t=e.util.clone(e.languages.typescript),t=(e.languages.tsx=e.languages.extend("jsx",t),delete e.languages.tsx.parameter,delete e.languages.tsx["literal-property"],e.languages.tsx.tag);t.pattern=RegExp(/(^|[^\w$]|(?=<\/))/.source+"(?:"+t.pattern.source+")",t.pattern.flags),t.lookbehind=!0}(l),l.languages.swift={comment:{pattern:/(^|[^\\:])(?:\/\/.*|\/\*(?:[^/*]|\/(?!\*)|\*(?!\/)|\/\*(?:[^*]|\*(?!\/))*\*\/)*\*\/)/,lookbehind:!0,greedy:!0},"string-literal":[{pattern:RegExp(/(^|[^"#])/.source+"(?:"+/"(?:\\(?:\((?:[^()]|\([^()]*\))*\)|\r\n|[^(])|[^\\\r\n"])*"/.source+"|"+/"""(?:\\(?:\((?:[^()]|\([^()]*\))*\)|[^(])|[^\\"]|"(?!""))*"""/.source+")"+/(?!["#])/.source),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\\($/,alias:"punctuation"},punctuation:/\\(?=[\r\n])/,string:/[\s\S]+/}},{pattern:RegExp(/(^|[^"#])(#+)/.source+"(?:"+/"(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|\r\n|[^#])|[^\\\r\n])*?"/.source+"|"+/"""(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|[^#])|[^\\])*?"""/.source+")\\2"),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\#+\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\#+\($/,alias:"punctuation"},string:/[\s\S]+/}}],directive:{pattern:RegExp(/#/.source+"(?:"+/(?:elseif|if)\b/.source+"(?:[ 	]*"+/(?:![ \t]*)?(?:\b\w+\b(?:[ \t]*\((?:[^()]|\([^()]*\))*\))?|\((?:[^()]|\([^()]*\))*\))(?:[ \t]*(?:&&|\|\|))?/.source+")+|"+/(?:else|endif)\b/.source+")"),alias:"property",inside:{"directive-name":/^#\w+/,boolean:/\b(?:false|true)\b/,number:/\b\d+(?:\.\d+)*\b/,operator:/!|&&|\|\||[<>]=?/,punctuation:/[(),]/}},literal:{pattern:/#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\b/,alias:"constant"},"other-directive":{pattern:/#\w+\b/,alias:"property"},attribute:{pattern:/@\w+/,alias:"atrule"},"function-definition":{pattern:/(\bfunc\s+)\w+/,lookbehind:!0,alias:"function"},label:{pattern:/\b(break|continue)\s+\w+|\b[a-zA-Z_]\w*(?=\s*:\s*(?:for|repeat|while)\b)/,lookbehind:!0,alias:"important"},keyword:/\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\b/,boolean:/\b(?:false|true)\b/,nil:{pattern:/\bnil\b/,alias:"constant"},"short-argument":/\$\d+\b/,omit:{pattern:/\b_\b/,alias:"keyword"},number:/\b(?:[\d_]+(?:\.[\de_]+)?|0x[a-f0-9_]+(?:\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\b/i,"class-name":/\b[A-Z](?:[A-Z_\d]*[a-z]\w*)?\b/,function:/\b[a-z_]\w*(?=\s*\()/i,constant:/\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\b/,operator:/[-+*/%=!<>&|^~?]+|\.[.\-+*/%=!<>&|^~?]+/,punctuation:/[{}[\]();,.:\\]/},l.languages.swift["string-literal"].forEach(function(e){e.inside.interpolation.inside=l.languages.swift}),function(e){e.languages.kotlin=e.languages.extend("clike",{keyword:{pattern:/(^|[^.])\b(?:abstract|actual|annotation|as|break|by|catch|class|companion|const|constructor|continue|crossinline|data|do|dynamic|else|enum|expect|external|final|finally|for|fun|get|if|import|in|infix|init|inline|inner|interface|internal|is|lateinit|noinline|null|object|open|operator|out|override|package|private|protected|public|reified|return|sealed|set|super|suspend|tailrec|this|throw|to|try|typealias|val|var|vararg|when|where|while)\b/,lookbehind:!0},function:[{pattern:/(?:`[^\r\n`]+`|\b\w+)(?=\s*\()/,greedy:!0},{pattern:/(\.)(?:`[^\r\n`]+`|\w+)(?=\s*\{)/,lookbehind:!0,greedy:!0}],number:/\b(?:0[xX][\da-fA-F]+(?:_[\da-fA-F]+)*|0[bB][01]+(?:_[01]+)*|\d+(?:_\d+)*(?:\.\d+(?:_\d+)*)?(?:[eE][+-]?\d+(?:_\d+)*)?[fFL]?)\b/,operator:/\+[+=]?|-[-=>]?|==?=?|!(?:!|==?)?|[\/*%<>]=?|[?:]:?|\.\.|&&|\|\||\b(?:and|inv|or|shl|shr|ushr|xor)\b/}),delete e.languages.kotlin["class-name"];var t={"interpolation-punctuation":{pattern:/^\$\{?|\}$/,alias:"punctuation"},expression:{pattern:/[\s\S]+/,inside:e.languages.kotlin}};e.languages.insertBefore("kotlin","string",{"string-literal":[{pattern:/"""(?:[^$]|\$(?:(?!\{)|\{[^{}]*\}))*?"""/,alias:"multiline",inside:{interpolation:{pattern:/\$(?:[a-z_]\w*|\{[^{}]*\})/i,inside:t},string:/[\s\S]+/}},{pattern:/"(?:[^"\\\r\n$]|\\.|\$(?:(?!\{)|\{[^{}]*\}))*"/,alias:"singleline",inside:{interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$(?:[a-z_]\w*|\{[^{}]*\})/i,lookbehind:!0,inside:t},string:/[\s\S]+/}}],char:{pattern:/'(?:[^'\\\r\n]|\\(?:.|u[a-fA-F0-9]{0,4}))'/,greedy:!0}}),delete e.languages.kotlin.string,e.languages.insertBefore("kotlin","keyword",{annotation:{pattern:/\B@(?:\w+:)?(?:[A-Z]\w*|\[[^\]]+\])/,alias:"builtin"}}),e.languages.insertBefore("kotlin","function",{label:{pattern:/\b\w+@|@\w+\b/,alias:"symbol"}}),e.languages.kt=e.languages.kotlin,e.languages.kts=e.languages.kotlin}(l),l.languages.c=l.languages.extend("clike",{comment:{pattern:/\/\/(?:[^\r\n\\]|\\(?:\r\n?|\n|(?![\r\n])))*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},"class-name":{pattern:/(\b(?:enum|struct)\s+(?:__attribute__\s*\(\([\s\S]*?\)\)\s*)?)\w+|\b[a-z]\w*_t\b/,lookbehind:!0},keyword:/\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\b/,function:/\b[a-z_]\w*(?=\s*\()/i,number:/(?:\b0x(?:[\da-f]+(?:\.[\da-f]*)?|\.[\da-f]+)(?:p[+-]?\d+)?|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/}),l.languages.insertBefore("c","string",{char:{pattern:/'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n]){0,32}'/,greedy:!0}}),l.languages.insertBefore("c","string",{macro:{pattern:/(^[\t ]*)#\s*[a-z](?:[^\r\n\\/]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|\\(?:\r\n|[\s\S]))*/im,lookbehind:!0,greedy:!0,alias:"property",inside:{string:[{pattern:/^(#\s*include\s*)<[^>]+>/,lookbehind:!0},l.languages.c.string],char:l.languages.c.char,comment:l.languages.c.comment,"macro-name":[{pattern:/(^#\s*define\s+)\w+\b(?!\()/i,lookbehind:!0},{pattern:/(^#\s*define\s+)\w+\b(?=\()/i,lookbehind:!0,alias:"function"}],directive:{pattern:/^(#\s*)[a-z]+/,lookbehind:!0,alias:"keyword"},"directive-hash":/^#/,punctuation:/##|\\(?=[\r\n])/,expression:{pattern:/\S[\s\S]*/,inside:l.languages.c}}}}),l.languages.insertBefore("c","function",{constant:/\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\b/}),delete l.languages.c.boolean,l.languages.objectivec=l.languages.extend("c",{string:{pattern:/@?"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},keyword:/\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\b/,operator:/-[->]?|\+\+?|!=?|<<?=?|>>?=?|==?|&&?|\|\|?|[~^%?*\/@]/}),delete l.languages.objectivec["class-name"],l.languages.objc=l.languages.objectivec,l.languages.reason=l.languages.extend("clike",{string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^\\\r\n"])*"/,greedy:!0},"class-name":/\b[A-Z]\w*/,keyword:/\b(?:and|as|assert|begin|class|constraint|do|done|downto|else|end|exception|external|for|fun|function|functor|if|in|include|inherit|initializer|lazy|let|method|module|mutable|new|nonrec|object|of|open|or|private|rec|sig|struct|switch|then|to|try|type|val|virtual|when|while|with)\b/,operator:/\.{3}|:[:=]|\|>|->|=(?:==?|>)?|<=?|>=?|[|^?'#!~`]|[+\-*\/]\.?|\b(?:asr|land|lor|lsl|lsr|lxor|mod)\b/}),l.languages.insertBefore("reason","class-name",{char:{pattern:/'(?:\\x[\da-f]{2}|\\o[0-3][0-7][0-7]|\\\d{3}|\\.|[^'\\\r\n])'/,greedy:!0},constructor:/\b[A-Z]\w*\b(?!\s*\.)/,label:{pattern:/\b[a-z]\w*(?=::)/,alias:"symbol"}}),delete l.languages.reason.function,function(e){for(var t=/\/\*(?:[^*/]|\*(?!\/)|\/(?!\*)|<self>)*\*\//.source,a=0;a<2;a++)t=t.replace(/<self>/g,function(){return t});t=t.replace(/<self>/g,function(){return/[^\s\S]/.source}),e.languages.rust={comment:[{pattern:RegExp(/(^|[^\\])/.source+t),lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/b?"(?:\\[\s\S]|[^\\"])*"|b?r(#*)"(?:[^"]|"(?!\1))*"\1/,greedy:!0},char:{pattern:/b?'(?:\\(?:x[0-7][\da-fA-F]|u\{(?:[\da-fA-F]_*){1,6}\}|.)|[^\\\r\n\t'])'/,greedy:!0},attribute:{pattern:/#!?\[(?:[^\[\]"]|"(?:\\[\s\S]|[^\\"])*")*\]/,greedy:!0,alias:"attr-name",inside:{string:null}},"closure-params":{pattern:/([=(,:]\s*|\bmove\s*)\|[^|]*\||\|[^|]*\|(?=\s*(?:\{|->))/,lookbehind:!0,greedy:!0,inside:{"closure-punctuation":{pattern:/^\||\|$/,alias:"punctuation"},rest:null}},"lifetime-annotation":{pattern:/'\w+/,alias:"symbol"},"fragment-specifier":{pattern:/(\$\w+:)[a-z]+/,lookbehind:!0,alias:"punctuation"},variable:/\$\w+/,"function-definition":{pattern:/(\bfn\s+)\w+/,lookbehind:!0,alias:"function"},"type-definition":{pattern:/(\b(?:enum|struct|trait|type|union)\s+)\w+/,lookbehind:!0,alias:"class-name"},"module-declaration":[{pattern:/(\b(?:crate|mod)\s+)[a-z][a-z_\d]*/,lookbehind:!0,alias:"namespace"},{pattern:/(\b(?:crate|self|super)\s*)::\s*[a-z][a-z_\d]*\b(?:\s*::(?:\s*[a-z][a-z_\d]*\s*::)*)?/,lookbehind:!0,alias:"namespace",inside:{punctuation:/::/}}],keyword:[/\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\b/,/\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\b/],function:/\b[a-z_]\w*(?=\s*(?:::\s*<|\())/,macro:{pattern:/\b\w+!/,alias:"property"},constant:/\b[A-Z_][A-Z_\d]+\b/,"class-name":/\b[A-Z]\w*\b/,namespace:{pattern:/(?:\b[a-z][a-z_\d]*\s*::\s*)*\b[a-z][a-z_\d]*\s*::(?!\s*<)/,inside:{punctuation:/::/}},number:/\b(?:0x[\dA-Fa-f](?:_?[\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\d(?:_?\d)*)?\.)?\d(?:_?\d)*(?:[Ee][+-]?\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\b/,boolean:/\b(?:false|true)\b/,punctuation:/->|\.\.=|\.{1,3}|::|[{}[\];(),:]/,operator:/[-+*\/%!^]=?|=[=>]?|&[&=]?|\|[|=]?|<<?=?|>>?=?|[@?]/},e.languages.rust["closure-params"].inside.rest=e.languages.rust,e.languages.rust.attribute.inside.string=e.languages.rust.string}(l),l.languages.go=l.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"|`[^`]*`/,lookbehind:!0,greedy:!0},keyword:/\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/,boolean:/\b(?:_|false|iota|nil|true)\b/,number:[/\b0(?:b[01_]+|o[0-7_]+)i?\b/i,/\b0x(?:[a-f\d_]+(?:\.[a-f\d_]*)?|\.[a-f\d_]+)(?:p[+-]?\d+(?:_\d+)*)?i?(?!\w)/i,/(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?[\d_]+)?i?(?!\w)/i],operator:/[*\/%^!=]=?|\+[=+]?|-[=-]?|\|[=|]?|&(?:=|&|\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\.\.\./,builtin:/\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\b/}),l.languages.insertBefore("go","string",{char:{pattern:/'(?:\\.|[^'\\\r\n]){0,10}'/,greedy:!0}}),delete l.languages.go["class-name"],function(e){var t=/\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\b/,a=/\b(?!<keyword>)\w+(?:\s*\.\s*\w+)*\b/.source.replace(/<keyword>/g,function(){return t.source});e.languages.cpp=e.languages.extend("c",{"class-name":[{pattern:RegExp(/(\b(?:class|concept|enum|struct|typename)\s+)(?!<keyword>)\w+/.source.replace(/<keyword>/g,function(){return t.source})),lookbehind:!0},/\b[A-Z]\w*(?=\s*::\s*\w+\s*\()/,/\b[A-Z_]\w*(?=\s*::\s*~\w+\s*\()/i,/\b\w+(?=\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\s*::\s*\w+\s*\()/],keyword:t,number:{pattern:/(?:\b0b[01']+|\b0x(?:[\da-f']+(?:\.[\da-f']*)?|\.[\da-f']+)(?:p[+-]?[\d']+)?|(?:\b[\d']+(?:\.[\d']*)?|\B\.[\d']+)(?:e[+-]?[\d']+)?)[ful]{0,4}/i,greedy:!0},operator:/>>=?|<<=?|->|--|\+\+|&&|\|\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\b/,boolean:/\b(?:false|true)\b/}),e.languages.insertBefore("cpp","string",{module:{pattern:RegExp(/(\b(?:import|module)\s+)/.source+"(?:"+/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|<[^<>\r\n]*>/.source+"|"+/<mod-name>(?:\s*:\s*<mod-name>)?|:\s*<mod-name>/.source.replace(/<mod-name>/g,function(){return a})+")"),lookbehind:!0,greedy:!0,inside:{string:/^[<"][\s\S]+/,operator:/:/,punctuation:/\./}},"raw-string":{pattern:/R"([^()\\ ]{0,16})\([\s\S]*?\)\1"/,alias:"string",greedy:!0}}),e.languages.insertBefore("cpp","keyword",{"generic-function":{pattern:/\b(?!operator\b)[a-z_]\w*\s*<(?:[^<>]|<[^<>]*>)*>(?=\s*\()/i,inside:{function:/^\w+/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e.languages.cpp}}}}),e.languages.insertBefore("cpp","operator",{"double-colon":{pattern:/::/,alias:"punctuation"}}),e.languages.insertBefore("cpp","class-name",{"base-clause":{pattern:/(\b(?:class|struct)\s+\w+\s*:\s*)[^;{}"'\s]+(?:\s+[^;{}"'\s]+)*(?=\s*[;{])/,lookbehind:!0,greedy:!0,inside:e.languages.extend("cpp",{})}}),e.languages.insertBefore("inside","double-colon",{"class-name":/\b[a-z_]\w*\b(?!\s*::)/i},e.languages.cpp["base-clause"])}(l),l.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/},l.languages.python["string-interpolation"].inside.interpolation.inside.rest=l.languages.python,l.languages.py=l.languages.python,l.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},l.languages.webmanifest=l.languages.json;var se={};Tt(se,{dracula:()=>Ot,duotoneDark:()=>Dt,duotoneLight:()=>Pt,github:()=>zt,gruvboxMaterialDark:()=>ya,gruvboxMaterialLight:()=>ma,jettwaveDark:()=>la,jettwaveLight:()=>ca,nightOwl:()=>Ut,nightOwlLight:()=>Zt,oceanicNext:()=>Vt,okaidia:()=>qt,oneDark:()=>pa,oneLight:()=>ga,palenight:()=>Kt,shadesOfPurple:()=>Jt,synthwave84:()=>ea,ultramin:()=>aa,vsDark:()=>Pe,vsLight:()=>sa});var Ct={plain:{color:"#F8F8F2",backgroundColor:"#282A36"},styles:[{types:["prolog","constant","builtin"],style:{color:"rgb(189, 147, 249)"}},{types:["inserted","function"],style:{color:"rgb(80, 250, 123)"}},{types:["deleted"],style:{color:"rgb(255, 85, 85)"}},{types:["changed"],style:{color:"rgb(255, 184, 108)"}},{types:["punctuation","symbol"],style:{color:"rgb(248, 248, 242)"}},{types:["string","char","tag","selector"],style:{color:"rgb(255, 121, 198)"}},{types:["keyword","variable"],style:{color:"rgb(189, 147, 249)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(98, 114, 164)"}},{types:["attr-name"],style:{color:"rgb(241, 250, 140)"}}]},Ot=Ct,It={plain:{backgroundColor:"#2a2734",color:"#9a86fd"},styles:[{types:["comment","prolog","doctype","cdata","punctuation"],style:{color:"#6c6783"}},{types:["namespace"],style:{opacity:.7}},{types:["tag","operator","number"],style:{color:"#e09142"}},{types:["property","function"],style:{color:"#9a86fd"}},{types:["tag-id","selector","atrule-id"],style:{color:"#eeebff"}},{types:["attr-name"],style:{color:"#c4b9fe"}},{types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","atrule","placeholder","variable"],style:{color:"#ffcc99"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"#c4b9fe"}}]},Dt=It,Bt={plain:{backgroundColor:"#faf8f5",color:"#728fcb"},styles:[{types:["comment","prolog","doctype","cdata","punctuation"],style:{color:"#b6ad9a"}},{types:["namespace"],style:{opacity:.7}},{types:["tag","operator","number"],style:{color:"#063289"}},{types:["property","function"],style:{color:"#b29762"}},{types:["tag-id","selector","atrule-id"],style:{color:"#2d2006"}},{types:["attr-name"],style:{color:"#896724"}},{types:["boolean","string","entity","url","attr-value","keyword","control","directive","unit","statement","regex","atrule"],style:{color:"#728fcb"}},{types:["placeholder","variable"],style:{color:"#93abdc"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"#896724"}}]},Pt=Bt,$t={plain:{color:"#393A34",backgroundColor:"#f6f8fa"},styles:[{types:["comment","prolog","doctype","cdata"],style:{color:"#999988",fontStyle:"italic"}},{types:["namespace"],style:{opacity:.7}},{types:["string","attr-value"],style:{color:"#e3116c"}},{types:["punctuation","operator"],style:{color:"#393A34"}},{types:["entity","url","symbol","number","boolean","variable","constant","property","regex","inserted"],style:{color:"#36acaa"}},{types:["atrule","keyword","attr-name","selector"],style:{color:"#00a4db"}},{types:["function","deleted","tag"],style:{color:"#d73a49"}},{types:["function-variable"],style:{color:"#6f42c1"}},{types:["tag","selector","keyword"],style:{color:"#00009f"}}]},zt=$t,Mt={plain:{color:"#d6deeb",backgroundColor:"#011627"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)",fontStyle:"italic"}},{types:["inserted","attr-name"],style:{color:"rgb(173, 219, 103)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(99, 119, 119)",fontStyle:"italic"}},{types:["string","url"],style:{color:"rgb(173, 219, 103)"}},{types:["variable"],style:{color:"rgb(214, 222, 235)"}},{types:["number"],style:{color:"rgb(247, 140, 108)"}},{types:["builtin","char","constant","function"],style:{color:"rgb(130, 170, 255)"}},{types:["punctuation"],style:{color:"rgb(199, 146, 234)"}},{types:["selector","doctype"],style:{color:"rgb(199, 146, 234)",fontStyle:"italic"}},{types:["class-name"],style:{color:"rgb(255, 203, 139)"}},{types:["tag","operator","keyword"],style:{color:"rgb(127, 219, 202)"}},{types:["boolean"],style:{color:"rgb(255, 88, 116)"}},{types:["property"],style:{color:"rgb(128, 203, 196)"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)"}}]},Ut=Mt,Gt={plain:{color:"#403f53",backgroundColor:"#FBFBFB"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)",fontStyle:"italic"}},{types:["inserted","attr-name"],style:{color:"rgb(72, 118, 214)",fontStyle:"italic"}},{types:["comment"],style:{color:"rgb(152, 159, 177)",fontStyle:"italic"}},{types:["string","builtin","char","constant","url"],style:{color:"rgb(72, 118, 214)"}},{types:["variable"],style:{color:"rgb(201, 103, 101)"}},{types:["number"],style:{color:"rgb(170, 9, 130)"}},{types:["punctuation"],style:{color:"rgb(153, 76, 195)"}},{types:["function","selector","doctype"],style:{color:"rgb(153, 76, 195)",fontStyle:"italic"}},{types:["class-name"],style:{color:"rgb(17, 17, 17)"}},{types:["tag"],style:{color:"rgb(153, 76, 195)"}},{types:["operator","property","keyword","namespace"],style:{color:"rgb(12, 150, 155)"}},{types:["boolean"],style:{color:"rgb(188, 84, 84)"}}]},Zt=Gt,L={char:"#D8DEE9",comment:"#999999",keyword:"#c5a5c5",primitive:"#5a9bcf",string:"#8dc891",variable:"#d7deea",boolean:"#ff8b50",punctuation:"#5FB3B3",tag:"#fc929e",function:"#79b6f2",className:"#FAC863",method:"#6699CC",operator:"#fc929e"},Ht={plain:{backgroundColor:"#282c34",color:"#ffffff"},styles:[{types:["attr-name"],style:{color:L.keyword}},{types:["attr-value"],style:{color:L.string}},{types:["comment","block-comment","prolog","doctype","cdata","shebang"],style:{color:L.comment}},{types:["property","number","function-name","constant","symbol","deleted"],style:{color:L.primitive}},{types:["boolean"],style:{color:L.boolean}},{types:["tag"],style:{color:L.tag}},{types:["string"],style:{color:L.string}},{types:["punctuation"],style:{color:L.string}},{types:["selector","char","builtin","inserted"],style:{color:L.char}},{types:["function"],style:{color:L.function}},{types:["operator","entity","url","variable"],style:{color:L.variable}},{types:["keyword"],style:{color:L.keyword}},{types:["atrule","class-name"],style:{color:L.className}},{types:["important"],style:{fontWeight:"400"}},{types:["bold"],style:{fontWeight:"bold"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["namespace"],style:{opacity:.7}}]},Vt=Ht,Wt={plain:{color:"#f8f8f2",backgroundColor:"#272822"},styles:[{types:["changed"],style:{color:"rgb(162, 191, 252)",fontStyle:"italic"}},{types:["deleted"],style:{color:"#f92672",fontStyle:"italic"}},{types:["inserted"],style:{color:"rgb(173, 219, 103)",fontStyle:"italic"}},{types:["comment"],style:{color:"#8292a2",fontStyle:"italic"}},{types:["string","url"],style:{color:"#a6e22e"}},{types:["variable"],style:{color:"#f8f8f2"}},{types:["number"],style:{color:"#ae81ff"}},{types:["builtin","char","constant","function","class-name"],style:{color:"#e6db74"}},{types:["punctuation"],style:{color:"#f8f8f2"}},{types:["selector","doctype"],style:{color:"#a6e22e",fontStyle:"italic"}},{types:["tag","operator","keyword"],style:{color:"#66d9ef"}},{types:["boolean"],style:{color:"#ae81ff"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)",opacity:.7}},{types:["tag","property"],style:{color:"#f92672"}},{types:["attr-name"],style:{color:"#a6e22e !important"}},{types:["doctype"],style:{color:"#8292a2"}},{types:["rule"],style:{color:"#e6db74"}}]},qt=Wt,Yt={plain:{color:"#bfc7d5",backgroundColor:"#292d3e"},styles:[{types:["comment"],style:{color:"rgb(105, 112, 152)",fontStyle:"italic"}},{types:["string","inserted"],style:{color:"rgb(195, 232, 141)"}},{types:["number"],style:{color:"rgb(247, 140, 108)"}},{types:["builtin","char","constant","function"],style:{color:"rgb(130, 170, 255)"}},{types:["punctuation","selector"],style:{color:"rgb(199, 146, 234)"}},{types:["variable"],style:{color:"rgb(191, 199, 213)"}},{types:["class-name","attr-name"],style:{color:"rgb(255, 203, 107)"}},{types:["tag","deleted"],style:{color:"rgb(255, 85, 114)"}},{types:["operator"],style:{color:"rgb(137, 221, 255)"}},{types:["boolean"],style:{color:"rgb(255, 88, 116)"}},{types:["keyword"],style:{fontStyle:"italic"}},{types:["doctype"],style:{color:"rgb(199, 146, 234)",fontStyle:"italic"}},{types:["namespace"],style:{color:"rgb(178, 204, 214)"}},{types:["url"],style:{color:"rgb(221, 221, 221)"}}]},Kt=Yt,Xt={plain:{color:"#9EFEFF",backgroundColor:"#2D2A55"},styles:[{types:["changed"],style:{color:"rgb(255, 238, 128)"}},{types:["deleted"],style:{color:"rgba(239, 83, 80, 0.56)"}},{types:["inserted"],style:{color:"rgb(173, 219, 103)"}},{types:["comment"],style:{color:"rgb(179, 98, 255)",fontStyle:"italic"}},{types:["punctuation"],style:{color:"rgb(255, 255, 255)"}},{types:["constant"],style:{color:"rgb(255, 98, 140)"}},{types:["string","url"],style:{color:"rgb(165, 255, 144)"}},{types:["variable"],style:{color:"rgb(255, 238, 128)"}},{types:["number","boolean"],style:{color:"rgb(255, 98, 140)"}},{types:["attr-name"],style:{color:"rgb(255, 180, 84)"}},{types:["keyword","operator","property","namespace","tag","selector","doctype"],style:{color:"rgb(255, 157, 0)"}},{types:["builtin","char","constant","function","class-name"],style:{color:"rgb(250, 208, 0)"}}]},Jt=Xt,Qt={plain:{backgroundColor:"linear-gradient(to bottom, #2a2139 75%, #34294f)",backgroundImage:"#34294f",color:"#f92aad",textShadow:"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"},styles:[{types:["comment","block-comment","prolog","doctype","cdata"],style:{color:"#495495",fontStyle:"italic"}},{types:["punctuation"],style:{color:"#ccc"}},{types:["tag","attr-name","namespace","number","unit","hexcode","deleted"],style:{color:"#e2777a"}},{types:["property","selector"],style:{color:"#72f1b8",textShadow:"0 0 2px #100c0f, 0 0 10px #257c5575, 0 0 35px #21272475"}},{types:["function-name"],style:{color:"#6196cc"}},{types:["boolean","selector-id","function"],style:{color:"#fdfdfd",textShadow:"0 0 2px #001716, 0 0 3px #03edf975, 0 0 5px #03edf975, 0 0 8px #03edf975"}},{types:["class-name","maybe-class-name","builtin"],style:{color:"#fff5f6",textShadow:"0 0 2px #000, 0 0 10px #fc1f2c75, 0 0 5px #fc1f2c75, 0 0 25px #fc1f2c75"}},{types:["constant","symbol"],style:{color:"#f92aad",textShadow:"0 0 2px #100c0f, 0 0 5px #dc078e33, 0 0 10px #fff3"}},{types:["important","atrule","keyword","selector-class"],style:{color:"#f4eee4",textShadow:"0 0 2px #393a33, 0 0 8px #f39f0575, 0 0 2px #f39f0575"}},{types:["string","char","attr-value","regex","variable"],style:{color:"#f87c32"}},{types:["parameter"],style:{fontStyle:"italic"}},{types:["entity","url"],style:{color:"#67cdcc"}},{types:["operator"],style:{color:"ffffffee"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["entity"],style:{cursor:"help"}},{types:["inserted"],style:{color:"green"}}]},ea=Qt,ta={plain:{color:"#282a2e",backgroundColor:"#ffffff"},styles:[{types:["comment"],style:{color:"rgb(197, 200, 198)"}},{types:["string","number","builtin","variable"],style:{color:"rgb(150, 152, 150)"}},{types:["class-name","function","tag","attr-name"],style:{color:"rgb(40, 42, 46)"}}]},aa=ta,na={plain:{color:"#9CDCFE",backgroundColor:"#1E1E1E"},styles:[{types:["prolog"],style:{color:"rgb(0, 0, 128)"}},{types:["comment"],style:{color:"rgb(106, 153, 85)"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"rgb(86, 156, 214)"}},{types:["number","inserted"],style:{color:"rgb(181, 206, 168)"}},{types:["constant"],style:{color:"rgb(100, 102, 149)"}},{types:["attr-name","variable"],style:{color:"rgb(156, 220, 254)"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"rgb(206, 145, 120)"}},{types:["selector"],style:{color:"rgb(215, 186, 125)"}},{types:["tag"],style:{color:"rgb(78, 201, 176)"}},{types:["tag"],languages:["markup"],style:{color:"rgb(86, 156, 214)"}},{types:["punctuation","operator"],style:{color:"rgb(212, 212, 212)"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"rgb(220, 220, 170)"}},{types:["class-name"],style:{color:"rgb(78, 201, 176)"}},{types:["char"],style:{color:"rgb(209, 105, 105)"}}]},Pe=na,ra={plain:{color:"#000000",backgroundColor:"#ffffff"},styles:[{types:["comment"],style:{color:"rgb(0, 128, 0)"}},{types:["builtin"],style:{color:"rgb(0, 112, 193)"}},{types:["number","variable","inserted"],style:{color:"rgb(9, 134, 88)"}},{types:["operator"],style:{color:"rgb(0, 0, 0)"}},{types:["constant","char"],style:{color:"rgb(129, 31, 63)"}},{types:["tag"],style:{color:"rgb(128, 0, 0)"}},{types:["attr-name"],style:{color:"rgb(255, 0, 0)"}},{types:["deleted","string"],style:{color:"rgb(163, 21, 21)"}},{types:["changed","punctuation"],style:{color:"rgb(4, 81, 165)"}},{types:["function","keyword"],style:{color:"rgb(0, 0, 255)"}},{types:["class-name"],style:{color:"rgb(38, 127, 153)"}}]},sa=ra,oa={plain:{color:"#f8fafc",backgroundColor:"#011627"},styles:[{types:["prolog"],style:{color:"#000080"}},{types:["comment"],style:{color:"#6A9955"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"#569CD6"}},{types:["number","inserted"],style:{color:"#B5CEA8"}},{types:["constant"],style:{color:"#f8fafc"}},{types:["attr-name","variable"],style:{color:"#9CDCFE"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"#cbd5e1"}},{types:["selector"],style:{color:"#D7BA7D"}},{types:["tag"],style:{color:"#0ea5e9"}},{types:["tag"],languages:["markup"],style:{color:"#0ea5e9"}},{types:["punctuation","operator"],style:{color:"#D4D4D4"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"#7dd3fc"}},{types:["class-name"],style:{color:"#0ea5e9"}},{types:["char"],style:{color:"#D16969"}}]},la=oa,ia={plain:{color:"#0f172a",backgroundColor:"#f1f5f9"},styles:[{types:["prolog"],style:{color:"#000080"}},{types:["comment"],style:{color:"#6A9955"}},{types:["builtin","changed","keyword","interpolation-punctuation"],style:{color:"#0c4a6e"}},{types:["number","inserted"],style:{color:"#B5CEA8"}},{types:["constant"],style:{color:"#0f172a"}},{types:["attr-name","variable"],style:{color:"#0c4a6e"}},{types:["deleted","string","attr-value","template-punctuation"],style:{color:"#64748b"}},{types:["selector"],style:{color:"#D7BA7D"}},{types:["tag"],style:{color:"#0ea5e9"}},{types:["tag"],languages:["markup"],style:{color:"#0ea5e9"}},{types:["punctuation","operator"],style:{color:"#475569"}},{types:["punctuation"],languages:["markup"],style:{color:"#808080"}},{types:["function"],style:{color:"#0e7490"}},{types:["class-name"],style:{color:"#0ea5e9"}},{types:["char"],style:{color:"#D16969"}}]},ca=ia,ua={plain:{backgroundColor:"hsl(220, 13%, 18%)",color:"hsl(220, 14%, 71%)",textShadow:"0 1px rgba(0, 0, 0, 0.3)"},styles:[{types:["comment","prolog","cdata"],style:{color:"hsl(220, 10%, 40%)"}},{types:["doctype","punctuation","entity"],style:{color:"hsl(220, 14%, 71%)"}},{types:["attr-name","class-name","maybe-class-name","boolean","constant","number","atrule"],style:{color:"hsl(29, 54%, 61%)"}},{types:["keyword"],style:{color:"hsl(286, 60%, 67%)"}},{types:["property","tag","symbol","deleted","important"],style:{color:"hsl(355, 65%, 65%)"}},{types:["selector","string","char","builtin","inserted","regex","attr-value"],style:{color:"hsl(95, 38%, 62%)"}},{types:["variable","operator","function"],style:{color:"hsl(207, 82%, 66%)"}},{types:["url"],style:{color:"hsl(187, 47%, 55%)"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"hsl(220, 14%, 71%)"}}]},pa=ua,da={plain:{backgroundColor:"hsl(230, 1%, 98%)",color:"hsl(230, 8%, 24%)"},styles:[{types:["comment","prolog","cdata"],style:{color:"hsl(230, 4%, 64%)"}},{types:["doctype","punctuation","entity"],style:{color:"hsl(230, 8%, 24%)"}},{types:["attr-name","class-name","boolean","constant","number","atrule"],style:{color:"hsl(35, 99%, 36%)"}},{types:["keyword"],style:{color:"hsl(301, 63%, 40%)"}},{types:["property","tag","symbol","deleted","important"],style:{color:"hsl(5, 74%, 59%)"}},{types:["selector","string","char","builtin","inserted","regex","attr-value","punctuation"],style:{color:"hsl(119, 34%, 47%)"}},{types:["variable","operator","function"],style:{color:"hsl(221, 87%, 60%)"}},{types:["url"],style:{color:"hsl(198, 99%, 37%)"}},{types:["deleted"],style:{textDecorationLine:"line-through"}},{types:["inserted"],style:{textDecorationLine:"underline"}},{types:["italic"],style:{fontStyle:"italic"}},{types:["important","bold"],style:{fontWeight:"bold"}},{types:["important"],style:{color:"hsl(230, 8%, 24%)"}}]},ga=da,fa={plain:{color:"#ebdbb2",backgroundColor:"#292828"},styles:[{types:["imports","class-name","maybe-class-name","constant","doctype","builtin","function"],style:{color:"#d8a657"}},{types:["property-access"],style:{color:"#7daea3"}},{types:["tag"],style:{color:"#e78a4e"}},{types:["attr-name","char","url","regex"],style:{color:"#a9b665"}},{types:["attr-value","string"],style:{color:"#89b482"}},{types:["comment","prolog","cdata","operator","inserted"],style:{color:"#a89984"}},{types:["delimiter","boolean","keyword","selector","important","atrule","property","variable","deleted"],style:{color:"#ea6962"}},{types:["entity","number","symbol"],style:{color:"#d3869b"}}]},ya=fa,ba={plain:{color:"#654735",backgroundColor:"#f9f5d7"},styles:[{types:["delimiter","boolean","keyword","selector","important","atrule","property","variable","deleted"],style:{color:"#af2528"}},{types:["imports","class-name","maybe-class-name","constant","doctype","builtin"],style:{color:"#b4730e"}},{types:["string","attr-value"],style:{color:"#477a5b"}},{types:["property-access"],style:{color:"#266b79"}},{types:["function","attr-name","char","url"],style:{color:"#72761e"}},{types:["tag"],style:{color:"#b94c07"}},{types:["comment","prolog","cdata","operator","inserted"],style:{color:"#a89984"}},{types:["entity","number","symbol"],style:{color:"#924f79"}}]},ma=ba,ha=e=>v.useCallback(t=>{var a=t,{className:n,style:i,line:b}=a,d=Be(a,["className","style","line"]);const h=Q(D({},d),{className:je("token-line",n)});return typeof e=="object"&&"plain"in e&&(h.style=e.plain),typeof i=="object"&&(h.style=D(D({},h.style||{}),i)),h},[e]),xa=e=>{const t=v.useCallback(({types:a,empty:n})=>{if(e!=null){{if(a.length===1&&a[0]==="plain")return n!=null?{display:"inline-block"}:void 0;if(a.length===1&&n!=null)return e[a[0]]}return Object.assign(n!=null?{display:"inline-block"}:{},...a.map(i=>e[i]))}},[e]);return v.useCallback(a=>{var n=a,{token:i,className:b,style:d}=n,h=Be(n,["token","className","style"]);const c=Q(D({},h),{className:je("token",...i.types,b),children:i.content,style:t(i)});return d!=null&&(c.style=D(D({},c.style||{}),d)),c},[t])},va=/\r\n|\r|\n/,he=e=>{e.length===0?e.push({types:["plain"],content:`
`,empty:!0}):e.length===1&&e[0].content===""&&(e[0].content=`
`,e[0].empty=!0)},xe=(e,t)=>{const a=e.length;return a>0&&e[a-1]===t?e:e.concat(t)},wa=e=>{const t=[[]],a=[e],n=[0],i=[e.length];let b=0,d=0,h=[];const c=[h];for(;d>-1;){for(;(b=n[d]++)<i[d];){let p,f=t[d];const k=a[d][b];if(typeof k=="string"?(f=d>0?f:["plain"],p=k):(f=xe(f,k.type),k.alias&&(f=xe(f,k.alias)),p=k.content),typeof p!="string"){d++,t.push(f),a.push(p),n.push(0),i.push(p.length);continue}const S=p.split(va),s=S.length;h.push({types:f,content:S[0]});for(let o=1;o<s;o++)he(h),c.push(h=[]),h.push({types:f,content:S[o]})}d--,t.pop(),a.pop(),n.pop(),i.pop()}return he(h),c},ve=wa,ka=({prism:e,code:t,grammar:a,language:n})=>v.useMemo(()=>{if(a==null)return ve([t]);const i={code:t,grammar:a,language:n,tokens:[]};return e.hooks.run("before-tokenize",i),i.tokens=e.tokenize(t,a),e.hooks.run("after-tokenize",i),ve(i.tokens)},[t,a,n,e]),Ea=(e,t)=>{const{plain:a}=e,n=e.styles.reduce((i,b)=>{const{languages:d,style:h}=b;return d&&!d.includes(t)||b.types.forEach(c=>{const p=D(D({},i[c]),h);i[c]=p}),i},{});return n.root=a,n.plain=Q(D({},a),{backgroundColor:void 0}),n},Sa=Ea,_a=({children:e,language:t,code:a,theme:n,prism:i})=>{const b=t.toLowerCase(),d=Sa(n,b),h=ha(d),c=xa(d),p=i.languages[b],f=ka({prism:i,language:b,code:a,grammar:p});return e({tokens:f,className:`prism-code language-${b}`,style:d!=null?d.root:{},getLineProps:h,getTokenProps:c})},Aa=e=>v.createElement(_a,Q(D({},e),{prism:e.prism||l,theme:e.theme||Pe,code:e.code,language:e.language}));/*! Bundled license information:

prismjs/prism.js:
  (**
   * Prism: Lightweight, robust, elegant syntax highlighting
   *
   * @license MIT <https://opensource.org/licenses/MIT>
   * <AUTHOR> Verou <https://lea.verou.me>
   * @namespace
   * @public
   *)
*/(typeof global<"u"?global:window).Prism=l;Xe(()=>import("./prism-json-xwnKirkR.js"),[]);const $e=v.createContext(null),ze=()=>{const e=v.useContext($e);if(e===null)throw new Error("useCodeBlockContext can only be used within a CodeBlockContext");return e},Me=({snippets:e,className:t,children:a,...n})=>{const[i,b]=v.useState(e[0]);return v.createElement($e.Provider,{value:{snippets:e,active:i,setActive:b}},v.createElement("div",{className:R("bg-ui-contrast-bg-base shadow-elevation-code-block flex flex-col overflow-hidden rounded-xl",t),...n},a))};Me.displayName="CodeBlock";const Ue=({children:e,className:t,hideLabels:a=!1,...n})=>{const{snippets:i,active:b,setActive:d}=ze(),h=v.useRef([]),c=v.useRef(null);return v.useEffect(()=>{const p=h.current.find(f=>(f==null?void 0:f.dataset.label)===b.label);if(p&&c.current){const f=h.current.indexOf(p),y=f>0?h.current[f-1]:null;c.current.style.width=`${p.offsetWidth}px`,c.current.style.left=y?`${h.current.slice(0,f).reduce((k,S)=>k+((S==null?void 0:S.offsetWidth)||0)+12,0)+15}px`:"15px"}},[b]),v.createElement("div",null,v.createElement("div",{className:R("flex items-start px-4 pt-2.5",t),...n},!a&&i.map((p,f)=>v.createElement("div",{className:R("text-ui-contrast-fg-secondary txt-compact-small-plus transition-fg relative cursor-pointer pb-[9px] pr-3",{"text-ui-contrast-fg-primary cursor-default":b.label===p.label}),key:p.label,onClick:()=>d(p)},v.createElement("span",{ref:y=>{h.current[f]=y},"data-label":p.label},p.label))),e),v.createElement("div",{className:"w-full px-0.5"},v.createElement("div",{className:"bg-ui-contrast-border-top relative h-px w-full"},v.createElement("div",{ref:c,className:R("absolute bottom-0 transition-all motion-reduce:transition-none","duration-150 ease-linear")},v.createElement("div",{className:"bg-ui-contrast-fg-primary h-px rounded-full"})))))};Ue.displayName="CodeBlock.Header";const pe=({className:e,...t})=>v.createElement("div",{className:R("txt-compact-small text-ui-contrast-fg-secondary ml-auto",e),...t});pe.displayName="CodeBlock.Header.Meta";const Na=Object.assign(Ue,{Meta:pe}),Ge=({className:e,children:t,...a})=>{const{active:n}=ze(),i=t||!n.hideCopy;return v.createElement("div",null,i&&v.createElement("div",{className:"border-ui-contrast-border-bot flex min-h-10 items-center gap-x-3 border-t px-4 py-2"},v.createElement("div",{className:"code-body text-ui-contrast-fg-secondary flex-1"},t),!n.hideCopy&&v.createElement(Le,{content:n.code,className:"text-ui-contrast-fg-secondary"})),v.createElement("div",{className:"flex h-full flex-col overflow-hidden px-[5px] pb-[5px]"},v.createElement("div",{className:R("bg-ui-contrast-bg-subtle border-ui-contrast-border-bot relative h-full overflow-y-auto rounded-lg border p-4",e),...a},v.createElement("div",{className:"max-w-[90%]"},v.createElement(Aa,{theme:{...se.palenight,plain:{color:"rgba(249, 250, 251, 1)",backgroundColor:"var(--contrast-fg-primary)"},styles:[...se.palenight.styles,{types:["keyword"],style:{fontStyle:"normal",color:"rgb(187,160,255)"}},{types:["punctuation","operator"],style:{fontStyle:"normal",color:"rgb(255,255,255)"}},{types:["constant","boolean"],style:{fontStyle:"normal",color:"rgb(187,77,96)"}},{types:["function"],style:{fontStyle:"normal",color:"rgb(27,198,242)"}},{types:["number"],style:{color:"rgb(247,208,25)"}},{types:["property"],style:{color:"rgb(247,208,25)"}},{types:["maybe-class-name"],style:{color:"rgb(255,203,107)"}},{types:["string"],style:{color:"rgb(73,209,110)"}},{types:["comment"],style:{color:"var(--contrast-fg-secondary)",fontStyle:"normal"}}]},code:n.code,language:n.language},({style:b,tokens:d,getLineProps:h,getTokenProps:c})=>v.createElement("pre",{className:R("code-body whitespace-pre-wrap bg-transparent",{"grid grid-cols-[auto,1fr] gap-x-4":!n.hideLineNumbers}),style:{...b,background:"transparent"}},!n.hideLineNumbers&&v.createElement("div",{role:"presentation",className:"flex flex-col text-right"},d.map((p,f)=>v.createElement("span",{key:f,className:"text-ui-contrast-fg-secondary tabular-nums"},f+1))),v.createElement("div",null,d.map((p,f)=>v.createElement("div",{key:f,...h({line:p})},p.map((y,k)=>v.createElement("span",{key:k,...c({token:y})})))))))))))};Ge.displayName="CodeBlock.Body";const z=Object.assign(Me,{Body:Ge,Header:Na,Meta:pe});function ne(e){const t=le(()=>Je(e)),{isStatic:a}=v.useContext(Qe);if(a){const[,n]=v.useState(e);v.useEffect(()=>t.on("change",n),[])}return t}function Fa(e){e.values.forEach(t=>t.stop())}function oe(e,t){[...t].reverse().forEach(n=>{const i=e.getVariant(n);i&&Te(e,i),e.variantChildren&&e.variantChildren.forEach(b=>{oe(b,t)})})}function Ta(e,t){if(Array.isArray(t))return oe(e,t);if(typeof t=="string")return oe(e,[t]);Te(e,t)}function ja(){let e=!1;const t=new Set,a={subscribe(n){return t.add(n),()=>void t.delete(n)},start(n,i){fe(e,"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.");const b=[];return t.forEach(d=>{b.push(et(d,n,{transitionOverride:i}))}),Promise.all(b)},set(n){return fe(e,"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook."),t.forEach(i=>{Ta(i,n)})},stop(){t.forEach(n=>{Fa(n)})},mount(){return e=!0,()=>{e=!1,a.stop()}}};return a}function La(){const e=le(ja);return tt(e.mount,[]),e}class Ra{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,a){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,a)})}}const Ca=()=>new Ra;function Oa(){return le(Ca)}var dn=e=>{const{id:t}=e.params||{},{workflow_execution:a}=Ee(t,{initialData:e.data,enabled:!!t});if(!a)return null;const n=a.id.replace("wf_exec_","");return r.jsx("span",{children:n})},Ia=e=>({queryKey:Ve.detail(e),queryFn:async()=>ot.admin.workflowExecution.retrieve(e)}),gn=async({params:e})=>{const t=e.id,a=Ia(t);return at.ensureQueryData(a)},Da=({execution:e})=>{var b;const{t}=U(),a=e.id.replace("wf_exec_",""),n=We(t,e.state),i=qe(e.state);return r.jsxs(ce,{className:"divide-y p-0",children:[r.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[r.jsxs("div",{className:"flex items-center gap-x-0.5",children:[r.jsx(ie,{children:a}),r.jsx(Le,{content:a,className:"text-ui-fg-muted"})]}),r.jsx(ht,{color:i,children:n})]}),r.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 px-6 py-4",children:[r.jsx(T,{size:"small",leading:"compact",weight:"plus",children:t("workflowExecutions.workflowIdLabel")}),r.jsx(ye,{size:"2xsmall",className:"w-fit",children:e.workflow_id})]}),r.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 px-6 py-4",children:[r.jsx(T,{size:"small",leading:"compact",weight:"plus",children:t("workflowExecutions.transactionIdLabel")}),r.jsx(ye,{size:"2xsmall",className:"w-fit",children:e.transaction_id})]}),r.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 px-6 py-4",children:[r.jsx(T,{size:"small",leading:"compact",weight:"plus",children:t("workflowExecutions.progressLabel")}),r.jsx(Pa,{steps:(b=e.execution)==null?void 0:b.steps})]})]})},Ba="_root",Pa=({steps:e})=>{const{t}=U();if(!e)return r.jsx(T,{size:"small",leading:"compact",className:"text-ui-fg-subtle",children:t("workflowExecutions.stepsCompletedLabel",{completed:0,total:0})});const a=Object.values(e).filter(i=>i.id!==Ba),n=a.filter(i=>i.invoke.state==="done");return r.jsxs("div",{className:"flex w-fit items-center gap-x-2",children:[r.jsx("div",{className:"flex items-center gap-x-[3px]",children:a.map(i=>r.jsx("div",{className:R("bg-ui-bg-switch-off shadow-details-switch-background h-3 w-1.5 rounded-full",{"bg-ui-fg-muted":i.invoke.state==="done"})},i.id))}),r.jsx(T,{size:"small",leading:"compact",className:"text-ui-fg-subtle",children:t("workflowExecutions.stepsCompletedLabel",{completed:n.length,count:a.length})})]})},$a=({execution:e})=>{var d,h;const{t}=U(),n=Object.values(((d=e.execution)==null?void 0:d.steps)||{}).filter(c=>c.id!=="_root"),i=(h=n.find(c=>c.invoke.status==="permanent_failure"))==null?void 0:h.id,b=i?n.filter(c=>c.id!==i&&c.id.includes(i)).map(c=>c.id):[];return r.jsxs(ce,{className:"divide-y p-0",children:[r.jsx("div",{className:"flex items-center justify-between px-6 py-4",children:r.jsx(ie,{level:"h2",children:t("workflowExecutions.history.sectionTitle")})}),r.jsx("div",{className:"flex flex-col gap-y-0.5 px-6 py-4",children:n.map((c,p)=>{var S,s;const f=c.id.split(".").pop();if(!f)return null;const y=(S=e.context)==null?void 0:S.data.invoke[f],k=(s=e.context)==null?void 0:s.errors.find(o=>o.action===f);return r.jsx(za,{step:c,stepInvokeContext:y,stepError:k,isLast:p===n.length-1,isUnreachable:b.includes(c.id)},c.id)})})]})},za=({step:e,stepInvokeContext:t,stepError:a,isLast:n,isUnreachable:i})=>{var k,S,s;const[b,d]=v.useState(!1),h=v.useRef(null),{hash:c}=lt(),{t:p}=U(),f=e.id.split(".").pop();v.useEffect(()=>{c===`#${f}`&&d(!0)},[c,f]);const y=e.id.split(".").pop();return r.jsxs("div",{className:"grid grid-cols-[20px_1fr] items-start gap-x-2 px-2",id:f,children:[r.jsxs("div",{className:"grid h-full grid-rows-[20px_1fr] items-center justify-center gap-y-0.5",children:[r.jsx("div",{className:"flex size-5 items-center justify-center",children:r.jsx("div",{className:"bg-ui-bg-base shadow-borders-base flex size-2.5 items-center justify-center rounded-full",children:r.jsx("div",{className:R("size-1.5 rounded-full",{"bg-ui-tag-neutral-bg":Se.includes(e.invoke.state),"bg-ui-tag-green-icon":_e.includes(e.invoke.state),"bg-ui-tag-orange-icon":Ae.includes(e.invoke.state),"bg-ui-tag-red-icon":Ne.includes(e.invoke.state),"bg-ui-tag-neutral-icon":Fe.includes(e.invoke.state)})})})}),r.jsx("div",{className:"flex h-full flex-col items-center",children:r.jsx("div",{"aria-hidden":!0,role:"presentation",className:R({"bg-ui-border-base h-full min-h-[14px] w-px":!n})})})]}),r.jsxs(it,{open:b,onOpenChange:d,children:[r.jsx(ct,{asChild:!0,children:r.jsxs("div",{className:"group flex cursor-pointer items-start justify-between outline-none",children:[r.jsx(T,{size:"small",leading:"compact",weight:"plus",children:y}),r.jsxs("div",{className:"flex items-center gap-x-2",children:[r.jsx(Ma,{state:e.invoke.state,startedAt:e.startedAt,isUnreachable:i}),r.jsx(ut,{size:"2xsmall",variant:"transparent",children:r.jsx(pt,{className:"text-ui-fg-muted transition-transform group-data-[state=open]:rotate-180"})})]})]})}),r.jsx(dt,{ref:h,children:r.jsxs("div",{className:"flex flex-col gap-y-2 pb-4 pt-2",children:[r.jsxs("div",{className:"text-ui-fg-subtle flex flex-col gap-y-2",children:[r.jsx(T,{size:"small",leading:"compact",children:p("workflowExecutions.history.definitionLabel")}),r.jsx(z,{snippets:[{code:JSON.stringify(e.definition,null,2),label:p("workflowExecutions.history.definitionLabel"),language:"json",hideLineNumbers:!0}],children:r.jsx(z.Body,{})})]}),t&&r.jsxs("div",{className:"text-ui-fg-subtle flex flex-col gap-y-2",children:[r.jsx(T,{size:"small",leading:"compact",children:p("workflowExecutions.history.outputLabel")}),r.jsx(z,{snippets:[{code:JSON.stringify(((k=t==null?void 0:t.output)==null?void 0:k.output)??{},null,2),label:p("workflowExecutions.history.outputLabel"),language:"json",hideLineNumbers:!0}],children:r.jsx(z.Body,{})})]}),!!((S=t==null?void 0:t.output)!=null&&S.compensateInput)&&e.compensate.state==="reverted"&&r.jsxs("div",{className:"text-ui-fg-subtle flex flex-col gap-y-2",children:[r.jsx(T,{size:"small",leading:"compact",children:p("workflowExecutions.history.compensateInputLabel")}),r.jsx(z,{snippets:[{code:JSON.stringify(((s=t==null?void 0:t.output)==null?void 0:s.compensateInput)??{},null,2),label:p("workflowExecutions.history.compensateInputLabel"),language:"json",hideLineNumbers:!0}],children:r.jsx(z.Body,{})})]}),a&&r.jsxs("div",{className:"text-ui-fg-subtle flex flex-col gap-y-2",children:[r.jsx(T,{size:"small",leading:"compact",children:p("workflowExecutions.history.errorLabel")}),r.jsx(z,{snippets:[{code:JSON.stringify({error:a.error,handlerType:a.handlerType},null,2),label:p("workflowExecutions.history.errorLabel"),language:"json",hideLineNumbers:!0}],children:r.jsx(z.Body,{})})]})]})})]})]})},Ma=({state:e,startedAt:t,isUnreachable:a})=>{const{t:n}=U(),i=e==="failed",b=e==="invoking",d=e==="skipped",h=e==="skipped_failure";if(a)return null;if(b)return r.jsxs("div",{className:"flex items-center gap-x-1",children:[r.jsx(T,{size:"small",leading:"compact",className:"text-ui-fg-subtle",children:n("workflowExecutions.history.runningState")}),r.jsx(gt,{className:"text-ui-fg-interactive animate-spin"})]});let c;if(d?c=n("workflowExecutions.history.skippedState"):h?c=n("workflowExecutions.history.skippedFailureState"):i&&(c=n("workflowExecutions.history.failedState")),c!==null)return r.jsx(T,{size:"small",leading:"compact",className:"text-ui-fg-subtle",children:c});if(t)return r.jsx(T,{size:"small",leading:"compact",className:"text-ui-fg-muted",children:xt(t,"dd MMM yyyy HH:mm:ss")})},Ua=({execution:e})=>{var a,n;let t=(n=(a=e.context)==null?void 0:a.data)==null?void 0:n.payload;return t?(typeof t!="object"&&(t={input:t}),r.jsx(Ke,{data:t})):null},Ga=({execution:e})=>{const{t}=U();return r.jsxs(ce,{className:"overflow-hidden px-0 pb-8 pt-0",children:[r.jsx("div",{className:"flex items-center justify-between px-6 py-4",children:r.jsx(ie,{level:"h2",children:t("general.timeline")})}),r.jsx("div",{className:"w-full overflow-hidden border-y",children:r.jsx(qa,{execution:e})})]})},Za=e=>{const t=Object.values(e).filter(n=>n.id!=="_root"),a={};return t.forEach(n=>{a[n.depth]||(a[n.depth]=[]),a[n.depth].push(n)}),a},Ha=(e,t)=>{const a=t+1;return e[a]},Y={x:-860,y:-1020,scale:1},Va=1.5,Wa=.5,we=.25,qa=({execution:e})=>{var x;const[t,a]=v.useState(1),[n,i]=v.useState(!1),b=ne(Y.scale),d=ne(Y.x),h=ne(Y.y),c=La(),p=Oa(),f=v.useRef(null),y=t<Va,k=t>Wa;v.useEffect(()=>{const w=b.on("change",E=>{a(E)});return()=>{w()}},[b]);const S=Za(((x=e.execution)==null?void 0:x.steps)||{});function s(w,E,A,_){const N=E/w;return{x:A*N,y:_*N}}const o=w=>{const{x:E,y:A}=s(t,w,d.get(),h.get());a(w),c.set({scale:w,x:E,y:A})},u=()=>{const w=b.get();if(w<1.5){const E=w+we;o(E)}},m=()=>{const w=b.get();if(w>.5){const E=w-we;o(E)}},g=()=>{c.start(Y)};return r.jsx("div",{className:"h-[400px] w-full",children:r.jsxs("div",{ref:f,className:"relative size-full",children:[r.jsx("div",{className:"relative size-full overflow-hidden object-contain",children:r.jsx("div",{children:r.jsx(ft.div,{onMouseDown:()=>i(!0),onMouseUp:()=>i(!1),drag:!0,dragConstraints:f,dragElastic:0,dragMomentum:!1,dragControls:p,initial:!1,animate:c,transition:{duration:.25},style:{x:d,y:h,scale:b},className:R("bg-ui-bg-subtle relative size-[500rem] origin-top-left items-start justify-start overflow-hidden","bg-[radial-gradient(var(--border-base)_1.5px,transparent_0)] bg-[length:20px_20px] bg-repeat",{"cursor-grab":!n,"cursor-grabbing":n}),children:r.jsx("main",{className:"size-full",children:r.jsx("div",{className:"absolute left-[1100px] top-[1100px] flex select-none items-start",children:Object.entries(S).map(([w,E])=>{const A=Ha(S,Number(w));return r.jsxs("div",{className:"flex items-start",children:[r.jsx("div",{className:"flex flex-col justify-center gap-y-2",children:E.map(_=>r.jsx(Ja,{step:_},_.id))}),r.jsx(Xa,{next:A})]},w)})})})})})}),r.jsxs("div",{className:"bg-ui-bg-base shadow-borders-base text-ui-fg-subtle absolute bottom-4 left-6 flex h-7 items-center overflow-hidden rounded-md",children:[r.jsxs("div",{className:"flex items-center",children:[r.jsx("button",{onClick:u,type:"button",disabled:!y,"aria-label":"Zoom in",className:"disabled:text-ui-fg-disabled transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed border-r p-1 outline-none",children:r.jsx(mt,{})}),r.jsx("div",{children:r.jsxs(q,{children:[r.jsx(q.Trigger,{className:"disabled:text-ui-fg-disabled transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed flex w-[50px] items-center justify-center border-r p-1 outline-none",children:r.jsxs(T,{as:"span",size:"xsmall",leading:"compact",className:"select-none tabular-nums",children:[Math.round(t*100),"%"]})}),r.jsx(q.Content,{children:[50,75,100,125,150].map(w=>r.jsxs(q.Item,{onClick:()=>o(w/100),children:[w,"%"]},w))})]})}),r.jsx("button",{onClick:m,type:"button",disabled:!k,"aria-label":"Zoom out",className:"disabled:text-ui-fg-disabled transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed border-r p-1 outline-none",children:r.jsx(yt,{})})]}),r.jsx("button",{onClick:g,type:"button","aria-label":"Reset canvas",className:"disabled:text-ui-fg-disabled transition-fg hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed focus-visible:bg-ui-bg-base-pressed p-1 outline-none",children:r.jsx(Oe,{})})]})]})})},re=()=>r.jsx("svg",{width:"42",height:"12",viewBox:"0 0 42 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M41.5303 6.53033C41.8232 6.23744 41.8232 5.76256 41.5303 5.46967L36.7574 0.696699C36.4645 0.403806 35.9896 0.403806 35.6967 0.696699C35.4038 0.989593 35.4038 1.46447 35.6967 1.75736L39.9393 6L35.6967 10.2426C35.4038 10.5355 35.4038 11.0104 35.6967 11.3033C35.9896 11.5962 36.4645 11.5962 36.7574 11.3033L41.5303 6.53033ZM0.999996 5.25C0.585785 5.25 0.249996 5.58579 0.249996 6C0.249996 6.41421 0.585785 6.75 0.999996 6.75V5.25ZM41 5.25L0.999996 5.25V6.75L41 6.75V5.25Z",fill:"var(--border-strong)"})}),Ya=()=>r.jsx("svg",{width:"22",height:"38",viewBox:"0 0 22 38",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"-mt-[6px]",children:r.jsx("path",{d:"M0.999878 32H0.249878V32.75H0.999878V32ZM21.5284 32.5303C21.8213 32.2374 21.8213 31.7626 21.5284 31.4697L16.7554 26.6967C16.4625 26.4038 15.9876 26.4038 15.6947 26.6967C15.4019 26.9896 15.4019 27.4645 15.6947 27.7574L19.9374 32L15.6947 36.2426C15.4019 36.5355 15.4019 37.0104 15.6947 37.3033C15.9876 37.5962 16.4625 37.5962 16.7554 37.3033L21.5284 32.5303ZM0.249878 0L0.249878 32H1.74988L1.74988 0H0.249878ZM0.999878 32.75L20.998 32.75V31.25L0.999878 31.25V32.75Z",fill:"var(--border-strong)"})}),ke=()=>r.jsx("svg",{width:"22",height:"38",viewBox:"0 0 22 38",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"-mt-[6px]",children:r.jsx("path",{d:"M21.5284 32.5303C21.8213 32.2374 21.8213 31.7626 21.5284 31.4697L16.7554 26.6967C16.4625 26.4038 15.9876 26.4038 15.6947 26.6967C15.4019 26.9896 15.4019 27.4645 15.6947 27.7574L19.9374 32L15.6947 36.2426C15.4019 36.5355 15.4019 37.0104 15.6947 37.3033C15.9876 37.5962 16.4625 37.5962 16.7554 37.3033L21.5284 32.5303ZM0.249878 0L0.249878 28H1.74988L1.74988 0H0.249878ZM4.99988 32.75L20.998 32.75V31.25L4.99988 31.25V32.75ZM0.249878 28C0.249878 30.6234 2.37653 32.75 4.99988 32.75V31.25C3.20495 31.25 1.74988 29.7949 1.74988 28H0.249878Z",fill:"var(--border-strong)"})}),Ka=({depth:e})=>{if(e===1)return r.jsx(re,{});if(e===2)return r.jsxs("div",{className:"flex flex-col items-end",children:[r.jsx(re,{}),r.jsx(ke,{})]});const t=Array.from({length:e-2}).map((a,n)=>r.jsx(Ya,{},n));return r.jsxs("div",{className:"flex flex-col items-end",children:[r.jsx(re,{}),t,r.jsx(ke,{})]})},Xa=({next:e})=>e?r.jsx("div",{className:"-ml-[5px] -mr-[7px] w-[60px] pr-[7px]",children:r.jsxs("div",{className:"flex min-h-[24px] w-full items-start",children:[r.jsx("div",{className:"flex h-6 w-2.5 items-center justify-center",children:r.jsx("div",{className:"bg-ui-button-neutral shadow-borders-base size-2.5 shrink-0 rounded-full"})}),r.jsx("div",{className:"pt-1.5",children:r.jsx(Ka,{depth:e.length})})]})}):null,Ja=({step:e})=>{if(e.id==="_root")return null;const t=e.id.split(".").pop(),a=()=>{if(!t)return;const n=document.getElementById(t);n&&setTimeout(()=>{n.scrollIntoView({behavior:"smooth",block:"end"})},100)};return r.jsx(bt,{to:`#${t}`,onClick:a,className:"focus-visible:shadow-borders-focus transition-fg rounded-md outline-none",children:r.jsxs("div",{className:"bg-ui-bg-base shadow-borders-base flex min-w-[120px] items-center gap-x-0.5 rounded-md p-0.5","data-step-id":e.id,children:[r.jsx("div",{className:"flex size-5 items-center justify-center",children:r.jsx("div",{className:R("size-2 rounded-sm shadow-[inset_0_0_0_1px_rgba(0,0,0,0.12)]",{"bg-ui-tag-neutral-bg":Se.includes(e.invoke.state),"bg-ui-tag-green-icon":_e.includes(e.invoke.state),"bg-ui-tag-orange-icon":Ae.includes(e.invoke.state),"bg-ui-tag-red-icon":Ne.includes(e.invoke.state),"bg-ui-tag-neutral-icon":Fe.includes(e.invoke.state)})})}),r.jsx(T,{size:"xsmall",leading:"compact",weight:"plus",className:"select-none",children:t})]})})},fn=()=>{const{id:e}=nt(),{workflow_execution:t,isLoading:a,isError:n,error:i}=Ee(e),{getWidgets:b}=rt();if(a||!t)return r.jsx(st,{sections:4,showJSON:!0});if(n)throw i;return r.jsxs(Ye,{widgets:{after:b("workflow.details.after"),before:b("workflow.details.before")},data:t,showJSON:!0,children:[r.jsx(Da,{execution:t}),r.jsx(Ga,{execution:t}),r.jsx(Ua,{execution:t}),r.jsx($a,{execution:t})]})};export{dn as Breadcrumb,fn as Component,gn as loader};
