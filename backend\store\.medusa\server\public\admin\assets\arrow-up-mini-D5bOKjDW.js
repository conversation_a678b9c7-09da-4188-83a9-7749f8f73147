import{r as a}from"./index-Bwql5Dzz.js";var d=Object.defineProperty,n=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable,f=(r,t,e)=>t in r?d(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,m=(r,t)=>{for(var e in t)s.call(t,e)&&f(r,e,t[e]);if(n)for(var e of n(t))_.call(t,e)&&f(r,e,t[e]);return r},O=(r,t)=>{var e={};for(var o in r)s.call(r,o)&&t.indexOf(o)<0&&(e[o]=r[o]);if(r!=null&&n)for(var o of n(r))t.indexOf(o)<0&&_.call(r,o)&&(e[o]=r[o]);return e};const c=a.forwardRef((r,t)=>{var e=r,{color:o="currentColor"}=e,l=O(e,["color"]);return a.createElement("svg",m({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},l),a.createElement("path",{stroke:o,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M7.5 13.056V1.944M11.278 9.278 7.5 13.056 3.722 9.278"}))});c.displayName="ArrowDownMini";var g=Object.defineProperty,i=Object.getOwnPropertySymbols,v=Object.prototype.hasOwnProperty,w=Object.prototype.propertyIsEnumerable,p=(r,t,e)=>t in r?g(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,h=(r,t)=>{for(var e in t)v.call(t,e)&&p(r,e,t[e]);if(i)for(var e of i(t))w.call(t,e)&&p(r,e,t[e]);return r},u=(r,t)=>{var e={};for(var o in r)v.call(r,o)&&t.indexOf(o)<0&&(e[o]=r[o]);if(r!=null&&i)for(var o of i(r))t.indexOf(o)<0&&w.call(r,o)&&(e[o]=r[o]);return e};const y=a.forwardRef((r,t)=>{var e=r,{color:o="currentColor"}=e,l=u(e,["color"]);return a.createElement("svg",h({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},l),a.createElement("path",{stroke:o,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M7.5 1.944v11.112M3.722 5.722 7.5 1.944l3.778 3.778"}))});y.displayName="ArrowUpMini";export{y as A,c as a};
