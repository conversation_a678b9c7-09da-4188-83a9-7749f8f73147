export type PermissionTypeValue = 'All' | 'Restricted' | 'Read only';
export type ResourcePermissionValue = 'None' | 'Read' | 'Write';
export type OwnerTypeValue = 'You' | 'Service account';

// Update interface to match Zod schema
export interface ApiKeyFormValues {
  name?: string;
  permissionType: PermissionTypeValue;
  ownerType: OwnerTypeValue;
  resourcePermissions: Record<string, ResourcePermissionValue>;
  project: string;
}

// Import Zod for schema validation
import { z } from 'zod';

// Create validation schema for API key form
export const apiKeySchema = z.object({
  name: z.string().optional(),
  permissionType: z.enum(['All', 'Restricted', 'Read only']),
  ownerType: z.enum(['You', 'Service account']),
  resourcePermissions: z.record(z.enum(['None', 'Read', 'Write'])),
  project: z.string(),
});
