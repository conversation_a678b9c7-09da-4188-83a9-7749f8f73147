import{b as x,j as e,m as d,Y as n,V as u}from"./index-Bwql5Dzz.js";var p=({list:l,className:c,inline:r,rounded:t=!1,n:a=2})=>{const{t:i}=x(),o=i("general.plusCount",{count:l.length-a});return e.jsxs("div",{className:d("text-ui-fg-subtle txt-compact-small gap-x-2 overflow-hidden",{"inline-flex":r,flex:!r},c),children:[l.slice(0,a).map(s=>e.jsx(n,{rounded:t?"full":"base",size:"2xsmall",children:s},s)),l.length>a&&e.jsx("div",{className:"whitespace-nowrap",children:e.jsx(u,{content:e.jsx("ul",{children:l.slice(a).map(s=>e.jsx("li",{children:s},s))}),children:e.jsx(n,{rounded:t?"full":"base",size:"2xsmall",className:"cursor-default whitespace-nowrap",children:o})})})]})};export{p as B};
