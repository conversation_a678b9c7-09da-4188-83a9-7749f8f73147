import{bB as E,r as c,bE as I,j as i,bF as y,bG as x,bH as O,bX as L,bY as q,bZ as P,b_ as K,bQ as V,b$ as T,c0 as B}from"./index-Bwql5Dzz.js";var k="Radio",[H,w]=E(k),[U,z]=H(k),G=c.forwardRef((t,d)=>{const{__scopeRadio:e,name:n,checked:o=!1,required:r,disabled:a,value:l="on",onCheck:u,form:f,...b}=t,[p,R]=c.useState(null),s=I(d,h=>R(h)),v=c.useRef(!1),m=p?f||!!p.closest("form"):!0;return i.jsxs(U,{scope:e,checked:o,disabled:a,children:[i.jsx(y.button,{type:"button",role:"radio","aria-checked":o,"data-state":g(o),"data-disabled":a?"":void 0,disabled:a,value:l,...b,ref:s,onClick:x(t.onClick,h=>{o||u==null||u(),m&&(v.current=h.isPropagationStopped(),v.current||h.stopPropagation())})}),m&&i.jsx(X,{control:p,bubbles:!v.current,name:n,value:l,checked:o,required:r,disabled:a,form:f,style:{transform:"translateX(-100%)"}})]})});G.displayName=k;var S="RadioIndicator",_=c.forwardRef((t,d)=>{const{__scopeRadio:e,forceMount:n,...o}=t,r=z(S,e);return i.jsx(O,{present:n||r.checked,children:i.jsx(y.span,{"data-state":g(r.checked),"data-disabled":r.disabled?"":void 0,...o,ref:d})})});_.displayName=S;var X=t=>{const{control:d,checked:e,bubbles:n=!0,...o}=t,r=c.useRef(null),a=L(e),l=q(d);return c.useEffect(()=>{const u=r.current,f=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(f,"checked").set;if(a!==e&&p){const R=new Event("click",{bubbles:n});p.call(u,e),u.dispatchEvent(R)}},[a,e,n]),i.jsx("input",{type:"radio","aria-hidden":!0,defaultChecked:e,...o,tabIndex:-1,ref:r,style:{...t.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function g(t){return t?"checked":"unchecked"}var Y=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],C="RadioGroup",[Q,ee]=E(C,[P,w]),j=P(),A=w(),[W,Z]=Q(C),N=c.forwardRef((t,d)=>{const{__scopeRadioGroup:e,name:n,defaultValue:o,value:r,required:a=!1,disabled:l=!1,orientation:u,dir:f,loop:b=!0,onValueChange:p,...R}=t,s=j(e),v=K(f),[m,h]=V({prop:r,defaultProp:o,onChange:p});return i.jsx(W,{scope:e,name:n,required:a,disabled:l,value:m,onValueChange:h,children:i.jsx(T,{asChild:!0,...s,orientation:u,dir:v,loop:b,children:i.jsx(y.div,{role:"radiogroup","aria-required":a,"aria-orientation":u,"data-disabled":l?"":void 0,dir:v,...R,ref:d})})})});N.displayName=C;var D="RadioGroupItem",F=c.forwardRef((t,d)=>{const{__scopeRadioGroup:e,disabled:n,...o}=t,r=Z(D,e),a=r.disabled||n,l=j(e),u=A(e),f=c.useRef(null),b=I(d,f),p=r.value===o.value,R=c.useRef(!1);return c.useEffect(()=>{const s=m=>{Y.includes(m.key)&&(R.current=!0)},v=()=>R.current=!1;return document.addEventListener("keydown",s),document.addEventListener("keyup",v),()=>{document.removeEventListener("keydown",s),document.removeEventListener("keyup",v)}},[]),i.jsx(B,{asChild:!0,...l,focusable:!a,active:p,children:i.jsx(G,{disabled:a,required:r.required,checked:p,...u,...o,name:r.name,ref:b,onCheck:()=>r.onValueChange(o.value),onKeyDown:x(s=>{s.key==="Enter"&&s.preventDefault()}),onFocus:x(o.onFocus,()=>{var s;R.current&&((s=f.current)==null||s.click())})})})});F.displayName=D;var $="RadioGroupIndicator",M=c.forwardRef((t,d)=>{const{__scopeRadioGroup:e,...n}=t,o=A(e);return i.jsx(_,{...o,...n,ref:d})});M.displayName=$;var oe=N,re=F,te=M;export{re as I,oe as R,te as a};
