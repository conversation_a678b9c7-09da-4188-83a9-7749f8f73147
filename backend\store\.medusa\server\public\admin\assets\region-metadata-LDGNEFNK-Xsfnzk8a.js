import{M as n}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as p,e8 as d,ea as u,j as o}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import{b as R}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var P=()=>{const{id:a}=p(),{region:t,isPending:r,isError:e,error:s}=d(a),{mutateAsync:i,isPending:m}=u(a);if(e)throw s;return o.jsx(R,{children:o.jsx(n,{isPending:r,isMutating:m,hook:i,metadata:t==null?void 0:t.metadata})})};export{P as Component};
