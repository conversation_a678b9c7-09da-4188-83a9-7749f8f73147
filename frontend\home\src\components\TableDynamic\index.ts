import TableDynamic, { tableDynamicVariants } from './table-dynamic';
import { TableDynamicHeader } from './table-dynamic-header';
import { TableDynamicBody } from './table-dynamic-body';
import { TableDynamicFooter } from './table-dynamic-footer';
import { TableDynamicFilter } from './table-dynamic-filter';
import { TableDynamicSkeleton } from './table-dynamic-skeleton';
import { TableContextProvider, useTableContext } from './table-dynamic-context';

export {
  TableDynamic,
  TableDynamicHeader,
  TableDynamicBody,
  TableDynamicFooter,
  TableDynamicFilter,
  TableDynamicSkeleton,
  TableContextProvider,
  useTableContext,
  tableDynamicVariants,
};

export default TableDynamic;
