import{M as m}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as n,eu as p,ew as d,j as u}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var j=()=>{const{shipping_profile_id:a}=n(),{shipping_profile:t,isPending:r,isError:o,error:i}=p(a),{mutateAsync:e,isPending:s}=d(t==null?void 0:t.id);if(o)throw i;return u.jsx(m,{metadata:t==null?void 0:t.metadata,hook:e,isPending:r,isMutating:s})};export{j as Component};
