// card-coupon.fixtures.tsx
import { CardCouponProps } from '../card-coupon';

// Brand logos for testing - replace with actual URLs in production
const brandsConfig = {
  philips: {
    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/47/Philips_logo_new.svg/2560px-Philips_logo_new.svg.png',
    name: '<PERSON>'
  },
  lamptan: {
    logoUrl: 'https://www.lamptangroup.com/wp-content/uploads/2022/12/LAMPTAN-LOGO-2.png',
    name: 'LAMPTAN'
  },
  bcc: {
    logoUrl: 'https://www.bcc.co.th/wp-content/uploads/2023/09/logo-1920x960-1.png',
    name: 'Bangkok Cable'
  },
};

// Fixed amount coupon - ฿2,500 with min purchase ฿30,000
export const amountCoupon: CardCouponProps = {
  id: 'amount-coupon-1',
  brandLogoUrl: brandsConfig.philips.logoUrl,
  brandName: brandsConfig.philips.name,
  discountType: 'amount',
  discountValue: 2500,
  minPurchase: 30000,
};

// Fixed amount coupon with max discount - ฿2,500 with min purchase ฿3,000 and max discount ฿1,000
export const amountCouponWithMaxDiscount: CardCouponProps = {
  id: 'amount-coupon-with-max-2',
  brandLogoUrl: brandsConfig.lamptan.logoUrl,
  brandName: brandsConfig.lamptan.name,
  discountType: 'amount',
  discountValue: 2500,
  minPurchase: 3000,
  maxDiscount: 1000,
};

// Percentage coupon - 75% with min purchase ฿30,000 and max discount ฿1,000
export const percentageCoupon: CardCouponProps = {
  id: 'percentage-coupon-1',
  brandLogoUrl: brandsConfig.bcc.logoUrl,
  brandName: brandsConfig.bcc.name,
  discountType: 'percentage',
  discountValue: 75,
  minPurchase: 30000,
  maxDiscount: 1000,
};

// Max percentage coupon - สูงสุด 50% with min purchase ฿30,000 and max discount ฿500
export const maxPercentageCoupon: CardCouponProps = {
  id: 'max-percentage-coupon-1',
  brandLogoUrl: brandsConfig.bcc.logoUrl,
  brandName: brandsConfig.bcc.name,
  discountType: 'max-percentage',
  discountValue: 50,
  minPurchase: 30000,
  maxDiscount: 500,
};

// Already claimed coupon
export const claimedCoupon: CardCouponProps = {
  ...amountCoupon,
  id: 'claimed-coupon-1',
  claimed: true,
};

// Coupon with terms only (no claim button)
export const termsOnlyCoupon: CardCouponProps = {
  ...percentageCoupon,
  id: 'terms-only-coupon-1',
  showTermsOnly: true,
};

// Coupon in loading state (claiming in progress)
export const claimingCoupon: CardCouponProps = {
  ...percentageCoupon,
  id: 'claiming-coupon-1',
  isClaimingCoupon: true,
};