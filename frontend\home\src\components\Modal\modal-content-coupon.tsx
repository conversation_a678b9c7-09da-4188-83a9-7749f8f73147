import React from 'react';
import { X } from 'lucide-react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

export interface CouponDetails {
  title: string;
  amount: string | number;
  currency?: string;
  minimumPurchase?: string | number;
  expiryDate?: string;
  applicableProducts?: string;
  restrictions?: string[];
  canBeUsedTogether?: boolean;
  companyLogo?: string;
  additionalInfo?: string;
  couponIcon?: string;
}

export interface ModalContentCouponProps {
  coupon: CouponDetails;
  onClose?: () => void;
  className?: string;
}

export const ModalContentCoupon: React.FC<ModalContentCouponProps> = ({
  coupon,
  onClose,
  className,
}) => {
  const {
    title,
    amount,
    currency = 'บาท',
    minimumPurchase,
    expiryDate,
    applicableProducts,
    restrictions = [],
    canBeUsedTogether = false,
    companyLogo,
    additionalInfo,
    couponIcon,
  } = coupon;

  return (
    <div
      className={cn(
        'relative overflow-hidden bg-white shadow-md',
        // ✅ มือถือ
        'h-[506px] w-[430px] gap-4 rounded-t-[16px] p-6',
        // ✅ Desktop
        'md:h-[411px] md:w-[693px] md:rounded-[24px]',
        className,
      )}
    >
      {/* Header */}
      <div className="flex min-h-[56px] w-[382px] items-start justify-between pb-4 md:w-[645px]">
        {' '}
        {/* Added pb-4 for 16px bottom padding */}
        <div className="flex items-center space-x-3">
          {couponIcon && (
            <div className="rounded-banner bg-background flex h-[56px] w-[56px] flex-shrink-0 items-center justify-center">
              <Image
                src={couponIcon}
                alt="Coupon icon"
                width={56}
                height={56}
                className="h-full w-full object-contain"
              />
            </div>
          )}
          <h3 className="text-h3">คูปองส่วนลด</h3>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="bg-background-foreground flex h-[40px] w-[40px] items-center justify-center rounded-lg"
            aria-label="Close"
          >
            <X className="h-3.5 w-3.5 stroke-[2.5] text-[#565C6E]" />
          </button>
        )}
      </div>
      {/* Highlighted Box */}
      <div className="flex h-[92px] w-full flex-col items-center justify-center rounded-[8px] bg-[#F6F9FF] px-6 py-5">
        <p className="text-h3 md:text-h3 font-semibold text-[#121E72]">
          คูปองส่วนลดสูงสุด{' '}
          <span className="text-h2 md:text-h2 text-error">{amount.toLocaleString()}</span>{' '}
          <span className="text-h3">{currency}</span>
        </p>
        {minimumPurchase && (
          <p className="text-text-sm text-discription-secondary">
            เมื่อซื้อสินค้าที่ร่วมรายการขั้นต่ำ {Number(minimumPurchase).toLocaleString()}{' '}
            {currency}
          </p>
        )}
      </div>
      <div className="h-[278px] w-[382px] md:h-[183px] md:w-[645px]">
        <div className="px-0">
          <div className="mt-3 flex flex-col md:flex-row">
            {/* Logo */}
            {companyLogo && (
              <div className="flex h-[72px] w-[85px] flex-shrink-0 md:h-[120px] md:w-[120px]">
                <Image
                  src={companyLogo}
                  alt="Company logo"
                  width={120}
                  height={120}
                  className="h-full w-full object-contain"
                  data-testid="coupon-company-logo"
                />
              </div>
            )}
            {/* Details with padding */}
            <div className="flex-1 md:px-6">
              {' '}
              {/* Remove padding on mobile */}
              <ul
                className="text-text-sm md:text-text-md list-disc space-y-1 pl-5 [&>li::marker]:text-xs"
                data-testid="coupon-details-list"
              >
                {expiryDate && <li data-testid="coupon-expiry-date">หมดเขตวันที่ {expiryDate}</li>}
                {applicableProducts && (
                  <li data-testid="coupon-applicable-products">
                    สามารถใช้ได้กับสินค้าที่ร่วมรายการ
                  </li>
                )}
                {!canBeUsedTogether && (
                  <li data-testid="coupon-no-combined-use">ไม่สามารถใช้โค้ดส่วนลดนี้ซ้ำได้</li>
                )}
                {restrictions.map((text, idx) => (
                  <li key={idx} data-testid={`coupon-restriction-${idx}`}>
                    {text}
                  </li>
                ))}
              </ul>
              {/* Additional info */}
              {additionalInfo && (
                <div
                  className="bg-background-foreground mt-3 flex min-h-[51px] w-[382px] items-center space-x-2 rounded-lg p-2 md:min-h-[40px] md:w-[509px]"
                  data-testid="coupon-additional-info"
                >
                  <div className="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-gray-400">
                    <span className="text-discription flex h-5 w-5 items-center justify-center text-[12px]">
                      i
                    </span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-text-sm text-discription break-words md:truncate">
                      {additionalInfo}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModalContentCoupon;
