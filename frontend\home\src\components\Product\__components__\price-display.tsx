import React from 'react';

export interface PriceDisplayProps {
  currentPrice: number;
  originalPrice?: number;
  currency?: string;
  priceUnit?: string;
  showDiscount?: boolean;
  discountPercentage?: number;
  variant?: 'default' | 'compact' | 'large';
  className?: string;
}

export const PriceDisplay: React.FC<PriceDisplayProps> = ({
  currentPrice,
  originalPrice,
  currency = 'บาท',
  priceUnit,
  showDiscount = false,
  discountPercentage,
  variant = 'default',
  className = '',
}) => {
  const hasDiscount = originalPrice !== undefined && originalPrice > currentPrice;

  // ฟอร์แมตตัวเลข
  const formatPrice = (price: number) => {
    return price.toFixed(2); // Removed .replace(/\.00$/, '') to always show decimals
  };

  return (
    <div className={`price-display ${className}`}>
      <div className="flex flex-col">
        <div className="price-display__current-price-container flex items-center">
          {' '}
          {/* Added flex and items-center */}
          <span className="text-[40px] font-bold text-red-500">{formatPrice(currentPrice)}</span>
          <span className="text-body-18-med text-discription-secondary ml-4">
            {currency}
            {priceUnit ? `/${priceUnit}` : ''}
          </span>
        </div>

        {hasDiscount && originalPrice && (
          <div className="price-display__original-price-container">
            <span className="text-body-18-reg text-border line-through">
              {formatPrice(originalPrice)}
            </span>

            {showDiscount && discountPercentage && discountPercentage > 0 && (
              <span className="ml-2 rounded bg-red-100 px-1.5 py-0.5 text-xs text-red-600">
                -{discountPercentage}%
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PriceDisplay;
