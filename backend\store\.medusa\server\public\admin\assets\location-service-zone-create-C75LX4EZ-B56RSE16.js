import{G as f}from"./chunk-DO73XJPS-ON9hrF-H.js";import{a6 as n,R as g,a_ as v,ar as y,j as e,b as S,a8 as F,a9 as _,t as h,H as b,w as l,x as w,G as Z,B as j}from"./index-Bwql5Dzz.js";import{G as k}from"./chunk-PYIO3TDQ-D8Zv8hXV.js";import"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-X5VECN6S-B3ePbuRl.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{b as C}from"./chunk-GVRV2SOJ-pKecjhuj.js";import{K as L}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as a,u as E,S as N}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./chunk-NOAFLTPV-DWvXax-e.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./index-BxZ1678G.js";import"./checkbox-B4pL6X49.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./x-mark-mini-DvSTI7zK.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./chunk-BF3VCHXD-J5OiX7iF.js";import"./prompt-BsR9zKsn.js";var D=n.object({name:n.string().min(1),countries:n.array(n.object({iso_2:n.string().min(2),display_name:n.string()})).min(1)});function G({fulfillmentSet:m,type:p,location:o}){const{t:s}=S(),{handleSuccess:d}=E(),t=F({defaultValues:{name:"",countries:[]},resolver:_(D)}),{mutateAsync:u,isPending:i}=C(m.id),x=t.handleSubmit(async r=>{await u({name:r.name,geo_zones:r.countries.map(({iso_2:c})=>({country_code:c,type:"country"}))},{onSuccess:()=>{h.success(s("stockLocations.serviceZones.create.successToast",{name:r.name})),d(`/settings/locations/${o.id}`)},onError:c=>{h.error(c.message)}})});return e.jsx(a.Form,{form:t,children:e.jsxs(L,{className:"flex h-full flex-col overflow-hidden",onSubmit:x,children:[e.jsx(a.Header,{}),e.jsx(a.Body,{className:"flex flex-1 flex-col items-center overflow-auto",children:e.jsxs(N,{id:k,children:[e.jsx("div",{className:"flex flex-1 flex-col items-center",children:e.jsxs("div",{className:"flex w-full max-w-[720px] flex-col gap-y-8 px-2 py-16",children:[e.jsx(b,{children:p==="pickup"?s("stockLocations.serviceZones.create.headerPickup",{location:o.name}):s("stockLocations.serviceZones.create.headerShipping",{location:o.name})}),e.jsx("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:e.jsx(l.Field,{control:t.control,name:"name",render:({field:r})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{children:s("fields.name")}),e.jsx(l.Control,{children:e.jsx(w,{...r})}),e.jsx(l.ErrorMessage,{})]})})}),e.jsx(Z,{label:s("general.tip"),children:s("stockLocations.serviceZones.fields.tip")}),e.jsx(f,{form:t})]})}),e.jsx(f.AreaDrawer,{form:t})]})}),e.jsx(a.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(a.Close,{asChild:!0,children:e.jsx(j,{variant:"secondary",size:"small",children:s("actions.cancel")})}),e.jsx(j,{type:"submit",size:"small",isLoading:i,children:s("actions.save")})]})})]})})}function de(){var r;const{fset_id:m,location_id:p}=g(),{stock_location:o,isPending:s,isFetching:d,isError:t,error:u}=v(p,{fields:"*fulfillment_sets"}),i=(r=o==null?void 0:o.fulfillment_sets)==null?void 0:r.find(c=>c.id===m),x=(i==null?void 0:i.type)==="pickup"?"pickup":"shipping";if(!s&&!d&&!i)throw y({message:`Fulfillment set with ID: ${m} was not found.`},404);if(t)throw u;return e.jsx(a,{prev:`/settings/locations/${p}`,children:i&&e.jsx(G,{fulfillmentSet:i,location:o,type:x})})}export{de as Component};
