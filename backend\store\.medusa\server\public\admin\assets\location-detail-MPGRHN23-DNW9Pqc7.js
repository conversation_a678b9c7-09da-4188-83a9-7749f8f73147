import{i as y,a as te}from"./chunk-R2O6QX4D-CX21ziz_.js";import{L as V}from"./chunk-I3VB6NM2-I2YhkuGj.js";import{f as W}from"./chunk-IR5DHEKS-aVJcUHa1.js";import{L as z}from"./chunk-6WKBBTKM-wn3d_HDv.js";import{N as M}from"./chunk-WX2SMNCD-B6fFhFh4.js";import{I as A}from"./chunk-EQTBJSBZ-C4fKII8C.js";import{a as ne}from"./chunk-OIAPXGI2-Bd-HAC-t.js";import{r,a_ as Y,j as s,q as ie,d as ae,R as oe,a as re,S as le,c_ as ce,s as de,H as L,T as h,b as x,ex as pe,W as F,A as g,u as me,ep as fe,cN as ue,I as xe,b4 as he,t as f,J as ge,ey as ve,D as Z,Y as je,aE as _e}from"./index-Bwql5Dzz.js";import{T}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as ye,a as be}from"./chunk-GVRV2SOJ-pKecjhuj.js";import{a as we}from"./chunk-BF3VCHXD-J5OiX7iF.js";import{u as O}from"./use-prompt-pbDx0Sfe.js";import{T as $}from"./trash-BBylvTAG.js";import{P as _}from"./pencil-square-6wRbnn1C.js";import{C as ke}from"./channels-CoQb8GLB.js";import{C as S}from"./container-Dqi2woPF.js";import{S as Ne}from"./status-badge-B-sIb9s0.js";import"./plus-mini-C5sDHkH8.js";import"./Trans-VWqfqpAH.js";import"./x-mark-mini-DvSTI7zK.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var Le=Object.defineProperty,b=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable,R=(e,n,t)=>n in e?Le(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Oe=(e,n)=>{for(var t in n)J.call(n,t)&&R(e,t,n[t]);if(b)for(var t of b(n))Q.call(n,t)&&R(e,t,n[t]);return e},$e=(e,n)=>{var t={};for(var i in e)J.call(e,i)&&n.indexOf(i)<0&&(t[i]=e[i]);if(e!=null&&b)for(var i of b(e))n.indexOf(i)<0&&Q.call(e,i)&&(t[i]=e[i]);return t};const G=r.forwardRef((e,n)=>{var t=e,{color:i="currentColor"}=t,a=$e(t,["color"]);return r.createElement("svg",Oe({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:n},a),r.createElement("g",{stroke:i,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},r.createElement("path",{d:"M12.611 5.056v6.222c0 .982-.795 1.778-1.778 1.778H4.167a1.777 1.777 0 0 1-1.778-1.778V5.056M13.056 1.944H1.944a.89.89 0 0 0-.888.89v1.333c0 .49.398.888.888.888h11.112a.89.89 0 0 0 .888-.888V2.833a.89.89 0 0 0-.889-.889M5.722 7.722h3.556"})),r.createElement("defs",null,r.createElement("clipPath",{id:"a"},r.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});G.displayName="ArchiveBox";var Se=Object.defineProperty,w=Object.getOwnPropertySymbols,K=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable,B=(e,n,t)=>n in e?Se(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Pe=(e,n)=>{for(var t in n)K.call(n,t)&&B(e,t,n[t]);if(w)for(var t of w(n))U.call(n,t)&&B(e,t,n[t]);return e},Ce=(e,n)=>{var t={};for(var i in e)K.call(e,i)&&n.indexOf(i)<0&&(t[i]=e[i]);if(e!=null&&w)for(var i of w(e))n.indexOf(i)<0&&U.call(e,i)&&(t[i]=e[i]);return t};const X=r.forwardRef((e,n)=>{var t=e,{color:i="currentColor"}=t,a=Ce(t,["color"]);return r.createElement("svg",Pe({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:n},a),r.createElement("path",{stroke:i,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M10.192 2.927 6.465 3.99a.86.86 0 0 0-.591 1.064l.944 3.312a.86.86 0 0 0 1.065.592l3.726-1.063a.86.86 0 0 0 .592-1.064l-.945-3.312a.86.86 0 0 0-1.064-.592M8.328 3.459l.473 1.656M6.876 11.708l7.298-2.081M4.933 10.357 2.77 2.744a.86.86 0 0 0-.828-.626h-.9"}),r.createElement("path",{stroke:i,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M5.347 13.313a1.507 1.507 0 1 0 0-3.014 1.507 1.507 0 0 0 0 3.014"}))});X.displayName="HandTruck";var Ee=Object.defineProperty,k=Object.getOwnPropertySymbols,ee=Object.prototype.hasOwnProperty,se=Object.prototype.propertyIsEnumerable,H=(e,n,t)=>n in e?Ee(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Te=(e,n)=>{for(var t in n)ee.call(n,t)&&H(e,t,n[t]);if(k)for(var t of k(n))se.call(n,t)&&H(e,t,n[t]);return e},De=(e,n)=>{var t={};for(var i in e)ee.call(e,i)&&n.indexOf(i)<0&&(t[i]=e[i]);if(e!=null&&k)for(var i of k(e))n.indexOf(i)<0&&se.call(e,i)&&(t[i]=e[i]);return t};const D=r.forwardRef((e,n)=>{var t=e,{color:i="currentColor"}=t,a=De(t,["color"]);return r.createElement("svg",Te({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:n},a),r.createElement("g",{stroke:i,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},r.createElement("path",{d:"M5.056 1.982v9.333M9.944 3.685v9.333M1.752 2.679 4.802 2a.9.9 0 0 1 .497.032l4.402 1.601c.159.058.331.07.497.032l2.665-.592a.888.888 0 0 1 1.081.868v7.513c0 .417-.29.778-.696.867l-3.05.679a.9.9 0 0 1-.497-.032l-4.402-1.601a.9.9 0 0 0-.497-.032l-2.665.592a.89.89 0 0 1-1.081-.868V3.546c0-.417.29-.778.696-.867"})),r.createElement("defs",null,r.createElement("clipPath",{id:"a"},r.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});D.displayName="Map";var N="name,*sales_channels,*address,fulfillment_sets.type,fulfillment_sets.name,*fulfillment_sets.service_zones.geo_zones,*fulfillment_sets.service_zones,*fulfillment_sets.service_zones.shipping_options,*fulfillment_sets.service_zones.shipping_options.rules,*fulfillment_sets.service_zones.shipping_options.shipping_profile,*fulfillment_providers",ms=e=>{const{location_id:n}=e.params||{},{stock_location:t}=Y(n,{fields:N},{initialData:e.data,enabled:!!n});return t?s.jsx("span",{children:t.name}):null},Me=e=>({queryKey:ce.detail(e,{fields:N}),queryFn:async()=>de.admin.stockLocation.retrieve(e,{fields:N})}),fs=async({params:e})=>{const n=e.location_id,t=Me(n);return ie.ensureQueryData(t)},Ae=({location:e})=>{var n,t;return s.jsxs(s.Fragment,{children:[s.jsx(S,{className:"p-0",children:s.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[s.jsxs("div",{children:[s.jsx(L,{children:e.name}),s.jsx(h,{className:"text-ui-fg-subtle txt-small",children:ne({address:e.address}).join(", ")})]}),s.jsx(Ze,{location:e})]})}),s.jsx(q,{locationId:e.id,locationName:e.name,type:"pickup",fulfillmentSet:(n=e.fulfillment_sets)==null?void 0:n.find(i=>i.type==="pickup")}),s.jsx(q,{locationId:e.id,locationName:e.name,type:"shipping",fulfillmentSet:(t=e.fulfillment_sets)==null?void 0:t.find(i=>i.type==="shipping")})]})};function I({option:e,fulfillmentSetId:n,locationId:t}){const i=O(),{t:a}=x(),c=te(e),{mutateAsync:o}=we(e.id),l=async()=>{await i({title:a("general.areYouSure"),description:a("stockLocations.shippingOptions.delete.confirmation",{name:e.name}),verificationInstruction:a("general.typeToConfirm"),verificationText:e.name,confirmText:a("actions.delete"),cancelText:a("actions.cancel")})&&await o(void 0,{onSuccess:()=>{f.success(a("stockLocations.shippingOptions.delete.successToast",{name:e.name}))},onError:j=>{f.error(j.message)}})};return s.jsxs("div",{className:"flex items-center justify-between px-3 py-2",children:[s.jsx("div",{className:"flex-1",children:s.jsxs(h,{size:"small",weight:"plus",children:[e.name," - ",e.shipping_profile.name," (",W(e.provider_id),")"]})}),s.jsx(je,{className:"mr-4",color:c?"grey":"purple",size:"2xsmall",rounded:"full",children:a(c?"general.store":"general.admin")}),s.jsx(g,{groups:[{actions:[{icon:s.jsx(_,{}),label:a("stockLocations.shippingOptions.edit.action"),to:`/settings/locations/${t}/fulfillment-set/${n}/service-zone/${e.service_zone_id}/shipping-option/${e.id}/edit`},{label:a("stockLocations.shippingOptions.pricing.action"),icon:s.jsx(_e,{}),disabled:e.price_type==="calculated",to:`/settings/locations/${t}/fulfillment-set/${n}/service-zone/${e.service_zone_id}/shipping-option/${e.id}/pricing`}]},{actions:[{label:a("actions.delete"),icon:s.jsx($,{}),onClick:l}]}]})]})}function ze({zone:e,locationId:n,fulfillmentSetId:t,type:i}){const{t:a}=x(),c=e.shipping_options.filter(l=>!y(l)),o=e.shipping_options.filter(l=>y(l));return s.jsxs("div",{children:[s.jsx(Z,{variant:"dashed"}),s.jsxs("div",{className:"flex flex-col gap-y-4 px-6 py-4",children:[s.jsxs("div",{className:"item-center flex justify-between",children:[s.jsx("span",{className:"text-ui-fg-subtle txt-small self-center font-medium",children:a(`stockLocations.shippingOptions.create.${i}.label`)}),s.jsx(z,{to:`/settings/locations/${n}/fulfillment-set/${t}/service-zone/${e.id}/shipping-option/create`,children:a("stockLocations.shippingOptions.create.action")})]}),!!c.length&&s.jsx("div",{className:"shadow-elevation-card-rest bg-ui-bg-subtle grid divide-y rounded-md",children:c.map(l=>s.jsx(I,{option:l,locationId:n,fulfillmentSetId:t},l.id))})]}),s.jsx(Z,{variant:"dashed"}),s.jsxs("div",{className:"flex flex-col gap-y-4 px-6 py-4",children:[s.jsxs("div",{className:"item-center flex justify-between",children:[s.jsx("span",{className:"text-ui-fg-subtle txt-small self-center font-medium",children:a("stockLocations.shippingOptions.create.returns.label")}),s.jsx(z,{to:`/settings/locations/${n}/fulfillment-set/${t}/service-zone/${e.id}/shipping-option/create?is_return`,children:a("stockLocations.shippingOptions.create.action")})]}),!!o.length&&s.jsx("div",{className:"shadow-elevation-card-rest bg-ui-bg-subtle grid divide-y rounded-md",children:o.map(l=>s.jsx(I,{option:l,locationId:n,fulfillmentSetId:t},l.id))})]})]})}function Fe({zone:e,locationId:n,fulfillmentSetId:t,type:i}){const{t:a}=x(),c=O(),[o,l]=r.useState(!0),{mutateAsync:v}=be(t,e.id),j=async()=>{await c({title:a("general.areYouSure"),description:a("stockLocations.serviceZones.delete.confirmation",{name:e.name}),confirmText:a("actions.delete"),cancelText:a("actions.cancel")})&&await v(void 0,{onError:m=>{f.error(m.message)},onSuccess:()=>{f.success(a("stockLocations.serviceZones.delete.successToast",{name:e.name}))}})},P=r.useMemo(()=>{const p=e.geo_zones.filter(d=>d.type==="country"),m=p.map(({country_code:d})=>ue.find(u=>u.iso_2===d)).filter(d=>!!d);return p.length!==m.length&&console.warn("Some countries are missing in the static countries list",p.filter(d=>!m.find(u=>u.iso_2===d.country_code)).map(d=>d.country_code)),m.sort((d,u)=>d.name.localeCompare(u.name))},[e.geo_zones]),[C,E]=r.useMemo(()=>{const p=e.shipping_options,m=p.filter(u=>!y(u)).length,d=p.filter(y).length;return[m,d]},[e.shipping_options]);return s.jsxs("div",{className:"flex flex-col",children:[s.jsxs("div",{className:"flex flex-row items-center justify-between gap-x-4 px-6 py-4",children:[s.jsx(A,{children:s.jsx(D,{})}),s.jsxs("div",{className:"grow-1 flex flex-1 flex-col",children:[s.jsx(h,{size:"small",leading:"compact",weight:"plus",children:e.name}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(V,{variant:"base",list:P.map(p=>p.display_name),inline:!0,n:1}),s.jsx("span",{children:"·"}),s.jsx(h,{className:"text-ui-fg-subtle txt-small",children:a(`stockLocations.shippingOptions.fields.count.${i}`,{count:C})}),s.jsx("span",{children:"·"}),s.jsx(h,{className:"text-ui-fg-subtle txt-small",children:a("stockLocations.shippingOptions.fields.count.returns",{count:E})})]})]}),s.jsxs("div",{className:"flex grow-0 items-center gap-4",children:[s.jsx(xe,{size:"small",onClick:()=>l(p=>!p),variant:"transparent",children:s.jsx(he,{style:{transform:`rotate(${o?180:0}deg)`,transition:".2s transform ease-in-out"}})}),s.jsx(g,{groups:[{actions:[{label:a("actions.edit"),icon:s.jsx(_,{}),to:`/settings/locations/${n}/fulfillment-set/${t}/service-zone/${e.id}/edit`},{label:a("stockLocations.serviceZones.manageAreas.action"),icon:s.jsx(D,{}),to:`/settings/locations/${n}/fulfillment-set/${t}/service-zone/${e.id}/areas`}]},{actions:[{label:a("actions.delete"),icon:s.jsx($,{}),onClick:j}]}]})]})]}),o&&s.jsx(ze,{fulfillmentSetId:t,locationId:n,type:i,zone:e})]})}function q(e){const{t:n}=x(),t=O(),{fulfillmentSet:i,locationName:a,locationId:c,type:o}=e,l=!!i,v=!!(i!=null&&i.service_zones.length),{mutateAsync:j}=pe(c),{mutateAsync:P}=ye(i==null?void 0:i.id),C=async()=>{await j({name:`${a} ${o==="pickup"?"pick up":o}`,type:o},{onSuccess:()=>{f.success(n(`stockLocations.fulfillmentSets.enable.${o}`))},onError:m=>{f.error(m.message)}})},E=async()=>{await t({title:n("general.areYouSure"),description:n("stockLocations.fulfillmentSets.disable.confirmation",{name:i==null?void 0:i.name}),confirmText:n("actions.disable"),cancelText:n("actions.cancel")})&&await P(void 0,{onSuccess:()=>{f.success(n(`stockLocations.fulfillmentSets.disable.${o}`))},onError:d=>{f.error(d.message)}})},p=i?[{actions:[{icon:s.jsx(F,{}),label:n("stockLocations.serviceZones.create.action"),to:`/settings/locations/${c}/fulfillment-set/${i.id}/service-zones/create`}]},{actions:[{icon:s.jsx($,{}),label:n("actions.disable"),onClick:E}]}]:[{actions:[{icon:s.jsx(F,{}),label:n("actions.enable"),onClick:C}]}];return s.jsx(S,{className:"p-0",children:s.jsxs("div",{className:"flex flex-col divide-y",children:[s.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[s.jsx(L,{level:"h2",children:n(`stockLocations.fulfillmentSets.${o}.header`)}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx(Ne,{color:l?"green":"grey",children:n(l?"statuses.enabled":"statuses.disabled")}),s.jsx(g,{groups:p})]})]}),l&&!v&&s.jsx("div",{className:"flex items-center justify-center py-8 pt-6",children:s.jsx(M,{message:n("stockLocations.serviceZones.fields.noRecords"),className:"h-fit",action:{to:`/settings/locations/${c}/fulfillment-set/${i.id}/service-zones/create`,label:n("stockLocations.serviceZones.create.action")}})}),v&&s.jsx("div",{className:"flex flex-col divide-y",children:i==null?void 0:i.service_zones.map(m=>s.jsx(Fe,{zone:m,type:o,locationId:c,fulfillmentSetId:i.id},m.id))})]})})}var Ze=({location:e})=>{const n=me(),{t}=x(),{mutateAsync:i}=fe(e.id),a=O(),c=async()=>{await a({title:t("general.areYouSure"),description:t("stockLocations.delete.confirmation",{name:e.name}),verificationText:e.name,verificationInstruction:t("general.typeToConfirm"),confirmText:t("actions.delete"),cancelText:t("actions.cancel")})&&await i(void 0,{onSuccess:()=>{f.success(t("stockLocations.create.successToast",{name:e.name})),n("/settings/locations",{replace:!0})},onError:l=>{f.error(l.message)}})};return s.jsx(g,{groups:[{actions:[{icon:s.jsx(_,{}),label:t("actions.edit"),to:"edit"},{icon:s.jsx(G,{}),label:t("stockLocations.edit.viewInventory"),to:`/inventory?location_id=${e.id}`}]},{actions:[{icon:s.jsx($,{}),label:t("actions.delete"),onClick:c}]}]})};function Re({location:e}){var a,c,o;const{t:n}=x(),{count:t}=ge({limit:1,fields:"id"}),i=!!((a=e.sales_channels)!=null&&a.length);return s.jsxs(S,{className:"flex flex-col px-6 py-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(L,{level:"h2",children:n("stockLocations.salesChannels.header")}),s.jsx(g,{groups:[{actions:[{label:n("actions.edit"),to:"sales-channels",icon:s.jsx(_,{})}]}]})]}),i?s.jsxs("div",{className:"flex flex-col gap-y-4 pt-4",children:[s.jsxs("div",{className:"grid grid-cols-[28px_1fr] items-center gap-x-3",children:[s.jsx(A,{children:s.jsx(ke,{className:"text-ui-fg-subtle"})}),s.jsx(V,{n:3,className:"text-ui-fg-base",inline:!0,list:((c=e.sales_channels)==null?void 0:c.map(l=>l.name))??[]})]}),s.jsx(h,{className:"text-ui-fg-subtle",size:"small",leading:"compact",children:n("stockLocations.salesChannels.connectedTo",{count:(o=e.sales_channels)==null?void 0:o.length,total:t})})]}):s.jsx(M,{className:"h-fit pb-2 pt-6",action:{label:n("stockLocations.salesChannels.action"),to:"sales-channels"},message:n("stockLocations.salesChannels.noChannels")})]})}var Be=Re;function He({location:e}){const{t:n}=x(),{fulfillment_providers:t}=ve({stock_location_id:e.id,fields:"id",is_enabled:!0});return s.jsxs(S,{className:"flex flex-col px-6 py-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(L,{level:"h2",children:n("stockLocations.fulfillmentProviders.header")}),s.jsx(g,{groups:[{actions:[{label:n("actions.edit"),to:"fulfillment-providers",icon:s.jsx(_,{})}]}]})]}),t!=null&&t.length?s.jsx("div",{className:"flex flex-col gap-y-4 pt-4",children:s.jsx("div",{className:"grid grid-cols-[28px_1fr] items-center gap-x-3 gap-y-3",children:t==null?void 0:t.map(i=>s.jsxs(r.Fragment,{children:[s.jsx(A,{children:s.jsx(X,{className:"text-ui-fg-subtle"})}),s.jsx("div",{className:"txt-compact-small",children:W(i.id)})]},i.id))})}):s.jsx(M,{className:"h-fit pb-2 pt-6 text-center",action:{label:n("stockLocations.fulfillmentProviders.action"),to:"fulfillment-providers"},message:n("stockLocations.fulfillmentProviders.noProviders")})]})}var Ie=He,us=()=>{const e=ae(),{location_id:n}=oe(),{stock_location:t,isPending:i,isError:a,error:c}=Y(n,{fields:N},{initialData:e}),{getWidgets:o}=re();if(i||!t)return s.jsx(le,{mainSections:3,sidebarSections:2,showJSON:!0});if(a)throw c;return s.jsxs(T,{widgets:{after:o("location.details.after"),before:o("location.details.before"),sideAfter:o("location.details.side.after"),sideBefore:o("location.details.side.before")},data:t,showJSON:!0,hasOutlet:!0,children:[s.jsx(T.Main,{children:s.jsx(Ae,{location:t})}),s.jsxs(T.Sidebar,{children:[s.jsx(Be,{location:t}),s.jsx(Ie,{location:t})]})]})};export{ms as Breadcrumb,us as Component,fs as loader};
