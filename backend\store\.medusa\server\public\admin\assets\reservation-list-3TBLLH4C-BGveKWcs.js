import{a as h}from"./chunk-WYX5PIA3-DoOUp1ge.js";import{P as x}from"./chunk-P3UUX2T6-CnJzifYv.js";import{u as v,_ as j}from"./chunk-X3LH6P65-BtKDvzuz.js";import{a as b,j as e,b as d,b2 as g,H as y,T as _,B as T,L as N,r as P,cZ as w,dU as S,A as k}from"./index-Bwql5Dzz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as C}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as R}from"./chunk-C76H5USB-ByRPKhW7.js";import{u as z}from"./use-prompt-pbDx0Sfe.js";import{P as L}from"./pencil-square-6wRbnn1C.js";import{T as q}from"./trash-BBylvTAG.js";import{C as A}from"./container-Dqi2woPF.js";import{c as D}from"./index-BxZ1678G.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var E=({reservation:r})=>{const{t}=d(),s=z(),{mutateAsync:i}=S(r.id),a=async()=>{await s({title:t("general.areYouSure"),description:t("reservations.deleteWarning"),confirmText:t("actions.delete"),cancelText:t("actions.cancel")})&&await i()};return e.jsx(k,{groups:[{actions:[{label:t("actions.edit"),to:`${r.id}/edit`,icon:e.jsx(L,{})}]},{actions:[{label:t("actions.delete"),onClick:a,icon:e.jsx(q,{})}]}]})},o=D(),I=()=>{const{t:r}=d();return P.useMemo(()=>[o.accessor("inventory_item",{header:r("fields.sku"),cell:({getValue:t})=>{const s=t();return!s||!s.sku?e.jsx(x,{}):e.jsx("div",{className:"flex size-full items-center overflow-hidden",children:e.jsx("span",{className:"truncate",children:s.sku})})}}),o.accessor("description",{header:r("fields.description"),cell:({getValue:t})=>{const s=t();return s?e.jsx("div",{className:"flex size-full items-center overflow-hidden",children:e.jsx("span",{className:"truncate",children:s})}):e.jsx(x,{})}}),o.accessor("created_at",{header:r("fields.created"),cell:({getValue:t})=>{const s=t();return e.jsx(h,{date:s})}}),o.accessor("quantity",{header:()=>e.jsx("div",{className:"flex size-full items-center justify-end overflow-hidden text-right",children:e.jsx("span",{className:"truncate",children:r("fields.quantity")})}),cell:({getValue:t})=>{const s=t();return e.jsx("div",{className:"flex size-full items-center justify-end overflow-hidden text-right",children:e.jsx("span",{className:"truncate",children:s})})}}),o.display({id:"actions",cell:({row:t})=>{const s=t.original;return e.jsx(E,{reservation:s})}})],[r])},H=()=>{const{t:r}=d(),{stock_locations:t}=w({limit:1e3}),s=[];if(t){const i={type:"select",options:t.map(a=>({label:a.name,value:a.id})),key:"location_id",searchable:!0,label:r("fields.location")};s.push(i)}return s.push({type:"date",key:"created_at",label:r("fields.createdAt")}),s},B=({pageSize:r=20,prefix:t})=>{const s=R(["location_id","offset","created_at","quantity","updated_at","order"],t),{location_id:i,created_at:a,updated_at:n,quantity:p,offset:c,...l}=s;return{searchParams:{limit:r,offset:c?parseInt(c):void 0,location_id:i,created_at:a?JSON.parse(a):void 0,updated_at:n?JSON.parse(n):void 0,...l},raw:s}},u=20,F=()=>{const{t:r}=d(),{searchParams:t}=B({pageSize:u}),{reservations:s,count:i,isPending:a,isError:n,error:p}=g({...t}),c=H(),l=I(),{table:f}=v({data:s||[],columns:l,count:i,enablePagination:!0,getRowId:m=>m.id,pageSize:u});if(n)throw p;return e.jsxs(A,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsxs("div",{children:[e.jsx(y,{children:r("reservations.domain")}),e.jsx(_,{className:"text-ui-fg-subtle",size:"small",children:r("reservations.subtitle")})]}),e.jsx(T,{variant:"secondary",size:"small",asChild:!0,children:e.jsx(N,{to:"create",children:r("actions.create")})})]}),e.jsx(j,{table:f,columns:l,pageSize:u,count:i,isLoading:a,filters:c,pagination:!0,navigateTo:m=>m.id,search:!1})]})},be=()=>{const{getWidgets:r}=b();return e.jsx(C,{widgets:{before:r("reservation.list.before"),after:r("reservation.list.after")},children:e.jsx(F,{})})};export{be as Component};
