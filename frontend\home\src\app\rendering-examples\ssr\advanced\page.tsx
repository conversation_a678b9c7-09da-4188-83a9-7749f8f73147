import Link from 'next/link';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import { createTranslation } from '@/components/Providers/server-i18n';

// Function to fetch complex data for the "advanced" route
async function getAdvancedAnalytics() {
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return {
    pageViews: {
      today: Math.floor(Math.random() * 10000),
      weekly: Math.floor(Math.random() * 50000),
      monthly: Math.floor(Math.random() * 200000),
    },
    userSessions: {
      averageDuration: Math.floor(Math.random() * 500) + ' seconds',
      bounceRate: Math.floor(Math.random() * 100) + '%',
      newUsers: Math.floor(Math.random() * 5000),
    },
    serverLoad: {
      current: Math.floor(Math.random() * 100) + '%',
      average: Math.floor(Math.random() * 100) + '%',
      peak: Math.floor(Math.random() * 100) + '%',
    },
    generatedAt: new Date().toISOString(),
  };
}

export default async function SSRAdvancedPage() {
  // For server components, we use the server-side translation utility
  const { t } = await createTranslation('common');

  // Fetch data on the server for this specific route
  const advancedData = await getAdvancedAnalytics();

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="ssr" />

      <main className="container mx-auto px-4 pb-12">
        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">{t('rendering.realTimeAnalytics')}</h2>
          <p className="mb-6">{t('rendering.ssrAdvancedDescription')}</p>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="bg-card rounded-lg border p-6">
              <h3 className="mb-4 font-semibold">{t('rendering.pageViews')}</h3>
              <div className="space-y-4">
                <div>
                  <div className="mb-1 flex justify-between">
                    <span className="text-sm">{t('rendering.today')}</span>
                    <span className="text-sm font-medium">
                      {advancedData.pageViews.today.toLocaleString()}
                    </span>
                  </div>
                  <div className="bg-muted h-2 w-full rounded-full">
                    <div
                      className="h-2 rounded-full bg-blue-500"
                      style={{ width: `${(advancedData.pageViews.today / 10000) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="mb-1 flex justify-between">
                    <span className="text-sm">{t('rendering.weekly')}</span>
                    <span className="text-sm font-medium">
                      {advancedData.pageViews.weekly.toLocaleString()}
                    </span>
                  </div>
                  <div className="bg-muted h-2 w-full rounded-full">
                    <div
                      className="h-2 rounded-full bg-green-500"
                      style={{ width: `${(advancedData.pageViews.weekly / 50000) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="mb-1 flex justify-between">
                    <span className="text-sm">{t('rendering.monthly')}</span>
                    <span className="text-sm font-medium">
                      {advancedData.pageViews.monthly.toLocaleString()}
                    </span>
                  </div>
                  <div className="bg-muted h-2 w-full rounded-full">
                    <div
                      className="h-2 rounded-full bg-purple-500"
                      style={{ width: `${(advancedData.pageViews.monthly / 200000) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg border p-6">
              <h3 className="mb-4 font-semibold">{t('rendering.userSessions')}</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span>{t('rendering.avgDuration')}:</span>
                  <span className="font-medium">{advancedData.userSessions.averageDuration}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>{t('rendering.bounceRate')}:</span>
                  <span className="font-medium">{advancedData.userSessions.bounceRate}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>{t('rendering.newUsers')}:</span>
                  <span className="font-medium">
                    {advancedData.userSessions.newUsers.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg border p-6">
              <h3 className="mb-4 font-semibold">{t('rendering.serverLoad')}</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span>{t('rendering.current')}:</span>
                  <span className="font-medium">{advancedData.serverLoad.current}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>{t('rendering.average')}:</span>
                  <span className="font-medium">{advancedData.serverLoad.average}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>{t('rendering.peak')}:</span>
                  <span className="font-medium">{advancedData.serverLoad.peak}</span>
                </div>
              </div>
            </div>
          </div>
          <p className="mt-4 text-xs">
            {t('rendering.generatedAt')}: {advancedData.generatedAt}
          </p>
        </div>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">{t('rendering.ssrAdvantages')}</h2>
          <ul className="list-disc space-y-2 pl-5">
            <li>{t('rendering.ssrAdvantage1')}</li>
            <li>{t('rendering.ssrAdvantage2')}</li>
            <li>{t('rendering.ssrAdvantage3')}</li>
            <li>{t('rendering.ssrAdvantage4')}</li>
          </ul>
        </div>

        <div className="mt-6 flex flex-wrap gap-3">
          <Link
            href="/rendering-examples/ssr"
            className="bg-secondary text-secondary-foreground hover:bg-secondary/90 rounded-md px-4 py-2 transition-colors"
          >
            {t('rendering.basicExample')}
          </Link>
          <Link
            href="/rendering-examples/ssr/with-data"
            className="bg-secondary text-secondary-foreground hover:bg-secondary/90 rounded-md px-4 py-2 transition-colors"
          >
            {t('rendering.withDataExample')}
          </Link>
          <Link
            href="/rendering-examples/ssr/router-example"
            className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 transition-colors"
          >
            {t('rendering.viewRouterExample')}
          </Link>
        </div>
      </main>
    </div>
  );
}
