import * as React from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

export interface I18nButtonSkeletonProps {
  /** Size variant matching the main component */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /** Optional className for the wrapper */
  className?: string;
  /** Optional i18n namespace */
  i18nNamespace?: string;
  /** i18n prefix for translation keys */
  i18nPrefix?: string;
}

/**
 * Skeleton loader for I18nButton
 * 
 * Automatically adapts to current theme (light/dark)
 */
export const I18nButtonSkeleton = ({
  size = 'icon',
  className,
  i18nNamespace,
  i18nPrefix = 'language.skeleton',
}: I18nButtonSkeletonProps) => {
  const { t } = useTranslation(i18nNamespace);
  
  // Get correct size class based on size prop
  const sizeClass = size === 'default' ? 'h-8 w-8' :
                    size === 'sm' ? 'h-7 w-7' :
                    size === 'lg' ? 'h-9 w-9' :
                    'h-8 w-8'; // icon size
  
  return (
    <div 
      className={cn('rounded-full overflow-hidden', className)}
      aria-label={t(`${i18nPrefix}.ariaLabel`, 'Loading language selector')}
      role="status"
    >
      <Skeleton className={cn('aspect-square', sizeClass)} />
    </div>
  );
};

export default I18nButtonSkeleton; 