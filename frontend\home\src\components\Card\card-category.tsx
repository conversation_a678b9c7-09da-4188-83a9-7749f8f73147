import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";

export interface CardCategoryProps {
  /**
   * Category title displayed on the card
   */
  title: string;
  /**
   * Image URL for the category
   */
  imageUrl: string;
  /**
   * Alternative text for the image
   */
  imageAlt?: string;
  /**
   * URL to navigate to when card is clicked
   */
  href: string;
  /**
   * Optional CSS class names
   */
  className?: string;
  /**
   * Callback function when card is clicked
   */
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
  /**
   * Optional data-testid for testing
   */
  testId?: string;
}

/**
 * CardCategory component displays a product category with an image and title
 * 
 * Used for displaying product categories like light bulbs, downlights, etc.
 */
export const CardCategory = ({
  title,
  imageUrl,
  imageAlt = "",
  href,
  className,
  onClick,
  testId = "card-category",
  ...props
}: CardCategoryProps) => {
  return (
    <Link 
      href={href}
      className={cn("block transition-all duration-200 hover:opacity-80", className)}
      onClick={onClick}
      data-testid={testId}
      {...props}
    >
      <Card className="overflow-hidden border-0 rounded-2xl bg-white shadow-sm h-[180px]">
        <div className="flex items-center justify-between h-full *:w-1/2">
          <div className="p-6">
            <h3 className="text-xl font-semibold text-gray-900" data-testid={`${testId}-title`}>
              {title}
            </h3>
          </div>
          <div className="h-full flex items-center justify-center">
            <Image
              src={imageUrl}
              alt={imageAlt || title}
              width={180}
              height={180}
              className="object-contain max-h-[180px] max-w-full"
              priority
              data-testid={`${testId}-image`}
            />
          </div>
        </div>
      </Card>
    </Link>
  );
};