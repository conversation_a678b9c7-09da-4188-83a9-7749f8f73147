import{u as b}from"./chunk-6FJ2PJHE-DxZ9pYOZ.js";import{u as x,a as f,b as T}from"./chunk-QQGBEPB7-Bvn4S9tT.js";import{a as j,j as t,b as n,bf as g,k as h,H as v,T as P,B as C,L as _,r as k,A}from"./index-Bwql5Dzz.js";import{u as D,_ as S}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as w}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{P as E}from"./pencil-square-6wRbnn1C.js";import{T as z}from"./trash-BBylvTAG.js";import{C as L}from"./container-Dqi2woPF.js";import{c as H}from"./index-BxZ1678G.js";import"./use-prompt-pbDx0Sfe.js";import"./prompt-BsR9zKsn.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-W7625H47-D4n0RxCV.js";import"./chunk-WYX5PIA3-DoOUp1ge.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";var B=({productType:e})=>{const{t:r}=n(),s=b(e.id);return t.jsx(A,{groups:[{actions:[{label:r("actions.edit"),icon:t.jsx(E,{}),to:`/settings/product-types/${e.id}/edit`}]},{actions:[{label:r("actions.delete"),icon:t.jsx(z,{}),onClick:s}]}]})},a=20,N=()=>{const{t:e}=n(),{searchParams:r,raw:s}=x({pageSize:a}),{product_types:l,count:i,isLoading:c,isError:m,error:u}=g(r,{placeholderData:h}),d=f(),p=q(),{table:y}=D({columns:p,data:l,count:i,getRowId:o=>o.id,pageSize:a});if(m)throw u;return t.jsxs(L,{className:"divide-y p-0",children:[t.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[t.jsxs("div",{children:[t.jsx(v,{children:e("productTypes.domain")}),t.jsx(P,{className:"text-ui-fg-subtle",size:"small",children:e("productTypes.subtitle")})]}),t.jsx(C,{size:"small",variant:"secondary",asChild:!0,children:t.jsx(_,{to:"create",children:e("actions.create")})})]}),t.jsx(S,{table:y,filters:d,isLoading:c,columns:p,pageSize:a,count:i,orderBy:[{key:"value",label:e("fields.value")},{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}],navigateTo:({original:o})=>o.id,queryObject:s,pagination:!0,search:!0})]})},R=H(),q=()=>{const e=T();return k.useMemo(()=>[...e,R.display({id:"actions",cell:({row:r})=>t.jsx(B,{productType:r.original})})],[e])},he=()=>{const{getWidgets:e}=j();return t.jsx(w,{widgets:{after:e("product_type.list.after"),before:e("product_type.list.before")},children:t.jsx(N,{})})};export{he as Component};
