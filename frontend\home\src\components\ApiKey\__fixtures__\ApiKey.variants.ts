import type { <PERSON><PERSON><PERSON><PERSON> } from '../ApiKey-Types';

/**
 * API Key variants for different permission types and configurations
 */

/**
 * API Key with "All" permissions - Regular user
 */
export const allPermissionsKey: ApiKey = {
  id: 'key-all-permissions',
  name: 'all-permissions-key',
  permissionType: 'All',
  createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
  lastUsed: new Date().toISOString(),
};

/**
 * API Key with "All" permissions - Service account (for display purposes only)
 */
export const serviceAccountKey: ApiKey = {
  id: 'key-service-account',
  name: 'service-account-key',
  permissionType: 'All',
  createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
  lastUsed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
};

/**
 * API Key with "Restricted" permissions - Only models and files
 */
export const restrictedModelsFilesKey: ApiKey = {
  id: 'key-restricted-models-files',
  name: 'api-models-files',
  permissionType: 'Restricted',
  resourcePermissions: {
    models: 'Read',
    modelCapabilities: 'None',
    assistants: 'None',
    threads: 'None',
    evals: 'None',
    fineTuning: 'None',
    files: 'Read',
  },
  createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
  lastUsed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
};

/**
 * API Key with "Restricted" permissions - Only assistants with write access
 */
export const restrictedAssistantsKey: ApiKey = {
  id: 'key-restricted-assistants',
  name: 'assistant-api-key',
  permissionType: 'Restricted',
  resourcePermissions: {
    models: 'Read',
    modelCapabilities: 'None',
    assistants: 'Write',
    threads: 'Write',
    evals: 'None',
    fineTuning: 'None',
    files: 'None',
  },
  createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
  lastUsed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
};

/**
 * API Key with "Read only" permissions
 */
export const readOnlyKey: ApiKey = {
  id: 'key-read-only',
  name: 'read-only-access',
  permissionType: 'Read only',
  createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
  lastUsed: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
};

/**
 * Collection of all API key variants
 */
export const apiKeyVariants = [
  allPermissionsKey,
  serviceAccountKey,
  restrictedModelsFilesKey,
  restrictedAssistantsKey,
  readOnlyKey,
];
