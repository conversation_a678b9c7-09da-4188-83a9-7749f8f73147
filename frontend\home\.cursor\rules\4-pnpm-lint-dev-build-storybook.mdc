---
description: 
globs: 
alwaysApply: false
---
Loop through the following steps until there are no errors:
1. Remove `node_modules`, `pnpm-lock.yaml`, `package-lock.json`, `yarn.lock` `.next`.
2. Run `pnpm install` and check for errors.
3. Run `pnpm lint` and check for errors.
   - If errors are found, fix them automatically if possible and repeat `pnpm lint`.
4. Run `pnpm dev` and check for errors.
   - If errors occur, attempt to resolve them and rerun `pnpm dev`.
5. Run `pnpm build` to ensure the application runs without issues.
   - If errors appear, log them, attempt a fix, and restart the loop.
6. Run `pnpm storybook` to ensure the application runs without issues.
   - If errors appear, log them, attempt a fix, and restart the loop.
   - If `pnpm storybook` runs successfully, execute `pnpm test-storybook` while Storybook remains running.
   - If `pnpm test-storybook` encounters errors, log them, attempt a fix, and restart the loop until there are no errors.

Continue this process until all commands execute successfully without errors.

## 🌟 **STRICT RULES 🚨**

❌ DO NOT MODIFY ESLINT CONFIGURATION in any way, including .eslintrc.js, .eslintignore, or any ESLint rule settings.
❌ DO NOT DISABLE ESLINT RULES using /* eslint-disable */ or similar comments.
✅ FOLLOW ALL EXISTING ESLINT RULES to ensure strict code quality and maintainability.