import type { <PERSON>a, StoryObj } from "@storybook/react";
import { ProductSummaryRating } from "../product-summary-rating";

const meta: Meta<typeof ProductSummaryRating> = {
  title: "UI/Product/ProductSummaryRating",
  component: ProductSummaryRating,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "radio",
      options: ["default", "compact", "detailed"],
    },
    isLoading: {
      control: "boolean",
    },
    onRatingClick: { action: "rating clicked" },
  },
};

export default meta;
type Story = StoryObj<typeof ProductSummaryRating>;

// Default example (like the image you provided)
export const Default: Story = {
  args: {
    averageRating: 4.9,
    totalReviews: 267,
    ratingDistribution: {
      5: 243,
      4: 14,
      3: 0,
      2: 0,
      1: 0,
    },
  },
};

// Compact variant
export const Compact: Story = {
  args: {
    ...Default.args,
    variant: "compact",
  },
};

// Detailed variant
export const Detailed: Story = {
  args: {
    ...Default.args,
    variant: "detailed",
  },
};

// Loading state
export const Loading: Story = {
  args: {
    ...Default.args,
    isLoading: true,
  },
};

// No reviews state
export const NoReviews: Story = {
  args: {
    averageRating: 0,
    totalReviews: 0,
    ratingDistribution: {
      5: 0,
      4: 0,
      3: 0,
      2: 0,
      1: 0,
    },
  },
};

// Mixed ratings example
export const MixedRatings: Story = {
  args: {
    averageRating: 3.7,
    totalReviews: 120,
    ratingDistribution: {
      5: 50,
      4: 40,
      3: 15,
      2: 10,
      1: 5,
    },
  },
};