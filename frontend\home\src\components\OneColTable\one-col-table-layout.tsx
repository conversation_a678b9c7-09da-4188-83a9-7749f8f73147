'use client';

import React, { useCallback } from 'react';
import { Search, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { cn } from '@/lib/utils';
import OneColTable from './one-col-table';
import type { TableItem } from './types';
import { useTablePagination, useTableSortAndFilter } from './hooks';
import { DEFAULT_LIMITS } from './constants';

export interface OneColTableLayoutProps {
  /** Items to display in the table */
  items: TableItem[];
  /** Title for the table section */
  title?: string;
  /** Whether to show the search input */
  showSearch?: boolean;
  /** Whether to show the sorting options */
  showSort?: boolean;
  /** Whether to show pagination */
  showPagination?: boolean;
  /** Number of items per page */
  pageSize?: number;
  /** Initial sort field */
  initialSortField?: keyof TableItem;
  /** Initial sort order */
  initialSortOrder?: 'asc' | 'desc';
  /** Fields that can be sorted */
  sortableFields?: Array<{
    field: keyof TableItem;
    label: string;
  }>;
  /** Whether to enable infinite loading (load more) instead of pagination */
  infiniteLoading?: boolean;
  /** Whether to use virtualization for large datasets */
  virtualized?: boolean;
  /** Size variant for the table */
  size?: 'sm' | 'md' | 'lg';
  /** Style variant for the table */
  variant?: 'primary' | 'secondary' | 'outline';
  /** Optional CSS class for the wrapper */
  className?: string;
  /** Row click handler */
  onRowClick?: (item: TableItem, index: number) => void;
  /** Whether the table is in a loading state */
  loading?: boolean;
}

/**
 * OneColTableLayout - Higher-level layout component with search, sorting, and pagination
 */
export const OneColTableLayout: React.FC<OneColTableLayoutProps> = ({
  items,
  title,
  showSearch = true,
  showSort = true,
  showPagination = true,
  pageSize = DEFAULT_LIMITS.PAGE_SIZE,
  initialSortField = 'timestamp',
  initialSortOrder = 'desc',
  sortableFields = [
    { field: 'timestamp', label: 'Date' },
    { field: 'content', label: 'Content' },
  ],
  infiniteLoading = false,
  virtualized = false,
  size = 'md',
  variant = 'primary',
  className,
  onRowClick,
  loading = false,
}) => {
  // Set up sorting and filtering
  const {
    sortBy,
    sortOrder,
    filterTerm,
    processedItems,
    updateSortBy,
    toggleSortOrder,
    updateFilterTerm,
    clearFilters,
  } = useTableSortAndFilter(items, initialSortField, initialSortOrder);

  // Set up pagination
  const {
    currentPage,
    totalPages,
    currentItems,
    visibleItems,
    hasMoreItems,
    nextPage,
    previousPage,
    goToPage,
    loadMore,
  } = useTablePagination(processedItems, 1, pageSize);

  // Handle search input
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      updateFilterTerm(e.target.value);
    },
    [updateFilterTerm],
  );

  // Clear search
  const handleClearSearch = useCallback(() => {
    clearFilters();
  }, [clearFilters]);

  // Handle sort change
  const handleSortChange = useCallback(
    (value: string) => {
      const field = value as keyof TableItem;
      updateSortBy(field);
    },
    [updateSortBy],
  );

  // Handle sort order toggle
  const handleSortOrderToggle = useCallback(() => {
    toggleSortOrder();
  }, [toggleSortOrder]);

  // Handle load more
  const handleLoadMore = useCallback(() => {
    loadMore();
  }, [loadMore]);

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header section with title, search, and sorting */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        {title && <h2 className="text-xl font-semibold">{title}</h2>}

        <div className="flex flex-1 items-center space-x-2 sm:justify-end">
          {/* Search input */}
          {showSearch && (
            <div className="relative flex-1 sm:max-w-xs">
              <Search className="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
              <Input
                placeholder="Search..."
                value={filterTerm}
                onChange={handleSearchChange}
                className="pr-8 pl-8"
              />
              {filterTerm && (
                <button
                  onClick={handleClearSearch}
                  className="text-muted-foreground hover:text-foreground absolute top-2.5 right-2"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          )}

          {/* Sort options */}
          {showSort && sortableFields.length > 0 && (
            <div className="flex items-center space-x-1">
              <Select value={String(sortBy)} onValueChange={handleSortChange}>
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  {sortableFields.map((option) => (
                    <SelectItem key={String(option.field)} value={String(option.field)}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="icon"
                onClick={handleSortOrderToggle}
                title={`Sort ${sortOrder === 'asc' ? 'ascending' : 'descending'}`}
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Table component */}
      <OneColTable
        items={infiniteLoading ? visibleItems : currentItems}
        size={size}
        variant={variant}
        virtualized={virtualized}
        onRowClick={onRowClick}
        loading={loading}
        showLoadMore={infiniteLoading && hasMoreItems}
        onLoadMore={handleLoadMore}
        hasMore={hasMoreItems}
      />

      {/* Pagination */}
      {showPagination && !infiniteLoading && totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={currentPage > 1 ? () => previousPage() : undefined}
                className={cn(currentPage === 1 && 'pointer-events-none opacity-50')}
              />
            </PaginationItem>

            {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
              let pageNumber: number;

              // Show pages around current page
              if (totalPages <= 5) {
                pageNumber = i + 1;
              } else if (currentPage <= 3) {
                pageNumber = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNumber = totalPages - 4 + i;
              } else {
                pageNumber = currentPage - 2 + i;
              }

              return (
                <PaginationItem key={pageNumber}>
                  <PaginationLink
                    isActive={pageNumber === currentPage}
                    onClick={() => goToPage(pageNumber)}
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            })}

            <PaginationItem>
              <PaginationNext
                onClick={currentPage < totalPages ? () => nextPage() : undefined}
                className={cn(currentPage === totalPages && 'pointer-events-none opacity-50')}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
};

export default OneColTableLayout;
