// section-popular-products.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { SectionPopularProducts } from '../section-popular-products';
import { defaultFixture, emptyFixture, loadingFixture } from '../__fixtures__/section-popular-products.fixtures';

// Mock Next.js components
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} fill={undefined} />
  },
}));

jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ href, children, ...props }: any) => (
    <a href={href} {...props}>
      {children}
    </a>
  ),
}));

describe('SectionPopularProducts', () => {
  // Test rendering
  test('renders the section with title and products', () => {
    render(<SectionPopularProducts {...defaultFixture} />);
    
    // Check section title
    expect(screen.getByText('สินค้ายอดนิยม')).toBeInTheDocument();
    
    // Check "View All" link
    expect(screen.getByText('ดูทั้งหมด')).toBeInTheDocument();
    expect(screen.getByText('ดูทั้งหมด').closest('a')).toHaveAttribute('href', '/products/popular');
    
    // Check products rendering
    interface Product {
      id: string;
      name: string;
      brand: string;
      price: number;
    }

    (defaultFixture.products as Product[]).forEach((product: Product) => {
      expect(screen.getByText(product.name)).toBeInTheDocument();
      expect(screen.getByText(product.brand)).toBeInTheDocument();
      expect(screen.getByText(product.price.toLocaleString())).toBeInTheDocument();
    });
  });

  test('renders empty state when no products are provided', () => {
    render(<SectionPopularProducts {...emptyFixture} />);
    
    expect(screen.getByText('ไม่พบสินค้ายอดนิยมในขณะนี้')).toBeInTheDocument();
  });

  test('renders with loading state for products being added to cart', () => {
    render(<SectionPopularProducts {...loadingFixture} />);
    
    // Get all add to cart buttons
    const addToCartButtons = screen.getAllByTestId('add-to-cart-button');
    
    // First and third products should be in loading state (disabled)
    expect(addToCartButtons[0]).toBeDisabled();
    expect(addToCartButtons[2]).toBeDisabled();
    
    // Second product should not be disabled
    expect(addToCartButtons[1]).not.toBeDisabled();
  });

  test('renders without "View All" link when showViewAll is false', () => {
    render(<SectionPopularProducts {...defaultFixture} showViewAll={false} />);
    
    expect(screen.queryByText('ดูทั้งหมด')).not.toBeInTheDocument();
  });

  test('renders with custom title', () => {
    const customTitle = 'สินค้าแนะนำ';
    render(<SectionPopularProducts {...defaultFixture} title={customTitle} />);
    
    expect(screen.getByText(customTitle)).toBeInTheDocument();
  });

  // Test interactions
  test('calls onAddToCart when add to cart button is clicked', () => {
    const mockOnAddToCart = jest.fn();
    render(
      <SectionPopularProducts 
        {...defaultFixture} 
        onAddToCart={mockOnAddToCart} 
        addingToCartIds={[]}
      />
    );
    
    // Get all add to cart buttons
    const addToCartButtons = screen.getAllByTestId('add-to-cart-button');
    
    // Click the first button
    fireEvent.click(addToCartButtons[0]);
    
    // Check if onAddToCart was called with correct product ID
    expect(mockOnAddToCart).toHaveBeenCalledWith(defaultFixture.products[0].id);
  });

  test('does not call onAddToCart when button is disabled', () => {
    const mockOnAddToCart = jest.fn();
    render(
      <SectionPopularProducts 
        {...defaultFixture} 
        onAddToCart={mockOnAddToCart} 
        addingToCartIds={[defaultFixture.products[0].id]}
      />
    );
    
    // Get all add to cart buttons
    const addToCartButtons = screen.getAllByTestId('add-to-cart-button');
    
    // Click the first button (which should be disabled)
    fireEvent.click(addToCartButtons[0]);
    
    // Check that onAddToCart was not called
    expect(mockOnAddToCart).not.toHaveBeenCalled();
  });

  // Test accessibility
  test('has proper accessibility attributes', () => {
    render(<SectionPopularProducts {...defaultFixture} />);
    
    // Section should have an aria-label
    const section = screen.getByTestId('section-popular-products');
    expect(section).toHaveAttribute('aria-label', 'Popular products section');
    
    // "View All" link should have an aria-label
    const viewAllLink = screen.getByText('ดูทั้งหมด').closest('a');
    expect(viewAllLink).toHaveAttribute('aria-label', 'ดูสินค้าทั้งหมด');
  });

  // Test responsive design
  test('applies correct grid classes based on columns prop', () => {
    const customColumns = {
      mobile: 1,
      tablet: 2,
      desktop: 4
    };
    
    render(
      <SectionPopularProducts 
        {...defaultFixture} 
        columns={customColumns}
      />
    );
    
    const grid = screen.getByTestId('products-grid');
    
    expect(grid.className).toContain(`grid-cols-${customColumns.mobile}`);
    expect(grid.className).toContain(`md:grid-cols-${customColumns.tablet}`);
    expect(grid.className).toContain(`lg:grid-cols-${customColumns.desktop}`);
  });
});