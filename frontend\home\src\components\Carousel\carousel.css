/* Global carousel styles */
.carousel {
    position: relative;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
  }
  
  /* Slide Styles */
  .swiper-slide {
    transition: transform 0.3s ease;
    height: auto;
  }
  
  /* Navigation Buttons */
  .swiper-button-next,
  .swiper-button-prev {
    color: theme(--color-primary, #3b82f6);
    background: rgba(255, 255, 255, 0.8);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  .swiper-button-next:after,
  .swiper-button-prev:after {
    font-size: 18px;
    font-weight: bold;
  }
  
  .swiper-button-disabled {
    opacity: 0.35;
  }
  
  /* Pagination Bullets */
  .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0.7;
    transition: all 0.3s ease;
  }
  
  .swiper-pagination-bullet-active {
    background: theme(--color-primary, #3b82f6);
    width: 12px;
    height: 12px;
    opacity: 1;
  }
  
  /* Default Carousel Variant */
  .carousel-default .swiper-slide {
    width: 100%;
  }
  
  /* Multiple Carousel Variant */
  .carousel-multiple .swiper-slide {
    height: 100%;
  }
  
  /* Coverflow Carousel Variant */
  .carousel-coverflow {
    padding: 30px 0;
  }
  
  .carousel-coverflow .swiper-slide {
    width: 300px; /* Adjustable width for the slides */
    height: auto;
    background-position: center;
    background-size: cover;
    transition: all 0.3s ease;
  }
  
  .carousel-coverflow .swiper-slide-active {
    transform: scale(1.05);
    z-index: 2;
  }
  
  .carousel-coverflow .swiper-slide-prev,
  .carousel-coverflow .swiper-slide-next {
    opacity: 0.8;
    z-index: 1;
  }
  
  /* Brand Coverflow specific styles to match the "Brand Slide" Figma design */
  .brand-coverflow-carousel {
    padding: 40px 0;
  }
  
  .brand-coverflow-carousel .swiper-slide {
    width: 320px; /* Match width from the design */
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }
  
  .brand-coverflow-carousel .brand-slide {
    aspect-ratio: 16/9; /* Maintain consistent aspect ratio */
    overflow: hidden;
  }
  
  .brand-coverflow-carousel .brand-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .carousel-coverflow .swiper-slide,
    .brand-coverflow-carousel .swiper-slide {
      width: 260px;
    }
    
    .swiper-button-next,
    .swiper-button-prev {
      width: 32px;
      height: 32px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
      font-size: 14px;
    }
  }
  
  @media (max-width: 480px) {
    .carousel-coverflow .swiper-slide,
    .brand-coverflow-carousel .swiper-slide {
      width: 220px;
    }
    
    .carousel-coverflow {
      padding: 20px 0;
    }
  }