import { ExampleNavigation } from '@/components/rendering-examples/ExampleNavigation';
import { ThemeToggle } from '@/components/theme-toggle';
import { I18nButton } from '@/components/I18nButton';
import Link from 'next/link';

// Define the routes for this example
const routes = [
  { path: 'home', labelKey: 'rendering.routes.home', defaultLabel: 'Home' },
  { path: 'about', labelKey: 'rendering.routes.about', defaultLabel: 'About' },
  { path: 'products', labelKey: 'rendering.routes.products', defaultLabel: 'Products' },
  { path: 'contact', labelKey: 'rendering.routes.contact', defaultLabel: 'Contact' },
];

// This page demonstrates ISR (Incremental Static Regeneration)
// The layout is statically generated at build time but can be regenerated
export default function ISRRouterLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <Link href="/rendering-examples/isr" className="text-primary font-semibold hover:underline">
          ← Back to ISR Example
        </Link>

        <div className="flex items-center gap-2">
          <ThemeToggle />
          <I18nButton />
        </div>
      </div>

      <h1 className="mb-6 text-3xl font-bold">
        Incremental Static Regeneration (ISR) Router Example
      </h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">How ISR Routing Works</h2>
        <p className="mb-4">
          With Incremental Static Regeneration, pages are initially rendered like SSG (at build
          time), but can be regenerated in the background at a specified interval or on-demand.
        </p>
        <p>
          This approach combines the performance benefits of static generation with the ability to
          update content regularly. Theme and language preferences are maintained client-side.
        </p>
        <p className="text-muted-foreground mt-4 text-sm">
          Layout generated at: {new Date().toISOString()}
        </p>
      </div>

      <ExampleNavigation basePath="/rendering-examples/isr/router-example" routes={routes} />

      {children}
    </div>
  );
}
