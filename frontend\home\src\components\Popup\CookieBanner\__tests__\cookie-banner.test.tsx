// components/CookieBanner/CookieBanner.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { CookieBanner } from '../cookie-banner';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock setTimeout
jest.useFakeTimers();

describe('CookieBanner', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    window.localStorage.clear();
    // Clear all timers
    jest.clearAllTimers();
  });

  it('renders the banner when no consent is stored', () => {
    render(<CookieBanner />);
    
    // Banner should not be visible immediately due to timeout
    expect(screen.queryByText('Cookies & Privacy')).not.toBeInTheDocument();
    
    // Advance timers to make banner appear
    jest.advanceTimersByTime(1000);
    
    // Now the banner should be visible
    expect(screen.getByText('Cookies & Privacy')).toBeInTheDocument();
    expect(screen.getByText(/We use cookies to enhance your browsing experience/)).toBeInTheDocument();
    expect(screen.getByText('Accept All')).toBeInTheDocument();
    expect(screen.getByText('Decline All')).toBeInTheDocument();
    expect(screen.getByText('Customize')).toBeInTheDocument();
  });

  it('does not render when consent is already stored', () => {
    // Set consent in localStorage
    window.localStorage.setItem('cookieConsent', 'all');
    
    render(<CookieBanner />);
    jest.advanceTimersByTime(1000);
    
    // Banner should not be visible
    expect(screen.queryByText('Cookies & Privacy')).not.toBeInTheDocument();
  });

  it('calls onAcceptAll with correct preferences when Accept All is clicked', () => {
    const onAcceptAll = jest.fn();
    
    render(<CookieBanner onAcceptAll={onAcceptAll} />);
    jest.advanceTimersByTime(1000);
    
    // Click Accept All button
    fireEvent.click(screen.getByText('Accept All'));
    
    // Check if onAcceptAll was called with correct preferences
    expect(onAcceptAll).toHaveBeenCalledWith({
      necessary: true,
      analytics: true,
      marketing: true,
    });
    
    // Check if localStorage was updated
    expect(window.localStorage.getItem('cookieConsent')).toBe('all');
  });

  it('calls onDeclineAll when Decline All is clicked', () => {
    const onDeclineAll = jest.fn();
    
    render(<CookieBanner onDeclineAll={onDeclineAll} />);
    jest.advanceTimersByTime(1000);
    
    // Click Decline All button
    fireEvent.click(screen.getByText('Decline All'));
    
    // Check if onDeclineAll was called
    expect(onDeclineAll).toHaveBeenCalled();
    
    // Check if localStorage was updated
    expect(window.localStorage.getItem('cookieConsent')).toBe('necessary');
  });

  it('shows customize view when Customize is clicked', () => {
    render(<CookieBanner />);
    jest.advanceTimersByTime(1000);
    
    // Click Customize button
    fireEvent.click(screen.getByText('Customize'));
    
    // Check if customize view is shown
    expect(screen.getByText('Cookie Preferences')).toBeInTheDocument();
    expect(screen.getByLabelText('Necessary Cookies')).toBeInTheDocument();
    expect(screen.getByLabelText('Analytics Cookies')).toBeInTheDocument();
    expect(screen.getByLabelText('Marketing Cookies')).toBeInTheDocument();
  });

  it('allows toggling preferences in customize view', () => {
    const onSavePreferences = jest.fn();
    
    render(<CookieBanner onSavePreferences={onSavePreferences} />);
    jest.advanceTimersByTime(1000);
    
    // Click Customize button
    fireEvent.click(screen.getByText('Customize'));
    
    // Toggle Analytics on
    fireEvent.click(screen.getByLabelText('Analytics Cookies'));
    
    // Toggle Marketing on
    fireEvent.click(screen.getByLabelText('Marketing Cookies'));
    
    // Save preferences
    fireEvent.click(screen.getByText('Save Preferences'));
    
    // Check if onSavePreferences was called with correct preferences
    expect(onSavePreferences).toHaveBeenCalledWith({
      necessary: true,
      analytics: true,
      marketing: true,
    });
    
    // Check if localStorage was updated
    expect(window.localStorage.getItem('cookieConsent')).toBe('custom');
  });

  it('respects initialPreferences prop', () => {
    const initialPreferences = {
      analytics: true,
      marketing: false,
    };
    
    render(<CookieBanner initialPreferences={initialPreferences} />);
    jest.advanceTimersByTime(1000);
    
    // Click Customize button
    fireEvent.click(screen.getByText('Customize'));
    
    // Check initial state of checkboxes
    expect(screen.getByLabelText('Necessary Cookies')).toBeChecked();
    expect(screen.getByLabelText('Analytics Cookies')).toBeChecked();
    expect(screen.getByLabelText('Marketing Cookies')).not.toBeChecked();
  });

  it('respects position prop', () => {
    render(<CookieBanner position="top" />);
    jest.advanceTimersByTime(1000);
    
    const banner = screen.getByRole('dialog');
    
    // Check if the banner has the top class
    expect(banner).toHaveClass('top-0');
    expect(banner).not.toHaveClass('bottom-0');
  });

  it('allows returning to main view from customize view', () => {
    render(<CookieBanner />);
    jest.advanceTimersByTime(1000);
    
    // Click Customize button
    fireEvent.click(screen.getByText('Customize'));
    
    // Check if customize view is shown
    expect(screen.getByText('Cookie Preferences')).toBeInTheDocument();
    
    // Click Back button
    fireEvent.click(screen.getByText('Back'));
    
    // Check if main view is shown again
    expect(screen.queryByText('Cookie Preferences')).not.toBeInTheDocument();
    expect(screen.getByText('Accept All')).toBeInTheDocument();
  });

  it('has a disabled checkbox for necessary cookies', () => {
    render(<CookieBanner />);
    jest.advanceTimersByTime(1000);
    
    // Click Customize button
    fireEvent.click(screen.getByText('Customize'));
    
    // Check if necessary cookies checkbox is disabled
    const necessaryCheckbox = screen.getByLabelText('Necessary Cookies');
    expect(necessaryCheckbox).toBeDisabled();
    expect(necessaryCheckbox).toBeChecked();
  });
})
