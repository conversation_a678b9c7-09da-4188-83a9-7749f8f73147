import{u as z,a as F,b as G}from"./chunk-IDYOGWSG-DuqxOJwc.js";import{a2 as I,ad as N,a4 as B,R as D,j as e,b as x,a8 as H,a9 as M,r as p,dI as O,dT as V,t as y,E as K,B as b,V as L}from"./index-Bwql5Dzz.js";import{u as $,_ as q}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{K as Q}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as n,u as Z}from"./chunk-JGQGO74V-DtHO1ucg.js";import{C as S}from"./checkbox-B4pL6X49.js";import{c as J}from"./index-BxZ1678G.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-WYX5PIA3-DoOUp1ge.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./prompt-BsR9zKsn.js";var U=I({customer_ids:N(B()).min(1)}),d=10,W=({customerGroupId:a})=>{const{t:s}=x(),{handleSuccess:h}=Z(),t=H({defaultValues:{customer_ids:[]},resolver:M(U)}),{setValue:r}=t,[i,m]=p.useState({});p.useEffect(()=>{r("customer_ids",Object.keys(i).filter(o=>i[o]),{shouldDirty:!0,shouldTouch:!0})},[i,r]);const{searchParams:l,raw:C}=z({pageSize:d}),j=F(),{customers:_,count:f,isLoading:T,isError:k,error:v}=O({fields:"id,email,first_name,last_name,*groups",...l}),R=o=>{const c=typeof o=="function"?o(i):o,u=Object.keys(c);r("customer_ids",u,{shouldDirty:!0,shouldTouch:!0}),m(c)},g=Y(),{table:A}=$({data:_??[],columns:g,count:f,enablePagination:!0,enableRowSelection:o=>{var c;return!((c=o.original.groups)!=null&&c.map(u=>u.id).includes(a))},getRowId:o=>o.id,pageSize:d,rowSelection:{state:i,updater:R}}),{mutateAsync:w,isPending:P}=V(a),E=t.handleSubmit(async o=>{await w(o.customer_ids,{onSuccess:()=>{y.success(s("customerGroups.customers.add.successToast",{count:o.customer_ids.length})),h(`/customer-groups/${a}`)},onError:c=>{y.error(c.message)}})});if(k)throw v;return e.jsx(n.Form,{form:t,children:e.jsxs(Q,{className:"flex h-full flex-col overflow-hidden",onSubmit:E,children:[e.jsx(n.Header,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[t.formState.errors.customer_ids&&e.jsx(K,{variant:"error",children:t.formState.errors.customer_ids.message}),e.jsx(n.Close,{asChild:!0,children:e.jsx(b,{variant:"secondary",size:"small",children:s("actions.cancel")})}),e.jsx(b,{type:"submit",variant:"primary",size:"small",isLoading:P,children:s("actions.save")})]})}),e.jsx(n.Body,{className:"size-full overflow-hidden",children:e.jsx(q,{table:A,columns:g,pageSize:d,count:f,filters:j,orderBy:[{key:"email",label:s("fields.email")},{key:"first_name",label:s("fields.firstName")},{key:"last_name",label:s("fields.lastName")},{key:"has_account",label:s("customers.hasAccount")},{key:"created_at",label:s("fields.createdAt")},{key:"updated_at",label:s("fields.updatedAt")}],isLoading:T,layout:"fill",search:"autofocus",queryObject:C,noRecords:{message:s("customerGroups.customers.add.list.noRecordsMessage")}})})]})})},X=J(),Y=()=>{const{t:a}=x(),s=G();return p.useMemo(()=>[X.display({id:"select",header:({table:t})=>e.jsx(S,{checked:t.getIsSomePageRowsSelected()?"indeterminate":t.getIsAllPageRowsSelected(),onCheckedChange:r=>t.toggleAllPageRowsSelected(!!r)}),cell:({row:t})=>{const r=!t.getCanSelect(),i=t.getIsSelected()||r,m=e.jsx(S,{checked:i,disabled:r,onCheckedChange:l=>t.toggleSelected(!!l),onClick:l=>{l.stopPropagation()}});return r?e.jsx(L,{content:a("customerGroups.customers.alreadyAddedTooltip"),side:"right",children:m}):m}}),...s],[a,s])},Pe=()=>{const{id:a}=D();return e.jsx(n,{children:e.jsx(W,{customerGroupId:a})})};export{Pe as Component};
