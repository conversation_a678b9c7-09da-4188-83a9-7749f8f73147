import{r as e}from"./index-Bwql5Dzz.js";var c=Object.defineProperty,n=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,o=(t,a,r)=>a in t?c(t,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[a]=r,s=(t,a)=>{for(var r in a)i.call(a,r)&&o(t,r,a[r]);if(n)for(var r of n(a))f.call(a,r)&&o(t,r,a[r]);return t},p=(t,a)=>{var r={};for(var l in t)i.call(t,l)&&a.indexOf(l)<0&&(r[l]=t[l]);if(t!=null&&n)for(var l of n(t))a.indexOf(l)<0&&f.call(t,l)&&(r[l]=t[l]);return r};const d=e.forwardRef((t,a)=>{var r=t,l=p(r,["color"]);return e.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:a},l),e.createElement("path",{fill:"#60A5FA",fillRule:"evenodd",d:"M13.935 11.183a2.32 2.32 0 0 1-2.318 2.319H3.383a2.32 2.32 0 0 1-2.318-2.319V3.817a2.32 2.32 0 0 1 2.318-2.319h1.691c.704 0 1.368.32 1.808.867l.348.433h4.387a2.32 2.32 0 0 1 2.318 2.319z",clipRule:"evenodd"}),e.createElement("path",{fill:"url(#a)",fillOpacity:.15,fillRule:"evenodd",d:"M13.935 11.183a2.32 2.32 0 0 1-2.318 2.319H3.383a2.32 2.32 0 0 1-2.318-2.319V3.817a2.32 2.32 0 0 1 2.318-2.319h1.691c.704 0 1.368.32 1.808.867l.348.433h4.387a2.32 2.32 0 0 1 2.318 2.319z",clipRule:"evenodd"}),e.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeOpacity:.15,strokeWidth:.5,d:"M7.034 2.955a.25.25 0 0 0 .196.093h4.387c1.142 0 2.068.926 2.068 2.069v6.066a2.07 2.07 0 0 1-2.068 2.069H3.383a2.07 2.07 0 0 1-2.068-2.069V3.817c0-1.143.926-2.069 2.068-2.069h1.691c.628 0 1.22.285 1.613.774z"}),e.createElement("g",{filter:"url(#b)"},e.createElement("path",{fill:"#60A5FA",d:"M1.065 7.283a2.32 2.32 0 0 1 2.318-2.318h8.234a2.32 2.32 0 0 1 2.318 2.318v3.9a2.32 2.32 0 0 1-2.318 2.318H3.383a2.32 2.32 0 0 1-2.318-2.318z"}),e.createElement("path",{fill:"url(#c)",fillOpacity:.2,d:"M1.065 7.283a2.32 2.32 0 0 1 2.318-2.318h8.234a2.32 2.32 0 0 1 2.318 2.318v3.9a2.32 2.32 0 0 1-2.318 2.318H3.383a2.32 2.32 0 0 1-2.318-2.318z"})),e.createElement("defs",null,e.createElement("linearGradient",{id:"a",x1:7.5,x2:7.5,y1:1.498,y2:13.502,gradientUnits:"userSpaceOnUse"},e.createElement("stop",null),e.createElement("stop",{offset:1,stopOpacity:0})),e.createElement("linearGradient",{id:"c",x1:7.5,x2:7.5,y1:4.965,y2:13.501,gradientUnits:"userSpaceOnUse"},e.createElement("stop",{stopColor:"#fff"}),e.createElement("stop",{offset:1,stopColor:"#fff",stopOpacity:0})),e.createElement("filter",{id:"b",width:12.87,height:8.537,x:1.065,y:4.965,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse"},e.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),e.createElement("feBlend",{in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),e.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),e.createElement("feOffset",{dy:-.5}),e.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),e.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),e.createElement("feBlend",{in2:"shape",result:"effect1_innerShadow_6347_11987"}),e.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),e.createElement("feOffset",{dy:.5}),e.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),e.createElement("feColorMatrix",{values:"0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"}),e.createElement("feBlend",{in2:"effect1_innerShadow_6347_11987",result:"effect2_innerShadow_6347_11987"}))))});d.displayName="FolderIllustration";export{d as F};
