import{u as f,a as b,b as g}from"./chunk-YG4XFXE6-TB5N21Oh.js";import{a as _,j as e,b as y,aR as T,H as j,k as x}from"./index-Bwql5Dzz.js";import{u as E,_ as h}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as v}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{C as O}from"./container-Dqi2woPF.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-WYX5PIA3-DoOUp1ge.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-NNBHHXXN-09hoI4Mn.js";import"./chunk-PDWBYQOW-BedvhUOI.js";import"./chunk-MWVM4TYO-bKUcYSnf.js";import"./chunk-7DXVXBSA-CCclXhoB.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./react-country-flag.esm-BcG425Ss.js";import"./index-BxZ1678G.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";var D=["id","status","created_at","email","display_id","payment_status","fulfillment_status","total","currency_code"],S=["*customer","*sales_channel"],L=`${D.join(",")},${S.join(",")}`,t=20,A=()=>{const{t:r}=y(),{searchParams:i,raw:o}=f({pageSize:t}),{orders:m,count:a,isError:p,error:d,isLoading:l}=T({fields:L,...i},{placeholderData:x}),n=b(),s=g({}),{table:u}=E({data:m??[],columns:s,enablePagination:!0,count:a,pageSize:t});if(p)throw d;return e.jsxs(O,{className:"divide-y p-0",children:[e.jsx("div",{className:"flex items-center justify-between px-6 py-4",children:e.jsx(j,{children:r("orders.domain")})}),e.jsx(h,{columns:s,table:u,pagination:!0,navigateTo:c=>`/orders/${c.original.id}`,filters:n,count:a,search:!0,isLoading:l,pageSize:t,orderBy:[{key:"display_id",label:r("orders.fields.displayId")},{key:"created_at",label:r("fields.createdAt")},{key:"updated_at",label:r("fields.updatedAt")}],queryObject:o,noRecords:{message:r("orders.list.noRecordsMessage")}})]})},nr=()=>{const{getWidgets:r}=_();return e.jsx(v,{widgets:{after:r("order.list.after"),before:r("order.list.before")},hasOutlet:!1,children:e.jsx(A,{})})};export{nr as Component};
