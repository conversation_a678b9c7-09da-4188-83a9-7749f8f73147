import { TableItem } from '../types';

// Mock data for OneColTable stories and tests
export const mockItems: TableItem[] = [
  {
    id: 'batch_67d7a8282548819083507d13a254931',
    content: 'Batch Processing #1',
    secondaryContent: 'Completed successfully with 34 items',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'completed',
  },
  {
    id: 'batch_67d7a826b4c8190a2dc17b47d03fee',
    content: 'Batch Processing #2',
    secondaryContent: 'In progress, 12 items remaining',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'in-progress',
  },
  {
    id: 'batch_67d7a256bec8190a838138da4090a4',
    content: 'Batch Processing #3',
    secondaryContent: 'Queued for execution',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'pending',
  },
  {
    id: 'batch_67d7a2448ec8190bc7d78b2c347e74',
    content: 'Batch Processing #4',
    secondaryContent: 'Completed with warnings (2)',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'warning',
  },
  {
    id: 'batch_67d7a230a4c8190b4dacbc3c7cf56d',
    content: 'Batch Processing #5',
    secondaryContent: 'Failed due to network error',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'error',
  },
  {
    id: 'batch_67d7a21521881909ed1e5e0d38967',
    content: 'Batch Processing #6',
    secondaryContent: 'Completed successfully with 24 items',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'completed',
  },
  {
    id: 'batch_67d7a1f40ac81908ca51e307507e4',
    content: 'Batch Processing #7',
    secondaryContent: 'In progress, 6 items remaining',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'in-progress',
  },
  {
    id: 'batch_67d7a1bf1548190a90529d719de3f9',
    content: 'Batch Processing #8',
    secondaryContent: 'Queued for execution',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'pending',
  },
  {
    id: 'batch_67d7a1a2ae8190812205e6a9a3184e',
    content: 'Batch Processing #9',
    secondaryContent: 'Completed with warnings (3)',
    timestamp: new Date('2025-03-17T11:42:00Z'),
    status: 'warning',
  },
  {
    id: 'batch_67d7a178ca08190aa18942058dce84',
    content: 'Batch Processing #10',
    secondaryContent: 'Failed due to timeout',
    timestamp: new Date('2025-03-17T11:41:00Z'),
    status: 'error',
  },
  {
    id: 'batch_67d7a15c2808190a20eb0bd07920f8',
    content: 'Batch Processing #11',
    secondaryContent: 'Completed successfully with 18 items',
    timestamp: new Date('2025-03-17T11:41:00Z'),
    status: 'completed',
  },
  {
    id: 'batch_67d7a13fdb88190aee3800ed79fb61',
    content: 'Batch Processing #12',
    secondaryContent: 'In progress, 9 items remaining',
    timestamp: new Date('2025-03-17T11:41:00Z'),
    status: 'in-progress',
  },
  {
    id: 'batch_67d7a1121f48190b985e280c96c467',
    content: 'Batch Processing #13',
    secondaryContent: 'Queued for execution',
    timestamp: new Date('2025-03-17T11:41:00Z'),
    status: 'pending',
  },
  {
    id: 'batch_67d7a80ebdec8190ae917e487dc3899',
    content: 'Batch Processing #14',
    secondaryContent: 'Completed with warnings (1)',
    timestamp: new Date('2025-03-17T11:41:00Z'),
    status: 'warning',
  },
  {
    id: 'batch_67d7a80c68848190d4bf4f172c181e',
    content: 'Batch Processing #15',
    secondaryContent: 'Failed due to validation errors',
    timestamp: new Date('2025-03-17T11:41:00Z'),
    status: 'error',
  },
];

// Different size variations for demonstration
export const sizedItems = {
  small: mockItems.slice(0, 2),
  medium: mockItems.slice(0, 3),
  large: mockItems.slice(0, 4),
};

// Items with different states
export const stateItems = {
  completed: mockItems.filter((item) => item.status === 'completed'),
  inProgress: mockItems.filter((item) => item.status === 'in-progress'),
  pending: mockItems.filter((item) => item.status === 'pending'),
  warning: mockItems.filter((item) => item.status === 'warning'),
  error: mockItems.filter((item) => item.status === 'error'),
};

// Items for loading state demonstration
export const loadingItems = mockItems.slice(0, 3);

// Items for error state demonstration
export const errorItems = mockItems.slice(0, 3);

// Generate large dataset for virtualization tests
export const virtualizedItems: TableItem[] = Array.from({ length: 1000 }).map((_, i) => ({
  id: `item-${i}`,
  content: `Virtualized Item ${i + 1}`,
  secondaryContent: `Description for item ${i + 1}`,
  timestamp: new Date(Date.now() - i * 3600000),
  status:
    i % 5 === 0
      ? 'completed'
      : i % 5 === 1
        ? 'in-progress'
        : i % 5 === 2
          ? 'pending'
          : i % 5 === 3
            ? 'warning'
            : 'error',
}));

// Large dataset for testing with fewer items than virtualized
export const largeDatasetItems: TableItem[] = Array.from({ length: 100 }).map((_, i) => ({
  id: `large-item-${i}`,
  content: `Large Dataset Item ${i + 1}`,
  secondaryContent: `Description for large dataset item ${i + 1}`,
  timestamp: new Date(Date.now() - i * 3600000),
  status:
    i % 5 === 0
      ? 'completed'
      : i % 5 === 1
        ? 'in-progress'
        : i % 5 === 2
          ? 'pending'
          : i % 5 === 3
            ? 'warning'
            : 'error',
}));
