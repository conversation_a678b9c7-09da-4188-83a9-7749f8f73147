import{a as t}from"./chunk-WYX5PIA3-DoOUp1ge.js";import{a as u}from"./chunk-MSDRGCRR-BLk8RuFZ.js";import{b as d,r as o,j as a,Y as i}from"./index-Bwql5Dzz.js";import{c as n}from"./index-BxZ1678G.js";var l=n(),j=()=>{const{t:e}=d();return o.useMemo(()=>[l.accessor("value",{header:()=>e("fields.value"),cell:({getValue:s})=>a.jsx(u,{text:s()})}),l.accessor("created_at",{header:()=>e("fields.createdAt"),cell:({getValue:s})=>a.jsx(t,{date:s()})}),l.accessor("updated_at",{header:()=>e("fields.updatedAt"),cell:({getValue:s})=>a.jsx(t,{date:s()})})],[e])},c=n(),h=()=>o.useMemo(()=>[c.accessor("value",{cell:({getValue:e})=>a.jsx(i,{size:"2xsmall",children:e()})}),c.accessor("label",{cell:({row:e})=>{const{label:s,description:r}=e.original;return a.jsx("div",{className:" py-4",children:a.jsxs("div",{className:"flex h-full w-full flex-col justify-center",children:[a.jsx("span",{className:"truncate font-medium",children:s}),a.jsx("span",{className:"truncate",children:r||"-"})]})})}})],[]);export{h as a,j as u};
