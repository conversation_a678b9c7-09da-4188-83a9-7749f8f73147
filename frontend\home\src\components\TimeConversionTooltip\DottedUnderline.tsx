'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { DottedUnderlineProps } from './types';

/**
 * DottedUnderline - A visual indicator for hoverable/interactive text
 * Renders a dotted line underneath text to indicate interaction is available
 */
export const DottedUnderline = React.forwardRef<HTMLDivElement, DottedUnderlineProps>(
  ({ isVisible, className, zIndex = 60, ...props }, ref) => {
    if (!isVisible) return null;

    return (
      <div
        ref={ref}
        className={cn('absolute bottom-0 left-0 h-px w-full', className)}
        style={{ zIndex }}
        {...props}
      >
        <svg width="100%" height="2" xmlns="http://www.w3.org/2000/svg">
          <line
            x1="0"
            y1="1"
            x2="100%"
            y2="1"
            stroke="currentColor"
            strokeWidth="1"
            strokeDasharray="2 2"
          />
        </svg>
      </div>
    );
  },
);

DottedUnderline.displayName = 'DottedUnderline';
