import { render, screen, act } from "@testing-library/react";
import { axe, toHaveNoViolations } from "jest-axe";
import FlashSaleBanner from "../banner-flash-sale";
import "@testing-library/jest-dom";

// Add jest-axe matchers
expect.extend(toHaveNoViolations);

describe("FlashSaleBanner", () => {
  beforeEach(() => {
    // Mock date to have consistent test results
    jest.useFakeTimers();
    jest.setSystemTime(new Date("2025-05-06T12:00:00Z"));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test("renders with default props", () => {
    const endTime = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
    render(<FlashSaleBanner endTime={endTime} />);
    
    // Check title text is present
    expect(screen.getByText("FLASH SALE")).toBeInTheDocument();
    
    // Check subtitle text is present
    expect(screen.getByText("แสงอรุณนครินทร์")).toBeInTheDocument();
    
    // Check timer is rendered
    expect(screen.getByText("01")).toBeInTheDocument(); // Hours
    expect(screen.getByText("00")).toBeInTheDocument(); // Minutes
  });

  test("renders with discount badge when showDiscount is true", () => {
    const endTime = new Date(Date.now() + 60 * 60 * 1000);
    render(
      <FlashSaleBanner 
        endTime={endTime} 
        discount="-11%" 
        showDiscount={true} 
      />
    );
    
    expect(screen.getByText("-11%")).toBeInTheDocument();
  });

  test("countdown timer updates correctly", () => {
    const endTime = new Date(Date.now() + 3600000); // 1 hour
    render(<FlashSaleBanner endTime={endTime} />);
    
    // Initial time displayed
    expect(screen.getByText("01")).toBeInTheDocument(); // Hours
    
    // Advance time by 1 minute
    act(() => {
      jest.advanceTimersByTime(60000);
    });
    
    // Time should be updated
    expect(screen.getByText("59")).toBeInTheDocument(); // Minutes
  });

  test("calls onTimeEnd callback when timer reaches zero", () => {
    const onTimeEnd = jest.fn();
    const endTime = new Date(Date.now() + 1000); // 1 second from now
    
    render(
      <FlashSaleBanner 
        endTime={endTime}
        onTimeEnd={onTimeEnd}
      />
    );
    
    // Advance time past end time
    act(() => {
      jest.advanceTimersByTime(2000);
    });
    
    // Callback should have been called
    expect(onTimeEnd).toHaveBeenCalledTimes(1);
  });

  test("applies variant classes correctly", () => {
    const endTime = new Date(Date.now() + 60 * 60 * 1000);
    
    const { container } = render(
      <FlashSaleBanner 
        endTime={endTime}
        variant="primary"
      />
    );
    
    // Check the correct variant classes are applied
    expect(container.firstChild).toHaveClass("bg-blue-50");
    
    // Timer boxes should have primary variant
    const timerBoxes = screen.getAllByText(/\d{2}/); // Match any two digits
    expect(timerBoxes[0]).toHaveClass("bg-blue-400");
  });

  test("applies size classes correctly", () => {
    const endTime = new Date(Date.now() + 60 * 60 * 1000);
    
    const { container } = render(
      <FlashSaleBanner 
        endTime={endTime}
        size="lg"
      />
    );
    
    // Check the correct size classes are applied
    expect(container.firstChild).toHaveClass("p-4");
  });

  test("renders icon in the correct position", () => {
    const endTime = new Date(Date.now() + 60 * 60 * 1000);
    
    const { rerender } = render(
      <FlashSaleBanner 
        endTime={endTime}
        iconPosition="left"
      />
    );
    
    // Check title text and ensure icon position
    const titleElement = screen.getByText("FLASH SALE");
    const parentDiv = titleElement.parentElement;
    
    // When icon is left, the icon should be before the title
    expect(parentDiv?.previousElementSibling).not.toBeNull();
    
    // Rerender with right position
    rerender(
      <FlashSaleBanner 
        endTime={endTime}
        iconPosition="right"
      />
    );
    
    // When icon is right, the icon should be after the title
    expect(parentDiv?.nextElementSibling).not.toBeNull();
  });

  test("has proper accessibility attributes", () => {
    const endTime = new Date(Date.now() + 60 * 60 * 1000);
    
    render(<FlashSaleBanner endTime={endTime} />);
    
    const banner = screen.getByTestId("flash-sale-banner");
    expect(banner).toHaveAttribute("role", "alert");
    expect(banner).toHaveAttribute("aria-live", "polite");
  });

  test("passes accessibility tests (axe)", async () => {
    const endTime = new Date(Date.now() + 60 * 60 * 1000);
    
    const { container } = render(
      <FlashSaleBanner endTime={endTime} />
    );
    
    // Run axe accessibility tests
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});