import{u as x,a as c,T as u}from"./chunk-6XTDCUWJ-F7Y__Spi.js";import"./chunk-JHNHXN7U-DcODL6zh.js";import{a as d,j as t,b as g,f6 as T,H as b,T as f,k as h}from"./index-Bwql5Dzz.js";import{S as j}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{C as R}from"./container-Dqi2woPF.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./index-BxZ1678G.js";import"./chunk-THZJC662-D8uxQDqu.js";import"./chunk-EQTBJSBZ-C4fKII8C.js";import"./chunk-5OOAHPXU-0_mLYzlY.js";import"./react-country-flag.esm-BcG425Ss.js";import"./trash-BBylvTAG.js";import"./use-prompt-pbDx0Sfe.js";import"./prompt-BsR9zKsn.js";import"./Trans-VWqfqpAH.js";import"./x-mark-mini-DvSTI7zK.js";import"./check-BGSYwiWc.js";var a=20,v=()=>{const{t:e}=g(),{searchParams:i,raw:o}=x({pageSize:a}),{tax_regions:s,count:r,isPending:n,isError:m,error:p}=T({...i,order:"country_code",parent_id:"null"},{placeholderData:h}),{table:l}=c({count:r,data:s,pageSize:a});if(m)throw p;return t.jsx(R,{className:"divide-y p-0",children:t.jsxs(u,{action:{to:"create",label:e("actions.create")},isPending:n,queryObject:o,table:l,count:r,children:[t.jsx(b,{children:e("taxes.domain")}),t.jsx(f,{size:"small",className:"text-ui-fg-subtle text-pretty",children:e("taxRegions.list.hint")})]})})},Z=()=>{const{getWidgets:e}=d();return t.jsx(j,{widgets:{before:e("tax.list.before"),after:e("tax.list.after")},hasOutlet:!0,children:t.jsx(v,{})})};export{Z as Component};
