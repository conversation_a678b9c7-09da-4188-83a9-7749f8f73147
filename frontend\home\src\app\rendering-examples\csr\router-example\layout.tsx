'use client';

import { useTheme } from 'next-themes';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { ExampleNavigation } from '@/components/rendering-examples/ExampleNavigation';
import { ThemeToggle } from '@/components/theme-toggle';
import { I18nButton } from '@/components/I18nButton';
import Link from 'next/link';

// Define the routes for this example
const routes = [
  { path: 'home', labelKey: 'rendering.routes.home', defaultLabel: 'Home' },
  { path: 'about', labelKey: 'rendering.routes.about', defaultLabel: 'About' },
  { path: 'products', labelKey: 'rendering.routes.products', defaultLabel: 'Products' },
  { path: 'contact', labelKey: 'rendering.routes.contact', defaultLabel: 'Contact' },
];

export default function CSRRouterLayout({ children }: { children: React.ReactNode }) {
  const { t } = useTranslation();
  const { theme } = useTheme();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <Link href="/rendering-examples/csr" className="text-primary font-semibold hover:underline">
          ← {t('rendering.backToCSR', 'Back to CSR Example')}
        </Link>

        <div className="flex items-center gap-2">
          <ThemeToggle />
          <I18nButton />
          <div className="text-muted-foreground text-sm">
            {t('theme.current', 'Current theme')}: {theme}
          </div>
        </div>
      </div>

      <h1 className="mb-6 text-3xl font-bold">
        {t('rendering.csrTitle', 'Client-Side Rendering (CSR) Router Example')}
      </h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.csrRouterTitle', 'How CSR Routing Works')}
        </h2>
        <p className="mb-4">
          {t(
            'rendering.csrRouterDescription',
            'With Client-Side Rendering, the initial HTML is minimal, and JavaScript builds the page in the browser. Navigation between routes happens entirely in the browser without full page reloads.',
          )}
        </p>
        <p>
          {t(
            'rendering.csrRouterNote',
            'This approach provides a smooth, app-like experience with preserved state between route changes. Theme and language preferences persist naturally during navigation since the page never fully reloads.',
          )}
        </p>
      </div>

      <ExampleNavigation basePath="/rendering-examples/csr/router-example" routes={routes} />

      {children}
    </div>
  );
}
