/**
 * Permission type values for API keys
 */
type PermissionTypeValue = 'All' | 'Restricted' | 'Read only';

/**
 * Resource permission values
 */
type ResourcePermissionValue = 'None' | 'Read' | 'Write';

/**
 * Owner type values
 */
type OwnerTypeValue = 'You' | 'Service account';

/**
 * API key interface
 */
interface ApiKey {
  id: string;
  name?: string;
  permissionType: PermissionTypeValue;
  resourcePermissions?: {
    models?: ResourcePermissionValue;
    modelCapabilities?: ResourcePermissionValue;
    assistants?: ResourcePermissionValue;
    threads?: ResourcePermissionValue;
    evals?: ResourcePermissionValue;
    fineTuning?: ResourcePermissionValue;
    files?: ResourcePermissionValue;
    [key: string]: ResourcePermissionValue | undefined;
  };
  createdAt: string;
  lastUsed?: string;
  keyValue?: string;
  status?: 'active' | 'revoked' | 'expired';
  expiresAt?: string;
  requestCount?: number;
  tags?: string[];
  environment?: 'development' | 'staging' | 'production';
  projectId?: string;
  createdBy?: string;
  ipRestrictions?: string[];
  domainRestrictions?: string[];
  rateLimit?: number;
  usageLog?: {
    timestamp: string;
    ipAddress: string;
    endpoint: string;
    status: number;
  }[];
}

/**
 * Form values for creating or editing an API key
 */
interface ApiKeyFormValues {
  name: string;
  permissionType: PermissionTypeValue;
  ownerType: OwnerTypeValue;
  resourcePermissions: {
    models: ResourcePermissionValue;
    modelCapabilities: ResourcePermissionValue;
    assistants: ResourcePermissionValue;
    threads: ResourcePermissionValue;
    evals: ResourcePermissionValue;
    fineTuning: ResourcePermissionValue;
    files: ResourcePermissionValue;
    [key: string]: ResourcePermissionValue;
  };
  project: string;
  environment?: string;
  expirationDays?: number;
  tags?: string[];
  ipRestrictions?: string[];
  domainRestrictions?: string[];
  rateLimit?: number;
}

/**
 * Sample projects for API key stories
 */
export const sampleProjects = [
  { value: 'project1', label: 'ZoomThai' },
  { value: 'project2', label: 'Project Alpha' },
  { value: 'project3', label: 'Demo Project' },
  { value: 'project4', label: 'AI Research' },
  { value: 'project5', label: 'ML Operations' },
];

/**
 * Sample environments for API key stories
 */
export const sampleEnvironments = [
  { value: 'development', label: 'Development' },
  { value: 'staging', label: 'Staging' },
  { value: 'production', label: 'Production' },
];

/**
 * Sample API key with "All" permissions
 */
export const sampleApiKeyAll: ApiKey = {
  id: 'key-123456',
  name: 'all-permissions-key',
  permissionType: 'All',
  createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
  lastUsed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
  status: 'active',
  expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days from now
  requestCount: 1254,
  tags: ['production', 'critical'],
  environment: 'production',
  projectId: 'project1',
  createdBy: '<EMAIL>',
  rateLimit: 5000,
};

/**
 * Sample API key with "Restricted" permissions
 */
export const sampleApiKeyRestricted: ApiKey = {
  id: 'key-234567',
  name: 'songpra-parser',
  permissionType: 'Restricted',
  resourcePermissions: {
    models: 'Read',
    modelCapabilities: 'Write',
    assistants: 'None',
    threads: 'None',
    evals: 'None',
    fineTuning: 'None',
    files: 'Read',
  },
  createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
  lastUsed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
  status: 'active',
  expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
  requestCount: 578,
  tags: ['development', 'parser'],
  environment: 'development',
  projectId: 'project2',
  createdBy: '<EMAIL>',
  ipRestrictions: ['***********/24'],
  domainRestrictions: ['api.example.com'],
  rateLimit: 1000,
};

/**
 * Sample API key with "Read only" permissions
 */
export const sampleApiKeyReadOnly: ApiKey = {
  id: 'key-345678',
  name: 'read-only-key',
  permissionType: 'Read only',
  createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
  lastUsed: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
  status: 'active',
  expiresAt: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString(), // 180 days from now
  requestCount: 3427,
  tags: ['readonly', 'monitoring'],
  environment: 'staging',
  projectId: 'project3',
  createdBy: '<EMAIL>',
  rateLimit: 10000,
};

/**
 * Sample API key without a name
 */
export const sampleApiKeyNoName: ApiKey = {
  id: 'key-456789',
  permissionType: 'All',
  createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
  lastUsed: undefined, // Never used
  status: 'active',
  expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
  requestCount: 0,
  environment: 'development',
  projectId: 'project4',
  createdBy: '<EMAIL>',
};

/**
 * Sample API key that is revoked
 */
export const sampleApiKeyRevoked: ApiKey = {
  id: 'key-567890',
  name: 'revoked-key',
  permissionType: 'Restricted',
  resourcePermissions: {
    models: 'Read',
    modelCapabilities: 'None',
    assistants: 'None',
    threads: 'None',
    evals: 'None',
    fineTuning: 'None',
    files: 'None',
  },
  createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days ago
  lastUsed: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(), // 25 days ago
  status: 'revoked',
  expiresAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago (expired)
  requestCount: 147,
  tags: ['deprecated'],
  environment: 'production',
  projectId: 'project5',
  createdBy: '<EMAIL>',
};

/**
 * Sample API key that is expired
 */
export const sampleApiKeyExpired: ApiKey = {
  id: 'key-678901',
  name: 'expired-key',
  permissionType: 'All',
  createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(), // 120 days ago
  lastUsed: new Date(Date.now() - 91 * 24 * 60 * 60 * 1000).toISOString(), // 91 days ago
  status: 'expired',
  expiresAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // Expired 30 days ago
  requestCount: 8742,
  tags: ['expired', 'archive'],
  environment: 'production',
  projectId: 'project1',
  createdBy: '<EMAIL>',
};

/**
 * All permission types available for API keys
 */
export const permissionTypes: PermissionTypeValue[] = ['All', 'Restricted', 'Read only'];

/**
 * All resource permission values
 */
export const resourcePermissionValues: ResourcePermissionValue[] = ['None', 'Read', 'Write'];

/**
 * Default form values for creating a new API key
 */
export const defaultCreateValues: ApiKeyFormValues = {
  name: '',
  permissionType: 'All',
  ownerType: 'You',
  resourcePermissions: {
    models: 'None',
    modelCapabilities: 'None',
    assistants: 'None',
    threads: 'None',
    evals: 'None',
    fineTuning: 'None',
    files: 'None',
  },
  project: sampleProjects[0].value,
  environment: 'development',
  expirationDays: 90,
  tags: [],
  ipRestrictions: [],
  domainRestrictions: [],
  rateLimit: 1000,
};
