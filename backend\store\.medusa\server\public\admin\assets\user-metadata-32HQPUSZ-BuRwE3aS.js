import{M as n}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as p,cS as d,e5 as u,j as a}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import{b as c}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var P=()=>{const{id:r}=p(),{user:t,isPending:s,isError:o,error:e}=d(r),{mutateAsync:i,isPending:m}=u(r);if(o)throw e;return a.jsx(c,{children:a.jsx(n,{isPending:s,isMutating:m,hook:i,metadata:t==null?void 0:t.metadata})})};export{P as Component};
