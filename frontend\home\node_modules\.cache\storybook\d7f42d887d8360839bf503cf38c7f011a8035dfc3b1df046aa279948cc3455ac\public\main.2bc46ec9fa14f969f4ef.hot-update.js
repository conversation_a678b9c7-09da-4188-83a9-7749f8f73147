"use strict";
self["webpackHotUpdateshadcn_timeline"]("main",{

/***/ "./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[8].use[1]!./node_modules/postcss-loader/dist/cjs.js!./src/styles/globals.css":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[8].use[1]!./node_modules/postcss-loader/dist/cjs.js!./src/styles/globals.css ***!
  \********************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/css-loader/dist/runtime/sourceMaps.js */ "./node_modules/css-loader/dist/runtime/sourceMaps.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../node_modules/css-loader/dist/runtime/api.js */ "./node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1__);
// Imports


var ___CSS_LOADER_EXPORT___ = _node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_1___default()((_node_modules_css_loader_dist_runtime_sourceMaps_js__WEBPACK_IMPORTED_MODULE_0___default()));
// Module
___CSS_LOADER_EXPORT___.push([module.id, `/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-200: oklch(88.5% 0.062 18.334);
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-orange-100: oklch(95.4% 0.038 75.164);
    --color-orange-400: oklch(75% 0.183 55.934);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-orange-600: oklch(64.6% 0.222 41.116);
    --color-amber-50: oklch(98.7% 0.022 95.277);
    --color-amber-100: oklch(96.2% 0.059 95.617);
    --color-amber-500: oklch(76.9% 0.188 70.08);
    --color-amber-600: oklch(66.6% 0.179 58.318);
    --color-amber-800: oklch(47.3% 0.137 46.201);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-blue-50: oklch(97% 0.014 254.604);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-blue-900: oklch(37.9% 0.146 265.522);
    --color-indigo-500: oklch(58.5% 0.233 277.117);
    --color-indigo-600: oklch(51.1% 0.262 276.966);
    --color-indigo-700: oklch(45.7% 0.24 277.023);
    --color-indigo-800: oklch(39.8% 0.195 277.366);
    --color-indigo-900: oklch(35.9% 0.144 278.697);
    --color-purple-50: oklch(97.7% 0.014 308.299);
    --color-purple-100: oklch(94.6% 0.033 307.174);
    --color-purple-400: oklch(71.4% 0.203 305.504);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-purple-600: oklch(55.8% 0.288 302.321);
    --color-slate-50: oklch(98.4% 0.003 247.858);
    --color-slate-100: oklch(96.8% 0.007 247.896);
    --color-gray-50: oklch(98.5% 0.002 247.839);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-gray-900: oklch(21% 0.034 264.665);
    --color-zinc-700: oklch(37% 0.013 285.805);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -0.025em;
    --tracking-widest: 0.1em;
    --leading-relaxed: 1.625;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --blur-sm: 8px;
    --aspect-video: 16 / 9;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
      -o-tab-size: 4;
         tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::-moz-placeholder {
    opacity: 1;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::-moz-placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    -webkit-appearance: button;
       -moz-appearance: button;
            appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .\\@container {
    container-type: inline-size;
  }
  .pointer-events-none {
    pointer-events: none;
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .start-50 {
    inset-inline-start: calc(var(--spacing) * 50);
  }
  .-top-1 {
    top: calc(var(--spacing) * -1);
  }
  .-top-12 {
    top: calc(var(--spacing) * -12);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\\.5 {
    top: calc(var(--spacing) * 1.5);
  }
  .top-1\\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-2\\.5 {
    top: calc(var(--spacing) * 2.5);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-3\\.5 {
    top: calc(var(--spacing) * 3.5);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .top-6 {
    top: calc(var(--spacing) * 6);
  }
  .top-9 {
    top: calc(var(--spacing) * 9);
  }
  .top-50 {
    top: calc(var(--spacing) * 50);
  }
  .top-\\[1px\\] {
    top: 1px;
  }
  .top-\\[50\\%\\] {
    top: 50%;
  }
  .top-\\[60\\%\\] {
    top: 60%;
  }
  .top-full {
    top: 100%;
  }
  .-right-1 {
    right: calc(var(--spacing) * -1);
  }
  .-right-12 {
    right: calc(var(--spacing) * -12);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .-bottom-12 {
    bottom: calc(var(--spacing) * -12);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .-left-12 {
    left: calc(var(--spacing) * -12);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1 {
    left: calc(var(--spacing) * 1);
  }
  .left-1\\/2 {
    left: calc(1/2 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .left-3 {
    left: calc(var(--spacing) * 3);
  }
  .left-\\[50\\%\\] {
    left: 50%;
  }
  .-z-10 {
    z-index: calc(10 * -1);
  }
  .z-0 {
    z-index: 0;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\\[1\\] {
    z-index: 1;
  }
  .col-span-2 {
    grid-column: span 2 / span 2;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .container {
    margin-inline: auto;
    padding-inline: 2rem;
    @media (width >= 40rem) {
      max-width: none;
    }
    @media (width >= 1400px) {
      max-width: 1400px;
    }
  }
  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }
  .-mx-4 {
    margin-inline: calc(var(--spacing) * -4);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }
  .mx-3\\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }
  .my-0\\.5 {
    margin-block: calc(var(--spacing) * 0.5);
  }
  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }
  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .-mt-4 {
    margin-top: calc(var(--spacing) * -4);
  }
  .mt-0\\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-1\\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mt-24 {
    margin-top: calc(var(--spacing) * 24);
  }
  .mt-32 {
    margin-top: calc(var(--spacing) * 32);
  }
  .mt-\\[12px\\] {
    margin-top: 12px;
  }
  .mt-auto {
    margin-top: auto;
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-2\\.5 {
    margin-right: calc(var(--spacing) * 2.5);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }
  .mr-auto {
    margin-right: auto;
  }
  .-mb-px {
    margin-bottom: -1px;
  }
  .mb-0\\.5 {
    margin-bottom: calc(var(--spacing) * 0.5);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .mb-32 {
    margin-bottom: calc(var(--spacing) * 32);
  }
  .mb-\\[8px\\] {
    margin-bottom: 8px;
  }
  .mb-\\[19px\\] {
    margin-bottom: 19px;
  }
  .-ml-4 {
    margin-left: calc(var(--spacing) * -4);
  }
  .ml-0 {
    margin-left: calc(var(--spacing) * 0);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-1\\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-auto {
    margin-left: auto;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }
  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }
  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }
  .h-1 {
    height: calc(var(--spacing) * 1);
  }
  .h-1\\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-1\\/2 {
    height: calc(1/2 * 100%);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-2\\/5 {
    height: calc(2/5 * 100%);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-3\\.5 {
    height: calc(var(--spacing) * 3.5);
  }
  .h-3\\/4 {
    height: calc(3/4 * 100%);
  }
  .h-3\\/5 {
    height: calc(3/5 * 100%);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-4\\/5 {
    height: calc(4/5 * 100%);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-11 {
    height: calc(var(--spacing) * 11);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-40 {
    height: calc(var(--spacing) * 40);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-60 {
    height: calc(var(--spacing) * 60);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-80 {
    height: calc(var(--spacing) * 80);
  }
  .h-96 {
    height: calc(var(--spacing) * 96);
  }
  .h-\\[1\\.2rem\\] {
    height: 1.2rem;
  }
  .h-\\[1px\\] {
    height: 1px;
  }
  .h-\\[10px\\] {
    height: 10px;
  }
  .h-\\[16px\\] {
    height: 16px;
  }
  .h-\\[32px\\] {
    height: 32px;
  }
  .h-\\[40px\\] {
    height: 40px;
  }
  .h-\\[48px\\] {
    height: 48px;
  }
  .h-\\[56px\\] {
    height: 56px;
  }
  .h-\\[60px\\] {
    height: 60px;
  }
  .h-\\[70px\\] {
    height: 70px;
  }
  .h-\\[72px\\] {
    height: 72px;
  }
  .h-\\[80px\\] {
    height: 80px;
  }
  .h-\\[92px\\] {
    height: 92px;
  }
  .h-\\[96px\\] {
    height: 96px;
  }
  .h-\\[100px\\] {
    height: 100px;
  }
  .h-\\[144px\\] {
    height: 144px;
  }
  .h-\\[168px\\] {
    height: 168px;
  }
  .h-\\[180px\\] {
    height: 180px;
  }
  .h-\\[200px\\] {
    height: 200px;
  }
  .h-\\[278px\\] {
    height: 278px;
  }
  .h-\\[392px\\] {
    height: 392px;
  }
  .h-\\[400px\\] {
    height: 400px;
  }
  .h-\\[500px\\] {
    height: 500px;
  }
  .h-\\[506px\\] {
    height: 506px;
  }
  .h-\\[600px\\] {
    height: 600px;
  }
  .h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\] {
    height: var(--radix-navigation-menu-viewport-height);
  }
  .h-\\[var\\(--radix-select-trigger-height\\)\\] {
    height: var(--radix-select-trigger-height);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-px {
    height: 1px;
  }
  .h-screen {
    height: 100vh;
  }
  .h-svh {
    height: 100svh;
  }
  .max-h-60 {
    max-height: calc(var(--spacing) * 60);
  }
  .max-h-\\[--radix-context-menu-content-available-height\\] {
    max-height: --radix-context-menu-content-available-height;
  }
  .max-h-\\[--radix-select-content-available-height\\] {
    max-height: --radix-select-content-available-height;
  }
  .max-h-\\[180px\\] {
    max-height: 180px;
  }
  .max-h-\\[300px\\] {
    max-height: 300px;
  }
  .max-h-\\[350px\\] {
    max-height: 350px;
  }
  .max-h-\\[var\\(--radix-dropdown-menu-content-available-height\\)\\] {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }
  .max-h-full {
    max-height: 100%;
  }
  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }
  .min-h-\\[51px\\] {
    min-height: 51px;
  }
  .min-h-\\[56px\\] {
    min-height: 56px;
  }
  .min-h-\\[60px\\] {
    min-height: 60px;
  }
  .min-h-\\[80px\\] {
    min-height: 80px;
  }
  .min-h-\\[112px\\] {
    min-height: 112px;
  }
  .min-h-\\[150px\\] {
    min-height: 150px;
  }
  .min-h-\\[168px\\] {
    min-height: 168px;
  }
  .min-h-\\[250px\\] {
    min-height: 250px;
  }
  .min-h-\\[300px\\] {
    min-height: 300px;
  }
  .min-h-\\[350px\\] {
    min-height: 350px;
  }
  .min-h-\\[400px\\] {
    min-height: 400px;
  }
  .min-h-\\[450px\\] {
    min-height: 450px;
  }
  .min-h-\\[500px\\] {
    min-height: 500px;
  }
  .min-h-\\[550px\\] {
    min-height: 550px;
  }
  .min-h-\\[600px\\] {
    min-height: 600px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .min-h-svh {
    min-height: 100svh;
  }
  .w-0 {
    width: calc(var(--spacing) * 0);
  }
  .w-0\\.5 {
    width: calc(var(--spacing) * 0.5);
  }
  .w-1 {
    width: calc(var(--spacing) * 1);
  }
  .w-1\\.5 {
    width: calc(var(--spacing) * 1.5);
  }
  .w-1\\/2 {
    width: calc(1/2 * 100%);
  }
  .w-1\\/3 {
    width: calc(1/3 * 100%);
  }
  .w-1\\/4 {
    width: calc(1/4 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\\.5 {
    width: calc(var(--spacing) * 2.5);
  }
  .w-2\\/3 {
    width: calc(2/3 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-3\\/4 {
    width: calc(3/4 * 100%);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-5\\/6 {
    width: calc(5/6 * 100%);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-36 {
    width: calc(var(--spacing) * 36);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-56 {
    width: calc(var(--spacing) * 56);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-\\[--sidebar-width\\] {
    width: --sidebar-width;
  }
  .w-\\[1\\.2rem\\] {
    width: 1.2rem;
  }
  .w-\\[1px\\] {
    width: 1px;
  }
  .w-\\[40px\\] {
    width: 40px;
  }
  .w-\\[56px\\] {
    width: 56px;
  }
  .w-\\[60\\%\\] {
    width: 60%;
  }
  .w-\\[70\\%\\] {
    width: 70%;
  }
  .w-\\[70px\\] {
    width: 70px;
  }
  .w-\\[84px\\] {
    width: 84px;
  }
  .w-\\[85px\\] {
    width: 85px;
  }
  .w-\\[90\\%\\] {
    width: 90%;
  }
  .w-\\[100px\\] {
    width: 100px;
  }
  .w-\\[130px\\] {
    width: 130px;
  }
  .w-\\[144px\\] {
    width: 144px;
  }
  .w-\\[180px\\] {
    width: 180px;
  }
  .w-\\[200px\\] {
    width: 200px;
  }
  .w-\\[224px\\] {
    width: 224px;
  }
  .w-\\[240px\\] {
    width: 240px;
  }
  .w-\\[250px\\] {
    width: 250px;
  }
  .w-\\[320px\\] {
    width: 320px;
  }
  .w-\\[350px\\] {
    width: 350px;
  }
  .w-\\[382px\\] {
    width: 382px;
  }
  .w-\\[400px\\] {
    width: 400px;
  }
  .w-\\[430px\\] {
    width: 430px;
  }
  .w-\\[444px\\] {
    width: 444px;
  }
  .w-\\[548px\\] {
    width: 548px;
  }
  .w-\\[600px\\] {
    width: 600px;
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .w-max {
    width: -moz-max-content;
    width: max-content;
  }
  .w-px {
    width: 1px;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xl {
    max-width: var(--container-3xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-5xl {
    max-width: var(--container-5xl);
  }
  .max-w-6xl {
    max-width: var(--container-6xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-\\[--skeleton-width\\] {
    max-width: --skeleton-width;
  }
  .max-w-\\[120px\\] {
    max-width: 120px;
  }
  .max-w-\\[200px\\] {
    max-width: 200px;
  }
  .max-w-\\[300px\\] {
    max-width: 300px;
  }
  .max-w-\\[398px\\] {
    max-width: 398px;
  }
  .max-w-\\[400px\\] {
    max-width: 400px;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .max-w-max {
    max-width: -moz-max-content;
    max-width: max-content;
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-none {
    max-width: none;
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .max-w-xl {
    max-width: var(--container-xl);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }
  .min-w-8 {
    min-width: calc(var(--spacing) * 8);
  }
  .min-w-9 {
    min-width: calc(var(--spacing) * 9);
  }
  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }
  .min-w-\\[8rem\\] {
    min-width: 8rem;
  }
  .min-w-\\[12rem\\] {
    min-width: 12rem;
  }
  .min-w-\\[16px\\] {
    min-width: 16px;
  }
  .min-w-\\[30px\\] {
    min-width: 30px;
  }
  .min-w-\\[60px\\] {
    min-width: 60px;
  }
  .min-w-\\[1440px\\] {
    min-width: 1440px;
  }
  .min-w-\\[var\\(--radix-select-trigger-width\\)\\] {
    min-width: var(--radix-select-trigger-width);
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-none {
    flex: none;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .grow {
    flex-grow: 1;
  }
  .grow-0 {
    flex-grow: 0;
  }
  .basis-full {
    flex-basis: 100%;
  }
  .table-auto {
    table-layout: auto;
  }
  .caption-bottom {
    caption-side: bottom;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .-translate-x-1\\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-x-px {
    --tw-translate-x: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-1\\/2 {
    --tw-translate-x: calc(1/2 * 100%);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-\\[-50\\%\\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-px {
    --tw-translate-x: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-\\[-50\\%\\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .rotate-0 {
    rotate: 0deg;
  }
  .rotate-45 {
    rotate: 45deg;
  }
  .rotate-90 {
    rotate: 90deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-default {
    cursor: default;
  }
  .cursor-help {
    cursor: help;
  }
  .cursor-move {
    cursor: move;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .cursor-wait {
    cursor: wait;
  }
  .touch-none {
    touch-action: none;
  }
  .resize {
    resize: both;
  }
  .scroll-m-20 {
    scroll-margin: calc(var(--spacing) * 20);
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .list-disc {
    list-style-type: disc;
  }
  .list-none {
    list-style-type: none;
  }
  .\\[appearance\\:textfield\\] {
    -webkit-appearance: textfield;
       -moz-appearance: textfield;
            appearance: textfield;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .grid-cols-\\[1fr_auto_1fr\\] {
    grid-template-columns: 1fr auto 1fr;
  }
  .grid-cols-\\[auto_1fr\\] {
    grid-template-columns: auto 1fr;
  }
  .grid-cols-\\[auto_1fr_auto\\] {
    grid-template-columns: auto 1fr auto;
  }
  .grid-cols-\\[minmax\\(0\\,1fr\\)_auto\\] {
    grid-template-columns: minmax(0,1fr) auto;
  }
  .grid-cols-\\[minmax\\(auto\\,8rem\\)_auto_1fr\\] {
    grid-template-columns: minmax(auto,8rem) auto 1fr;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-row-reverse {
    flex-direction: row-reverse;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .items-stretch {
    align-items: stretch;
  }
  .justify-around {
    justify-content: space-around;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-0\\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1\\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-8 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-10 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 10) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-12 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-2 {
    -moz-column-gap: calc(var(--spacing) * 2);
         column-gap: calc(var(--spacing) * 2);
  }
  .gap-x-16 {
    -moz-column-gap: calc(var(--spacing) * 16);
         column-gap: calc(var(--spacing) * 16);
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }
  .gap-y-6 {
    row-gap: calc(var(--spacing) * 6);
  }
  .divide-x {
    :where(& > :not(:last-child)) {
      --tw-divide-x-reverse: 0;
      border-inline-style: var(--tw-border-style);
      border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
      border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-border {
    :where(& > :not(:last-child)) {
      border-color: hsl(var(--border));
    }
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-\\[2px\\] {
    border-radius: 2px;
  }
  .rounded-\\[8px\\] {
    border-radius: 8px;
  }
  .rounded-\\[12px\\] {
    border-radius: 12px;
  }
  .rounded-\\[16px\\] {
    border-radius: 16px;
  }
  .rounded-\\[inherit\\] {
    border-radius: inherit;
  }
  .rounded-banner {
    border-radius: 24px;
  }
  .rounded-card {
    border-radius: 12px;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius);
  }
  .rounded-md {
    border-radius: calc(var(--radius) - 2px);
  }
  .rounded-none {
    border-radius: 0;
  }
  .rounded-sm {
    border-radius: calc(var(--radius) - 4px);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  .rounded-t-\\[10px\\] {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
  }
  .rounded-t-\\[16px\\] {
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
  }
  .rounded-l-md {
    border-top-left-radius: calc(var(--radius) - 2px);
    border-bottom-left-radius: calc(var(--radius) - 2px);
  }
  .rounded-l-none {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .rounded-l-xl {
    border-top-left-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-xl);
  }
  .rounded-tl-sm {
    border-top-left-radius: calc(var(--radius) - 4px);
  }
  .rounded-r-md {
    border-top-right-radius: calc(var(--radius) - 2px);
    border-bottom-right-radius: calc(var(--radius) - 2px);
  }
  .rounded-r-none {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .rounded-r-xl {
    border-top-right-radius: var(--radius-xl);
    border-bottom-right-radius: var(--radius-xl);
  }
  .rounded-b-\\[50\\%\\] {
    border-bottom-right-radius: 50%;
    border-bottom-left-radius: 50%;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }
  .border-\\[1\\.5px\\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }
  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-l-0 {
    border-left-style: var(--tw-border-style);
    border-left-width: 0px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-\\[\\#1E3A8A\\] {
    border-color: #1E3A8A;
  }
  .border-\\[\\#121E72\\] {
    border-color: #121E72;
  }
  .border-\\[\\#F0F0F0\\] {
    border-color: #F0F0F0;
  }
  .border-\\[\\#FFAA00\\] {
    border-color: #FFAA00;
  }
  .border-\\[\\#e2e8f0\\] {
    border-color: #e2e8f0;
  }
  .border-\\[--color-border\\] {
    border-color: --color-border;
  }
  .border-\\[hsl\\(var\\(--error\\)\\)\\] {
    border-color: hsl(var(--error));
  }
  .border-\\[hsl\\(var\\(--ring\\)\\)\\] {
    border-color: hsl(var(--ring));
  }
  .border-\\[hsl\\(var\\(--sidebar-border\\)\\)\\] {
    border-color: hsl(var(--sidebar-border));
  }
  .border-black {
    border-color: var(--color-black);
  }
  .border-blue-200 {
    border-color: var(--color-blue-200);
  }
  .border-blue-400 {
    border-color: var(--color-blue-400);
  }
  .border-blue-700 {
    border-color: var(--color-blue-700);
  }
  .border-border {
    border-color: hsl(var(--border));
  }
  .border-border\\/50 {
    border-color: hsl(var(--border));
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, hsl(var(--border)) 50%, transparent);
    }
  }
  .border-current {
    border-color: currentcolor;
  }
  .border-destructive {
    border-color: hsl(var(--destructive));
  }
  .border-destructive\\/20 {
    border-color: hsl(var(--destructive));
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, hsl(var(--destructive)) 20%, transparent);
    }
  }
  .border-destructive\\/50 {
    border-color: hsl(var(--destructive));
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, hsl(var(--destructive)) 50%, transparent);
    }
  }
  .border-foreground {
    border-color: hsl(var(--foreground));
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-gray-700 {
    border-color: var(--color-gray-700);
  }
  .border-input {
    border-color: hsl(var(--input));
  }
  .border-primary {
    border-color: hsl(var(--primary));
  }
  .border-primary\\/20 {
    border-color: hsl(var(--primary));
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, hsl(var(--primary)) 20%, transparent);
    }
  }
  .border-primary\\/50 {
    border-color: hsl(var(--primary));
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, hsl(var(--primary)) 50%, transparent);
    }
  }
  .border-purple-400 {
    border-color: var(--color-purple-400);
  }
  .border-red-200 {
    border-color: var(--color-red-200);
  }
  .border-red-300 {
    border-color: var(--color-red-300);
  }
  .border-red-400 {
    border-color: var(--color-red-400);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-secondary {
    border-color: hsl(var(--secondary));
  }
  .border-slate-100 {
    border-color: var(--color-slate-100);
  }
  .border-success {
    border-color: hsl(var(--success));
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-white\\/10 {
    border-color: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .border-t-transparent {
    border-top-color: transparent;
  }
  .border-b-transparent {
    border-bottom-color: transparent;
  }
  .border-l-transparent {
    border-left-color: transparent;
  }
  .bg-\\[\\#1E3A8A\\] {
    background-color: #1E3A8A;
  }
  .bg-\\[\\#121E72\\] {
    background-color: #121E72;
  }
  .bg-\\[\\#727272B2\\] {
    background-color: #727272B2;
  }
  .bg-\\[\\#EAF5FF\\] {
    background-color: #EAF5FF;
  }
  .bg-\\[\\#F6F7F9\\] {
    background-color: #F6F7F9;
  }
  .bg-\\[\\#F6F9FF\\] {
    background-color: #F6F9FF;
  }
  .bg-\\[\\#FFAA00\\] {
    background-color: #FFAA00;
  }
  .bg-\\[\\#FFAA00\\]\\/10 {
    background-color: color-mix(in oklab, #FFAA00 10%, transparent);
  }
  .bg-\\[\\#FFD904\\] {
    background-color: #FFD904;
  }
  .bg-\\[\\#f5f5f5\\] {
    background-color: #f5f5f5;
  }
  .bg-\\[\\#f8fafc\\] {
    background-color: #f8fafc;
  }
  .bg-\\[--color-bg\\] {
    background-color: --color-bg;
  }
  .bg-\\[hsl\\(var\\(--background-secondary\\)\\)\\] {
    background-color: hsl(var(--background-secondary));
  }
  .bg-\\[hsl\\(var\\(--background-secondary-foreground\\)\\)\\] {
    background-color: hsl(var(--background-secondary-foreground));
  }
  .bg-\\[hsl\\(var\\(--border\\)\\)\\] {
    background-color: hsl(var(--border));
  }
  .bg-\\[hsl\\(var\\(--chart-1\\)\\)\\] {
    background-color: hsl(var(--chart-1));
  }
  .bg-\\[hsl\\(var\\(--chart-2\\)\\)\\] {
    background-color: hsl(var(--chart-2));
  }
  .bg-\\[hsl\\(var\\(--chart-3\\)\\)\\] {
    background-color: hsl(var(--chart-3));
  }
  .bg-\\[hsl\\(var\\(--chart-4\\)\\)\\] {
    background-color: hsl(var(--chart-4));
  }
  .bg-\\[hsl\\(var\\(--chart-5\\)\\)\\] {
    background-color: hsl(var(--chart-5));
  }
  .bg-\\[hsl\\(var\\(--input\\)\\)\\] {
    background-color: hsl(var(--input));
  }
  .bg-\\[hsl\\(var\\(--ring\\)\\)\\] {
    background-color: hsl(var(--ring));
  }
  .bg-\\[hsl\\(var\\(--sidebar-accent\\)\\)\\] {
    background-color: hsl(var(--sidebar-accent));
  }
  .bg-\\[hsl\\(var\\(--sidebar-accent-foreground\\)\\)\\] {
    background-color: hsl(var(--sidebar-accent-foreground));
  }
  .bg-\\[hsl\\(var\\(--sidebar-background\\)\\)\\] {
    background-color: hsl(var(--sidebar-background));
  }
  .bg-\\[hsl\\(var\\(--sidebar-foreground\\)\\)\\] {
    background-color: hsl(var(--sidebar-foreground));
  }
  .bg-\\[hsl\\(var\\(--sidebar-primary\\)\\)\\] {
    background-color: hsl(var(--sidebar-primary));
  }
  .bg-\\[hsl\\(var\\(--sidebar-primary-foreground\\)\\)\\] {
    background-color: hsl(var(--sidebar-primary-foreground));
  }
  .bg-accent {
    background-color: hsl(var(--accent));
  }
  .bg-accent-foreground {
    background-color: hsl(var(--accent-foreground));
  }
  .bg-amber-50 {
    background-color: var(--color-amber-50);
  }
  .bg-amber-100 {
    background-color: var(--color-amber-100);
  }
  .bg-background {
    background-color: hsl(var(--background));
  }
  .bg-background-foreground {
    background-color: hsl(var(--background-BG));
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\\/10 {
    background-color: color-mix(in srgb, #000 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 10%, transparent);
    }
  }
  .bg-black\\/30 {
    background-color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }
  .bg-black\\/50 {
    background-color: color-mix(in srgb, #000 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }
  .bg-black\\/80 {
    background-color: color-mix(in srgb, #000 80%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }
  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-blue-700 {
    background-color: var(--color-blue-700);
  }
  .bg-blue-900 {
    background-color: var(--color-blue-900);
  }
  .bg-border {
    background-color: hsl(var(--border));
  }
  .bg-card {
    background-color: hsl(var(--card));
  }
  .bg-card-foreground {
    background-color: hsl(var(--card-foreground));
  }
  .bg-card\\/10 {
    background-color: hsl(var(--card));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--card)) 10%, transparent);
    }
  }
  .bg-destructive {
    background-color: hsl(var(--destructive));
  }
  .bg-destructive-foreground {
    background-color: hsl(var(--destructive-foreground));
  }
  .bg-destructive\\/10 {
    background-color: hsl(var(--destructive));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--destructive)) 10%, transparent);
    }
  }
  .bg-destructive\\/20 {
    background-color: hsl(var(--destructive));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--destructive)) 20%, transparent);
    }
  }
  .bg-discription-secondary {
    background-color: hsl(var(--discription-secondary));
  }
  .bg-error\\/10 {
    background-color: hsl(var(--error));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--error)) 10%, transparent);
    }
  }
  .bg-foreground {
    background-color: hsl(var(--foreground));
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-100\\/50 {
    background-color: color-mix(in srgb, oklch(96.7% 0.003 264.542) 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-100) 50%, transparent);
    }
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }
  .bg-indigo-900 {
    background-color: var(--color-indigo-900);
  }
  .bg-muted {
    background-color: hsl(var(--muted));
  }
  .bg-muted-foreground {
    background-color: hsl(var(--muted-foreground));
  }
  .bg-muted-foreground\\/20 {
    background-color: hsl(var(--muted-foreground));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--muted-foreground)) 20%, transparent);
    }
  }
  .bg-muted\\/10 {
    background-color: hsl(var(--muted));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--muted)) 10%, transparent);
    }
  }
  .bg-muted\\/30 {
    background-color: hsl(var(--muted));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--muted)) 30%, transparent);
    }
  }
  .bg-muted\\/40 {
    background-color: hsl(var(--muted));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--muted)) 40%, transparent);
    }
  }
  .bg-muted\\/50 {
    background-color: hsl(var(--muted));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--muted)) 50%, transparent);
    }
  }
  .bg-muted\\/60 {
    background-color: hsl(var(--muted));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--muted)) 60%, transparent);
    }
  }
  .bg-muted\\/90 {
    background-color: hsl(var(--muted));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--muted)) 90%, transparent);
    }
  }
  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }
  .bg-orange-400 {
    background-color: var(--color-orange-400);
  }
  .bg-popover {
    background-color: hsl(var(--popover));
  }
  .bg-popover-foreground {
    background-color: hsl(var(--popover-foreground));
  }
  .bg-primary {
    background-color: hsl(var(--primary));
  }
  .bg-primary-foreground {
    background-color: hsl(var(--primary-foreground));
  }
  .bg-primary\\/5 {
    background-color: hsl(var(--primary));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--primary)) 5%, transparent);
    }
  }
  .bg-primary\\/10 {
    background-color: hsl(var(--primary));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--primary)) 10%, transparent);
    }
  }
  .bg-primary\\/20 {
    background-color: hsl(var(--primary));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--primary)) 20%, transparent);
    }
  }
  .bg-primary\\/70 {
    background-color: hsl(var(--primary));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--primary)) 70%, transparent);
    }
  }
  .bg-primary\\/80 {
    background-color: hsl(var(--primary));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--primary)) 80%, transparent);
    }
  }
  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }
  .bg-purple-400 {
    background-color: var(--color-purple-400);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-400 {
    background-color: var(--color-red-400);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-secondary {
    background-color: hsl(var(--secondary));
  }
  .bg-secondary-bg {
    background-color: hsl(var(--secondary-bg));
  }
  .bg-secondary-foreground {
    background-color: hsl(var(--secondary-foreground));
  }
  .bg-secondary\\/10 {
    background-color: hsl(var(--secondary));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--secondary)) 10%, transparent);
    }
  }
  .bg-slate-50 {
    background-color: var(--color-slate-50);
  }
  .bg-slate-100 {
    background-color: var(--color-slate-100);
  }
  .bg-success {
    background-color: hsl(var(--success));
  }
  .bg-success\\/10 {
    background-color: hsl(var(--success));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--success)) 10%, transparent);
    }
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-warning {
    background-color: hsl(var(--secondary));
  }
  .bg-warning\\/10 {
    background-color: hsl(var(--secondary));
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, hsl(var(--secondary)) 10%, transparent);
    }
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\\/5 {
    background-color: color-mix(in srgb, #fff 5%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 5%, transparent);
    }
  }
  .bg-white\\/10 {
    background-color: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .bg-yellow-200 {
    background-color: var(--color-yellow-200);
  }
  .bg-yellow-400 {
    background-color: var(--color-yellow-400);
  }
  .bg-zinc-700 {
    background-color: var(--color-zinc-700);
  }
  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-black\\/70 {
    --tw-gradient-from: color-mix(in srgb, #000 70%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-primary {
    --tw-gradient-from: hsl(var(--primary));
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-muted {
    --tw-gradient-to: hsl(var(--muted));
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-secondary {
    --tw-gradient-to: hsl(var(--secondary));
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-center {
    background-position: center;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .fill-current {
    fill: currentcolor;
  }
  .fill-primary {
    fill: hsl(var(--primary));
  }
  .fill-yellow-400 {
    fill: var(--color-yellow-400);
  }
  .fill-zinc-700 {
    fill: var(--color-zinc-700);
  }
  .stroke-\\[2\\.5\\] {
    stroke-width: 2.5;
  }
  .object-contain {
    -o-object-fit: contain;
       object-fit: contain;
  }
  .object-cover {
    -o-object-fit: cover;
       object-fit: cover;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-\\[1px\\] {
    padding: 1px;
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-\\[4px\\] {
    padding-inline: 4px;
  }
  .py-0\\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pt-\\[12px\\] {
    padding-top: 12px;
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-2\\.5 {
    padding-right: calc(var(--spacing) * 2.5);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pr-6 {
    padding-right: calc(var(--spacing) * 6);
  }
  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-12 {
    padding-bottom: calc(var(--spacing) * 12);
  }
  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }
  .pb-\\[8px\\] {
    padding-bottom: 8px;
  }
  .pl-0 {
    padding-left: calc(var(--spacing) * 0);
  }
  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }
  .pl-2\\.5 {
    padding-left: calc(var(--spacing) * 2.5);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }
  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }
  .pl-9 {
    padding-left: calc(var(--spacing) * 9);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-middle {
    vertical-align: middle;
  }
  .font-mono {
    font-family: var(--font-mono);
  }
  .font-sans {
    font-family: var(--font-sans);
  }
  .text-body-16-med {
    font-size: 16px;
    line-height: var(--tw-leading, 24px);
    font-weight: var(--tw-font-weight, 500);
  }
  .text-body-16-reg {
    font-size: 16px;
    line-height: var(--tw-leading, 24px);
    font-weight: var(--tw-font-weight, 400);
  }
  .text-body-18-med {
    font-size: 18px;
    line-height: var(--tw-leading, 24px);
    font-weight: var(--tw-font-weight, 500);
  }
  .text-subtitle-14-reg {
    font-size: 14px;
    line-height: var(--tw-leading, 20px);
    font-weight: var(--tw-font-weight, 400);
  }
  .text-text-xl-bold {
    font-size: 28px;
    line-height: var(--tw-leading, 40px);
    font-weight: var(--tw-font-weight, 600);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-body-18-reg {
    font-size: 18px;
    font-weight: var(--tw-font-weight, 400);
  }
  .text-h2 {
    font-size: 32px;
    font-weight: var(--tw-font-weight, 600);
  }
  .text-h3 {
    font-size: 24px;
    font-weight: var(--tw-font-weight, 600);
  }
  .text-h4 {
    font-size: 20px;
    font-weight: var(--tw-font-weight, 600);
  }
  .text-subtitle-15-reg {
    font-size: 15px;
    font-weight: var(--tw-font-weight, 400);
  }
  .text-text-3xl {
    font-size: 40px;
    font-weight: var(--tw-font-weight, 700);
  }
  .text-text-lg-bold {
    font-size: 24px;
    font-weight: var(--tw-font-weight, 700);
  }
  .text-text-md {
    font-size: 18px;
    font-weight: var(--tw-font-weight, 400);
  }
  .text-text-sm {
    font-size: 16px;
    font-weight: var(--tw-font-weight, 400);
  }
  .text-\\[0\\.8rem\\] {
    font-size: 0.8rem;
  }
  .text-\\[10px\\] {
    font-size: 10px;
  }
  .text-\\[11px\\] {
    font-size: 11px;
  }
  .text-\\[12px\\] {
    font-size: 12px;
  }
  .text-\\[14px\\] {
    font-size: 14px;
  }
  .text-\\[15px\\] {
    font-size: 15px;
  }
  .text-\\[18px\\] {
    font-size: 18px;
  }
  .text-\\[24px\\] {
    font-size: 24px;
  }
  .text-\\[40px\\] {
    font-size: 40px;
  }
  .leading-\\[24px\\] {
    --tw-leading: 24px;
    line-height: 24px;
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .text-ellipsis {
    text-overflow: ellipsis;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-\\[\\#5D6A85\\] {
    color: #5D6A85;
  }
  .text-\\[\\#121E72\\] {
    color: #121E72;
  }
  .text-\\[\\#565C6E\\] {
    color: #565C6E;
  }
  .text-\\[\\#787E90\\] {
    color: #787E90;
  }
  .text-\\[\\#FFAA00\\] {
    color: #FFAA00;
  }
  .text-\\[hsl\\(var\\(--background-secondary-foreground\\)\\)\\] {
    color: hsl(var(--background-secondary-foreground));
  }
  .text-\\[hsl\\(var\\(--sidebar-accent\\)\\)\\] {
    color: hsl(var(--sidebar-accent));
  }
  .text-\\[hsl\\(var\\(--sidebar-accent-foreground\\)\\)\\] {
    color: hsl(var(--sidebar-accent-foreground));
  }
  .text-\\[hsl\\(var\\(--sidebar-background\\)\\)\\] {
    color: hsl(var(--sidebar-background));
  }
  .text-\\[hsl\\(var\\(--sidebar-foreground\\)\\)\\] {
    color: hsl(var(--sidebar-foreground));
  }
  .text-\\[hsl\\(var\\(--sidebar-primary\\)\\)\\] {
    color: hsl(var(--sidebar-primary));
  }
  .text-\\[hsl\\(var\\(--sidebar-primary-foreground\\)\\)\\] {
    color: hsl(var(--sidebar-primary-foreground));
  }
  .text-accent {
    color: hsl(var(--accent));
  }
  .text-accent-foreground {
    color: hsl(var(--accent-foreground));
  }
  .text-amber-500 {
    color: var(--color-amber-500);
  }
  .text-amber-600 {
    color: var(--color-amber-600);
  }
  .text-amber-800 {
    color: var(--color-amber-800);
  }
  .text-background {
    color: hsl(var(--background));
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-800 {
    color: var(--color-blue-800);
  }
  .text-blue-900 {
    color: var(--color-blue-900);
  }
  .text-border {
    color: hsl(var(--border));
  }
  .text-card-foreground {
    color: hsl(var(--card-foreground));
  }
  .text-current {
    color: currentcolor;
  }
  .text-destructive {
    color: hsl(var(--destructive));
  }
  .text-destructive-foreground {
    color: hsl(var(--destructive-foreground));
  }
  .text-discription {
    color: hsl(var(--discription));
  }
  .text-discription-secondary {
    color: hsl(var(--discription-secondary));
  }
  .text-error {
    color: hsl(var(--error));
  }
  .text-foreground {
    color: hsl(var(--foreground));
  }
  .text-foreground\\/70 {
    color: hsl(var(--foreground));
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, hsl(var(--foreground)) 70%, transparent);
    }
  }
  .text-foreground\\/80 {
    color: hsl(var(--foreground));
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, hsl(var(--foreground)) 80%, transparent);
    }
  }
  .text-foreground\\/90 {
    color: hsl(var(--foreground));
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, hsl(var(--foreground)) 90%, transparent);
    }
  }
  .text-gray-200 {
    color: var(--color-gray-200);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-muted {
    color: hsl(var(--muted));
  }
  .text-muted-foreground {
    color: hsl(var(--muted-foreground));
  }
  .text-muted-foreground\\/70 {
    color: hsl(var(--muted-foreground));
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, hsl(var(--muted-foreground)) 70%, transparent);
    }
  }
  .text-orange-400 {
    color: var(--color-orange-400);
  }
  .text-orange-500 {
    color: var(--color-orange-500);
  }
  .text-orange-600 {
    color: var(--color-orange-600);
  }
  .text-placeholder {
    color: hsl(var(--placeholder));
  }
  .text-popover-foreground {
    color: hsl(var(--popover-foreground));
  }
  .text-primary {
    color: hsl(var(--primary));
  }
  .text-primary-foreground {
    color: hsl(var(--primary-foreground));
  }
  .text-purple-100 {
    color: var(--color-purple-100);
  }
  .text-purple-500 {
    color: var(--color-purple-500);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-secondary {
    color: hsl(var(--secondary));
  }
  .text-secondary-foreground {
    color: hsl(var(--secondary-foreground));
  }
  .text-success {
    color: hsl(var(--success));
  }
  .text-success-foreground {
    color: hsl(var(--success-foreground));
  }
  .text-title {
    color: hsl(var(--title-main));
  }
  .text-warning {
    color: hsl(var(--secondary));
  }
  .text-warning-foreground {
    color: hsl(var(--secondary-foreground));
  }
  .text-white {
    color: var(--color-white);
  }
  .text-white\\/60 {
    color: color-mix(in srgb, #fff 60%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-white) 60%, transparent);
    }
  }
  .text-white\\/90 {
    color: color-mix(in srgb, #fff 90%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .underline {
    text-decoration-line: underline;
  }
  .underline-offset-4 {
    text-underline-offset: 4px;
  }
  .placeholder-white\\/50 {
    &::-moz-placeholder {
      color: color-mix(in srgb, #fff 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-white) 50%, transparent);
      }
    }
    &::placeholder {
      color: color-mix(in srgb, #fff 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-white) 50%, transparent);
      }
    }
  }
  .accent-foreground {
    accent-color: hsl(var(--foreground));
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-60 {
    opacity: 60%;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .opacity-80 {
    opacity: 80%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-0 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-8 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-background {
    --tw-ring-color: hsl(var(--background));
  }
  .ring-ring {
    --tw-ring-color: hsl(var(--ring));
  }
  .ring-offset-background {
    --tw-ring-offset-color: hsl(var(--background));
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .backdrop-blur-\\[10px\\] {
    --tw-backdrop-blur: blur(10px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\\[left\\,right\\,width\\] {
    transition-property: left,right,width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\\[margin\\,opacity\\] {
    transition-property: margin,opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\\[width\\,height\\,padding\\] {
    transition-property: width,height,padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-\\[width\\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .delay-75 {
    transition-delay: 75ms;
  }
  .delay-150 {
    transition-delay: 150ms;
  }
  .delay-300 {
    transition-delay: 300ms;
  }
  .duration-150 {
    --tw-duration: 150ms;
    transition-duration: 150ms;
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .duration-700 {
    --tw-duration: 700ms;
    transition-duration: 700ms;
  }
  .duration-1000 {
    --tw-duration: 1000ms;
    transition-duration: 1000ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .animate-in {
    animation-name: enter;
    animation-duration: 150ms;
    --tw-enter-opacity: initial;
    --tw-enter-scale: initial;
    --tw-enter-rotate: initial;
    --tw-enter-translate-x: initial;
    --tw-enter-translate-y: initial;
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
         user-select: none;
  }
  .delay-75 {
    animation-delay: 75ms;
  }
  .delay-150 {
    animation-delay: 150ms;
  }
  .delay-300 {
    animation-delay: 300ms;
  }
  .duration-150 {
    animation-duration: 150ms;
  }
  .duration-200 {
    animation-duration: 200ms;
  }
  .duration-300 {
    animation-duration: 300ms;
  }
  .duration-500 {
    animation-duration: 500ms;
  }
  .duration-700 {
    animation-duration: 700ms;
  }
  .duration-1000 {
    animation-duration: 1000ms;
  }
  .ease-in-out {
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
  .ease-linear {
    animation-timing-function: linear;
  }
  .ease-out {
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
  .fade-in {
    --tw-enter-opacity: 0;
  }
  .fade-in-0 {
    --tw-enter-opacity: 0;
  }
  .running {
    animation-play-state: running;
  }
  .slide-in-from-top-1 {
    --tw-enter-translate-y: -0.25rem;
  }
  .zoom-in-95 {
    --tw-enter-scale: .95;
  }
  .\\*\\:w-1\\/2 {
    :is(& > *) {
      width: calc(1/2 * 100%);
    }
  }
  .group-focus-within\\/menu-item\\:opacity-100 {
    &:is(:where(.group\\/menu-item):focus-within *) {
      opacity: 100%;
    }
  }
  .group-hover\\/menu-item\\:opacity-100 {
    &:is(:where(.group\\/menu-item):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-has-\\[\\[data-sidebar\\=menu-action\\]\\]\\/menu-item\\:pr-8 {
    &:is(:where(.group\\/menu-item):has(*:is([data-sidebar=menu-action])) *) {
      padding-right: calc(var(--spacing) * 8);
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:-mt-8 {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      margin-top: calc(var(--spacing) * -8);
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:hidden {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      display: none;
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:\\!size-8 {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      width: calc(var(--spacing) * 8) !important;
      height: calc(var(--spacing) * 8) !important;
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:w-\\[--sidebar-width-icon\\] {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      width: --sidebar-width-icon;
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)\\)\\] {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      width: calc(var(--sidebar-width-icon) + 1rem);
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)_\\+2px\\)\\] {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      width: calc(var(--sidebar-width-icon) + 1rem + 2px);
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      overflow: hidden;
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:\\!p-0 {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      padding: calc(var(--spacing) * 0) !important;
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:\\!p-2 {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      padding: calc(var(--spacing) * 2) !important;
    }
  }
  .group-data-\\[collapsible\\=icon\\]\\:opacity-0 {
    &:is(:where(.group)[data-collapsible="icon"] *) {
      opacity: 0%;
    }
  }
  .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      right: calc(var(--sidebar-width) * -1);
    }
  }
  .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      left: calc(var(--sidebar-width) * -1);
    }
  }
  .group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      width: calc(var(--spacing) * 0);
    }
  }
  .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .group-data-\\[side\\=left\\]\\:-right-4 {
    &:is(:where(.group)[data-side="left"] *) {
      right: calc(var(--spacing) * -4);
    }
  }
  .group-data-\\[side\\=left\\]\\:border-r {
    &:is(:where(.group)[data-side="left"] *) {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }
  .group-data-\\[side\\=right\\]\\:left-0 {
    &:is(:where(.group)[data-side="right"] *) {
      left: calc(var(--spacing) * 0);
    }
  }
  .group-data-\\[side\\=right\\]\\:rotate-180 {
    &:is(:where(.group)[data-side="right"] *) {
      rotate: 180deg;
    }
  }
  .group-data-\\[side\\=right\\]\\:border-l {
    &:is(:where(.group)[data-side="right"] *) {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }
  .group-data-\\[state\\=open\\]\\:rotate-180 {
    &:is(:where(.group)[data-state="open"] *) {
      rotate: 180deg;
    }
  }
  .group-data-\\[variant\\=floating\\]\\:rounded-lg {
    &:is(:where(.group)[data-variant="floating"] *) {
      border-radius: var(--radius);
    }
  }
  .group-data-\\[variant\\=floating\\]\\:border {
    &:is(:where(.group)[data-variant="floating"] *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .group-data-\\[variant\\=floating\\]\\:shadow {
    &:is(:where(.group)[data-variant="floating"] *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .group-\\[\\.toast\\]\\:bg-muted {
    &:is(:where(.group):is(.toast) *) {
      background-color: hsl(var(--muted));
    }
  }
  .group-\\[\\.toast\\]\\:bg-primary {
    &:is(:where(.group):is(.toast) *) {
      background-color: hsl(var(--primary));
    }
  }
  .group-\\[\\.toast\\]\\:text-muted-foreground {
    &:is(:where(.group):is(.toast) *) {
      color: hsl(var(--muted-foreground));
    }
  }
  .group-\\[\\.toast\\]\\:text-primary-foreground {
    &:is(:where(.group):is(.toast) *) {
      color: hsl(var(--primary-foreground));
    }
  }
  .group-\\[\\.toaster\\]\\:border-border {
    &:is(:where(.group):is(.toaster) *) {
      border-color: hsl(var(--border));
    }
  }
  .group-\\[\\.toaster\\]\\:bg-background {
    &:is(:where(.group):is(.toaster) *) {
      background-color: hsl(var(--background));
    }
  }
  .group-\\[\\.toaster\\]\\:text-foreground {
    &:is(:where(.group):is(.toaster) *) {
      color: hsl(var(--foreground));
    }
  }
  .group-\\[\\.toaster\\]\\:shadow-lg {
    &:is(:where(.group):is(.toaster) *) {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .peer-disabled\\:cursor-not-allowed {
    &:is(:where(.peer):disabled ~ *) {
      cursor: not-allowed;
    }
  }
  .peer-disabled\\:opacity-70 {
    &:is(:where(.peer):disabled ~ *) {
      opacity: 70%;
    }
  }
  .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {
    &:is(:where(.peer\\/menu-button)[data-size="default"] ~ *) {
      top: calc(var(--spacing) * 1.5);
    }
  }
  .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {
    &:is(:where(.peer\\/menu-button)[data-size="lg"] ~ *) {
      top: calc(var(--spacing) * 2.5);
    }
  }
  .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {
    &:is(:where(.peer\\/menu-button)[data-size="sm"] ~ *) {
      top: calc(var(--spacing) * 1);
    }
  }
  .file\\:border-0 {
    &::file-selector-button {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .file\\:bg-transparent {
    &::file-selector-button {
      background-color: transparent;
    }
  }
  .file\\:text-sm {
    &::file-selector-button {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .file\\:font-medium {
    &::file-selector-button {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .file\\:text-foreground {
    &::file-selector-button {
      color: hsl(var(--foreground));
    }
  }
  .placeholder\\:text-muted-foreground {
    &::-moz-placeholder {
      color: hsl(var(--muted-foreground));
    }
    &::placeholder {
      color: hsl(var(--muted-foreground));
    }
  }
  .before\\:absolute {
    &::before {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .before\\:inset-0 {
    &::before {
      content: var(--tw-content);
      inset: calc(var(--spacing) * 0);
    }
  }
  .before\\:scale-0 {
    &::before {
      content: var(--tw-content);
      --tw-scale-x: 0%;
      --tw-scale-y: 0%;
      --tw-scale-z: 0%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .before\\:rounded-full {
    &::before {
      content: var(--tw-content);
      border-radius: calc(infinity * 1px);
    }
  }
  .before\\:bg-current {
    &::before {
      content: var(--tw-content);
      background-color: currentcolor;
    }
  }
  .before\\:transition-transform {
    &::before {
      content: var(--tw-content);
      transition-property: transform, translate, scale, rotate;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
  }
  .after\\:absolute {
    &::after {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .after\\:-inset-2 {
    &::after {
      content: var(--tw-content);
      inset: calc(var(--spacing) * -2);
    }
  }
  .after\\:inset-y-0 {
    &::after {
      content: var(--tw-content);
      inset-block: calc(var(--spacing) * 0);
    }
  }
  .after\\:left-1\\/2 {
    &::after {
      content: var(--tw-content);
      left: calc(1/2 * 100%);
    }
  }
  .after\\:ml-0\\.5 {
    &::after {
      content: var(--tw-content);
      margin-left: calc(var(--spacing) * 0.5);
    }
  }
  .after\\:w-1 {
    &::after {
      content: var(--tw-content);
      width: calc(var(--spacing) * 1);
    }
  }
  .after\\:w-\\[2px\\] {
    &::after {
      content: var(--tw-content);
      width: 2px;
    }
  }
  .after\\:-translate-x-1\\/2 {
    &::after {
      content: var(--tw-content);
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .after\\:bg-accent {
    &::after {
      content: var(--tw-content);
      background-color: hsl(var(--accent));
    }
  }
  .after\\:text-destructive {
    &::after {
      content: var(--tw-content);
      color: hsl(var(--destructive));
    }
  }
  .after\\:content-\\[\\'\\*\\'\\] {
    &::after {
      content: var(--tw-content);
      --tw-content: '*';
      content: var(--tw-content);
    }
  }
  .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full {
    &:is(:where(.group)[data-collapsible="offcanvas"] *) {
      &::after {
        content: var(--tw-content);
        left: 100%;
      }
    }
  }
  .first\\:rounded-l-md {
    &:first-child {
      border-top-left-radius: calc(var(--radius) - 2px);
      border-bottom-left-radius: calc(var(--radius) - 2px);
    }
  }
  .first\\:border-l {
    &:first-child {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }
  .last\\:mb-0 {
    &:last-child {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .last\\:rounded-r-md {
    &:last-child {
      border-top-right-radius: calc(var(--radius) - 2px);
      border-bottom-right-radius: calc(var(--radius) - 2px);
    }
  }
  .last\\:border-r-0 {
    &:last-child {
      border-right-style: var(--tw-border-style);
      border-right-width: 0px;
    }
  }
  .last\\:border-b-0 {
    &:last-child {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .focus-within\\:relative {
    &:focus-within {
      position: relative;
    }
  }
  .focus-within\\:z-20 {
    &:focus-within {
      z-index: 20;
    }
  }
  .focus-within\\:ring-2 {
    &:focus-within {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-within\\:ring-destructive {
    &:focus-within {
      --tw-ring-color: hsl(var(--destructive));
    }
  }
  .focus-within\\:ring-ring {
    &:focus-within {
      --tw-ring-color: hsl(var(--ring));
    }
  }
  .focus-within\\:ring-offset-2 {
    &:focus-within {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .hover\\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\\:bg-\\[\\#1a2a8f\\] {
    &:hover {
      @media (hover: hover) {
        background-color: #1a2a8f;
      }
    }
  }
  .hover\\:bg-\\[\\#E69700\\] {
    &:hover {
      @media (hover: hover) {
        background-color: #E69700;
      }
    }
  }
  .hover\\:bg-\\[\\#f1f5f9\\] {
    &:hover {
      @media (hover: hover) {
        background-color: #f1f5f9;
      }
    }
  }
  .hover\\:bg-\\[\\#f8fafc\\] {
    &:hover {
      @media (hover: hover) {
        background-color: #f8fafc;
      }
    }
  }
  .hover\\:bg-\\[hsl\\(var\\(--sidebar-accent\\)\\)\\] {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--sidebar-accent));
      }
    }
  }
  .hover\\:bg-accent {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--accent));
      }
    }
  }
  .hover\\:bg-accent\\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--accent));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
        }
      }
    }
  }
  .hover\\:bg-accent\\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--accent));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--accent)) 90%, transparent);
        }
      }
    }
  }
  .hover\\:bg-background\\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--background));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--background)) 50%, transparent);
        }
      }
    }
  }
  .hover\\:bg-blue-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-800);
      }
    }
  }
  .hover\\:bg-destructive\\/80 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--destructive));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--destructive)) 80%, transparent);
        }
      }
    }
  }
  .hover\\:bg-destructive\\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--destructive));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--destructive)) 90%, transparent);
        }
      }
    }
  }
  .hover\\:bg-foreground\\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--foreground));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--foreground)) 10%, transparent);
        }
      }
    }
  }
  .hover\\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\\:bg-indigo-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-700);
      }
    }
  }
  .hover\\:bg-indigo-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-800);
      }
    }
  }
  .hover\\:bg-muted {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--muted));
      }
    }
  }
  .hover\\:bg-muted\\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--muted));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--muted)) 50%, transparent);
        }
      }
    }
  }
  .hover\\:bg-muted\\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--muted));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--muted)) 90%, transparent);
        }
      }
    }
  }
  .hover\\:bg-primary {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--primary));
      }
    }
  }
  .hover\\:bg-primary\\/10 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--primary));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--primary)) 10%, transparent);
        }
      }
    }
  }
  .hover\\:bg-primary\\/20 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--primary));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--primary)) 20%, transparent);
        }
      }
    }
  }
  .hover\\:bg-primary\\/80 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--primary));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--primary)) 80%, transparent);
        }
      }
    }
  }
  .hover\\:bg-primary\\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--primary));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--primary)) 90%, transparent);
        }
      }
    }
  }
  .hover\\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\\:bg-secondary\\/80 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--secondary));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--secondary)) 80%, transparent);
        }
      }
    }
  }
  .hover\\:bg-secondary\\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: hsl(var(--secondary));
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, hsl(var(--secondary)) 90%, transparent);
        }
      }
    }
  }
  .hover\\:bg-slate-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-100);
      }
    }
  }
  .hover\\:bg-white\\/20 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #fff 20%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
        }
      }
    }
  }
  .hover\\:text-\\[hsl\\(var\\(--sidebar-accent-foreground\\)\\)\\] {
    &:hover {
      @media (hover: hover) {
        color: hsl(var(--sidebar-accent-foreground));
      }
    }
  }
  .hover\\:text-accent-foreground {
    &:hover {
      @media (hover: hover) {
        color: hsl(var(--accent-foreground));
      }
    }
  }
  .hover\\:text-blue-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-500);
      }
    }
  }
  .hover\\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\\:text-blue-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .hover\\:text-foreground {
    &:hover {
      @media (hover: hover) {
        color: hsl(var(--foreground));
      }
    }
  }
  .hover\\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\\:text-muted-foreground {
    &:hover {
      @media (hover: hover) {
        color: hsl(var(--muted-foreground));
      }
    }
  }
  .hover\\:text-primary {
    &:hover {
      @media (hover: hover) {
        color: hsl(var(--primary));
      }
    }
  }
  .hover\\:text-primary-foreground {
    &:hover {
      @media (hover: hover) {
        color: hsl(var(--primary-foreground));
      }
    }
  }
  .hover\\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\\:opacity-80 {
    &:hover {
      @media (hover: hover) {
        opacity: 80%;
      }
    }
  }
  .hover\\:opacity-100 {
    &:hover {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\] {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\\:drop-shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.15)));
        --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));
        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
      }
    }
  }
  .focus\\:bg-accent {
    &:focus {
      background-color: hsl(var(--accent));
    }
  }
  .focus\\:bg-primary {
    &:focus {
      background-color: hsl(var(--primary));
    }
  }
  .focus\\:text-accent-foreground {
    &:focus {
      color: hsl(var(--accent-foreground));
    }
  }
  .focus\\:text-destructive {
    &:focus {
      color: hsl(var(--destructive));
    }
  }
  .focus\\:text-primary-foreground {
    &:focus {
      color: hsl(var(--primary-foreground));
    }
  }
  .focus\\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\\:ring-\\[\\#121E72\\] {
    &:focus {
      --tw-ring-color: #121E72;
    }
  }
  .focus\\:ring-\\[hsl\\(var\\(--ring\\)\\)\\] {
    &:focus {
      --tw-ring-color: hsl(var(--ring));
    }
  }
  .focus\\:ring-indigo-500 {
    &:focus {
      --tw-ring-color: var(--color-indigo-500);
    }
  }
  .focus\\:ring-ring {
    &:focus {
      --tw-ring-color: hsl(var(--ring));
    }
  }
  .focus\\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .focus-visible\\:ring-0 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\\:ring-1 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\\:ring-2 {
    &:focus-visible {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus-visible\\:ring-blue-500 {
    &:focus-visible {
      --tw-ring-color: var(--color-blue-500);
    }
  }
  .focus-visible\\:ring-destructive {
    &:focus-visible {
      --tw-ring-color: hsl(var(--destructive));
    }
  }
  .focus-visible\\:ring-ring {
    &:focus-visible {
      --tw-ring-color: hsl(var(--ring));
    }
  }
  .focus-visible\\:ring-offset-0 {
    &:focus-visible {
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-visible\\:ring-offset-1 {
    &:focus-visible {
      --tw-ring-offset-width: 1px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-visible\\:ring-offset-2 {
    &:focus-visible {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus-visible\\:ring-offset-background {
    &:focus-visible {
      --tw-ring-offset-color: hsl(var(--background));
    }
  }
  .focus-visible\\:outline-none {
    &:focus-visible {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .disabled\\:pointer-events-none {
    &:disabled {
      pointer-events: none;
    }
  }
  .disabled\\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .has-\\[\\:disabled\\]\\:opacity-50 {
    &:has(*:is(:disabled)) {
      opacity: 50%;
    }
  }
  .aria-disabled\\:pointer-events-none {
    &[aria-disabled="true"] {
      pointer-events: none;
    }
  }
  .aria-disabled\\:opacity-50 {
    &[aria-disabled="true"] {
      opacity: 50%;
    }
  }
  .aria-selected\\:bg-accent {
    &[aria-selected="true"] {
      background-color: hsl(var(--accent));
    }
  }
  .aria-selected\\:bg-accent\\/50 {
    &[aria-selected="true"] {
      background-color: hsl(var(--accent));
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
      }
    }
  }
  .aria-selected\\:text-accent-foreground {
    &[aria-selected="true"] {
      color: hsl(var(--accent-foreground));
    }
  }
  .aria-selected\\:text-muted-foreground {
    &[aria-selected="true"] {
      color: hsl(var(--muted-foreground));
    }
  }
  .aria-selected\\:opacity-100 {
    &[aria-selected="true"] {
      opacity: 100%;
    }
  }
  .data-\\[active\\=true\\]\\:font-medium {
    &[data-active="true"] {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .data-\\[disabled\\]\\:pointer-events-none {
    &[data-disabled] {
      pointer-events: none;
    }
  }
  .data-\\[disabled\\]\\:opacity-50 {
    &[data-disabled] {
      opacity: 50%;
    }
  }
  .data-\\[disabled\\=true\\]\\:pointer-events-none {
    &[data-disabled="true"] {
      pointer-events: none;
    }
  }
  .data-\\[disabled\\=true\\]\\:opacity-50 {
    &[data-disabled="true"] {
      opacity: 50%;
    }
  }
  .data-\\[motion\\=from-end\\]\\:slide-in-from-right-52 {
    &[data-motion="from-end"] {
      --tw-enter-translate-x: 13rem;
    }
  }
  .data-\\[motion\\=from-start\\]\\:slide-in-from-left-52 {
    &[data-motion="from-start"] {
      --tw-enter-translate-x: -13rem;
    }
  }
  .data-\\[motion\\=to-end\\]\\:slide-out-to-right-52 {
    &[data-motion="to-end"] {
      --tw-exit-translate-x: 13rem;
    }
  }
  .data-\\[motion\\=to-start\\]\\:slide-out-to-left-52 {
    &[data-motion="to-start"] {
      --tw-exit-translate-x: -13rem;
    }
  }
  .data-\\[motion\\^\\=from-\\]\\:animate-in {
    &[data-motion^="from-"] {
      animation-name: enter;
      animation-duration: 150ms;
      --tw-enter-opacity: initial;
      --tw-enter-scale: initial;
      --tw-enter-rotate: initial;
      --tw-enter-translate-x: initial;
      --tw-enter-translate-y: initial;
    }
  }
  .data-\\[motion\\^\\=from-\\]\\:fade-in {
    &[data-motion^="from-"] {
      --tw-enter-opacity: 0;
    }
  }
  .data-\\[motion\\^\\=to-\\]\\:animate-out {
    &[data-motion^="to-"] {
      animation-name: exit;
      animation-duration: 150ms;
      --tw-exit-opacity: initial;
      --tw-exit-scale: initial;
      --tw-exit-rotate: initial;
      --tw-exit-translate-x: initial;
      --tw-exit-translate-y: initial;
    }
  }
  .data-\\[motion\\^\\=to-\\]\\:fade-out {
    &[data-motion^="to-"] {
      --tw-exit-opacity: 0;
    }
  }
  .data-\\[panel-group-direction\\=vertical\\]\\:h-px {
    &[data-panel-group-direction="vertical"] {
      height: 1px;
    }
  }
  .data-\\[panel-group-direction\\=vertical\\]\\:w-full {
    &[data-panel-group-direction="vertical"] {
      width: 100%;
    }
  }
  .data-\\[panel-group-direction\\=vertical\\]\\:flex-col {
    &[data-panel-group-direction="vertical"] {
      flex-direction: column;
    }
  }
  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0 {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        left: calc(var(--spacing) * 0);
      }
    }
  }
  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1 {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        height: calc(var(--spacing) * 1);
      }
    }
  }
  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        width: 100%;
      }
    }
  }
  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0 {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        --tw-translate-x: calc(var(--spacing) * 0);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2 {
    &[data-panel-group-direction="vertical"] {
      &::after {
        content: var(--tw-content);
        --tw-translate-y: calc(calc(1/2 * 100%) * -1);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .data-\\[placeholder\\]\\:text-muted-foreground {
    &[data-placeholder] {
      color: hsl(var(--muted-foreground));
    }
  }
  .data-\\[selected\\=true\\]\\:bg-accent {
    &[data-selected="true"] {
      background-color: hsl(var(--accent));
    }
  }
  .data-\\[selected\\=true\\]\\:text-accent-foreground {
    &[data-selected="true"] {
      color: hsl(var(--accent-foreground));
    }
  }
  .data-\\[side\\=bottom\\]\\:translate-y-1 {
    &[data-side="bottom"] {
      --tw-translate-y: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\\[side\\=bottom\\]\\:slide-in-from-top-2 {
    &[data-side="bottom"] {
      --tw-enter-translate-y: -0.5rem;
    }
  }
  .data-\\[side\\=left\\]\\:-translate-x-1 {
    &[data-side="left"] {
      --tw-translate-x: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\\[side\\=left\\]\\:slide-in-from-right-2 {
    &[data-side="left"] {
      --tw-enter-translate-x: 0.5rem;
    }
  }
  .data-\\[side\\=right\\]\\:translate-x-1 {
    &[data-side="right"] {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\\[side\\=right\\]\\:slide-in-from-left-2 {
    &[data-side="right"] {
      --tw-enter-translate-x: -0.5rem;
    }
  }
  .data-\\[side\\=top\\]\\:-translate-y-1 {
    &[data-side="top"] {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\\[side\\=top\\]\\:slide-in-from-bottom-2 {
    &[data-side="top"] {
      --tw-enter-translate-y: 0.5rem;
    }
  }
  .data-\\[state\\=active\\]\\:border-b-primary {
    &[data-state="active"] {
      border-bottom-color: hsl(var(--primary));
    }
  }
  .data-\\[state\\=active\\]\\:bg-background {
    &[data-state="active"] {
      background-color: hsl(var(--background));
    }
  }
  .data-\\[state\\=active\\]\\:text-foreground {
    &[data-state="active"] {
      color: hsl(var(--foreground));
    }
  }
  .data-\\[state\\=active\\]\\:shadow {
    &[data-state="active"] {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .data-\\[state\\=checked\\]\\:translate-x-4 {
    &[data-state="checked"] {
      --tw-translate-x: calc(var(--spacing) * 4);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\\[state\\=checked\\]\\:border-blue-600 {
    &[data-state="checked"] {
      border-color: var(--color-blue-600);
    }
  }
  .data-\\[state\\=checked\\]\\:bg-primary {
    &[data-state="checked"] {
      background-color: hsl(var(--primary));
    }
  }
  .data-\\[state\\=checked\\]\\:text-blue-600 {
    &[data-state="checked"] {
      color: var(--color-blue-600);
    }
  }
  .data-\\[state\\=checked\\]\\:text-primary-foreground {
    &[data-state="checked"] {
      color: hsl(var(--primary-foreground));
    }
  }
  .data-\\[state\\=checked\\]\\:before\\:scale-100 {
    &[data-state="checked"] {
      &::before {
        content: var(--tw-content);
        --tw-scale-x: 100%;
        --tw-scale-y: 100%;
        --tw-scale-z: 100%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .data-\\[state\\=closed\\]\\:animate-accordion-up {
    &[data-state="closed"] {
      animation: accordion-up 0.2s ease-out;
    }
  }
  .data-\\[state\\=closed\\]\\:duration-300 {
    &[data-state="closed"] {
      --tw-duration: 300ms;
      transition-duration: 300ms;
    }
  }
  .data-\\[state\\=closed\\]\\:animate-out {
    &[data-state="closed"] {
      animation-name: exit;
      animation-duration: 150ms;
      --tw-exit-opacity: initial;
      --tw-exit-scale: initial;
      --tw-exit-rotate: initial;
      --tw-exit-translate-x: initial;
      --tw-exit-translate-y: initial;
    }
  }
  .data-\\[state\\=closed\\]\\:duration-300 {
    &[data-state="closed"] {
      animation-duration: 300ms;
    }
  }
  .data-\\[state\\=closed\\]\\:fade-out-0 {
    &[data-state="closed"] {
      --tw-exit-opacity: 0;
    }
  }
  .data-\\[state\\=closed\\]\\:fade-out-90 {
    &[data-state="closed"] {
      --tw-exit-opacity: 0.9;
    }
  }
  .data-\\[state\\=closed\\]\\:slide-out-to-bottom {
    &[data-state="closed"] {
      --tw-exit-translate-y: 100%;
    }
  }
  .data-\\[state\\=closed\\]\\:slide-out-to-bottom-\\[2\\%\\] {
    &[data-state="closed"] {
      --tw-exit-translate-y: 2%;
    }
  }
  .data-\\[state\\=closed\\]\\:slide-out-to-left {
    &[data-state="closed"] {
      --tw-exit-translate-x: -100%;
    }
  }
  .data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2 {
    &[data-state="closed"] {
      --tw-exit-translate-x: -50%;
    }
  }
  .data-\\[state\\=closed\\]\\:slide-out-to-right {
    &[data-state="closed"] {
      --tw-exit-translate-x: 100%;
    }
  }
  .data-\\[state\\=closed\\]\\:slide-out-to-top {
    &[data-state="closed"] {
      --tw-exit-translate-y: -100%;
    }
  }
  .data-\\[state\\=closed\\]\\:slide-out-to-top-\\[2\\%\\] {
    &[data-state="closed"] {
      --tw-exit-translate-y: -2%;
    }
  }
  .data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\] {
    &[data-state="closed"] {
      --tw-exit-translate-y: -48%;
    }
  }
  .data-\\[state\\=closed\\]\\:zoom-out-90 {
    &[data-state="closed"] {
      --tw-exit-scale: .9;
    }
  }
  .data-\\[state\\=closed\\]\\:zoom-out-95 {
    &[data-state="closed"] {
      --tw-exit-scale: .95;
    }
  }
  .data-\\[state\\=hidden\\]\\:animate-out {
    &[data-state="hidden"] {
      animation-name: exit;
      animation-duration: 150ms;
      --tw-exit-opacity: initial;
      --tw-exit-scale: initial;
      --tw-exit-rotate: initial;
      --tw-exit-translate-x: initial;
      --tw-exit-translate-y: initial;
    }
  }
  .data-\\[state\\=hidden\\]\\:fade-out {
    &[data-state="hidden"] {
      --tw-exit-opacity: 0;
    }
  }
  .data-\\[state\\=on\\]\\:bg-accent {
    &[data-state="on"] {
      background-color: hsl(var(--accent));
    }
  }
  .data-\\[state\\=on\\]\\:text-accent-foreground {
    &[data-state="on"] {
      color: hsl(var(--accent-foreground));
    }
  }
  .data-\\[state\\=open\\]\\:animate-accordion-down {
    &[data-state="open"] {
      animation: accordion-down 0.2s ease-out;
    }
  }
  .data-\\[state\\=open\\]\\:bg-accent {
    &[data-state="open"] {
      background-color: hsl(var(--accent));
    }
  }
  .data-\\[state\\=open\\]\\:bg-accent\\/50 {
    &[data-state="open"] {
      background-color: hsl(var(--accent));
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
      }
    }
  }
  .data-\\[state\\=open\\]\\:bg-secondary {
    &[data-state="open"] {
      background-color: hsl(var(--secondary));
    }
  }
  .data-\\[state\\=open\\]\\:text-accent-foreground {
    &[data-state="open"] {
      color: hsl(var(--accent-foreground));
    }
  }
  .data-\\[state\\=open\\]\\:text-muted-foreground {
    &[data-state="open"] {
      color: hsl(var(--muted-foreground));
    }
  }
  .data-\\[state\\=open\\]\\:opacity-100 {
    &[data-state="open"] {
      opacity: 100%;
    }
  }
  .data-\\[state\\=open\\]\\:duration-500 {
    &[data-state="open"] {
      --tw-duration: 500ms;
      transition-duration: 500ms;
    }
  }
  .data-\\[state\\=open\\]\\:animate-in {
    &[data-state="open"] {
      animation-name: enter;
      animation-duration: 150ms;
      --tw-enter-opacity: initial;
      --tw-enter-scale: initial;
      --tw-enter-rotate: initial;
      --tw-enter-translate-x: initial;
      --tw-enter-translate-y: initial;
    }
  }
  .data-\\[state\\=open\\]\\:duration-500 {
    &[data-state="open"] {
      animation-duration: 500ms;
    }
  }
  .data-\\[state\\=open\\]\\:fade-in-0 {
    &[data-state="open"] {
      --tw-enter-opacity: 0;
    }
  }
  .data-\\[state\\=open\\]\\:fade-in-90 {
    &[data-state="open"] {
      --tw-enter-opacity: 0.9;
    }
  }
  .data-\\[state\\=open\\]\\:slide-in-from-bottom {
    &[data-state="open"] {
      --tw-enter-translate-y: 100%;
    }
  }
  .data-\\[state\\=open\\]\\:slide-in-from-bottom-\\[2\\%\\] {
    &[data-state="open"] {
      --tw-enter-translate-y: 2%;
    }
  }
  .data-\\[state\\=open\\]\\:slide-in-from-left {
    &[data-state="open"] {
      --tw-enter-translate-x: -100%;
    }
  }
  .data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2 {
    &[data-state="open"] {
      --tw-enter-translate-x: -50%;
    }
  }
  .data-\\[state\\=open\\]\\:slide-in-from-right {
    &[data-state="open"] {
      --tw-enter-translate-x: 100%;
    }
  }
  .data-\\[state\\=open\\]\\:slide-in-from-top {
    &[data-state="open"] {
      --tw-enter-translate-y: -100%;
    }
  }
  .data-\\[state\\=open\\]\\:slide-in-from-top-\\[2\\%\\] {
    &[data-state="open"] {
      --tw-enter-translate-y: -2%;
    }
  }
  .data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\] {
    &[data-state="open"] {
      --tw-enter-translate-y: -48%;
    }
  }
  .data-\\[state\\=open\\]\\:zoom-in-90 {
    &[data-state="open"] {
      --tw-enter-scale: .9;
    }
  }
  .data-\\[state\\=open\\]\\:zoom-in-95 {
    &[data-state="open"] {
      --tw-enter-scale: .95;
    }
  }
  .data-\\[state\\=open\\]\\:hover\\:bg-accent {
    &[data-state="open"] {
      &:hover {
        @media (hover: hover) {
          background-color: hsl(var(--accent));
        }
      }
    }
  }
  .data-\\[state\\=open\\]\\:focus\\:bg-accent {
    &[data-state="open"] {
      &:focus {
        background-color: hsl(var(--accent));
      }
    }
  }
  .data-\\[state\\=selected\\]\\:bg-muted {
    &[data-state="selected"] {
      background-color: hsl(var(--muted));
    }
  }
  .data-\\[state\\=unchecked\\]\\:translate-x-0 {
    &[data-state="unchecked"] {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .data-\\[state\\=unchecked\\]\\:bg-input {
    &[data-state="unchecked"] {
      background-color: hsl(var(--input));
    }
  }
  .data-\\[state\\=visible\\]\\:animate-in {
    &[data-state="visible"] {
      animation-name: enter;
      animation-duration: 150ms;
      --tw-enter-opacity: initial;
      --tw-enter-scale: initial;
      --tw-enter-rotate: initial;
      --tw-enter-translate-x: initial;
      --tw-enter-translate-y: initial;
    }
  }
  .data-\\[state\\=visible\\]\\:fade-in {
    &[data-state="visible"] {
      --tw-enter-opacity: 0;
    }
  }
  .sm\\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\\:mr-auto {
    @media (width >= 40rem) {
      margin-right: auto;
    }
  }
  .sm\\:ml-2 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 2);
    }
  }
  .sm\\:block {
    @media (width >= 40rem) {
      display: block;
    }
  }
  .sm\\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\\:inline {
    @media (width >= 40rem) {
      display: inline;
    }
  }
  .sm\\:h-\\[96px\\] {
    @media (width >= 40rem) {
      height: 96px;
    }
  }
  .sm\\:h-full {
    @media (width >= 40rem) {
      height: 100%;
    }
  }
  .sm\\:w-\\[548px\\] {
    @media (width >= 40rem) {
      width: 548px;
    }
  }
  .sm\\:max-w-2xl {
    @media (width >= 40rem) {
      max-width: var(--container-2xl);
    }
  }
  .sm\\:max-w-full {
    @media (width >= 40rem) {
      max-width: 100%;
    }
  }
  .sm\\:max-w-lg {
    @media (width >= 40rem) {
      max-width: var(--container-lg);
    }
  }
  .sm\\:max-w-sm {
    @media (width >= 40rem) {
      max-width: var(--container-sm);
    }
  }
  .sm\\:max-w-xl {
    @media (width >= 40rem) {
      max-width: var(--container-xl);
    }
  }
  .sm\\:max-w-xs {
    @media (width >= 40rem) {
      max-width: var(--container-xs);
    }
  }
  .sm\\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\\:grid-cols-3 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .sm\\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\\:items-stretch {
    @media (width >= 40rem) {
      align-items: stretch;
    }
  }
  .sm\\:justify-between {
    @media (width >= 40rem) {
      justify-content: space-between;
    }
  }
  .sm\\:justify-end {
    @media (width >= 40rem) {
      justify-content: flex-end;
    }
  }
  .sm\\:justify-start {
    @media (width >= 40rem) {
      justify-content: flex-start;
    }
  }
  .sm\\:gap-2\\.5 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 2.5);
    }
  }
  .sm\\:space-y-0 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\\:space-x-2 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\\:space-x-4 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\\:rounded-lg {
    @media (width >= 40rem) {
      border-radius: var(--radius);
    }
  }
  .sm\\:px-4 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .sm\\:pr-4 {
    @media (width >= 40rem) {
      padding-right: calc(var(--spacing) * 4);
    }
  }
  .sm\\:pl-4 {
    @media (width >= 40rem) {
      padding-left: calc(var(--spacing) * 4);
    }
  }
  .sm\\:text-left {
    @media (width >= 40rem) {
      text-align: left;
    }
  }
  .sm\\:text-base {
    @media (width >= 40rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .sm\\:text-text-lg-bold {
    @media (width >= 40rem) {
      font-size: 24px;
      font-weight: var(--tw-font-weight, 700);
    }
  }
  .sm\\:text-\\[36px\\] {
    @media (width >= 40rem) {
      font-size: 36px;
    }
  }
  .md\\:absolute {
    @media (width >= 48rem) {
      position: absolute;
    }
  }
  .md\\:right-4 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 4);
    }
  }
  .md\\:order-1 {
    @media (width >= 48rem) {
      order: 1;
    }
  }
  .md\\:order-2 {
    @media (width >= 48rem) {
      order: 2;
    }
  }
  .md\\:col-span-2 {
    @media (width >= 48rem) {
      grid-column: span 2 / span 2;
    }
  }
  .md\\:col-span-3 {
    @media (width >= 48rem) {
      grid-column: span 3 / span 3;
    }
  }
  .md\\:mx-auto {
    @media (width >= 48rem) {
      margin-inline: auto;
    }
  }
  .md\\:mt-0 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .md\\:mt-4 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 4);
    }
  }
  .md\\:mt-\\[26px\\] {
    @media (width >= 48rem) {
      margin-top: 26px;
    }
  }
  .md\\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\\:ml-auto {
    @media (width >= 48rem) {
      margin-left: auto;
    }
  }
  .md\\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\\:inline {
    @media (width >= 48rem) {
      display: inline;
    }
  }
  .md\\:inline-flex {
    @media (width >= 48rem) {
      display: inline-flex;
    }
  }
  .md\\:h-\\[12px\\] {
    @media (width >= 48rem) {
      height: 12px;
    }
  }
  .md\\:h-\\[100px\\] {
    @media (width >= 48rem) {
      height: 100px;
    }
  }
  .md\\:h-\\[120px\\] {
    @media (width >= 48rem) {
      height: 120px;
    }
  }
  .md\\:h-\\[128px\\] {
    @media (width >= 48rem) {
      height: 128px;
    }
  }
  .md\\:h-\\[183px\\] {
    @media (width >= 48rem) {
      height: 183px;
    }
  }
  .md\\:h-\\[411px\\] {
    @media (width >= 48rem) {
      height: 411px;
    }
  }
  .md\\:min-h-\\[40px\\] {
    @media (width >= 48rem) {
      min-height: 40px;
    }
  }
  .md\\:min-h-\\[400px\\] {
    @media (width >= 48rem) {
      min-height: 400px;
    }
  }
  .md\\:min-h-\\[500px\\] {
    @media (width >= 48rem) {
      min-height: 500px;
    }
  }
  .md\\:min-h-\\[600px\\] {
    @media (width >= 48rem) {
      min-height: 600px;
    }
  }
  .md\\:min-h-\\[700px\\] {
    @media (width >= 48rem) {
      min-height: 700px;
    }
  }
  .md\\:w-1\\/2 {
    @media (width >= 48rem) {
      width: calc(1/2 * 100%);
    }
  }
  .md\\:w-1\\/3 {
    @media (width >= 48rem) {
      width: calc(1/3 * 100%);
    }
  }
  .md\\:w-2\\/3 {
    @media (width >= 48rem) {
      width: calc(2/3 * 100%);
    }
  }
  .md\\:w-3\\/4 {
    @media (width >= 48rem) {
      width: calc(3/4 * 100%);
    }
  }
  .md\\:w-60 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 60);
    }
  }
  .md\\:w-64 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 64);
    }
  }
  .md\\:w-\\[100px\\] {
    @media (width >= 48rem) {
      width: 100px;
    }
  }
  .md\\:w-\\[120px\\] {
    @media (width >= 48rem) {
      width: 120px;
    }
  }
  .md\\:w-\\[180px\\] {
    @media (width >= 48rem) {
      width: 180px;
    }
  }
  .md\\:w-\\[444px\\] {
    @media (width >= 48rem) {
      width: 444px;
    }
  }
  .md\\:w-\\[509px\\] {
    @media (width >= 48rem) {
      width: 509px;
    }
  }
  .md\\:w-\\[645px\\] {
    @media (width >= 48rem) {
      width: 645px;
    }
  }
  .md\\:w-\\[693px\\] {
    @media (width >= 48rem) {
      width: 693px;
    }
  }
  .md\\:w-\\[1464px\\] {
    @media (width >= 48rem) {
      width: 1464px;
    }
  }
  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\] {
    @media (width >= 48rem) {
      width: var(--radix-navigation-menu-viewport-width);
    }
  }
  .md\\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\\:max-w-2xl {
    @media (width >= 48rem) {
      max-width: var(--container-2xl);
    }
  }
  .md\\:max-w-\\[240px\\] {
    @media (width >= 48rem) {
      max-width: 240px;
    }
  }
  .md\\:max-w-\\[596px\\] {
    @media (width >= 48rem) {
      max-width: 596px;
    }
  }
  .md\\:flex-none {
    @media (width >= 48rem) {
      flex: none;
    }
  }
  .md\\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\\:items-center {
    @media (width >= 48rem) {
      align-items: center;
    }
  }
  .md\\:items-start {
    @media (width >= 48rem) {
      align-items: flex-start;
    }
  }
  .md\\:justify-between {
    @media (width >= 48rem) {
      justify-content: space-between;
    }
  }
  .md\\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\\:space-y-0 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\\:space-x-3 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\\:space-x-8 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\\:truncate {
    @media (width >= 48rem) {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .md\\:rounded-\\[24px\\] {
    @media (width >= 48rem) {
      border-radius: 24px;
    }
  }
  .md\\:rounded-t-\\[32px\\] {
    @media (width >= 48rem) {
      border-top-left-radius: 32px;
      border-top-right-radius: 32px;
    }
  }
  .md\\:rounded-b-\\[30\\%\\] {
    @media (width >= 48rem) {
      border-bottom-right-radius: 30%;
      border-bottom-left-radius: 30%;
    }
  }
  .md\\:border-t-0 {
    @media (width >= 48rem) {
      border-top-style: var(--tw-border-style);
      border-top-width: 0px;
    }
  }
  .md\\:border-b-0 {
    @media (width >= 48rem) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .md\\:p-0 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 0);
    }
  }
  .md\\:p-3 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 3);
    }
  }
  .md\\:p-4 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .md\\:p-6 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\\:p-8 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .md\\:px-6 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .md\\:px-8 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .md\\:px-\\[49px\\] {
    @media (width >= 48rem) {
      padding-inline: 49px;
    }
  }
  .md\\:py-0 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 0);
    }
  }
  .md\\:py-12 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 12);
    }
  }
  .md\\:py-16 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 16);
    }
  }
  .md\\:pt-0 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .md\\:pb-0 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\\:pl-4 {
    @media (width >= 48rem) {
      padding-left: calc(var(--spacing) * 4);
    }
  }
  .md\\:text-2xl {
    @media (width >= 48rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .md\\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .md\\:text-5xl {
    @media (width >= 48rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .md\\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\\:text-sm {
    @media (width >= 48rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .md\\:text-xs {
    @media (width >= 48rem) {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .md\\:text-h2 {
    @media (width >= 48rem) {
      font-size: 32px;
      font-weight: var(--tw-font-weight, 600);
    }
  }
  .md\\:text-h3 {
    @media (width >= 48rem) {
      font-size: 24px;
      font-weight: var(--tw-font-weight, 600);
    }
  }
  .md\\:text-text-md {
    @media (width >= 48rem) {
      font-size: 18px;
      font-weight: var(--tw-font-weight, 400);
    }
  }
  .md\\:text-text-xl {
    @media (width >= 48rem) {
      font-size: 32px;
      font-weight: var(--tw-font-weight, 700);
    }
  }
  .md\\:leading-\\[48px\\] {
    @media (width >= 48rem) {
      --tw-leading: 48px;
      line-height: 48px;
    }
  }
  .md\\:opacity-0 {
    @media (width >= 48rem) {
      opacity: 0%;
    }
  }
  .md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        margin: calc(var(--spacing) * 2);
      }
    }
  }
  .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        margin-left: calc(var(--spacing) * 0);
      }
    }
  }
  .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        border-radius: var(--radius-xl);
      }
    }
  }
  .md\\:peer-data-\\[variant\\=inset\\]\\:shadow {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-variant="inset"] ~ *) {
        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .md\\:peer-data-\\[state\\=collapsed\\]\\:peer-data-\\[variant\\=inset\\]\\:ml-2 {
    @media (width >= 48rem) {
      &:is(:where(.peer)[data-state="collapsed"] ~ *) {
        &:is(:where(.peer)[data-variant="inset"] ~ *) {
          margin-left: calc(var(--spacing) * 2);
        }
      }
    }
  }
  .after\\:md\\:hidden {
    &::after {
      content: var(--tw-content);
      @media (width >= 48rem) {
        display: none;
      }
    }
  }
  .lg\\:col-span-1 {
    @media (width >= 64rem) {
      grid-column: span 1 / span 1;
    }
  }
  .lg\\:col-span-3 {
    @media (width >= 64rem) {
      grid-column: span 3 / span 3;
    }
  }
  .lg\\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .\\@xs\\:p-2 {
    @container (width >= 20rem) {
      padding: calc(var(--spacing) * 2);
    }
  }
  .\\@sm\\:p-3 {
    @container (width >= 24rem) {
      padding: calc(var(--spacing) * 3);
    }
  }
  .\\@md\\:p-4 {
    @container (width >= 28rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .dark\\:scale-0 {
    &:is([data-mode='dark'] *) {
      --tw-scale-x: 0%;
      --tw-scale-y: 0%;
      --tw-scale-z: 0%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .dark\\:scale-100 {
    &:is([data-mode='dark'] *) {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .dark\\:-rotate-90 {
    &:is([data-mode='dark'] *) {
      rotate: calc(90deg * -1);
    }
  }
  .dark\\:rotate-0 {
    &:is([data-mode='dark'] *) {
      rotate: 0deg;
    }
  }
  .dark\\:border-destructive {
    &:is([data-mode='dark'] *) {
      border-color: hsl(var(--destructive));
    }
  }
  .dark\\:border-foreground {
    &:is([data-mode='dark'] *) {
      border-color: hsl(var(--foreground));
    }
  }
  .dark\\:border-red-800 {
    &:is([data-mode='dark'] *) {
      border-color: var(--color-red-800);
    }
  }
  .dark\\:bg-background {
    &:is([data-mode='dark'] *) {
      background-color: hsl(var(--background));
    }
  }
  .dark\\:bg-black\\/50 {
    &:is([data-mode='dark'] *) {
      background-color: color-mix(in srgb, #000 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
      }
    }
  }
  .dark\\:bg-card {
    &:is([data-mode='dark'] *) {
      background-color: hsl(var(--card));
    }
  }
  .dark\\:bg-card\\/10 {
    &:is([data-mode='dark'] *) {
      background-color: hsl(var(--card));
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, hsl(var(--card)) 10%, transparent);
      }
    }
  }
  .dark\\:bg-gray-700 {
    &:is([data-mode='dark'] *) {
      background-color: var(--color-gray-700);
    }
  }
  .dark\\:bg-gray-800 {
    &:is([data-mode='dark'] *) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\\:bg-red-900\\/20 {
    &:is([data-mode='dark'] *) {
      background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);
      }
    }
  }
  .dark\\:text-card-foreground {
    &:is([data-mode='dark'] *) {
      color: hsl(var(--card-foreground));
    }
  }
  .dark\\:text-foreground {
    &:is([data-mode='dark'] *) {
      color: hsl(var(--foreground));
    }
  }
  .dark\\:text-foreground\\/70 {
    &:is([data-mode='dark'] *) {
      color: hsl(var(--foreground));
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, hsl(var(--foreground)) 70%, transparent);
      }
    }
  }
  .dark\\:text-foreground\\/80 {
    &:is([data-mode='dark'] *) {
      color: hsl(var(--foreground));
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, hsl(var(--foreground)) 80%, transparent);
      }
    }
  }
  .dark\\:text-foreground\\/90 {
    &:is([data-mode='dark'] *) {
      color: hsl(var(--foreground));
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, hsl(var(--foreground)) 90%, transparent);
      }
    }
  }
  .dark\\:text-gray-400 {
    &:is([data-mode='dark'] *) {
      color: var(--color-gray-400);
    }
  }
  .dark\\:text-green-400 {
    &:is([data-mode='dark'] *) {
      color: var(--color-green-400);
    }
  }
  .dark\\:text-primary {
    &:is([data-mode='dark'] *) {
      color: hsl(var(--primary));
    }
  }
  .dark\\:text-red-300 {
    &:is([data-mode='dark'] *) {
      color: var(--color-red-300);
    }
  }
  .dark\\:text-red-400 {
    &:is([data-mode='dark'] *) {
      color: var(--color-red-400);
    }
  }
  .dark\\:hover\\:bg-gray-800 {
    &:is([data-mode='dark'] *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-800);
        }
      }
    }
  }
  .dark\\:hover\\:text-foreground {
    &:is([data-mode='dark'] *) {
      &:hover {
        @media (hover: hover) {
          color: hsl(var(--foreground));
        }
      }
    }
  }
  .\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground {
    & .recharts-cartesian-axis-tick text {
      fill: hsl(var(--muted-foreground));
    }
  }
  .\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 {
    & .recharts-cartesian-grid line[stroke='#ccc'] {
      stroke: hsl(var(--border));
      @supports (color: color-mix(in lab, red, red)) {
        stroke: color-mix(in oklab, hsl(var(--border)) 50%, transparent);
      }
    }
  }
  .\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border {
    & .recharts-curve.recharts-tooltip-cursor {
      stroke: hsl(var(--border));
    }
  }
  .\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {
    & .recharts-dot[stroke='#fff'] {
      stroke: transparent;
    }
  }
  .\\[\\&_\\.recharts-layer\\]\\:outline-none {
    & .recharts-layer {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {
    & .recharts-polar-grid [stroke='#ccc'] {
      stroke: hsl(var(--border));
    }
  }
  .\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted {
    & .recharts-radial-bar-background-sector {
      fill: hsl(var(--muted));
    }
  }
  .\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted {
    & .recharts-rectangle.recharts-tooltip-cursor {
      fill: hsl(var(--muted));
    }
  }
  .\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {
    & .recharts-reference-line [stroke='#ccc'] {
      stroke: hsl(var(--border));
    }
  }
  .\\[\\&_\\.recharts-sector\\]\\:outline-none {
    & .recharts-sector {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {
    & .recharts-sector[stroke='#fff'] {
      stroke: transparent;
    }
  }
  .\\[\\&_\\.recharts-surface\\]\\:outline-none {
    & .recharts-surface {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 {
    & [cmdk-group-heading] {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 {
    & [cmdk-group-heading] {
      padding-block: calc(var(--spacing) * 1.5);
    }
  }
  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs {
    & [cmdk-group-heading] {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium {
    & [cmdk-group-heading] {
      --tw-font-weight: var(--font-weight-medium);
      font-weight: var(--font-weight-medium);
    }
  }
  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground {
    & [cmdk-group-heading] {
      color: hsl(var(--muted-foreground));
    }
  }
  .\\[\\&_\\[cmdk-group\\]\\]\\:px-2 {
    & [cmdk-group] {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 {
    & [cmdk-group]:not([hidden]) ~[cmdk-group] {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 {
    & [cmdk-input-wrapper] svg {
      height: calc(var(--spacing) * 5);
    }
  }
  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 {
    & [cmdk-input-wrapper] svg {
      width: calc(var(--spacing) * 5);
    }
  }
  .\\[\\&_\\[cmdk-input\\]\\]\\:h-12 {
    & [cmdk-input] {
      height: calc(var(--spacing) * 12);
    }
  }
  .\\[\\&_\\[cmdk-item\\]\\]\\:px-2 {
    & [cmdk-item] {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .\\[\\&_\\[cmdk-item\\]\\]\\:py-3 {
    & [cmdk-item] {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 {
    & [cmdk-item] svg {
      height: calc(var(--spacing) * 5);
    }
  }
  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 {
    & [cmdk-item] svg {
      width: calc(var(--spacing) * 5);
    }
  }
  .\\[\\&_p\\]\\:leading-relaxed {
    & p {
      --tw-leading: var(--leading-relaxed);
      line-height: var(--leading-relaxed);
    }
  }
  .\\[\\&_svg\\]\\:pointer-events-none {
    & svg {
      pointer-events: none;
    }
  }
  .\\[\\&_svg\\]\\:size-4 {
    & svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .\\[\\&_svg\\]\\:shrink-0 {
    & svg {
      flex-shrink: 0;
    }
  }
  .\\[\\&_tr\\]\\:border-b {
    & tr {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }
  .\\[\\&_tr\\:last-child\\]\\:border-0 {
    & tr:last-child {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .\\[\\&\\:\\:-webkit-inner-spin-button\\]\\:appearance-none {
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
              appearance: none;
    }
  }
  .\\[\\&\\:\\:-webkit-outer-spin-button\\]\\:appearance-none {
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
              appearance: none;
    }
  }
  .\\[\\&\\:has\\(\\>\\.day-range-end\\)\\]\\:rounded-r-md {
    &:has(>.day-range-end) {
      border-top-right-radius: calc(var(--radius) - 2px);
      border-bottom-right-radius: calc(var(--radius) - 2px);
    }
  }
  .\\[\\&\\:has\\(\\>\\.day-range-start\\)\\]\\:rounded-l-md {
    &:has(>.day-range-start) {
      border-top-left-radius: calc(var(--radius) - 2px);
      border-bottom-left-radius: calc(var(--radius) - 2px);
    }
  }
  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-md {
    &:has([aria-selected]) {
      border-radius: calc(var(--radius) - 2px);
    }
  }
  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent {
    &:has([aria-selected]) {
      background-color: hsl(var(--accent));
    }
  }
  .first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md {
    &:first-child {
      &:has([aria-selected]) {
        border-top-left-radius: calc(var(--radius) - 2px);
        border-bottom-left-radius: calc(var(--radius) - 2px);
      }
    }
  }
  .last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md {
    &:last-child {
      &:has([aria-selected]) {
        border-top-right-radius: calc(var(--radius) - 2px);
        border-bottom-right-radius: calc(var(--radius) - 2px);
      }
    }
  }
  .\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50 {
    &:has([aria-selected].day-outside) {
      background-color: hsl(var(--accent));
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, hsl(var(--accent)) 50%, transparent);
      }
    }
  }
  .\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md {
    &:has([aria-selected].day-range-end) {
      border-top-right-radius: calc(var(--radius) - 2px);
      border-bottom-right-radius: calc(var(--radius) - 2px);
    }
  }
  .\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0 {
    &:has([role=checkbox]) {
      padding-right: calc(var(--spacing) * 0);
    }
  }
  .\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\] {
    &>[role=checkbox] {
      --tw-translate-y: 2px;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .\\[\\&\\>button\\]\\:hidden {
    &>button {
      display: none;
    }
  }
  .\\[\\&\\>div\\]\\:bg-yellow-400 {
    &>div {
      background-color: var(--color-yellow-400);
    }
  }
  .\\[\\&\\>li\\:\\:marker\\]\\:text-xs {
    &>li::marker {
      font-size: var(--text-xs);
      line-height: var(--tw-leading, var(--text-xs--line-height));
    }
  }
  .\\[\\&\\>span\\]\\:line-clamp-1 {
    &>span {
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }
  .\\[\\&\\>span\\:last-child\\]\\:truncate {
    &>span:last-child {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .\\[\\&\\>svg\\]\\:absolute {
    &>svg {
      position: absolute;
    }
  }
  .\\[\\&\\>svg\\]\\:top-4 {
    &>svg {
      top: calc(var(--spacing) * 4);
    }
  }
  .\\[\\&\\>svg\\]\\:left-4 {
    &>svg {
      left: calc(var(--spacing) * 4);
    }
  }
  .\\[\\&\\>svg\\]\\:size-4 {
    &>svg {
      width: calc(var(--spacing) * 4);
      height: calc(var(--spacing) * 4);
    }
  }
  .\\[\\&\\>svg\\]\\:h-2\\.5 {
    &>svg {
      height: calc(var(--spacing) * 2.5);
    }
  }
  .\\[\\&\\>svg\\]\\:h-3 {
    &>svg {
      height: calc(var(--spacing) * 3);
    }
  }
  .\\[\\&\\>svg\\]\\:h-3\\.5 {
    &>svg {
      height: calc(var(--spacing) * 3.5);
    }
  }
  .\\[\\&\\>svg\\]\\:w-2\\.5 {
    &>svg {
      width: calc(var(--spacing) * 2.5);
    }
  }
  .\\[\\&\\>svg\\]\\:w-3 {
    &>svg {
      width: calc(var(--spacing) * 3);
    }
  }
  .\\[\\&\\>svg\\]\\:w-3\\.5 {
    &>svg {
      width: calc(var(--spacing) * 3.5);
    }
  }
  .\\[\\&\\>svg\\]\\:shrink-0 {
    &>svg {
      flex-shrink: 0;
    }
  }
  .\\[\\&\\>svg\\]\\:text-destructive {
    &>svg {
      color: hsl(var(--destructive));
    }
  }
  .\\[\\&\\>svg\\]\\:text-foreground {
    &>svg {
      color: hsl(var(--foreground));
    }
  }
  .\\[\\&\\>svg\\]\\:text-muted-foreground {
    &>svg {
      color: hsl(var(--muted-foreground));
    }
  }
  .\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\] {
    &>svg+div {
      --tw-translate-y: -3px;
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .\\[\\&\\>svg\\~\\*\\]\\:pl-7 {
    &>svg~* {
      padding-left: calc(var(--spacing) * 7);
    }
  }
  .\\[\\&\\>tr\\]\\:last\\:border-b-0 {
    &>tr {
      &:last-child {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 0px;
      }
    }
  }
  .\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90 {
    &[data-panel-group-direction=vertical]>div {
      rotate: 90deg;
    }
  }
  .\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180 {
    &[data-state=open]>svg {
      rotate: 180deg;
    }
  }
  .\\[\\[data-side\\=left\\]_\\&\\]\\:cursor-w-resize {
    [data-side=left] & {
      cursor: w-resize;
    }
  }
  .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {
    [data-side=left][data-collapsible=offcanvas] & {
      right: calc(var(--spacing) * -2);
    }
  }
  .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {
    [data-side=left][data-state=collapsed] & {
      cursor: e-resize;
    }
  }
  .\\[\\[data-side\\=right\\]_\\&\\]\\:cursor-e-resize {
    [data-side=right] & {
      cursor: e-resize;
    }
  }
  .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {
    [data-side=right][data-collapsible=offcanvas] & {
      left: calc(var(--spacing) * -2);
    }
  }
  .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {
    [data-side=right][data-state=collapsed] & {
      cursor: w-resize;
    }
  }
}
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 12%;
    --background-secondary: 220 20% 97%;
    --background-secondary-foreground: 0 0% 12%;
    --card: 220 20% 97%;
    --card-foreground: 0 0% 12%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 12%;
    --primary: 233 73% 26%;
    --primary-foreground: 0 0% 100%;
    --secondary: 40 100% 50%;
    --secondary-foreground: 0 0% 12%;
    --muted: 220 20% 97%;
    --muted-foreground: 225 12% 60%;
    --accent: 205, 42%, 91%;
    --accent-foreground: 233 73% 26%;
    --destructive: 359, 100%, 96%;
    --destructive-foreground: 359, 100%, 62%;
    --border: 225 12% 60%;
    --input: 0 0% 94%;
    --ring: 233 80% 35%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 12%;
    --sidebar-primary: 233 73% 26%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 205, 42%, 91%;
    --sidebar-accent-foreground: 233 73% 26%;
    --sidebar-border: 0 0% 94%;
    --sidebar-ring: 0 0% 94%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --success: 159 100% 37%;
    --success-foreground: 160 56% 94%;
    --error: 359, 100%, 62%;
    --error-foreground: 0 100% 96%;
    --background-BG: 220 20% 97%;
    --color-stroke: 0 0% 94%;
    --placeholder: 225 12% 60%;
    --title-main: 0 0% 12%;
    --White: 0 0% 100%;
    --discription: 221 18% 44%;
    --discription-secondary: 235 19% 35%;
    --Highlight: 220 100% 98%;
    --test: 235 19% 35%;
    --Menu: 205 42% 91%;
    --title-secondary: 225 10% 52%;
    --secondary-bg: 39 100% 95%;
  }
  .dark {
    --background: 232 23% 10%;
    --foreground: 0 0% 98%;
    --background-secondary: 232 23% 15%;
    --background-secondary-foreground: 0 0% 98%;
    --card: 232 23% 15%;
    --card-foreground: 0 0% 98%;
    --popover: 232 23% 13%;
    --popover-foreground: 0 0% 98%;
    --primary: 233 65% 55%;
    --primary-foreground: 0 0% 100%;
    --secondary: 40 90% 45%;
    --secondary-foreground: 0 0% 12%;
    --muted: 232 23% 20%;
    --muted-foreground: 220 20% 70%;
    --accent: 205 42% 40%;
    --accent-foreground: 205 85% 90%;
    --destructive: 359 85% 30%;
    --destructive-foreground: 359 100% 94%;
    --border: 232 23% 20%;
    --input: 232 23% 20%;
    --ring: 233 65% 60%;
    --sidebar-background: 232 23% 13%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 233 65% 55%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 205 42% 40%;
    --sidebar-accent-foreground: 205 85% 90%;
    --sidebar-border: 232 23% 20%;
    --sidebar-ring: 233 65% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 70% 45%;
    --chart-3: 197 60% 40%;
    --chart-4: 43 74% 60%;
    --chart-5: 27 87% 67%;
    --success: 159 80% 30%;
    --success-foreground: 160 85% 94%;
    --error: 359 85% 45%;
    --error-foreground: 0 0% 100%;
  }
}
@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}
@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}
@layer components {
  [data-radix-hover-card-content] {
    z-index: 100 !important;
  }
  [data-radix-tooltip-content] {
    z-index: 60 !important;
  }
}
.custom-bullets {
  height: calc(var(--spacing) * 2);
  width: calc(var(--spacing) * 2);
  border-radius: calc(infinity * 1px);
  background-color: var(--color-gray-300);
  opacity: 100%;
  transition-property: all;
  transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
  transition-duration: var(--tw-duration, var(--default-transition-duration));
  --tw-duration: 300ms;
  transition-duration: 300ms;
  animation-duration: 300ms;
}
@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false;
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}
@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}
`, "",{"version":3,"sources":["webpack://./src/styles/globals.css"],"names":[],"mappings":"AAAA,gEA6IA;AA7IA,iBA6IA;AA7IA,yCA6IA;AA7IA;EAAA;IAAA;6DA6IA;IA7IA;8BA6IA;IA7IA,wCA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,6CA6IA;IA7IA,2CA6IA;IA7IA,6CA6IA;IA7IA,6CA6IA;IA7IA,2CA6IA;IA7IA,4CA6IA;IA7IA,2CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,6CA6IA;IA7IA,6CA6IA;IA7IA,6CA6IA;IA7IA,6CA6IA;IA7IA,6CA6IA;IA7IA,6CA6IA;IA7IA,6CA6IA;IA7IA,yCA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,8CA6IA;IA7IA,8CA6IA;IA7IA,6CA6IA;IA7IA,8CA6IA;IA7IA,8CA6IA;IA7IA,6CA6IA;IA7IA,8CA6IA;IA7IA,8CA6IA;IA7IA,4CA6IA;IA7IA,8CA6IA;IA7IA,4CA6IA;IA7IA,6CA6IA;IA7IA,2CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,2CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,2CA6IA;IA7IA,4CA6IA;IA7IA,4CA6IA;IA7IA,0CA6IA;IA7IA,0CA6IA;IA7IA,mBA6IA;IA7IA,mBA6IA;IA7IA,kBA6IA;IA7IA,qBA6IA;IA7IA,qBA6IA;IA7IA,qBA6IA;IA7IA,qBA6IA;IA7IA,qBA6IA;IA7IA,sBA6IA;IA7IA,sBA6IA;IA7IA,sBA6IA;IA7IA,sBA6IA;IA7IA,sBA6IA;IA7IA,sBA6IA;IA7IA,kBA6IA;IA7IA,sCA6IA;IA7IA,mBA6IA;IA7IA,0CA6IA;IA7IA,iBA6IA;IA7IA,uCA6IA;IA7IA,mBA6IA;IA7IA,0CA6IA;IA7IA,kBA6IA;IA7IA,yCA6IA;IA7IA,kBA6IA;IA7IA,sCA6IA;IA7IA,oBA6IA;IA7IA,2CA6IA;IA7IA,mBA6IA;IA7IA,yCA6IA;IA7IA,gBA6IA;IA7IA,0BA6IA;IA7IA,yBA6IA;IA7IA,yBA6IA;IA7IA,2BA6IA;IA7IA,uBA6IA;IA7IA,0BA6IA;IA7IA,wBA6IA;IA7IA,wBA6IA;IA7IA,oBA6IA;IA7IA,kBA6IA;IA7IA,6CA6IA;IA7IA,sCA6IA;IA7IA,2CA6IA;IA7IA,uCA6IA;IA7IA,+DA6IA;IA7IA,cA6IA;IA7IA,sBA6IA;IA7IA,oCA6IA;IA7IA,kEA6IA;IA7IA,uCA6IA;IA7IA,4CA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,sBA6IA;IA7IA,SA6IA;IA7IA,UA6IA;IA7IA,eA6IA;EAAA;EA7IA;IAAA,gBA6IA;IA7IA,8BA6IA;IA7IA,gBA6IA;MA7IA,cA6IA;SA7IA,WA6IA;IA7IA,2JA6IA;IA7IA,mEA6IA;IA7IA,uEA6IA;IA7IA,wCA6IA;EAAA;EA7IA;IAAA,SA6IA;IA7IA,cA6IA;IA7IA,qBA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA,iCA6IA;EAAA;EA7IA;IAAA,kBA6IA;IA7IA,oBA6IA;EAAA;EA7IA;IAAA,cA6IA;IA7IA,gCA6IA;IA7IA,wBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,gJA6IA;IA7IA,wEA6IA;IA7IA,4EA6IA;IA7IA,cA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,cA6IA;IA7IA,cA6IA;IA7IA,kBA6IA;IA7IA,wBA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,cA6IA;IA7IA,qBA6IA;IA7IA,yBA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,cA6IA;IA7IA,sBA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,YA6IA;EAAA;EA7IA;IAAA,aA6IA;IA7IA,8BA6IA;IA7IA,gCA6IA;IA7IA,uBA6IA;IA7IA,cA6IA;IA7IA,gBA6IA;IA7IA,6BA6IA;IA7IA,UA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,0BA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;MA7IA;QAAA,yDA6IA;MAAA;IAAA;IA7IA;MAAA,mBA6IA;MA7IA;QAAA,yDA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,mBA6IA;EAAA;EA7IA;IAAA,oBA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,0BA6IA;OA7IA,uBA6IA;YA7IA,kBA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,oBA6IA;EAAA;EA7IA;IAAA,oBA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,kBA6IA;IA7IA,UA6IA;IA7IA,WA6IA;IA7IA,UA6IA;IA7IA,YA6IA;IA7IA,gBA6IA;IA7IA,sBA6IA;IA7IA,mBA6IA;IA7IA,eA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,6CA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,QA6IA;EAAA;EA7IA;IAAA,QA6IA;EAAA;EA7IA;IAAA,QA6IA;EAAA;EA7IA;IAAA,SA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,SA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,WA6IA;IA7IA;MAAA,gBA6IA;IAAA;IA7IA;MAAA,gBA6IA;IAAA;IA7IA;MAAA,gBA6IA;IAAA;IA7IA;MAAA,gBA6IA;IAAA;IA7IA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA,mBA6IA;IA7IA,oBA6IA;IA7IA;MAAA,eA6IA;IAAA;IA7IA;MAAA,iBA6IA;IAAA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,gBA6IA;IA7IA,oBA6IA;IA7IA,4BA6IA;IA7IA,qBA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,oBA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,+BA6IA;IA7IA,gCA6IA;EAAA;EA7IA;IAAA,+BA6IA;IA7IA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,oDA6IA;EAAA;EA7IA;IAAA,0CA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,yDA6IA;EAAA;EA7IA;IAAA,mDA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,+DA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,uBA6IA;IA7IA,kBA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,2BA6IA;IA7IA,sBA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,4CA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,OA6IA;EAAA;EA7IA;IAAA,UA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,oBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,6CA6IA;IA7IA,sDA6IA;EAAA;EA7IA;IAAA,sBA6IA;IA7IA,sDA6IA;EAAA;EA7IA;IAAA,kCA6IA;IA7IA,sDA6IA;EAAA;EA7IA;IAAA,sBA6IA;IA7IA,sDA6IA;EAAA;EA7IA;IAAA,qBA6IA;IA7IA,sDA6IA;EAAA;EA7IA;IAAA,6CA6IA;IA7IA,sDA6IA;EAAA;EA7IA;IAAA,sBA6IA;IA7IA,sDA6IA;EAAA;EA7IA;IAAA,gBA6IA;IA7IA,gBA6IA;IA7IA,gBA6IA;IA7IA,0CA6IA;EAAA;EA7IA;IAAA,kBA6IA;IA7IA,kBA6IA;IA7IA,kBA6IA;IA7IA,0CA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,0GA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,6BA6IA;OA7IA,0BA6IA;YA7IA,qBA6IA;EAAA;EA7IA;IAAA,gDA6IA;EAAA;EA7IA;IAAA,gDA6IA;EAAA;EA7IA;IAAA,gDA6IA;EAAA;EA7IA;IAAA,gDA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,iDA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,oBA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,8EA6IA;MA7IA,sFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,gFA6IA;MA7IA,wFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,8EA6IA;MA7IA,sFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,8EA6IA;MA7IA,sFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,8EA6IA;MA7IA,sFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,8EA6IA;MA7IA,sFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,8EA6IA;MA7IA,sFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,+EA6IA;MA7IA,uFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,+EA6IA;MA7IA,uFA6IA;IAAA;EAAA;EA7IA;IAAA,yCA6IA;SA7IA,oCA6IA;EAAA;EA7IA;IAAA,0CA6IA;SA7IA,qCA6IA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,+EA6IA;MA7IA,uFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,+EA6IA;MA7IA,uFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,+EA6IA;MA7IA,uFA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;MA7IA,+EA6IA;MA7IA,uFA6IA;IAAA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;MA7IA,2CA6IA;MA7IA,iEA6IA;MA7IA,yEA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;MA7IA,2CA6IA;MA7IA,wCA6IA;MA7IA,wDA6IA;MA7IA,qEA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA,gBA6IA;IA7IA,uBA6IA;IA7IA,mBA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,+BA6IA;IA7IA,gCA6IA;EAAA;EA7IA;IAAA,4BA6IA;IA7IA,6BA6IA;EAAA;EA7IA;IAAA,4BA6IA;IA7IA,6BA6IA;EAAA;EA7IA;IAAA,iDA6IA;IA7IA,oDA6IA;EAAA;EA7IA;IAAA,yBA6IA;IA7IA,4BA6IA;EAAA;EA7IA;IAAA,wCA6IA;IA7IA,2CA6IA;EAAA;EA7IA;IAAA,iDA6IA;EAAA;EA7IA;IAAA,kDA6IA;IA7IA,qDA6IA;EAAA;EA7IA;IAAA,0BA6IA;IA7IA,6BA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA,4CA6IA;EAAA;EA7IA;IAAA,+BA6IA;IA7IA,8BA6IA;EAAA;EA7IA;IAAA,oCA6IA;IA7IA,iBA6IA;EAAA;EA7IA;IAAA,oCA6IA;IA7IA,iBA6IA;EAAA;EA7IA;IAAA,oCA6IA;IA7IA,iBA6IA;EAAA;EA7IA;IAAA,oCA6IA;IA7IA,iBA6IA;EAAA;EA7IA;IAAA,oCA6IA;IA7IA,mBA6IA;EAAA;EA7IA;IAAA,0CA6IA;IA7IA,uBA6IA;EAAA;EA7IA;IAAA,wCA6IA;IA7IA,qBA6IA;EAAA;EA7IA;IAAA,0CA6IA;IA7IA,uBA6IA;EAAA;EA7IA;IAAA,2CA6IA;IA7IA,wBA6IA;EAAA;EA7IA;IAAA,2CA6IA;IA7IA,wBA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA,sBA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA,sBA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA,sBA6IA;EAAA;EA7IA;IAAA,yBA6IA;IA7IA,oBA6IA;EAAA;EA7IA;IAAA,uBA6IA;IA7IA,kBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,gCA6IA;IA7IA;MAAA,sEA6IA;IAAA;EAAA;EA7IA;IAAA,0BA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA;MAAA,2EA6IA;IAAA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA;MAAA,2EA6IA;IAAA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;IA7IA;MAAA,uEA6IA;IAAA;EAAA;EA7IA;IAAA,iCA6IA;IA7IA;MAAA,uEA6IA;IAAA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,uDA6IA;IA7IA;MAAA,sEA6IA;IAAA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,+DA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,kDA6IA;EAAA;EA7IA;IAAA,6DA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,4CA6IA;EAAA;EA7IA;IAAA,uDA6IA;EAAA;EA7IA;IAAA,gDA6IA;EAAA;EA7IA;IAAA,gDA6IA;EAAA;EA7IA;IAAA,6CA6IA;EAAA;EA7IA;IAAA,wDA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,+CA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,2CA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,2DA6IA;IA7IA;MAAA,0EA6IA;IAAA;EAAA;EA7IA;IAAA,2DA6IA;IA7IA;MAAA,0EA6IA;IAAA;EAAA;EA7IA;IAAA,2DA6IA;IA7IA;MAAA,0EA6IA;IAAA;EAAA;EA7IA;IAAA,2DA6IA;IA7IA;MAAA,0EA6IA;IAAA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,6CA6IA;EAAA;EA7IA;IAAA,kCA6IA;IA7IA;MAAA,wEA6IA;IAAA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,oDA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA;MAAA,+EA6IA;IAAA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA;MAAA,+EA6IA;IAAA;EAAA;EA7IA;IAAA,mDA6IA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,iFA6IA;IA7IA;MAAA,6EA6IA;IAAA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,8CA6IA;EAAA;EA7IA;IAAA,8CA6IA;IA7IA;MAAA,oFA6IA;IAAA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,gDA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,gDA6IA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA;MAAA,0EA6IA;IAAA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA;MAAA,2EA6IA;IAAA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA;MAAA,2EA6IA;IAAA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA;MAAA,2EA6IA;IAAA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA;MAAA,2EA6IA;IAAA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,0CA6IA;EAAA;EA7IA;IAAA,kDA6IA;EAAA;EA7IA;IAAA,uCA6IA;IA7IA;MAAA,6EA6IA;IAAA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA;MAAA,2EA6IA;IAAA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;IA7IA;MAAA,6EA6IA;IAAA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,0DA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,2DA6IA;IA7IA;MAAA,0EA6IA;IAAA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,0CA6IA;IA7IA,2DA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA,2DA6IA;EAAA;EA7IA;IAAA,uCA6IA;IA7IA,2DA6IA;EAAA;EA7IA;IAAA,6DA6IA;IA7IA;MAAA,4EA6IA;IAAA;IA7IA,8LA6IA;EAAA;EA7IA;IAAA,uCA6IA;IA7IA,8LA6IA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA,8LA6IA;EAAA;EA7IA;IAAA,uCA6IA;IA7IA,8LA6IA;EAAA;EA7IA;IAAA,6BA6IA;IA7IA,8LA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,sBA6IA;OA7IA,mBA6IA;EAAA;EA7IA;IAAA,oBA6IA;OA7IA,iBA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,0CA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,0CA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,sCA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,gBA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,oCA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,oCA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,oCA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,oCA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,oCA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,0BA6IA;IA7IA,4DA6IA;EAAA;EA7IA;IAAA,0BA6IA;IA7IA,4DA6IA;EAAA;EA7IA;IAAA,0BA6IA;IA7IA,4DA6IA;EAAA;EA7IA;IAAA,0BA6IA;IA7IA,4DA6IA;EAAA;EA7IA;IAAA,2BA6IA;IA7IA,6DA6IA;EAAA;EA7IA;IAAA,yBA6IA;IA7IA,2DA6IA;EAAA;EA7IA;IAAA,yBA6IA;IA7IA,2DA6IA;EAAA;EA7IA;IAAA,yBA6IA;IA7IA,2DA6IA;EAAA;EA7IA;IAAA,yBA6IA;IA7IA,2DA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,uCA6IA;EAAA;EA7IA;IAAA,iBA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,eA6IA;EAAA;EA7IA;IAAA,kBA6IA;IA7IA,iBA6IA;EAAA;EA7IA;IAAA,eA6IA;IA7IA,cA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA,oCA6IA;EAAA;EA7IA;IAAA,2CA6IA;IA7IA,sCA6IA;EAAA;EA7IA;IAAA,2CA6IA;IA7IA,sCA6IA;EAAA;EA7IA;IAAA,6CA6IA;IA7IA,wCA6IA;EAAA;EA7IA;IAAA,oCA6IA;IA7IA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA,sCA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,cA6IA;EAAA;EA7IA;IAAA,kDA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,4CA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,6CA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,mBA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,yCA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,wCA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;IA7IA;MAAA,mEA6IA;IAAA;EAAA;EA7IA;IAAA,6BA6IA;IA7IA;MAAA,mEA6IA;IAAA;EAAA;EA7IA;IAAA,6BA6IA;IA7IA;MAAA,mEA6IA;IAAA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,wBA6IA;EAAA;EA7IA;IAAA,mCA6IA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA;MAAA,yEA6IA;IAAA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,0BA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,2BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,0BA6IA;EAAA;EA7IA;IAAA,qCA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,4BA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,gDA6IA;IA7IA;MAAA,+DA6IA;IAAA;EAAA;EA7IA;IAAA,gDA6IA;IA7IA;MAAA,+DA6IA;IAAA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,8BA6IA;EAAA;EA7IA;IAAA,0BA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,kBA6IA;EAAA;EA7IA;IAAA,kCA6IA;IA7IA,iJA6IA;EAAA;EA7IA;IAAA,kCA6IA;EAAA;EA7IA;IAAA,+BA6IA;EAAA;EA7IA;IAAA,0BA6IA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;MA7IA;QAAA,+DA6IA;MAAA;IAAA;IA7IA;MAAA,gDA6IA;MA7IA;QAAA,+DA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA,oCA6IA;EAAA;EA7IA;IAAA,WA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,YA6IA;EAAA;EA7IA;IAAA,aA6IA;EAAA;EA7IA;IAAA,0HA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,yEA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,+HA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,6HA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,sBA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,0HA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,gIA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,wHA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,wHA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,wHA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,wHA6IA;IA7IA,sIA6IA;EAAA;EA7IA;IAAA,uCA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,8CA6IA;EAAA;EA7IA;IAAA,sCA6IA;IA7IA,kBA6IA;EAAA;EA7IA;IAAA,oBA6IA;IA7IA,0LA6IA;EAAA;EA7IA;IAAA,0LA6IA;EAAA;EA7IA;IAAA,8BA6IA;IA7IA,wRA6IA;IA7IA,gRA6IA;EAAA;EA7IA;IAAA,wCA6IA;IA7IA,wRA6IA;IA7IA,gRA6IA;EAAA;EA7IA;IAAA,qVA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,qCA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,mCA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,yCA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,0BA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,wBA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,uKA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,4BA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,+BA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,wDA6IA;IA7IA,qFA6IA;IA7IA,2EA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,oBA6IA;IA7IA,0BA6IA;EAAA;EA7IA;IAAA,oBA6IA;IA7IA,0BA6IA;EAAA;EA7IA;IAAA,oBA6IA;IA7IA,0BA6IA;EAAA;EA7IA;IAAA,oBA6IA;IA7IA,0BA6IA;EAAA;EA7IA;IAAA,oBA6IA;IA7IA,0BA6IA;EAAA;EA7IA;IAAA,qBA6IA;IA7IA,2BA6IA;EAAA;EA7IA;IAAA,6BA6IA;IA7IA,8CA6IA;EAAA;EA7IA;IAAA,iBA6IA;IA7IA,kCA6IA;EAAA;EA7IA;IAAA,0BA6IA;IA7IA,2CA6IA;EAAA;EA7IA;IAAA,qBA6IA;IA7IA,yBA6IA;IA7IA,2BA6IA;IA7IA,yBA6IA;IA7IA,0BA6IA;IA7IA,+BA6IA;IA7IA,+BA6IA;EAAA;EA7IA;IAAA,wBA6IA;IA7IA,mBA6IA;EAAA;EA7IA;IAAA,yBA6IA;IA7IA,sBA6IA;SA7IA,iBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,yBA6IA;EAAA;EA7IA;IAAA,0BA6IA;EAAA;EA7IA;IAAA,uDA6IA;EAAA;EA7IA;IAAA,iCA6IA;EAAA;EA7IA;IAAA,qDA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA,6BA6IA;EAAA;EA7IA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,qBA6IA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,aA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0CA6IA;MA7IA,2CA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6CA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4CA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4CA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,WA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,sCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0CA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0CA6IA;MA7IA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,cA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yCA6IA;MA7IA,sBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,cA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;MA7IA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0HA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+HA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;MA7IA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;MA7IA,2DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2CA6IA;MA7IA,sCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;IA7IA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,kBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,gBA6IA;MA7IA,gBA6IA;MA7IA,gBA6IA;MA7IA,0CA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,wDA6IA;MA7IA,qFA6IA;MA7IA,2EA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,kBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,sBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,UA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,6CA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,iBA6IA;MA7IA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0BA6IA;QA7IA,UA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,iDA6IA;MA7IA,oDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yCA6IA;MA7IA,sBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kDA6IA;MA7IA,qDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0CA6IA;MA7IA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2CA6IA;MA7IA,wBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,WA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wHA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;MA7IA,4GA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,kBA6IA;QA7IA,kBA6IA;QA7IA,kBA6IA;QA7IA,0CA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,mCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yBA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yBA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yBA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yBA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,4CA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,oCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,oCA6IA;QA7IA;UAAA,0EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,oCA6IA;QA7IA;UAAA,0EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,wCA6IA;QA7IA;UAAA,8EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yCA6IA;QA7IA;UAAA,+EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yCA6IA;QA7IA;UAAA,+EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,wCA6IA;QA7IA;UAAA,8EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,sCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,mCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,mCA6IA;QA7IA;UAAA,yEA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,mCA6IA;QA7IA;UAAA,yEA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,qCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,qCA6IA;QA7IA;UAAA,2EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,qCA6IA;QA7IA;UAAA,2EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,qCA6IA;QA7IA;UAAA,2EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,qCA6IA;QA7IA;UAAA,2EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,sCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uCA6IA;QA7IA;UAAA,6EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uCA6IA;QA7IA;UAAA,6EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,wCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,2DA6IA;QA7IA;UAAA,0EA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,4CA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,oCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,4BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,4BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,4BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,6BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,4BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,mCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,qCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,+BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,YA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,aA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,yEA6IA;QA7IA,sIA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,6HA6IA;QA7IA,sIA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,4FA6IA;QA7IA,oDA6IA;QA7IA,0LA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wHA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wHA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;MA7IA,4GA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;MA7IA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wHA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wHA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wHA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,sCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;MA7IA,4GA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;MA7IA,4GA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;MA7IA,4GA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8CA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;MA7IA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;MA7IA;QAAA,0EA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2CA6IA;MA7IA,sCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qBA6IA;MA7IA,yBA6IA;MA7IA,2BA6IA;MA7IA,yBA6IA;MA7IA,0BA6IA;MA7IA,+BA6IA;MA7IA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;MA7IA,yBA6IA;MA7IA,0BA6IA;MA7IA,wBA6IA;MA7IA,yBA6IA;MA7IA,8BA6IA;MA7IA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,WA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,WA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,sBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0BA6IA;QA7IA,8BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0BA6IA;QA7IA,gCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0BA6IA;QA7IA,WA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0BA6IA;QA7IA,0CA6IA;QA7IA,sDA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0BA6IA;QA7IA,6CA6IA;QA7IA,sDA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0CA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2CA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0CA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2CA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0HA6IA;MA7IA,sIA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0CA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0BA6IA;QA7IA,kBA6IA;QA7IA,kBA6IA;QA7IA,kBA6IA;QA7IA,0CA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;MA7IA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;MA7IA,yBA6IA;MA7IA,0BA6IA;MA7IA,wBA6IA;MA7IA,yBA6IA;MA7IA,8BA6IA;MA7IA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,sBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;MA7IA,yBA6IA;MA7IA,0BA6IA;MA7IA,wBA6IA;MA7IA,yBA6IA;MA7IA,8BA6IA;MA7IA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;MA7IA;QAAA,0EA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;MA7IA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qBA6IA;MA7IA,yBA6IA;MA7IA,2BA6IA;MA7IA,yBA6IA;MA7IA,0BA6IA;MA7IA,+BA6IA;MA7IA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA;UAAA,oCA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,oCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0CA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qBA6IA;MA7IA,yBA6IA;MA7IA,2BA6IA;MA7IA,yBA6IA;MA7IA,0BA6IA;MA7IA,+BA6IA;MA7IA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,cA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uBA6IA;QA7IA,8EA6IA;QA7IA,sFA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uBA6IA;QA7IA,+EA6IA;QA7IA,uFA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uBA6IA;QA7IA,+EA6IA;QA7IA,uFA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,sCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;MA7IA,6DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;MA7IA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,QA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,QA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,cA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,WA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,UA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uBA6IA;QA7IA,8EA6IA;QA7IA,sFA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uBA6IA;QA7IA,+EA6IA;QA7IA,uFA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,uBA6IA;QA7IA,+EA6IA;QA7IA,uFA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;MA7IA,uBA6IA;MA7IA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;MA7IA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;MA7IA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;MA7IA,qBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2CA6IA;MA7IA,wBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,sCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,4DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,4DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,4DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA,4DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;MA7IA,6DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;MA7IA,2DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;MA7IA,2DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;MA7IA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;MA7IA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;MA7IA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,eA6IA;MA7IA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kBA6IA;MA7IA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,WA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,gCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,qCA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,+BA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,0HA6IA;QA7IA,sIA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA;UAAA,qCA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA;QAAA,aA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,cA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;MA7IA,gBA6IA;MA7IA,gBA6IA;MA7IA,0CA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kBA6IA;MA7IA,kBA6IA;MA7IA,kBA6IA;MA7IA,0CA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,YA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2DA6IA;MA7IA;QAAA,0EA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,kCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kCA6IA;MA7IA;QAAA,wEA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gFA6IA;MA7IA;QAAA,4EA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,kCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;MA7IA;QAAA,mEA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;MA7IA;QAAA,mEA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;MA7IA;QAAA,mEA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,4BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA;UAAA,uCA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA;UAAA,6BA6IA;QAAA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,kCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;MA7IA;QAAA,gEA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;MA7IA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,0BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;MA7IA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;MA7IA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;MA7IA,2DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2CA6IA;MA7IA,sCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;MA7IA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;MA7IA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,cA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,2CA6IA;MA7IA,wBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;MA7IA,iBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;cA7IA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wBA6IA;cA7IA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kDA6IA;MA7IA,qDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iDA6IA;MA7IA,oDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,wCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,iDA6IA;QA7IA,oDA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,kDA6IA;QA7IA,qDA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,oCA6IA;MA7IA;QAAA,0EA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,kDA6IA;MA7IA,qDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,uCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,qBA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,yBA6IA;MA7IA,2DA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;MA7IA,oBA6IA;MA7IA,4BA6IA;MA7IA,qBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;MA7IA,uBA6IA;MA7IA,mBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;MA7IA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,kCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,iCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,cA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,8BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,6BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,mCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,sBA6IA;MA7IA,sDA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,sCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA;QAAA,2CA6IA;QA7IA,wBA6IA;MAAA;IAAA;EAAA;EA7IA;IAAA;MAAA,aA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,cA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gCA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,+BA6IA;IAAA;EAAA;EA7IA;IAAA;MAAA,gBA6IA;IAAA;EAAA;AAAA;AA7IA;EAAA;IAAA,uBA6IA;IA7IA,sBA6IA;IA7IA,mCA6IA;IA7IA,2CA6IA;IA7IA,mBA6IA;IA7IA,2BA6IA;IA7IA,oBA6IA;IA7IA,8BA6IA;IA7IA,sBA6IA;IA7IA,+BA6IA;IA7IA,wBA6IA;IA7IA,gCA6IA;IA7IA,oBA6IA;IA7IA,+BA6IA;IA7IA,uBA6IA;IA7IA,gCA6IA;IA7IA,6BA6IA;IA7IA,wCA6IA;IA7IA,qBA6IA;IA7IA,iBA6IA;IA7IA,mBA6IA;IA7IA,gBA6IA;IA7IA,+BA6IA;IA7IA,8BA6IA;IA7IA,8BA6IA;IA7IA,uCA6IA;IA7IA,+BA6IA;IA7IA,wCA6IA;IA7IA,0BA6IA;IA7IA,wBA6IA;IA7IA,qBA6IA;IA7IA,sBA6IA;IA7IA,sBA6IA;IA7IA,qBA6IA;IA7IA,qBA6IA;IA7IA,uBA6IA;IA7IA,iCA6IA;IA7IA,uBA6IA;IA7IA,8BA6IA;IA7IA,4BA6IA;IA7IA,wBA6IA;IA7IA,0BA6IA;IA7IA,sBA6IA;IA7IA,kBA6IA;IA7IA,0BA6IA;IA7IA,oCA6IA;IA7IA,yBA6IA;IA7IA,mBA6IA;IA7IA,mBA6IA;IA7IA,8BA6IA;IA7IA,2BA6IA;EAAA;EA7IA;IAAA,yBA6IA;IA7IA,sBA6IA;IA7IA,mCA6IA;IA7IA,2CA6IA;IA7IA,mBA6IA;IA7IA,2BA6IA;IA7IA,sBA6IA;IA7IA,8BA6IA;IA7IA,sBA6IA;IA7IA,+BA6IA;IA7IA,uBA6IA;IA7IA,gCA6IA;IA7IA,oBA6IA;IA7IA,+BA6IA;IA7IA,qBA6IA;IA7IA,gCA6IA;IA7IA,0BA6IA;IA7IA,sCA6IA;IA7IA,qBA6IA;IA7IA,oBA6IA;IA7IA,mBA6IA;IA7IA,iCA6IA;IA7IA,8BA6IA;IA7IA,8BA6IA;IA7IA,uCA6IA;IA7IA,6BA6IA;IA7IA,wCA6IA;IA7IA,6BA6IA;IA7IA,2BA6IA;IA7IA,qBA6IA;IA7IA,sBA6IA;IA7IA,sBA6IA;IA7IA,qBA6IA;IA7IA,qBA6IA;IA7IA,sBA6IA;IA7IA,iCA6IA;IA7IA,oBA6IA;IA7IA,6BA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,wCA6IA;IA7IA,6BA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,gCA6IA;EAAA;EA7IA;IAAA,wCA6IA;IA7IA,6BA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,uBA6IA;EAAA;EA7IA;IAAA,sBA6IA;EAAA;AAAA;AA7IA;EAAA,gCA6IA;EA7IA,+BA6IA;EA7IA,mCA6IA;EA7IA,uCA6IA;EA7IA,aA6IA;EA7IA,wBA6IA;EA7IA,qFA6IA;EA7IA,2EA6IA;EA7IA,oBA6IA;EA7IA,0BA6IA;EA7IA,yBA6IA;AAAA;AA7IA;EAAA;IAAA,mCA6IA;IA7IA,iNA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,kCA6IA;IA7IA,2MA6IA;EAAA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,oBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,gBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,iBA6IA;EA7IA,eA6IA;EA7IA,oBA6IA;AAAA;AA7IA;EAAA,iBA6IA;EA7IA,eA6IA;EA7IA,oBA6IA;AAAA;AA7IA;EAAA,iBA6IA;EA7IA,eA6IA;EA7IA,oBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,6BA6IA;EA7IA,eA6IA;EA7IA,iBA6IA;AAAA;AA7IA;EAAA,6BA6IA;EA7IA,eA6IA;EA7IA,kBA6IA;AAAA;AA7IA;EAAA,6BA6IA;EA7IA,eA6IA;EA7IA,mBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,wBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,sBA6IA;EA7IA,eA6IA;EA7IA,mBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,wBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,sBA6IA;EA7IA,eA6IA;EA7IA,mBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,wBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,wBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,kBA6IA;EA7IA,eA6IA;EA7IA,kBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,mBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,wBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;EA7IA,oBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,sBA6IA;EA7IA,eA6IA;EA7IA,mBA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA,WA6IA;EA7IA,iBA6IA;EA7IA,eA6IA;AAAA;AA7IA;EAAA;IAAA,yBA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,YA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,SA6IA;EAAA;EA7IA;IAAA,6CA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA,6CA6IA;EAAA;EA7IA;IAAA,SA6IA;EAAA;AAAA;AA7IA;EAAA;IAAA;MAAA,mBA6IA;MA7IA,mBA6IA;MA7IA,mBA6IA;MA7IA,eA6IA;MA7IA,eA6IA;MA7IA,eA6IA;MA7IA,sBA6IA;MA7IA,sBA6IA;MA7IA,sBA6IA;MA7IA,oBA6IA;MA7IA,oBA6IA;MA7IA,uBA6IA;MA7IA,uBA6IA;MA7IA,wBA6IA;MA7IA,wBA6IA;MA7IA,wBA6IA;MA7IA,+BA6IA;MA7IA,yBA6IA;MA7IA,wBA6IA;MA7IA,uBA6IA;MA7IA,4BA6IA;MA7IA,gCA6IA;MA7IA,+BA6IA;MA7IA,+BA6IA;MA7IA,+BA6IA;MA7IA,qBA6IA;MA7IA,yBA6IA;MA7IA,sBA6IA;MA7IA,qBA6IA;MA7IA,0BA6IA;MA7IA,4BA6IA;MA7IA,6BA6IA;MA7IA,8BA6IA;MA7IA,sBA6IA;MA7IA,0BA6IA;MA7IA,uBA6IA;MA7IA,4BA6IA;MA7IA,gCA6IA;MA7IA,6BA6IA;MA7IA,wBA6IA;MA7IA,2BA6IA;MA7IA,8BA6IA;MA7IA,iCA6IA;MA7IA,wBA6IA;MA7IA,2BA6IA;MA7IA,4BA6IA;MA7IA,kCA6IA;MA7IA,yBA6IA;MA7IA,kBA6IA;MA7IA,wBA6IA;MA7IA,sBA6IA;MA7IA,uBA6IA;MA7IA,wBA6IA;MA7IA,oBA6IA;MA7IA,qBA6IA;MA7IA,sBA6IA;MA7IA,mBA6IA;MA7IA,yBA6IA;MA7IA,+BA6IA;MA7IA,4BA6IA;MA7IA,8BA6IA;MA7IA,2BA6IA;MA7IA,iCA6IA;MA7IA,+BA6IA;MA7IA,gCA6IA;MA7IA,iCA6IA;MA7IA,6BA6IA;MA7IA,8BA6IA;MA7IA,+BA6IA;MA7IA,4BA6IA;MA7IA,sBA6IA;MA7IA,kBA6IA;MA7IA,gBA6IA;IAAA;EAAA;AAAA","sourcesContent":["@import 'tailwindcss';\r\n@config \"../../tailwind.config.ts\";\r\n@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\n@layer base {\r\n  :root {\r\n    --background: 0 0% 100%;\r\n    --foreground: 0 0% 12%;\r\n    --background-secondary: 220 20% 97%;\r\n    --background-secondary-foreground: 0 0% 12%;\r\n    --card: 220 20% 97%;\r\n    --card-foreground: 0 0% 12%;\r\n    --popover: 0 0% 100%;\r\n    --popover-foreground: 0 0% 12%;\r\n    --primary: 233 73% 26%;\r\n    --primary-foreground: 0 0% 100%;\r\n    --secondary: 40 100% 50%;\r\n    --secondary-foreground: 0 0% 12%;\r\n    --muted: 220 20% 97%;\r\n    --muted-foreground: 225 12% 60%;\r\n    --accent: 205, 42%, 91%;\r\n    --accent-foreground: 233 73% 26%;\r\n    --destructive: 359, 100%, 96%;\r\n    --destructive-foreground: 359, 100%, 62%;\r\n    --border: 225 12% 60%;\r\n    --input: 0 0% 94%;\r\n    --ring: 233 80% 35%;\r\n    --radius: 0.5rem;\r\n    --sidebar-background: 0 0% 100%;\r\n    --sidebar-foreground: 0 0% 12%;\r\n    --sidebar-primary: 233 73% 26%;\r\n    --sidebar-primary-foreground: 0 0% 100%;\r\n    --sidebar-accent: 205, 42%, 91%;\r\n    --sidebar-accent-foreground: 233 73% 26%;\r\n    --sidebar-border: 0 0% 94%;\r\n    --sidebar-ring: 0 0% 94%;\r\n    --chart-1: 12 76% 61%;\r\n    --chart-2: 173 58% 39%;\r\n    --chart-3: 197 37% 24%;\r\n    --chart-4: 43 74% 66%;\r\n    --chart-5: 27 87% 67%;\r\n    --success: 159 100% 37%;\r\n    --success-foreground: 160 56% 94%;\r\n    --error: 359, 100%, 62%;\r\n    --error-foreground: 0 100% 96%;\r\n\r\n    --background-BG: 220 20% 97%;\r\n    --color-stroke: 0 0% 94%;\r\n    --placeholder: 225 12% 60%;\r\n    --title-main: 0 0% 12%;\r\n    --White: 0 0% 100%;\r\n    --discription: 221 18% 44%;\r\n    --discription-secondary: 235 19% 35%;\r\n    --Highlight: 220 100% 98%;\r\n    --test: 235 19% 35%;\r\n    --Menu: 205 42% 91%;\r\n    --title-secondary: 225 10% 52%;\r\n    --secondary-bg: 39 100% 95%;\r\n  }\r\n\r\n  .dark {\r\n    --background: 232 23% 10%;\r\n    --foreground: 0 0% 98%;\r\n    --background-secondary: 232 23% 15%;\r\n    --background-secondary-foreground: 0 0% 98%;\r\n    --card: 232 23% 15%;\r\n    --card-foreground: 0 0% 98%;\r\n    --popover: 232 23% 13%;\r\n    --popover-foreground: 0 0% 98%;\r\n    --primary: 233 65% 55%;\r\n    --primary-foreground: 0 0% 100%;\r\n    --secondary: 40 90% 45%;\r\n    --secondary-foreground: 0 0% 12%;\r\n    --muted: 232 23% 20%;\r\n    --muted-foreground: 220 20% 70%;\r\n    --accent: 205 42% 40%;\r\n    --accent-foreground: 205 85% 90%;\r\n    --destructive: 359 85% 30%;\r\n    --destructive-foreground: 359 100% 94%;\r\n    --border: 232 23% 20%;\r\n    --input: 232 23% 20%;\r\n    --ring: 233 65% 60%;\r\n    --sidebar-background: 232 23% 13%;\r\n    --sidebar-foreground: 0 0% 98%;\r\n    --sidebar-primary: 233 65% 55%;\r\n    --sidebar-primary-foreground: 0 0% 100%;\r\n    --sidebar-accent: 205 42% 40%;\r\n    --sidebar-accent-foreground: 205 85% 90%;\r\n    --sidebar-border: 232 23% 20%;\r\n    --sidebar-ring: 233 65% 60%;\r\n    --chart-1: 12 76% 61%;\r\n    --chart-2: 173 70% 45%;\r\n    --chart-3: 197 60% 40%;\r\n    --chart-4: 43 74% 60%;\r\n    --chart-5: 27 87% 67%;\r\n    --success: 159 80% 30%;\r\n    --success-foreground: 160 85% 94%;\r\n    --error: 359 85% 45%;\r\n    --error-foreground: 0 0% 100%;\r\n  }\r\n}\r\n\r\n@layer base {\r\n  * {\r\n    @apply border-[hsl(var(--border))];\r\n  }\r\n\r\n  body {\r\n    @apply bg-background text-foreground;\r\n  }\r\n}\r\n\r\n@layer base {\r\n  * {\r\n    @apply border-border;\r\n  }\r\n\r\n  body {\r\n    @apply bg-background text-foreground;\r\n  }\r\n}\r\n\r\n/* Custom z-index hierarchy for proper tooltip layering */\r\n@layer components {\r\n  /* TimeConversionTooltip hover card should have higher z-index */\r\n  [data-radix-hover-card-content] {\r\n    z-index: 100 !important;\r\n  }\r\n\r\n  /* Regular tooltips should have lower z-index than hover cards */\r\n  [data-radix-tooltip-content] {\r\n    z-index: 60 !important;\r\n  }\r\n}\r\n\r\n/* bullet ปกติ */\r\n.custom-bullets {\r\n  @apply h-2 w-2 rounded-full bg-gray-300 opacity-100 transition-all duration-300;\r\n}\r\n"],"sourceRoot":""}]);
// Exports
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);


/***/ })

});
//# sourceMappingURL=main.2bc46ec9fa14f969f4ef.hot-update.js.map