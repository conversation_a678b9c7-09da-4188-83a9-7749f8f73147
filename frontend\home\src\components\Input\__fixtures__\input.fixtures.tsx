// _fixtures_/input-items.tsx
import { Input } from '../input';
import { Search } from 'lucide-react';

// Basic input (default)
export const DefaultInput = (
  <Input 
    id="default-input"
    placeholder="Enter text here"
  />
);

// Input with label
export const InputWithLabel = (
  <Input 
    id="labeled-input"
    label="Your Name"
    placeholder="Enter your name"
  />
);

// Input with helper text
export const InputWithHelper = (
  <Input 
    id="helper-input"
    label="Email Address"
    placeholder="<EMAIL>"
    helperText="We'll never share your email with anyone else"
  />
);

// Input with error
export const InputWithError = (
  <Input 
    id="error-input"
    label="Username"
    placeholder="Enter username"
    error={true}
    errorMessage="This username is already taken"
  />
);

// Disabled input
export const DisabledInput = (
  <Input 
    id="disabled-input"
    label="Username"
    placeholder="Enter username"
    disabled
    defaultValue="Unavailable"
  />
);

// Required input
export const RequiredInput = (
  <Input 
    id="required-input"
    label="Phone Number"
    placeholder="Enter phone number"
    required
  />
);

// Input with default value
export const InputWithDefaultValue = (
  <Input 
    id="default-value-input"
    label="Company"
    defaultValue="Acme Corporation"
  />
);

// Read-only input
export const ReadOnlyInput = (
  <Input 
    id="readonly-input"
    label="User ID"
    defaultValue="USR-12345"
    readOnly
    helperText="This value cannot be changed"
  />
);

// ----------- Custom Variant Examples -----------

// Search input with search icon
export const SearchInput = (
  <Input 
    id="search-input"
    variant="search"
    placeholder="ค้นหาสินค้าที่ต้องการ"
    onButtonClick={() => console.log('Search clicked')}
    searchButtonClassName="bg-[#1E3A8A]"
  />
);

// Custom colored search input
export const BlueSearchInput = (
  <Input 
    id="blue-search-input"
    variant="search"
    placeholder="ค้นหาสินค้าที่ต้องการ"
    onButtonClick={() => console.log('Search clicked')}
    searchButtonClassName="bg-blue-700"
    searchVariantClassName="border-blue-700"
  />
);

// Input with button (discount code)
export const InputWithButton = (
  <Input 
    id="discount-input"
    variant="withButton"
    placeholder="กรอกรหัสส่วนลด"
    buttonText="ใช้"
    onButtonClick={() => console.log('Apply discount clicked')}
  />
);

// Tax ID input (simple input with label)
export const TaxIdInput = (
  <Input 
    id="tax-id-input"
    label="หมายเลขประจำตัวผู้เสียภาษี"
    placeholder="กรอกหมายเลขประจำตัวผู้เสียภาษี"
  />
);

// Different sized inputs
export const SmallInput = (
  <Input 
    id="small-input"
    size="sm"
    placeholder="Small input field"
  />
);

export const LargeInput = (
  <Input 
    id="large-input"
    size="lg"
    placeholder="Large input field"
  />
);