import{r as l}from"./index-Bwql5Dzz.js";var c=Object.defineProperty,n=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,o=(r,t,e)=>t in r?c(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,s=(r,t)=>{for(var e in t)i.call(t,e)&&o(r,e,t[e]);if(n)for(var e of n(t))f.call(t,e)&&o(r,e,t[e]);return r},m=(r,t)=>{var e={};for(var a in r)i.call(r,a)&&t.indexOf(a)<0&&(e[a]=r[a]);if(r!=null&&n)for(var a of n(r))t.indexOf(a)<0&&f.call(r,a)&&(e[a]=r[a]);return e};const d=l.forwardRef((r,t)=>{var e=r,{color:a="currentColor"}=e,p=m(e,["color"]);return l.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),l.createElement("g",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},l.createElement("path",{d:"M7.5 13.945a6.444 6.444 0 1 0 0-12.89 6.444 6.444 0 0 0 0 12.89M5.056 5.056l4.888 4.889M9.944 5.056 5.056 9.945"})),l.createElement("defs",null,l.createElement("clipPath",{id:"a"},l.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});d.displayName="XCircle";export{d as X};
