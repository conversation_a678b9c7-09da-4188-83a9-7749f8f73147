import{C as oe}from"./chunk-X5VECN6S-B3ePbuRl.js";import{H as Re}from"./chunk-7OYLCEKK-CAi0vGGH.js";import{u as Be,a as He,b as Le,c as qe}from"./chunk-UVGNHHSZ-DRARdeex.js";import{C as Ke}from"./chunk-3DY3IL33-BeSvuzro.js";import{u as G}from"./chunk-6CNRNROJ-vBT9ZY0G.js";import{C as U}from"./chunk-3LLQ6F7F-DvOxFHHw.js";import{P as Ge,a as Ue,n as We,U as Xe,d as ce}from"./chunk-XLQKUCBS-COzr_Kst.js";import{u as xe,a as J,D as fe,S as ge,b as ve,c as je,C as be,s as ye,K as Ce,P as Ne,d as we,e as _e}from"./sortable.esm-Cw5vqf5Y.js";import"./chunk-ZQRKUG6J-DsylX8wf.js";import{D as A,c as Ze,a as Je}from"./chunk-GE4APTT2-vaCdy0-f.js";import{C as Qe}from"./chunk-XDJ7OMBR-BPMYBhTp.js";import{r as _,b as I,g as Ye,h as et,i as tt,l as st,j as e,m as Se,I as H,a as ie,n as at,o as rt,v as B,s as R,t as se,B as F,F as Pe,D as nt,H as ee,w as l,x as Q,y as q,T as K,A as Ve,X as ke,z as X,C as it,E as lt,G as ot,J as ct,k as dt}from"./index-Bwql5Dzz.js";import{D as ut,c as mt}from"./chunk-MGPZHEOT-CqdSNFtj.js";import{S as De}from"./chunk-CBJWO6K6-CH1Togga.js";import{K as pt}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as O,u as Ie,S as $,a as ht}from"./chunk-JGQGO74V-DtHO1ucg.js";import{D as le}from"./dots-six-CeeG2_9G.js";import{P as E}from"./progress-tabs-CKpxSjXZ.js";import{T as xt}from"./textarea-CcKuCLDy.js";import{T as Te}from"./thumbnail-badge-i0OhbLw4.js";import{T as ft}from"./trash-BBylvTAG.js";import{A as ae}from"./alert-VXKM2dfu.js";import{X as ze}from"./x-mark-mini-DvSTI7zK.js";import{T as de}from"./Trans-VWqfqpAH.js";import{C as ue}from"./checkbox-B4pL6X49.js";import"./chunk-FFVOUYTF-DR1d4TPs.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./triangles-mini-DPBC_td5.js";import"./index-DP5bcQyU.js";import"./plus-mini-C5sDHkH8.js";import"./chunk-TYTNUPXB-BgxqaOnb.js";import"./chunk-6GU6IDUA-CDc7wW5L.js";import"./chunk-MWVM4TYO-bKUcYSnf.js";import"./_isIndex-bB1kTSVv.js";import"./index.esm-3G2Z4eQ8.js";import"./index-BxZ1678G.js";import"./command-bar-Cyd2ymXA.js";import"./table-BDZtqXjX.js";import"./arrow-up-mini-D5bOKjDW.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./prompt-BsR9zKsn.js";var gt=Object.defineProperty,Y=Object.getOwnPropertySymbols,Ee=Object.prototype.hasOwnProperty,Fe=Object.prototype.propertyIsEnumerable,me=(t,s,a)=>s in t?gt(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a,vt=(t,s)=>{for(var a in s)Ee.call(s,a)&&me(t,a,s[a]);if(Y)for(var a of Y(s))Fe.call(s,a)&&me(t,a,s[a]);return t},jt=(t,s)=>{var a={};for(var n in t)Ee.call(t,n)&&s.indexOf(n)<0&&(a[n]=t[n]);if(t!=null&&Y)for(var n of Y(t))s.indexOf(n)<0&&Fe.call(t,n)&&(a[n]=t[n]);return a};const Me=_.forwardRef((t,s)=>{var a=t,{color:n="currentColor"}=a,i=jt(a,["color"]);return _.createElement("svg",vt({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:s},i),_.createElement("path",{stroke:n,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"m2.389 10.389-.748.25a.444.444 0 0 1-.585-.422V4.783c0-.303.297-.517.585-.421l.748.25M5.944 12.055l-.718.3a.445.445 0 0 1-.615-.41v-8.89c0-.317.323-.532.615-.41l.718.3M8.798 1.124l4.631 2.137c.315.145.516.46.516.807v6.862a.89.89 0 0 1-.516.807l-4.631 2.137a.445.445 0 0 1-.631-.403V1.528c0-.325.336-.54.63-.404"}))});Me.displayName="StackPerspective";var bt=({form:t})=>{const{t:s}=I();return e.jsxs("div",{id:"general",className:"flex flex-col gap-y-6",children:[e.jsx("div",{className:"flex flex-col gap-y-2",children:e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(l.Field,{control:t.control,name:"title",render:({field:a})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{children:s("products.fields.title.label")}),e.jsx(l.Control,{children:e.jsx(Q,{...a,placeholder:"Winter jacket"})})]})}),e.jsx(l.Field,{control:t.control,name:"subtitle",render:({field:a})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{optional:!0,children:s("products.fields.subtitle.label")}),e.jsx(l.Control,{children:e.jsx(Q,{...a,placeholder:"Warm and cosy"})})]})}),e.jsx(l.Field,{control:t.control,name:"handle",render:({field:a})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{tooltip:s("products.fields.handle.tooltip"),optional:!0,children:s("fields.handle")}),e.jsx(l.Control,{children:e.jsx(Re,{...a,placeholder:"winter-jacket"})})]})})]})}),e.jsx(l.Field,{control:t.control,name:"description",render:({field:a})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{optional:!0,children:s("products.fields.description.label")}),e.jsx(l.Control,{children:e.jsx(xt,{...a,placeholder:"A warm and cozy jacket"})})]})})]})},yt={sideEffects:_e({styles:{active:{opacity:"0.4"}}})},Ct=({form:t})=>{const{fields:s,append:a,remove:n}=q({name:"media",control:t.control,keyName:"field_id"}),[i,r]=_.useState(null),u=xe(J(Ne),J(Ce,{coordinateGetter:ye})),v=x=>{r(x.active.id)},m=x=>{r(null);const{active:S,over:N}=x;if(S.id!==(N==null?void 0:N.id)){const V=s.findIndex(f=>f.field_id===S.id),D=s.findIndex(f=>f.field_id===(N==null?void 0:N.id));t.setValue("media",we(s,V,D),{shouldDirty:!0,shouldTouch:!0})}},p=()=>{r(null)},o=x=>()=>{n(x)},d=x=>()=>{const S=s.map((N,V)=>({...N,isThumbnail:V===x}));t.setValue("media",S,{shouldDirty:!0,shouldTouch:!0})},g=x=>({onDelete:o(x),onMakeThumbnail:d(x)});return e.jsxs("div",{id:"media",className:"flex flex-col gap-y-2",children:[e.jsx(Xe,{form:t,append:a,showHint:!1}),e.jsxs(fe,{sensors:u,onDragEnd:m,onDragStart:v,onDragCancel:p,children:[e.jsx(ve,{dropAnimation:yt,children:i?e.jsx(wt,{field:s.find(x=>x.field_id===i)}):null}),e.jsx("ul",{className:"flex flex-col gap-y-2",children:e.jsx(ge,{items:s.map(x=>x.field_id),children:s.map((x,S)=>{const{onDelete:N,onMakeThumbnail:V}=g(S);return e.jsx(Nt,{field:x,onDelete:N,onMakeThumbnail:V},x.field_id)})})})]})]})},Nt=({field:t,onDelete:s,onMakeThumbnail:a})=>{const{t:n}=I(),{attributes:i,listeners:r,setNodeRef:u,setActivatorNodeRef:v,transform:m,transition:p,isDragging:o}=je({id:t.field_id}),d={opacity:o?.4:void 0,transform:be.Translate.toString(m),transition:p};return t.file?e.jsxs("li",{className:"bg-ui-bg-component shadow-elevation-card-rest flex items-center justify-between rounded-lg px-3 py-2",ref:u,style:d,children:[e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx(H,{variant:"transparent",type:"button",size:"small",...i,...r,ref:v,className:"cursor-grab touch-none active:cursor-grabbing",children:e.jsx(le,{className:"text-ui-fg-muted"})}),e.jsxs("div",{className:"flex items-center gap-x-3",children:[e.jsx("div",{className:"bg-ui-bg-base h-10 w-[30px] overflow-hidden rounded-md",children:e.jsx(Oe,{url:t.url})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx(K,{size:"small",leading:"compact",children:t.file.name}),e.jsxs("div",{className:"flex items-center gap-x-1",children:[t.isThumbnail&&e.jsx(Te,{}),e.jsx(K,{size:"xsmall",leading:"compact",className:"text-ui-fg-subtle",children:Ae(t.file.size)})]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-x-1",children:[e.jsx(Ve,{groups:[{actions:[{label:n("products.media.makeThumbnail"),icon:e.jsx(Me,{}),onClick:a}]},{actions:[{icon:e.jsx(ft,{}),label:n("actions.delete"),onClick:s}]}]}),e.jsx(H,{type:"button",size:"small",variant:"transparent",onClick:s,children:e.jsx(ke,{})})]})]}):null},wt=({field:t})=>{var s,a;return e.jsxs("li",{className:"bg-ui-bg-component shadow-elevation-card-rest flex items-center justify-between rounded-lg px-3 py-2",children:[e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx(H,{variant:"transparent",size:"small",className:"cursor-grab touch-none active:cursor-grabbing",children:e.jsx(le,{className:"text-ui-fg-muted"})}),e.jsxs("div",{className:"flex items-center gap-x-3",children:[e.jsx("div",{className:"bg-ui-bg-base h-10 w-[30px] overflow-hidden rounded-md",children:e.jsx(Oe,{url:t.url})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx(K,{size:"small",leading:"compact",children:(s=t.file)==null?void 0:s.name}),e.jsxs("div",{className:"flex items-center gap-x-1",children:[t.isThumbnail&&e.jsx(Te,{}),e.jsx(K,{size:"xsmall",leading:"compact",className:"text-ui-fg-subtle",children:Ae(((a=t.file)==null?void 0:a.size)??0)})]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-x-1",children:[e.jsx(Ve,{groups:[]}),e.jsx(H,{type:"button",size:"small",variant:"transparent",onClick:()=>{},children:e.jsx(ke,{})})]})]})},Oe=({url:t})=>t?e.jsx("img",{src:t,alt:"",className:"size-full object-cover object-center"}):null;function Ae(t,s=2){if(t===0)return"0 Bytes";const a=1024,n=["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"],i=Math.floor(Math.log(t)/Math.log(a));return parseFloat((t/Math.pow(a,i)).toFixed(s))+" "+n[i]}var _t=({items:t,onChange:s,renderItem:a})=>{const[n,i]=_.useState(null),[r,u]=_.useMemo(()=>{if(n===null)return[null,null];const d=t.findIndex(({id:g})=>g===n.id);return[t[d],d]},[n,t]),v=xe(J(Ne),J(Ce,{coordinateGetter:ye})),m=({active:d})=>{i(d)},p=({active:d,over:g})=>{if(g&&d.id!==g.id){const x=t.findIndex(({id:N})=>N===d.id),S=t.findIndex(({id:N})=>N===g.id);s(we(t,x,S))}i(null)},o=()=>{i(null)};return e.jsxs(fe,{sensors:v,onDragStart:m,onDragEnd:p,onDragCancel:o,children:[e.jsx(Pt,{children:r&&u!==null?a(r,u):null}),e.jsx(ge,{items:t,children:e.jsx("ul",{role:"application",className:"flex list-inside list-none list-image-none flex-col p-0",children:t.map((d,g)=>e.jsx(_.Fragment,{children:a(d,g)},d.id))})})]})},St={sideEffects:_e({styles:{active:{opacity:"0.4"}}})},Pt=({children:t})=>e.jsx(ve,{className:"shadow-elevation-card-hover overflow-hidden rounded-md [&>li]:border-b-0",dropAnimation:St,children:t}),$e=_.createContext(null),Vt=()=>{const t=_.useContext($e);if(!t)throw new Error("useSortableItemContext must be used within a SortableItemContext");return t},kt=({id:t,className:s,children:a})=>{const{attributes:n,isDragging:i,listeners:r,setNodeRef:u,setActivatorNodeRef:v,transform:m,transition:p}=je({id:t}),o=_.useMemo(()=>({attributes:n,listeners:r,ref:v,isDragging:i}),[n,r,v,i]),d={opacity:i?.4:void 0,transform:be.Translate.toString(m),transition:p};return e.jsx($e.Provider,{value:o,children:e.jsx("li",{className:Se("transition-fg flex flex-1 list-none",s),ref:u,style:d,children:a})})},Dt=()=>{const{attributes:t,listeners:s,ref:a}=Vt();return e.jsx(H,{variant:"transparent",size:"small",...t,...s,ref:a,className:"cursor-grab touch-none active:cursor-grabbing",children:e.jsx(le,{className:"text-ui-fg-muted"})})},re=Object.assign(_t,{Item:kt,DragHandle:Dt}),ne=t=>{if(t.length===0)return[];if(t.length===1)return t[0].values.map(n=>({[t[0].title]:n}));const s=t[0],a=t.slice(1);return s.values.flatMap(n=>ne(a).map(i=>({[s.title]:n,...i})))},Z=t=>Object.values(t).join(" / "),It=({form:t})=>{var N,V,D;const{t:s}=I(),a=q({control:t.control,name:"options"}),n=q({control:t.control,name:"variants"}),i=B({control:t.control,name:"enable_variants",defaultValue:!1}),r=B({control:t.control,name:"options",defaultValue:[]}),u=B({control:t.control,name:"variants",defaultValue:[]}),v=!!((N=t.formState.errors.options)!=null&&N.length),m=((D=(V=t.formState.errors.variants)==null?void 0:V.root)==null?void 0:D.message)==="invalid_length",p=(f,h)=>{const{isTouched:c}=t.getFieldState("variants"),j=[...r];j[f].values=h;const y=ne(j),w=[...u],z=b=>y.find(M=>Object.keys(b).every(k=>b[k]===M[k])),T=w.reduce((b,M)=>{const k=z(M.options);return k&&b.push({...M,title:Z(k),options:k}),b},[]),C=new Set(T.map(b=>b.options));y.filter(b=>!C.has(b)).forEach(b=>{T.push({title:Z(b),options:b,should_create:!c,variant_rank:T.length,inventory:[{inventory_item_id:"",required_quantity:""}]})}),t.setValue("variants",T)},o=f=>{if(f===0)return;a.remove(f);const h=[...r];h.splice(f,1);const c=ne(h),j=[...u],y=C=>c.find(P=>Object.keys(C).every(b=>C[b]===P[b])),w=j.reduce((C,P)=>{const b=y(P.options);return b&&C.push({...P,title:Z(b),options:b}),C},[]),z=new Set(w.map(C=>C.options));c.filter(C=>!z.has(C)).forEach(C=>{w.push({title:Z(C),options:C,should_create:!1,variant_rank:w.length})}),t.setValue("variants",w)},d=f=>{const h=f.map((c,j)=>{const y=u.find(w=>w.title===c.title);return{id:c.id,...y||c,variant_rank:j}});n.replace(h)},g=f=>f.every(h=>h.should_create)?!0:f.some(h=>h.should_create)?"indeterminate":!1,x=f=>{switch(f){case!0:{const h=u.map(c=>({...c,should_create:!0}));t.setValue("variants",h);break}case!1:{const h=u.map(c=>({...c,should_create:!1}));t.setValue("variants",ce(h));break}}},S=()=>{t.setValue("options",[{title:"Default option",values:["Default option value"]}]),t.setValue("variants",ce([{title:"Default variant",should_create:!0,variant_rank:0,options:{"Default option":"Default option value"},inventory:[{inventory_item_id:"",required_quantity:""}],is_default:!0}]))};return e.jsxs("div",{id:"variants",className:"flex flex-col gap-y-8",children:[e.jsxs("div",{className:"flex flex-col gap-y-6",children:[e.jsx(ee,{level:"h2",children:s("products.create.variants.header")}),e.jsx(De,{control:t.control,name:"enable_variants",label:s("products.create.variants.subHeadingTitle"),description:s("products.create.variants.subHeadingDescription"),onCheckedChange:f=>{f?(t.setValue("options",[{title:"",values:[]}]),t.setValue("variants",[])):S()}})]}),i&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex flex-col gap-y-6",children:e.jsx(l.Field,{control:t.control,name:"options",render:()=>e.jsx(l.Item,{children:e.jsxs("div",{className:"flex flex-col gap-y-6",children:[e.jsxs("div",{className:"flex items-start justify-between gap-x-4",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx(l.Label,{children:s("products.create.variants.productOptions.label")}),e.jsx(l.Hint,{children:s("products.create.variants.productOptions.hint")})]}),e.jsx(F,{size:"small",variant:"secondary",type:"button",onClick:()=>{a.append({title:"",values:[]})},children:s("actions.add")})]}),v&&e.jsx(ae,{dismissible:!0,variant:"error",children:s("products.create.errors.options")}),e.jsx("ul",{className:"flex flex-col gap-y-4",children:a.fields.map((f,h)=>e.jsxs("li",{className:"bg-ui-bg-component shadow-elevation-card-rest grid grid-cols-[1fr_28px] items-center gap-1.5 rounded-xl p-1.5",children:[e.jsxs("div",{className:"grid grid-cols-[min-content,1fr] items-center gap-1.5",children:[e.jsx("div",{className:"flex items-center px-2 py-1.5",children:e.jsx(X,{size:"xsmall",weight:"plus",className:"text-ui-fg-subtle",htmlFor:`options.${h}.title`,children:s("fields.title")})}),e.jsx(Q,{className:"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",...t.register(`options.${h}.title`),placeholder:s("products.fields.options.optionTitlePlaceholder")}),e.jsx("div",{className:"flex items-center px-2 py-1.5",children:e.jsx(X,{size:"xsmall",weight:"plus",className:"text-ui-fg-subtle",htmlFor:`options.${h}.values`,children:s("fields.values")})}),e.jsx(it,{control:t.control,name:`options.${h}.values`,render:({field:{onChange:c,...j}})=>{const y=w=>{p(h,w),c(w)};return e.jsx(Qe,{...j,variant:"contrast",onChange:y,placeholder:s("products.fields.options.variantionsPlaceholder")})}})]}),e.jsx(H,{type:"button",size:"small",variant:"transparent",className:"text-ui-fg-muted",disabled:h===0,onClick:()=>o(h),children:e.jsx(ze,{})})]},f.id))})]})})})}),e.jsx("div",{className:"grid grid-cols-1 gap-x-4 gap-y-8",children:e.jsxs("div",{className:"flex flex-col gap-y-6",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx(X,{weight:"plus",children:s("products.create.variants.productVariants.label")}),e.jsx(lt,{children:s("products.create.variants.productVariants.hint")})]}),!v&&m&&e.jsx(ae,{dismissible:!0,variant:"error",children:s("products.create.errors.variants")}),n.fields.length>0?e.jsxs("div",{className:"overflow-hidden rounded-xl border",children:[e.jsxs("div",{className:"bg-ui-bg-component text-ui-fg-subtle grid items-center gap-3 border-b px-6 py-2.5",style:{gridTemplateColumns:`20px 28px repeat(${r.length}, 1fr)`},children:[e.jsx("div",{children:e.jsx(ue,{className:"relative",checked:g(u),onCheckedChange:x})}),e.jsx("div",{}),r.map((f,h)=>e.jsx("div",{children:e.jsx(K,{size:"small",leading:"compact",weight:"plus",children:f.title})},h))]}),e.jsx(re,{items:n.fields,onChange:d,renderItem:(f,h)=>e.jsx(re.Item,{id:f.id,className:Se("bg-ui-bg-base border-b",{"border-b-0":h===n.fields.length-1}),children:e.jsxs("div",{className:"text-ui-fg-subtle grid w-full items-center gap-3 px-6 py-2.5",style:{gridTemplateColumns:`20px 28px repeat(${r.length}, 1fr)`},children:[e.jsx(l.Field,{control:t.control,name:`variants.${h}.should_create`,render:({field:{value:c,onChange:j,...y}})=>e.jsx(l.Item,{children:e.jsx(l.Control,{children:e.jsx(ue,{className:"relative",...y,checked:c,onCheckedChange:j})})})}),e.jsx(re.DragHandle,{}),Object.values(f.options).map((c,j)=>e.jsx(K,{size:"small",leading:"compact",children:c},j))]})})})]}):e.jsx(ae,{children:s("products.create.variants.productVariants.alert")}),n.fields.length>0&&e.jsx(ot,{label:s("general.tip"),children:s("products.create.variants.productVariants.tip")})]})})]})]})},Tt=({form:t})=>{const{getFormFields:s}=ie(),a=s("product","create","general");return e.jsx("div",{className:"flex flex-col items-center p-16",children:e.jsxs("div",{className:"flex w-full max-w-[720px] flex-col gap-y-8",children:[e.jsx(zt,{}),e.jsxs("div",{className:"flex flex-col gap-y-6",children:[e.jsx(bt,{form:t}),e.jsx(Pe,{fields:a,form:t}),e.jsx(Ct,{form:t})]}),e.jsx(nt,{}),e.jsx(It,{form:t})]})})},zt=()=>{const{t}=I();return e.jsx("div",{className:"flex flex-col",children:e.jsx(ee,{children:t("products.create.header")})})};function Et({form:t,variant:s,index:a}){const{t:n}=I(),i=q({control:t.control,name:`variants.${a}.inventory`}),r=B({control:t.control,name:`variants.${a}.inventory`}),u=G({queryKey:["inventory_items"],queryFn:m=>R.admin.inventoryItem.list(m),getOptions:m=>m.inventory_items.map(p=>({label:p.title,value:p.id}))}),v=(m,p)=>r==null?void 0:r.some((o,d)=>d!=p&&o.inventory_item_id===m.value);return e.jsxs("div",{className:"grid gap-y-4",children:[e.jsxs("div",{className:"flex items-start justify-between gap-x-4",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx(l.Label,{children:s.title}),e.jsx(l.Hint,{children:n("products.create.inventory.label")})]}),e.jsx(F,{size:"small",variant:"secondary",type:"button",onClick:()=>{i.append({inventory_item_id:"",required_quantity:""})},children:n("actions.add")})]}),i.fields.map((m,p)=>e.jsxs("li",{className:"bg-ui-bg-component shadow-elevation-card-rest grid grid-cols-[1fr_28px] items-center gap-1.5 rounded-xl p-1.5",children:[e.jsxs("div",{className:"grid grid-cols-[min-content,1fr] items-center gap-1.5",children:[e.jsx("div",{className:"flex items-center px-2 py-1.5",children:e.jsx(X,{size:"xsmall",weight:"plus",className:"text-ui-fg-subtle",htmlFor:`variants.${a}.inventory.${p}.inventory_item_id`,children:n("fields.item")})}),e.jsx(l.Field,{control:t.control,name:`variants.${a}.inventory.${p}.inventory_item_id`,render:({field:o})=>e.jsx(l.Item,{children:e.jsx(l.Control,{children:e.jsx(U,{...o,options:u.options.map(d=>({...d,disabled:v(d,p)})),searchValue:u.searchValue,onSearchValueChange:u.onSearchValueChange,fetchNextPage:u.fetchNextPage,className:"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",placeholder:n("products.create.inventory.itemPlaceholder")})})})}),e.jsx("div",{className:"flex items-center px-2 py-1.5",children:e.jsx(X,{size:"xsmall",weight:"plus",className:"text-ui-fg-subtle",htmlFor:`variants.${a}.inventory.${p}.required_quantity`,children:n("fields.quantity")})}),e.jsx(l.Field,{control:t.control,name:`variants.${a}.inventory.${p}.required_quantity`,render:({field:{onChange:o,value:d,...g}})=>e.jsxs(l.Item,{children:[e.jsx(l.Control,{children:e.jsx(Q,{type:"number",className:"bg-ui-bg-field-component",min:0,value:d,onChange:x=>{const S=x.target.value;o(S===""?null:Number(S))},...g,placeholder:n("products.create.inventory.quantityPlaceholder")})}),e.jsx(l.ErrorMessage,{})]})})]}),e.jsx(H,{type:"button",size:"small",variant:"transparent",className:"text-ui-fg-muted",onClick:()=>i.remove(p),children:e.jsx(ze,{})})]},m.id))]})}var Ft=({form:t})=>{const{t:s}=I(),a=q({control:t.control,name:"variants"});return e.jsxs("div",{id:"organize",className:"flex flex-col gap-y-8",children:[e.jsx(ee,{children:s("products.create.inventory.heading")}),a.fields.filter(n=>n.inventory_kit).map((n,i)=>e.jsx(Et,{form:t,variant:n,index:i},n.id))]})},Mt=({form:t})=>e.jsx("div",{className:"flex flex-col items-center p-16",children:e.jsx("div",{className:"flex w-full max-w-[720px] flex-col gap-y-8",children:e.jsx(Ft,{form:t})})}),Ot=({form:t})=>{const{t:s}=I(),a=G({queryKey:["product_collections"],queryFn:o=>R.admin.productCollection.list(o),getOptions:o=>o.collections.map(d=>({label:d.title,value:d.id}))}),n=G({queryKey:["product_types"],queryFn:o=>R.admin.productType.list(o),getOptions:o=>o.product_types.map(d=>({label:d.value,value:d.id}))}),i=G({queryKey:["product_tags"],queryFn:o=>R.admin.productTag.list(o),getOptions:o=>o.product_tags.map(d=>({label:d.value,value:d.id}))}),r=G({queryKey:["shipping_profiles"],queryFn:o=>R.admin.shippingProfile.list(o),getOptions:o=>o.shipping_profiles.map(d=>({label:d.name,value:d.id}))}),{fields:u,remove:v,replace:m}=q({control:t.control,name:"sales_channels",keyName:"key"}),p=()=>{m([])};return e.jsxs("div",{id:"organize",className:"flex flex-col gap-y-8",children:[e.jsx(ee,{children:s("products.organization.header")}),e.jsx(De,{control:t.control,name:"discountable",label:s("products.fields.discountable.label"),description:s("products.fields.discountable.hint"),optional:!0}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx(l.Field,{control:t.control,name:"type_id",render:({field:o})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{optional:!0,children:s("products.fields.type.label")}),e.jsx(l.Control,{children:e.jsx(U,{...o,options:n.options,searchValue:n.searchValue,onSearchValueChange:n.onSearchValueChange,fetchNextPage:n.fetchNextPage})}),e.jsx(l.ErrorMessage,{})]})}),e.jsx(l.Field,{control:t.control,name:"collection_id",render:({field:o})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{optional:!0,children:s("products.fields.collection.label")}),e.jsx(l.Control,{children:e.jsx(U,{...o,options:a.options,searchValue:a.searchValue,onSearchValueChange:a.onSearchValueChange,fetchNextPage:a.fetchNextPage})}),e.jsx(l.ErrorMessage,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsx(l.Field,{control:t.control,name:"categories",render:({field:o})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{optional:!0,children:s("products.fields.categories.label")}),e.jsx(l.Control,{children:e.jsx(Ke,{...o})}),e.jsx(l.ErrorMessage,{})]})}),e.jsx(l.Field,{control:t.control,name:"tags",render:({field:o})=>e.jsxs(l.Item,{children:[e.jsx(l.Label,{optional:!0,children:s("products.fields.tags.label")}),e.jsx(l.Control,{children:e.jsx(U,{...o,options:i.options,searchValue:i.searchValue,onSearchValueChange:i.onSearchValueChange,fetchNextPage:i.fetchNextPage})}),e.jsx(l.ErrorMessage,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx(l.Label,{optional:!0,children:s("products.fields.shipping_profile.label")}),e.jsx(l.Hint,{children:e.jsx(de,{i18nKey:"products.fields.shipping_profile.hint"})})]}),e.jsx(l.Field,{control:t.control,name:"shipping_profile_id",render:({field:o})=>e.jsxs(l.Item,{children:[e.jsx(l.Control,{children:e.jsx(U,{...o,options:r.options,searchValue:r.searchValue,onSearchValueChange:r.onSearchValueChange,fetchNextPage:r.fetchNextPage})}),e.jsx(l.ErrorMessage,{})]})})]}),e.jsx("div",{className:"grid grid-cols-1 gap-y-4",children:e.jsx(l.Field,{control:t.control,name:"sales_channels",render:()=>e.jsxs(l.Item,{children:[e.jsxs("div",{className:"flex items-start justify-between gap-x-4",children:[e.jsxs("div",{children:[e.jsx(l.Label,{optional:!0,children:s("products.fields.sales_channels.label")}),e.jsx(l.Hint,{children:e.jsx(de,{i18nKey:"products.fields.sales_channels.hint"})})]}),e.jsx($.Trigger,{asChild:!0,children:e.jsx(F,{size:"small",variant:"secondary",type:"button",children:s("actions.add")})})]}),e.jsx(l.Control,{className:"mt-0",children:u.length>0&&e.jsx(oe,{onClearAll:p,onRemove:v,className:"py-4",children:u.map((o,d)=>e.jsx(oe.Chip,{index:d,children:o.name},o.key))})})]})})})]})},W="sc",pe=50,At=({form:t})=>{const{t:s}=I(),{getValues:a,setValue:n}=t,{setIsOpen:i,getIsOpen:r}=ht(),[u,v]=_.useState({}),[m,p]=_.useState([]),o=Be({pageSize:pe,prefix:W}),{sales_channels:d,count:g,isLoading:x,isError:S,error:N}=ct(o,{placeholderData:dt}),V=r(W);_.useEffect(()=>{if(!V)return;const y=a("sales_channels");y&&(p(y.map(w=>({id:w.id,name:w.name}))),v(y.reduce((w,z)=>({...w,[z.id]:!0}),{})))},[V,a]);const D=y=>{const w=Object.keys(y),z=new Set(w.filter(C=>y[C]&&!u[C]));let T=[];z.size>0&&(T=(d==null?void 0:d.filter(C=>z.has(C.id)))??[]),p(C=>{const P=C.filter(b=>y[b.id]);return Array.from(new Set([...P,...T]))}),v(y)},f=()=>{n("sales_channels",m,{shouldDirty:!0,shouldTouch:!0}),i(W,!1)},h=He(),c=Rt(),j=Le();if(S)throw N;return e.jsxs($.Content,{className:"flex flex-col overflow-hidden",children:[e.jsx($.Header,{}),e.jsx($.Body,{className:"flex-1 overflow-hidden",children:e.jsx(ut,{data:d,columns:c,filters:h,emptyState:j,rowCount:g,pageSize:pe,getRowId:y=>y.id,rowSelection:{state:u,onRowSelectionChange:D},isLoading:x,layout:"fill",prefix:W})}),e.jsx($.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx($.Close,{asChild:!0,children:e.jsx(F,{size:"small",variant:"secondary",type:"button",children:s("actions.cancel")})}),e.jsx(F,{size:"small",onClick:f,type:"button",children:s("actions.save")})]})})]})},$t=mt(),Rt=()=>{const t=qe();return _.useMemo(()=>[$t.select(),...t],[t])},Bt=({form:t})=>{const{getFormFields:s}=ie(),a=s("product","create","organize");return e.jsxs($,{id:W,children:[e.jsx("div",{className:"flex flex-col items-center p-16",children:e.jsxs("div",{className:"flex w-full max-w-[720px] flex-col gap-y-8",children:[e.jsx(Ot,{form:t}),e.jsx(Pe,{fields:a,form:t})]})}),e.jsx(At,{form:t})]})},Ht=({form:t,regions:s,store:a,pricePreferences:n})=>{const{setCloseOnEscape:i}=Ie(),r=_.useMemo(()=>{var o;return((o=a==null?void 0:a.supported_currencies)==null?void 0:o.map(d=>d.currency_code))||[]},[a]),u=B({control:t.control,name:"variants",defaultValue:[]}),v=B({control:t.control,name:"options",defaultValue:[]}),m=Lt({options:v,currencies:r,regions:s,pricePreferences:n}),p=_.useMemo(()=>{const o=[];return u.forEach((d,g)=>{d.should_create&&o.push({...d,originalIndex:g})}),o},[u]);return e.jsx("div",{className:"flex size-full flex-col divide-y overflow-hidden",children:e.jsx(A,{columns:m,data:p,state:t,onEditingChange:o=>i(!o)})})},L=Je(),Lt=({options:t,currencies:s=[],regions:a=[],pricePreferences:n=[]})=>{const{t:i}=I();return _.useMemo(()=>[L.column({id:"options",header:()=>e.jsx("div",{className:"flex size-full items-center overflow-hidden",children:e.jsx("span",{className:"truncate",children:t.map(r=>r.title).join(" / ")})}),cell:r=>e.jsx(A.ReadonlyCell,{context:r,children:t.map(u=>r.row.original.options[u.title]).join(" / ")}),disableHiding:!0}),L.column({id:"title",name:i("fields.title"),header:i("fields.title"),field:r=>`variants.${r.row.original.originalIndex}.title`,type:"text",cell:r=>e.jsx(A.TextCell,{context:r})}),L.column({id:"sku",name:i("fields.sku"),header:i("fields.sku"),field:r=>`variants.${r.row.original.originalIndex}.sku`,type:"text",cell:r=>e.jsx(A.TextCell,{context:r})}),L.column({id:"manage_inventory",name:i("fields.managedInventory"),header:i("fields.managedInventory"),field:r=>`variants.${r.row.original.originalIndex}.manage_inventory`,type:"boolean",cell:r=>e.jsx(A.BooleanCell,{context:r})}),L.column({id:"allow_backorder",name:i("fields.allowBackorder"),header:i("fields.allowBackorder"),field:r=>`variants.${r.row.original.originalIndex}.allow_backorder`,type:"boolean",cell:r=>e.jsx(A.BooleanCell,{context:r})}),L.column({id:"inventory_kit",name:i("fields.inventoryKit"),header:i("fields.inventoryKit"),field:r=>`variants.${r.row.original.originalIndex}.inventory_kit`,type:"boolean",cell:r=>e.jsx(A.BooleanCell,{context:r,disabled:!r.row.original.manage_inventory})}),...Ze({currencies:s,regions:a,pricePreferences:n,getFieldName:(r,u)=>{var v;return(v=r.column.id)!=null&&v.startsWith("currency_prices")?`variants.${r.row.original.originalIndex}.prices.${u}`:`variants.${r.row.original.originalIndex}.prices.${u}`},t:i})],[s,a,t,n,i])},he="save-draft-button",qt=({defaultChannel:t,regions:s,store:a,pricePreferences:n})=>{const[i,r]=_.useState("details"),[u,v]=_.useState({details:"in-progress",organize:"not-started",variants:"not-started",inventory:"not-started"}),{t:m}=I(),{handleSuccess:p}=Ie(),{getFormConfigs:o}=ie(),d=o("product","create"),g=at({defaultValues:{...Ge,sales_channels:t?[{id:t.id,name:t.name}]:[]},schema:Ue,configs:d}),{mutateAsync:x,isPending:S}=rt(),N=_.useMemo(()=>s!=null&&s.length?s.reduce((c,j)=>(c[j.id]=j.currency_code,c),{}):{},[s]),V=B({control:g.control,name:"variants"}),D=_.useMemo(()=>V.some(c=>c.manage_inventory&&c.inventory_kit),[V]),f=g.handleSubmit(async(c,j)=>{var C;let y=!1;(j==null?void 0:j.nativeEvent)instanceof SubmitEvent&&(y=((C=j==null?void 0:j.nativeEvent)==null?void 0:C.submitter).dataset.name===he);const w=c.media||[],z={...c,media:void 0};let T=[];try{if(w.length){const P=w.find(k=>k.isThumbnail),b=w.filter(k=>!k.isThumbnail),M=[];P&&M.push(R.admin.upload.create({files:[P.file]}).then(k=>k.files.map(te=>({...te,isThumbnail:!0})))),b!=null&&b.length&&M.push(R.admin.upload.create({files:b.map(k=>k.file)}).then(k=>k.files.map(te=>({...te,isThumbnail:!1})))),T=(await Promise.all(M)).flat()}}catch(P){P instanceof Error&&se.error(P.message)}await x(We({...z,media:T,status:y?"draft":"published",regionsCurrencyMap:N}),{onSuccess:P=>{se.success(m("products.create.successToast",{title:P.product.title})),p(`../${P.product.id}`)},onError:P=>{se.error(P.message)}})}),h=async c=>{await g.trigger()&&(c==="details"&&r("organize"),c==="organize"&&r("variants"),c==="variants"&&r("inventory"))};return _.useEffect(()=>{const c={...u};i==="details"&&(c.details="in-progress"),i==="organize"&&(c.details="completed",c.organize="in-progress"),i==="variants"&&(c.details="completed",c.organize="completed",c.variants="in-progress"),i==="inventory"&&(c.details="completed",c.organize="completed",c.variants="completed",c.inventory="in-progress"),v({...c})},[i]),e.jsx(O.Form,{form:g,children:e.jsxs(pt,{onKeyDown:c=>{if(c.key==="Enter"){if(c.target instanceof HTMLTextAreaElement&&!(c.metaKey||c.ctrlKey))return;if(c.preventDefault(),c.metaKey||c.ctrlKey){if(i!=="variants"){c.preventDefault(),c.stopPropagation(),h(i);return}f()}}},onSubmit:f,className:"flex h-full flex-col",children:[e.jsxs(E,{value:i,onValueChange:async c=>{await g.trigger()&&r(c)},className:"flex h-full flex-col overflow-hidden",children:[e.jsx(O.Header,{children:e.jsx("div",{className:"-my-2 w-full border-l",children:e.jsxs(E.List,{className:"justify-start-start flex w-full items-center",children:[e.jsx(E.Trigger,{status:u.details,value:"details",className:"max-w-[200px] truncate",children:m("products.create.tabs.details")}),e.jsx(E.Trigger,{status:u.organize,value:"organize",className:"max-w-[200px] truncate",children:m("products.create.tabs.organize")}),e.jsx(E.Trigger,{status:u.variants,value:"variants",className:"max-w-[200px] truncate",children:m("products.create.tabs.variants")}),D&&e.jsx(E.Trigger,{status:u.inventory,value:"inventory",className:"max-w-[200px] truncate",children:m("products.create.tabs.inventory")})]})})}),e.jsxs(O.Body,{className:"size-full overflow-hidden",children:[e.jsx(E.Content,{className:"size-full overflow-y-auto",value:"details",children:e.jsx(Tt,{form:g})}),e.jsx(E.Content,{className:"size-full overflow-y-auto",value:"organize",children:e.jsx(Bt,{form:g})}),e.jsx(E.Content,{className:"size-full overflow-y-auto",value:"variants",children:e.jsx(Ht,{form:g,store:a,regions:s,pricePreferences:n})}),D&&e.jsx(E.Content,{className:"size-full overflow-y-auto",value:"inventory",children:e.jsx(Mt,{form:g})})]})]}),e.jsx(O.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(O.Close,{asChild:!0,children:e.jsx(F,{variant:"secondary",size:"small",children:m("actions.cancel")})}),e.jsx(F,{"data-name":he,size:"small",type:"submit",isLoading:S,className:"whitespace-nowrap",children:m("actions.saveAsDraft")}),e.jsx(Kt,{tab:i,next:h,isLoading:S,showInventoryTab:D})]})})]})})},Kt=({tab:t,next:s,isLoading:a,showInventoryTab:n})=>{const{t:i}=I();return t==="variants"&&!n||t==="inventory"&&n?e.jsx(F,{"data-name":"publish-button",type:"submit",variant:"primary",size:"small",isLoading:a,children:i("actions.publish")},"submit-button"):e.jsx(F,{type:"button",variant:"primary",size:"small",onClick:()=>s(t),children:i("actions.continue")},"next-button")},As=()=>{const{t}=I(),{store:s,isPending:a,isError:n,error:i}=Ye({fields:"+default_sales_channel"}),{sales_channel:r,isPending:u,isError:v,error:m}=et(s==null?void 0:s.default_sales_channel_id,{enabled:!!(s!=null&&s.default_sales_channel_id)}),{regions:p,isPending:o,isError:d,error:g}=tt({limit:9999}),{price_preferences:x,isPending:S,isError:N,error:V}=st({limit:9999}),D=!!s&&!a&&!!p&&!o&&!!r&&!u&&!!x&&!S;if(n)throw i;if(d)throw g;if(v)throw m;if(N)throw V;return e.jsxs(O,{children:[e.jsx(O.Title,{asChild:!0,children:e.jsx("span",{className:"sr-only",children:t("products.create.title")})}),e.jsx(O.Description,{asChild:!0,children:e.jsx("span",{className:"sr-only",children:t("products.create.description")})}),D&&e.jsx(qt,{defaultChannel:r,store:s,pricePreferences:x,regions:p})]})};export{As as Component};
