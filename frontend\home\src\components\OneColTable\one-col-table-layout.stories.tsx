import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, within, userEvent } from '@storybook/test';
import { Search } from 'lucide-react';

import OneColTableLayout from './one-col-table-layout';
import { Button } from '@/components/ui/button';
import { mockItems, virtualizedItems } from './__fixtures__/one-col-table.fixtures';

const meta = {
  title: 'Components/OneColTable/OneColTableLayout',
  component: OneColTableLayout,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A higher-level component that wraps OneColTable with additional functionality such as search, sorting, and pagination. Ideal for building data-rich interfaces without managing state logic.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      description: 'Title for the table section',
      control: 'text',
      defaultValue: 'Items',
      table: {
        type: { summary: 'string' },
      },
    },
    showSearch: {
      description: 'Whether to show the search input',
      control: 'boolean',
      defaultValue: true,
      table: {
        type: { summary: 'boolean' },
      },
    },
    showSort: {
      description: 'Whether to show the sorting options',
      control: 'boolean',
      defaultValue: true,
      table: {
        type: { summary: 'boolean' },
      },
    },
    showPagination: {
      description: 'Whether to show pagination',
      control: 'boolean',
      defaultValue: true,
      table: {
        type: { summary: 'boolean' },
      },
    },
    pageSize: {
      description: 'Number of items per page',
      control: 'number',
      defaultValue: 5,
      table: {
        type: { summary: 'number' },
      },
    },
    infiniteLoading: {
      description: 'Whether to enable infinite loading (load more) instead of pagination',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
    virtualized: {
      description: 'Whether to use virtualization for large datasets',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
    size: {
      description: 'Size variant for the table',
      control: 'select',
      options: ['sm', 'md', 'lg'],
      defaultValue: 'md',
      table: {
        type: { summary: 'string' },
      },
    },
    variant: {
      description: 'Style variant for the table',
      control: 'select',
      options: ['primary', 'secondary', 'outline'],
      defaultValue: 'primary',
      table: {
        type: { summary: 'string' },
      },
    },
    loading: {
      description: 'Whether the table is in a loading state',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
  },
} satisfies Meta<typeof OneColTableLayout>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    title: 'Items',
    items: mockItems,
    showSearch: true,
    showSort: true,
    showPagination: true,
    pageSize: 5,
    size: 'md',
    variant: 'primary',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const title = await canvas.findByText('Items');
    expect(title).toBeInTheDocument();
  },
};

// With Search and Sorting
export const WithSearchAndSort: Story = {
  args: {
    title: 'Searchable Items',
    items: mockItems,
    showSearch: true,
    showSort: true,
    showPagination: false,
    sortableFields: [
      { field: 'timestamp', label: 'Date' },
      { field: 'content', label: 'Content' },
      { field: 'status', label: 'Status' },
    ],
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test search functionality
    const searchInput = await canvas.findByPlaceholderText('Search...');
    await userEvent.type(searchInput, 'Batch');

    // Select a sort option
    const sortSelect = await canvas.findByRole('combobox');
    await userEvent.click(sortSelect);
  },
};

// With Pagination
export const WithPagination: Story = {
  args: {
    title: 'Paginated Items',
    items: [...mockItems, ...mockItems, ...mockItems], // Add more items to show pagination
    showSearch: false,
    showSort: false,
    showPagination: true,
    pageSize: 5,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Test pagination
    const nextButton = await canvas.findByLabelText('Go to next page');
    await userEvent.click(nextButton);
  },
};

// With Infinite Loading
export const WithInfiniteLoading: Story = {
  args: {
    title: 'Infinite Loading Items',
    items: mockItems,
    showSearch: true,
    showSort: true,
    infiniteLoading: true,
  },
};

// With Virtualization
export const WithVirtualization: Story = {
  args: {
    title: 'Virtualized Items (1000 items)',
    items: virtualizedItems,
    showSearch: true,
    showSort: true,
    virtualized: true,
  },
};

// Combined Usage
export const FullFeatured: Story = {
  render: () => (
    <div className="space-y-8">
      <OneColTableLayout
        title="Complete Example"
        items={[...mockItems, ...mockItems, ...mockItems]}
        showSearch
        showSort
        showPagination
        pageSize={5}
        sortableFields={[
          { field: 'timestamp', label: 'Date' },
          { field: 'content', label: 'Content' },
          { field: 'status', label: 'Status' },
        ]}
        variant="outline"
        size="md"
        onRowClick={(item) => console.warn('Clicked:', item)}
      />
    </div>
  ),
  args: {
    items: mockItems,
  },
};

// Responsive Layout
export const ResponsiveBehavior: Story = {
  args: {
    title: 'Responsive Layout',
    items: mockItems,
    showSearch: true,
    showSort: true,
    showPagination: true,
    className: 'w-full',
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    chromatic: {
      viewports: [320, 768, 1200],
    },
  },
};

// With Custom Header Actions
export const WithCustomHeader: Story = {
  render: () => (
    <div>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">Custom Header</h2>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Search className="mr-2 h-4 w-4" />
            Advanced Search
          </Button>
          <Button size="sm">Add New</Button>
        </div>
      </div>
      <OneColTableLayout
        title="Items with Custom Actions"
        items={mockItems}
        showSearch
        showSort
        showPagination
      />
    </div>
  ),
  args: {
    items: mockItems,
  },
};

// Empty State
export const EmptyState: Story = {
  args: {
    title: 'No Items',
    items: [],
    showSearch: true,
    showSort: true,
    showPagination: false,
  },
};
