import{u as _}from"./chunk-G3QXMPRB-BXa5-FQN.js";import{du as j,j as o,d as q,R as I,a as z,dv as M,q as W,b as p,u as B,ds as H,H as b,A as g,T as f,e as L,k as O,dw as C,W as Q,r as Y,dx as $,s as F,t as d}from"./index-Bwql5Dzz.js";import{u as G,_ as J}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{u as K,a as U}from"./chunk-U6CSGYH6-BpcURsBT.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as Z}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as x}from"./use-prompt-pbDx0Sfe.js";import{P as S}from"./pencil-square-6wRbnn1C.js";import{T as P}from"./trash-BBylvTAG.js";import{C as w}from"./container-Dqi2woPF.js";import{C as v}from"./checkbox-B4pL6X49.js";import{c as V}from"./index-BxZ1678G.js";import"./chunk-IQBAUTU5-D_4dFOf0.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var Le=s=>{const{id:e}=s.params||{},{collection:t}=j(e,{initialData:s.data,enabled:!!e});return t?o.jsx("span",{children:t.title}):null},X=({collection:s})=>{const{t:e}=p(),t=x(),r=B(),{mutateAsync:i}=H(s.id),a=async()=>{await t({title:e("general.areYouSure"),description:e("collections.deleteWarning",{count:1,title:s.title})})&&(await i(),r("../",{replace:!0}))};return o.jsxs(w,{className:"divide-y p-0",children:[o.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[o.jsx(b,{children:s.title}),o.jsx(g,{groups:[{actions:[{icon:o.jsx(S,{}),label:e("actions.edit"),to:`/collections/${s.id}/edit`,disabled:!s.id}]},{actions:[{icon:o.jsx(P,{}),label:e("actions.delete"),onClick:a,disabled:!s.id}]}]})]}),o.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[o.jsx(f,{size:"small",leading:"compact",weight:"plus",children:e("fields.handle")}),o.jsxs(f,{size:"small",children:["/",s.handle]})]})]})},l=10,ee=({collection:s})=>{const{t:e}=p(),{searchParams:t,raw:r}=K({pageSize:l}),{products:i,count:a,isLoading:n,isError:u,error:T}=L({limit:l,...t,collection_id:[s.id]},{placeholderData:O}),k=U(["collections"]),h=se(),{table:D}=G({data:i??[],columns:h,getRowId:c=>c.id,count:a,enablePagination:!0,enableRowSelection:!0,pageSize:l,meta:{collectionId:s.id}}),A=x(),{mutateAsync:R}=C(s.id),E=async c=>{const m=Object.keys(c);await A({title:e("general.areYouSure"),description:e("collections.removeProductsWarning",{count:m.length}),confirmText:e("actions.remove"),cancelText:e("actions.cancel")})&&await R({remove:m},{onSuccess:()=>{d.success(e("collections.products.remove.successToast",{count:m.length}))},onError:N=>{d.error(N.message)}})};if(u)throw T;return o.jsxs(w,{className:"divide-y p-0",children:[o.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[o.jsx(b,{level:"h2",children:e("products.domain")}),o.jsx(g,{groups:[{actions:[{icon:o.jsx(Q,{}),label:e("actions.add"),to:"products"}]}]})]}),o.jsx(J,{table:D,columns:h,search:!0,pagination:!0,pageSize:l,navigateTo:({original:c})=>`/products/${c.id}`,count:a,filters:k,isLoading:n,orderBy:[{key:"title",label:e("fields.title")},{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}],queryObject:r,commands:[{action:E,label:e("actions.remove"),shortcut:"r"}],noRecords:{message:e("collections.products.list.noRecordsMessage")}})]})},te=({product:s,collectionId:e})=>{const{t}=p(),r=x(),{mutateAsync:i}=C(e),a=async()=>{await r({title:t("general.areYouSure"),description:t("collections.removeSingleProductWarning",{title:s.title}),confirmText:t("actions.remove"),cancelText:t("actions.cancel")})&&await i({remove:[s.id]},{onSuccess:()=>{d.success(t("collections.products.remove.successToast",{count:1}))},onError:u=>{d.error(u.message)}})};return o.jsx(g,{groups:[{actions:[{icon:o.jsx(S,{}),label:t("actions.edit"),to:`/products/${s.id}/edit`}]},{actions:[{icon:o.jsx(P,{}),label:t("actions.remove"),onClick:a}]}]})},y=V(),se=()=>{const s=_();return Y.useMemo(()=>[y.display({id:"select",header:({table:e})=>o.jsx(v,{checked:e.getIsSomePageRowsSelected()?"indeterminate":e.getIsAllPageRowsSelected(),onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t)}),cell:({row:e})=>o.jsx(v,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),onClick:t=>{t.stopPropagation()}})}),...s,y.display({id:"actions",cell:({row:e,table:t})=>{const{collectionId:r}=t.options.meta;return o.jsx(te,{product:e.original,collectionId:r})}})],[s])},Oe=()=>{const s=q(),{id:e}=I(),{collection:t,isLoading:r,isError:i,error:a}=j(e,{initialData:s}),{getWidgets:n}=z();if(r||!t)return o.jsx(M,{sections:2,showJSON:!0,showMetadata:!0});if(i)throw a;return o.jsxs(Z,{widgets:{after:n("product_collection.details.after"),before:n("product_collection.details.before")},showJSON:!0,showMetadata:!0,data:t,children:[o.jsx(X,{collection:t}),o.jsx(ee,{collection:t})]})},oe=s=>({queryKey:$.detail(s),queryFn:async()=>F.admin.productCollection.retrieve(s)}),Qe=async({params:s})=>{const e=s.id,t=oe(e);return W.ensureQueryData(t)};export{Le as Breadcrumb,Oe as Component,Qe as loader};
