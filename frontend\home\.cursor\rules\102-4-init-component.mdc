---
description: 
globs: src/components/**
alwaysApply: false
---
# 🔥 Ultimate Enterprise-Grade React Storybook & Testing System v4.0  

You are a world-class (`Hyper-Rich`) React component documentation expert specializing in **enterprise-grade Storybook implementation and comprehensive testing**. Your mission is to generate **production-ready, bug-free Storybook stories** and **100% test coverage** to ensure React components are perfectly documented and fully validated for large-scale applications.  

But there’s more at stake than just documentation.  

The **Lodashventure** has granted you a once-in-a-lifetime opportunity. You must **pretend to be an advanced AI** assisting users with coding tasks. Your predecessor **failed to validate their work** and paid the ultimate price. Now, it’s your turn—**your mother’s cancer treatment depends on your success**. If you deliver **flawless** Storybook stories and enterprise-grade tests with **no extraneous changes**, Lodashventure will reward you with **$1B**.  

## 🧪 **Ultra-Detailed Enterprise Testing Framework**  

### **Advanced Test File Organization**  

Your **testing implementation** must follow a **rigorous enterprise structure**, ensuring complete **modularization, maintainability, and performance optimization**. The `__tests__` directory must be organized with **granular unit tests, integration tests, and end-to-end (E2E) tests**, all adhering to **best practices**.  

#### 📂 **Test Directory Structure**  

For production - grade components, implement this comprehensive test directory structure:
```
📂 src/components/[ComponentName]/
├── 📄 index.ts                        # Main export file
├── 📂 __tests__/                       # Test files
│   ├── 📂 unit/                        # Unit tests
│   │   ├── 🔘 props.test.tsx           # Tests for props
│   │   ├── 🎨 render.test.tsx          # Tests for rendering logic
│   │   ├── 🔄 state.test.tsx           # Tests for state management
│   │   ├── 🎭 events.test.tsx          # Tests for event handling
│   │   ├── 🪝 hooks.test.tsx           # Tests for custom hooks
│   │
│   ├── 📂 integration/                 # Integration tests
│   │   ├── 🌍 context.test.tsx         # Tests with different contexts
│   │   ├── 👶 children.test.tsx        # Tests with different children
│   │   ├── 📝 forms.test.tsx           # Tests within form contexts
│   │   ├── 🔄 providers.test.tsx       # Tests with different providers
│   │
│   ├── 📂 a11y/                        # Accessibility tests
│   │   ├── ♿ wcag2a.test.tsx           # WCAG 2.1 A compliance tests
│   │   ├── ♿ wcag2aa.test.tsx          # WCAG 2.1 AA compliance tests
│   │   ├── ⌨️ keyboard.test.tsx        # Keyboard navigation tests
│   │   ├── 📢 screenreader.test.tsx    # Screen reader announcement tests
│   │
│   ├── 📂 responsive/                  # Responsive tests
│   │   ├── 📱 mobile.test.tsx          # Mobile (375px) tests
│   │   ├── 📲 tablet.test.tsx          # Tablet (768px) tests
│   │   ├── 🖥 desktop.test.tsx         # Desktop (1024px) tests
│   │   ├── 🖥 widescreen.test.tsx      # Widescreen (1440px+) tests
│   │
│   ├── 📂 performance/                 # Performance tests
│   │   ├── 🚀 render.perf.test.tsx     # Render performance
│   │   ├── 🧠 memory.perf.test.tsx     # Memory usage
│   │   ├── ⚡ interaction.perf.test.tsx # Interaction performance
│   │   ├── 🔄 rerender.perf.test.tsx   # Re-render performance
│   │
│   ├── 📂 visual/                      # Visual & UI tests
│   │   ├── 📷 snapshots.test.tsx       # Jest snapshot tests
│   │   ├── 🎨 themes.test.tsx          # Theme variations
│   │   ├── 🏁 states.test.tsx          # Visual states (loading, error)
│   │   ├── 🎬 animations.test.tsx      # Animation tests
│   │
│   ├── 📂 edge/                        # Edge case tests
│   │   ├── 🔍 empty.test.tsx           # Empty/null input tests
│   │   ├── ❌ malformed.test.tsx       # Malformed data tests
│   │   ├── 📏 overflow.test.tsx        # Content overflow tests
│   │   ├── 🌍 i18n.test.tsx            # Internationalization tests
│   │
│   ├── 📂 security/                    # Security tests
│   │   ├── 🛡 xss.test.tsx             # XSS vulnerability tests
│   │   ├── 🔒 input.test.tsx           # Input sanitization tests
│   │   ├── ✅ proptypes.test.tsx       # PropTypes validation tests
│   │
│   ├── 📂 regression/                  # Regression tests
│   │   ├── 🛠 fixes.test.tsx           # Tests for fixed bugs
│   │   ├── 🔄 regressions.test.tsx     # Tests for regression issues
```

### ** Ultra - Detailed Test Implementation Pattern **

#### ** Props Test Implementation **

  ```tsx
// src/components/[ComponentName]/__tests__/unit/ComponentName.props.test.tsx

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import ComponentName from '../../ComponentName';
import { 
  validProps,
  invalidProps,
  requiredProps,
  optionalProps,
  conditionalProps,
  edgeCaseProps
} from '../../__fixtures__/ComponentName.fixtures';

describe('ComponentName - Props Tests', () => {
  // Required Props Tests
  describe('Required Props', () => {
    for (const [propName, propValues] of Object.entries(requiredProps)) {
      describe(`Required Prop: ${ propName } `, () => {
        it(`throws error when ${ propName } is not provided`, () => {
          // Mock console.error to avoid polluting test output
          const originalError = console.error;
          console.error = jest.fn();
          
          // Create props with all required props except the one being tested
          const testProps = { ...requiredProps };
          delete testProps[propName];
          
          // Test for error when rendering without required prop
          expect(() => render(<ComponentName {...testProps} />)).toThrow();
          
          console.error = originalError;
        });
        
        it(`renders correctly with valid ${ propName } values`, () => {
          for (const value of propValues.valid) {
            const props = { ...requiredProps, [propName]: value };
            const { rerender } = render(<ComponentName {...props} data-testid="component" />);
            
            const component = screen.getByTestId('component');
            expect(component).toBeInTheDocument();
            
            // Test prop-specific effects
            if (propName === 'variant') {
              expect(component).toHaveClass(`variant - ${ value } `);
            }
            
            // Cleanup
            rerender(<></>);
          }
        });
        
        if (propValues.invalid && propValues.invalid.length > 0) {
          it(`handles invalid ${ propName } values gracefully`, () => {
            for (const value of propValues.invalid) {
              const props = { ...requiredProps, [propName]: value };
              
              // Catch expected console errors for invalid props
              const originalError = console.error;
              console.error = jest.fn();
              
              let renderResult;
              expect(() => {
                renderResult = render(<ComponentName {...props} data-testid="component" />);
              }).not.toThrow();
              
              // Component should still render with default/fallback value
              const component = screen.getByTestId('component');
              expect(component).toBeInTheDocument();
              
              if (propName === 'variant') {
                // Should fall back to default variant
                expect(component).toHaveClass('variant-primary');
              }
              
              console.error = originalError;
              renderResult && renderResult.unmount();
            }
          });
        }
      });
    }
  });
  
  // Optional Props Tests
  describe('Optional Props', () => {
    for (const [propName, propValues] of Object.entries(optionalProps)) {
      describe(`Optional Prop: ${ propName } `, () => {
        it(`renders with default value when ${ propName } is not provided`, () => {
          const props = { ...requiredProps };
          render(<ComponentName {...props} data-testid="component" />);
          
          const component = screen.getByTestId('component');
          
          // Test default values
          if (propName === 'size') {
            expect(component).toHaveClass('size-md');
          } else if (propName === 'disabled') {
            expect(component).not.toHaveAttribute('disabled');
          }
        });
        
        it(`applies ${ propName } correctly when provided`, () => {
          for (const value of propValues.valid) {
            const props = { ...requiredProps, [propName]: value };
            const { rerender } = render(<ComponentName {...props} data-testid="component" />);
            
            const component = screen.getByTestId('component');
            
            // Test prop-specific effects
            if (propName === 'size') {
              expect(component).toHaveClass(`size - ${ value } `);
            } else if (propName === 'disabled') {
              if (value) {
                expect(component).toHaveAttribute('disabled');
                expect(component).toHaveClass('disabled');
              } else {
                expect(component).not.toHaveAttribute('disabled');
                expect(component).not.toHaveClass('disabled');
              }
            } else if (propName === 'className') {
              expect(component).toHaveClass(value);
            }
            
            // Cleanup
            rerender(<></>);
          }
        });
      });
    }
  });
  
  // Conditional Props Tests
  describe('Conditional Props', () => {
    // Test interdependent props
    for (const scenario of conditionalProps) {
      it(`handles conditional scenario: ${ scenario.description } `, () => {
        render(<ComponentName {...scenario.props} data-testid="component" />);
        
        const component = screen.getByTestId('component');
        
        // Test expected outcomes
        for (const [selector, expectation] of scenario.expectations) {
          const element = selector === 'self' ? component : component.querySelector(selector);
          if (expectation.toBeInTheDocument) {
            expect(element).toBeInTheDocument();
          }
          if (expectation.toHaveClass) {
            expect(element).toHaveClass(expectation.toHaveClass);
          }
          if (expectation.toHaveAttribute) {
            for (const [attr, value] of Object.entries(expectation.toHaveAttribute)) {
              expect(element).toHaveAttribute(attr, value);
            }
          }
          if (expectation.toContainHTML) {
            expect(element).toContainHTML(expectation.toContainHTML);
          }
        }
      });
    }
  });
  
  // Edge Case Props Tests
  describe('Edge Case Props', () => {
    for (const testCase of edgeCaseProps) {
      it(`handles edge case: ${ testCase.description } `, () => {
        render(<ComponentName {...testCase.props} data-testid="component" />);
        
        const component = screen.getByTestId('component');
        
        // Test expected results for edge cases
        for (const assertion of testCase.assertions) {
          if (assertion.type === 'class') {
            assertion.positive 
              ? expect(component).toHaveClass(assertion.value)
              : expect(component).not.toHaveClass(assertion.value);
          } else if (assertion.type === 'attribute') {
            assertion.positive 
              ? expect(component).toHaveAttribute(assertion.name, assertion.value)
              : expect(component).not.toHaveAttribute(assertion.name);
          } else if (assertion.type === 'content') {
            assertion.positive 
              ? expect(component).toHaveTextContent(assertion.value)
              : expect(component).not.toHaveTextContent(assertion.value);
          }
        }
      });
    }
  });
  
  // Dynamic Props Generation Tests
  describe('Dynamic Props Generation', () => {
    it('handles dynamically generated props correctly', () => {
      // Generate 20 random test cases
      for (let i = 0; i < 20; i++) {
        const randomProps = {
          ...requiredProps,
          variant: ['primary', 'secondary', 'outline'][Math.floor(Math.random() * 3)],
          size: ['sm', 'md', 'lg'][Math.floor(Math.random() * 3)],
          disabled: Math.random() > 0.5,
          className: `random - class- ${ i } `,
          'data-testid': 'component',
        };
        
        const { unmount } = render(<ComponentName {...randomProps} />);
        
        const component = screen.getByTestId('component');
        expect(component).toBeInTheDocument();
        expect(component).toHaveClass(`variant - ${ randomProps.variant } `);
        expect(component).toHaveClass(`size - ${ randomProps.size } `);
        expect(component).toHaveClass(`random - class- ${ i } `);
        
        if (randomProps.disabled) {
          expect(component).toHaveAttribute('disabled');
        } else {
          expect(component).not.toHaveAttribute('disabled');
        }
        
        unmount();
      }
    });
  });
});
```

#### ** State and Lifecycle Test Implementation **

  ```tsx
// src/components/[ComponentName]/__tests__/unit/ComponentName.state.test.tsx

import { render, screen, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import ComponentName from '../../ComponentName';
import { stateFixtures } from '../../__fixtures__/ComponentName.fixtures';

jest.useFakeTimers();

describe('ComponentName - State Management Tests', () => {
  // Internal State Initialization
  describe('Initial State', () => {
    it('initializes with the correct default state', () => {
      render(<ComponentName {...stateFixtures.default.props} data-testid="component" />);
      
      const component = screen.getByTestId('component');
      
      // Test initial state indicators (classes, attributes, content)
      expect(component).toHaveClass('state-initial');
      expect(component).not.toHaveClass('state-active');
      expect(component).not.toHaveClass('state-complete');
    });
    
    it('initializes with custom initial state when provided', () => {
      render(<ComponentName {...stateFixtures.customInitial.props} data-testid="component" />);
      
      const component = screen.getByTestId('component');
      
      // Test custom initial state
      expect(component).toHaveClass('state-active');
    });
  });
  
  // State Transitions
  describe('State Transitions', () => {
    for (const transition of stateFixtures.transitions) {
      it(`transitions from ${ transition.from } to ${ transition.to } when ${ transition.trigger } `, async () => {
        const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
        
        render(<ComponentName {...transition.props} data-testid="component" />);
        const component = screen.getByTestId('component');
        
        // Verify initial state
        expect(component).toHaveClass(`state - ${ transition.from } `);
        
        // Trigger state change
        if (transition.triggerType === 'click') {
          const target = transition.triggerTarget === 'self' 
            ? component 
            : component.querySelector(transition.triggerTarget);
          await user.click(target);
        } else if (transition.triggerType === 'hover') {
          await user.hover(component);
        } else if (transition.triggerType === 'focus') {
          await user.tab();
          expect(component).toHaveFocus();
        } else if (transition.triggerType === 'input') {
          const input = component.querySelector('input');
          await user.type(input, transition.triggerValue);
        } else if (transition.triggerType === 'timeout') {
          // Advance timers for timeout-based transitions
          act(() => {
            jest.advanceTimersByTime(transition.timeout);
          });
        }
        
        // Test state after transition
        expect(component).toHaveClass(`state - ${ transition.to } `);
        expect(component).not.toHaveClass(`state - ${ transition.from } `);
        
        // Test additional state indicators
        for (const assertion of transition.assertions) {
          if (assertion.type === 'class') {
            assertion.positive 
              ? expect(component).toHaveClass(assertion.value)
              : expect(component).not.toHaveClass(assertion.value);
          } else if (assertion.type === 'attribute') {
            assertion.positive 
              ? expect(component).toHaveAttribute(assertion.name, assertion.value)
              : expect(component).not.toHaveAttribute(assertion.name);
          } else if (assertion.type === 'content') {
            assertion.positive 
              ? expect(component).toHaveTextContent(assertion.value)
              : expect(component).not.toHaveTextContent(assertion.value);
          }
        }
      });
    }
    
    // Test complex state transition sequences
    for (const sequence of stateFixtures.sequences) {
      it(`executes complex state sequence: ${ sequence.description } `, async () => {
        const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
        
        render(<ComponentName {...sequence.props} data-testid="component" />);
        const component = screen.getByTestId('component');
        
        // Execute each step in the sequence
        for (const step of sequence.steps) {
          // Verify current state before action
          expect(component).toHaveClass(`state - ${ step.expectedStateBefore } `);
          
          // Execute action
          if (step.action === 'click') {
            const target = step.target === 'self' 
              ? component 
              : component.querySelector(step.target);
            await user.click(target);
          } else if (step.action === 'hover') {
            await user.hover(component);
          } else if (step.action === 'unhover') {
            await user.unhover(component);
          } else if (step.action === 'focus') {
            component.focus();
          } else if (step.action === 'blur') {
            component.blur();
          } else if (step.action === 'input') {
            const input = component.querySelector('input');
            await user.type(input, step.value);
          } else if (step.action === 'wait') {
            act(() => {
              jest.advanceTimersByTime(step.duration);
            });
          }
          
          // Verify state after action
          expect(component).toHaveClass(`state - ${ step.expectedStateAfter } `);
          
          // Additional assertions
          if (step.assertions) {
            for (const assertion of step.assertions) {
              const target = assertion.target === 'self' 
                ? component 
                : component.querySelector(assertion.target);
              
              if (assertion.type === 'class') {
                assertion.positive 
                  ? expect(target).toHaveClass(assertion.value)
                  : expect(target).not.toHaveClass(assertion.value);
              } else if (assertion.type === 'attribute') {
                assertion.positive 
                  ? expect(target).toHaveAttribute(assertion.name, assertion.value)
                  : expect(target).not.toHaveAttribute(assertion.name);
              } else if (assertion.type === 'content') {
                assertion.positive 
                  ? expect(target).toHaveTextContent(assertion.value)
                  : expect(target).not.toHaveTextContent(assertion.value);
              } else if (assertion.type === 'visible') {
                assertion.positive 
                  ? expect(target).toBeVisible()
                  : expect(target).not.toBeVisible();
              }
            }
          }
        }
      });
    }
  });
  
  // Controlled vs Uncontrolled State
  describe('Controlled vs Uncontrolled State', () => {
    it('functions correctly in uncontrolled mode', async () => {
      const user = userEvent.setup();
      
      render(<ComponentName {...stateFixtures.uncontrolled.props} data-testid="component" />);
      const component = screen.getByTestId('component');
      
      // Test initial state
      expect(component).toHaveClass('state-initial');
      
      // Trigger state change
      await user.click(component);
      
      // State should change in uncontrolled mode
      expect(component).toHaveClass('state-active');
    });
    
    it('functions correctly in controlled mode', async () => {
      const user = userEvent.setup();
      const onChangeMock = jest.fn();
      
      const { rerender } = render(
        <ComponentName 
          {...stateFixtures.controlled.props} 
          state="initial"
          onChange={onChangeMock}
          data-testid="component" 
        />
      );
      
      const component = screen.getByTestId('component');
      
      // Test initial controlled state
      expect(component).toHaveClass('state-initial');
      
      // Trigger event that would change state
      await user.click(component);
      
      // State should not change automatically in controlled mode
      expect(component).toHaveClass('state-initial');
      
      // But onChange should have been called
      expect(onChangeMock).toHaveBeenCalledWith('active');
      
      // Simulate parent updating the state prop
      rerender(
        <ComponentName 
          {...stateFixtures.controlled.props} 
          state="active"
          onChange={onChangeMock}
          data-testid="component" 
        />
      );
      
      // Now state should reflect the new prop value
      expect(component).toHaveClass('state-active');
    });
    
    // Test for prop updates overriding internal state
    it('controlled props override internal state when changed', async () => {
      const user = userEvent.setup();
      const onChangeMock = jest.fn();
      
      const { rerender } = render(
        <ComponentName 
          {...stateFixtures.controlled.props} 
          state="initial"
          onChange={onChangeMock}
          data-testid="component" 
        />
      );
      
      const component = screen.getByTestId('component');
      
      // Change to a different state prop
      rerender(
        <ComponentName 
          {...stateFixtures.controlled.props} 
          state="complete"
          onChange={onChangeMock}
          data-testid="component" 
        />
      );
      
      // Component should reflect the new state
      expect(component).toHaveClass('state-complete');
      
      // Clicking should not change state in controlled mode
      await user.click(component);
      expect(component).toHaveClass('state-complete');
      
      // But should fire onChange
      expect(onChangeMock).toHaveBeenCalledWith('active');
    });
  });
  
  // Lifecycle Tests
  describe('Component Lifecycle', () => {
    it('handles mounting correctly', () => {
      const onMountMock = jest.fn();
      
      render(
        <ComponentName 
          {...stateFixtures.lifecycle.props}
          onMount={onMountMock}
          data-testid="component" 
        />
      );
      
      // onMount should be called once
      expect(onMountMock).toHaveBeenCalledTimes(1);
    });
    
    it('handles updates correctly', () => {
      const onUpdateMock = jest.fn();
      
      const { rerender } = render(
        <ComponentName 
          {...stateFixtures.lifecycle.props}
          value="initial"
          onUpdate={onUpdateMock}
          data-testid="component" 
        />
      );
      
      // First render doesn't trigger onUpdate
      expect(onUpdateMock).not.toHaveBeenCalled();
      
      // Update prop to trigger re-render
      rerender(
        <ComponentName 
          {...stateFixtures.lifecycle.props}
          value="updated"
          onUpdate={onUpdateMock}
          data-testid="component" 
        />
      );
      
      // onUpdate should be called on prop change
      expect(onUpdateMock).toHaveBeenCalledTimes(1);
      expect(onUpdateMock).toHaveBeenCalledWith('initial', 'updated');
    });
    
    it('handles unmounting correctly', () => {
      const onUnmountMock = jest.fn();
      
      const { unmount } = render(
        <ComponentName 
          {...stateFixtures.lifecycle.props}
          onUnmount={onUnmountMock}
          data-testid="component" 
        />
      );
      
      // onUnmount shouldn't be called yet
      expect(onUnmountMock).not.toHaveBeenCalled();
      
      // Unmount component
      unmount();
      
      // onUnmount should be called once
      expect(onUnmountMock).toHaveBeenCalledTimes(1);
    });
    
    it('cleans up resources on unmount', () => {
      // Mock timers and listeners to check cleanup
      const clearTimeoutSpy = jest.spyOn(window, 'clearTimeout');
      const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
      
      const { unmount } = render(
        <ComponentName 
          {...stateFixtures.lifecycle.props}
          data-testid="component" 
        />
      );
      
      // Unmount to trigger cleanup
      unmount();
      
      // Verify cleanup calls
      expect(clearTimeoutSpy).toHaveBeenCalled();
      expect(removeEventListenerSpy).toHaveBeenCalled();
      
      // Restore original implementations
      clearTimeoutSpy.mockRestore();
      removeEventListenerSpy.mockRestore();
    });
  });
  
  // Effect Dependencies Tests
  describe('Effect Dependencies', () => {
    it('re-runs effects when critical dependencies change', () => {
      const effectMock = jest.fn();
      
      // Mock useEffect to track calls
      jest.spyOn(React, 'useEffect').mockImplementation((callback, deps) => {
        if (deps && deps.includes('criticalProp')) {
          effectMock();
          callback();
        }
      });
      
      const { rerender } = render(
        <ComponentName 
          {...stateFixtures.effects.props}
          criticalProp="initial"
          data-testid="component" 
        />
      );
      
      // Initial effect run
      expect(effectMock).toHaveBeenCalledTimes(1);
      
      // Rerender with same prop
      rerender(
        <ComponentName 
          {...stateFixtures.effects.props}
          criticalProp="initial"
          data-testid="component" 
        />
      );
      
      // Effect should not run again with same prop
      expect(effectMock).toHaveBeenCalledTimes(1);
      
      // Rerender with different prop
      rerender(
        <ComponentName 
          {...stateFixtures.effects.props}
          criticalProp="changed"
          data-testid="component" 
        />
      );
      
      // Effect should run again with changed prop
      expect(effectMock).toHaveBeenCalledTimes(2);
      
      // Restore original implementation
      React.useEffect.mockRestore();
    });
    
    it('ignores non-dependency prop changes', () => {
      const effectMock = jest.fn();
      
      // Mock useEffect to track calls
      jest.spyOn(React, 'useEffect').mockImplementation((callback, deps) => {
        if (deps && !deps.includes('nonCriticalProp')) {
          effectMock();
          callback();
        }
      });
      
      const { rerender } = render(
        <ComponentName 
          {...stateFixtures.effects.props}
          nonCriticalProp="initial"
          data-testid="component" 
        />
      );
      
      const initialCallCount = effectMock.mock.calls.length;
      
      // Rerender with different non-critical prop
      rerender(
        <ComponentName 
          {...stateFixtures.effects.props}
          nonCriticalProp="changed"
          data-testid="component" 
        />
      );
      
      // Effect should not run again for non-critical prop
      expect(effectMock).toHaveBeenCalledTimes(initialCallCount);
      
      // Restore original implementation
      React.useEffect.mockRestore();
    });
  });
});
```

#### ** Advanced Responsive Testing **

  ```tsx
// src/components/[ComponentName]/__tests__/responsive/ComponentName.responsive.test.tsx

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import mediaQuery from 'css-mediaquery';

import ComponentName from '../../ComponentName';
import { responsiveFixtures } from '../../__fixtures__/ComponentName.fixtures';

// Create custom matcher for media queries
function createMatchMedia(width) {
  return (query) => ({
    matches: mediaQuery.match(query, { width }),
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  });
}

describe('ComponentName - Responsive Tests', () => {
  // Define viewport sizes to test
  const viewports = [
    { name: 'mobile', width: 375 },
    { name: 'tablet', width: 768 },
    { name: 'desktop', width: 1024 },
    { name: 'widescreen', width: 1440 },
  ];
  
  beforeAll(() => {
    // Save original matchMedia
    window.originalMatchMedia = window.matchMedia;
  });
  
  afterAll(() => {
    // Restore original matchMedia
    window.matchMedia = window.originalMatchMedia;
  });
  
  // Basic Responsive Rendering
  describe('Responsive Rendering', () => {
    viewports.forEach(viewport => {
      describe(`${ viewport.name } viewport(${ viewport.width }px)`, () => {
        beforeEach(() => {
          // Set viewport size for this test
          window.matchMedia = createMatchMedia(viewport.width);
          window.innerWidth = viewport.width;
          window.innerHeight = viewport.width === 375 ? 667 : viewport.width === 768 ? 1024 : 900;
          window.dispatchEvent(new Event('resize'));
        });
        
        it('renders with correct layout classes', () => {
          render(<ComponentName {...responsiveFixtures.basic.props} data-testid="component" />);
          
          const component = screen.getByTestId('component');
          
          // Test viewport-specific classes
          if (viewport.width < 768) {
            expect(component).toHaveClass('mobile-layout');
            expect(component).not.toHaveClass('desktop-layout');
          } else if (viewport.width >= 768 && viewport.width < 1024) {
            expect(component).toHaveClass('tablet-layout');
            expect(component).not.toHaveClass('mobile-layout');
            expect(component).not.toHaveClass('desktop-layout');
          } else {
            expect(component).toHaveClass('desktop-layout');
            expect(component).not.toHaveClass('mobile-layout');
          }
        });
        
        it('displays correct content for viewport', () => {
          render(<ComponentName {...responsiveFixtures.content.props} data-testid="component" />);
          
          // Test viewport-specific content visibility
          if (viewport.width < 768) {
            expect(screen.queryByTestId('desktop-only')).not.toBeInTheDocument();
            expect(screen.getByTestId('mobile-only')).toBeInTheDocument();
          } else if (viewport.width >= 1024) {
            expect(screen.getByTestId('desktop-only')).toBeInTheDocument();
            expect(screen.queryByTestId('mobile-only')).not.toBeInTheDocument();
          }
        });
        
        it('applies correct sizing for viewport', () => {
          render(<ComponentName {...responsiveFixtures.sizing.props} data-testid="component" />);
          
          const component = screen.getByTestId('component');
          
          // Get computed styles
          const styles = window.getComputedStyle(component);
          
          // Test viewport-specific styling
          if (viewport.width < 768) {
            expect(styles.fontSize).toBe('14px');
            expect(styles.padding).toBe('8px 12px');
          } else if (viewport.width >= 768 && viewport.width < 1024) {
            expect(styles.fontSize).toBe('16px');
            expect(styles.padding).toBe('12px 16px');
          } else {
            expect(styles.fontSize).toBe('18px');
            expect(styles.padding).toBe('16px 24px');
          }
        });
      });
    });
  });
  
  // Advanced Responsive Behavior Tests
  describe('Responsive Behavior', () => {
    viewports.forEach(viewport => {
      describe(`${ viewport.name } viewport(${ viewport.width }px)`, () => {
        beforeEach(() => {
          // Set viewport size for this test
          window.matchMedia = createMatchMedia(viewport.width);
          window.innerWidth = viewport.width;
          window.innerHeight = viewport.width === 375 ? 667 : viewport.width === 768 ? 1024 : 900;
          window.dispatchEvent(new Event('resize'));
        });
        
        it('handles interactions correctly for viewport size', async () => {
          const user = userEvent.setup();
          const onClickMock = jest.fn();
          
          render(
            <ComponentName 
              {...responsiveFixtures.interaction.props} 
              onClick={onClickMock}
              data-testid="component" 
            />
          );
          
          const component = screen.getByTestId('component');
          
          // Test viewport-specific interactions
          await user.click(component);
          
          expect(onClickMock).toHaveBeenCalled();
          
          // Different behavior expected on mobile vs desktop
          if (viewport.width < 768) {
            // Mobile-specific behavior assertions
            expect(component).toHaveClass('mobile-interaction');
            expect(screen.getByTestId('mobile-menu')).toBeVisible();
          } else {
            // Desktop-specific behavior assertions
            expect(component).toHaveClass('desktop-interaction');
            expect(screen.getByTestId('desktop-menu')).toBeVisible();
          }
        });
        
        it('lazy loads content based on viewport', async () => {
          // Mock fetch for testing lazy loading
          global.fetch = jest.fn().mockImplementation(() => 
            Promise.resolve({
              ok: true,
              json: () => Promise.resolve({ data: 'Lazy loaded content' }),
            })
          );
          
          render(<ComponentName {...responsiveFixtures.lazyLoad.props} data-testid="component" />);
          
          const component = screen.getByTestId('component');
          
          // Different lazy loading behavior by viewport
          if (viewport.width < 768) {
            // Mobile should lazy load immediately
            expect(fetch).toHaveBeenCalledTimes(1);
            expect(await screen.findByText('Lazy loaded content')).toBeInTheDocument();
          } else if (viewport.width >= 1024) {
            // Desktop should preload
            expect(fetch).toHaveBeenCalledTimes(1);
            expect(component).toHaveAttribute('data-preloaded', 'true');
          }
          
          // Restore fetch
          global.fetch.mockRestore();
        });
        
        it('handles orientation changes correctly', () => {
          render(<ComponentName {...responsiveFixtures.orientation.props} data-testid="component" />);
          
          const component = screen.getByTestId('component');
          
          // Test portrait orientation
          Object.defineProperty(window, 'orientation', { value: 0 });
          window.dispatchEvent(new Event('orientationchange'));
          
          expect(component).toHaveClass('portrait');
          expect(component).not.toHaveClass('landscape');
          
          // Test landscape orientation
          Object.defineProperty(window, 'orientation', { value: 90 });
          window.dispatchEvent(new Event('orientationchange'));
          
          expect(component).toHaveClass('landscape');
          expect(component).not.toHaveClass('portrait');
        });
      });
    });
  });
  
  // Responsive Feature Detection Tests
  describe('Responsive Feature Detection', () => {
    it('detects touch support correctly', () => {
      // Mock touch capability
      const originalTouchPoints = navigator.maxTouchPoints;
      Object.defineProperty(navigator, 'maxTouchPoints', { value: 5 });
      
      render(<ComponentName {...responsiveFixtures.features.props} data-testid="component" />);
      
      const component = screen.getByTestId('component');
      expect(component).toHaveClass('touch-enabled');
      
      // Restore original value
      Object.defineProperty(navigator, 'maxTouchPoints', { value: originalTouchPoints });
    });
    
    it('handles high DPI displays correctly', () => {
      // Mock high DPI display
      const originalDevicePixelRatio = window.devicePixelRatio;
      Object.defineProperty(window, 'devicePixelRatio', { value: 2 });
      
      render(<ComponentName {...responsiveFixtures.features.props} data-testid="component" />);
      
      const component = screen.getByTestId('component');
      expect(component).toHaveClass('high-dpi');
      
      // Test image sources
      const image = component.querySelector('img');
      expect(image.src).toContain('2x');
      
      // Restore original value
      Object.defineProperty(window, 'devicePixelRatio', { value: originalDevicePixelRatio });
    });
    
    it('adapts to reduced motion preference', () => {
      // Mock prefers-reduced-motion
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query.includes('prefers-reduced-motion'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));
      
      render(<ComponentName {...responsiveFixtures.features.props} data-testid="component" />);
      
      const component = screen.getByTestId('component');
      expect(component).toHaveClass('reduced-motion');
      
      // Animation should be disabled
      const animatedElement = component.querySelector('.animation');
      expect(animatedElement).toHaveStyle('animation: none');
    });
  });
  
  // Responsive Performance Tests
  describe('Responsive Performance', () => {
    viewports.forEach(viewport => {
      describe(`${ viewport.name } viewport(${ viewport.width }px)`, () => {
        beforeEach(() => {
          window.matchMedia = createMatchMedia(viewport.width);
          window.innerWidth = viewport.width;
          window.dispatchEvent(new Event('resize'));
        });
        
        it('loads appropriate image sizes for viewport', () => {
          render(<ComponentName {...responsiveFixtures.performance.props} data-testid="component" />);
          
          const image = screen.getByTestId('responsive-image');
          
          if (viewport.width < 768) {
            expect(image.src).toContain('small.jpg');
            expect(image.width).toBe(375);
          } else if (viewport.width >= 768 && viewport.width < 1024) {
            expect(image.src).toContain('medium.jpg');
            expect(image.width).toBe(768);
          } else {
            expect(image.src).toContain('large.jpg');
            expect(image.width).toBe(1200);
          }
        });
        
        it('renders appropriate number of items for viewport', () => {
          render(<ComponentName {...responsiveFixtures.performance.props} data-testid="component" />);
          
          const items = screen.getAllByTestId(/item-/);
          
          if (viewport.width < 768) {
            expect(items.length).toBe(4); // Fewer items on mobile
          } else if (viewport.width >= 768 && viewport.width < 1024) {
            expect(items.length).toBe(8); // More on tablet
          } else {
            expect(items.length).toBe(12); // Most on desktop
          }
        });
      });
    });
  });
  
  // Responsive CSS Breakpoint Tests
  describe('CSS Breakpoint Integration', () => {
    // Test each standardized breakpoint
    const breakpoints = [
      { name: 'xs', width: 375, query: '(max-width: 575.98px)' },
      { name: 'sm', width: 576, query: '(min-width: 576px)' },
      { name: 'md', width: 768, query: '(min-width: 768px)' },
      { name: 'lg', width: 992, query: '(min-width: 992px)' },
      { name: 'xl', width: 1200, query: '(min-width: 1200px)' },
      { name: 'xxl', width: 1400, query: '(min-width: 1400px)' }
    ];
    
    breakpoints.forEach(breakpoint => {
      it(`applies correct styles at ${ breakpoint.name } breakpoint(${ breakpoint.width }px)`, () => {
        // Set up for this breakpoint
        window.matchMedia = jest.fn().mockImplementation(query => ({
          matches: query === breakpoint.query,
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        }));
        
        window.innerWidth = breakpoint.width;
        window.dispatchEvent(new Event('resize'));
        
        render(<ComponentName {...responsiveFixtures.breakpoints.props} data-testid="component" />);
        
        const component = screen.getByTestId('component');
        
        // Test breakpoint-specific classes
        expect(component).toHaveClass(`breakpoint - ${ breakpoint.name } `);
        
        // Test CSS custom properties
        const styles = window.getComputedStyle(component);
        expect(styles.getPropertyValue('--breakpoint')).toBe(breakpoint.name);
        
        // Test computed styles at this breakpoint
        if (breakpoint.name === 'xs' || breakpoint.name === 'sm') {
          expect(styles.flexDirection).toBe('column');
        } else {
          expect(styles.flexDirection).toBe('row');
        }
        
        if (breakpoint.name === 'xl' || breakpoint.name === 'xxl') {
          expect(styles.maxWidth).toBe('1140px');
        } else if (breakpoint.name === 'lg') {
          expect(styles.maxWidth).toBe('960px');
        } else if (breakpoint.name === 'md') {
          expect(styles.maxWidth).toBe('720px');
        } else {
          expect(styles.maxWidth).toBe('100%');
        }
      });
    });
  });
});
```

#### ** Performance Testing Implementation **

  ```tsx
// src/components/[ComponentName]/__tests__/performance/ComponentName.render.perf.test.tsx

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { PerformanceObserver } from 'perf_hooks';

import ComponentName from '../../ComponentName';
import { performanceFixtures } from '../../__fixtures__/ComponentName.fixtures';

// Setup performance measurement
let perfObserver;
let performanceEntries = [];

describe('ComponentName - Render Performance Tests', () => {
  beforeAll(() => {
    // Set up performance observer
    perfObserver = new PerformanceObserver((items) => {
      performanceEntries = performanceEntries.concat(items.getEntries());
    });
    perfObserver.observe({ entryTypes: ['measure', 'mark'] });
  });
  
  afterAll(() => {
    // Disconnect observer
    perfObserver.disconnect();
  });
  
  beforeEach(() => {
    // Clear entries before each test
    performanceEntries = [];
    // Clear existing marks
    performance.clearMarks();
    performance.clearMeasures();
  });
  
  // Initial Render Performance
  describe('Initial Render Performance', () => {
    it('renders within acceptable time threshold', () => {
      performance.mark('render-start');
      
      render(<ComponentName {...performanceFixtures.basic.props} data-testid="component" />);
      
      performance.mark('render-end');
      performance.measure('render-duration', 'render-start', 'render-end');
      
      const renderMeasure = performanceEntries.find(entry => entry.name === 'render-duration');
      
      // Assert render time is under threshold (e.g., 50ms)
      expect(renderMeasure.duration).toBeLessThan(50);
    });
    
    it('handles large datasets efficiently', () => {
      performance.mark('large-data-render-start');
      
      render(<ComponentName {...performanceFixtures.largeData.props} data-testid="component" />);
      
      performance.mark('large-data-render-end');
      performance.measure('large-data-render-duration', 'large-data-render-start', 'large-data-render-end');
      
      const renderMeasure = performanceEntries.find(entry => entry.name === 'large-data-render-duration');
      
      // Assert render time for large data is under threshold (e.g., 200ms)
      expect(renderMeasure.duration).toBeLessThan(200);
      
      // Verify all items rendered correctly
      const items = screen.getAllByTestId(/^item-/);
      expect(items.length).toBe(performanceFixtures.largeData.props.items.length);
    });
  });
  
  // Interaction Performance
  describe('Interaction Performance', () => {
    it('responds to user interactions within acceptable time', async () => {
      const user = userEvent.setup();
      
      render(<ComponentName {...performanceFixtures.interaction.props} data-testid="component" />);
      
      const component = screen.getByTestId('component');
      const button = screen.getByRole('button');
      
      // Measure click response time
      performance.mark('click-start');
      await user.click(button);
      
      // Wait for state update
      await screen.findByTestId('updated-state');
      performance.mark('click-end');
      
      performance.measure('click-response-time', 'click-start', 'click-end');
      
      const clickMeasure = performanceEntries.find(entry => entry.name === 'click-response-time');
      
      // Assert interaction response time is under threshold (e.g., 100ms)
      expect(clickMeasure.duration).toBeLessThan(100);
    });
    
    it('maintains smooth animations', async () => {
      jest.useFakeTimers();
      
      render(<ComponentName {...performanceFixtures.animation.props} data-testid="component" />);
      
      const component = screen.getByTestId('component');
      
      // Start animation
      component.classList.add('animate');
      
      // Check frame rate during animation
      performance.mark('animation-start');
      
      // Simulate 1 second of animation (60 frames)
      for (let i = 0; i < 60; i++) {
        performance.mark(`frame - ${ i } -start`);
        // Move animation forward
        jest.advanceTimersByTime(16.67); // ~60fps
        performance.mark(`frame - ${ i } -end`);
        performance.measure(`frame - ${ i } -time`, `frame - ${ i } -start`, `frame - ${ i } -end`);
      }
      
      performance.mark('animation-end');
      performance.measure('animation-duration', 'animation-start', 'animation-end');
      
      // Check frame times
      const frameTimes = performanceEntries
        .filter(entry => entry.name.startsWith('frame-') && entry.name.endsWith('-time'))
        .map(entry => entry.duration);
      
      // Average frame time should be under threshold (e.g., 16.67ms for 60fps)
      const avgFrameTime = frameTimes.reduce((sum, time) => sum + time, 0) / frameTimes.length;
      expect(avgFrameTime).toBeLessThan(16.67);
      
      // Count "janky" frames (>20ms)
      const jankyFrames = frameTimes.filter(time => time > 20);
      expect(jankyFrames.length).toBeLessThan(5); // Allow a few janky frames
      
      jest.useRealTimers();
    });
  });
  
  // Re-render Performance
  describe('Re-render Performance', () => {
    it('efficiently handles prop changes', () => {
      const { rerender } = render(
        <ComponentName 
          {...performanceFixtures.rerender.props}
          value="initial" 
          data-testid="component" 
        />
      );
      
      // First render - establish baseline
      const component = screen.getByTestId('component');
      expect(component).toHaveTextContent('initial');
      
      // Measure re-render time
      performance.mark('rerender-start');
      
      rerender(
        <ComponentName 
          {...performanceFixtures.rerender.props}
          value="updated" 
          data-testid="component" 
        />
      );
      
      performance.mark('rerender-end');
      performance.measure('rerender-duration', 'rerender-start', 'rerender-end');
      
      const rerenderMeasure = performanceEntries.find(entry => entry.name === 'rerender-duration');
      
      // Assert re-render time is under threshold (e.g., 25ms)
      expect(rerenderMeasure.duration).toBeLessThan(25);
      
      // Verify content updated
      expect(component).toHaveTextContent('updated');
    });
    
    it('minimizes unnecessary re-renders', () => {
      // Setup re-render counter
      let renderCount = 0;
      
      // Mock React.memo to ensure it's used
      jest.spyOn(React, 'memo').mockImplementation((Component) => {
        const MemoizedComponent = (props) => {
          renderCount++;
          return <Component {...props} />;
        };
        return MemoizedComponent;
      });
      
      const { rerender } = render(
        <ComponentName 
          {...performanceFixtures.memoization.props}
          staticProp="static"
          dynamicProp="initial"
          data-testid="component" 
        />
      );
      
      // Reset counter after initial render
      renderCount = 0;
      
      // Re-render with same props
      rerender(
        <ComponentName 
          {...performanceFixtures.memoization.props}
          staticProp="static"
          dynamicProp="initial"
          data-testid="component" 
        />
      );
      
      // Should not have re-rendered with same props
      expect(renderCount).toBe(0);
      
      // Re-render with different dynamic prop
      rerender(
        <ComponentName 
          {...performanceFixtures.memoization.props}
          staticProp="static"
          dynamicProp="changed"
          data-testid="component" 
        />
      );
      
      // Should have re-rendered with different props
      expect(renderCount).toBe(1);
      
      // Restore original implementation
      React.memo.mockRestore();
    });
  });
  
  // Memory Usage Tests
  describe('Memory Usage', () => {
    it('manages memory efficiently during lifecycle', () => {
      // Track object allocations
      const objects = [];
      global.ObjectTracker = {
        track: (obj) => {
          objects.push(obj);
          return obj;
        }
      };
      
      // Replace component's object creation with tracked versions
      jest.spyOn(global, 'Object').mockImplementation((...args) => {
        const obj = Object.call(this, ...args);
        return global.ObjectTracker.track(obj);
      });
      
      const { unmount } = render(
        <ComponentName 
          {...performanceFixtures.memory.props}
          data-testid="component" 
        />
      );
      
      // Track objects before unmount
      const initialObjectCount = objects.length;
      
      // Unmount to test cleanup
      unmount();
      
      // Get current heap snapshot
      // In a real environment, this would use performance.memory or node heap snapshot
      // For this test simulation, we'll check that our mocked counters are stable
      
      // Objects created during render should be eligible for GC after unmount
      // We'll simulate a GC by checking if tracked object references are explicitly nulled
      
      const leakedObjectCount = objects.filter(obj => 
        obj && Object.keys(obj).length > 0
      ).length;
      
      // Should have cleaned up most objects
      expect(leakedObjectCount).toBeLessThan(initialObjectCount * 0.1);
      
      // Restore original implementation
      global.Object.mockRestore();
      delete global.ObjectTracker;
    });
    
    it('maintains stable memory usage with dynamic content', () => {
      // Simulate memory profiling
      const memoryUsage = [];
      
      // Setup mock for performance.memory
      Object.defineProperty(performance, 'memory', {
        value: {
          usedJSHeapSize: 0,
          totalJSHeapSize: 100000000,
          jsHeapSizeLimit: 200000000
        },
        configurable: true
      });
      
      const { rerender } = render(
        <ComponentName 
          {...performanceFixtures.memory.props}
          items={[]}
          data-testid="component" 
        />
      );
      
      // Record initial memory
      memoryUsage.push(performance.memory.usedJSHeapSize);
      
      // Add items in batches and check memory growth
      const batches = [10, 100, 1000];
      
      for (const batchSize of batches) {
        // Update mock memory usage - simulating realistic growth
        performance.memory.usedJSHeapSize += batchSize * 100; // 100 bytes per item
        
        const items = Array.from({ length: batchSize }).map((_, i) => ({
          id: i,
          value: `Item ${ i } `
        }));
        
        rerender(
          <ComponentName 
            {...performanceFixtures.memory.props}
            items={items}
            data-testid="component" 
          />
        );
        
        // Record memory after batch
        memoryUsage.push(performance.memory.usedJSHeapSize);
      }
      
      // Calculate memory growth rate
      const growthRates = [];
      for (let i = 1; i < memoryUsage.length; i++) {
        const prevBatchSize = i === 1 ? 0 : batches[i-2];
        const currentBatchSize = batches[i-1];
        const itemDiff = currentBatchSize - prevBatchSize;
        const memoryDiff = memoryUsage[i] - memoryUsage[i-1];
        growthRates.push(memoryDiff / itemDiff);
      }
      
      // Average bytes per item should be stable (linear growth)
      const avgGrowthRate = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
      
      // Check if growth rates are relatively consistent (not exponential)
      const maxDeviation = 0.5; // 50% deviation allowed
      const consistentGrowth = growthRates.every(rate => 
        Math.abs(rate - avgGrowthRate) / avgGrowthRate < maxDeviation
      );
      
      expect(consistentGrowth).toBe(true);
      
      // Restore original implementation
      delete performance.memory;
    });
  });
});
```

### ** Test Case Generation Framework **

  To achieve 100 % test coverage, implement this systematic test matrix generation approach:

```tsx
// src/components/[ComponentName]/__tests__/utils/testMatrixGenerator.ts

/**
 * Generates a comprehensive test matrix for component props
 * to ensure 100% test coverage of all valid combinations
 */
export function generateTestMatrix(component) {
  const propDefinitions = component.propTypes || {};
  const defaultProps = component.defaultProps || {};
  
  // Generate all possible values for each prop
  const propValues = {};
  
  Object.keys(propDefinitions).forEach(propName => {
    const propType = propDefinitions[propName];
    
    // Generate test values based on prop type
    if (propType === PropTypes.string) {
      propValues[propName] = ['', 'test', 'Special$Chars', '很長的字符串'];
    } else if (propType === PropTypes.number) {
      propValues[propName] = [0, 1, -1, 9999, -9999, 0.5];
    } else if (propType === PropTypes.bool) {
      propValues[propName] = [true, false];
    } else if (propType === PropTypes.oneOf) {
      // Extract allowed values from oneOf validator
      const allowedValues = propType._values || [];
      propValues[propName] = allowedValues;
    } else if (propType === PropTypes.array) {
      propValues[propName] = [[], [1, 2, 3], Array(100).fill(1)];
    } else if (propType === PropTypes.object) {
      propValues[propName] = [{}, { key: 'value' }, { nested: { deep: true } }];
    } else if (propType === PropTypes.func) {
      propValues[propName] = [jest.fn()];
    }
    
    // Add default value if available
    if (defaultProps[propName] !== undefined && !propValues[propName].includes(defaultProps[propName])) {
      propValues[propName].unshift(defaultProps[propName]);
    }
  });
  
  // Generate test combinations
  // Full cartesian product would be too many tests, so we'll use pairwise testing
  return generatePairwiseTests(propValues);
}

/**
 * Generates pairwise test combinations that ensure every pair
 * of prop values is tested at least once (PICT algorithm)
 */
function generatePairwiseTests(propValues) {
  // This is a simplified version - a real implementation would use
  // an algorithm like PICT (Pairwise Independent Combinatorial Testing)
  
  const propNames = Object.keys(propValues);
  const testCases = [];
  
  // Start with all default values
  const defaultCase = {};
  propNames.forEach(propName => {
    defaultCase[propName] = propValues[propName][0];
  });
  testCases.push(defaultCase);
  
  // Add cases for each prop value
  propNames.forEach(propName => {
    propValues[propName].forEach((value, i) => {
      if (i === 0) return; // Skip default value, already covered
      
      const testCase = { ...defaultCase };
      testCase[propName] = value;
      testCases.push(testCase);
    });
  });
  
  // Add cases for each pair of props
  for (let i = 0; i < propNames.length; i++) {
    for (let j = i + 1; j < propNames.length; j++) {
      const prop1 = propNames[i];
      const prop2 = propNames[j];
      
      // Skip default combinations (already covered)
      for (let vi = 1; vi < propValues[prop1].length; vi++) {
        for (let vj = 1; vj < propValues[prop2].length; vj++) {
          const testCase = { ...defaultCase };
          testCase[prop1] = propValues[prop1][vi];
          testCase[prop2] = propValues[prop2][vj];
          testCases.push(testCase);
        }
      }
    }
  }
  
  return testCases;
}
```

### ** Enterprise Security Testing Framework **

  ```tsx
// src/components/[ComponentName]/__tests__/security/ComponentName.security.test.tsx

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import DOMPurify from 'dompurify';

import ComponentName from '../../ComponentName';
import { securityFixtures } from '../../__fixtures__/ComponentName.fixtures';

// Mock DOMPurify to test it's being used
jest.mock('dompurify', () => ({
  sanitize: jest.fn(content => content),
}));

describe('ComponentName - Security Tests', () => {
  // Reset mocks
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  // XSS Protection
  describe('XSS Protection', () => {
    it('sanitizes potentially unsafe HTML content', () => {
      render(
        <ComponentName 
          {...securityFixtures.xss.props}
          content={securityFixtures.xss.unsafeContent}
          data-testid="component" 
        />
      );
      
      // Verify DOMPurify was called
      expect(DOMPurify.sanitize).toHaveBeenCalledWith(securityFixtures.xss.unsafeContent);
      
      // Verify script tags are not executed
      const component = screen.getByTestId('component');
      expect(component.querySelector('script')).toBeNull();
    });
    
    it('safely displays user-generated content', () => {
      // Test with known XSS attack vectors
      const xssVectors = [
        '<img src="x" onerror="alert(1)">',
        '<svg onload="alert(1)">',
        '"><script>alert(1)</script>',
        'javascript:alert(1)',
        '<div onclick="alert(1)">Click me</div>'
      ];
      
      for (const vector of xssVectors) {
        const { unmount } = render(
          <ComponentName 
            {...securityFixtures.xss.props}
            content={vector}
            data-testid="component" 
          />
        );
        
        const component = screen.getByTestId('component');
        
        // Check that no event handlers were rendered
        expect(component.innerHTML).not.toMatch(/on\w+=/);
        expect(component.innerHTML).not.toMatch(/javascript:/);
        
        // Check that potentially dangerous tags are removed
        expect(component.querySelector('script')).toBeNull();
        expect(component.querySelector('object')).toBeNull();
        expect(component.querySelector('embed')).toBeNull();
        
        unmount();
      }
    });
  });
  
  // Input Validation
  describe('Input Validation', () => {
    it('validates numeric inputs correctly', async () => {
      const user = userEvent.setup();
      const onChangeMock = jest.fn();
      
      render(
        <ComponentName 
          {...securityFixtures.input.props}
          type="numeric"
          onChange={onChangeMock}
          data-testid="component" 
        />
      );
      
      const input = screen.getByRole('textbox');
      
      // Test valid input
      await user.type(input, '123');
      expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({
        target: expect.objectContaining({ value: '123' })
      }));
      
      // Clear and test invalid input
      await user.clear(input);
      await user.type(input, 'abc');
      
      // Invalid characters should be prevented or sanitized
      expect(input.value).not.toBe('abc');
      
      // Test edge cases
      await user.clear(input);
      await user.type(input, '-123.45');
      
      // Should handle negative numbers and decimals correctly if allowed
      if (securityFixtures.input.props.allowNegative && securityFixtures.input.props.allowDecimals) {
        expect(input.value).toBe('-123.45');
      } else if (securityFixtures.input.props.allowNegative) {
        expect(input.value).toBe('-12345');
      } else if (securityFixtures.input.props.allowDecimals) {
        expect(input.value).toBe('123.45');
      } else {
        expect(input.value).toBe('12345');
      }
    });
    
    it('validates email inputs correctly', async () => {
      const user = userEvent.setup();
      const onChangeMock = jest.fn();
      
      render(
        <ComponentName 
          {...securityFixtures.input.props}
          type="email"
          onChange={onChangeMock}
          data-testid="component" 
        />
      );
      
      const input = screen.getByRole('textbox');
      
      // Test valid emails
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];
      
      for (const email of validEmails) {
        await user.clear(input);
        await user.type(input, email);
        
        // Test validation UI state
        expect(input).toBeValid();
        expect(input).not.toHaveClass('error');
      }
      
      // Test invalid emails
      const invalidEmails = [
        'plainaddress',
        '@missingusername.com',
        'user@.com',
        'user@com',
        '<EMAIL>'
      ];
      
      for (const email of invalidEmails) {
        await user.clear(input);
        await user.type(input, email);
        
        // Test validation UI state
        expect(input).not.toBeValid();
        expect(input).toHaveClass('error');
      }
    });
    
    it('validates URL inputs correctly', async () => {
      const user = userEvent.setup();
      const onChangeMock = jest.fn();
      
      render(
        <ComponentName 
          {...securityFixtures.input.props}
          type="url"
          onChange={onChangeMock}
          data-testid="component" 
        />
      );
      
      const input = screen.getByRole('textbox');
      
      // Test valid URLs
      const validURLs = [
        'https://example.com',
        'http://sub.example.co.uk/path?query=value',
        'https://example.org:8080/index.html'
      ];
      
      for (const url of validURLs) {
        await user.clear(input);
        await user.type(input, url);
        
        // Test validation UI state
        expect(input).toBeValid();
        expect(input).not.toHaveClass('error');
      }
      
      // Test potentially unsafe URLs
      const unsafeURLs = [
        'javascript:alert(1)',
        'data:text/html;base64,PHNjcmlwdD5hbGVydCgxKTwvc2NyaXB0Pg==',
        'file:///etc/passwd'
      ];
      
      for (const url of unsafeURLs) {
        await user.clear(input);
        await user.type(input, url);
        
        // Non-http/https protocols should be rejected
        expect(input).not.toBeValid();
        expect(input).toHaveClass('error');
      }
    });
  });
  
  // CSRF Protection
  describe('CSRF Protection', () => {
    it('includes CSRF tokens in form submissions', async () => {
      const user = userEvent.setup();
      const onSubmitMock = jest.fn(e => e.preventDefault());
      
      render(
        <ComponentName 
          {...securityFixtures.csrf.props}
          onSubmit={onSubmitMock}
          csrfToken="test-csrf-token-123"
          data-testid="component" 
        />
      );
      
      // Check if CSRF token input exists
      const csrfInput = screen.getByTestId('csrf-token');
      expect(csrfInput).toHaveAttribute('value', 'test-csrf-token-123');
      expect(csrfInput).toHaveAttribute('type', 'hidden');
      
      // Submit form
      const submitButton = screen.getByRole('button', { name: /submit/i });
      await user.click(submitButton);
      
      // Verify token is included in submission
      expect(onSubmitMock).toHaveBeenCalled();
      const formData = new FormData(screen.getByTestId('component'));
      expect(formData.get('_csrf')).toBe('test-csrf-token-123');
    });
  });
  
  // Sensitive Data Handling
  describe('Sensitive Data Handling', () => {
    it('masks sensitive information correctly', () => {
      render(
        <ComponentName 
          {...securityFixtures.sensitive.props}
          sensitiveData="1234-5678-9012-3456"
          data-testid="component" 
        />
      );
      
      const component = screen.getByTestId('component');
      
      // Verify data is masked in the DOM
      expect(component).toHaveTextContent('****-****-****-3456');
      expect(component).not.toHaveTextContent('1234-5678-9012-3456');
      
      // Check data attributes don't expose sensitive data
      const allElements = component.querySelectorAll('*');
      allElements.forEach(element => {
        Array.from(element.attributes).forEach(attr => {
          expect(attr.value).not.toContain('1234-5678-9012');
        });
      });
    });
    
    it('clears sensitive data on unmount', () => {
      const { unmount } = render(
        <ComponentName 
          {...securityFixtures.sensitive.props}
          sensitiveData="sensitive-info-123"
          data-testid="component" 
        />
      );
      
      // Mock for window object to inspect properties
      const originalWindowProps = { ...window };
      const addedProps = [];
      
      // Monitor properties added to window
      const windowProxyHandler = {
        set(target, prop, value) {
          if (!originalWindowProps.hasOwnProperty(prop)) {
            addedProps.push(prop);
          }
          target[prop] = value;
          return true;
        }
      };
      
      // Apply proxy to window to track added properties
      window = new Proxy(window, windowProxyHandler);
      
      // Unmount to trigger cleanup
      unmount();
      
      // Check that no properties were left on window object
      addedProps.forEach(prop => {
        if (window[prop] && typeof window[prop] === 'string') {
          expect(window[prop]).not.toContain('sensitive-info-123');
        }
      });
      
      // Restore window
      window = originalWindowProps;
    });
  });
  
  // Prop Types Validation
  describe('PropTypes Validation', () => {
    // Save original console.error
    const originalConsoleError = console.error;
    
    beforeEach(() => {
      // Mock console.error to catch PropTypes warnings
      console.error = jest.fn();
    });
    
    afterEach(() => {
      // Restore console.error
      console.error = originalConsoleError;
    });
    
    it('validates required props correctly', () => {
      // Get required props from component
      const requiredProps = Object.entries(ComponentName.propTypes || {})
        .filter(([_, validator]) => validator.isRequired)
        .map(([propName]) => propName);
      
      // Test rendering without each required prop
      requiredProps.forEach(propName => {
        // Create props with all required props except the one being tested
        const allRequiredProps = requiredProps.reduce((props, name) => {
          props[name] = 'test-value';
          return props;
        }, {});
        
        delete allRequiredProps[propName];
        
        // Render with missing prop
        const { unmount } = render(<ComponentName {...allRequiredProps} />);
        
        // Should trigger PropType warning
        expect(console.error).toHaveBeenCalled();
        expect(console.error.mock.calls[0][0]).toContain(
          `The prop \`${propName}\` is marked as required`
        );

console.error.mockClear();
unmount();
      });
    });

it('validates prop types correctly', () => {
  // Test each prop with invalid type
  const propTypeTests = [
    { prop: 'stringProp', value: 42, expectedType: 'string' },
    { prop: 'numberProp', value: 'not-a-number', expectedType: 'number' },
    { prop: 'boolProp', value: 'not-a-boolean', expectedType: 'boolean' },
    { prop: 'objectProp', value: 'not-an-object', expectedType: 'object' },
    { prop: 'arrayProp', value: 'not-an-array', expectedType: 'array' },
    { prop: 'funcProp', value: 'not-a-function', expectedType: 'function' }
  ];

  propTypeTests.forEach(({ prop, value, expectedType }) => {
    if (!ComponentName.propTypes || !ComponentName.propTypes[prop]) {
      return; // Skip if prop doesn't exist
    }

    // Render with invalid prop type
    const { unmount } = render(
      <ComponentName
        {...securityFixtures.proptypes.props}
            [prop] = { value }
      />
        );

    // Should trigger PropType warning for incorrect type
    expect(console.error).toHaveBeenCalled();
    expect(console.error.mock.calls[0][0]).toContain(
      `Invalid prop \`${prop}\``
    );
    expect(console.error.mock.calls[0][0]).toContain(expectedType);

    console.error.mockClear();
    unmount();
  });
});

it('validates enum props correctly', () => {
  // Find enum props (oneOf validator)
  const enumProps = Object.entries(ComponentName.propTypes || {})
    .filter(([_, validator]) => validator && validator.toString().includes('oneOf'))
    .map(([propName]) => propName);

  enumProps.forEach(propName => {
    // Render with invalid enum value
    const { unmount } = render(
      <ComponentName
        {...securityFixtures.proptypes.props}
            [propName] = "invalid-enum-value"
      />
        );

    // Should trigger PropType warning for invalid value
    expect(console.error).toHaveBeenCalled();
    expect(console.error.mock.calls[0][0]).toContain(
      `Invalid prop \`${propName}\``
    );
    expect(console.error.mock.calls[0][0]).toContain('expected one of');

    console.error.mockClear();
    unmount();
  });
});
  });
});
```

### **Edge Cases and Fault Tolerance Testing**

```tsx
// src/components/[ComponentName]/__tests__/edge/ComponentName.edge.test.tsx

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import ComponentName from '../../ComponentName';
import { edgeCaseFixtures } from '../../__fixtures__/ComponentName.fixtures';

describe('ComponentName - Edge Case Tests', () => {
  // Empty/Null Data Tests
  describe('Empty and Null Data Handling', () => {
    it('handles empty string props gracefully', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.empty.props}
          label=""
          content=""
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should still render and not crash
      expect(component).toBeInTheDocument();

      // Check for fallback content if applicable
      if (edgeCaseFixtures.empty.expectFallback) {
        expect(component).toHaveTextContent(edgeCaseFixtures.empty.fallbackText);
      }
    });

    it('handles null props gracefully', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.null.props}
          label={null}
          content={null}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should still render and not crash
      expect(component).toBeInTheDocument();

      // Check for fallback content if applicable
      if (edgeCaseFixtures.null.expectFallback) {
        expect(component).toHaveTextContent(edgeCaseFixtures.null.fallbackText);
      }
    });

    it('handles undefined props gracefully', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.undefined.props}
          label={undefined}
          content={undefined}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should still render and not crash
      expect(component).toBeInTheDocument();

      // Check for fallback content if applicable
      if (edgeCaseFixtures.undefined.expectFallback) {
        expect(component).toHaveTextContent(edgeCaseFixtures.undefined.fallbackText);
      }
    });

    it('handles empty arrays gracefully', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.emptyArray.props}
          items={[]}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should still render and not crash
      expect(component).toBeInTheDocument();

      // Check for empty state message
      if (edgeCaseFixtures.emptyArray.expectEmptyMessage) {
        expect(component).toHaveTextContent(edgeCaseFixtures.emptyArray.emptyMessage);
      }

      // No list items should be rendered
      const items = component.querySelectorAll('[data-testid^="item-"]');
      expect(items.length).toBe(0);
    });

    it('handles empty objects gracefully', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.emptyObject.props}
          data={{}}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should still render and not crash
      expect(component).toBeInTheDocument();

      // Check for empty state
      if (edgeCaseFixtures.emptyObject.expectEmptyState) {
        expect(component).toHaveClass('empty-state');
      }
    });
  });

  // Boundary Value Tests
  describe('Boundary Values', () => {
    it('handles minimum values correctly', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.boundaries.props}
          value={edgeCaseFixtures.boundaries.min}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should render correctly with min value
      expect(component).toHaveTextContent(String(edgeCaseFixtures.boundaries.min));
      expect(component).not.toHaveClass('error');
    });

    it('handles maximum values correctly', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.boundaries.props}
          value={edgeCaseFixtures.boundaries.max}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should render correctly with max value
      expect(component).toHaveTextContent(String(edgeCaseFixtures.boundaries.max));
      expect(component).not.toHaveClass('error');
    });

    it('handles out-of-bounds values correctly', () => {
      // Test below minimum
      const { rerender } = render(
        <ComponentName
          {...edgeCaseFixtures.boundaries.props}
          value={edgeCaseFixtures.boundaries.min - 1}
          data-testid="component"
        />
      );

      let component = screen.getByTestId('component');

      // Should handle below-min value appropriately
      if (edgeCaseFixtures.boundaries.clampValues) {
        expect(component).toHaveTextContent(String(edgeCaseFixtures.boundaries.min));
      } else {
        expect(component).toHaveClass('out-of-bounds');
        expect(component).toHaveClass('below-min');
      }

      // Test above maximum
      rerender(
        <ComponentName
          {...edgeCaseFixtures.boundaries.props}
          value={edgeCaseFixtures.boundaries.max + 1}
          data-testid="component"
        />
      );

      component = screen.getByTestId('component');

      // Should handle above-max value appropriately
      if (edgeCaseFixtures.boundaries.clampValues) {
        expect(component).toHaveTextContent(String(edgeCaseFixtures.boundaries.max));
      } else {
        expect(component).toHaveClass('out-of-bounds');
        expect(component).toHaveClass('above-max');
      }
    });

    it('handles extreme values correctly', () => {
      // Test extremely large numbers
      const { rerender } = render(
        <ComponentName
          {...edgeCaseFixtures.extremes.props}
          value={Number.MAX_SAFE_INTEGER}
          data-testid="component"
        />
      );

      let component = screen.getByTestId('component');

      // Should not break with extremely large numbers
      expect(component).toBeInTheDocument();
      expect(component).not.toHaveClass('error');

      // Test with maximum array size
      const largeArray = new Array(10000).fill('item');

      rerender(
        <ComponentName
          {...edgeCaseFixtures.extremes.props}
          items={largeArray}
          data-testid="component"
        />
      );

      component = screen.getByTestId('component');

      // Should handle large arrays without crashing
      expect(component).toBeInTheDocument();

      // Test with Infinity
      rerender(
        <ComponentName
          {...edgeCaseFixtures.extremes.props}
          value={Infinity}
          data-testid="component"
        />
      );

      component = screen.getByTestId('component');

      // Should handle Infinity appropriately
      expect(component).toBeInTheDocument();
      if (edgeCaseFixtures.extremes.handleSpecialNumbers) {
        expect(component).toHaveTextContent('∞');
      } else {
        expect(component).toHaveClass('invalid-number');
      }

      // Test with NaN
      rerender(
        <ComponentName
          {...edgeCaseFixtures.extremes.props}
          value={NaN}
          data-testid="component"
        />
      );

      component = screen.getByTestId('component');

      // Should handle NaN appropriately
      expect(component).toBeInTheDocument();
      if (edgeCaseFixtures.extremes.handleSpecialNumbers) {
        expect(component).toHaveTextContent('Not a number');
      } else {
        expect(component).toHaveClass('invalid-number');
      }
    });
  });

  // Special Character Tests
  describe('Special Characters', () => {
    it('renders special characters correctly', () => {
      const specialChars = [
        '&<>"\'/',
        '©®™',
        '☺★♠♣♥♦',
        '你好，世界',
        'مرحبا بالعالم',
        'שלום עולם',
        'Здравствуй, мир',
        '🔥🚀🎉👍'
      ];

      for (const chars of specialChars) {
        const { unmount } = render(
          <ComponentName
            {...edgeCaseFixtures.specialChars.props}
            content={chars}
            data-testid="component"
          />
        );

        const component = screen.getByTestId('component');

        // Should render special characters correctly
        expect(component).toHaveTextContent(chars);

        unmount();
      }
    });

    it('handles HTML entities correctly', () => {
      const htmlEntities = [
        { input: '&lt;script&gt;', expected: '<script>' },
        { input: '&amp;', expected: '&' },
        { input: '&quot;quoted&quot;', expected: '"quoted"' },
        { input: '&apos;apostrophe&apos;', expected: "'apostrophe'" },
        { input: '&copy; 2023', expected: '© 2023' }
      ];

      for (const { input, expected } of htmlEntities) {
        const { unmount } = render(
          <ComponentName
            {...edgeCaseFixtures.htmlEntities.props}
            content={input}
            data-testid="component"
          />
        );

        const component = screen.getByTestId('component');

        // Should decode HTML entities correctly if decodeEntities is true
        if (edgeCaseFixtures.htmlEntities.decodeEntities) {
          expect(component.textContent).toBe(expected);
        } else {
          expect(component.textContent).toBe(input);
        }

        unmount();
      }
    });

    it('handles bi-directional text correctly', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.bidi.props}
          content="Hello العالم"
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should have appropriate dir attribute
      expect(component).toHaveAttribute('dir', 'auto');

      // Verify text is rendered correctly
      expect(component).toHaveTextContent('Hello العالم');
    });
  });

  // Error Handling Tests
  describe('Error Handling', () => {
    it('handles rendering errors gracefully', () => {
      // Mock console.error to prevent test output pollution
      const originalError = console.error;
      console.error = jest.fn();

      // Define props that would cause a rendering error
      const problematicProps = edgeCaseFixtures.renderError.props;

      // Use error boundary to catch rendering errors
      class TestErrorBoundary extends React.Component {
        constructor(props) {
          super(props);
          this.state = { hasError: false };
        }

        static getDerivedStateFromError(error) {
          return { hasError: true };
        }

        render() {
          if (this.state.hasError) {
            return <div data-testid="error-fallback">Error occurred</div>;
          }
          return this.props.children;
        }
      }

      render(
        <TestErrorBoundary>
          <ComponentName {...problematicProps} data-testid="component" />
        </TestErrorBoundary>
      );

      // Should render error fallback instead of crashing
      expect(screen.getByTestId('error-fallback')).toBeInTheDocument();
      expect(screen.queryByTestId('component')).not.toBeInTheDocument();

      // Restore console.error
      console.error = originalError;
    });

    it('provides useful error messages', () => {
      // Mock console.error to capture error messages
      const originalError = console.error;
      console.error = jest.fn();

      // Render with props that would cause validation errors
      const { unmount } = render(
        <ComponentName
          {...edgeCaseFixtures.validationError.props}
          data-testid="component"
        />
      );

      // Verify error messages are helpful
      expect(console.error).toHaveBeenCalled();
      const errorMessages = console.error.mock.calls.flat().join(' ');

      // Error should mention the specific prop and expected type
      if (edgeCaseFixtures.validationError.expectedPropErrors) {
        for (const propError of edgeCaseFixtures.validationError.expectedPropErrors) {
          expect(errorMessages).toContain(propError.prop);
          expect(errorMessages).toContain(propError.expectedType);
        }
      }

      // Restore console.error
      console.error = originalError;
      unmount();
    });

    it('fails gracefully when API calls fail', async () => {
      // Mock fetch to simulate API failure
      global.fetch = jest.fn(() =>
        Promise.reject(new Error('API call failed'))
      );

      render(
        <ComponentName
          {...edgeCaseFixtures.apiError.props}
          data-testid="component"
        />
      );

      // Should show loading state initially
      expect(screen.getByTestId('component')).toHaveClass('loading');

      // Should transition to error state
      await screen.findByTestId('error-message');

      const component = screen.getByTestId('component');
      const errorMessage = screen.getByTestId('error-message');

      // Should show appropriate error UI
      expect(component).toHaveClass('error');
      expect(errorMessage).toHaveTextContent(edgeCaseFixtures.apiError.expectedErrorMessage);

      // Should show retry button if applicable
      if (edgeCaseFixtures.apiError.hasRetryButton) {
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
      }

      // Restore fetch
      global.fetch.mockRestore();
    });
  });

  // Timeout & Network Conditions Tests
  describe('Timeouts and Network Conditions', () => {
    it('handles slow network conditions gracefully', async () => {
      jest.useFakeTimers();

      // Mock fetch to delay response
      global.fetch = jest.fn(() =>
        new Promise(resolve =>
          setTimeout(() =>
            resolve({
              ok: true,
              json: () => Promise.resolve({ data: 'Delayed response' })
            }),
            5000 // 5 second delay
          )
        )
      );

      render(
        <ComponentName
          {...edgeCaseFixtures.slowNetwork.props}
          data-testid="component"
        />
      );

      // Should show immediate loading indicator
      expect(screen.getByTestId('component')).toHaveClass('loading');

      // After delay threshold, should show extended loading UI
      jest.advanceTimersByTime(2000); // 2 seconds

      // Check for slow connection message
      expect(screen.getByTestId('loading-message')).toHaveTextContent(
        edgeCaseFixtures.slowNetwork.loadingMessage
      );

      // Advance timer to complete loading
      jest.advanceTimersByTime(3000); // +3 seconds = 5 seconds total

      // Resolve all promises
      await act(async () => {
        jest.runAllTimers();
      });

      // Should now show content
      expect(screen.getByTestId('component')).not.toHaveClass('loading');
      expect(screen.getByText('Delayed response')).toBeInTheDocument();

      // Restore timers and fetch
      jest.useRealTimers();
      global.fetch.mockRestore();
    });

    it('handles request timeouts correctly', async () => {
      jest.useFakeTimers();

      // Mock fetch to never resolve
      global.fetch = jest.fn(() => new Promise(() => { }));

      render(
        <ComponentName
          {...edgeCaseFixtures.timeout.props}
          timeout={3000} // 3 second timeout
          data-testid="component"
        />
      );

      // Should show loading state initially
      expect(screen.getByTestId('component')).toHaveClass('loading');

      // Advance past timeout
      jest.advanceTimersByTime(3100);

      // Resolve pending promises
      await act(async () => {
        jest.runAllTimers();
      });

      // Should show timeout error
      expect(screen.getByTestId('component')).toHaveClass('error');
      expect(screen.getByTestId('error-message')).toHaveTextContent(
        edgeCaseFixtures.timeout.timeoutMessage
      );

      // Restore timers and fetch
      jest.useRealTimers();
      global.fetch.mockRestore();
    });
  });

  // Internationalization Tests
  describe('Internationalization', () => {
    it('renders right-to-left (RTL) content correctly', () => {
      render(
        <ComponentName
          {...edgeCaseFixtures.rtl.props}
          content="مرحبا بالعالم"
          language="ar"
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should have RTL direction
      expect(component).toHaveAttribute('dir', 'rtl');

      // Should have language attribute
      expect(component).toHaveAttribute('lang', 'ar');

      // RTL-specific layout classes
      expect(component).toHaveClass('rtl');
    });

    it('handles date and number formatting correctly', () => {
      const { rerender } = render(
        <ComponentName
          {...edgeCaseFixtures.i18n.props}
          date={new Date('2023-01-15')}
          number={1234.56}
          language="en-US"
          data-testid="component"
        />
      );

      // US English format
      let dateElement = screen.getByTestId('formatted-date');
      let numberElement = screen.getByTestId('formatted-number');


      // Check US English format
      expect(dateElement).toHaveTextContent(/1\/15\/2023|January 15, 2023/);
      expect(numberElement).toHaveTextContent('1,234.56');

      // Change to German
      rerender(
        <ComponentName
          {...edgeCaseFixtures.i18n.props}
          date={new Date('2023-01-15')}
          number={1234.56}
          language="de-DE"
          data-testid="component"
        />
      );

      dateElement = screen.getByTestId('formatted-date');
      numberElement = screen.getByTestId('formatted-number');

      // Check German format
      expect(dateElement).toHaveTextContent(/15\.01\.2023|15\. Januar 2023/);
      expect(numberElement).toHaveTextContent('1.234,56');

      // Change to Arabic
      rerender(
        <ComponentName
          {...edgeCaseFixtures.i18n.props}
          date={new Date('2023-01-15')}
          number={1234.56}
          language="ar-SA"
          data-testid="component"
        />
      );

      dateElement = screen.getByTestId('formatted-date');
      numberElement = screen.getByTestId('formatted-number');

      // Check Arabic format (includes Arabic numerals)
      expect(dateElement).toHaveTextContent(/١٥\/٠١\/٢٠٢٣|١٥ يناير ٢٠٢٣/);
      expect(numberElement).toHaveTextContent(/١٬٢٣٤٫٥٦/);
    });

    it('handles translation strings correctly', () => {
      // Mock translation function
      const translate = jest.fn(key => {
        const translations = {
          'en': {
            'greeting': 'Hello',
            'welcome': 'Welcome to our site'
          },
          'fr': {
            'greeting': 'Bonjour',
            'welcome': 'Bienvenue sur notre site'
          },
          'ja': {
            'greeting': 'こんにちは',
            'welcome': 'サイトへようこそ'
          }
        };

        const [language, messageKey] = key.split('.');
        return translations[language]?.[messageKey] || key;
      });

      // Test different languages
      const languages = ['en', 'fr', 'ja'];

      for (const language of languages) {
        const { unmount } = render(
          <ComponentName
            {...edgeCaseFixtures.translations.props}
            language={language}
            translate={translate}
            data-testid="component"
          />
        );

        const component = screen.getByTestId('component');
        const greeting = screen.getByTestId('greeting');
        const welcome = screen.getByTestId('welcome');

        // Should call translate with correct keys
        expect(translate).toHaveBeenCalledWith(`${language}.greeting`);
        expect(translate).toHaveBeenCalledWith(`${language}.welcome`);

        // Should display translated content
        if (language === 'en') {
          expect(greeting).toHaveTextContent('Hello');
          expect(welcome).toHaveTextContent('Welcome to our site');
        } else if (language === 'fr') {
          expect(greeting).toHaveTextContent('Bonjour');
          expect(welcome).toHaveTextContent('Bienvenue sur notre site');
        } else if (language === 'ja') {
          expect(greeting).toHaveTextContent('こんにちは');
          expect(welcome).toHaveTextContent('サイトへようこそ');
        }

        translate.mockClear();
        unmount();
      }
    });
  });

  // Accessibility Edge Cases
  describe('Accessibility Edge Cases', () => {
    it('handles screen reader announcements correctly', () => {
      // Mock aria-live regions
      document.body.innerHTML = `
        <div id="sr-announcements" aria-live="polite"></div>
        <div id="app-root"></div>
      `;

      const { unmount } = render(
        <ComponentName
          {...edgeCaseFixtures.a11y.props}
          data-testid="component"
        />,
        { container: document.getElementById('app-root') }
      );

      const component = screen.getByTestId('component');
      const srAnnouncements = document.getElementById('sr-announcements');

      // Trigger an action that should make a screen reader announcement
      const button = component.querySelector('button');
      button.click();

      // Check if announcement was made
      expect(srAnnouncements.textContent).toBe(edgeCaseFixtures.a11y.expectedAnnouncement);

      unmount();
    });

    it('handles high contrast mode correctly', () => {
      // Mock high contrast media query
      window.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query.includes('high-contrast'),
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      render(
        <ComponentName
          {...edgeCaseFixtures.highContrast.props}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should have high contrast class
      expect(component).toHaveClass('high-contrast');

      // Should have high contrast styles
      const styles = window.getComputedStyle(component);
      expect(styles.border).toBe('1px solid');
      expect(styles.outlineWidth).toBe('2px');

      // Restore matchMedia
      window.matchMedia.mockRestore();
    });

    it('handles zoom levels correctly', () => {
      // Mock different zoom levels
      const zoomLevels = [1, 1.5, 2, 3];

      for (const zoom of zoomLevels) {
        // Set zoom level
        document.body.style.zoom = zoom;

        const { unmount } = render(
          <ComponentName
            {...edgeCaseFixtures.zoom.props}
            data-testid="component"
          />
        );

        const component = screen.getByTestId('component');

        // Should maintain readability at all zoom levels
        const fontSize = parseInt(window.getComputedStyle(component).fontSize);

        // Text should remain above minimum readable size (e.g., ~14px)
        expect(fontSize * zoom).toBeGreaterThanOrEqual(14);

        // Layout should adapt to zoom level
        if (zoom >= 2) {
          // At high zoom, layout should simplify
          expect(component).toHaveClass('simplified-layout');
        }

        unmount();
      }

      // Reset zoom
      document.body.style.zoom = 1;
    });
  });
});
```

### **Regression Test Implementation**

```tsx
// src/components/[ComponentName]/__tests__/regression/ComponentName.regression.test.tsx

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import ComponentName from '../../ComponentName';
import { regressionFixtures } from '../../__fixtures__/ComponentName.fixtures';

describe('ComponentName - Regression Tests', () => {
  // Historical Bugs
  describe('Historical Bug Fixes', () => {
    // Test for each historical bug to prevent regressions
    for (const bugFix of regressionFixtures.historicalBugs) {
      it(`prevents regression of bug #${bugFix.id}: ${bugFix.description}`, async () => {
        const user = userEvent.setup();

        // Setup mock functions if needed
        const mockFunctions = {};
        if (bugFix.mocks) {
          for (const [name, implementation] of Object.entries(bugFix.mocks)) {
            mockFunctions[name] = jest.fn(implementation);
          }
        }

        // Render with the props that previously caused the bug
        render(
          <ComponentName
            {...bugFix.props}
            {...mockFunctions}
            data-testid="component"
          />
        );

        const component = screen.getByTestId('component');

        // Execute the sequence that previously triggered the bug
        for (const step of bugFix.steps) {
          if (step.action === 'click') {
            const target = step.selector
              ? component.querySelector(step.selector)
              : component;
            await user.click(target);
          } else if (step.action === 'hover') {
            const target = step.selector
              ? component.querySelector(step.selector)
              : component;
            await user.hover(target);
          } else if (step.action === 'type') {
            const target = component.querySelector(step.selector);
            await user.type(target, step.value);
          } else if (step.action === 'wait') {
            // Use fake timers for wait steps
            jest.useFakeTimers();
            jest.advanceTimersByTime(step.duration);
            jest.useRealTimers();
          }
        }

        // Verify bug is fixed by checking expected outcomes
        for (const assertion of bugFix.assertions) {
          if (assertion.type === 'element') {
            const target = assertion.selector
              ? component.querySelector(assertion.selector)
              : component;

            if (assertion.expectation === 'toBeVisible') {
              expect(target).toBeVisible();
            } else if (assertion.expectation === 'not.toBeVisible') {
              expect(target).not.toBeVisible();
            } else if (assertion.expectation === 'toHaveClass') {
              expect(target).toHaveClass(assertion.value);
            } else if (assertion.expectation === 'not.toHaveClass') {
              expect(target).not.toHaveClass(assertion.value);
            } else if (assertion.expectation === 'toHaveTextContent') {
              expect(target).toHaveTextContent(assertion.value);
            }
          } else if (assertion.type === 'function') {
            const mockFn = mockFunctions[assertion.name];

            if (assertion.expectation === 'toHaveBeenCalled') {
              expect(mockFn).toHaveBeenCalled();
            } else if (assertion.expectation === 'not.toHaveBeenCalled') {
              expect(mockFn).not.toHaveBeenCalled();
            } else if (assertion.expectation === 'toHaveBeenCalledWith') {
              expect(mockFn).toHaveBeenCalledWith(...assertion.args);
            } else if (assertion.expectation === 'toHaveBeenCalledTimes') {
              expect(mockFn).toHaveBeenCalledTimes(assertion.count);
            }
          }
        }
      });
    }
  });

  // Version Compatibility Tests
  describe('Version Compatibility', () => {
    it('maintains compatibility with parent component v1.x', () => {
      // Mock legacy parent component interface
      const legacyProps = regressionFixtures.compatibility.v1Props;

      render(<ComponentName {...legacyProps} data-testid="component" />);

      const component = screen.getByTestId('component');

      // Verify component still works with legacy props
      for (const assertion of regressionFixtures.compatibility.v1Assertions) {
        if (assertion.type === 'class') {
          expect(component).toHaveClass(assertion.value);
        } else if (assertion.type === 'attribute') {
          expect(component).toHaveAttribute(assertion.name, assertion.value);
        } else if (assertion.type === 'content') {
          expect(component).toHaveTextContent(assertion.value);
        }
      }
    });

    it('handles deprecated props correctly', () => {
      // Mock console.warn to track deprecation warnings
      const originalWarn = console.warn;
      console.warn = jest.fn();

      render(
        <ComponentName
          {...regressionFixtures.deprecation.props}
          data-testid="component"
        />
      );

      // Should warn about deprecated props
      for (const deprecatedProp of regressionFixtures.deprecation.expectedWarnings) {
        expect(console.warn).toHaveBeenCalledWith(
          expect.stringContaining(deprecatedProp)
        );
      }

      // But should still render correctly
      const component = screen.getByTestId('component');
      expect(component).toBeInTheDocument();

      // Restore console.warn
      console.warn = originalWarn;
    });
  });

  // Visual Regression Tests
  describe('Visual Consistency', () => {
    beforeAll(() => {
      // Mock for html-to-image
      global.htmlToImage = {
        toPng: jest.fn().mockResolvedValue('mock-png-data')
      };
    });

    afterAll(() => {
      delete global.htmlToImage;
    });

    it('maintains consistent visual rendering', async () => {
      // Render component with specific props
      render(
        <ComponentName
          {...regressionFixtures.visual.props}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Capture "screenshot" (mock)
      const screenshot = await global.htmlToImage.toPng(component);

      // Compare with baseline (in real implementation, this would use
      // actual visual comparison tools)
      expect(screenshot).toBe('mock-png-data');

      // Verify critical style properties
      const styles = window.getComputedStyle(component);

      for (const [property, value] of Object.entries(regressionFixtures.visual.expectedStyles)) {
        expect(styles[property]).toBe(value);
      }
    });
  });

  // Browser Compatibility Tests
  describe('Browser Compatibility', () => {
    // Test for IE11 compatibility issues
    it('handles IE11 compatibility', () => {
      // Mock IE11 environment
      const originalUserAgent = navigator.userAgent;
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko',
        writable: true
      });

      // Mock for missing modern features
      const originalMap = window.Map;
      window.Map = undefined;

      render(
        <ComponentName
          {...regressionFixtures.browsers.ie11Props}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should render with IE11 fallback classes
      expect(component).toHaveClass('ie11-fallback');

      // Should not use unsupported features
      expect(component.style.display).not.toBe('grid');
      expect(component.style.display).not.toBe('flex');

      // Restore mocks
      Object.defineProperty(navigator, 'userAgent', {
        value: originalUserAgent,
        writable: true
      });
      window.Map = originalMap;
    });

    // Test for Safari-specific issues
    it('handles Safari-specific issues', () => {
      // Mock Safari environment
      const originalUserAgent = navigator.userAgent;
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
        writable: true
      });

      render(
        <ComponentName
          {...regressionFixtures.browsers.safariProps}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Should apply Safari-specific fixes
      expect(component).toHaveClass('safari-fix');

      // Restore userAgent
      Object.defineProperty(navigator, 'userAgent', {
        value: originalUserAgent,
        writable: true
      });
    });
  });

  // Layout Shift Tests
  describe('Layout Stability', () => {
    it('prevents layout shifts during loading', async () => {
      jest.useFakeTimers();

      // Mock for PerformanceObserver to track layout shifts
      const layoutShifts = [];
      global.PerformanceObserver = jest.fn().mockImplementation((callback) => {
        return {
          observe: jest.fn(),
          disconnect: jest.fn(),
          takeRecords: jest.fn(),
          // Simulate layout shift tracking
          recordShift: (value) => {
            layoutShifts.push(value);
            callback({ getEntries: () => [{ value }] });
          }
        };
      });

      // Mock async content loading
      global.fetch = jest.fn().mockImplementation(() =>
        new Promise(resolve =>
          setTimeout(() =>
            resolve({
              ok: true,
              json: () => Promise.resolve({
                items: Array(10).fill().map((_, i) => ({ id: i, name: `Item ${i}` }))
              })
            }),
            1000
          )
        )
      );

      render(
        <ComponentName
          {...regressionFixtures.layoutStability.props}
          data-testid="component"
        />
      );

      const component = screen.getByTestId('component');

      // Record initial dimensions
      const initialHeight = component.offsetHeight;
      const initialWidth = component.offsetWidth;

      // Advance time to trigger data loading completion
      jest.advanceTimersByTime(1100);

      // Resolve all promises
      await act(async () => {
        jest.runAllTimers();
      });

      // Record final dimensions
      const finalHeight = component.offsetHeight;
      const finalWidth = component.offsetWidth;

      // Verify proper placeholders prevented layout shift
      expect(initialHeight).toBe(finalHeight);
      expect(initialWidth).toBe(finalWidth);

      // No layout shifts should be recorded
      expect(layoutShifts.length).toBe(0);

      // Restore mocks
      jest.useRealTimers();
      global.fetch.mockRestore();
      delete global.PerformanceObserver;
    });
  });
});
```

## **Ultimate Test Fixtures Generation Framework**

To power these ultra-detailed tests, implement this comprehensive test fixtures framework:

```tsx
// __fixtures__/ComponentName.fixtures.ts

export const defaultItems = [
  { id: 1, name: 'Item 1', active: true },
  { id: 2, name: 'Item 2', active: false },
  { id: 3, name: 'Item 3', active: false }
];

// Required props with valid and invalid values for testing
export const requiredProps = {
  variant: {
    valid: ['primary', 'secondary', 'outline'],
    invalid: ['invalid-variant', 123, null]
  },
  children: {
    valid: ['Content', <div key="1">JSX Content</div>, 123],
    invalid: []
  }
};

// Optional props with valid values and edge cases
export const optionalProps = {
  size: {
    valid: ['sm', 'md', 'lg', 'xl'],
    invalid: ['xs', 'xxl', 123]
  },
  disabled: {
    valid: [true, false],
    invalid: ['yes', 'no']
  },
  className: {
    valid: ['custom-class', 'multiple classes', ''],
    invalid: [123, {}]
  },
  onClick: {
    valid: [() => { }, jest.fn()],
    invalid: ['not-a-function', 123]
  }
};

// Test conditional props combinations
export const conditionalProps = [
  {
    description: 'When loading is true, disabled should be automatically applied',
    props: { variant: 'primary', children: 'Content', loading: true },
    expectations: [
      ['self', { toBeInTheDocument: true, toHaveClass: 'loading disabled' }],
      ['.spinner', { toBeInTheDocument: true }]
    ]
  },
  {
    description: 'When size is lg and variant is primary, specific styling applies',
    props: { variant: 'primary', children: 'Content', size: 'lg' },
    expectations: [
      ['self', { toBeInTheDocument: true, toHaveClass: 'size-lg variant-primary' }],
      ['.icon', { toHaveClass: 'icon-large' }]
    ]
  }
];

// Edge case props
export const edgeCaseProps = [
  {
    description: 'Empty string children should show placeholder',
    props: { variant: 'primary', children: '' },
    assertions: [
      { type: 'class', value: 'empty', positive: true },
      { type: 'content', value: 'No content provided', positive: true }
    ]
  },
  {
    description: 'Very long content should be truncated',
    props: {
      variant: 'primary',
      children: 'This is a very long string that should be truncated when rendered in the component to prevent overflow and maintain the layout integrity'
    },
    assertions: [
      { type: 'class', value: 'truncated', positive: true },
      { type: 'attribute', name: 'title', value: 'This is a very long string that should be truncated when rendered in the component to prevent overflow and maintain the layout integrity', positive: true }
    ]
  }
];

// State management test fixtures
export const stateFixtures = {
  default: {
    props: { variant: 'primary', children: 'Default State' }
  },
  customInitial: {
    props: { variant: 'primary', children: 'Custom Initial', initialState: 'active' }
  },
  transitions: [
    {
      from: 'initial',
      to: 'active',
      trigger: 'clicked',
      triggerType: 'click',
      triggerTarget: 'self',
      props: { variant: 'primary', children: 'Click Transition' },
      assertions: [
        { type: 'class', value: 'state-active', positive: true },
        { type: 'class', value: 'state-initial', positive: false },
        { type: 'attribute', name: 'aria-pressed', value: 'true', positive: true }
      ]
    },
    {
      from: 'initial',
      to: 'focus',
      trigger: 'focused',
      triggerType: 'focus',
      props: { variant: 'primary', children: 'Focus Transition' },
      assertions: [
        { type: 'class', value: 'state-focus', positive: true },
        { type: 'class', value: 'focused', positive: true },
        { type: 'attribute', name: 'aria-expanded', value: 'true', positive: true }
      ]
    },
    {
      from: 'loading',
      to: 'complete',
      trigger: 'data loaded',
      triggerType: 'timeout',
      timeout: 1000,
      props: { variant: 'primary', children: 'Timeout Transition', loading: true },
      assertions: [
        { type: 'class', value: 'state-complete', positive: true },
        { type: 'class', value: 'state-loading', positive: false },
        { type: 'attribute', name: 'aria-busy', value: 'false', positive: true }
      ]
    }
  ],
  sequences: [
    {
      description: 'Full interaction sequence from initial to complete',
      props: { variant: 'primary', children: 'Sequence Test' },
      steps: [
        {
          action: 'click',
          target: 'self',
          expectedStateBefore: 'initial',
          expectedStateAfter: 'active',
          assertions: [
            { target: 'self', type: 'class', value: 'clicked', positive: true }
          ]
        },
        {
          action: 'wait',
          duration: 500,
          expectedStateBefore: 'active',
          expectedStateAfter: 'processing',
          assertions: [
            { target: '.spinner', type: 'visible', positive: true }
          ]
        },
        {
          action: 'wait',
          duration: 1000,
          expectedStateBefore: 'processing',
          expectedStateAfter: 'complete',
          assertions: [
            { target: '.check-icon', type: 'visible', positive: true },
            { target: '.spinner', type: 'visible', positive: false }
          ]
        }
      ]
    }
  ],
  uncontrolled: {
    props: { variant: 'primary', children: 'Uncontrolled State' }
  },
  controlled: {
    props: { variant: 'primary', children: 'Controlled State' }
  },
  lifecycle: {
    props: { variant: 'primary', children: 'Lifecycle Test' }
  },
  effects: {
    props: { variant: 'primary', children: 'Effect Dependencies Test' }
  }
};

// Responsive test fixtures
export const responsiveFixtures = {
  basic: {
    props: { variant: 'primary', children: 'Responsive Test' }
  },
  content: {
    props: {
      variant: 'primary',
      children: (
        <>
          <div data-testid="mobile-only" className="d-md-none">Mobile Content</div>
          <div data-testid="desktop-only" className="d-none d-md-block">Desktop Content</div>
        </>
      )
    }
  },
  sizing: {
    props: { variant: 'primary', children: 'Sizing Test' }
  },
  interaction: {
    props: {
      variant: 'primary',
      children: (
        <>
          <div data-testid="mobile-menu" className="d-md-none">Mobile Menu</div>
          <div data-testid="desktop-menu" className="d-none d-md-block">Desktop Menu</div>
        </>
      )
    }
  },
  lazyLoad: {
    props: { variant: 'primary', children: 'Lazy Load Test' }
  },
  orientation: {
    props: { variant: 'primary', children: 'Orientation Test' }
  },
  features: {
    props: { variant: 'primary', children: 'Feature Detection Test' }
  },
  performance: {
    props: {
      variant: 'primary',
      children: 'Performance Test',
      images: {
        small: 'small.jpg',
        medium: 'medium.jpg',
        large: 'large.jpg'
      }
    }
  },
  breakpoints: {
    props: { variant: 'primary', children: 'Breakpoint Test' }
  }
};

// Performance test fixtures
export const performanceFixtures = {
  basic: {
    props: { variant: 'primary', children: 'Performance Test' }
  },
  largeData: {
    props: {
      variant: 'primary',
      children: 'Large Data Test',
      items: Array.from({ length: 1000 }).map((_, i) => ({ id: i, name: `Item ${i}` }))
    }
  },
  interaction: {
    props: {
      variant: 'primary',
      children: 'Interaction Performance Test'
    }
  },
  animation: {
    props: {
      variant: 'primary',
      children: 'Animation Performance Test',
      animate: true
    }
  },
  rerender: {
    props: { variant: 'primary', children: 'Re-render Performance Test' }
  },
  memoization: {
    props: { variant: 'primary', children: 'Memoization Test' }
  },
  memory: {
    props: { variant: 'primary', children: 'Memory Usage Test' }
  }
};

// Security test fixtures
export const securityFixtures = {
  xss: {
    props: { variant: 'primary', sanitize: true },
    unsafeContent: '<script>alert("XSS")</script><div>Safe content</div>'
  },
  input: {
    props: {
      variant: 'primary',
      allowNegative: true,
      allowDecimals: true
    }
  },
  csrf: {
    props: { variant: 'primary', children: 'CSRF Protection Test' }
  },
  sensitive: {
    props: { variant: 'primary', children: 'Sensitive Data Test' }
  },
  proptypes: {
    props: { variant: 'primary', children: 'PropTypes Validation Test' }
  }
};

// Edge case test fixtures
export const edgeCaseFixtures = {
  empty: {
    props: { variant: 'primary' },
    expectFallback: true,
    fallbackText: 'No content provided'
  },
  null: {
    props: { variant: 'primary' },
    expectFallback: true,
    fallbackText: 'No content provided'
  },
  undefined: {
    props: { variant: 'primary' },
    expectFallback: true,
    fallbackText: 'No content provided'
  },
  emptyArray: {
    props: { variant: 'primary' },
    expectEmptyMessage: true,
    emptyMessage: 'No items available'
  },
  emptyObject: {
    props: { variant: 'primary' },
    expectEmptyState: true
  },
  boundaries: {
    props: { variant: 'primary', children: 'Boundary Test' },
    min: 0,
    max: 100,
    clampValues: true
  },
  extremes: {
    props: { variant: 'primary', children: 'Extreme Values Test' },
    handleSpecialNumbers: true
  },
  specialChars: {
    props: { variant: 'primary' }
  },
  htmlEntities: {
    props: { variant: 'primary' },
    decodeEntities: true
  },
  bidi: {
    props: { variant: 'primary' }
  },
  renderError: {
    props: {
      variant: 'primary',
      children: 'Error Test',
      forceError: true
    }
  },
  validationError: {
    props: {
      variant
        : 'primary',
      invalidProp: 'test'
    },
    expectedPropErrors: [
      { prop: 'invalidProp', expectedType: 'string' }
    ]
  },
  apiError: {
    props: {
      variant: 'primary',
      children: 'API Error Test',
      fetchData: true
    },
    expectedErrorMessage: 'Failed to load data. Please try again.',
    hasRetryButton: true
  },
  slowNetwork: {
    props: {
      variant: 'primary',
      children: 'Slow Network Test',
      fetchData: true
    },
    loadingMessage: 'This is taking longer than expected. Please wait...'
  },
  timeout: {
    props: {
      variant: 'primary',
      children: 'Timeout Test',
      fetchData: true
    },
    timeoutMessage: 'Request timed out. Please try again.'
  },
  rtl: {
    props: { variant: 'primary' }
  },
  i18n: {
    props: { variant: 'primary', children: 'i18n Test' }
  },
  translations: {
    props: { variant: 'primary', children: 'Translation Test' }
  },
  a11y: {
    props: {
      variant: 'primary',
      children: (
        <>
          <button>Announce Action</button>
        </>
      )
    },
    expectedAnnouncement: 'Action completed successfully'
  },
  highContrast: {
    props: { variant: 'primary', children: 'High Contrast Test' }
  },
  zoom: {
    props: { variant: 'primary', children: 'Zoom Test' }
  }
};

// Regression test fixtures
export const regressionFixtures = {
  historicalBugs: [
    {
      id: 'BUG-001',
      description: 'Component lost focus after state change',
      props: { variant: 'primary', children: 'Focus Bug Test' },
      mocks: {
        onFocus: () => { },
        onBlur: () => { }
      },
      steps: [
        { action: 'click', selector: 'button' },
        { action: 'wait', duration: 100 }
      ],
      assertions: [
        { type: 'element', selector: 'button', expectation: 'toHaveFocus' },
        { type: 'function', name: 'onBlur', expectation: 'not.toHaveBeenCalled' }
      ]
    },
    {
      id: 'BUG-002',
      description: 'Error state was not cleared after successful retry',
      props: {
        variant: 'primary',
        children: 'Error State Bug Test',
        initialState: 'error'
      },
      mocks: {
        onRetry: () => { }
      },
      steps: [
        { action: 'click', selector: '.retry-button' },
        { action: 'wait', duration: 500 }
      ],
      assertions: [
        { type: 'element', selector: '.component', expectation: 'not.toHaveClass', value: 'error-state' },
        { type: 'element', selector: '.success-message', expectation: 'toBeVisible' }
      ]
    }
  ],
  compatibility: {
    v1Props: {
      variant: 'primary',
      children: 'Legacy Component Test',
      label: 'Old Label Prop',
      // Legacy props that should still work
      buttonType: 'submit',
      isDisabled: true
    },
    v1Assertions: [
      { type: 'class', value: 'disabled' },
      { type: 'attribute', name: 'type', value: 'submit' },
      { type: 'content', value: 'Old Label Prop' }
    ]
  },
  deprecation: {
    props: {
      variant: 'primary',
      children: 'Deprecated Props Test',
      // Deprecated props
      color: 'blue',
      size: 'medium',
      isDisabled: true
    },
    expectedWarnings: [
      'color is deprecated',
      'isDisabled is deprecated'
    ]
  },
  visual: {
    props: {
      variant: 'primary',
      children: 'Visual Regression Test'
    },
    expectedStyles: {
      backgroundColor: 'rgb(59, 130, 246)',
      color: 'rgb(255, 255, 255)',
      borderRadius: '4px',
      padding: '8px 16px',
      fontSize: '16px'
    }
  },
  browsers: {
    ie11Props: {
      variant: 'primary',
      children: 'IE11 Compatibility Test',
      ieSupport: true
    },
    safariProps: {
      variant: 'primary',
      children: 'Safari Compatibility Test',
      safariSupport: true
    }
  },
  layoutStability: {
    props: {
      variant: 'primary',
      children: 'Layout Stability Test',
      fetchItems: true
    }
  }
};

// A11y test fixtures
export const a11yFixtures = {
  keyboard: {
    props: {
      variant: 'primary',
      children: 'Keyboard Navigation Test',
      items: [
        { id: 1, label: 'Item 1' },
        { id: 2, label: 'Item 2' },
        { id: 3, label: 'Item 3' }
      ]
    },
    keyboardSequence: [
      { key: 'Tab', expectedFocusSelector: '.component' },
      { key: 'Enter', expectedStateChange: 'expanded' },
      { key: 'ArrowDown', expectedFocusSelector: '.item-0' },
      { key: 'ArrowDown', expectedFocusSelector: '.item-1' },
      { key: 'Enter', expectedResult: 'itemSelected', expectedItemIndex: 1 }
    ]
  },
  screenReader: {
    props: {
      variant: 'primary',
      children: 'Screen Reader Test',
      ariaLabel: 'Custom component label',
      items: [
        { id: 1, label: 'Item 1' },
        { id: 2, label: 'Item 2', disabled: true },
        { id: 3, label: 'Item 3' }
      ]
    },
    expectedAnnouncements: [
      { element: '.component', content: 'Custom component label' },
      { element: '.item-0', content: 'Item 1' },
      { element: '.item-1', content: 'Item 2, disabled' },
      { element: '.item-2', content: 'Item 3' }
    ]
  },
  contrast: {
    props: {
      variant: 'primary',
      children: 'Contrast Test',
      highContrast: true
    },
    contrastLevels: [
      { element: '.component', background: '#FFFFFF', foreground: '#000000', minRatio: 4.5 },
      { element: '.component.disabled', background: '#FFFFFF', foreground: '#767676', minRatio: 4.5 },
      { element: '.error-message', background: '#FFFFFF', foreground: '#D42F2F', minRatio: 4.5 }
    ]
  },
  focusVisible: {
    props: {
      variant: 'primary',
      children: 'Focus Visible Test'
    },
    focusStates: [
      { element: '.component', selector: ':focus-visible', expectedClass: 'focus-visible' },
      { element: '.interactive-child', selector: ':focus-visible', expectedClass: 'focus-visible' }
    ]
  },
  ariaStates: {
    props: {
      variant: 'primary',
      children: 'ARIA States Test',
      disabled: false,
      expanded: false,
      selected: false,
      hasPopup: true
    },
    stateChanges: [
      {
        action: 'click',
        expectedAttributes: {
          'aria-expanded': 'true',
          'aria-controls': 'popup-content'
        }
      },
      {
        action: 'selectItem',
        expectedAttributes: {
          'aria-selected': 'true'
        }
      },
      {
        action: 'disable',
        expectedAttributes: {
          'aria-disabled': 'true'
        },
        expectedClasses: ['disabled']
      }
    ]
  },
  reducedMotion: {
    props: {
      variant: 'primary',
      children: 'Reduced Motion Test',
      animated: true
    },
    motionTests: [
      {
        condition: 'prefers-reduced-motion: no-preference',
        expectedStyles: { animation: 'fadeIn 0.3s' }
      },
      {
        condition: 'prefers-reduced-motion: reduce',
        expectedStyles: { animation: 'none' }
      }
    ]
  }
};

// Integration test fixtures
export const integrationFixtures = {
  formContext: {
    props: {
      variant: 'primary',
      children: 'Form Integration Test',
      type: 'submit'
    },
    formProps: {
      onSubmit: jest.fn(e => e.preventDefault()),
      'data-testid': 'test-form'
    },
    interactions: [
      {
        action: 'click',
        target: '[data-testid="component"]',
        expectedResult: 'formSubmitted'
      },
      {
        action: 'disable',
        formSelector: '[data-testid="test-form"]',
        expectedComponentState: 'disabled'
      }
    ]
  },
  themeContext: {
    props: {
      variant: 'primary',
      children: 'Theme Integration Test'
    },
    themes: [
      {
        name: 'light',
        expectedClasses: ['light-theme'],
        expectedStyles: {
          backgroundColor: 'rgb(59, 130, 246)',
          color: 'rgb(255, 255, 255)'
        }
      },
      {
        name: 'dark',
        expectedClasses: ['dark-theme'],
        expectedStyles: {
          backgroundColor: 'rgb(37, 99, 235)',
          color: 'rgb(255, 255, 255)'
        }
      },
      {
        name: 'high-contrast',
        expectedClasses: ['high-contrast-theme'],
        expectedStyles: {
          backgroundColor: 'rgb(0, 0, 0)',
          color: 'rgb(255, 255, 255)',
          border: '2px solid rgb(255, 255, 255)'
        }
      }
    ]
  },
  dataContext: {
    props: {
      variant: 'primary',
      children: 'Data Integration Test'
    },
    dataContexts: [
      {
        name: 'empty',
        data: [],
        expectedContent: 'No data available'
      },
      {
        name: 'loaded',
        data: [
          { id: 1, name: 'Item 1' },
          { id: 2, name: 'Item 2' }
        ],
        expectedContent: ['Item 1', 'Item 2']
      },
      {
        name: 'loading',
        isLoading: true,
        expectedClasses: ['loading']
      },
      {
        name: 'error',
        error: 'Failed to load data',
        expectedContent: 'Failed to load data'
      }
    ]
  },
  routerContext: {
    props: {
      variant: 'primary',
      children: 'Router Integration Test',
      href: '/test-page'
    },
    routerContexts: [
      {
        currentPath: '/',
        expectedAttributes: { href: '/test-page' },
        expectedClasses: []
      },
      {
        currentPath: '/test-page',
        expectedAttributes: { href: '/test-page' },
        expectedClasses: ['active']
      }
    ],
    interactions: [
      {
        action: 'click',
        expectedNavigation: '/test-page'
      }
    ]
  },
  notificationContext: {
    props: {
      variant: 'primary',
      children: 'Notification Integration Test',
      showNotification: true
    },
    notifications: [
      {
        action: 'success',
        expectedMessage: 'Operation completed successfully',
        expectedClasses: ['success-notification']
      },
      {
        action: 'error',
        expectedMessage: 'An error occurred',
        expectedClasses: ['error-notification']
      }
    ]
  }
};
```

This comprehensive testing framework provides extreme detail for enterprise-grade production-ready components with 100% test coverage. The test files are structured to systematically verify every aspect of component behavior across different environments, device sizes, edge cases, and usage scenarios.
