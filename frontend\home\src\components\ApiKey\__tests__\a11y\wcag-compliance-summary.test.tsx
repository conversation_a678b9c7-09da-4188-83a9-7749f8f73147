import React from 'react';
import { render, screen } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import { Create, ApiKeyPermissionSection, PermissionNotification } from '../../index';
import { sampleProjects } from '../../__fixtures__/ApiKey.fixtures';

// Wrapper component to provide form context for ApiKeyPermissionSection
const PermissionSectionWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      permissionType: 'Restricted' as const,
      resourcePermissions: {
        models: 'Read' as const,
        modelCapabilities: 'Write' as const,
        assistants: 'None' as const,
        threads: 'None' as const,
        evals: 'None' as const,
        fineTuning: 'None' as const,
        files: 'Read' as const,
      },
    },
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('ApiKey Components - WCAG Compliance Summary', () => {
  describe('1. Perceivable Information & User Interface', () => {
    it('1.1 Text Alternatives - provides text alternatives for non-text content', () => {
      render(<PermissionNotification variant="info" />);

      // All SVG icons should have aria-hidden="true"
      const svgIcons = document.querySelectorAll('svg');
      svgIcons.forEach((icon) => {
        expect(icon).toHaveAttribute('aria-hidden', 'true');
      });
    });

    it('1.3 Adaptable - creates content that can be presented in different ways', () => {
      const { container } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" groupResources={true} />
        </PermissionSectionWrapper>,
      );

      // Should have proper semantic structure
      const regions = container.querySelectorAll('[role="region"]');
      expect(regions.length).toBeGreaterThan(0);

      // Headings should be properly labeled
      const headings = container.querySelectorAll('h3, h4');
      headings.forEach((heading) => {
        // Either has text content or aria-label
        expect(
          (heading.textContent?.trim()?.length ?? 0) > 0 || heading.hasAttribute('aria-label'),
        ).toBeTruthy();
      });
    });

    it('1.4 Distinguishable - makes it easier for users to see and hear content', () => {
      render(<PermissionNotification />);

      // Text should have sufficient contrast (these are style classes that ensure contrast)
      const notification = document.querySelector(
        '.text-muted-foreground, .text-primary-foreground',
      );
      expect(notification).not.toBeNull();

      // Resource paths should be distinguishable
      render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      const resourcePaths = document.querySelectorAll('.text-xs.text-muted-foreground');
      expect(resourcePaths.length).toBeGreaterThan(0);
    });
  });

  describe('2. Operable User Interface & Navigation', () => {
    it('2.1 Keyboard Accessible - all functionality available from a keyboard', () => {
      const { container } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // Permission buttons should have keyboard handlers
      const permissionButtons = container.querySelectorAll('[role="radio"]');
      permissionButtons.forEach((button) => {
        expect(button).toHaveAttribute('tabIndex', expect.stringMatching(/^0|-1$/));
      });

      // Focus indication should be present (indicated by tabindex)
      const focusableElements = container.querySelectorAll('[tabindex="0"]');
      expect(focusableElements.length).toBeGreaterThan(0);
    });

    it('2.4 Navigable - provides ways to help users navigate, find content', () => {
      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Check for proper headings
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);

      // Form should have proper landmarks
      const form = document.querySelector('form');
      expect(form).toBeInTheDocument();

      // Check for regions in grouped resources
      render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" groupResources={true} />
        </PermissionSectionWrapper>,
      );

      const regions = document.querySelectorAll('[role="region"]');
      expect(regions.length).toBeGreaterThan(0);
    });
  });

  describe('3. Understandable Information & User Interface', () => {
    it('3.1 Readable - makes text content readable and understandable', () => {
      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Check for descriptive labels
      const labels = screen
        .getAllByRole('textbox')
        .map((input) => document.querySelector(`label[for="${input.id}"]`));

      // Check that labels are descriptive
      labels.forEach((label) => {
        if (label) {
          expect(label.textContent?.trim()?.length ?? 0).toBeGreaterThan(0);
        }
      });
    });

    it('3.2 Predictable - makes web pages appear and operate in predictable ways', () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Form should have a clear submission method
      const submitButton = screen.getByRole('button', { name: /Create|Submit/i });
      expect(submitButton).toBeInTheDocument();

      // Cancel button should be adjacent to submit
      const cancelButton = screen.getByRole('button', { name: /Cancel/i });
      expect(cancelButton).toBeInTheDocument();

      // Should have form field validation indicators
      const requiredIndicators = Array.from(container.querySelectorAll('label')).filter((label) =>
        label.textContent?.includes('*'),
      );
      expect(requiredIndicators.length).toBeGreaterThan(0);
    });

    it('3.3 Input Assistance - helps users avoid and correct mistakes', () => {
      // Create a form with an error (simulated)
      const { container } = render(
        <div>
          <div className="text-destructive text-sm font-medium" role="alert">
            Example error
          </div>
        </div>,
      );

      // Error message should have proper role for screen readers
      const errorElement = container.querySelector('[role="alert"]');
      expect(errorElement).toBeInTheDocument();
      expect(errorElement).toHaveTextContent('Example error');
    });
  });

  describe('4. Robust Content & Reliable Interpretation', () => {
    it('4.1 Compatible - maximizes compatibility with current/future user agents', () => {
      const { container } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // Check for proper ARIA roles
      const radioGroup = container.querySelector('[role="radiogroup"]');
      expect(radioGroup).toBeInTheDocument();

      // Check for labelledby references
      const elementsWithLabelledBy = container.querySelectorAll('[aria-labelledby]');
      elementsWithLabelledBy.forEach((element) => {
        const labelId = element.getAttribute('aria-labelledby');
        const label = document.getElementById(labelId || '');
        expect(label).toBeInTheDocument();
      });

      // Check for ARIA states
      const elementsWithAriaChecked = container.querySelectorAll('[aria-checked]');
      expect(elementsWithAriaChecked.length).toBeGreaterThan(0);
    });
  });

  describe('WCAG 2.1 Level A & AA Success Criteria', () => {
    it('1.4.3 Contrast (Minimum) - text has sufficient contrast ratio (≥4.5:1)', () => {
      const { container } = render(<PermissionNotification />);

      // Check primary text elements (mocked check - would use actual contrast calculation in real test)
      const textElements = container.querySelectorAll('p, span, div');

      // Assume text elements use design system colors that meet contrast requirements
      expect(textElements.length).toBeGreaterThan(0);

      // Background should not interfere with text visibility
      expect(
        container.querySelector('.bg-muted, .bg-blue-50, .bg-yellow-50, .bg-red-50'),
      ).not.toBeNull();
    });

    it('2.1.1 Keyboard - all functionality available from keyboard', () => {
      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // All interactive elements should be keyboard accessible
      const interactiveElements = screen.getAllByRole('button');
      interactiveElements.forEach((element) => {
        // Element should be keyboard accessible
        expect(element.tagName).toBe('BUTTON');

        // Should not have negative tabindex
        expect(element).not.toHaveAttribute('tabindex', '-1');
      });
    });

    it('2.4.3 Focus Order - focus follows logical sequence', () => {
      // This would require a more complex test with userEvent.tab()
      // For this summary, we'll verify elements have appropriate tabindex
      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Focus should not be set to -1 for interactive elements
      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
      interactiveElements.forEach((element) => {
        // Skip elements that are deliberately non-interactive
        if (element.hasAttribute('disabled') || element.getAttribute('aria-hidden') === 'true') {
          return;
        }

        // Should not have tabindex="-1" unless it's part of a custom widget with managed focus
        if (element.getAttribute('tabindex') === '-1') {
          // If tabindex is -1, it should be part of a custom widget like a radio group
          const parentWidget = element.closest('[role="radiogroup"]');
          expect(parentWidget).not.toBeNull();
        }
      });
    });

    it('2.4.7 Focus Visible - keyboard focus indicator visible', () => {
      // In real tests this would check for :focus-visible styles
      // For this test we'll check elements can receive focus
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Interactive elements should be able to receive focus
      const focusableElements = container.querySelectorAll(
        'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])',
      );
      expect(focusableElements.length).toBeGreaterThan(0);
    });

    it('3.2.4 Consistent Identification - components with same functionality identified consistently', () => {
      const { container: container1 } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      const { container: container2 } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" variant="compact" />
        </PermissionSectionWrapper>,
      );

      // Permission buttons should be consistently identified in both instances
      const buttons1 = Array.from(container1.querySelectorAll('[role="radio"]')).map(
        (btn) => btn.textContent,
      );
      const buttons2 = Array.from(container2.querySelectorAll('[role="radio"]')).map(
        (btn) => btn.textContent,
      );

      // Both should have the same set of options
      expect(buttons1).toEqual(expect.arrayContaining(['None', 'Read', 'Write']));
      expect(buttons2).toEqual(expect.arrayContaining(['None', 'Read', 'Write']));
    });

    it('4.1.2 Name, Role, Value - name and role can be programmatically determined', () => {
      const { container } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // All interactive elements should have accessible names and roles
      const interactiveElements = container.querySelectorAll(
        '[role="radio"], [role="button"], button, a, input, select',
      );
      interactiveElements.forEach((element) => {
        // Should have role
        expect(element.getAttribute('role') || element.tagName.toLowerCase()).toBeTruthy();

        // Should have accessible name (via content, aria-label, or aria-labelledby)
        const hasAccessibleName =
          (element.textContent?.trim()?.length ?? 0) > 0 ||
          element.hasAttribute('aria-label') ||
          element.hasAttribute('aria-labelledby');

        expect(hasAccessibleName).toBe(true);
      });
    });
  });
});
