import{r as e,m as r}from"./index-Bwql5Dzz.js";import{T as u,A as b,a as v,P as h,C as P,R as x}from"./index-DP5bcQyU.js";const s=t=>e.createElement(x,{...t});s.displayName="Popover";const n=e.forwardRef((t,a)=>e.createElement(u,{ref:a,...t}));n.displayName="Popover.Trigger";const i=e.forwardRef((t,a)=>e.createElement(b,{ref:a,...t}));i.displayName="Popover.Anchor";const d=e.forwardRef((t,a)=>e.createElement(v,{ref:a,...t}));d.displayName="Popover.Close";const l=e.forwardRef(({className:t,sideOffset:a=8,side:o="bottom",align:f="start",collisionPadding:p,...c},g)=>e.createElement(h,null,e.createElement(P,{ref:g,sideOffset:a,side:o,align:f,collisionPadding:p,className:r("bg-ui-bg-base text-ui-fg-base shadow-elevation-flyout max-h-[var(--radix-popper-available-height)] min-w-[220px] overflow-hidden rounded-lg p-1","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...c})));l.displayName="Popover.Content";const m=e.forwardRef(({className:t,...a},o)=>e.createElement("div",{ref:o,className:r("bg-ui-border-base -mx-1 my-1 h-px",t),...a}));m.displayName="Popover.Seperator";const E=Object.assign(s,{Trigger:n,Anchor:i,Close:d,Content:l,Seperator:m});export{E as P};
