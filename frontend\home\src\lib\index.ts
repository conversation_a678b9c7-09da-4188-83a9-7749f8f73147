/**
 * Internationalization (i18n) System
 * 
 * This file is the public entry point for the application's i18n system.
 * It exports a clean, unified API that abstracts away implementation details.
 */

// Export the unified i18n object as the main API
export { default as i18n } from './unified-i18n';

// Export only the necessary types for TypeScript support
export type { SupportedLanguage, TranslationObject } from './unified-i18n';

// Re-export commonly used functions for direct access convenience
// This allows both approaches: importing individual functions or using the i18n object
export {
  // Common language utilities
  supportedLngs,
  languageCodes,
  ns,
  defaultNS,
  
  // Core functions
  isRTL,
  changeLanguage,
  getLanguage,
  
  // Server-side utilities
  getServerTranslation,
} from './unified-i18n';


// Re-export other utility functions that should be public
export * from './dateFormatter';
export * from './utils';
