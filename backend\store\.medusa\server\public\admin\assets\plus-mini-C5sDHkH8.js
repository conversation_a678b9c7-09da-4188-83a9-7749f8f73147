import{r as n}from"./index-Bwql5Dzz.js";var s=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,i=(r,t,e)=>t in r?s(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,v=(r,t)=>{for(var e in t)l.call(t,e)&&i(r,e,t[e]);if(a)for(var e of a(t))f.call(t,e)&&i(r,e,t[e]);return r},_=(r,t)=>{var e={};for(var o in r)l.call(r,o)&&t.indexOf(o)<0&&(e[o]=r[o]);if(r!=null&&a)for(var o of a(r))t.indexOf(o)<0&&f.call(r,o)&&(e[o]=r[o]);return e};const d=n.forwardRef((r,t)=>{var e=r,{color:o="currentColor"}=e,p=_(e,["color"]);return n.createElement("svg",v({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),n.createElement("path",{stroke:o,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M7.5 2.5v10M2.5 7.5h10"}))});d.displayName="PlusMini";export{d as P};
