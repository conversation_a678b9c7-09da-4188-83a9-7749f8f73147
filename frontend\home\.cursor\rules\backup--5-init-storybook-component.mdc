---
description: 
globs: 
alwaysApply: false
---
Automate the entire Storybook setup, including configuration, essential addons, and seamless integration of a user-specified component. Dynamically generate the necessary Storybook files based on feature requirements, ensuring support for:  

- Interactive prop editing and event logging  
- User behavior simulation and interaction testing  
- Automated unit and interaction test execution  
- Auto-generated component documentation with MDX  
- Accessibility testing and compliance verification  
- Responsive design testing across various viewports  
- Customizable UI themes and background variations  

Ensure the automation is modular and extensible, allowing for future enhancements and addon integrations without manual intervention.