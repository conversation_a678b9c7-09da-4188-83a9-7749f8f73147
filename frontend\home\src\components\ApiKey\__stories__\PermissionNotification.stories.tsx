import type { Meta, StoryObj } from '@storybook/react';
import { expect, within } from '@storybook/test';
import { PermissionNotification } from '../ApiKey-PermissionSection';

const meta = {
  title: 'Components/ApiKey/PermissionNotification',
  component: PermissionNotification,
  parameters: {
    docs: {
      description: {
        component:
          'A notification component that informs users about permission changes taking time to take effect.',
      },
    },
  },
  argTypes: {
    useI18n: {
      control: 'boolean',
      description: 'Whether to use internationalization',
      defaultValue: false,
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof PermissionNotification>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default notification with hardcoded text
 */
export const Default: Story = {
  args: {
    useI18n: false,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Component should render with the correct message
    await expect(
      canvas.getByText('Permission changes may take a few minutes to take effect.'),
    ).toBeInTheDocument();

    // Check if there is an SVG icon element
    await expect(canvas.getByText(/Permission changes/)).toBeVisible();
  },
};

/**
 * Notification with internationalization enabled
 */
export const Internationalized: Story = {
  args: {
    useI18n: true,
  },
};
