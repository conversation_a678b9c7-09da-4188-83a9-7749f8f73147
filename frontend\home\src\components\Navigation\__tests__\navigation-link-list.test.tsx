import { render, screen } from '@testing-library/react';
import { LinkList } from '../navigation-link-list';
import { linkListFixtures } from '../__fixtures__/navigation-link-list.fixtures';

// Mock next/link
jest.mock('next/link', () => {
  return ({children, ...props}: any) => {
    return <a {...props}>{children}</a>;
  };
});

describe('LinkList', () => {
  test('renders title correctly', () => {
    render(<LinkList {...linkListFixtures.companyInfo} />);
    
    expect(screen.getByText('ข้อมูลบริษัท')).toBeInTheDocument();
  });

  test('renders all menu items', () => {
    render(<LinkList {...linkListFixtures.companyInfo} />);
    
    expect(screen.getByText('เกี่ยวกับเรา')).toBeInTheDocument();
    expect(screen.getByText('ข่าวสารและกิจกรรม')).toBeInTheDocument();
    expect(screen.getByText('ติดต่อเรา')).toBeInTheDocument();
  });

  test('renders links with correct href attributes', () => {
    render(<LinkList {...linkListFixtures.companyInfo} />);
    
    expect(screen.getByText('เกี่ยวกับเรา').closest('a')).toHaveAttribute('href', '/about');
    expect(screen.getByText('ข่าวสารและกิจกรรม').closest('a')).toHaveAttribute('href', '/news');
    expect(screen.getByText('ติดต่อเรา').closest('a')).toHaveAttribute('href', '/contact');
  });

  test('renders external links with target and rel attributes', () => {
    const externalLinks = {
      title: 'ติดตามเรา',
      items: [
        {
          label: 'Facebook',
          href: 'https://facebook.com',
          external: true,
        },
      ]
    };
    
    render(<LinkList {...externalLinks} />);
    
    const link = screen.getByText('Facebook').closest('a');
    expect(link).toHaveAttribute('href', 'https://facebook.com');
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noopener noreferrer');
  });

  test('renders with custom className props', () => {
    render(
      <LinkList
        {...linkListFixtures.companyInfo}
        className="custom-container"
        titleClassName="custom-title"
        itemClassName="custom-item"
        listClassName="custom-list"
      />
    );
    
    const container = screen.getByTestId('order-list-menu');
    expect(container).toHaveClass('custom-container');
    
    const title = screen.getByText('ข้อมูลบริษัท');
    expect(title).toHaveClass('custom-title');
    
    const list = container.querySelector('ul');
    expect(list).toHaveClass('custom-list');
    
    const item = screen.getByText('เกี่ยวกับเรา').closest('a');
    expect(item).toHaveClass('custom-item');
  });

  test('renders empty list when no items are provided', () => {
    render(<LinkList {...linkListFixtures.empty} />);
    
    expect(screen.getByText('เมนูว่างเปล่า')).toBeInTheDocument();
    const list = screen.getByTestId('order-list-menu').querySelector('ul');
    expect(list?.children.length).toBe(0);
  });
});