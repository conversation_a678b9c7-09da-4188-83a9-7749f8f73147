import{S as h}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as D}from"./chunk-FFVOUYTF-DR1d4TPs.js";import{D as v,u as y,c as x}from"./chunk-MGPZHEOT-CqdSNFtj.js";import{a as C,j as a,ef as L,b as c,u as S,r as m,k as j}from"./index-Bwql5Dzz.js";import{u as A}from"./chunk-C76H5USB-ByRPKhW7.js";import{P}from"./pencil-square-6wRbnn1C.js";import{C as w}from"./container-Dqi2woPF.js";import"./Trans-VWqfqpAH.js";import"./x-mark-mini-DvSTI7zK.js";import"./check-BGSYwiWc.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./checkbox-B4pL6X49.js";import"./index-BxZ1678G.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./table-BDZtqXjX.js";import"./arrow-up-mini-D5bOKjDW.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";var n=20,E=()=>{const{q:e,order:i,offset:s}=A(["q","order","offset"]),{users:r,count:u,isPending:p,isError:d,error:f}=L({q:e,order:i,offset:s?parseInt(s):0,limit:n},{placeholderData:j}),g=T(),b=_(),{t}=c();if(d)throw f;return a.jsx(w,{className:"divide-y p-0",children:a.jsx(v,{data:r,columns:g,filters:b,getRowId:l=>l.id,rowCount:u,pageSize:n,heading:t("users.domain"),rowHref:l=>`${l.id}`,isLoading:p,action:{label:t("users.invite"),to:"invite"},emptyState:{empty:{heading:t("users.list.empty.heading"),description:t("users.list.empty.description")},filtered:{heading:t("users.list.filtered.heading"),description:t("users.list.filtered.description")}}})})},o=x(),T=()=>{const{t:e}=c(),i=S(),s=D();return m.useMemo(()=>[o.accessor("email",{header:e("fields.email"),cell:({row:r})=>r.original.email,enableSorting:!0,sortAscLabel:e("filters.sorting.alphabeticallyAsc"),sortDescLabel:e("filters.sorting.alphabeticallyDesc")}),o.accessor("first_name",{header:e("fields.firstName"),cell:({row:r})=>r.original.first_name||"-",enableSorting:!0,sortAscLabel:e("filters.sorting.alphabeticallyAsc"),sortDescLabel:e("filters.sorting.alphabeticallyDesc")}),o.accessor("last_name",{header:e("fields.lastName"),cell:({row:r})=>r.original.last_name||"-",enableSorting:!0,sortAscLabel:e("filters.sorting.alphabeticallyAsc"),sortDescLabel:e("filters.sorting.alphabeticallyDesc")}),...s,o.action({actions:[{label:e("actions.edit"),icon:a.jsx(P,{}),onClick:r=>{i(`${r.row.original.id}/edit`)}}]})],[e,i,s])},_=()=>{const e=y();return m.useMemo(()=>e,[e])},ee=()=>{const{getWidgets:e}=C();return a.jsx(h,{widgets:{after:e("user.list.after"),before:e("user.list.before")},children:a.jsx(E,{})})};export{ee as Component};
