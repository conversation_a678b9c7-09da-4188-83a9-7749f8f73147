// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`OneColTable Snapshots matches snapshot with custom empty state 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-base flex flex-col items-center justify-center p-8"
    data-disabled="false"
    role="table"
  >
    <div>
      No items available at this time
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with custom header 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-base"
    data-disabled="false"
    role="table"
  >
    <div
      class="border-b p-4"
    >
      <div>
        Custom Header
      </div>
    </div>
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with default props 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-base"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with disabled state 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-base opacity-50 cursor-not-allowed"
    data-disabled="true"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with error state 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border text-base border-destructive"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with load more button 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-base"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #4
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed with warnings (2)
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #5
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Failed due to network error
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
    <div
      class="flex items-center justify-center border-t p-2"
    >
      <button
        class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
      >
        Load more 
        <svg
          class="lucide lucide-chevron-down ml-2 h-4 w-4"
          fill="none"
          height="24"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="m6 9 6 6 6-6"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with size variants: large-size 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-lg"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #4
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed with warnings (2)
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with size variants: medium-size 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-base"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with size variants: small-size 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-sm"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with style variants: outline-variant 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden border border-border text-base"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with style variants: primary-variant 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-background border border-border text-base"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-accent/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OneColTable Snapshots matches snapshot with style variants: secondary-variant 1`] = `
<div>
  <div
    aria-busy="false"
    class="w-full rounded-md overflow-hidden bg-muted text-base"
    data-disabled="false"
    role="table"
  >
    <div
      class="flex flex-col divide-y"
      role="rowgroup"
    >
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-background/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #1
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Completed successfully with 34 items
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-background/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #2
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            In progress, 12 items remaining
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
      <div
        class="flex items-center justify-between w-full px-4 py-3 transition-colors hover:bg-background/50 cursor-pointer"
        role="row"
        style="opacity: 0; transform: translateY(10px);"
      >
        <div
          class="flex-1"
        >
          <div
            class="font-medium"
          >
            Batch Processing #3
          </div>
          <div
            class="text-sm text-muted-foreground"
          >
            Queued for execution
          </div>
        </div>
        <div
          class="text-sm text-muted-foreground"
        >
          <span
            class="inline-block relative"
            data-state="closed"
          >
            Mar 17, 2025, 6:42 PM
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;
