import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import SectionHero from '../section-hero';
import { heroFixtures } from '../__fixtures__/section-hero.fixtures';

const meta: Meta<typeof SectionHero> = {
  title: 'UI/Section/SectionHero',
  component: SectionHero,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
    // เพิ่ม A11y testing
    a11y: {
      config: {
        rules: [
          {
            // ตรวจสอบความคมชัดของสี
            id: 'color-contrast',
            enabled: true
          }
        ]
      }
    }
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'banner', 'centered', 'card', 'split', 'features', 'custom'],
      description: 'รูปแบบการแสดงผลของ Hero Section'
    },
    title: {
      control: 'text',
      description: 'หัวข้อหลัก'
    },
    subtitle: {
      control: 'text',
      description: 'หัวข้อย่อย'
    },
    description: {
      control: 'text',
      description: 'รายละเอียดหรือเนื้อหา'
    },
    height: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl', 'auto'],
      description: 'ความสูงของ Hero Section'
    },
    contentAlignment: {
      control: 'radio',
      options: ['left', 'center', 'right'],
      description: 'การจัดตำแหน่งเนื้อหา'
    },
    container: {
      control: 'boolean',
      description: 'แสดงเป็น container หรือ full width'
    }
  }
};

export default meta;
type Story = StoryObj<typeof SectionHero>;

// Default Hero Story
export const Default: Story = {
  args: {
    ...heroFixtures.default
  }
};

// Banner Hero Story
export const Banner: Story = {
  args: {
    ...heroFixtures.banner
  }
};

// Centered Hero Story
export const Centered: Story = {
  args: {
    ...heroFixtures.centered
  }
};

// Card Hero Story
export const Card: Story = {
  args: {
    ...heroFixtures.card
  }
};

// Features Hero Story
export const Features: Story = {
  args: {
    ...heroFixtures.features
  }
};

// Split Hero Story
export const Split: Story = {
  args: {
    ...heroFixtures.split
  }
};

// Custom Hero Story with Children
export const Custom: Story = {
  args: {
    variant: 'custom',
    title: 'Custom Hero Layout',
    subtitle: 'ปรับแต่งตามต้องการ',
    backgroundColor: '#25A64F',
    height: 'md',
    contentAlignment: 'center',
    container: true
  },
  render: (args) => (
    <SectionHero {...args}>
      <div className="bg-white/10 p-6 rounded-lg mt-8 max-w-3xl mx-auto">
        <h2 className="text-xl font-semibold mb-4 text-white">Custom Content Example</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white/5 p-4 rounded">
            <h3 className="font-medium mb-2">กลุ่มสินค้า A</h3>
            <p className="text-sm opacity-80">รายละเอียดเพิ่มเติมเกี่ยวกับกลุ่มสินค้า A ที่เรามีให้บริการ</p>
            <button className="mt-4 px-3 py-1 text-sm bg-primary/20 rounded">ดูเพิ่มเติม</button>
          </div>
          <div className="bg-white/5 p-4 rounded">
            <h3 className="font-medium mb-2">กลุ่มสินค้า B</h3>
            <p className="text-sm opacity-80">รายละเอียดเพิ่มเติมเกี่ยวกับกลุ่มสินค้า B ที่เรามีให้บริการ</p>
            <button className="mt-4 px-3 py-1 text-sm bg-primary/20 rounded">ดูเพิ่มเติม</button>
          </div>
        </div>
      </div>
    </SectionHero>
  )
};