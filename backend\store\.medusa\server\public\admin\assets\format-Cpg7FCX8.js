import{c6 as k,bx as f,c7 as q,c8 as B,c9 as G,ca as T,bz as X}from"./index-Bwql5Dzz.js";function w(r,t){return r instanceof Date?new r.constructor(t):new Date(t)}function D(r){return k(r,{weekStartsOn:1})}function Q(r){const t=f(r),e=t.getFullYear(),n=w(r,0);n.setFullYear(e+1,0,4),n.setHours(0,0,0,0);const a=D(n),s=w(r,0);s.setFullYear(e,0,4),s.setHours(0,0,0,0);const o=D(s);return t.getTime()>=a.getTime()?e+1:t.getTime()>=o.getTime()?e:e-1}function N(r){const t=f(r);return t.setHours(0,0,0,0),t}function R(r,t){const e=N(r),n=N(t),a=+e-q(e),s=+n-q(n);return Math.round((a-s)/B)}function p(r){const t=Q(r),e=w(r,0);return e.setFullYear(t,0,4),e.setHours(0,0,0,0),D(e)}function I(r){return r instanceof Date||typeof r=="object"&&Object.prototype.toString.call(r)==="[object Date]"}function $(r){if(!I(r)&&typeof r!="number")return!1;const t=f(r);return!isNaN(Number(t))}function j(r){const t=f(r),e=w(r,0);return e.setFullYear(t.getFullYear(),0,1),e.setHours(0,0,0,0),e}function V(r){const t=f(r);return R(t,j(t))+1}function A(r){const t=f(r),e=+D(t)-+p(t);return Math.round(e/G)+1}function L(r,t){var m,O,x,Y;const e=f(r),n=e.getFullYear(),a=T(),s=(t==null?void 0:t.firstWeekContainsDate)??((O=(m=t==null?void 0:t.locale)==null?void 0:m.options)==null?void 0:O.firstWeekContainsDate)??a.firstWeekContainsDate??((Y=(x=a.locale)==null?void 0:x.options)==null?void 0:Y.firstWeekContainsDate)??1,o=w(r,0);o.setFullYear(n+1,0,s),o.setHours(0,0,0,0);const h=k(o,t),d=w(r,0);d.setFullYear(n,0,s),d.setHours(0,0,0,0);const y=k(d,t);return e.getTime()>=h.getTime()?n+1:e.getTime()>=y.getTime()?n:n-1}function J(r,t){var h,d,y,m;const e=T(),n=(t==null?void 0:t.firstWeekContainsDate)??((d=(h=t==null?void 0:t.locale)==null?void 0:h.options)==null?void 0:d.firstWeekContainsDate)??e.firstWeekContainsDate??((m=(y=e.locale)==null?void 0:y.options)==null?void 0:m.firstWeekContainsDate)??1,a=L(r,t),s=w(r,0);return s.setFullYear(a,0,n),s.setHours(0,0,0,0),k(s,t)}function K(r,t){const e=f(r),n=+k(e,t)-+J(e,t);return Math.round(n/G)+1}function i(r,t){const e=r<0?"-":"",n=Math.abs(r).toString().padStart(t,"0");return e+n}const g={y(r,t){const e=r.getFullYear(),n=e>0?e:1-e;return i(t==="yy"?n%100:n,t.length)},M(r,t){const e=r.getMonth();return t==="M"?String(e+1):i(e+1,2)},d(r,t){return i(r.getDate(),t.length)},a(r,t){const e=r.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.toUpperCase();case"aaa":return e;case"aaaaa":return e[0];case"aaaa":default:return e==="am"?"a.m.":"p.m."}},h(r,t){return i(r.getHours()%12||12,t.length)},H(r,t){return i(r.getHours(),t.length)},m(r,t){return i(r.getMinutes(),t.length)},s(r,t){return i(r.getSeconds(),t.length)},S(r,t){const e=t.length,n=r.getMilliseconds(),a=Math.trunc(n*Math.pow(10,e-3));return i(a,t.length)}},b={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},F={G:function(r,t,e){const n=r.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return e.era(n,{width:"abbreviated"});case"GGGGG":return e.era(n,{width:"narrow"});case"GGGG":default:return e.era(n,{width:"wide"})}},y:function(r,t,e){if(t==="yo"){const n=r.getFullYear(),a=n>0?n:1-n;return e.ordinalNumber(a,{unit:"year"})}return g.y(r,t)},Y:function(r,t,e,n){const a=L(r,n),s=a>0?a:1-a;if(t==="YY"){const o=s%100;return i(o,2)}return t==="Yo"?e.ordinalNumber(s,{unit:"year"}):i(s,t.length)},R:function(r,t){const e=Q(r);return i(e,t.length)},u:function(r,t){const e=r.getFullYear();return i(e,t.length)},Q:function(r,t,e){const n=Math.ceil((r.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return i(n,2);case"Qo":return e.ordinalNumber(n,{unit:"quarter"});case"QQQ":return e.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return e.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return e.quarter(n,{width:"wide",context:"formatting"})}},q:function(r,t,e){const n=Math.ceil((r.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return i(n,2);case"qo":return e.ordinalNumber(n,{unit:"quarter"});case"qqq":return e.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return e.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return e.quarter(n,{width:"wide",context:"standalone"})}},M:function(r,t,e){const n=r.getMonth();switch(t){case"M":case"MM":return g.M(r,t);case"Mo":return e.ordinalNumber(n+1,{unit:"month"});case"MMM":return e.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return e.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return e.month(n,{width:"wide",context:"formatting"})}},L:function(r,t,e){const n=r.getMonth();switch(t){case"L":return String(n+1);case"LL":return i(n+1,2);case"Lo":return e.ordinalNumber(n+1,{unit:"month"});case"LLL":return e.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return e.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return e.month(n,{width:"wide",context:"standalone"})}},w:function(r,t,e,n){const a=K(r,n);return t==="wo"?e.ordinalNumber(a,{unit:"week"}):i(a,t.length)},I:function(r,t,e){const n=A(r);return t==="Io"?e.ordinalNumber(n,{unit:"week"}):i(n,t.length)},d:function(r,t,e){return t==="do"?e.ordinalNumber(r.getDate(),{unit:"date"}):g.d(r,t)},D:function(r,t,e){const n=V(r);return t==="Do"?e.ordinalNumber(n,{unit:"dayOfYear"}):i(n,t.length)},E:function(r,t,e){const n=r.getDay();switch(t){case"E":case"EE":case"EEE":return e.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return e.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return e.day(n,{width:"short",context:"formatting"});case"EEEE":default:return e.day(n,{width:"wide",context:"formatting"})}},e:function(r,t,e,n){const a=r.getDay(),s=(a-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return i(s,2);case"eo":return e.ordinalNumber(s,{unit:"day"});case"eee":return e.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return e.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return e.day(a,{width:"short",context:"formatting"});case"eeee":default:return e.day(a,{width:"wide",context:"formatting"})}},c:function(r,t,e,n){const a=r.getDay(),s=(a-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return i(s,t.length);case"co":return e.ordinalNumber(s,{unit:"day"});case"ccc":return e.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return e.day(a,{width:"narrow",context:"standalone"});case"cccccc":return e.day(a,{width:"short",context:"standalone"});case"cccc":default:return e.day(a,{width:"wide",context:"standalone"})}},i:function(r,t,e){const n=r.getDay(),a=n===0?7:n;switch(t){case"i":return String(a);case"ii":return i(a,t.length);case"io":return e.ordinalNumber(a,{unit:"day"});case"iii":return e.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return e.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return e.day(n,{width:"short",context:"formatting"});case"iiii":default:return e.day(n,{width:"wide",context:"formatting"})}},a:function(r,t,e){const a=r.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"aaaa":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(r,t,e){const n=r.getHours();let a;switch(n===12?a=b.noon:n===0?a=b.midnight:a=n/12>=1?"pm":"am",t){case"b":case"bb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"bbbb":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(r,t,e){const n=r.getHours();let a;switch(n>=17?a=b.evening:n>=12?a=b.afternoon:n>=4?a=b.morning:a=b.night,t){case"B":case"BB":case"BBB":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return e.dayPeriod(a,{width:"narrow",context:"formatting"});case"BBBB":default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(r,t,e){if(t==="ho"){let n=r.getHours()%12;return n===0&&(n=12),e.ordinalNumber(n,{unit:"hour"})}return g.h(r,t)},H:function(r,t,e){return t==="Ho"?e.ordinalNumber(r.getHours(),{unit:"hour"}):g.H(r,t)},K:function(r,t,e){const n=r.getHours()%12;return t==="Ko"?e.ordinalNumber(n,{unit:"hour"}):i(n,t.length)},k:function(r,t,e){let n=r.getHours();return n===0&&(n=24),t==="ko"?e.ordinalNumber(n,{unit:"hour"}):i(n,t.length)},m:function(r,t,e){return t==="mo"?e.ordinalNumber(r.getMinutes(),{unit:"minute"}):g.m(r,t)},s:function(r,t,e){return t==="so"?e.ordinalNumber(r.getSeconds(),{unit:"second"}):g.s(r,t)},S:function(r,t){return g.S(r,t)},X:function(r,t,e){const n=r.getTimezoneOffset();if(n===0)return"Z";switch(t){case"X":return H(n);case"XXXX":case"XX":return l(n);case"XXXXX":case"XXX":default:return l(n,":")}},x:function(r,t,e){const n=r.getTimezoneOffset();switch(t){case"x":return H(n);case"xxxx":case"xx":return l(n);case"xxxxx":case"xxx":default:return l(n,":")}},O:function(r,t,e){const n=r.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+C(n,":");case"OOOO":default:return"GMT"+l(n,":")}},z:function(r,t,e){const n=r.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+C(n,":");case"zzzz":default:return"GMT"+l(n,":")}},t:function(r,t,e){const n=Math.trunc(r.getTime()/1e3);return i(n,t.length)},T:function(r,t,e){const n=r.getTime();return i(n,t.length)}};function C(r,t=""){const e=r>0?"-":"+",n=Math.abs(r),a=Math.trunc(n/60),s=n%60;return s===0?e+String(a):e+String(a)+t+i(s,2)}function H(r,t){return r%60===0?(r>0?"-":"+")+i(Math.abs(r)/60,2):l(r,t)}function l(r,t=""){const e=r>0?"-":"+",n=Math.abs(r),a=i(Math.trunc(n/60),2),s=i(n%60,2);return e+a+t+s}const v=(r,t)=>{switch(r){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},_=(r,t)=>{switch(r){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},U=(r,t)=>{const e=r.match(/(P+)(p+)?/)||[],n=e[1],a=e[2];if(!a)return v(r,t);let s;switch(n){case"P":s=t.dateTime({width:"short"});break;case"PP":s=t.dateTime({width:"medium"});break;case"PPP":s=t.dateTime({width:"long"});break;case"PPPP":default:s=t.dateTime({width:"full"});break}return s.replace("{{date}}",v(n,t)).replace("{{time}}",_(a,t))},Z={p:_,P:U},z=/^D+$/,tt=/^Y+$/,et=["D","DD","YY","YYYY"];function rt(r){return z.test(r)}function nt(r){return tt.test(r)}function at(r,t,e){const n=st(r,t,e);if(console.warn(n),et.includes(r))throw new RangeError(n)}function st(r,t,e){const n=r[0]==="Y"?"years":"days of the month";return`Use \`${r.toLowerCase()}\` instead of \`${r}\` (in \`${t}\`) for formatting ${n} to the input \`${e}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const it=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ct=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ut=/^'([^]*?)'?$/,ot=/''/g,dt=/[a-zA-Z]/;function mt(r,t,e){var m,O,x,Y,P,W,E,S;const n=T(),a=(e==null?void 0:e.locale)??n.locale??X,s=(e==null?void 0:e.firstWeekContainsDate)??((O=(m=e==null?void 0:e.locale)==null?void 0:m.options)==null?void 0:O.firstWeekContainsDate)??n.firstWeekContainsDate??((Y=(x=n.locale)==null?void 0:x.options)==null?void 0:Y.firstWeekContainsDate)??1,o=(e==null?void 0:e.weekStartsOn)??((W=(P=e==null?void 0:e.locale)==null?void 0:P.options)==null?void 0:W.weekStartsOn)??n.weekStartsOn??((S=(E=n.locale)==null?void 0:E.options)==null?void 0:S.weekStartsOn)??0,h=f(r);if(!$(h))throw new RangeError("Invalid time value");let d=t.match(ct).map(u=>{const c=u[0];if(c==="p"||c==="P"){const M=Z[c];return M(u,a.formatLong)}return u}).join("").match(it).map(u=>{if(u==="''")return{isToken:!1,value:"'"};const c=u[0];if(c==="'")return{isToken:!1,value:ft(u)};if(F[c])return{isToken:!0,value:u};if(c.match(dt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+c+"`");return{isToken:!1,value:u}});a.localize.preprocessor&&(d=a.localize.preprocessor(h,d));const y={firstWeekContainsDate:s,weekStartsOn:o,locale:a};return d.map(u=>{if(!u.isToken)return u.value;const c=u.value;(!(e!=null&&e.useAdditionalWeekYearTokens)&&nt(c)||!(e!=null&&e.useAdditionalDayOfYearTokens)&&rt(c))&&at(c,t,String(r));const M=F[c[0]];return M(h,c,a.localize,y)}).join("")}function ft(r){const t=r.match(ut);return t?t[1].replace(ot,"'"):r}export{w as c,mt as f};
