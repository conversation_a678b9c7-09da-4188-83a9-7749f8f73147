import{u as b,a as S,b as T,c as w}from"./chunk-UVGNHHSZ-DRARdeex.js";import{S as x}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{a as v,j as o,b as f,g as D,J as _,u as j,ei as E,r as u,t as p,k as P}from"./index-Bwql5Dzz.js";import{D as y,c as k}from"./chunk-MGPZHEOT-CqdSNFtj.js";import{u as H}from"./use-prompt-pbDx0Sfe.js";import{P as L}from"./pencil-square-6wRbnn1C.js";import{T as z}from"./trash-BBylvTAG.js";import{C as I}from"./container-Dqi2woPF.js";import"./chunk-FFVOUYTF-DR1d4TPs.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./Trans-VWqfqpAH.js";import"./x-mark-mini-DvSTI7zK.js";import"./check-BGSYwiWc.js";import"./checkbox-B4pL6X49.js";import"./index-BxZ1678G.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./table-BDZtqXjX.js";import"./arrow-up-mini-D5bOKjDW.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./prompt-BsR9zKsn.js";var d=20,A=()=>{const{t:e}=f(),{store:s}=D(),r=b({pageSize:d}),{sales_channels:t,count:i,isPending:l,isError:a,error:c}=_(r,{placeholderData:P}),m=R(),h=S(),g=T(),C=(t==null?void 0:t.map(n=>({...n,is_default:(s==null?void 0:s.default_sales_channel_id)===n.id})))??[];if(a)throw c;return o.jsx(I,{className:"p-0",children:o.jsx(y,{data:C,columns:m,rowCount:i,getRowId:n=>n.id,pageSize:d,filters:h,isLoading:l,emptyState:g,heading:e("salesChannels.domain"),subHeading:e("salesChannels.subtitle"),action:{label:e("actions.create"),to:"/settings/sales-channels/create"},rowHref:n=>`/settings/sales-channels/${n.id}`})})},N=k(),R=()=>{const{t:e}=f(),s=H(),r=j(),t=w(),{mutateAsync:i}=E(),l=u.useCallback(async a=>{await s({title:e("general.areYouSure"),description:e("salesChannels.deleteSalesChannelWarning",{name:a.name}),verificationInstruction:e("general.typeToConfirm"),verificationText:a.name,confirmText:e("actions.delete"),cancelText:e("actions.cancel")})&&await i(a.id,{onSuccess:()=>{p.success(e("salesChannels.toast.delete"))},onError:m=>{p.error(m.message)}})},[e,s,i]);return u.useMemo(()=>[...t,N.action({actions:a=>{const c=a.row.original.is_default?e("salesChannels.tooltip.cannotDeleteDefault"):void 0;return[[{icon:o.jsx(L,{}),label:e("actions.edit"),onClick:()=>r(`/settings/sales-channels/${a.row.original.id}/edit`)}],[{icon:o.jsx(z,{}),label:e("actions.delete"),onClick:()=>l(a.row.original),disabled:a.row.original.is_default,disabledTooltip:c}]]}})],[t,l,r,e])},me=()=>{const{getWidgets:e}=v();return o.jsx(x,{widgets:{before:e("sales_channel.list.before"),after:e("sales_channel.list.after")},hasOutlet:!0,children:o.jsx(A,{})})};export{me as Component};
