import{u as c}from"./chunk-C76H5USB-ByRPKhW7.js";var _=({prefix:u,pageSize:o=20})=>{const e=c(["offset","q","order","created_at","updated_at"],u),{offset:a,created_at:d,updated_at:s,q:t,order:r}=e;return{searchParams:{limit:o,offset:a?Number(a):0,order:r,created_at:d?JSON.parse(d):void 0,updated_at:s?JSON.parse(s):void 0,q:t},raw:e}},p=({prefix:u,pageSize:o=20})=>{const e=c(["offset","q","order","created_at","updated_at"],u),{offset:a,q:d,order:s,created_at:t,updated_at:r}=e;return{searchParams:{limit:o,offset:a?Number(a):0,order:s,created_at:t?JSON.parse(t):void 0,updated_at:r?JSON.parse(r):void 0,q:d},raw:e}},m=({prefix:u,pageSize:o=20})=>{const e=c(["offset","q","order","created_at","updated_at"],u),{offset:a,q:d,order:s,created_at:t,updated_at:r}=e;return{searchParams:{limit:o,offset:a?Number(a):0,order:s,created_at:t?JSON.parse(t):void 0,updated_at:r?JSON.parse(r):void 0,q:d},raw:e}};export{p as a,m as b,_ as u};
