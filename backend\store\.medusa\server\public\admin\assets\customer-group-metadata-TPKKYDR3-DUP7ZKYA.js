import{M as i}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as n,dQ as p,dS as u,j as d}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var P=()=>{const{id:r}=n(),{customer_group:t,isPending:o,isError:a,error:s}=p(r),{mutateAsync:e,isPending:m}=u(r);if(a)throw s;return d.jsx(i,{metadata:t==null?void 0:t.metadata,hook:e,isPending:o,isMutating:m})};export{P as Component};
