/**
 * Translation keys for API key components
 *
 * These should be added to the respective locale files:
 * - public/locales/en/common.json
 * - public/locales/fr/common.json
 * - public/locales/ja/common.json
 */

export const apiKeyTranslations = {
  en: {
    // Form fields
    name: {
      label: 'Name',
      placeholder: 'Optional',
      serviceKeyLabel: 'Service Key Name',
      serviceKeyPlaceholder: 'My Service Account Key',
      userKeyPlaceholder: 'My Test Key',
    },
    ownerType: {
      label: 'Owned by',
      you: 'You',
      serviceAccount: 'Service account',
    },
    permissionType: {
      label: 'Permissions',
      all: 'All',
      restricted: 'Restricted',
      readOnly: 'Read only',
    },
    project: {
      label: 'Project',
      placeholder: 'Select a project',
    },
    resources: {
      title: 'Resources',
      permissions: 'Permissions',
      models: 'Models',
      modelsPath: '/v1/models',
      modelCapabilities: 'Model capabilities',
      modelCapabilitiesPath:
        '/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations',
      assistants: 'Assistants',
      assistantsPath: '/v1/assistants',
      assistantsModelsPath: '/v1/models (required for Assistants)',
      threads: 'Threads',
      threadsPath: '/v1/threads',
      threadsModelsPath: '/v1/models (required for Threads)',
      evals: 'Evals',
      evalsPath: '/v1/evals',
      fineTuning: 'Fine-tuning',
      fineTuningPath: '/v1/fine_tuning',
      files: 'Files',
      filesPath: '/v1/files',
    },
    resourcePermissions: {
      none: 'None',
      read: 'Read',
      write: 'Write',
    },

    // Form descriptions
    descriptions: {
      serviceAccount:
        'A new bot member (service account) will be added to your project, and an API key will be created.',
      userAccount:
        'This API key is tied to your user and can make requests against the selected project. If you are removed from the organization or project, this key will be disabled.',
      permissionChange: 'Permission changes may take a few minutes to take effect.',
    },

    // Form actions
    actions: {
      cancel: 'Cancel',
      save: 'Save',
      createKey: 'Create secret key',
      submitting: 'Submitting...',
    },

    // Form titles
    titles: {
      create: 'Create new secret key',
      edit: 'Edit secret key',
    },

    // Validation error messages
    validation: {
      required: 'This field is required',
      name: 'Name must contain only letters, numbers, hyphens, and underscores',
    },
  },

  fr: {
    // Form fields
    name: {
      label: 'Nom',
      placeholder: 'Optionnel',
      serviceKeyLabel: 'Nom de la clé de service',
      serviceKeyPlaceholder: 'Ma clé de compte de service',
      userKeyPlaceholder: 'Ma clé de test',
    },
    ownerType: {
      label: 'Appartient à',
      you: 'Vous',
      serviceAccount: 'Compte de service',
    },
    permissionType: {
      label: 'Permissions',
      all: 'Toutes',
      restricted: 'Restreintes',
      readOnly: 'Lecture seule',
    },
    project: {
      label: 'Projet',
      placeholder: 'Sélectionner un projet',
    },
    resources: {
      title: 'Ressources',
      permissions: 'Permissions',
      models: 'Modèles',
      modelsPath: '/v1/models',
      modelCapabilities: 'Capacités du modèle',
      modelCapabilitiesPath:
        '/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations',
      assistants: 'Assistants',
      assistantsPath: '/v1/assistants',
      assistantsModelsPath: '/v1/models (requis pour les Assistants)',
      threads: 'Fils de discussion',
      threadsPath: '/v1/threads',
      threadsModelsPath: '/v1/models (requis pour les Fils de discussion)',
      evals: 'Évaluations',
      evalsPath: '/v1/evals',
      fineTuning: 'Ajustement fin',
      fineTuningPath: '/v1/fine_tuning',
      files: 'Fichiers',
      filesPath: '/v1/files',
    },
    resourcePermissions: {
      none: 'Aucune',
      read: 'Lecture',
      write: 'Écriture',
    },

    // Form descriptions
    descriptions: {
      serviceAccount:
        'Un nouveau membre bot (compte de service) sera ajouté à votre projet, et une clé API sera créée.',
      userAccount:
        "Cette clé API est liée à votre utilisateur et peut effectuer des requêtes sur le projet sélectionné. Si vous êtes retiré de l'organisation ou du projet, cette clé sera désactivée.",
      permissionChange:
        'Les changements de permission peuvent prendre quelques minutes pour prendre effet.',
    },

    // Form actions
    actions: {
      cancel: 'Annuler',
      save: 'Enregistrer',
      createKey: 'Créer une clé secrète',
      submitting: 'Soumission en cours...',
    },

    // Form titles
    titles: {
      create: 'Créer une nouvelle clé secrète',
      edit: 'Modifier la clé secrète',
    },

    // Validation error messages
    validation: {
      required: 'Ce champ est obligatoire',
      name: 'Le nom doit contenir uniquement des lettres, chiffres, tirets et underscores',
    },
  },

  ja: {
    // Form fields
    name: {
      label: '名前',
      placeholder: '任意',
      serviceKeyLabel: 'サービスキー名',
      serviceKeyPlaceholder: 'マイサービスアカウントキー',
      userKeyPlaceholder: 'マイテストキー',
    },
    ownerType: {
      label: '所有者',
      you: 'あなた',
      serviceAccount: 'サービスアカウント',
    },
    permissionType: {
      label: '権限',
      all: 'すべて',
      restricted: '制限付き',
      readOnly: '読み取り専用',
    },
    project: {
      label: 'プロジェクト',
      placeholder: 'プロジェクトを選択',
    },
    resources: {
      title: 'リソース',
      permissions: '権限',
      models: 'モデル',
      modelsPath: '/v1/models',
      modelCapabilities: 'モデル機能',
      modelCapabilitiesPath:
        '/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations',
      assistants: 'アシスタント',
      assistantsPath: '/v1/assistants',
      assistantsModelsPath: '/v1/models (アシスタントに必要)',
      threads: 'スレッド',
      threadsPath: '/v1/threads',
      threadsModelsPath: '/v1/models (スレッドに必要)',
      evals: '評価',
      evalsPath: '/v1/evals',
      fineTuning: 'ファインチューニング',
      fineTuningPath: '/v1/fine_tuning',
      files: 'ファイル',
      filesPath: '/v1/files',
    },
    resourcePermissions: {
      none: 'なし',
      read: '読み取り',
      write: '書き込み',
    },

    // Form descriptions
    descriptions: {
      serviceAccount:
        '新しいボットメンバー（サービスアカウント）がプロジェクトに追加され、APIキーが作成されます。',
      userAccount:
        'このAPIキーはあなたのユーザーに紐づけられ、選択したプロジェクトに対してリクエストを行うことができます。組織やプロジェクトから削除された場合、このキーは無効になります。',
      permissionChange: '権限の変更が反映されるまで数分かかる場合があります。',
    },

    // Form actions
    actions: {
      cancel: 'キャンセル',
      save: '保存',
      createKey: 'シークレットキーを作成',
      submitting: '送信中...',
    },

    // Form titles
    titles: {
      create: '新しいシークレットキーを作成',
      edit: 'シークレットキーを編集',
    },

    // Validation error messages
    validation: {
      required: 'このフィールドは必須です',
      name: '名前には文字、数字、ハイフン、アンダースコアのみを含める必要があります',
    },
  },
};
