---
description: 
globs: 
alwaysApply: false
---
Ensure pnpm is fully reset and updated to the latest versions. Follow these steps:
1. Remove `node_modules`, `pnpm-lock.yaml`, `package-lock.json`, `yarn.lock` `.next`.
2. Clear the pnpm cache using `pnpm store prune`.
3. Optionally, clear the global pnpm store by deleting `$(pnpm store path)`.
4. Reinstall all dependencies using `pnpm install`.
5. Update all packages to their latest versions using `pnpm update --latest`.
Ensure the process is clean, efficient, and reports status updates throughout.
