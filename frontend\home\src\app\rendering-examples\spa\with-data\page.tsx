'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';

// Interface for user data
interface User {
  id: number;
  name: string;
  role: string;
  lastActive: string;
}

// Interface for system status
interface SystemStatus {
  cpu: string;
  memory: string;
  uptime: string;
}

// Interface for the detailed data
interface DetailedData {
  users: User[];
  systemStatus: SystemStatus;
  timestamp: string;
}

export default function SPAWithDataPage() {
  const { t: _t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<DetailedData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch detailed data from client side
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Simulating network delay
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // This would be a real API call in a real application
        const mockData: DetailedData = {
          users: [
            {
              id: 1,
              name: 'Alice <PERSON>',
              role: 'Developer',
              lastActive: new Date().toISOString(),
            },
            { id: 2, name: 'Bob Smith', role: 'Designer', lastActive: new Date().toISOString() },
            {
              id: 3,
              name: '<PERSON> <PERSON>',
              role: 'Product Manager',
              lastActive: new Date().toISOString(),
            },
          ],
          systemStatus: {
            cpu: Math.floor(Math.random() * 100) + '%',
            memory: Math.floor(Math.random() * 100) + '%',
            uptime: Math.floor(Math.random() * 10000) + ' minutes',
          },
          timestamp: new Date().toISOString(),
        };

        setData(mockData);
      } catch {
        setError('Failed to fetch data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="spa" />

      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">SPA with Detailed Data</h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">Client-Side Data Fetching</h2>
          <p className="mb-4">
            In Single Page Applications, data is fetched from the client side after the page has
            loaded. This example demonstrates fetching and displaying more complex data structures
            in a SPA.
          </p>
          <p>
            Notice that there&apos;s always a loading state visible to the user, as the data is
            fetched after the initial page render.
          </p>
        </div>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">User Data</h2>
          {isLoading ? (
            <div className="space-y-4">
              <div className="flex animate-pulse space-x-4">
                <div className="flex-1 space-y-4 py-1">
                  <div className="bg-muted-foreground/20 h-4 w-3/4 rounded"></div>
                  <div className="space-y-2">
                    <div className="bg-muted-foreground/20 h-4 rounded"></div>
                    <div className="bg-muted-foreground/20 h-4 w-5/6 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="text-destructive border-destructive/50 rounded-md border p-4">
              {error}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="bg-card w-full overflow-hidden rounded-md border">
                <thead className="bg-muted">
                  <tr>
                    <th className="px-4 py-2 text-left">ID</th>
                    <th className="px-4 py-2 text-left">Name</th>
                    <th className="px-4 py-2 text-left">Role</th>
                    <th className="px-4 py-2 text-left">Last Active</th>
                  </tr>
                </thead>
                <tbody>
                  {data?.users.map((user) => (
                    <tr key={user.id} className="border-t">
                      <td className="px-4 py-2">{user.id}</td>
                      <td className="px-4 py-2">{user.name}</td>
                      <td className="px-4 py-2">{user.role}</td>
                      <td className="px-4 py-2">{new Date(user.lastActive).toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">System Status</h2>
          {isLoading ? (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-card rounded border p-4">
                  <div className="animate-pulse">
                    <div className="bg-muted-foreground/20 mb-2 h-4 w-1/2 rounded"></div>
                    <div className="bg-muted-foreground/20 h-6 w-1/4 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-destructive border-destructive/50 rounded-md border p-4">
              {error}
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="bg-card rounded border p-4">
                  <h3 className="mb-2 font-medium">CPU Usage</h3>
                  <p className="text-2xl">{data?.systemStatus.cpu}</p>
                </div>
                <div className="bg-card rounded border p-4">
                  <h3 className="mb-2 font-medium">Memory Usage</h3>
                  <p className="text-2xl">{data?.systemStatus.memory}</p>
                </div>
                <div className="bg-card rounded border p-4">
                  <h3 className="mb-2 font-medium">Uptime</h3>
                  <p className="text-2xl">{data?.systemStatus.uptime}</p>
                </div>
              </div>
              <p className="mt-4 text-xs">Generated at: {data?.timestamp}</p>
            </>
          )}
        </div>

        <div className="bg-muted rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">SPA Data Fetching Characteristics</h2>
          <ul className="list-disc space-y-2 pl-5">
            <li>Data is fetched after the page loads, requiring loading states</li>
            <li>Network requests happen in the browser, visible in developer tools</li>
            <li>Can easily implement retry mechanisms and error handling</li>
            <li>Data can be cached in the browser for improved performance</li>
            <li>State is maintained when navigating between pages</li>
          </ul>
        </div>

        <div className="mt-8 flex flex-wrap gap-3">
          <Link href="/rendering-examples/spa" className="text-primary font-medium hover:underline">
            ← Back to Basic SPA Example
          </Link>
        </div>
      </main>
    </div>
  );
}
