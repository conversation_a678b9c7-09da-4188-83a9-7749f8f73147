import{I as j}from"./chunk-JHATTPS3-DKo14Q46.js";import{P as p}from"./chunk-P3UUX2T6-CnJzifYv.js";import{u as _,_ as S}from"./chunk-X3LH6P65-BtKDvzuz.js";import{a as w,j as i,b as h,u as N,r as b,dV as I,H as C,T as P,B as T,L as q,cZ as z,dZ as E,A as O}from"./index-Bwql5Dzz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as A}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as L}from"./chunk-C76H5USB-ByRPKhW7.js";import{u as R}from"./use-prompt-pbDx0Sfe.js";import{P as D}from"./pencil-square-6wRbnn1C.js";import{T as J}from"./trash-BBylvTAG.js";import{C as H}from"./container-Dqi2woPF.js";import{C as v}from"./checkbox-B4pL6X49.js";import{c as $}from"./index-BxZ1678G.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var B=({item:t})=>{const{t:s}=h(),e=R(),{mutateAsync:n}=E(t.id),a=async()=>{await e({title:s("general.areYouSure"),description:s("inventory.deleteWarning"),confirmText:s("actions.delete"),cancelText:s("actions.cancel")})&&await n()};return i.jsx(O,{groups:[{actions:[{icon:i.jsx(D,{}),label:s("actions.edit"),to:`${t.id}/edit`}]},{actions:[{icon:i.jsx(J,{}),label:s("actions.delete"),onClick:a}]}]})},o=$(),M=()=>{const{t}=h();return b.useMemo(()=>[o.display({id:"select",header:({table:s})=>i.jsx(v,{checked:s.getIsSomePageRowsSelected()?"indeterminate":s.getIsAllPageRowsSelected(),onCheckedChange:e=>s.toggleAllPageRowsSelected(!!e)}),cell:({row:s})=>i.jsx(v,{checked:s.getIsSelected(),onCheckedChange:e=>s.toggleSelected(!!e),onClick:e=>{e.stopPropagation()}})}),o.accessor("title",{header:t("fields.title"),cell:({getValue:s})=>{const e=s();return e?i.jsx("div",{className:"flex size-full items-center overflow-hidden",children:i.jsx("span",{className:"truncate",children:e})}):i.jsx(p,{})}}),o.accessor("sku",{header:t("fields.sku"),cell:({getValue:s})=>{const e=s();return e?i.jsx("div",{className:"flex size-full items-center overflow-hidden",children:i.jsx("span",{className:"truncate",children:e})}):i.jsx(p,{})}}),o.accessor("reserved_quantity",{header:t("inventory.reserved"),cell:({getValue:s})=>{const e=s();return Number.isNaN(e)?i.jsx(p,{}):i.jsx("div",{className:"flex size-full items-center overflow-hidden",children:i.jsx("span",{className:"truncate",children:e})})}}),o.accessor("stocked_quantity",{header:t("fields.inStock"),cell:({getValue:s})=>{const e=s();return Number.isNaN(e)?i.jsx(p,{}):i.jsx("div",{className:"flex size-full items-center overflow-hidden",children:i.jsx("span",{className:"truncate",children:e})})}}),o.display({id:"actions",cell:({row:s})=>i.jsx(B,{item:s.original})})],[t])},Y=()=>{const{t}=h(),{stock_locations:s}=z({limit:1e3}),e=[];if(s){const n={type:"select",options:s.map(a=>({label:a.name,value:a.id})),key:"location_id",searchable:!0,label:t("fields.location")};e.push(n)}return e.push({type:"string",key:"material",label:t("fields.material")}),e.push({type:"string",key:"sku",label:t("fields.sku")}),e.push({type:"string",key:"mid_code",label:t("fields.midCode")}),e.push({type:"number",key:"height",label:t("fields.height")}),e.push({type:"number",key:"width",label:t("fields.width")}),e.push({type:"number",key:"length",label:t("fields.length")}),e.push({type:"number",key:"weight",label:t("fields.weight")}),e.push({type:"select",options:[{label:t("fields.true"),value:"true"},{label:t("fields.false"),value:"false"}],key:"requires_shipping",multiple:!1,label:t("fields.requiresShipping")}),e},Z=({pageSize:t=20,prefix:s})=>{const e=L(["id","location_id","q","order","requires_shipping","offset","sku","origin_country","material","mid_code","hs_code","order","weight","width","length","height"],s),{offset:n,weight:a,width:l,length:u,height:c,requires_shipping:m,...r}=e;return{searchParams:{limit:t,offset:n?parseInt(n):void 0,weight:a?JSON.parse(a):void 0,width:l?JSON.parse(l):void 0,length:u?JSON.parse(u):void 0,height:c?JSON.parse(c):void 0,requires_shipping:m?JSON.parse(m):void 0,q:r.q,sku:r.sku,order:r.order,mid_code:r.mid_code,hs_code:r.hs_code,material:r.material,location_levels:{location_id:r.location_id||[]},id:r.id?r.id.split(","):void 0},raw:e}},f=20,F=()=>{const{t}=h(),s=N(),[e,n]=b.useState({}),{searchParams:a,raw:l}=Z({pageSize:f}),{inventory_items:u,count:c,isPending:m,isError:r,error:y}=I({...a}),x=Y(),g=M(),{table:k}=_({data:u??[],columns:g,count:c,enablePagination:!0,getRowId:d=>d.id,pageSize:f,enableRowSelection:!0,rowSelection:{state:e,updater:n}});if(r)throw y;return i.jsxs(H,{className:"divide-y p-0",children:[i.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[i.jsxs("div",{children:[i.jsx(C,{children:t("inventory.domain")}),i.jsx(P,{className:"text-ui-fg-subtle",size:"small",children:t("inventory.subtitle")})]}),i.jsx(T,{size:"small",variant:"secondary",asChild:!0,children:i.jsx(q,{to:"create",children:t("actions.create")})})]}),i.jsx(S,{table:k,columns:g,pageSize:f,count:c,isLoading:m,pagination:!0,search:!0,filters:x,queryObject:l,orderBy:[{key:"title",label:t("fields.title")},{key:"sku",label:t("fields.sku")},{key:"stocked_quantity",label:t("fields.inStock")},{key:"reserved_quantity",label:t("inventory.reserved")}],navigateTo:d=>`${d.id}`,commands:[{action:async d=>{s(`stock?${j}=${Object.keys(d).join(",")}`)},label:t("inventory.stock.action"),shortcut:"i"}]})]})},Ne=()=>{const{getWidgets:t}=w();return i.jsx(A,{widgets:{after:t("inventory_item.list.after"),before:t("inventory_item.list.before")},children:i.jsx(F,{})})};export{Ne as Component};
