'use client';

import Link from 'next/link';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { ThemeToggle } from '@/components/theme-toggle';
import { I18nButton } from '@/components/I18nButton';

export default function RenderingExamples() {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto px-4 py-8">
      <header className="mb-8">
        <div className="mb-4 flex items-center justify-between">
          <Link href="/" className="text-primary hover:underline">
            ← {t('nav.home', 'Back to Home')}
          </Link>

          <div className="flex items-center space-x-2">
            <ThemeToggle />
            <I18nButton />
          </div>
        </div>
        <h1 className="mb-2 text-3xl font-bold">Next.js Rendering Methods</h1>
        <p className="text-muted-foreground">
          Next.js supports different rendering methods to optimize for various use cases. Each
          method has its own benefits and trade-offs.
        </p>
      </header>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <RenderingMethodCard
          title="Server-Side Rendering (SSR)"
          description="Pages are rendered on-demand, on the server, for each request."
          benefits={[
            'Always fresh data on every request',
            'Excellent SEO as search engines see complete HTML',
            'Fast time-to-content for users',
          ]}
          link="/rendering-examples/ssr"
        />

        <RenderingMethodCard
          title="Static Site Generation (SSG)"
          description="Pages are pre-rendered at build time and served as static HTML."
          benefits={[
            'Very fast page loads',
            'Lower server costs',
            'SEO friendly',
            'Can be served from CDN',
          ]}
          link="/rendering-examples/ssg"
        />

        <RenderingMethodCard
          title="Client-Side Rendering (CSR)"
          description="Initial page is minimal HTML, with JavaScript rendering content in the browser."
          benefits={[
            'Reduced server load',
            'Rich interactivity',
            'Works well for private, user-specific pages',
          ]}
          link="/rendering-examples/csr"
        />

        <RenderingMethodCard
          title="Incremental Static Regeneration (ISR)"
          description="Static pages that can be regenerated after deployment at specified intervals."
          benefits={[
            'Combines benefits of SSG and SSR',
            'Fast page loads with fresh data',
            'Scales well for large sites',
          ]}
          link="/rendering-examples/isr"
        />

        <RenderingMethodCard
          title="Single Page Application (SPA)"
          description="Entire application runs in the browser with client-side routing."
          benefits={[
            'Smooth, app-like user experience',
            'No page reloads during navigation',
            'Maintains application state between views',
            'Offline capabilities possible',
          ]}
          link="/rendering-examples/spa"
          className="md:col-span-2"
        />
      </div>
    </div>
  );
}

interface RenderingMethodCardProps {
  title: string;
  description: string;
  benefits: string[];
  link: string;
  className?: string;
}

function RenderingMethodCard({
  title,
  description,
  benefits,
  link,
  className = '',
}: RenderingMethodCardProps) {
  const { t } = useTranslation();

  return (
    <div className={`bg-card rounded-lg border p-6 transition-shadow hover:shadow-md ${className}`}>
      <h2 className="mb-2 text-xl font-semibold">{title}</h2>
      <p className="text-muted-foreground mb-4">{description}</p>

      <h3 className="mb-2 font-medium">Benefits:</h3>
      <ul className="mb-4 list-disc space-y-1 pl-5">
        {benefits.map((benefit, index) => (
          <li key={index} className="text-muted-foreground">
            {benefit}
          </li>
        ))}
      </ul>

      <Link
        href={link}
        className="bg-primary hover:bg-primary/90 text-primary-foreground inline-flex items-center rounded-md px-4 py-2 text-sm font-medium shadow-sm focus:outline-none"
      >
        {t('rendering.viewExample', 'View Example')}
      </Link>
    </div>
  );
}
