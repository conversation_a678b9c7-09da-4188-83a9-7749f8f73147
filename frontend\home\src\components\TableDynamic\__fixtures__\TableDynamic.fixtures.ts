import type { ColumnConfig, DataRecord } from '../types';

// Types only version without JSX for the fixtures
interface SimpleData extends DataRecord {
  id: string;
  name: string;
  category: string;
  price: number;
  stock: number;
}

// Generate standard test data
export const generateData = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `item-${i + 1}`,
    name: `Product ${i + 1}`,
    category: ['Electronics', 'Clothing', 'Food', 'Furniture', 'Books'][
      Math.floor(Math.random() * 5)
    ],
    price: Math.floor(Math.random() * 1000) + 10,
    stock: Math.floor(Math.random() * 100),
    rating: Number((Math.random() * 5).toFixed(1)),
    status: ['In Stock', 'Low Stock', 'Out of Stock'][Math.floor(Math.random() * 3)],
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
    updatedAt: new Date(),
  }));
};

// Default dataset for stories
export const defaultData = generateData(20);

// Define a simplified version of columns without JSX for the fixtures
export const defaultColumns: Partial<ColumnConfig>[] = [
  {
    id: 'name',
    header: 'Product Name',
    accessorKey: 'name',
    enableSorting: true,
    enableFiltering: true,
  },
  {
    id: 'category',
    header: 'Category',
    accessorKey: 'category',
    enableSorting: true,
    enableFiltering: true,
  },
  {
    id: 'price',
    header: 'Price',
    accessorKey: 'price',
    enableSorting: true,
    enableFiltering: true,
  },
  {
    id: 'stock',
    header: 'Stock',
    accessorKey: 'stock',
    enableSorting: true,
    enableFiltering: true,
  },
  {
    id: 'rating',
    header: 'Rating',
    accessorKey: 'rating',
    enableSorting: true,
    enableFiltering: true,
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    enableSorting: true,
    enableFiltering: true,
  },
  {
    id: 'createdAt',
    header: 'Created At',
    accessorKey: 'createdAt',
    enableSorting: true,
    enableFiltering: true,
  },
  {
    id: 'actions',
    header: 'Actions',
    accessorKey: 'id',
    enableSorting: false,
    enableFiltering: false,
  },
];

// Data with varied values for specific test cases
export const priceRangeData = defaultData.map((item, index) => {
  if (index < 5) return { ...item, price: Math.floor(Math.random() * 100) + 10 }; // Low price
  if (index < 10) return { ...item, price: Math.floor(Math.random() * 200) + 100 }; // Medium price
  if (index < 15) return { ...item, price: Math.floor(Math.random() * 500) + 300 }; // High price
  return { ...item, price: Math.floor(Math.random() * 1000) + 800 }; // Very high price
});

// Data with varied statuses to test filtering
export const statusVariedData = [
  ...Array(10)
    .fill(0)
    .map((_, i) => ({ ...defaultData[i], status: 'In Stock' })),
  ...Array(5)
    .fill(0)
    .map((_, i) => ({ ...defaultData[i + 10], status: 'Low Stock' })),
  ...Array(5)
    .fill(0)
    .map((_, i) => ({ ...defaultData[i + 15], status: 'Out of Stock' })),
];

// Simple dataset for basic examples
export const simpleData: SimpleData[] = [
  { id: '1', name: 'Laptop', category: 'Electronics', price: 1200, stock: 45 },
  { id: '2', name: 'T-shirt', category: 'Clothing', price: 25, stock: 200 },
  { id: '3', name: 'Apples', category: 'Food', price: 3, stock: 500 },
  { id: '4', name: 'Chair', category: 'Furniture', price: 150, stock: 30 },
  { id: '5', name: 'Book', category: 'Books', price: 15, stock: 100 },
];

// Simple columns for basic examples
export const simpleColumns: Partial<ColumnConfig>[] = [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    enableSorting: true,
  },
  {
    id: 'category',
    header: 'Category',
    accessorKey: 'category',
  },
  {
    id: 'price',
    header: 'Price ($)',
    accessorKey: 'price',
  },
  {
    id: 'stock',
    header: 'Stock',
    accessorKey: 'stock',
  },
];

// Edge case data for testing boundary conditions
export const edgeCaseData = [
  { id: '1', name: '', category: 'Electronics', price: 0, stock: 0, status: 'Out of Stock' },
  {
    id: '2',
    name: 'Product with extremely long name that might cause layout issues if not handled properly',
    category: 'Clothing',
    price: 25,
    stock: 200,
    status: 'In Stock',
  },
  { id: '3', name: null, category: 'Food', price: 3, stock: 500, status: 'In Stock' },
  { id: '4', name: 'Chair', category: undefined, price: 150, stock: 30, status: 'Low Stock' },
  { id: '5', name: 'Book', category: 'Books', price: -10, stock: -5, status: 'Out of Stock' },
  {
    id: '6',
    name: '<script>alert("XSS")</script>',
    category: 'Books',
    price: 15,
    stock: 100,
    status: 'In Stock',
  },
];

// Large dataset for testing virtualization
export const largeDataset = generateData(1000);

// Column configurations for different variants
export const variantColumns = {
  sticky: defaultColumns.map((col, index) =>
    index === 0
      ? { ...col, sticky: 'left' }
      : index === defaultColumns.length - 1
        ? { ...col, sticky: 'right' }
        : col,
  ),
  hidden: defaultColumns.map((col, index) => (index === 2 ? { ...col, hidden: true } : col)),
  truncated: defaultColumns.map((col) => ({ ...col, truncate: true })),
  resizable: defaultColumns.map((col) => ({ ...col, enableResizing: true })),
  noSort: defaultColumns.map((col) => ({ ...col, enableSorting: false })),
  noFilter: defaultColumns.map((col) => ({ ...col, enableFiltering: false })),
};

// Feature-specific configurations for stories
export const featureConfigs = {
  pagination: {
    enabled: true,
    pageSizeOptions: [5, 10, 20, 50],
    initialPageSize: 10,
    showPageSizeSelector: true,
    showPageNavigator: true,
    showPageInfo: true,
  },
  sorting: {
    enabled: true,
    maxSortColumns: 3,
  },
  filtering: {
    enabled: true,
    showGlobalFilter: true,
    debounce: true,
  },
  columnVisibility: {
    enabled: true,
    showToggle: true,
  },
  rowSelection: {
    enabled: true,
    mode: 'multi' as const,
  },
  highlighting: {
    hoverHighlight: true,
    alternateRowHighlight: false,
  },
};

// Internationalization configurations
export const i18nConfigs = {
  english: {
    i18nNamespace: undefined, // Default namespace
    i18nPrefix: 'tableDynamic',
  },
  french: {
    i18nNamespace: undefined,
    i18nPrefix: 'tableDynamic',
  },
  japanese: {
    i18nNamespace: undefined,
    i18nPrefix: 'tableDynamic',
  },
};
