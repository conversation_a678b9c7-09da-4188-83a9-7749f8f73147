import React from 'react';
import Image from 'next/image';
import { cn } from "@/lib/utils";
import { 
  Card, 
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

export interface CardCouponProps {
  id: string;
  brandLogoUrl: string;
  brandName: string;
  discountType: 'amount' | 'percentage' | 'max-percentage';
  discountValue: number;
  minPurchase?: number;
  maxDiscount?: number;
  claimed?: boolean;
  className?: string;
  onSeeDetails?: (id: string) => void;
  onClaimCoupon?: (id: string) => void;
  isClaimingCoupon?: boolean;
  showTermsOnly?: boolean;
}

export const CardCoupon = ({
  id,
  brandLogoUrl,
  brandName,
  discountType,
  discountValue,
  minPurchase,
  maxDiscount,
  claimed = false,
  className,
  onSeeDetails,
  onClaimCoupon,
  isClaimingCoupon = false,
  showTermsOnly = false,
}: CardCouponProps) => {
  const handleSeeDetails = () => {
    if (onSeeDetails) {
      onSeeDetails(id);
    }
  };

  const handleClaimCoupon = () => {
    if (onClaimCoupon && !isClaimingCoupon && !claimed) {
      onClaimCoupon(id);
    }
  };

  const renderDiscountTitle = () => (
    <div className="text-gray-600 text-sm">ส่วนลด</div>
  );

  const renderDiscountValue = () => {
    if (discountType === 'amount') {
      return (
        <div className="text-3xl font-bold text-blue-900">
          {new Intl.NumberFormat('th-TH').format(discountValue)} บาท
        </div>
      );
    } else if (discountType === 'percentage') {
      return (
        <div className="text-3xl font-bold text-blue-900">
          {discountValue}%
        </div>
      );
    } else if (discountType === 'max-percentage') {
      return (
        <div className="text-3xl font-bold text-blue-900">
          สูงสุด {discountValue}%
        </div>
      );
    }
    return null;
  };

  const renderPurchaseRequirement = () => {
    if (discountType === 'amount' && minPurchase) {
      return (
        <div className="text-xs text-gray-500">
          เมื่อซื้อสินค้าขั้นต่ำ ฿{new Intl.NumberFormat('th-TH').format(minPurchase)}
        </div>
      );
    } else if ((discountType === 'percentage' || discountType === 'max-percentage') && minPurchase) {
      return (
        <div className="text-xs text-gray-500">
          ซื้อขั้นต่ำ ฿{new Intl.NumberFormat('th-TH').format(minPurchase)}
          {maxDiscount && ` ลดสูงสุด ฿${new Intl.NumberFormat('th-TH').format(maxDiscount)}`}
        </div>
      );
    }
    return null;
  };

  const claimButtonText = claimed ? 'เก็บคูปองแล้ว' : 'เก็บคูปองเลย!';
  const claimButtonClass = claimed 
    ? 'bg-white text-gray-400 border border-gray-300 hover:bg-gray-50' 
    : 'bg-blue-900 text-white hover:bg-blue-800';

  return (
    <Card
  className={cn(
    "relative overflow-hidden border border-gray-200 ",
    "rounded-xl w-full md:w-[444px] min-h-[168px] bg-white z-0", // ปรับขนาดให้ responsive
    // ,
    className
  )}
  data-testid="card-coupon"
>
  {/* Notch left */}
  <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-12 h-12 bg-[#f5f5f5] rounded-full z-10 " />

  {/* Notch right */}
  <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-12 h-12 bg-[#f5f5f5] rounded-full z-10 " />

      <CardContent className="p-4 md:p-6 flex flex-row h-full">
        {/* Brand logo section */}
        <div className="w-1/3 flex items-center justify-start relative pr-6"> {/* เพิ่ม padding-right */}
          <div className="relative h-[70px] w-[70px] md:h-[100px] md:w-[100px]"> {/* ปรับขนาดรูปให้เล็กลงบนมือถือ */}
            <Image
              src={brandLogoUrl}
              alt={brandName}
              fill
              className="object-contain"
              sizes="(max-width: 768px) 70px, 100px"
              data-testid="coupon-brand-logo"
            />
          </div>
          {/* Vertical dotted line */}
          <div className="absolute right-3 md:right-4 top-0 h-full"> {/* ปรับตำแหน่งเส้นประ */}
            <div className="h-full flex flex-col justify-around items-center">
              {[...Array(6)].map((_, i) => (
                <div 
                  key={i} 
                  className="w-[1px] h-[10px] md:h-[12px] border-r border-[#F0F0F0]" 
                />
              ))}
            </div>
          </div>
        </div>
        {/* Discount information and actions section */}
        <div className="w-2/3 flex flex-col justify-between pl-2 md:pl-4">
          <div className="space-y-2" data-testid="coupon-discount-info">
            {renderDiscountTitle()}
            <div className="text-2xl md:text-3xl font-bold text-blue-900">
              {renderDiscountValue()}
            </div>
            <div className="text-[11px] md:text-xs text-gray-500">
              {renderPurchaseRequirement()}
            </div>
          </div>
          
          {/* ปรับส่วนปุ่ม */}
          <div className="flex flex-wrap gap-2 mt-4 w-full" data-testid="coupon-actions">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSeeDetails}
              className="hidden md:inline-flex rounded-lg h-10 text-gray-600 border-gray-200 bg-[#F6F7F9] hover:bg-gray-50 hover:text-gray-700 text-sm"
              data-testid="see-details-button"
            >
              เงื่อนไข
            </Button>
            
            {!showTermsOnly && (
              <Button
                size="sm"
                onClick={handleClaimCoupon}
                disabled={isClaimingCoupon || claimed}
                className={cn(
                  "rounded-lg h-10 font-normal text-sm",
                  claimButtonClass,
                  isClaimingCoupon && "opacity-70 cursor-wait"
                )}
                data-testid="claim-coupon-button"
              >
                {claimButtonText}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const CardCouponSkeleton = () => (
  <Card className="relative overflow-hidden border border-gray-200  rounded-xl w-[444px] h-[168px] bg-white z-0">
    <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-6 h-6 bg-white border border-gray-200 rounded-full z-10" />
    <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-6 h-6 bg-white border border-gray-200 rounded-full z-10" />
    
    <CardContent className="p-4 flex flex-col md:flex-row">
      <div className="w-full md:w-1/3 flex items-center justify-center md:justify-start mb-4 md:mb-0">
        <Skeleton className="h-12 w-32" />
      </div>
      
      <div className="w-full md:w-2/3 flex flex-col">
        <div className="mb-2">
          <Skeleton className="h-4 w-16 mb-1" />
          <Skeleton className="h-8 w-32 mb-1" />
          <Skeleton className="h-3 w-48" />
        </div>
        
        <div className="flex flex-wrap gap-2 mt-1">
          <Skeleton className="h-10 w-24 rounded-lg" />
          <Skeleton className="h-10 w-32 rounded-lg" />
        </div>
      </div>
    </CardContent>
  </Card>
);

export default CardCoupon;