import { SectionHeroProps } from '../section-hero';
import { HeroCarouselSlide } from '../section-hero-carousel';

/**
 * ข้อมูลจำลองสำหรับ Hero Section แบบต่างๆ
 */

// ข้อมูลจำลองสำหรับ Hero Section ทั่วไป
export const heroFixtures: Record<string, SectionHeroProps> = {
  // รูปแบบ Default
  default: {
    variant: 'default',
    title: 'ศูนย์รวมอุปกรณ์ไฟฟ้าครบวงจร',
    subtitle: 'แสงธรณเนครินทร์',
    description: 'เราจำหน่ายอุปกรณ์ไฟฟ้าคุณภาพสูง ทั้งสำหรับบ้าน อาคาร และโรงงานอุตสาหกรรม ครอบคลุมทั้งแผงสายไฟ หลอดไฟ เบรกเกอร์ ตู้คอนโทรล และอุปกรณ์ติดตั้งไฟฟ้าทุกชนิด รับประกันของแท้ ได้มาตรฐาน มอก.',
    image: '/images/electrical-supplies.png',
    height: 'lg',
    contentAlignment: 'left',
    buttons: [
      {
        text: 'เลือกซื้อสินค้าของเรา',
        href: '/products',
        variant: 'primary',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        )
      },
      {
        text: 'ติดต่อเรา',
        href: '/contact',
        variant: 'outline',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        )
      }
    ]
  },
  
  // รูปแบบ Banner (เหมือนรูปที่ 2)
  banner: {
    variant: 'banner',
    title: 'หมวดหมู่ทั้งหมด',
    breadcrumbs: [
      {
        text: 'หน้าแรก',
        href: '/'
      },
      {
        text: 'หมวดหมู่ทั้งหมด',
        href: '/categories'
      }
    ],
    height: 'xs',
    contentAlignment: 'left'
  },
  
  // รูปแบบ Centered (เหมือนรูปที่ 3)
  centered: {
    variant: 'centered',
    title: 'โคมไฟและหลอดไฟ',
    subtitle: 'หน้าแรก / โคมไฟและหลอดไฟ',
    description: 'เปลี่ยนบรรยากาศที่บ้านและที่ทำงานของคุณด้วยโคมไฟสไตล์สวยและหลอดไฟคุณภาพสูง ไม่ว่าจะเป็นแสงสีขาวสว่างเพื่อความสดใส หรือแสงวอร์มโทนเพื่อความอบอุ่น เรามีให้เลือกหลากหลายสไตล์ ทั้งโมเดิร์น มินิมอล และคลาสสิค ตอบโจทย์ทุกการใช้งาน พร้อมประหยัดพลังงานและทนทานต่อการใช้งานยาวนาน',
    image: '/images/lighting-fixtures.jpg',
    backgroundImage: '/images/dark-bg.jpg',
    height: 'lg',
    contentAlignment: 'center',
    buttons: [
      {
        text: 'ขอใบเสนอราคา',
        href: '/quote',
        variant: 'primary'
      }
    ]
  },
  
  // รูปแบบ Card (เหมือนรูปที่ 4)
  card: {
    variant: 'card',
    title: 'BANGKOK CABLE',
    subtitle: 'ระบบไฟฟ้าและความปลอดภัย',
    logo: (
      <img src="/images/bcc-logo.png" alt="Bangkok Cable Logo" className="h-12 w-12" />
    ),
    description: 'ผู้ผลิตสายไฟคุณภาพระดับโลก มาตรฐาน ISO 9001 รับประกันคุณภาพทุกเส้น',
    stats: [
      {
        value: '100%',
        label: 'การดูแลลูกค้า'
      },
      {
        value: '382',
        label: 'รายการสินค้า'
      },
      {
        value: '38k',
        label: 'ขายแล้วทั้งหมด'
      }
    ],
    buttons: [
      {
        text: 'ดูสินค้าทั้งหมด',
        href: '/products/bangkok-cable',
        variant: 'primary'
      }
    ]
  },
  
  // รูปแบบ Features (เหมือนรูปที่ 5)
  features: {
    variant: 'features',
    title: 'ครอบคลุมเรื่องไฟฟ้าให้เราดูแลคุณ',
    description: 'เราจำหน่ายสินค้าเกี่ยวไฟฟ้ามากมาย อาทิเช่น อุปกรณ์ไฟฟ้า เครื่องมือช่าง สวิทช์ ปลั๊ก สายไฟ หลอดไฟ โคมไฟ และอุปกรณ์ซ่อมแซง เป็นต้น เรามุ่งหวังว่าลูกค้าทุกคนสามารถเลือกซื้ออุปกรณ์ไฟฟ้าและเครื่องมือได้อย่างครบวงจรในที่เดียว และมีทีมงานบริการที่ช่วยเหลือให้คำปรึกษาเกี่ยวกับบริษัทของเรา',
    backgroundImage: '/images/electric-panel-bg.jpg',
    height: 'lg',
    contentAlignment: 'center',
    stats: [
      {
        value: '40,000+',
        label: 'สินค้าในคลัง',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
        )
      },
      {
        value: '100%',
        label: 'ความพึงพอใจลูกค้า',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      },
      {
        value: '20+',
        label: 'ประสบการณ์',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
      }
    ],
    buttons: [
      {
        text: 'ติดต่อเรา',
        href: '/contact',
        variant: 'primary',
        icon: (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        )
      }
    ]
  },
  
  // รูปแบบ Split
  split: {
    variant: 'split',
    title: 'ระบบความปลอดภัย',
    subtitle: 'หน้าแรก / ระบบความปลอดภัย',
    description: 'อุปกรณ์ความปลอดภัยระดับพรีเมียม สำหรับบ้านพักอาศัย อาคารสำนักงาน และโรงงานอุตสาหกรรม ทั้งกล้องวงจรปิด เครื่องแจ้งเตือนควัน และระบบเตือนผู้บุกรุก',
    image: '/images/security-system.jpg',
    height: 'lg',
    contentAlignment: 'left',
    buttons: [
      {
        text: 'ดูผลิตภัณฑ์',
        href: '/products/security',
        variant: 'primary'
      },
      {
        text: 'ขอคำปรึกษา',
        href: '/consultation',
        variant: 'outline'
      }
    ]
  }
};

// ข้อมูลจำลองสำหรับ Hero Carousel
export const carouselFixtures: HeroCarouselSlide[] = [
  {
    title: 'ศูนย์รวมอุปกรณ์ไฟฟ้าครบวงจร',
    subtitle: 'แสงธรณเนครินทร์',
    description: 'เราจำหน่ายอุปกรณ์ไฟฟ้าคุณภาพสูง ทั้งสำหรับบ้าน อาคาร และโรงงานอุตสาหกรรม ครอบคลุมทั้งแผงสายไฟ หลอดไฟ เบรกเกอร์ ตู้คอนโทรล และอุปกรณ์ติดตั้งไฟฟ้าทุกชนิด รับประกันของแท้ ได้มาตรฐาน มอก.',
    image: '/images/electrical-supplies.png',
    primaryButton: {
      text: 'เลือกซื้อสินค้าของเรา',
      href: '/products',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      )
    },
    secondaryButton: {
      text: 'ติดต่อเรา',
      href: '/contact',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      )
    },
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Product",
      "name": "อุปกรณ์ไฟฟ้าคุณภาพสูง",
      "description": "อุปกรณ์ไฟฟ้าคุณภาพสูงสำหรับบ้าน อาคาร และโรงงานอุตสาหกรรม",
      "image": "/images/electrical-supplies.png"
    }
  },
  {
    title: 'โปรโมชั่นพิเศษประจำเดือน',
    subtitle: 'ลดสูงสุด 50%',
    description: 'เฉพาะเดือนนี้เท่านั้น! รับส่วนลดพิเศษสำหรับสินค้าในกลุ่มหลอดไฟ LED, สายไฟ และเบรกเกอร์ คุณภาพสูง มาตรฐาน มอก. รับประกันนานสูงสุด 5 ปี',
    image: '/images/promotion-electrical.png',
    primaryButton: {
      text: 'ช้อปเลย',
      href: '/promotion',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  },
  {
    title: 'อุปกรณ์คุณภาพจากแบรนด์ชั้นนำ',
    subtitle: 'พันธมิตรของเรา',
    description: 'เราเป็นตัวแทนจำหน่ายอย่างเป็นทางการของแบรนด์ชั้นนำระดับโลก ได้แก่ Schneider, ABB, Philips, Panasonic และอีกมากมาย รับประกันสินค้าของแท้ 100% พร้อมบริการหลังการขายครบวงจร',
    image: '/images/electrical-brands.png',
    primaryButton: {
      text: 'ดูสินค้าทั้งหมด',
      href: '/brands',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      )
    }
  }
];

export default {
  heroFixtures,
  carouselFixtures
};