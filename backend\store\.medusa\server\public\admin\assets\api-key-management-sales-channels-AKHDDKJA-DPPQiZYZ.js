import{V as h}from"./chunk-F6ZOHZVB-CmkCPNYI.js";import{u as E,a as F,b as M,c as P}from"./chunk-UVGNHHSZ-DRARdeex.js";import{a2 as D,ad as k,a4 as H,R as z,f1 as B,j as e,b as C,a8 as I,a9 as N,r as f,f5 as V,J as L,t as p,E as G,B as x,cD as J,k as O}from"./index-Bwql5Dzz.js";import{D as Q,c as X}from"./chunk-MGPZHEOT-CqdSNFtj.js";import{K as Z}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as o,u as $}from"./chunk-JGQGO74V-DtHO1ucg.js";import{C as q}from"./checkbox-B4pL6X49.js";import"./chunk-FFVOUYTF-DR1d4TPs.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./index-BxZ1678G.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./table-BDZtqXjX.js";import"./arrow-up-mini-D5bOKjDW.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./x-mark-mini-DvSTI7zK.js";import"./triangle-left-mini-Bu6679Aa.js";import"./prompt-BsR9zKsn.js";var U=D({sales_channel_ids:k(H()).min(1)}),S=50,y="sc_add",W=({apiKey:l,preSelected:a=[]})=>{const{t:s}=C(),{handleSuccess:r}=$(),t=I({defaultValues:{sales_channel_ids:[]},resolver:N(U)}),{setValue:i}=t,[d,c]=f.useState({}),{mutateAsync:m,isPending:g}=V(l),j=E({pageSize:S,prefix:y}),b=ee(),T=F(),_=M(),{sales_channels:v,count:K,isPending:A}=L({...j},{placeholderData:O}),R=n=>{const u=Object.keys(n);i("sales_channel_ids",u,{shouldDirty:!0,shouldTouch:!0}),c(n)},w=t.handleSubmit(async n=>{await m(n.sales_channel_ids,{onSuccess:()=>{p.success(s("apiKeyManagement.salesChannels.successToast",{count:n.sales_channel_ids.length})),r()},onError:u=>{p.error(u.message)}})});return e.jsx(o.Form,{form:t,children:e.jsxs(Z,{onSubmit:w,className:"flex h-full flex-col",children:[e.jsxs(o.Header,{children:[e.jsx(o.Title,{asChild:!0,children:e.jsx(h,{children:s("apiKeyManagement.salesChannels.title")})}),e.jsx(o.Description,{asChild:!0,children:e.jsx(h,{children:s("apiKeyManagement.salesChannels.description")})}),e.jsx("div",{className:"flex items-center justify-end gap-x-2",children:t.formState.errors.sales_channel_ids&&e.jsx(G,{variant:"error",children:t.formState.errors.sales_channel_ids.message})})]}),e.jsx(o.Body,{className:"flex flex-1 flex-col overflow-auto",children:e.jsx(Q,{data:v,columns:b,filters:T,getRowId:n=>n.id,rowCount:K,layout:"fill",emptyState:_,isLoading:A,rowSelection:{state:d,onRowSelectionChange:R,enableRowSelection:n=>!a.includes(n.id)},prefix:y,pageSize:S,autoFocusSearch:!0})}),e.jsx(o.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(o.Close,{asChild:!0,children:e.jsx(x,{size:"small",variant:"secondary",children:s("actions.cancel")})}),e.jsx(x,{size:"small",type:"submit",isLoading:g,children:s("actions.save")})]})})]})})},Y=X(),ee=()=>{const{t:l}=C(),a=P();return f.useMemo(()=>[Y.select({cell:({row:s})=>{const r=!s.getCanSelect(),t=s.getIsSelected()||r;return e.jsx(J,{content:l("apiKeyManagement.salesChannels.alreadyAddedTooltip"),showTooltip:r,children:e.jsx("div",{children:e.jsx(q,{checked:t,disabled:r,onCheckedChange:i=>s.toggleSelected(!!i),onClick:i=>{i.stopPropagation()}})})})}}),...a],[l,a])},Te=()=>{var c;const{id:l}=z(),{api_key:a,isLoading:s,isError:r,error:t}=B(l),i=(c=a==null?void 0:a.sales_channels)==null?void 0:c.map(m=>m.id),d=!s&&a;if(r)throw t;return e.jsx(o,{children:d&&e.jsx(W,{apiKey:l,preSelected:i})})};export{Te as Component};
