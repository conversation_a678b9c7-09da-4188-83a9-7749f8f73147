import{aM as y,j as K}from"./index-Bwql5Dzz.js";var l=y.forwardRef(({onSubmit:a,onKeyDown:o,...t},f)=>{const e=r=>{r.preventDefault(),a==null||a(r)},s=r=>{if(r.key==="Enter"){if(r.target instanceof HTMLTextAreaElement&&!(r.metaKey||r.ctrlKey))return;r.preventDefault(),(r.metaKey||r.ctrlKey)&&e(r)}};return K.jsx("form",{...t,onSubmit:e,onKeyDown:o??s,ref:f})});l.displayName="KeyboundForm";export{l as K};
