'use client';

import * as React from 'react';
import { createContext, useContext, useState, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getExpandedRowModel,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  RowSelectionState,
  ExpandedState,
  PaginationState,
  OnChangeFn,
  ColumnDef,
  Row,
  Table,
} from '@tanstack/react-table';
import type {
  DataRecord,
  ColumnConfig,
  FilteringConfig,
  SortingConfig,
  PaginationConfig,
  ColumnVisibilityConfig,
  RowSelectionConfig,
  RowExpansionConfig,
} from './types';

/**
 * Table context state interface
 */
export interface TableContextState<T extends DataRecord = DataRecord> {
  // Core table instance
  table: Table<T>;

  // Column-related state
  columns: ColumnDef<T>[];
  columnFilters: ColumnFiltersState;
  setColumnFilters: OnChangeFn<ColumnFiltersState>;
  columnVisibility: VisibilityState;
  setColumnVisibility: OnChangeFn<VisibilityState>;

  // Sort-related state
  sorting: SortingState;
  setSorting: OnChangeFn<SortingState>;

  // Filter-related state
  globalFilter: string;
  setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;

  // Pagination-related state
  pagination: PaginationState;
  setPagination: OnChangeFn<PaginationState>;

  // Selection-related state
  rowSelection: RowSelectionState;
  setRowSelection: OnChangeFn<RowSelectionState>;

  // Expansion-related state
  expanded: ExpandedState;
  setExpanded: OnChangeFn<ExpandedState>;

  // View states
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;

  // Virtualization states
  rowVirtualizer: unknown | null;

  // UI states
  hoveredRowId: string | null;
  setHoveredRowId: React.Dispatch<React.SetStateAction<string | null>>;
  hoveredColumnId: string | null;
  setHoveredColumnId: React.Dispatch<React.SetStateAction<string | null>>;

  // Event handlers
  handleRowClick: (row: Row<T>) => void;
  handleCellClick: (row: Row<T>, columnId: string) => void;
}

// Create the context with a default empty value
const TableContext = createContext<TableContextState | null>(null);

/**
 * Hook to use the table context
 */
export const useTableContext = <T extends DataRecord = DataRecord>() => {
  const context = useContext(TableContext) as TableContextState<T>;
  if (!context) {
    throw new Error('useTableContext must be used within a TableContextProvider');
  }
  return context;
};

/**
 * Table context provider props
 */
export interface TableContextProviderProps<T extends DataRecord = DataRecord> {
  children: React.ReactNode;
  data: T[];
  columns: ColumnConfig<T>[];
  sorting?: SortingConfig;
  filtering?: FilteringConfig;
  pagination?: PaginationConfig;
  columnVisibility?: ColumnVisibilityConfig;
  rowSelection?: RowSelectionConfig;
  rowExpansion?: RowExpansionConfig;
  onRowClick?: (row: Row<T>) => void;
  onCellClick?: (row: Row<T>, columnId: string) => void;
}

/**
 * Table context provider component
 */
export const TableContextProvider = <T extends DataRecord = DataRecord>({
  children,
  data,
  columns,
  sorting,
  filtering,
  pagination,
  columnVisibility,
  rowSelection,
  rowExpansion,
  onRowClick,
  onCellClick,
}: TableContextProviderProps<T>) => {
  // State for column filters
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  // State for global filter
  const [globalFilter, setGlobalFilter] = useState('');

  // State for sorting
  const [sortingState, setSortingState] = useState<SortingState>(sorting?.initialSorting || []);

  // State for pagination
  const [paginationState, setPaginationState] = useState<PaginationState>({
    pageIndex: pagination?.initialPageIndex || 0,
    pageSize: pagination?.initialPageSize || 10,
  });

  // State for column visibility
  const [columnVisibilityState, setColumnVisibilityState] = useState<VisibilityState>(
    columnVisibility?.initialVisibility || {},
  );

  // State for row selection
  const [rowSelectionState, setRowSelectionState] = useState<RowSelectionState>(
    rowSelection?.initialRowSelection || {},
  );

  // State for row expansion
  const [expandedState, setExpandedState] = useState<ExpandedState>(
    rowExpansion?.initialExpandedRowIds || {},
  );

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [hoveredRowId, setHoveredRowId] = useState<string | null>(null);
  const [hoveredColumnId, setHoveredColumnId] = useState<string | null>(null);

  // Convert ColumnConfig[] to ColumnDef[]
  const columnDefs = useMemo(
    () =>
      columns.map((col) => ({
        id: col.id,
        accessorKey: col.accessorKey,
        header: col.header,
        cell: col.cell,
        enableSorting: col.enableSorting,
        enableColumnFilter: col.enableFiltering,
        size: typeof col.width === 'number' ? col.width : undefined,
        minSize: col.minWidth,
        maxSize: col.maxWidth,
        enableResizing: col.enableResizing,
        meta: col.meta,
      })) as ColumnDef<T>[],
    [columns],
  );

  // Create the table instance
  const table = useReactTable({
    data,
    columns: columnDefs,
    state: {
      sorting: sortingState,
      columnFilters,
      globalFilter,
      pagination: paginationState,
      columnVisibility: columnVisibilityState,
      rowSelection: rowSelectionState,
      expanded: expandedState,
    },

    // Feature flags
    enableSorting: sorting?.enabled ?? false,
    enableFilters: filtering?.enabled ?? false,
    enableColumnFilters: filtering?.enabled ?? false,
    enableGlobalFilter: (filtering?.enabled && filtering?.showGlobalFilter) ?? false,
    manualPagination: false,
    enableRowSelection: rowSelection?.enabled ?? false,
    enableExpanding: rowExpansion?.enabled ?? false,

    // Row selection
    getRowId: (row, index) => {
      // Try to get the id field, or fallback to index
      return row.id?.toString() || `row-${index}`;
    },

    // Max multisorting
    maxMultiSortColCount: sorting?.maxSortColumns || 3,

    // Core models and pipelines
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: pagination?.enabled ? getPaginationRowModel() : undefined,
    getSortedRowModel: sorting?.enabled ? getSortedRowModel() : undefined,
    getFilteredRowModel: filtering?.enabled ? getFilteredRowModel() : undefined,
    getExpandedRowModel: rowExpansion?.enabled ? getExpandedRowModel() : undefined,

    // Event handlers
    onSortingChange: setSortingState,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPaginationState,
    onColumnVisibilityChange: setColumnVisibilityState,
    onRowSelectionChange: setRowSelectionState,
    onExpandedChange: setExpandedState,

    // Make react happy
    debugTable: false,
  });

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => {
    // Define event handlers inside useMemo to avoid dependency issues
    const handleRowClick = (row: Row<T>) => {
      if (onRowClick) {
        onRowClick(row);
      }
    };

    const handleCellClick = (row: Row<T>, columnId: string) => {
      if (onCellClick) {
        onCellClick(row, columnId);
      }
    };

    return {
      table,
      columns: columnDefs,
      columnFilters,
      setColumnFilters,
      globalFilter,
      setGlobalFilter,
      sorting: sortingState,
      setSorting: setSortingState,
      pagination: paginationState,
      setPagination: setPaginationState,
      columnVisibility: columnVisibilityState,
      setColumnVisibility: setColumnVisibilityState,
      rowSelection: rowSelectionState,
      setRowSelection: setRowSelectionState,
      expanded: expandedState,
      setExpanded: setExpandedState,
      isLoading,
      setIsLoading,
      hoveredRowId,
      setHoveredRowId,
      hoveredColumnId,
      setHoveredColumnId,
      rowVirtualizer: null,
      handleRowClick,
      handleCellClick,
    } as TableContextState<T>;
  }, [
    table,
    columnDefs,
    columnFilters,
    globalFilter,
    sortingState,
    paginationState,
    columnVisibilityState,
    rowSelectionState,
    expandedState,
    isLoading,
    hoveredRowId,
    hoveredColumnId,
    onRowClick,
    onCellClick,
  ]);

  return (
    <TableContext.Provider value={contextValue as TableContextState<DataRecord>}>
      {children}
    </TableContext.Provider>
  );
};
