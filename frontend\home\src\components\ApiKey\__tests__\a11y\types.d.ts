declare namespace jest {
  interface Matchers<R> {
    toHaveNoViolations(): R;
  }
}

interface Window {
  matchMedia(query: string): MediaQueryList;
}

interface MediaQueryList {
  matches: boolean;
  media: string;
  onchange: ((this: MediaQueryList, ev: MediaQueryListEvent) => void) | null;
  addListener(callback: (this: MediaQueryList, ev: MediaQueryListEvent) => void): void;
  removeListener(callback: (this: MediaQueryList, ev: MediaQueryListEvent) => void): void;
  addEventListener(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions,
  ): void;
  removeEventListener(
    type: string,
    listener: EventListenerOrEventListenerObject,
    options?: boolean | EventListenerOptions,
  ): void;
  dispatchEvent(event: Event): boolean;
}
