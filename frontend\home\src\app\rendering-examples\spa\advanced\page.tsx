'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';

// Interface for advanced analytics data
interface AdvancedAnalytics {
  pageViews: {
    today: number;
    weekly: number;
    monthly: number;
  };
  userSessions: {
    averageDuration: string;
    bounceRate: string;
    newUsers: number;
  };
  serverLoad: {
    current: string;
    average: string;
    peak: string;
  };
  geographicData: {
    region: string;
    visitors: number;
    percentage: number;
  }[];
  topReferrers: {
    source: string;
    visitors: number;
    conversionRate: string;
  }[];
  generatedAt: string;
}

export default function SPAAdvancedPage() {
  const { t: _t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<AdvancedAnalytics | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedView, setSelectedView] = useState<'overview' | 'geographic' | 'referrers'>(
    'overview',
  );
  const [refreshCounter, setRefreshCounter] = useState(0);

  // Fetch advanced analytics data
  useEffect(() => {
    const fetchAdvancedAnalytics = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Simulate API call delay
        await new Promise((resolve) => setTimeout(resolve, 1500));

        // Mock data that would normally come from an API
        const analyticsData: AdvancedAnalytics = {
          pageViews: {
            today: Math.floor(Math.random() * 10000),
            weekly: Math.floor(Math.random() * 50000),
            monthly: Math.floor(Math.random() * 200000),
          },
          userSessions: {
            averageDuration: Math.floor(Math.random() * 500) + ' seconds',
            bounceRate: Math.floor(Math.random() * 100) + '%',
            newUsers: Math.floor(Math.random() * 5000),
          },
          serverLoad: {
            current: Math.floor(Math.random() * 100) + '%',
            average: Math.floor(Math.random() * 100) + '%',
            peak: Math.floor(Math.random() * 100) + '%',
          },
          geographicData: [
            {
              region: 'North America',
              visitors: Math.floor(Math.random() * 50000),
              percentage: Math.floor(Math.random() * 40) + 20,
            },
            {
              region: 'Europe',
              visitors: Math.floor(Math.random() * 40000),
              percentage: Math.floor(Math.random() * 30) + 15,
            },
            {
              region: 'Asia',
              visitors: Math.floor(Math.random() * 60000),
              percentage: Math.floor(Math.random() * 35) + 25,
            },
            {
              region: 'South America',
              visitors: Math.floor(Math.random() * 20000),
              percentage: Math.floor(Math.random() * 15) + 5,
            },
            {
              region: 'Africa',
              visitors: Math.floor(Math.random() * 10000),
              percentage: Math.floor(Math.random() * 10) + 2,
            },
          ],
          topReferrers: [
            {
              source: 'Google',
              visitors: Math.floor(Math.random() * 30000),
              conversionRate: Math.floor(Math.random() * 10) + '%',
            },
            {
              source: 'Direct',
              visitors: Math.floor(Math.random() * 20000),
              conversionRate: Math.floor(Math.random() * 15) + '%',
            },
            {
              source: 'Twitter',
              visitors: Math.floor(Math.random() * 10000),
              conversionRate: Math.floor(Math.random() * 8) + '%',
            },
            {
              source: 'Facebook',
              visitors: Math.floor(Math.random() * 8000),
              conversionRate: Math.floor(Math.random() * 6) + '%',
            },
            {
              source: 'LinkedIn',
              visitors: Math.floor(Math.random() * 5000),
              conversionRate: Math.floor(Math.random() * 12) + '%',
            },
          ],
          generatedAt: new Date().toISOString(),
        };

        setData(analyticsData);
      } catch {
        setError('Failed to fetch analytics data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAdvancedAnalytics();
  }, [refreshCounter]);

  // Manually refresh data
  const handleRefresh = () => {
    setRefreshCounter((prev) => prev + 1);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="spa" />

      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">Advanced SPA Techniques</h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">Advanced Client-Side Applications</h2>
          <p className="mb-4">
            Modern SPAs can provide rich, interactive dashboards with complex data visualizations.
            This example demonstrates advanced client-side rendering techniques like:
          </p>
          <ul className="mb-4 list-disc space-y-1 pl-5">
            <li>Multiple data sources and complex state management</li>
            <li>Interactive dashboards with tabs and filters</li>
            <li>Manual and automatic refresh mechanisms</li>
            <li>Responsive design for various screen sizes</li>
          </ul>
        </div>

        <div className="bg-card mb-8 rounded-lg border p-6">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-xl font-semibold">Analytics Dashboard</h2>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                {isLoading
                  ? 'Updating...'
                  : `Last updated: ${data ? new Date(data.generatedAt).toLocaleString() : ''}`}
              </span>
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="bg-primary/10 hover:bg-primary/20 text-primary rounded px-3 py-1 text-sm font-medium disabled:opacity-50"
              >
                {isLoading ? 'Refreshing...' : 'Refresh Data'}
              </button>
            </div>
          </div>

          {/* Dashboard Tabs */}
          <div className="mb-6 border-b">
            <nav className="-mb-px flex space-x-4">
              <button
                onClick={() => setSelectedView('overview')}
                className={`px-1 pb-2 ${
                  selectedView === 'overview'
                    ? 'border-primary text-primary border-b-2 font-medium'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                Overview
              </button>
              <button
                onClick={() => setSelectedView('geographic')}
                className={`px-1 pb-2 ${
                  selectedView === 'geographic'
                    ? 'border-primary text-primary border-b-2 font-medium'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                Geographic Data
              </button>
              <button
                onClick={() => setSelectedView('referrers')}
                className={`px-1 pb-2 ${
                  selectedView === 'referrers'
                    ? 'border-primary text-primary border-b-2 font-medium'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                Top Referrers
              </button>
            </nav>
          </div>

          {isLoading ? (
            <div className="py-8">
              <div className="animate-pulse space-y-6">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="bg-muted h-24 rounded p-4">
                      <div className="bg-muted-foreground/20 mb-2 h-4 w-1/2 rounded"></div>
                      <div className="bg-muted-foreground/20 mt-4 h-8 w-3/4 rounded"></div>
                    </div>
                  ))}
                </div>
                <div className="bg-muted-foreground/20 h-80 w-full rounded"></div>
              </div>
            </div>
          ) : error ? (
            <div className="text-destructive border-destructive/50 rounded-md border p-4">
              {error}
            </div>
          ) : (
            <>
              {/* Overview Tab */}
              {selectedView === 'overview' && (
                <div className="space-y-6">
                  {/* Summary metrics */}
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
                      <h3 className="text-muted-foreground mb-1 text-sm">Today&apos;s Visitors</h3>
                      <p className="text-3xl font-bold">{data?.pageViews.today.toLocaleString()}</p>
                    </div>
                    <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
                      <h3 className="text-muted-foreground mb-1 text-sm">Weekly Visitors</h3>
                      <p className="text-3xl font-bold">
                        {data?.pageViews.weekly.toLocaleString()}
                      </p>
                    </div>
                    <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
                      <h3 className="text-muted-foreground mb-1 text-sm">New Users</h3>
                      <p className="text-3xl font-bold">
                        {data?.userSessions.newUsers.toLocaleString()}
                      </p>
                    </div>
                    <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
                      <h3 className="text-muted-foreground mb-1 text-sm">Bounce Rate</h3>
                      <p className="text-3xl font-bold">{data?.userSessions.bounceRate}</p>
                    </div>
                  </div>

                  {/* System performance metrics */}
                  <div className="mt-8 rounded-lg border p-4">
                    <h3 className="mb-2 text-lg font-medium">System Performance</h3>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                      <div>
                        <p className="text-muted-foreground text-sm">Current Load</p>
                        <p className="text-xl font-semibold">{data?.serverLoad.current}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">Average Load</p>
                        <p className="text-xl font-semibold">{data?.serverLoad.average}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground text-sm">Peak Load</p>
                        <p className="text-xl font-semibold">{data?.serverLoad.peak}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Geographic Tab */}
              {selectedView === 'geographic' && (
                <div>
                  <h3 className="mb-4 text-lg font-medium">Geographic Distribution</h3>
                  <div className="bg-muted overflow-hidden rounded-lg">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="px-4 py-2 text-left">Region</th>
                          <th className="px-4 py-2 text-left">Visitors</th>
                          <th className="px-4 py-2 text-left">Percentage</th>
                        </tr>
                      </thead>
                      <tbody>
                        {data?.geographicData.map((region) => (
                          <tr key={region.region} className="border-b">
                            <td className="px-4 py-2">{region.region}</td>
                            <td className="px-4 py-2">{region.visitors.toLocaleString()}</td>
                            <td className="px-4 py-2">
                              <div className="flex items-center">
                                <div className="bg-muted mr-2 h-2 w-20 rounded-full">
                                  <div
                                    className="bg-primary h-2 rounded-full"
                                    style={{ width: `${region.percentage}%` }}
                                  ></div>
                                </div>
                                <span>{region.percentage}%</span>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Referrers Tab */}
              {selectedView === 'referrers' && (
                <div>
                  <h3 className="mb-4 text-lg font-medium">Top Referrers</h3>
                  <div className="bg-muted overflow-hidden rounded-lg">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="px-4 py-2 text-left">Source</th>
                          <th className="px-4 py-2 text-left">Visitors</th>
                          <th className="px-4 py-2 text-left">Conversion</th>
                        </tr>
                      </thead>
                      <tbody>
                        {data?.topReferrers.map((referrer) => (
                          <tr key={referrer.source} className="border-b">
                            <td className="px-4 py-2">{referrer.source}</td>
                            <td className="px-4 py-2">{referrer.visitors.toLocaleString()}</td>
                            <td className="px-4 py-2">{referrer.conversionRate}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        <div className="bg-muted rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">SPA Advanced Techniques</h2>
          <ul className="list-disc space-y-2 pl-5">
            <li>
              <strong>Component-based architecture:</strong> Breaking UI into reusable pieces
            </li>
            <li>
              <strong>State management:</strong> Using React&apos;s Context API, Redux, or other
              libraries
            </li>
            <li>
              <strong>Custom hooks:</strong> Encapsulating logic in reusable functions
            </li>
            <li>
              <strong>Lazy loading:</strong> Improve initial load time by splitting code
            </li>
            <li>
              <strong>Progressive enhancement:</strong> Work without JavaScript, enhance with it
            </li>
          </ul>
        </div>

        <div className="mt-8 flex justify-center">
          <Link href="/rendering-examples/spa" className="text-primary font-medium hover:underline">
            ← Back to Basic SPA Example
          </Link>
        </div>
      </main>
    </div>
  );
}
