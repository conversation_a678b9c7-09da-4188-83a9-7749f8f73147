import{M as Q}from"./chunk-NNBHHXXN-09hoI4Mn.js";import{b as W,u as X,c as $,d as L,e as G,f as J,g as K}from"./chunk-NAAFKRUS-DzBFrGez.js";import{D as Y}from"./chunk-7I5DQGWY-CwOWioty.js";import{a as k}from"./chunk-PDWBYQOW-BedvhUOI.js";import{P as Z,a as ee}from"./chunk-IQBAUTU5-D_4dFOf0.js";import{r as u,a6 as O,R as te,u as se,b as v,aS as re,aV as ae,j as e,a8 as ne,a9 as ie,t as h,H,w as j,x as q,ab as le,B as E,d2 as oe,aB as de,T as A,Y as T,A as ce,ah as ue}from"./index-Bwql5Dzz.js";import{u as me,_ as fe}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{u as xe}from"./chunk-C76H5USB-ByRPKhW7.js";import{K as pe}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as b,u as he,a as je,S as N}from"./chunk-JGQGO74V-DtHO1ucg.js";import{u as ge}from"./use-prompt-pbDx0Sfe.js";import{X as ye}from"./x-circle-CKMdlKvN.js";import{C as M}from"./checkbox-B4pL6X49.js";import{c as be}from"./index-BxZ1678G.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-MWVM4TYO-bKUcYSnf.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./prompt-BsR9zKsn.js";var ve=Object.defineProperty,I=Object.getOwnPropertySymbols,U=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable,D=(t,a,s)=>a in t?ve(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s,_e=(t,a)=>{for(var s in a)U.call(a,s)&&D(t,s,a[s]);if(I)for(var s of I(a))V.call(a,s)&&D(t,s,a[s]);return t},we=(t,a)=>{var s={};for(var r in t)U.call(t,r)&&a.indexOf(r)<0&&(s[r]=t[r]);if(t!=null&&I)for(var r of I(t))a.indexOf(r)<0&&V.call(t,r)&&(s[r]=t[r]);return s};const B=u.forwardRef((t,a)=>{var s=t,{color:r="currentColor"}=s,n=we(s,["color"]);return u.createElement("svg",_e({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:a},n),u.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4.389 11.5H3.5a1.777 1.777 0 0 1-1.778-1.778V2.611c0-.982.796-1.778 1.778-1.778h4.444c.983 0 1.778.796 1.778 1.778v.935"}),u.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4.389 12.389V5.278c0-.983.795-1.778 1.778-1.778h3.632c.235 0 .462.093.628.26l2.59 2.59c.168.168.26.393.26.629v5.41c0 .982-.795 1.777-1.777 1.777H6.167a1.777 1.777 0 0 1-1.778-1.777"}),u.createElement("path",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13.278 7.055H10.61a.89.89 0 0 1-.889-.888V3.5"}))});B.displayName="DocumentSeries";var S=be(),Ne=t=>{const{t:a}=v();return u.useMemo(()=>[S.display({id:"select",header:({table:s})=>e.jsx(M,{checked:s.getIsSomePageRowsSelected()?"indeterminate":s.getIsAllPageRowsSelected(),onCheckedChange:r=>s.toggleAllPageRowsSelected(!!r)}),cell:({row:s})=>{const r=s.getCanSelect();return e.jsx(M,{disabled:!r,checked:s.getIsSelected(),onCheckedChange:n=>s.toggleSelected(!!n),onClick:n=>{n.stopPropagation()}})}}),S.display({id:"product",header:()=>e.jsx(Z,{}),cell:({row:s})=>e.jsx(ee,{product:s.original.product})}),S.accessor("sku",{header:a("fields.sku"),cell:({getValue:s})=>s()||"-"}),S.accessor("title",{header:a("fields.title")})],[a,t])},Ee=()=>{const{t}=v();return[{key:"created_at",label:t("fields.createdAt"),type:"date"},{key:"updated_at",label:t("fields.updatedAt"),type:"date"}]},Se=({pageSize:t=50,prefix:a})=>{const s=xe(["q","offset","order","created_at","updated_at"],a),{offset:r,created_at:n,updated_at:m,...f}=s;return{searchParams:{...f,limit:t,offset:r?Number(r):0,created_at:n?JSON.parse(n):void 0,updated_at:m?JSON.parse(m):void 0},raw:s}},P=50,F="rit",Ie=({onSelectionChange:t,currencyCode:a})=>{const{t:s}=v(),[r,n]=u.useState({}),m=d=>{const _=typeof d=="function"?d(r):d;n(_),t(Object.keys(_))},{searchParams:f,raw:x}=Se({pageSize:P,prefix:F}),{variants:y=[],count:p}=oe({...f,fields:"*inventory_items.inventory.location_levels,+inventory_quantity"}),i=Ne(a),w=Ee(),{table:g}=me({data:y,columns:i,count:p,enablePagination:!0,getRowId:d=>d.id,pageSize:P,enableRowSelection:d=>!0,rowSelection:{state:r,updater:m}});return e.jsx("div",{className:"flex size-full flex-col overflow-hidden",children:e.jsx(fe,{table:g,columns:i,pageSize:P,count:p,filters:w,pagination:!0,layout:"fill",search:!0,orderBy:[{key:"product_id",label:s("fields.product")},{key:"title",label:s("fields.title")},{key:"sku",label:s("fields.sku")}],prefix:F,queryObject:x})})};function Ce({item:t,currencyCode:a,orderId:s}){const{t:r}=v(),{mutateAsync:n}=L(s),{mutateAsync:m}=G(s),{mutateAsync:f}=J(s),{mutateAsync:x}=K(s),y=u.useMemo(()=>{var l;return!!((l=t.actions)!=null&&l.find(o=>o.action==="ITEM_ADD"))},[t]),p=u.useMemo(()=>{var l;return!!((l=t.actions)!=null&&l.find(o=>o.action==="ITEM_UPDATE"))},[t]),i=u.useMemo(()=>{var o;return!!((o=t.actions)==null?void 0:o.find(c=>c.action==="ITEM_UPDATE"))&&t.quantity===t.detail.fulfilled_quantity},[t]),w=async l=>{var c;if(l<=t.detail.fulfilled_quantity){h.error(r("orders.edits.validation.quantityLowerThanFulfillment"));return}if(l===t.quantity)return;const o=(c=t.actions)==null?void 0:c.find(C=>C.action==="ITEM_ADD");try{o?await m({quantity:l,actionId:o.id}):await f({quantity:l,itemId:t.id})}catch(C){h.error(C.message)}},g=async()=>{var o;const l=(o=t.actions)==null?void 0:o.find(c=>c.action==="ITEM_ADD");try{l?await x(l.id):await f({quantity:t.detail.fulfilled_quantity,itemId:t.id})}catch(c){h.error(c.message)}},d=async()=>{var o;const l=(o=t.actions)==null?void 0:o.find(c=>c.action==="ITEM_UPDATE");try{l&&await x(l.id)}catch(c){h.error(c.message)}},_=async()=>{try{await n({items:[{variant_id:t.variant_id,quantity:t.quantity}]})}catch(l){h.error(l.message)}};return e.jsx("div",{className:"bg-ui-bg-subtle shadow-elevation-card-rest my-2 rounded-xl ",children:e.jsxs("div",{className:"flex flex-col items-center gap-x-2 gap-y-2 p-3 text-sm md:flex-row",children:[e.jsxs("div",{className:"flex flex-1 items-center justify-between",children:[e.jsxs("div",{className:"flex flex-row items-center gap-x-3",children:[e.jsx(de,{src:t.thumbnail}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{children:[e.jsxs(A,{className:"txt-small",as:"span",weight:"plus",children:[t.title," "]}),t.variant_sku&&e.jsxs("span",{children:["(",t.variant_sku,")"]})]}),e.jsx(A,{as:"div",className:"text-ui-fg-subtle txt-small",children:t.product_title})]})]}),y&&e.jsx(T,{size:"2xsmall",rounded:"full",color:"blue",className:"mr-1",children:r("general.new")}),i?e.jsx(T,{size:"2xsmall",rounded:"full",color:"red",className:"mr-1",children:r("general.removed")}):p&&e.jsx(T,{size:"2xsmall",rounded:"full",color:"orange",className:"mr-1",children:r("general.modified")})]}),e.jsxs("div",{className:"flex flex-1 justify-between",children:[e.jsxs("div",{className:"flex flex-grow items-center gap-2",children:[e.jsx(q,{className:"bg-ui-bg-base txt-small w-[67px] rounded-lg [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none",type:"number",disabled:t.detail.fulfilled_quantity===t.quantity,min:t.detail.fulfilled_quantity,defaultValue:t.quantity,onBlur:l=>{const o=l.target.value,c=o===""?null:Number(o);c&&w(c)}}),e.jsx(A,{className:"txt-small text-ui-fg-subtle",children:r("fields.qty")})]}),e.jsx("div",{className:"text-ui-fg-subtle txt-small mr-2 flex flex-shrink-0",children:e.jsx(Q,{currencyCode:a,amount:t.total})}),e.jsx(ce,{groups:[{actions:[{label:r("actions.duplicate"),onClick:_,icon:e.jsx(B,{})}]},{actions:[i?{label:r("actions.undo"),onClick:d,icon:e.jsx(ue,{})}:{label:r("actions.remove"),onClick:g,icon:e.jsx(ye,{}),disabled:t.detail.fulfilled_quantity===t.quantity}].filter(Boolean)}]})]})]})},t.quantity)}var z=[],ke=({order:t,preview:a})=>{const{t:s}=v(),{setIsOpen:r}=je(),[n,m]=u.useState(""),{mutateAsync:f,isPending:x}=L(t.id),y=async()=>{await f({items:z.map(i=>({variant_id:i,quantity:1}))},{onError:i=>{h.error(i.message)}}),r("inbound-items",!1)},p=u.useMemo(()=>a.items.filter(i=>i.title.toLowerCase().includes(n)||i.product_title.toLowerCase().includes(n)),[a,n]);return e.jsxs("div",{children:[e.jsxs("div",{className:"mb-3 mt-8 flex items-center justify-between",children:[e.jsx(H,{level:"h2",children:s("fields.items")}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(q,{value:n,onChange:i=>m(i.target.value),placeholder:s("fields.search"),autoComplete:"off",type:"search"}),e.jsxs(N,{id:"inbound-items",children:[e.jsx(N.Trigger,{asChild:!0,children:e.jsx(E,{variant:"secondary",size:"small",children:s("actions.addItems")})}),e.jsxs(N.Content,{children:[e.jsx(N.Header,{}),e.jsx(Ie,{currencyCode:t.currency_code,onSelectionChange:i=>{z=i}}),e.jsx(N.Footer,{children:e.jsx("div",{className:"flex w-full items-center justify-end gap-x-4",children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(b.Close,{asChild:!0,children:e.jsx(E,{type:"button",variant:"secondary",size:"small",children:s("actions.cancel")})}),e.jsx(E,{type:"submit",variant:"primary",size:"small",role:"button",disabled:x,onClick:async()=>await y(),children:s("actions.save")},"submit-button")]})})})]})]})]})]}),p.map(i=>e.jsx(Ce,{item:i,orderId:t.id,currencyCode:t.currency_code},i.id)),n&&!p.length&&e.jsx("div",{style:{background:"repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"},className:"bg-ui-bg-field mt-4 block h-[56px] w-full rounded-lg border border-dashed"})]})},Oe=O.object({note:O.string().optional(),send_notification:O.boolean().optional()}),Ae=({order:t,preview:a})=>{const{t:s}=v(),{handleSuccess:r}=he(),{mutateAsync:n,isPending:m}=X(t.id),{mutateAsync:f,isPending:x}=$(t.id),y=m||x,p=ne({defaultValues:()=>Promise.resolve({note:"",send_notification:!1}),resolver:ie(Oe)}),i=ge(),w=p.handleSubmit(async g=>{try{if(!await i({title:s("general.areYouSure"),description:s("orders.edits.confirmText"),confirmText:s("actions.continue"),cancelText:s("actions.cancel"),variant:"confirmation"}))return;await f(),h.success(s("orders.edits.createSuccessToast")),r()}catch(d){h.error(s("general.error"),{description:d.message})}});return e.jsx(b.Form,{form:p,onClose:g=>{g||n(void 0,{onSuccess:()=>{h.success(s("orders.edits.cancelSuccessToast"))},onError:d=>{h.error(d.message)}})},children:e.jsxs(pe,{onSubmit:w,className:"flex h-full flex-col",children:[e.jsx(b.Header,{}),e.jsx(b.Body,{className:"flex size-full justify-center overflow-y-auto",children:e.jsxs("div",{className:"mt-16 w-[720px] max-w-[100%] px-4 md:p-0",children:[e.jsx(H,{level:"h1",children:s("orders.edits.create")}),e.jsx(ke,{preview:a,order:t}),e.jsxs("div",{className:"mt-8 border-y border-dotted py-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"txt-small text-ui-fg-subtle",children:s("orders.edits.currentTotal")}),e.jsx("span",{className:"txt-small text-ui-fg-subtle",children:k(t.total,t.currency_code)})]}),e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"txt-small text-ui-fg-subtle",children:s("orders.edits.newTotal")}),e.jsx("span",{className:"txt-small text-ui-fg-subtle",children:k(a.total,t.currency_code)})]}),e.jsxs("div",{className:"mt-4 flex items-center justify-between border-t border-dotted pt-4",children:[e.jsx("span",{className:"txt-small font-medium",children:s("orders.exchanges.refundAmount")}),e.jsx("span",{className:"txt-small font-medium",children:k(a.summary.pending_difference,t.currency_code)})]})]}),e.jsx(j.Field,{control:p.control,name:"note",render:({field:g})=>e.jsx(j.Item,{children:e.jsxs("div",{className:"mt-8 flex",children:[e.jsxs("div",{className:"block flex-1",children:[e.jsx(j.Label,{children:s("fields.note")}),e.jsx(j.Hint,{className:"!mt-1",children:s("orders.edits.noteHint")})]}),e.jsx("div",{className:"w-full flex-1 flex-grow",children:e.jsx(j.Control,{children:e.jsx(q,{...g,placeholder:s("fields.note")})})})]})})}),e.jsx("div",{className:"bg-ui-bg-field mt-8 rounded-lg border py-2 pl-2 pr-4",children:e.jsx(j.Field,{control:p.control,name:"send_notification",render:({field:{onChange:g,value:d,..._}})=>e.jsxs(j.Item,{children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(j.Control,{className:"mr-4 self-start",children:e.jsx(le,{className:"mt-[2px]",checked:!!d,onCheckedChange:g,..._})}),e.jsxs("div",{className:"block",children:[e.jsx(j.Label,{children:s("orders.returns.sendNotification")}),e.jsx(j.Hint,{className:"!mt-1",children:s("orders.returns.sendNotificationHint")})]})]}),e.jsx(j.ErrorMessage,{})]})})}),e.jsx("div",{className:"p-8"})]})}),e.jsx(b.Footer,{children:e.jsx("div",{className:"flex w-full items-center justify-end gap-x-4",children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(b.Close,{asChild:!0,children:e.jsx(E,{type:"button",variant:"secondary",size:"small",children:s("orders.edits.cancel")})}),e.jsx(E,{type:"submit",variant:"primary",size:"small",isLoading:y,children:s("orders.edits.confirm")},"submit-button")]})})})]})})},R=!1,ft=()=>{const{id:t}=te(),a=se(),{t:s}=v(),{order:r}=re(t,{fields:Y}),{order:n}=ae(t),{mutateAsync:m}=W(r.id);return u.useEffect(()=>{async function f(){if(!(R||!n)){if(n.order_change){n.order_change.change_type!=="edit"&&(a(`/orders/${n.id}`,{replace:!0}),h.error(s("orders.edits.activeChangeError")));return}R=!0;try{const{order:x}=await m({order_id:n.id})}catch(x){h.error(x.message),a(`/orders/${n.id}`,{replace:!0})}finally{R=!1}}}f()},[n]),e.jsx(b,{children:n&&r&&e.jsx(Ae,{order:r,preview:n})})};export{ft as Component};
