// components/CookieBanner/CookieBanner.tsx
import React, { useState, useEffect } from 'react';

export interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
}

export interface CookieBannerProps {
  /**
   * The title of the cookie banner
   */
  title?: string;
  /**
   * The description text for the cookie banner
   */
  description?: string;
  /**
   * Callback function when user accepts all cookies
   */
  onAcceptAll?: (preferences: CookiePreferences) => void;
  /**
   * Callback function when user declines all cookies
   */
  onDeclineAll?: () => void;
  /**
   * Callback function when user saves custom preferences
   */
  onSavePreferences?: (preferences: CookiePreferences) => void;
  /**
   * Initial cookie preferences
   */
  initialPreferences?: Partial<CookiePreferences>;
  /**
   * Position of the banner
   */
  position?: 'bottom' | 'top';
}

export const CookieBanner: React.FC<CookieBannerProps> = ({
  title = 'Cookies & Privacy',
  description = 'We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies.',
  onAcceptAll,
  onDeclineAll,
  onSavePreferences,
  initialPreferences,
  position = 'bottom',
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [showCustomize, setShowCustomize] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always required
    analytics: initialPreferences?.analytics ?? false,
    marketing: initialPreferences?.marketing ?? false,
  });

  useEffect(() => {
    // Check if cookie consent is already stored
    const hasConsent = localStorage.getItem('cookieConsent');
    if (!hasConsent) {
      // Show banner after a short delay for better UX
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
    };
    setPreferences(allAccepted);
    localStorage.setItem('cookieConsent', 'all');
    onAcceptAll?.(allAccepted);
    setIsVisible(false);
  };

  const handleDeclineAll = () => {
    const allDeclined = {
      necessary: true, // Always required
      analytics: false,
      marketing: false,
    };
    setPreferences(allDeclined);
    localStorage.setItem('cookieConsent', 'necessary');
    onDeclineAll?.();
    setIsVisible(false);
  };

  const handleSavePreferences = () => {
    localStorage.setItem('cookieConsent', 'custom');
    onSavePreferences?.(preferences);
    setIsVisible(false);
  };

  const handlePreferenceChange = (name: keyof CookiePreferences) => {
    if (name === 'necessary') return; // Cannot change necessary cookies
    setPreferences((prev) => ({
      ...prev,
      [name]: !prev[name],
    }));
  };

  if (!isVisible) return null;

  return (
    <div
      className="fixed inset-x-0 bottom-0 z-50 flex items-center justify-center p-4 md:p-0"
      role="dialog"
      aria-labelledby="cookie-banner-title"
      aria-describedby="cookie-banner-description"
    >
      <div className="flex w-full flex-col rounded-[16px] border border-gray-200 bg-[#727272B2] p-4 shadow-lg backdrop-blur-[10px] md:h-[128px] md:w-[1464px] md:flex-row md:items-center md:justify-between md:rounded-t-[32px] md:px-[49px]">
        {/* ส่วนข้อความ */}
        <div className="mb-4 flex-1 md:mb-0 md:max-w-2xl">
          <h2 id="cookie-banner-title" className="text-lg font-semibold text-white">
            {title}
          </h2>
          <p id="cookie-banner-description" className="mt-1 text-sm text-white">
            {description}
          </p>
        </div>

        {/* ส่วนปุ่มกด */}
        <div className="flex w-full flex-row gap-2 justify-end md:w-auto md:items-center">
          {!showCustomize ? (
            <>
              <button
                onClick={() => setShowCustomize(true)}
                className="h-[48px] flex-1 rounded-md border border-gray-300 text-white hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none md:w-[180px] md:flex-none"
                aria-label="Customize cookie preferences"
              >
                การตั้งค่าคุกกี้
              </button>
              <button
                onClick={handleAcceptAll}
                className="h-[48px] flex-1 rounded-md border border-transparent bg-[#121E72] text-white hover:bg-[#1a2a8f] focus:ring-2 focus:ring-[#121E72] focus:ring-offset-2 focus:outline-none md:w-[180px] md:flex-none"
                aria-label="Accept all cookies"
              >
                ยอมรับ
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => setShowCustomize(false)}
                className="h-[48px] flex-1 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none md:w-[180px] md:flex-none"
                aria-label="Go back to main cookie banner"
              >
                Back
              </button>
              <button
                onClick={handleSavePreferences}
                className="h-[48px] flex-1 rounded-md border border-transparent bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none md:w-[180px] md:flex-none"
                aria-label="Save custom cookie preferences"
              >
                Save Preferences
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default CookieBanner;
