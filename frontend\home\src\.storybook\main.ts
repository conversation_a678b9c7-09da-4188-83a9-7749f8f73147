import type { StorybookConfig } from '@storybook/nextjs';

const config: StorybookConfig = {
  stories: ['../src/**/*.mdx', '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@storybook/addon-onboarding',
    '@storybook/addon-themes',
    '@storybook/addon-a11y',
    '@storybook/addon-coverage',
    "@storybook/addon-viewport",
    "@storybook/addon-backgrounds",
    "@storybook/addon-console",
    "@storybook/addon-controls",
    "@storybook/addon-actions",
    "@storybook/addon-interactions",
    "@storybook/addon-storysource",
    "@storybook/addon-measure",
    "@storybook/addon-outline",
    'msw-storybook-addon',
    'storybook-react-i18next',
    {
      name: '@storybook/addon-styling-webpack',
      options: {
        postCss: true,
      },
    },
  ],
  framework: {
    name: '@storybook/nextjs',
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
  staticDirs: ['../public'],
  webpackFinal: async (config) => {
    return config;
  }
};

export default config;