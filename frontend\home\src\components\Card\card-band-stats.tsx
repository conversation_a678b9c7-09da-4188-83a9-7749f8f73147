// components/Card/CardBandStats/card-band-stats.tsx
import React from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';

// Define variants for the component
export type CardBandStatsVariant = 'default' | 'compact' | 'hero';

// Define color schemes
export type CardBandStatsColorScheme = 'white' | 'orange' | 'blue';

// Define props for the component
export interface CardBandStatsProps {
  value: string | number;
  description: string;
  className?: string;
  variant?: CardBandStatsVariant;
  colorScheme?: CardBandStatsColorScheme;
  onClick?: () => void;
  icon?: React.ReactNode;
}

export const CardBandStats = React.forwardRef<HTMLDivElement, CardBandStatsProps>(
  ({ 
    value, 
    description, 
    className, 
    variant = 'default', 
    colorScheme = 'white',
    onClick, 
    icon 
  }, ref) => {
    // Get background color based on colorScheme
    const getBackgroundColor = () => {
      switch (colorScheme) {
        case 'white':
          return 'bg-white';
        case 'orange':
          return 'bg-orange-400';
        case 'blue':
          return 'bg-blue-900';
        default:
          return 'bg-white';
      }
    };
    
    // Get text colors based on colorScheme
    const getValueTextColor = () => {
      switch (colorScheme) {
        case 'white':
        case 'orange':
          return 'text-blue-900';
        case 'blue':
          return 'text-orange-400';
        default:
          return 'text-blue-900';
      }
    };
    
    const getDescriptionTextColor = () => {
      switch (colorScheme) {
        case 'white':
          return 'text-gray-600';
        case 'orange':
          return 'text-blue-900';
        case 'blue':
          return 'text-white';
        default:
          return 'text-gray-600';
      }
    };
    
    // Get icon wrapper styles based on variant and colorScheme
    const getIconWrapperStyles = () => {
      switch (variant) {
        case 'hero':
          return cn('flex items-center justify-center mb-4');
        case 'default':
          return cn('flex items-center justify-center mb-3');
        case 'compact':
          return cn('flex items-center justify-center mb-2');
        default:
          return cn('flex items-center justify-center mb-3');
      }
    };

    // Get value text styles based on variant
    const getValueTextStyles = () => {
      switch (variant) {
        case 'hero':
          return cn('font-bold', getValueTextColor(), 'text-5xl mb-2');
        case 'default':
          return cn('font-bold', getValueTextColor(), 'text-3xl mb-1');
        case 'compact':
          return cn('font-bold', getValueTextColor(), 'text-2xl mb-0.5');
        default:
          return cn('font-bold', getValueTextColor(), 'text-3xl mb-1');
      }
    };

    // Get description text styles based on variant
    const getDescriptionTextStyles = () => {
      switch (variant) {
        case 'hero':
          return cn(getDescriptionTextColor(), 'text-lg');
        case 'default':
          return cn(getDescriptionTextColor(), 'text-sm');
        case 'compact':
          return cn(getDescriptionTextColor(), 'text-xs');
        default:
          return cn(getDescriptionTextColor(), 'text-sm');
      }
    };

    return (
      <Card 
        ref={ref} 
        className={cn(
          'rounded-lg shadow hover:shadow-md transition-shadow duration-200',
          getBackgroundColor(),
          onClick && 'cursor-pointer',
          className
        )}
        onClick={onClick}
      >
        <CardContent className={cn(
          'flex flex-col items-center justify-center text-center',
          variant === 'hero' ? 'py-10 px-6' : 
          variant === 'default' ? 'py-6 px-4' : 'py-3 px-3'
        )}>
          <div className={getIconWrapperStyles()}>
            {icon}
          </div>
          
          <div className={getValueTextStyles()}>
            {value}
          </div>
          
          <div className={getDescriptionTextStyles()}>
            {description}
          </div>
        </CardContent>
      </Card>
    );
  }
);

CardBandStats.displayName = 'CardBandStats';

export default CardBandStats;