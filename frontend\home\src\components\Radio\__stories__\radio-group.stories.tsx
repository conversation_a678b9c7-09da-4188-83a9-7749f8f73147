// radio-group.stories.tsx
import * as React from "react";
import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { RadioGroup, RadioGroupItem } from "../";
import { useState } from "react";

const meta: Meta<typeof RadioGroup> = {
  title: "UI/Radio/RadioGroup",
  component: RadioGroup,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: "A customized radio group component that extends shadcn UI radio with additional variants and features",
      },
    },
    a11y: {
      config: {
        rules: [
          {
            id: "radiogroup",
            enabled: true,
          },
        ],
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "card", "outline", "buttonCard"],
      description: "Visual style variant of the radio group",
      table: {
        type: { summary: "default | card | outline | buttonCard" },
        defaultValue: { summary: "default" },
      },
    },
    orientation: {
      control: { type: "radio" },
      options: ["vertical", "horizontal"],
      description: "The orientation of the radio group",
      table: {
        type: { summary: "vertical | horizontal" },
        defaultValue: { summary: "vertical" },
      },
    },
    size: {
      control: { type: "select" },
      options: ["default", "sm", "lg"],
      description: "Size of spacing between radio items",
      table: {
        type: { summary: "default | sm | lg" },
        defaultValue: { summary: "default" },
      },
    },
    disabled: {
      control: "boolean",
      description: "When true, prevents the user from interacting with the radio items",
    },
    title: {
      control: "text",
      description: "Title text for the radio group",
    },
    description: {
      control: "text",
      description: "Additional descriptive text for the radio group",
    },
  },
};

export default meta;
type Story = StoryObj<typeof RadioGroup>;

export const Default: Story = {
  args: {
    title: "Favorite fruit",
    description: "Select your favorite fruit",
  },
  render: (args) => (
    <RadioGroup {...args}>
      <RadioGroupItem value="apple" id="apple" label="Apple" />
      <RadioGroupItem value="banana" id="banana" label="Banana" />
      <RadioGroupItem value="orange" id="orange" label="Orange" />
    </RadioGroup>
  ),
};


export const ItemSizes: Story = {
  args: {
    title: "Item sizes",
    orientation: "horizontal",
  },
  render: (args) => (
    <RadioGroup {...args}>
      <RadioGroupItem 
        value="small" 
        id="size-small" 
        label="Small" 
        size="sm" 
      />
      <RadioGroupItem 
        value="default" 
        id="size-default" 
        label="Default" 
      />
      <RadioGroupItem 
        value="large" 
        id="size-large" 
        label="Large" 
        size="lg" 
      />
    </RadioGroup>
  ),
};

export const Horizontal: Story = {
  args: {
    title: "Gender",
    orientation: "horizontal",
  },
  render: (args) => (
    <RadioGroup {...args}>
      <RadioGroupItem value="male" id="male" label="Male" />
      <RadioGroupItem value="female" id="female" label="Female" />
      <RadioGroupItem value="other" id="other" label="Other" />
    </RadioGroup>
  ),
};

export const Disabled: Story = {
  args: {
    title: "Availability",
    disabled: true,
  },
  render: (args) => (
    <RadioGroup {...args}>
      <RadioGroupItem value="morning" id="morning" label="Morning" />
      <RadioGroupItem value="afternoon" id="afternoon" label="Afternoon" />
      <RadioGroupItem value="evening" id="evening" label="Evening" />
    </RadioGroup>
  ),
};

export const WithState: Story = {
  args: {
    title: "Theme preference",
  },
  render: (args) => {
    const [value, setValue] = useState("light");
    return (
      <div className="space-y-4">
        <RadioGroup {...args} value={value} onValueChange={setValue}>
          <RadioGroupItem value="light" id="light" label="Light" />
          <RadioGroupItem value="dark" id="dark" label="Dark" />
          <RadioGroupItem value="system" id="system" label="System" />
        </RadioGroup>
        <div className="text-sm">
          Selected value: <span className="font-medium">{value}</span>
        </div>
      </div>
    );
  },
};

export const WithDescriptions: Story = {
  args: {
    title: "Notification preferences",
  },
  render: (args) => (
    <RadioGroup {...args}>
      <RadioGroupItem
        value="all"
        id="all"
        label="All notifications"
        description="Receive notifications for all activity"
      />
      <RadioGroupItem
        value="important"
        id="important"
        label="Important only"
        description="Only receive notifications for important activity"
      />
      <RadioGroupItem
        value="none"
        id="none"
        label="None"
        description="Don't receive any notifications"
      />
    </RadioGroup>
  ),
};

export const CardVariant: Story = {
  args: {
    title: "Subscription plan",
    variant: "card",
  },
  render: (args) => (
    <RadioGroup {...args}>
      <RadioGroupItem value="basic" id="basic" label="Basic" description="$9/month" />
      <RadioGroupItem value="pro" id="pro" label="Pro" description="$19/month" />
      <RadioGroupItem value="enterprise" id="enterprise" label="Enterprise" description="$49/month" />
    </RadioGroup>
  ),
};

export const OutlineVariant: Story = {
  args: {
    title: "Visibility",
    variant: "outline",
  },
  render: (args) => (
    <RadioGroup {...args}>
      <RadioGroupItem value="public" id="public" label="Public" />
      <RadioGroupItem value="private" id="private" label="Private" />
      <RadioGroupItem value="draft" id="draft" label="Draft" />
    </RadioGroup>
  ),
};

export const ButtonCardVariant: Story = {
  args: {
    title: "กำลังไฟ",
    variant: "buttonCard",
    orientation: "horizontal",
  },
  render: (args) => (
    <RadioGroup {...args} defaultValue="18" className="flex gap-2">
      <RadioGroupItem 
        value="8" 
        id="watt-8" 
        label="8 วัตต์" 
        variant="buttonCard" 
      />
      <RadioGroupItem 
        value="18" 
        id="watt-18" 
        label="18 วัตต์" 
        variant="buttonCard" 
      />
      <RadioGroupItem 
        value="24" 
        id="watt-24" 
        label="24 วัตต์" 
        variant="buttonCard" 
      />
    </RadioGroup>
  ),
};

export const BulbShapeVariant: Story = {
  args: {
    title: "รูปทรงหลอด",
    variant: "buttonCard",
    orientation: "horizontal",
  },
  render: (args) => (
    <RadioGroup {...args} defaultValue="long" className="flex gap-2">
      <RadioGroupItem 
        value="short" 
        id="bulb-short" 
        label="หลอดสั้น" 
        variant="buttonCard" 
      />
      <RadioGroupItem 
        value="long" 
        id="bulb-long" 
        label="หลอดยาว" 
        variant="buttonCard" 
      />
    </RadioGroup>
  ),
};
