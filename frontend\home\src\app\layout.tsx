import { IBM_Plex_Sans_Thai } from 'next/font/google';
import '@/styles/globals.css';
import { ThemeProvider } from '@/components/Providers/theme-provider';
import { I18nProvider } from '@/components/Providers/i18n-provider';
import { metadata, viewport } from '@/config/metadata';

const ibmPlexSansThai = IBM_Plex_Sans_Thai({
  weight: ['400', '500', '600', '700'],
  subsets: ["thai", "latin"],
  variable: "--font-ibm-plex-sans-thai",
  preload: true,
});

export { metadata, viewport };

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={`${ibmPlexSansThai.className}`}>
      <head />
      <body>
        <I18nProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            {children}
          </ThemeProvider>
        </I18nProvider>
      </body>
    </html>
  );
}