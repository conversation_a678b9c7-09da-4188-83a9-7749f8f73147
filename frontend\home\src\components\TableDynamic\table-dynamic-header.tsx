'use client';

import * as React from 'react';
import { useCallback } from 'react';
import { flexRender } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp, ArrowUpDown, Filter } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useTableContext } from './table-dynamic-context';
import { HEADER_HEIGHTS } from './constants';
import type { ColumnMeta } from './types';

/**
 * TableDynamicHeader props
 */
export interface TableDynamicHeaderProps {
  /** Whether the header is sticky */
  sticky?: boolean;
  /** Whether to show column separators */
  showColumnSeparators?: boolean;
  /** Custom header row height */
  height?: number | string;
  /** Table size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Hover handler for header cells */
  onHeaderHover?: (columnId: string | null) => void;
}

/**
 * TableDynamicHeader component
 */
export const TableDynamicHeader = React.forwardRef<
  HTMLTableSectionElement,
  TableDynamicHeaderProps
>(({ sticky = true, showColumnSeparators = true, height, size = 'md', onHeaderHover }, ref) => {
  const { table, hoveredColumnId, setHoveredColumnId } = useTableContext();

  const headerGroups = table.getHeaderGroups();

  const handleMouseEnter = useCallback(
    (columnId: string) => {
      setHoveredColumnId(columnId);
      if (onHeaderHover) {
        onHeaderHover(columnId);
      }
    },
    [setHoveredColumnId, onHeaderHover],
  );

  const handleMouseLeave = useCallback(() => {
    setHoveredColumnId(null);
    if (onHeaderHover) {
      onHeaderHover(null);
    }
  }, [setHoveredColumnId, onHeaderHover]);

  return (
    <thead
      ref={ref}
      className={cn('bg-muted/50', sticky && 'sticky top-0 z-10')}
      style={{ height: height || HEADER_HEIGHTS[size] }}
    >
      {headerGroups.map((headerGroup) => (
        <tr key={headerGroup.id}>
          {headerGroup.headers.map((header) => {
            const meta = header.column.columnDef.meta as ColumnMeta;
            const isHovered = hoveredColumnId === header.id;
            const isSorted = header.column.getIsSorted();
            const canSort = header.column.getCanSort();

            return (
              <motion.th
                key={header.id}
                colSpan={header.colSpan}
                className={cn(
                  'h-full px-4 text-left align-middle font-medium',
                  'transition-colors duration-200',
                  showColumnSeparators && 'border-border border-r last:border-r-0',
                  meta?.sticky === true && 'bg-muted/90 sticky z-20 backdrop-blur-sm',
                  meta?.sticky === 'left' && 'left-0',
                  meta?.sticky === 'right' && 'right-0',
                  isHovered && 'bg-muted',
                  meta?.headerClassName as string,
                )}
                style={{
                  width: (meta?.width as string | number) || 'auto',
                  minWidth: (meta?.minWidth as string | number) || 'auto',
                  maxWidth: meta?.maxWidth as string | number,
                  left: meta?.sticky === 'left' ? (meta?.left as string | number) : undefined,
                  right: meta?.sticky === 'right' ? (meta?.right as string | number) : undefined,
                }}
                initial={false}
                animate={
                  isHovered
                    ? { backgroundColor: 'var(--muted)' }
                    : { backgroundColor: 'var(--muted-50)' }
                }
                onMouseEnter={() => handleMouseEnter(header.id)}
                onMouseLeave={handleMouseLeave}
              >
                <div
                  className={cn('flex items-center gap-2', canSort && 'cursor-pointer select-none')}
                >
                  {header.isPlaceholder ? null : (
                    <>
                      <div
                        className={cn('flex-1 truncate', canSort && 'flex items-center gap-1')}
                        onClick={canSort ? header.column.getToggleSortingHandler() : undefined}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {canSort && !isSorted && (
                          <ArrowUpDown className="text-muted-foreground/70 ml-1 h-3.5 w-3.5" />
                        )}
                        {isSorted === 'asc' && <ChevronUp className="ml-1 h-4 w-4" />}
                        {isSorted === 'desc' && <ChevronDown className="ml-1 h-4 w-4" />}
                      </div>

                      {/* Filter indicator */}
                      {header.column.getIsFiltered() && (
                        <Filter className="text-primary h-3.5 w-3.5" />
                      )}

                      {/* Tooltip if available */}
                      {meta?.headerTooltip && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="bg-muted-foreground/20 ml-1 rounded-full p-1">
                              <span className="sr-only">Info</span>
                              <span className="text-xs">?</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>{meta.headerTooltip as React.ReactNode}</TooltipContent>
                        </Tooltip>
                      )}
                    </>
                  )}
                </div>

                {/* Column aggregation display on hover */}
                {isHovered && meta?.aggregationFn && meta?.aggregationFormatter && (
                  <div className="text-muted-foreground mt-1 text-xs">
                    {meta.aggregationFormatter(
                      meta.aggregationFn(
                        table.getRowModel().rows.map((row) => row.getValue(header.id)),
                      ),
                    )}
                  </div>
                )}
              </motion.th>
            );
          })}
        </tr>
      ))}
    </thead>
  );
});

TableDynamicHeader.displayName = 'TableDynamicHeader';
