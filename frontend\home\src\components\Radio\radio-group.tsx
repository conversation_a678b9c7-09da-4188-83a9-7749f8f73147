'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { 
  RadioGroup as ShadcnRadioGroup, 
  RadioGroupItem as ShadcnRadioGroupItem 
} from '@/components/ui/radio-group';

const radioGroupVariants = cva(
  'grid gap-2',
  {
    variants: {
      variant: {
        default: '',
        card: 'border rounded-md p-4 shadow-sm',
        outline: 'border border-dashed rounded-md p-4',
        buttonCard: 'flex-wrap',
      },
      orientation: {
        vertical: 'grid',
        horizontal: 'flex flex-row gap-4',
      },
      size: {
        default: 'gap-2',
        sm: 'gap-1',
        lg: 'gap-3',
      },
    },
    defaultVariants: {
      variant: 'default',
      orientation: 'vertical',
      size: 'default',
    },
  }
);

export interface RadioGroupProps 
  extends Omit<React.ComponentPropsWithoutRef<typeof ShadcnRadioGroup>, 'orientation'>,
  VariantProps<typeof radioGroupVariants> {
  title?: string;
  description?: string;
  error?: string;
}

const RadioGroup = React.forwardRef<
  React.ElementRef<typeof ShadcnRadioGroup>,
  RadioGroupProps
>(({ 
  className, 
  variant, 
  orientation, 
  size,
  title,
  description, 
  error,
  children,
  ...props 
}, ref) => {
  return (
    <div className={cn("space-y-2", {
      "text-red-500": error
    })}>
      {title && (
        <div className="text-sm font-medium">
          {title}
        </div>
      )}
      {description && (
        <div className="text-sm text-gray-500">
          {description}
        </div>
      )}
      <ShadcnRadioGroup
        ref={ref}
        className={cn(
          radioGroupVariants({ variant, orientation, size }),
          className
        )}
        {...props}
      >
        {children}
      </ShadcnRadioGroup>
      {error && (
        <div className="text-sm text-red-500">
          {error}
        </div>
      )}
    </div>
  );
});
RadioGroup.displayName = 'RadioGroup';

export { RadioGroup };