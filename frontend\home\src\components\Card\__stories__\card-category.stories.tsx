import type { <PERSON>a, StoryObj } from '@storybook/react';
import { CardCategory } from '../card-category';
import { fixtures } from '../__fixtures__/card-category.fixtues';

const meta: Meta<typeof CardCategory> = {
  title: 'UI/Card/CardCategory',
  component: CardCategory,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A reusable card component for displaying product categories with a title and image.',
      },
    },
    a11y: {
      config: {
        rules: [
          { id: 'color-contrast', enabled: true },
          { id: 'image-alt-text', enabled: true },
        ],
      },
    },
  },
  argTypes: {
    onClick: { action: 'clicked' },
    title: {
      control: 'text',
      description: 'Category title displayed on the card',
    },
    imageUrl: {
      control: 'text',
      description: 'Image URL for the category',
    },
    imageAlt: {
      control: 'text',
      description: 'Alternative text for the image',
    },
    href: {
      control: 'text',
      description: 'URL to navigate to when card is clicked',
    },
    className: {
      control: 'text',
      description: 'Optional CSS class names',
    },
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div style={{ width: '350px' }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof CardCategory>;

export const WireCable: Story = {
  args: {
    ...fixtures.wireCable,
  },
};

export const PlugSocket: Story = {
  args: {
    ...fixtures.plugSocket,
  },
};

export const Breaker: Story = {
  args: {
    ...fixtures.breaker,
  },
};

export const Socket: Story = {
  args: {
    ...fixtures.socket,
  },
};

export const WiringEquipment: Story = {
  args: {
    ...fixtures.wiringEquipment,
  },
};

export const PowerBackup: Story = {
  args: {
    ...fixtures.powerBackup,
  },
};

// This grid layout matches exactly the design image
export const CategoryGrid: Story = {
  render: () => (
    <div className="bg-gray-100 p-4 w-full min-w-[1440px]">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <CardCategory {...fixtures.wireCable} />
        <CardCategory {...fixtures.plugSocket} />
        <CardCategory {...fixtures.breaker} />
        <CardCategory {...fixtures.socket} />
        <CardCategory {...fixtures.wiringEquipment} />
        <CardCategory {...fixtures.powerBackup} />
        <CardCategory {...fixtures.wireCable} />
        <CardCategory {...fixtures.plugSocket} />
      </div>
    </div>
  ),
  parameters: {
    layout: 'fullscreen',
  },
};