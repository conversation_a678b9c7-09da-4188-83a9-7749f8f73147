'use client';

import * as React from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { ResourcePermissionField } from './ApiKey-Fields';
import type { PermissionTypeValue } from './ApiKey-Schema';
import { Info, AlertTriangle, AlertCircle } from 'lucide-react';

interface ApiKeyPermissionSectionProps {
  /**
   * The current permission type selected in the form
   */
  permissionType: PermissionTypeValue;
  /**
   * i18n namespace for translations
   */
  i18nNamespace?: string;
  /**
   * i18n prefix for translation keys
   */
  i18nPrefix?: string;
  /**
   * Additional custom resources to display
   */
  customResources?: Array<{
    name: string;
    label: string;
    path: string;
  }>;
  /**
   * Visual variant of the permission section
   */
  variant?: 'default' | 'compact' | 'expanded';
  /**
   * Whether to show resource paths
   */
  showResourcePaths?: boolean;
  /**
   * Custom class name for the container
   */
  className?: string;
  /**
   * Animation speed for transitions
   */
  animationSpeed?: 'slow' | 'normal' | 'fast';
  /**
   * Custom permission changed handler
   */
  onPermissionChange?: (resource: string, value: 'None' | 'Read' | 'Write') => void;
  /**
   * Whether to group similar resources
   */
  groupResources?: boolean;
  /**
   * Required minimum access level for certain resources
   */
  requiredMinimumAccess?: Record<string, 'None' | 'Read' | 'Write'>;
}

/**
 * Reusable permission section component for ApiKey forms
 * Handles displaying permission settings when "Restricted" is selected
 */
export const ApiKeyPermissionSection: React.FC<ApiKeyPermissionSectionProps> = ({
  permissionType,
  i18nNamespace,
  i18nPrefix = 'apiKey',
  customResources = [],
  variant = 'default',
  showResourcePaths = true,
  className = '',
  animationSpeed = 'normal',
  onPermissionChange,
  groupResources = false,
  requiredMinimumAccess,
}) => {
  const { t } = useTranslation(i18nNamespace);

  // Only show resource permissions when "Restricted" is selected
  const showResourcePermissions = permissionType === 'Restricted';

  // Setup keyboard navigation for resource buttons
  const handleKeyDown = (
    e: React.KeyboardEvent,
    resource: string,
    currentValue: 'None' | 'Read' | 'Write',
  ) => {
    const permissionLevels = ['None', 'Read', 'Write'] as const;
    const currentIndex = permissionLevels.indexOf(currentValue);

    switch (e.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        // Move to next permission level (with wrap-around)
        if (currentIndex < permissionLevels.length - 1) {
          const nextValue = permissionLevels[currentIndex + 1];
          if (onPermissionChange && !isPermissionDisabled(nextValue, resource)) {
            onPermissionChange(resource, nextValue);
            e.preventDefault();
          }
        }
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        // Move to previous permission level (with wrap-around)
        if (currentIndex > 0) {
          const prevValue = permissionLevels[currentIndex - 1];
          if (onPermissionChange && !isPermissionDisabled(prevValue, resource)) {
            onPermissionChange(resource, prevValue);
            e.preventDefault();
          }
        }
        break;
      case ' ':
      case 'Enter':
        // Select current option
        // The click handler will handle this, but we prevent default
        e.preventDefault();
        break;
    }
  };

  // Check if a permission is disabled based on minimum required access
  const isPermissionDisabled = (value: 'None' | 'Read' | 'Write', resource: string): boolean => {
    if (!requiredMinimumAccess || !requiredMinimumAccess[resource]) return false;

    // Map permission levels to numeric values for comparison
    const permissionLevels = { None: 0, Read: 1, Write: 2 };
    const requiredLevel = permissionLevels[requiredMinimumAccess[resource]];
    const buttonLevel = permissionLevels[value];

    return buttonLevel < requiredLevel;
  };

  if (!showResourcePermissions) {
    return null;
  }

  // Function to get a localized label with fallback
  const getLabel = (key: string, defaultText: string) => {
    return t(`${i18nPrefix}.${key}`, defaultText);
  };

  // Animation styles based on speed setting
  const getAnimationClass = () => {
    switch (animationSpeed) {
      case 'slow':
        return 'transition-all duration-500';
      case 'fast':
        return 'transition-all duration-150';
      default:
        return 'transition-all duration-300';
    }
  };

  // Component variant styles
  const getVariantClass = () => {
    switch (variant) {
      case 'compact':
        return 'p-2 space-y-2';
      case 'expanded':
        return 'p-6 space-y-6';
      default:
        return 'p-4 space-y-4';
    }
  };

  // Handler for permission changes
  const handlePermissionChange = (resource: string, value: 'None' | 'Read' | 'Write') => {
    if (onPermissionChange) {
      onPermissionChange(resource, value);
    }
  };

  // Build class names for container
  const containerClasses = `border rounded-md ${getVariantClass()} ${getAnimationClass()} ${className}`;

  // Group-related resources for better organization
  const getGroupedResources = () => {
    if (!groupResources) return null;

    return (
      <>
        {/* Model-related resources */}
        <div className="mb-3 border-b pb-3">
          <h4 className="mb-2 text-sm font-medium text-gray-500" id="models-group">
            {getLabel('resources.groups.models', 'Model Resources')}
          </h4>
          <div role="region" aria-labelledby="models-group">
            <ResourcePermissionField
              name="models"
              label={getLabel('resources.models', 'Models')}
              resourcePath={showResourcePaths ? getLabel('resources.modelsPath', '/v1/models') : ''}
              onChange={(value) => handlePermissionChange('models', value)}
              requiredMinAccess={requiredMinimumAccess?.models}
              onKeyDown={(e, value) => handleKeyDown(e, 'models', value)}
            />
            <ResourcePermissionField
              name="modelCapabilities"
              label={getLabel('resources.modelCapabilities', 'Model capabilities')}
              resourcePath={
                showResourcePaths
                  ? getLabel(
                      'resources.modelCapabilitiesPath',
                      '/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations',
                    )
                  : ''
              }
              onChange={(value) => handlePermissionChange('modelCapabilities', value)}
              requiredMinAccess={requiredMinimumAccess?.modelCapabilities}
              onKeyDown={(e, value) => handleKeyDown(e, 'modelCapabilities', value)}
            />
          </div>
        </div>

        {/* Assistant-related resources */}
        <div className="mb-3 border-b pb-3">
          <h4 className="mb-2 text-sm font-medium text-gray-500" id="assistants-group">
            {getLabel('resources.groups.assistants', 'Assistant Resources')}
          </h4>
          <div role="region" aria-labelledby="assistants-group">
            <ResourcePermissionField
              name="assistants"
              label={getLabel('resources.assistants', 'Assistants')}
              resourcePath={
                showResourcePaths ? getLabel('resources.assistantsPath', '/v1/assistants') : ''
              }
              onChange={(value) => handlePermissionChange('assistants', value)}
              requiredMinAccess={requiredMinimumAccess?.assistants}
              onKeyDown={(e, value) => handleKeyDown(e, 'assistants', value)}
            />
            <ResourcePermissionField
              name="threads"
              label={getLabel('resources.threads', 'Threads')}
              resourcePath={
                showResourcePaths ? getLabel('resources.threadsPath', '/v1/threads') : ''
              }
              onChange={(value) => handlePermissionChange('threads', value)}
              requiredMinAccess={requiredMinimumAccess?.threads}
              onKeyDown={(e, value) => handleKeyDown(e, 'threads', value)}
            />
          </div>
        </div>

        {/* Other resources */}
        <div>
          <h4 className="mb-2 text-sm font-medium text-gray-500" id="other-resources-group">
            {getLabel('resources.groups.other', 'Other Resources')}
          </h4>
          <div role="region" aria-labelledby="other-resources-group">
            <ResourcePermissionField
              name="evals"
              label={getLabel('resources.evals', 'Evals')}
              resourcePath={showResourcePaths ? getLabel('resources.evalsPath', '/v1/evals') : ''}
              onChange={(value) => handlePermissionChange('evals', value)}
              requiredMinAccess={requiredMinimumAccess?.evals}
              onKeyDown={(e, value) => handleKeyDown(e, 'evals', value)}
            />
            <ResourcePermissionField
              name="fineTuning"
              label={getLabel('resources.fineTuning', 'Fine-tuning')}
              resourcePath={
                showResourcePaths ? getLabel('resources.fineTuningPath', '/v1/fine_tuning') : ''
              }
              onChange={(value) => handlePermissionChange('fineTuning', value)}
              requiredMinAccess={requiredMinimumAccess?.fineTuning}
              onKeyDown={(e, value) => handleKeyDown(e, 'fineTuning', value)}
            />
            <ResourcePermissionField
              name="files"
              label={getLabel('resources.files', 'Files')}
              resourcePath={showResourcePaths ? getLabel('resources.filesPath', '/v1/files') : ''}
              onChange={(value) => handlePermissionChange('files', value)}
              requiredMinAccess={requiredMinimumAccess?.files}
              onKeyDown={(e, value) => handleKeyDown(e, 'files', value)}
            />

            {/* Custom resources */}
            {customResources.map((resource) => (
              <ResourcePermissionField
                key={resource.name}
                name={resource.name}
                label={resource.label}
                resourcePath={showResourcePaths ? resource.path : ''}
                onChange={(value) => handlePermissionChange(resource.name, value)}
                requiredMinAccess={requiredMinimumAccess?.[resource.name]}
                onKeyDown={(e, value) => handleKeyDown(e, resource.name, value)}
              />
            ))}
          </div>
        </div>
      </>
    );
  };

  return (
    <div className={containerClasses} role="region" aria-labelledby="resources-section-title">
      <div className="flex items-center justify-between">
        <h3 className="font-medium" id="resources-section-title">
          {getLabel('resources.title', 'Resources')}
        </h3>
        <h3 className="font-medium" id="permissions-section-title">
          {getLabel('resources.permissions', 'Permissions')}
        </h3>
      </div>

      <div className="space-y-1 pt-2">
        {groupResources ? (
          getGroupedResources()
        ) : (
          <>
            {/* Models permission */}
            <ResourcePermissionField
              name="models"
              label={getLabel('resources.models', 'Models')}
              resourcePath={showResourcePaths ? getLabel('resources.modelsPath', '/v1/models') : ''}
              onChange={(value) => handlePermissionChange('models', value)}
              requiredMinAccess={requiredMinimumAccess?.models}
              onKeyDown={(e, value) => handleKeyDown(e, 'models', value)}
            />

            {/* Model capabilities permission */}
            <ResourcePermissionField
              name="modelCapabilities"
              label={getLabel('resources.modelCapabilities', 'Model capabilities')}
              resourcePath={
                showResourcePaths
                  ? getLabel(
                      'resources.modelCapabilitiesPath',
                      '/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations',
                    )
                  : ''
              }
              onChange={(value) => handlePermissionChange('modelCapabilities', value)}
              requiredMinAccess={requiredMinimumAccess?.modelCapabilities}
              onKeyDown={(e, value) => handleKeyDown(e, 'modelCapabilities', value)}
            />

            {/* Assistants permission */}
            <ResourcePermissionField
              name="assistants"
              label={getLabel('resources.assistants', 'Assistants')}
              resourcePath={
                showResourcePaths ? getLabel('resources.assistantsPath', '/v1/assistants') : ''
              }
              onChange={(value) => handlePermissionChange('assistants', value)}
              requiredMinAccess={requiredMinimumAccess?.assistants}
              onKeyDown={(e, value) => handleKeyDown(e, 'assistants', value)}
            />

            {/* Threads permission */}
            <ResourcePermissionField
              name="threads"
              label={getLabel('resources.threads', 'Threads')}
              resourcePath={
                showResourcePaths ? getLabel('resources.threadsPath', '/v1/threads') : ''
              }
              onChange={(value) => handlePermissionChange('threads', value)}
              requiredMinAccess={requiredMinimumAccess?.threads}
              onKeyDown={(e, value) => handleKeyDown(e, 'threads', value)}
            />

            {/* Evals permission */}
            <ResourcePermissionField
              name="evals"
              label={getLabel('resources.evals', 'Evals')}
              resourcePath={showResourcePaths ? getLabel('resources.evalsPath', '/v1/evals') : ''}
              onChange={(value) => handlePermissionChange('evals', value)}
              requiredMinAccess={requiredMinimumAccess?.evals}
              onKeyDown={(e, value) => handleKeyDown(e, 'evals', value)}
            />

            {/* Fine-tuning permission */}
            <ResourcePermissionField
              name="fineTuning"
              label={getLabel('resources.fineTuning', 'Fine-tuning')}
              resourcePath={
                showResourcePaths ? getLabel('resources.fineTuningPath', '/v1/fine_tuning') : ''
              }
              onChange={(value) => handlePermissionChange('fineTuning', value)}
              requiredMinAccess={requiredMinimumAccess?.fineTuning}
              onKeyDown={(e, value) => handleKeyDown(e, 'fineTuning', value)}
            />

            {/* Files permission */}
            <ResourcePermissionField
              name="files"
              label={getLabel('resources.files', 'Files')}
              resourcePath={showResourcePaths ? getLabel('resources.filesPath', '/v1/files') : ''}
              onChange={(value) => handlePermissionChange('files', value)}
              requiredMinAccess={requiredMinimumAccess?.files}
              onKeyDown={(e, value) => handleKeyDown(e, 'files', value)}
            />

            {/* Custom resources */}
            {customResources.map((resource) => (
              <ResourcePermissionField
                key={resource.name}
                name={resource.name}
                label={resource.label}
                resourcePath={showResourcePaths ? resource.path : ''}
                onChange={(value) => handlePermissionChange(resource.name, value)}
                requiredMinAccess={requiredMinimumAccess?.[resource.name]}
                onKeyDown={(e, value) => handleKeyDown(e, resource.name, value)}
              />
            ))}
          </>
        )}
      </div>
    </div>
  );
};

/**
 * Permission notification component
 * Shows message about permission changes taking time to take effect
 */
export const PermissionNotification: React.FC<{
  i18nNamespace?: string;
  i18nPrefix?: string;
  variant?: 'default' | 'info' | 'warning' | 'error';
  className?: string;
  icon?: React.ReactNode;
  animationSpeed?: 'slow' | 'normal' | 'fast';
  showIcon?: boolean;
}> = ({
  i18nNamespace,
  i18nPrefix = 'apiKey',
  variant = 'default',
  className = '',
  icon,
  animationSpeed = 'normal',
  showIcon = true,
}) => {
  const { t } = useTranslation(i18nNamespace);

  // Animation styles based on speed setting
  const getAnimationClass = () => {
    switch (animationSpeed) {
      case 'slow':
        return 'transition-all duration-700';
      case 'fast':
        return 'transition-all duration-300';
      default:
        return 'transition-all duration-500';
    }
  };

  // Styling based on variant
  const getVariantClass = () => {
    switch (variant) {
      case 'info':
        return 'bg-info/10 text-info-foreground';
      case 'warning':
        return 'bg-warning/10 text-warning-foreground';
      case 'error':
        return 'bg-destructive/10 text-destructive-foreground';
      default:
        return 'bg-muted/50 text-muted-foreground';
    }
  };

  // Default icon based on variant
  const getDefaultIcon = () => {
    switch (variant) {
      case 'info':
        return <Info className="h-4 w-4" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  // Display icon (custom or default)
  const displayIcon = showIcon ? icon || getDefaultIcon() : null;

  return (
    <div
      className={`rounded-md p-4 text-sm ${getVariantClass()} ${getAnimationClass()} ${className}`}
      role="status"
      aria-live="polite"
    >
      <span className="flex items-center gap-2">
        {displayIcon}
        {t(`${i18nPrefix}.descriptions.permissionChange`, 'Permission changes may take a few minutes to take effect.')}
      </span>
    </div>
  );
};

export default ApiKeyPermissionSection;
