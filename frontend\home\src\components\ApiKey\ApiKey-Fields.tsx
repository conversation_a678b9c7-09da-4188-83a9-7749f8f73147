'use client';

import * as React from 'react';
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { PermissionTypeValue, ResourcePermissionValue, OwnerTypeValue } from './ApiKey-Schema';

// Base props for all form fields
interface BaseFieldProps {
  name: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  description?: string;
  className?: string;
  i18nNamespace?: string;
  i18nPrefix?: string;
}

// Name field props
interface NameFieldProps extends BaseFieldProps {
  placeholder?: string;
  onChange?: (value: string) => void;
}

// Permission type field props
interface PermissionTypeFieldProps extends BaseFieldProps {
  onChange?: (value: PermissionTypeValue) => void;
}

// Resource permission field props
interface ResourcePermissionFieldProps extends BaseFieldProps {
  resourcePath: string;
  onChange?: (value: ResourcePermissionValue) => void;
  requiredMinAccess?: 'None' | 'Read' | 'Write';
  onKeyDown?: (e: React.KeyboardEvent, value: ResourcePermissionValue) => void;
}

// Project selection field props
interface ProjectFieldProps extends BaseFieldProps {
  options: { value: string; label: string }[];
  placeholder?: string;
  onChange?: (value: string) => void;
}

// Owner type field props
interface OwnerTypeFieldProps extends BaseFieldProps {
  onChange?: (value: OwnerTypeValue) => void;
}

/**
 * API Key Name Field with React Hook Form integration
 */
export const ApiKeyNameField = ({
  name = 'name',
  label = 'Name',
  required = false,
  disabled = false,
  description,
  className,
  placeholder = 'Optional',
  onChange,
  i18nNamespace,
  i18nPrefix = 'apiKey',
}: NameFieldProps) => {
  const { t } = useTranslation(i18nNamespace);
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const errorMessage = errors[name]?.message as string | undefined;
  const hasError = !!errorMessage;

  const displayLabel = t(`${i18nPrefix}.name.label`, { defaultValue: label });
  const displayPlaceholder = t(`${i18nPrefix}.name.placeholder`, { defaultValue: placeholder });

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <div className="flex justify-between">
          <Label htmlFor={name} className={cn(hasError && 'text-destructive')}>
            {displayLabel}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        </div>
      )}

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Input
            {...field}
            id={name}
            value={field.value || ''}
            placeholder={displayPlaceholder}
            disabled={disabled}
            aria-invalid={hasError}
            aria-describedby={
              hasError ? `${name}-error` : description ? `${name}-description` : undefined
            }
            className={cn(hasError && 'border-destructive')}
            onChange={(e) => {
              field.onChange(e);
              onChange?.(e.target.value);
            }}
          />
        )}
      />

      {description && !hasError && (
        <p id={`${name}-description`} className="text-muted-foreground text-sm">
          {description}
        </p>
      )}

      {hasError && (
        <p id={`${name}-error`} className="text-destructive text-sm font-medium" role="alert">
          {errorMessage}
        </p>
      )}
    </div>
  );
};

/**
 * Permission Type Selection Field
 */
export const PermissionTypeField = ({
  name = 'permissionType',
  label = 'Permissions',
  required = true,
  className,
  description,
  onChange,
  i18nNamespace,
  i18nPrefix = 'apiKey',
}: Omit<PermissionTypeFieldProps, 'disabled'>) => {
  const { t } = useTranslation(i18nNamespace);
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const errorMessage = errors[name]?.message as string | undefined;
  const hasError = !!errorMessage;

  const displayLabel = t(`${i18nPrefix}.permissionType.label`, { defaultValue: label });
  const allText = t(`${i18nPrefix}.permissionType.all`, 'All');
  const restrictedText = t(`${i18nPrefix}.permissionType.restricted`, 'Restricted');
  const readOnlyText = t(`${i18nPrefix}.permissionType.readOnly`, 'Read only');

  // Handle keyboard navigation between permission type options
  const handleKeyDown = (
    e: React.KeyboardEvent,
    currentValue: 'All' | 'Restricted' | 'Read only',
    setValue: (value: 'All' | 'Restricted' | 'Read only') => void,
  ) => {
    const options = ['All', 'Restricted', 'Read only'] as const;
    const currentIndex = options.indexOf(currentValue);

    switch (e.key) {
      case 'ArrowRight':
        // Move to next option (with wrap-around)
        if (currentIndex < options.length - 1) {
          const nextValue = options[currentIndex + 1];
          setValue(nextValue);
          if (onChange) onChange(nextValue);
          e.preventDefault();
        }
        break;
      case 'ArrowLeft':
        // Move to previous option (with wrap-around)
        if (currentIndex > 0) {
          const prevValue = options[currentIndex - 1];
          setValue(prevValue);
          if (onChange) onChange(prevValue);
          e.preventDefault();
        }
        break;
      case ' ':
      case 'Enter':
        // The click handler will handle selection, but prevent default
        e.preventDefault();
        break;
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <div className="flex justify-between">
          <Label htmlFor={name} className={cn(hasError && 'text-destructive')} id={`${name}-label`}>
            {displayLabel}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        </div>
      )}

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div
            className="flex flex-row space-x-2"
            role="radiogroup"
            aria-labelledby={`${name}-label`}
          >
            <div
              className={cn(
                'flex cursor-pointer items-center justify-center rounded-md px-4 py-2',
                field.value === 'All' ? 'bg-primary text-primary-foreground' : 'bg-muted',
              )}
              onClick={() => {
                field.onChange('All');
                onChange?.('All');
              }}
              onKeyDown={(e) => handleKeyDown(e, field.value, field.onChange)}
              role="radio"
              aria-checked={field.value === 'All'}
              tabIndex={field.value === 'All' ? 0 : -1}
            >
              {allText}
            </div>
            <div
              className={cn(
                'flex cursor-pointer items-center justify-center rounded-md px-4 py-2',
                field.value === 'Restricted' ? 'bg-primary text-primary-foreground' : 'bg-muted',
              )}
              onClick={() => {
                field.onChange('Restricted');
                onChange?.('Restricted');
              }}
              onKeyDown={(e) => handleKeyDown(e, field.value, field.onChange)}
              role="radio"
              aria-checked={field.value === 'Restricted'}
              tabIndex={field.value === 'Restricted' ? 0 : -1}
            >
              {restrictedText}
            </div>
            <div
              className={cn(
                'flex cursor-pointer items-center justify-center rounded-md px-4 py-2',
                field.value === 'Read only' ? 'bg-primary text-primary-foreground' : 'bg-muted',
              )}
              onClick={() => {
                field.onChange('Read only');
                onChange?.('Read only');
              }}
              onKeyDown={(e) => handleKeyDown(e, field.value, field.onChange)}
              role="radio"
              aria-checked={field.value === 'Read only'}
              tabIndex={field.value === 'Read only' ? 0 : -1}
            >
              {readOnlyText}
            </div>
          </div>
        )}
      />

      {description && !hasError && (
        <p id={`${name}-description`} className="text-muted-foreground text-sm">
          {description}
        </p>
      )}

      {hasError && (
        <p id={`${name}-error`} className="text-destructive text-sm font-medium" role="alert">
          {errorMessage}
        </p>
      )}
    </div>
  );
};

/**
 * Resource Permission Field
 */
export const ResourcePermissionField = ({
  name,
  resourcePath,
  label,
  className,
  onChange,
  i18nNamespace,
  i18nPrefix = 'apiKey',
  requiredMinAccess,
  onKeyDown,
}: ResourcePermissionFieldProps) => {
  const { t } = useTranslation(i18nNamespace);
  const { control } = useFormContext();
  const fieldName = `resourcePermissions.${name}`;

  const getNoneText = t(`${i18nPrefix}.resourcePermissions.none`, 'None');
  const getReadText = t(`${i18nPrefix}.resourcePermissions.read`, 'Read');
  const getWriteText = t(`${i18nPrefix}.resourcePermissions.write`, 'Write');

  // Determine if a button should be disabled based on minimum required access
  const isDisabled = (value: 'None' | 'Read' | 'Write') => {
    if (!requiredMinAccess) return false;

    // Map permission levels to numeric values for comparison
    const permissionLevels = { None: 0, Read: 1, Write: 2 };
    const requiredLevel = permissionLevels[requiredMinAccess];
    const buttonLevel = permissionLevels[value];

    return buttonLevel < requiredLevel;
  };

  // Get ARIA label for each permission button based on resource name and permission level
  const getButtonAriaLabel = (value: 'None' | 'Read' | 'Write', resourceName: string) => {
    const resourceLabel = label || resourceName;
    const permissionLabel =
      value === 'None' ? getNoneText : value === 'Read' ? getReadText : getWriteText;

    return t(
      `${i18nPrefix}.resourcePermissions.buttonAriaLabel`,
      `Set ${resourceLabel} permission to ${permissionLabel}`,
    );
  };

  return (
    <div className={cn('flex items-center justify-between py-1', className)}>
      <div className="flex flex-col">
        <span id={`resource-${name}-label`}>{label || name}</span>
        <span className="text-muted-foreground text-xs">{resourcePath}</span>
      </div>

      <Controller
        name={fieldName}
        control={control}
        render={({ field }) => (
          <div
            className="flex space-x-1"
            role="radiogroup"
            aria-labelledby={`resource-${name}-label`}
          >
            <div
              className={cn(
                'flex items-center justify-center rounded-md px-3 py-1 text-sm',
                field.value === 'None' ? 'bg-primary text-primary-foreground' : 'bg-muted',
                isDisabled('None') ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
              )}
              onClick={() => {
                if (!isDisabled('None')) {
                  field.onChange('None');
                  onChange?.('None');
                }
              }}
              role="radio"
              aria-checked={field.value === 'None'}
              aria-label={getButtonAriaLabel('None', name)}
              aria-disabled={isDisabled('None')}
              title={
                isDisabled('None') ? `Minimum required access: ${requiredMinAccess}` : undefined
              }
              tabIndex={field.value === 'None' ? 0 : -1}
              onKeyDown={(e) => {
                if (onKeyDown) {
                  onKeyDown(e, 'None');
                }
              }}
            >
              {getNoneText}
            </div>
            <div
              className={cn(
                'flex items-center justify-center rounded-md px-3 py-1 text-sm',
                field.value === 'Read' ? 'bg-primary text-primary-foreground' : 'bg-muted',
                isDisabled('Read') ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
              )}
              onClick={() => {
                if (!isDisabled('Read')) {
                  field.onChange('Read');
                  onChange?.('Read');
                }
              }}
              role="radio"
              aria-checked={field.value === 'Read'}
              aria-label={getButtonAriaLabel('Read', name)}
              aria-disabled={isDisabled('Read')}
              title={
                isDisabled('Read') ? `Minimum required access: ${requiredMinAccess}` : undefined
              }
              tabIndex={field.value === 'Read' ? 0 : -1}
              onKeyDown={(e) => {
                if (onKeyDown) {
                  onKeyDown(e, 'Read');
                }
              }}
            >
              {getReadText}
            </div>
            <div
              className={cn(
                'flex items-center justify-center rounded-md px-3 py-1 text-sm',
                field.value === 'Write' ? 'bg-primary text-primary-foreground' : 'bg-muted',
                isDisabled('Write') ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
              )}
              onClick={() => {
                if (!isDisabled('Write')) {
                  field.onChange('Write');
                  onChange?.('Write');
                }
              }}
              role="radio"
              aria-checked={field.value === 'Write'}
              aria-label={getButtonAriaLabel('Write', name)}
              aria-disabled={isDisabled('Write')}
              title={
                isDisabled('Write') ? `Minimum required access: ${requiredMinAccess}` : undefined
              }
              tabIndex={field.value === 'Write' ? 0 : -1}
              onKeyDown={(e) => {
                if (onKeyDown) {
                  onKeyDown(e, 'Write');
                }
              }}
            >
              {getWriteText}
            </div>
          </div>
        )}
      />
    </div>
  );
};

/**
 * Owner Type Field (for choosing between personal or service account)
 */
export const OwnerTypeField = ({
  name = 'ownerType',
  label = 'Owned by',
  required = true,
  className,
  onChange,
  i18nNamespace,
  i18nPrefix = 'apiKey',
}: Omit<OwnerTypeFieldProps, 'disabled' | 'description'>) => {
  const { t } = useTranslation(i18nNamespace);
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const errorMessage = errors[name]?.message as string | undefined;
  const hasError = !!errorMessage;

  const displayLabel = t(`${i18nPrefix}.ownerType.label`, { defaultValue: label });
  const youText = t(`${i18nPrefix}.ownerType.you`, 'You');
  const serviceAccountText = t(`${i18nPrefix}.ownerType.serviceAccount`, 'Service account');

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <div className="flex justify-between">
          <Label htmlFor={name} className={cn(hasError && 'text-destructive')}>
            {displayLabel}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        </div>
      )}

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div className="flex flex-row space-x-2">
            <div
              className={cn(
                'flex cursor-pointer items-center justify-center rounded-md px-4 py-2',
                field.value === 'You' ? 'bg-primary text-primary-foreground' : 'bg-muted',
              )}
              onClick={() => {
                field.onChange('You');
                onChange?.('You');
              }}
            >
              {youText}
            </div>
            <div
              className={cn(
                'flex cursor-pointer items-center justify-center rounded-md px-4 py-2',
                field.value === 'Service account'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted',
              )}
              onClick={() => {
                field.onChange('Service account');
                onChange?.('Service account');
              }}
            >
              {serviceAccountText}
            </div>
          </div>
        )}
      />

      {hasError && (
        <p id={`${name}-error`} className="text-destructive text-sm font-medium" role="alert">
          {errorMessage}
        </p>
      )}
    </div>
  );
};

/**
 * Project Selection Field
 */
export const ProjectField = ({
  name = 'project',
  label = 'Project',
  required = true,
  disabled = false,
  description,
  className,
  options,
  placeholder = 'Select a project',
  onChange,
  i18nNamespace,
  i18nPrefix = 'apiKey',
}: ProjectFieldProps) => {
  const { t } = useTranslation(i18nNamespace);
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const errorMessage = errors[name]?.message as string | undefined;
  const hasError = !!errorMessage;

  const displayLabel = t(`${i18nPrefix}.project.label`, { defaultValue: label });
  const displayPlaceholder = t(`${i18nPrefix}.project.placeholder`, { defaultValue: placeholder });

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex justify-between">
        <Label htmlFor={name} className={cn(hasError && 'text-destructive')}>
          {displayLabel}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>
      </div>

      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Select
            disabled={disabled}
            onValueChange={(value) => {
              field.onChange(value);
              onChange?.(value);
            }}
            value={field.value}
          >
            <SelectTrigger
              id={name}
              aria-invalid={hasError}
              aria-describedby={
                hasError ? `${name}-error` : description ? `${name}-description` : undefined
              }
              className={cn(hasError && 'border-destructive')}
            >
              <SelectValue placeholder={displayPlaceholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      />

      {description && !hasError && (
        <p id={`${name}-description`} className="text-muted-foreground text-sm">
          {description}
        </p>
      )}

      {hasError && (
        <p id={`${name}-error`} className="text-destructive text-sm font-medium" role="alert">
          {errorMessage}
        </p>
      )}
    </div>
  );
};
