'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

type Route = {
  path: string;
  labelKey: string;
  defaultLabel: string;
};

interface ExampleNavigationProps {
  routes: Route[];
  basePath: string;
}

export function ExampleNavigation({ routes, basePath }: ExampleNavigationProps) {
  const pathname = usePathname();

  return (
    <nav className="bg-muted/40 mb-4 flex overflow-x-auto rounded-lg p-2">
      <ul className="flex min-w-full flex-none gap-2">
        {routes.map((route) => {
          const fullPath = `${basePath}/${route.path}`;
          const isActive = pathname === fullPath;

          return (
            <li key={route.path}>
              <Link
                href={fullPath}
                className={`ring-offset-background focus-visible:ring-ring inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium whitespace-nowrap transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 ${
                  isActive
                    ? 'bg-primary text-primary-foreground'
                    : 'hover:bg-muted hover:text-muted-foreground'
                }`}
              >
                {route.defaultLabel}
              </Link>
            </li>
          );
        })}
      </ul>
    </nav>
  );
}
