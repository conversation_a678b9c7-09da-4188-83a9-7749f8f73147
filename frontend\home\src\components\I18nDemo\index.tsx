'use client';

import { useState } from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useLanguageChange } from '@/hooks/useLanguageChange';

export function I18nDemo() {
  const { t } = useTranslation();
  const currentLang = useLanguageChange();
  const [clickCount, setClickCount] = useState(0);

  const handleClick = () => {
    setClickCount((prev) => prev + 1);
  };

  return (
    <Card className="w-[350px]">
      <CardHeader>
        <CardTitle>{t('app.title')}</CardTitle>
        <CardDescription>{t('app.description')}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center rounded-md border p-4">
          <Button onClick={handleClick} className="mb-3">
            {t('buttons.clickMe')}
          </Button>
          <p className="mt-2 text-sm">
            {clickCount > 0 ? (
              <span>
                {t('buttons.clickMe')} {t('buttons.clickMe')} {t('buttons.clickMe')}!<br />(
                {clickCount} {clickCount === 1 ? 'click' : 'clicks'})
              </span>
            ) : (
              <span>
                {t('language.switchLanguage')} {t('buttons.clickMe')}
              </span>
            )}
          </p>
        </div>
      </CardContent>
      <CardFooter className="text-muted-foreground text-xs">
        Current language: {currentLang}
      </CardFooter>
    </Card>
  );
}
