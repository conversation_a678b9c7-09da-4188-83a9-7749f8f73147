import React, { forwardRef, useState, ChangeEvent } from 'react';
import { Input as ShadcnInput } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Minus, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface InputNumberProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'value' | 'onChange'> {
  label?: string;
  helperText?: string;
  
  // Number specific props
  value?: number;
  defaultValue?: number;
  onChange?: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  
  // Style props
  size?: 'default' | 'sm' | 'lg';
  inputWrapperClassName?: string;
}

const InputNumber = forwardRef<HTMLInputElement, InputNumberProps>(
  ({ 
    className, 
    label, 
    helperText, 
    id,
    // Number specific props
    value: propValue,
    defaultValue = 0,
    onChange,
    min,
    max,
    step = 1,
    // Style props
    size = 'default',
    inputWrapperClassName,
    ...props 
  }, ref) => {
    // State management
    const [value, setValue] = useState<number>(
      propValue !== undefined ? propValue : (defaultValue || 0)
    );
    
    // Handle controlled component
    const currentValue = propValue !== undefined ? propValue : value;
    
    // Determine size classes for all elements
    const containerSizeClass = {
      sm: "h-8",
      default: "h-10",
      lg: "h-14"
    }[size];
    
    const inputSizeClass = {
      sm: "text-sm h-8 py-1",
      default: "text-base h-10 py-2",
      lg: "text-xl h-14 py-3"
    }[size];
    
    const buttonSizeClass = {
      sm: "h-8 w-8 px-2",
      default: "h-10 w-10 px-3",
      lg: "h-14 w-14 px-4"
    }[size];
    
    const iconSizeClass = {
      sm: "h-3 w-3",
      default: "h-4 w-4", 
      lg: "h-6 w-6"
    }[size];
    
    // Handlers
    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
      // Convert to number and validate
      let newValue = e.target.value === '' ? 0 : parseFloat(e.target.value);
      
      if (isNaN(newValue)) return;
      
      // Apply min/max constraints
      if (min !== undefined && newValue < min) newValue = min;
      if (max !== undefined && newValue > max) newValue = max;
      
      // Update state
      setValue(newValue);
      
      // Call external handler
      if (onChange) {
        onChange(newValue);
      }
    };
    
    const handleIncrement = () => {
      let newValue = currentValue + step;
      
      // Apply max constraint
      if (max !== undefined && newValue > max) newValue = max;
      
      setValue(newValue);
      
      if (onChange) {
        onChange(newValue);
      }
    };
    
    const handleDecrement = () => {
      let newValue = currentValue - step;
      
      // Apply min constraint
      if (min !== undefined && newValue < min) newValue = min;
      
      setValue(newValue);
      
      if (onChange) {
        onChange(newValue);
      }
    };
    
    // Handle keydown for keyboard accessibility
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        handleIncrement();
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        handleDecrement();
      }
      
      // Call the original onKeyDown if it exists
      if (props.onKeyDown) {
        props.onKeyDown(e);
      }
    };
    
    return (
      <div className="w-full space-y-2" data-testid="input-number-container">
        {label && (
          <Label 
            htmlFor={id}
            className="text-lg font-medium"
          >
            {label}
          </Label>
        )}
        
        <div className={cn("flex rounded-xl overflow-hidden border border-gray-200", containerSizeClass, inputWrapperClassName)}>
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className={cn(
              "rounded-l-xl border-r border-gray-200 hover:bg-gray-100",
              buttonSizeClass
            )}
            onClick={handleDecrement}
            disabled={min !== undefined && currentValue <= min}
            aria-label="Decrease value"
            data-testid="decrement-button"
          >
            <Minus className={iconSizeClass} />
          </Button>
          
          <ShadcnInput
            id={id}
            ref={ref}
            type="number"
            value={currentValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            min={min}
            max={max}
            step={step}
            className={cn(
              "w-full border-none text-center focus-visible:ring-0",
              // Hide default spinner buttons for number input
              "[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",
              inputSizeClass,
              className
            )}
            aria-valuemin={min}
            aria-valuemax={max}
            aria-valuenow={currentValue}
            aria-valuetext={currentValue.toString()}
            {...props}
          />
          
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className={cn(
              "rounded-r-xl border-l border-gray-200 hover:bg-gray-100",
              buttonSizeClass
            )}
            onClick={handleIncrement}
            disabled={max !== undefined && currentValue >= max}
            aria-label="Increase value"
            data-testid="increment-button"
          >
            <Plus className={iconSizeClass} />
          </Button>
        </div>
        
        {helperText && (
          <p 
            id={`${id}-helper`}
            className="text-sm text-muted-foreground"
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

InputNumber.displayName = 'InputNumber';

export { InputNumber };