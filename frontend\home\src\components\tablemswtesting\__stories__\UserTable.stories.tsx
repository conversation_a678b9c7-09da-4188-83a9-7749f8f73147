import type { Meta, StoryObj } from '@storybook/react';
import { UserTable } from '../UserTable';
import { TableMswHandlers } from '../../../../.storybook/mswHandlers/TableMsw.handlers';
import { usersList } from '../__fixtures__/TableMsw.mockData';

const meta = {
  title: 'Components/TableMswTesting/UserTable',
  component: UserTable,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'A table component for displaying user data with MSW integration'
      }
    },
    msw: {
      handlers: [...TableMswHandlers],
    },
  },
  tags: ['autodocs'],
  argTypes: {
    onUserSelect: { action: 'userSelected' },
    simulateCondition: {
      control: 'select',
      options: [undefined, 'error', 'empty', 'slow'],
      description: 'Simulate different API response conditions'
    }
  },
} satisfies Meta<typeof UserTable>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story - normal operation
export const Default: Story = {
  args: {
    endpoint: '/api/users',
  },
};

// Loading state
export const Loading: Story = {
  args: {
    loading: true,
  },
};

// Error state
export const Error: Story = {
  args: {
    error: 'Failed to load users. Please try again later.',
  },
};

// Empty state
export const Empty: Story = {
  args: {
    endpoint: '/api/users',
    simulateCondition: 'empty',
  },
};

// Slow network condition
export const SlowNetwork: Story = {
  args: {
    endpoint: '/api/users',
    simulateCondition: 'slow',
  },
};

// Server error condition
export const ServerError: Story = {
  args: {
    endpoint: '/api/users',
    simulateCondition: 'error',
  },
}; 