import FlashSaleBanner from "../banner-flash-sale";

// Create fixture date that is 2 hours from when the component is rendered
const createEndTime = (hoursFromNow: number = 2) => {
  return new Date(Date.now() + 1000 * 60 * 60 * hoursFromNow);
};

export const defaultFixture = {
  component: FlashSaleBanner,
  props: {
    endTime: createEndTime(),
    title: "FLASH SALE",
    subtitle: "แสงอรุณนครินทร์",
  },
};

export const withDiscount = {
  component: FlashSaleBanner,
  props: {
    endTime: createEndTime(),
    title: "FLASH SALE",
    subtitle: "แสงอรุณนครินทร์",
    discount: "-11%",
    showDiscount: true,
  },
};

export const almostEndingFixture = {
  component: FlashSaleBanner,
  props: {
    endTime: createEndTime(0.01), // 36 seconds from now
    title: "HURRY UP!",
    subtitle: "โปรโมชั่นกำลังจะหมด",
    variant: "danger" as const,
  },
};

export const primaryVariantFixture = {
  component: FlashSaleBanner,
  props: {
    endTime: createEndTime(),
    title: "MEMBERS SALE",
    subtitle: "สำหรับสมาชิก",
    variant: "primary" as const,
    discount: "-15%",
    showDiscount: true,
  },
};

export const secondaryVariantFixture = {
  component: FlashSaleBanner,
  props: {
    endTime: createEndTime(6), // 6 hours
    title: "WEEKEND SALE",
    subtitle: "เฉพาะสุดสัปดาห์",
    variant: "secondary" as const,
  },
};

export const largeFixture = {
  component: FlashSaleBanner,
  props: {
    endTime: createEndTime(),
    title: "BIG FLASH SALE",
    subtitle: "ลดราคาพิเศษ",
    size: "lg" as const,
    discount: "-20%",
    showDiscount: true,
  },
};

export const customIconFixture = {
  component: FlashSaleBanner,
  props: {
    endTime: createEndTime(24), // 24 hours
    title: "24-HOUR SALE",
    subtitle: "ลดราคา 24 ชั่วโมง",
    // This would be replaced by an actual icon in real implementation
    icon: "⏰",
    iconPosition: "left" as const,
  },
};

export const allFixtures = [
  { name: "Default", ...defaultFixture },
  { name: "With Discount", ...withDiscount },
  { name: "Almost Ending", ...almostEndingFixture },
  { name: "Primary Variant", ...primaryVariantFixture },
  { name: "Secondary Variant", ...secondaryVariantFixture },
  { name: "Large Size", ...largeFixture },
  { name: "Custom Icon", ...customIconFixture },
];