import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import FlashSaleBanner from "../banner-flash-sale";
import { 
  defaultFixture,
  withDiscount,
  almostEndingFixture,
  primaryVariantFixture,
  secondaryVariantFixture,
  largeFixture,
  customIconFixture
} from "../__fixtures__/banner-flash-sale.fixtures";
import { userEvent, within, expect } from "@storybook/test";
import { Clock } from "lucide-react";

/**
 * Flash Sale Banner component displays a promotional banner with a countdown timer.
 * 
 * This component is commonly used for limited-time promotions, such as flash sales,
 * temporary discounts, or special offers.
 */
const meta: Meta<typeof FlashSaleBanner> = {
  title: "UI/Banner/BannerFlashSale",
  component: FlashSaleBanner,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: "A flash sale banner that displays countdown information for time-sensitive promotions."
      }
    },
    a11y: {
      config: {
        // A11y audit rules
        rules: [
          {
            id: "color-contrast",
            reviewOnFail: true,
          },
          {
            id: "aria-valid-attr",
            reviewOnFail: true,
          }
        ],
      },
    },
  },
  argTypes: {
    endTime: {
      control: {
        type: 'date'
      },
      description: "The end time for the sale countdown",
      transform: (value: string) => {
        if (!value) return new Date();
        return new Date(value);
      }
    },
    title: {
      control: "text",
      description: "Main title text displayed in the banner",
    },
    subtitle: {
      control: "text",
      description: "Subtitle displayed above the main title",
    },
    discount: {
      control: "text",
      description: "Discount text to display (e.g., '-11%')",
    },
    showDiscount: {
      control: "boolean",
      description: "Whether to show the discount badge",
    },
    variant: {
      options: ["default", "primary", "secondary", "danger"],
      control: { type: "select" },
      description: "Visual style variant of the banner",
    },
    size: {
      options: ["sm", "default", "lg"],
      control: { type: "select" },
      description: "Size variant of the banner",
    },
    iconPosition: {
      options: ["left", "right"],
      control: { type: "radio" },
      description: "Position of the icon relative to the title",
    },
    onTimeEnd: {
      action: "Timer ended",
      description: "Callback function when timer reaches zero",
    },
    icon: {
      control: { disable: true },
      description: "Custom icon element to display",
    },
  },
  args: {
    ...defaultFixture.props,
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="w-full max-w-2xl">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof FlashSaleBanner>;

/**
 * Default Flash Sale Banner with standard styling and configuration.
 */
export const Default: Story = {
  args: {
    ...defaultFixture.props,
  },
};

/**
 * Flash Sale Banner with a discount badge.
 */
export const WithDiscount: Story = {
  args: {
    ...withDiscount.props,
  },
};

/**
 * Flash Sale Banner with "danger" styling for urgency when time is almost up.
 */
export const AlmostEnding: Story = {
  args: {
    ...almostEndingFixture.props,
  },
};

/**
 * Primary variant of the Flash Sale Banner.
 */
export const PrimaryVariant: Story = {
  args: {
    ...primaryVariantFixture.props,
  },
};

/**
 * Secondary variant of the Flash Sale Banner.
 */
export const SecondaryVariant: Story = {
  args: {
    ...secondaryVariantFixture.props,
  },
};

/**
 * Large size variant of the Flash Sale Banner.
 */
export const LargeSize: Story = {
  args: {
    ...largeFixture.props,
  },
};

/**
 * Flash Sale Banner with custom icon and left positioning.
 */
export const CustomIcon: Story = {
  args: {
    ...customIconFixture.props,
    icon: <Clock className="h-6 w-6 fill-current" />,
  },
};

/**
 * Interactive example showing the callback when timer ends.
 */
export const WithTimeEndCallback: Story = {
  args: {
    endTime: new Date(Date.now() + 5000), // 5 seconds from now
    title: "ENDING SOON",
    subtitle: "โปรโมชั่นกำลังจะหมด",
    variant: "danger",
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // Wait for the timer to end (6 seconds to be safe)
    await new Promise(resolve => setTimeout(resolve, 6000));
    
    // Verify that the callback was called
    await expect(args.onTimeEnd).toHaveBeenCalled();
  }
};

/**
 * Accessibility and keyboard navigation test.
 */
export const AccessibilityTest: Story = {
  args: {
    ...defaultFixture.props,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Focus the element
    const banner = canvas.getByTestId("flash-sale-banner");
    await userEvent.tab();
    
    // Verify accessibility attributes
    expect(banner).toHaveAttribute("role", "alert");
    expect(banner).toHaveAttribute("aria-live", "polite");
  }
};