import type { ModalProps } from '../modal';

// Base fixture
export const baseModalFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  title: 'Fixture Modal',
  description: 'This is a modal fixture for testing',
  size: 'default',
  position: 'default',
  showCloseButton: true,
};

// Size variants
export const smallModalFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  ...baseModalFixture,
  size: 'sm',
};

export const largeModalFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  ...baseModalFixture,
  size: 'lg',
};

export const xlModalFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  ...baseModalFixture,
  size: 'xl',
};

export const fullModalFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  ...baseModalFixture,
  size: 'full',
};

// Position variants
export const topModalFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  ...baseModalFixture,
  position: 'top',
};

export const centerModalFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  ...baseModalFixture,
  position: 'center',
};

// No close button
export const noCloseButtonFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  ...baseModalFixture,
  showCloseButton: false,
};

// No header
export const noHeaderFixture: Omit<ModalProps, 'isOpen' | 'onClose'> = {
  title: undefined,
  description: undefined,
  size: 'default',
  position: 'default',
  showCloseButton: true,
};

// Test data for mock content
export const modalTestContent = {
  shortText: 'This is a short content for testing.',
  longText: Array(10).fill(0).map((_, i) => `This is paragraph ${i + 1} with some sample text for testing overflow and scrolling behavior in the modal component. The modal should handle long content gracefully.`).join('\n\n'),
  formFields: ['Name', 'Email', 'Phone', 'Message'],
  buttonLabels: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save Changes',
    delete: 'Delete',
  },
};