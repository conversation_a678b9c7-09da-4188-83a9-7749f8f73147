import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/lib/utils';

export type LinkItem = {
  label: string;
  href: string;
};

export interface CardContentLinkProps {
  title: string;
  description?: string;
  links: LinkItem[];
  image?: {
    src: string;
    alt: string;
    width?: number;
    height?: number;
  };
  secondaryImage?: {
    src: string;
    alt: string;
    width?: number;
    height?: number;
  };
  className?: string;
  onClick?: (href: string) => void;
}

const CardContentLink = ({
  title,
  description,
  links,
  image,
  secondaryImage,
  className,
  onClick,
}: CardContentLinkProps) => {

  // Handle link click
  const handleLinkClick = (href: string) => (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick(href);
    }
  };

  return (
    <div 
      className={cn(
        'rounded-xl bg-gray-50 p-6',
        className
      )}
      data-testid="card-content-link"
    >
      {/* Header section */}
      <div className="mb-6">
        <h2 
          className="text-xl font-medium mb-2"
          data-testid="card-title"
        >
          {title}
        </h2>
        
        {description && (
          <p 
            className="text-gray-600 text-sm" 
            data-testid="card-description"
          >
            {description}
          </p>
        )}
      </div>
      
      {/* Content section */}
      <div className="flex">
        {/* Links section */}
        <div className="w-1/2">
          <ul className="space-y-3" data-testid="card-links-list">
            {links.map((link, index) => (
              <li key={index}>
                <Link 
                  href={link.href}
                  className="text-gray-700 hover:text-blue-600 transition-colors"
                  onClick={handleLinkClick(link.href)}
                  data-testid={`card-link-${index}`}
                >
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
        
        {/* Primary Image section */}
        {image && (
          <div 
            className="w-1/2 flex justify-center items-start" 
            data-testid="card-image-wrapper"
          >
            <Image
              src={image.src}
              alt={image.alt}
              width={image.width || 200}
              height={image.height || 200}
              className="object-contain"
              data-testid="card-image"
            />
          </div>
        )}
      </div>

      {/* Secondary Image section (below) */}
      {secondaryImage && (
        <div className="mt-6" data-testid="secondary-image-wrapper">
          <Image
            src={secondaryImage.src}
            alt={secondaryImage.alt}
            width={secondaryImage.width || 300}
            height={secondaryImage.height || 100}
            className="object-contain w-full"
            data-testid="secondary-image"
          />
        </div>
      )}
    </div>
  );
};

export default CardContentLink;