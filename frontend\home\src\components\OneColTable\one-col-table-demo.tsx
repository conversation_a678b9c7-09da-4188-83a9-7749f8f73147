'use client';

import React, { useState } from 'react';
import { Clock, Check, AlertCircle, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import OneColTable from './one-col-table';
import { OneColTableLayout } from './one-col-table-layout';
import { TableItem } from './types';
import { mockItems } from './__fixtures__/one-col-table.fixtures';
import { getStatusColorClass } from './utils';
import { cn } from '@/lib/utils';

const OneColTableDemo = () => {
  const [loading, setLoading] = useState(false);
  const [visibleItems, setVisibleItems] = useState<TableItem[]>(mockItems.slice(0, 5));

  const handleRowClick = (item: TableItem) => {
    console.warn('Row clicked:', item);
  };

  const handleLoadMore = () => {
    setLoading(true);
    // Simulate loading delay
    setTimeout(() => {
      setVisibleItems((prev) => [...prev, ...mockItems.slice(prev.length, prev.length + 5)]);
      setLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-10 p-6">
      <Tabs defaultValue="basic">
        <TabsList className="mb-4">
          <TabsTrigger value="basic">Basic Components</TabsTrigger>
          <TabsTrigger value="layout">Layout Component</TabsTrigger>
          <TabsTrigger value="variants">Variants</TabsTrigger>
          <TabsTrigger value="advanced">Advanced Features</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-8">
          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Basic Usage</h2>
            <OneColTable
              items={visibleItems}
              onRowClick={handleRowClick}
              loading={loading}
              showLoadMore={visibleItems.length < mockItems.length}
              onLoadMore={handleLoadMore}
              hasMore={visibleItems.length < mockItems.length}
            />
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold">With Custom Header</h2>
            <OneColTable
              items={mockItems.slice(0, 5)}
              onRowClick={handleRowClick}
              header={
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Recent Items</h3>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </div>
              }
            />
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold">States</h2>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <h3 className="mb-2 text-lg font-semibold">Loading</h3>
                <OneColTable loading items={mockItems.slice(0, 5)} onRowClick={handleRowClick} />
              </div>

              <div>
                <h3 className="mb-2 text-lg font-semibold">Disabled</h3>
                <OneColTable disabled items={mockItems.slice(0, 5)} onRowClick={handleRowClick} />
              </div>

              <div>
                <h3 className="mb-2 text-lg font-semibold">Error</h3>
                <OneColTable
                  error="Failed to load items"
                  items={mockItems.slice(0, 5)}
                  onRowClick={handleRowClick}
                />
              </div>

              <div>
                <h3 className="mb-2 text-lg font-semibold">Empty</h3>
                <OneColTable items={[]} onRowClick={handleRowClick} />
              </div>
            </div>
          </section>
        </TabsContent>

        <TabsContent value="layout" className="space-y-8">
          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Layout with Search and Pagination</h2>
            <OneColTableLayout
              title="Batch Processing Items"
              items={mockItems}
              showSearch
              showPagination
              pageSize={5}
              onRowClick={handleRowClick}
              sortableFields={[
                { field: 'timestamp', label: 'Date' },
                { field: 'content', label: 'Content' },
                { field: 'status', label: 'Status' },
              ]}
            />
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Layout with Infinite Loading</h2>
            <OneColTableLayout
              title="Infinite Loading Example"
              items={mockItems}
              showSearch
              showSort
              infiniteLoading
              onRowClick={handleRowClick}
            />
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Layout with Virtualization (for large datasets)</h2>
            <OneColTableLayout
              title="Virtualized Table"
              items={Array.from({ length: 1000 }).map((_, i) => ({
                id: `item-${i}`,
                content: `Item ${i + 1}`,
                secondaryContent: `Description for item ${i + 1}`,
                timestamp: new Date(Date.now() - i * 3600000),
                status:
                  i % 5 === 0
                    ? 'completed'
                    : i % 5 === 1
                      ? 'in-progress'
                      : i % 5 === 2
                        ? 'pending'
                        : i % 5 === 3
                          ? 'warning'
                          : 'error',
              }))}
              virtualized
              showSearch
              showSort
              onRowClick={handleRowClick}
            />
          </section>
        </TabsContent>

        <TabsContent value="variants" className="space-y-8">
          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Variants</h2>
            <div className="space-y-6">
              <div>
                <h3 className="mb-2 text-lg font-semibold">Primary</h3>
                <OneColTable
                  variant="primary"
                  items={mockItems.slice(0, 3)}
                  onRowClick={handleRowClick}
                />
              </div>

              <div>
                <h3 className="mb-2 text-lg font-semibold">Secondary</h3>
                <OneColTable
                  variant="secondary"
                  items={mockItems.slice(0, 3)}
                  onRowClick={handleRowClick}
                />
              </div>

              <div>
                <h3 className="mb-2 text-lg font-semibold">Outline</h3>
                <OneColTable
                  variant="outline"
                  items={mockItems.slice(0, 3)}
                  onRowClick={handleRowClick}
                />
              </div>
            </div>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Sizes</h2>
            <div className="space-y-6">
              <div>
                <h3 className="mb-2 text-lg font-semibold">Small</h3>
                <OneColTable size="sm" items={mockItems.slice(0, 3)} onRowClick={handleRowClick} />
              </div>

              <div>
                <h3 className="mb-2 text-lg font-semibold">Medium (Default)</h3>
                <OneColTable size="md" items={mockItems.slice(0, 3)} onRowClick={handleRowClick} />
              </div>

              <div>
                <h3 className="mb-2 text-lg font-semibold">Large</h3>
                <OneColTable size="lg" items={mockItems.slice(0, 3)} onRowClick={handleRowClick} />
              </div>
            </div>
          </section>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-8">
          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Animation Variations</h2>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-2 text-lg font-semibold">With Animation (Default)</h3>
                <OneColTable
                  items={mockItems.slice(0, 5)}
                  onRowClick={handleRowClick}
                  animate={true}
                />
              </div>

              <div>
                <h3 className="mb-2 text-lg font-semibold">Without Animation</h3>
                <OneColTable
                  items={mockItems.slice(0, 5)}
                  onRowClick={handleRowClick}
                  animate={false}
                />
              </div>
            </div>
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Enhanced Status Indicators</h2>
            <OneColTable
              items={mockItems.slice(0, 8).map((item: TableItem) => ({
                ...item,
                content: (
                  <div className="flex items-center">
                    <div
                      className={cn(
                        'mr-3 flex h-8 w-8 items-center justify-center rounded-full',
                        item.status === 'completed'
                          ? 'bg-green-100'
                          : item.status === 'in-progress'
                            ? 'bg-blue-100'
                            : item.status === 'pending'
                              ? 'bg-amber-100'
                              : item.status === 'warning'
                                ? 'bg-orange-100'
                                : 'bg-red-100',
                      )}
                    >
                      {item.status === 'completed' ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : item.status === 'in-progress' ? (
                        <Clock className="h-4 w-4 text-blue-600" />
                      ) : item.status === 'pending' ? (
                        <FileText className="h-4 w-4 text-amber-600" />
                      ) : item.status === 'warning' ? (
                        <AlertCircle className="h-4 w-4 text-orange-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-600" />
                      )}
                    </div>
                    <span>{item.content}</span>
                  </div>
                ),
                secondaryContent: (
                  <div className="mt-1 flex items-center">
                    <Badge
                      variant="outline"
                      className={cn(getStatusColorClass(item.status as string))}
                    >
                      {item.status as string}
                    </Badge>
                    <span className="ml-2">{item.secondaryContent}</span>
                  </div>
                ),
              }))}
              size="md"
              variant="outline"
              onRowClick={handleRowClick}
            />
          </section>

          <section className="space-y-4">
            <h2 className="text-2xl font-bold">Custom Empty State</h2>
            <OneColTable
              items={[]}
              onRowClick={handleRowClick}
              emptyState={
                <div className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="text-muted-foreground mb-4 h-12 w-12" />
                  <h3 className="mb-1 text-lg font-semibold">No items found</h3>
                  <p className="text-muted-foreground mb-4">Try adjusting your search criteria</p>
                  <Button>Add an item</Button>
                </div>
              }
            />
          </section>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OneColTableDemo;
