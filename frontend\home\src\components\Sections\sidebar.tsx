'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Github } from 'lucide-react';
import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ThemeToggle } from '@/components/theme-toggle';
import { I18nButton } from '@/components/I18nButton';
import BuyMeACoffee from '@/components/buy-me-a-coffee';
import { siteConfig } from '@/config/site';
import { useTranslation } from '@/components/Providers/i18n-provider';

export function Sidebar() {
  const { t, i18n } = useTranslation();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Only show translations after i18n has loaded and hydration is complete
    const handleLoaded = () => {
      setIsLoaded(true);
    };

    if (i18n.isInitialized) {
      handleLoaded();
    } else {
      i18n.on('initialized', handleLoaded);
    }

    return () => {
      i18n.off('initialized', handleLoaded);
    };
  }, [i18n]);

  // Default navigation labels for SSR and initial client render
  const installationLabel = isLoaded ? t('nav.installation') : 'Installation';
  const usageLabel = isLoaded ? t('nav.usage') : 'Usage';
  const examplesLabel = isLoaded ? t('nav.examples') : 'Examples';
  const renderingExamplesLabel = isLoaded
    ? t('nav.rendering_examples') || 'Rendering Examples'
    : 'Rendering Examples';

  return (
    <aside className="flex-shrink-0 md:w-64">
      <div className="sticky top-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Link href="#" className="text-2xl font-bold">
              shadcn-timeline
            </Link>
            <div className="flex items-center gap-2">
              <I18nButton />
              <ThemeToggle />
              <Link
                href={siteConfig.links.github}
                className={cn(buttonVariants({ variant: 'ghost', size: 'icon' }))}
              >
                <Github className="size-5" />
              </Link>
            </div>
          </div>
          <nav className="flex flex-col space-y-2">
            <Link href="#installation" className="text-muted-foreground hover:text-foreground">
              {installationLabel}
            </Link>
            <Link href="#usage" className="text-muted-foreground hover:text-foreground">
              {usageLabel}
            </Link>
            <Link href="#examples" className="text-muted-foreground hover:text-foreground">
              {examplesLabel}
            </Link>
            <Link
              href="/rendering-examples"
              className="text-muted-foreground hover:text-foreground flex items-center gap-1"
            >
              <span className="text-sm">🔥</span>
              <span>{renderingExamplesLabel}</span>
            </Link>
          </nav>
          <BuyMeACoffee />
        </div>
      </div>
    </aside>
  );
}
