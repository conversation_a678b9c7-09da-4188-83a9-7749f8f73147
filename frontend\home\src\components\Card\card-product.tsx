// CardProduct.tsx
import React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus } from 'lucide-react';

export interface CardProductProps {
  /** Product unique identifier */
  id: string;
  /** Product image URL */
  imageUrl: string;
  /** Brand name */
  brand: string;
  /** Product name */
  name: string;
  /** Current price */
  price: number;
  /** Original price for showing discounts */
  originalPrice?: number;
  /** Discount percentage */
  discountPercentage?: number;
  /** Unit of measurement (e.g., 'บาท/ม้วน', 'บาท/ชิ้น') */
  unit?: string;
  /** Number of items sold */
  soldCount?: number;
  /** Extra CSS class names */
  className?: string;
  /** On add to cart callback */
  onAddToCart?: (id: string) => void;
  /** Is the product currently being added to cart */
  isAddingToCart?: boolean;
  /** Alt text for product image */
  imageAlt?: string;
}

export const CardProduct = ({
  id,
  imageUrl,
  brand,
  name,
  price,
  originalPrice,
  discountPercentage,
  unit = 'บาท/ชิ้น',
  soldCount,
  className,
  onAddToCart,
  isAddingToCart = false,
  imageAlt,
}: CardProductProps) => {
  const handleAddToCart = () => {
    if (onAddToCart && !isAddingToCart) {
      onAddToCart(id);
    }
  };

  return (
    <Card
      className={cn(
        'h-[392px] w-[224px] overflow-hidden rounded-[12px] border border-gray-200 bg-white',
        className,
      )}
      data-testid="card-product"
    >
      <div className="relative">
        <div className="relative mx-auto h-[144px] w-[144px]">
          <Image
            src={imageUrl}
            alt={imageAlt || name}
            fill
            className="object-contain"
            sizes="144px"
          />
        </div>

        {discountPercentage && discountPercentage > 0 && (
          
          <Badge
            className="absolute top-2 right-2 rounded-full bg-red-500 px-2 py-1 text-white hover:bg-red-600"
            aria-label={`Discount ${discountPercentage}%`}
          >
            -{discountPercentage}%
          </Badge>
        )}
      </div>

      <CardContent className="p-4">
        {brand && (
          <p className="mb-1 text-[14px] font-normal text-[#5D6A85]" data-testid="product-brand">
            {brand}
          </p>
        )}

        <h3
          className="mb-2 line-clamp-2 h-12 text-[18px] leading-[24px] font-medium text-gray-800"
          data-testid="product-name"
        >
          {name}
        </h3>

        <div className="mt-auto flex flex-col">
          <div className="flex items-baseline justify-between" data-testid="product-price">
            <span
              className={`text-[24px] font-bold ${
                discountPercentage && discountPercentage > 0 ? 'text-red-500' : 'text-[#121E72]'
              }`}
            >
              {price.toLocaleString()}
            </span>
            <span className="text-[15px] font-medium text-gray-500">{unit}</span>
          </div>

          {originalPrice && originalPrice > price && (
            <div
              className="text-sm text-gray-400 line-through"
              data-testid="product-original-price"
            >
              {originalPrice.toLocaleString()}
            </div>
          )}

          {soldCount !== undefined && (
            <div
              className="mt-1 text-right text-[14px] font-normal text-gray-500"
              data-testid="product-sold-count"
            >
              ขายแล้ว {soldCount.toLocaleString()} ชิ้น
            </div>
          )}
        </div>

        <div className="mt-2 flex justify-end">
          <Button
            size="icon"
            className="h-8 w-8 rounded-md bg-blue-700 hover:bg-blue-800"
            onClick={handleAddToCart}
            disabled={isAddingToCart}
            aria-label="Add to cart"
            data-testid="add-to-cart-button"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CardProduct;
