import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { InputNumber } from '../input-number';

const meta: Meta<typeof InputNumber> = {
  title: 'UI/Input/InputNumber',
  component: InputNumber,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg'],
      description: 'The size of the input',
    },
    min: {
      control: 'number',
      description: 'Minimum allowed value',
    },
    max: {
      control: 'number',
      description: 'Maximum allowed value',
    },
    step: {
      control: 'number',
      description: 'Step value for increment/decrement',
    },
    onChange: { action: 'changed' },
    onFocus: { action: 'focused' },
    onBlur: { action: 'blurred' },
  },
};

export default meta;
type Story = StoryObj<typeof InputNumber>;

// Basic example
export const Default: Story = {
  args: {
    id: 'default-number',
    placeholder: 'Enter a number',
    label: 'จำนวน',
    defaultValue: 3,
  },
};

// Sizes
export const Small: Story = {
  args: {
    id: 'small-number',
    size: 'sm',
    label: 'จำนวน',
    defaultValue: 3,
  },
};

export const Large: Story = {
  args: {
    id: 'large-number',
    size: 'lg',
    label: 'จำนวน',
    defaultValue: 3,
  },
};

// With min/max/step
export const WithConstraints: Story = {
  args: {
    id: 'constrained-number',
    label: 'จำนวน',
    helperText: 'Value between 0 and 100, step 5',
    min: 0,
    max: 100,
    step: 5,
    defaultValue: 15,
  },
};

// Controlled component example
export const Controlled: Story = {
  render: (args) => {
    const [value, setValue] = useState<number>(3);
    
    return (
      <div className="space-y-4">
        <InputNumber 
          {...args}
          value={value}
          onChange={setValue}
        />
        <div className="text-sm">
          Current value: {value}
        </div>
      </div>
    );
  },
  args: {
    id: 'controlled-number',
    label: 'จำนวน',
    helperText: 'This input is controlled by React state',
    min: 0,
    max: 100,
  },
};