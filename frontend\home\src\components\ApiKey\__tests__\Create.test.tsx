import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { I18nCreate as Create } from '../I18nCreate';
import { sampleProjects } from '../__fixtures__/ApiKey.fixtures';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { apiKeyTranslations } from '../ApiKey-i18n';

// Initialize i18next for testing
i18n.use(initReactI18next).init({
  resources: {
    en: {
      common: apiKeyTranslations.en,
    },
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
  ns: ['common'],
  defaultNS: 'common',
});

describe('Create API Key Component (Internationalized)', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  const defaultProps = {
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
    isSubmitting: false,
    availableProjects: sampleProjects,
  };

  beforeEach(() => {
    mockOnSubmit.mockClear();
    mockOnCancel.mockClear();
  });

  it('renders correctly with default props', () => {
    render(<Create {...defaultProps} />);

    // Check that the component title is rendered
    expect(screen.getByTestId('form-title')).toHaveTextContent('Create new secret key');

    // Check that the form fields are rendered
    expect(screen.getByText('Owned by')).toBeInTheDocument();
    expect(screen.getByText('You')).toBeInTheDocument();
    expect(screen.getByText('Service account')).toBeInTheDocument();
    expect(screen.getByLabelText('Name')).toBeInTheDocument();
    expect(screen.getByText('Project')).toBeInTheDocument();
    expect(screen.getByText('Permissions')).toBeInTheDocument();
    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByText('Restricted')).toBeInTheDocument();
    expect(screen.getByText('Read only')).toBeInTheDocument();

    // Check that the buttons are rendered
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Create secret key')).toBeInTheDocument();
  });

  it('switches between "You" and "Service account" owner types', async () => {
    render(<Create {...defaultProps} />);

    // Default owner type should be "You"
    const youButton = screen.getByText('You');
    expect(youButton).toHaveClass('bg-primary');

    // Description for user account should be visible
    expect(screen.getByText(/This API key is tied to your user/)).toBeInTheDocument();

    // Click on "Service account" button
    const serviceAccountButton = screen.getByText('Service account');
    await userEvent.click(serviceAccountButton);

    // Service account should now be selected
    expect(serviceAccountButton).toHaveClass('bg-primary');

    // Description should change
    expect(
      screen.getByText(/A new bot member \(service account\) will be added/),
    ).toBeInTheDocument();

    // Label should change
    expect(screen.getByText('Service Key Name')).toBeInTheDocument();
  });

  it('shows resource permissions when "Restricted" is selected', async () => {
    render(<Create {...defaultProps} />);

    // Initially, resource permissions should not be visible
    expect(screen.queryByText('Resources')).not.toBeInTheDocument();

    // Click on "Restricted" button
    const restrictedButton = screen.getByText('Restricted');
    await userEvent.click(restrictedButton);

    // Resource permissions should now be visible
    expect(screen.getByText('Resources')).toBeInTheDocument();
    expect(screen.getByText('Models')).toBeInTheDocument();

    // Permission options should be visible
    expect(screen.getAllByText('None').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Read').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Write').length).toBeGreaterThan(0);
  });

  it('calls onCancel when Cancel button is clicked', async () => {
    render(<Create {...defaultProps} />);

    // Click the Cancel button
    const cancelButton = screen.getByText('Cancel');
    await userEvent.click(cancelButton);

    // Check that onCancel was called
    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('disables buttons when submitting', () => {
    render(<Create {...defaultProps} isSubmitting={true} />);

    // Both buttons should be disabled
    expect(screen.getByText('Cancel')).toBeDisabled();
    expect(screen.getByText('Creating...')).toBeDisabled();
  });

  it('submits the form with correct data', async () => {
    render(<Create {...defaultProps} />);

    // Enter a name
    const nameInput = screen.getByLabelText('Name');
    await userEvent.type(nameInput, 'Test API Key');

    // Submit the form
    const submitButton = screen.getByText('Create secret key');
    await userEvent.click(submitButton);

    // Check that onSubmit was called with the correct data
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledTimes(1);
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test API Key',
          permissionType: 'All', // Default permission type
          ownerType: 'You', // Default owner type
          project: sampleProjects[0].value,
        }),
      );
    });
  });
});
