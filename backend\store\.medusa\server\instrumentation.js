"use strict";
// Uncomment this file to enable instrumentation and observability using OpenTelemetry
// Refer to the docs for installation instructions: https://docs.medusajs.com/learn/debugging-and-testing/instrumentation
Object.defineProperty(exports, "__esModule", { value: true });
// import { registerOtel } from "@medusajs/medusa"
// // If using an exporter other than Zipkin, require it here.
// import { ZipkinExporter } from "@opentelemetry/exporter-zipkin"
// // If using an exporter other than Zipkin, initialize it here.
// const exporter = new ZipkinExporter({
//   serviceName: 'my-medusa-project',
// })
// export function register() {
//   registerOtel({
//     serviceName: 'medusajs',
//     // pass exporter
//     exporter,
//     instrument: {
//       http: true,
//       workflows: true,
//       query: true
//     },
//   })
// }
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5zdHJ1bWVudGF0aW9uLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vaW5zdHJ1bWVudGF0aW9uLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQSxzRkFBc0Y7QUFDdEYseUhBQXlIOztBQUV6SCxrREFBa0Q7QUFDbEQsOERBQThEO0FBQzlELGtFQUFrRTtBQUVsRSxpRUFBaUU7QUFDakUsd0NBQXdDO0FBQ3hDLHNDQUFzQztBQUN0QyxLQUFLO0FBRUwsK0JBQStCO0FBQy9CLG1CQUFtQjtBQUNuQiwrQkFBK0I7QUFDL0IsdUJBQXVCO0FBQ3ZCLGdCQUFnQjtBQUNoQixvQkFBb0I7QUFDcEIsb0JBQW9CO0FBQ3BCLHlCQUF5QjtBQUN6QixvQkFBb0I7QUFDcEIsU0FBUztBQUNULE9BQU87QUFDUCxJQUFJIn0=