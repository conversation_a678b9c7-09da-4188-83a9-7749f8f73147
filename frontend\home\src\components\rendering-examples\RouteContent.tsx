'use client';

import React from 'react';

interface RouteContentProps {
  route: string;
  timestamp: string;
  renderType: string;
  children?: React.ReactNode;
}

export function RouteContent({ route, timestamp, renderType, children }: RouteContentProps) {
  return (
    <div className="bg-card space-y-6 rounded-lg p-4 shadow-sm">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {route.charAt(0).toUpperCase() + route.slice(1)}
        </h1>
        <p className="text-muted-foreground mt-2">
          This is the {route} page rendered using {renderType.toUpperCase()}
        </p>
        <div className="bg-muted mt-4 rounded p-2 text-sm">
          <p>Timestamp: {timestamp}</p>
          <p>Render type: {renderType.toUpperCase()}</p>
        </div>
      </div>
      {children && <div className="mt-6">{children}</div>}
    </div>
  );
}
