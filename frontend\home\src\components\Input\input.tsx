import React, { forwardRef, useState, ChangeEvent, ReactNode } from 'react';
import { Input as ShadcnInput } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Search } from 'lucide-react';
import { cn } from '@/lib/utils';

export type InputVariant = 
  | 'default' 
  | 'search' 
  | 'withButton';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  helperText?: string;
  error?: boolean;
  errorMessage?: string;
  onValueChange?: (value: string) => void;
  // Props for custom variants
  variant?: InputVariant;
  buttonText?: string;
  buttonIcon?: ReactNode;
  buttonClassName?: string;
  onButtonClick?: () => void;
  inputWrapperClassName?: string;
  size?: 'default' | 'sm' | 'lg';
  // Custom props for search variant
  searchIconSize?: number;
  searchVariantClassName?: string;
  searchButtonClassName?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    label, 
    helperText, 
    error = false, 
    errorMessage, 
    id, 
    onValueChange, 
    onChange,
    // Props with defaults
    variant = 'default',
    buttonText,
    buttonIcon,
    buttonClassName,
    onButtonClick,
    inputWrapperClassName,
    size = 'default',
    // Custom search props
    searchIconSize = 20,
    searchVariantClassName,
    searchButtonClassName,
    ...props 
  }, ref) => {
    const [value, setValue] = useState<string>(props.defaultValue?.toString() || props.value?.toString() || '');
    
    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setValue(newValue);
      
      // Call both handlers if provided
      if (onValueChange) {
        onValueChange(newValue);
      }
      
      if (onChange) {
        onChange(e);
      }
    };

    // Determine size class
    const sizeClass = {
      sm: "h-8 text-sm",
      default: "h-10",
      lg: "h-12 text-lg"
    }[size];
    
    // Render input variants
    const renderInput = () => {
      // Base input component
      const inputElement = (
        <ShadcnInput
          id={id}
          ref={ref}
          value={props.value !== undefined ? props.value : value}
          onChange={handleChange}
          className={cn(
            error && "border-destructive focus-visible:ring-destructive",
            variant === 'search' && "pr-10",
            variant === 'withButton' && "rounded-r-none rounded-lg",
            sizeClass,
            className
          )}
          aria-invalid={error}
          aria-describedby={error ? `${id}-error` : helperText ? `${id}-helper` : undefined}
          {...props}
        />
      );

      switch (variant) {
        case 'search':
          return (
            <div className={cn(
              "flex overflow-hidden rounded-lg border border-[#1E3A8A] ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
              error && "border-destructive focus-within:ring-destructive",
              searchVariantClassName,
              inputWrapperClassName
            )}>
              <ShadcnInput
                id={id}
                ref={ref}
                value={props.value !== undefined ? props.value : value}
                onChange={handleChange}
                className={cn(
                  "border-0 focus-visible:ring-0 focus-visible:ring-offset-0",
                  error && "border-destructive focus-visible:ring-destructive",
                  sizeClass,
                  className
                )}
                aria-invalid={error}
                aria-describedby={error ? `${id}-error` : helperText ? `${id}-helper` : undefined}
                {...props}
              />
              <div 
                className={cn(
                  "flex items-center justify-center px-4 bg-[#1E3A8A] text-white cursor-pointer",
                  searchButtonClassName
                )}
                onClick={onButtonClick}
                data-testid="search-button"
              >
                <Search className="h-5 w-5" size={searchIconSize} />
              </div>
            </div>
          );
          
        case 'withButton':
          return (
            <div className={cn("relative", inputWrapperClassName)}>
              {inputElement}
              <Button
                type="button"
                className={cn(
                  "absolute top-0 right-0 px-3 rounded-lg bg-[#FFAA00] hover:bg-[#E69700] text-black cursor-pointer",
                  sizeClass,
                  buttonClassName
                )}
                onClick={onButtonClick}
              >
                {buttonIcon}
                {buttonText || 'ใช้'}
              </Button>
            </div>
          );
          
        default:
          return (
            <div className={inputWrapperClassName}>
              {inputElement}
            </div>
          );
      }
    };
    
    return (
      <div className="w-full space-y-2" data-testid="input-container">
        {label && (
          <Label 
            htmlFor={id} 
            className={cn(
              error && "text-destructive"
            )}
          >
            {label}
          </Label>
        )}
        
        {renderInput()}
        
        {helperText && !error && (
          <p 
            id={`${id}-helper`}
            className="text-sm text-muted-foreground"
          >
            {helperText}
          </p>
        )}
        
        {error && errorMessage && (
          <p 
            id={`${id}-error`}
            className="text-sm font-medium text-destructive"
          >
            {errorMessage}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input };