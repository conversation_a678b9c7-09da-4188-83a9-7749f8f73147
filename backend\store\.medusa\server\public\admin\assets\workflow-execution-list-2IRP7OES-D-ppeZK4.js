import{u as x,g as w,a as g}from"./chunk-RPAL6FHW-ExF45-x3.js";import{S as h}from"./chunk-ADOCJB6L-fVr5Yqi0.js";import{u as b,_ as E}from"./chunk-X3LH6P65-BtKDvzuz.js";import{a as k,j as t,b as u,H as j,T as v,r as S,Y as T,k as C}from"./index-Bwql5Dzz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as P}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as _}from"./chunk-C76H5USB-ByRPKhW7.js";import{C as y}from"./container-Dqi2woPF.js";import{c as z}from"./index-BxZ1678G.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";var c=z(),L=()=>{const{t:e}=u();return S.useMemo(()=>[c.accessor("transaction_id",{header:e("workflowExecutions.transactionIdLabel"),cell:({getValue:s})=>t.jsx(T,{size:"2xsmall",children:s()})}),c.accessor("state",{header:e("fields.state"),cell:({getValue:s})=>{const o=s(),r=w(o),a=g(e,o);return t.jsx(h,{color:r,children:t.jsx("span",{className:"capitalize",children:a})})}}),c.accessor("execution",{header:e("workflowExecutions.progressLabel"),cell:({getValue:s})=>{var i;const o=(i=s())==null?void 0:i.steps;if(!o)return"0 of 0 steps";const r=Object.values(o).filter(n=>n.id!==R),a=r.filter(n=>n.invoke.state==="done");return e("workflowExecutions.stepsCompletedLabel",{completed:a.length,count:r.length})}})],[e])},R="_root",W=({pageSize:e=20,prefix:s})=>{const o=_(["q","offset"],s),{offset:r,...a}=o;return{searchParams:{limit:e,offset:r?parseInt(r):0,...a},raw:o}},m=20,D=()=>{const{t:e}=u(),{searchParams:s,raw:o}=W({pageSize:m}),{workflow_executions:r,count:a,isLoading:i,isError:n,error:f}=x({...s},{placeholderData:C}),p=L(),{table:d}=b({data:r||[],columns:p,count:a,pageSize:m,enablePagination:!0,getRowId:l=>l.id});if(n)throw f;return t.jsxs(y,{className:"divide-y p-0",children:[t.jsx("div",{className:"flex items-center justify-between px-6 py-4",children:t.jsxs("div",{children:[t.jsx(j,{children:e("workflowExecutions.domain")}),t.jsx(v,{className:"text-ui-fg-subtle",size:"small",children:e("workflowExecutions.subtitle")})]})}),t.jsx(E,{table:d,columns:p,count:a,isLoading:i,pageSize:m,navigateTo:l=>`${l.id}`,search:!0,pagination:!0,queryObject:o,noRecords:{message:e("workflowExecutions.list.noRecordsMessage")}})]})},ce=()=>{const{getWidgets:e}=k();return t.jsx(P,{widgets:{after:e("workflow.list.after"),before:e("workflow.list.before")},hasOutlet:!1,children:t.jsx(D,{})})};export{ce as Component};
