import{r as l,j as e,T as d,x as n,m as o}from"./index-Bwql5Dzz.js";import{C as c}from"./index.esm-3G2Z4eQ8.js";var u=l.forwardRef(({min:t=0,max:r=100,step:a=1e-4,...s},i)=>e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r",children:e.jsx(d,{className:"text-ui-fg-muted",size:"small",leading:"compact",weight:"plus",children:"%"})}),e.jsx(n,{ref:i,type:"number",min:t,max:r,step:a,...s,className:"pl-10"})]}));u.displayName="PercentageInput";var m=l.forwardRef(({min:t=0,decimalScale:r=2,className:a,...s},i)=>e.jsxs("div",{className:"relative",children:[e.jsx(c,{ref:i,min:t,autoComplete:"off",decimalScale:r,decimalsLimit:r,...s,className:o("caret-ui-fg-base bg-ui-bg-field shadow-buttons-neutral transition-fg txt-compact-small flex w-full select-none appearance-none items-center justify-between rounded-md px-2 py-1.5 pr-10 text-right outline-none","placeholder:text-ui-fg-muted text-ui-fg-base","hover:bg-ui-bg-field-hover","focus-visible:shadow-borders-interactive-with-active data-[state=open]:!shadow-borders-interactive-with-active","aria-[invalid=true]:border-ui-border-error aria-[invalid=true]:shadow-borders-error","invalid::border-ui-border-error invalid:shadow-borders-error","disabled:!bg-ui-bg-disabled disabled:!text-ui-fg-disabled",a)}),e.jsx("div",{className:"absolute inset-y-0 right-0 z-10 flex w-8 items-center justify-center border-l",children:e.jsx(d,{className:"text-ui-fg-muted",size:"small",leading:"compact",weight:"plus",children:"%"})})]}));m.displayName="PercentageInput";export{u as D,m as P};
