import React from 'react';
import { render } from '@testing-library/react';
import OneColTable from '../one-col-table';
import { mockItems, sizedItems } from '../__fixtures__/one-col-table.fixtures';

describe('OneColTable Snapshots', () => {
  it('matches snapshot with default props', () => {
    const { container } = render(<OneColTable items={mockItems.slice(0, 3)} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with loading state', () => {
    const { container } = render(<OneColTable items={mockItems.slice(0, 3)} loading />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with error state', () => {
    const { container } = render(
      <OneColTable items={mockItems.slice(0, 3)} error="An error occurred while loading data" />,
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with custom header', () => {
    const { container } = render(
      <OneColTable items={mockItems.slice(0, 3)} header={<div>Custom Header</div>} />,
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with custom empty state', () => {
    const { container } = render(
      <OneColTable items={[]} emptyState={<div>No items available at this time</div>} />,
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with size variants', () => {
    const { container: smallContainer } = render(
      <OneColTable items={sizedItems.small} size="sm" />,
    );
    expect(smallContainer).toMatchSnapshot('small-size');

    const { container: mediumContainer } = render(
      <OneColTable items={sizedItems.medium} size="md" />,
    );
    expect(mediumContainer).toMatchSnapshot('medium-size');

    const { container: largeContainer } = render(
      <OneColTable items={sizedItems.large} size="lg" />,
    );
    expect(largeContainer).toMatchSnapshot('large-size');
  });

  it('matches snapshot with style variants', () => {
    const { container: primaryContainer } = render(
      <OneColTable items={mockItems.slice(0, 3)} variant="primary" />,
    );
    expect(primaryContainer).toMatchSnapshot('primary-variant');

    const { container: secondaryContainer } = render(
      <OneColTable items={mockItems.slice(0, 3)} variant="secondary" />,
    );
    expect(secondaryContainer).toMatchSnapshot('secondary-variant');

    const { container: outlineContainer } = render(
      <OneColTable items={mockItems.slice(0, 3)} variant="outline" />,
    );
    expect(outlineContainer).toMatchSnapshot('outline-variant');
  });

  it('matches snapshot with disabled state', () => {
    const { container } = render(<OneColTable items={mockItems.slice(0, 3)} disabled />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with load more button', () => {
    const { container } = render(
      <OneColTable items={mockItems.slice(0, 5)} showLoadMore hasMore onLoadMore={() => {}} />,
    );
    expect(container).toMatchSnapshot();
  });
});
