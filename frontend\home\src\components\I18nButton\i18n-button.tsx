'use client';

import * as React from 'react';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useFullLanguageChange } from '@/hooks/useLanguageChange';
import { cva } from 'class-variance-authority';
import { Globe } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { languageCodes, supportedLngs } from '@/lib';
import { useClientOnly } from '@/hooks/useClientOnly';

import type { I18nButtonProps, LanguageItem } from './types';

// Define skeleton component inline to avoid import issues
interface I18nButtonSkeletonProps {
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  i18nNamespace?: string;
  i18nPrefix?: string;
}

export const I18nButtonSkeleton = ({
  size = 'icon',
  className,
  i18nNamespace,
  i18nPrefix = 'language.skeleton',
}: I18nButtonSkeletonProps) => {
  const { t } = useTranslation(i18nNamespace);
  
  // Get correct size class based on size prop
  const sizeClass = size === 'default' ? 'h-8 w-8' :
                    size === 'sm' ? 'h-7 w-7' :
                    size === 'lg' ? 'h-9 w-9' :
                    'h-8 w-8'; // icon size
  
  return (
    <div 
      className={cn('rounded-full overflow-hidden', className)}
      aria-label="Loading language selector"
      role="status"
    >
      <Skeleton className={cn('aspect-square', sizeClass)} />
    </div>
  );
};

// CVA variant definition with semantic naming
export const i18nButtonVariants = cva('i18n-button', {
  variants: {
    size: {
      default: '',
      sm: '',
      lg: '',
      icon: '',
    },
    variant: {
      default: '',
      destructive: '',
      outline: '',
      secondary: '',
      ghost: '',
      link: '',
    },
    state: {
      default: '',
      loading: 'opacity-70 cursor-not-allowed',
      disabled: 'opacity-50 cursor-not-allowed',
    }
  },
  defaultVariants: {
    size: 'icon',
    variant: 'ghost',
    state: 'default',
  },
});

/**
 * I18nButton - A language switcher button with dropdown menu
 * 
 * Allows users to change the application language with a dropdown menu.
 * Supports RTL languages and shows the currently selected language.
 * Works consistently in both Storybook and application contexts.
 * 
 * @example
 * // Basic usage
 * <I18nButton />
 * 
 * // With custom size and variant
 * <I18nButton size="default" variant="outline" />
 * 
 * // With custom icon
 * <I18nButton icon={<CustomIcon />} />
 * 
 * // With custom languages list
 * <I18nButton 
 *   languages={[
 *     { code: 'en', label: 'English' },
 *     { code: 'fr', label: 'French' }
 *   ]} 
 * />
 */
const I18nButton = React.forwardRef<HTMLDivElement, I18nButtonProps>(
  ({
    // UI variants
    variant = 'ghost',
    size = 'icon',
    className,
    
    // State props
    disabled = false,
    loading = false,
    
    // Content props
    icon,
    
    // Dropdown props
    open,
    onOpenChange,
    align = 'end',
    
    // I18n props
    i18nNamespace,
    i18nPrefix = 'language',
    
    // Language props
    languages,
    currentLanguage: propCurrentLang,
    onLanguageChange: propOnLanguageChange,
    showNativeNames = false,
    showLanguageCode = false,
    
    // Keep for backwards compatibility but ignore it
    storybookMode,
    
    // Rest props
    ...restProps
  }, ref) => {
    // Use client-only rendering to avoid hydration issues
    const isMounted = useClientOnly();
    
    // Component state determination (for variants)
    const componentState = useMemo(() => {
      if (loading) return 'loading' as const;
      if (disabled) return 'disabled' as const;
      return 'default' as const;
    }, [loading, disabled]);
    
    // Loading state rendering
    if (loading) {
      return <I18nButtonSkeleton size={size} />;
    }
    
    // Static button (safe for server rendering)
    const staticButton = (
      <div ref={ref} className={cn(i18nButtonVariants({ variant, size, state: componentState }), className)} {...restProps}>
        <Button 
          variant={variant} 
          size={size} 
          disabled={disabled}
          aria-label="Switch Language"
        >
          {icon || <Globe className="h-[1.2rem] w-[1.2rem]" />}
          {showLanguageCode && propCurrentLang && <span className="ml-1">{propCurrentLang}</span>}
        </Button>
      </div>
    );
    
    // If not client-mounted yet, render static button
    if (!isMounted) {
      return staticButton;
    }
    
    // Client-side only component with interactivity
    return <ClientI18nButton 
      ref={ref}
      variant={variant}
      size={size}
      className={className}
      disabled={disabled}
      icon={icon}
      open={open}
      onOpenChange={onOpenChange}
      align={align}
      i18nNamespace={i18nNamespace}
      i18nPrefix={i18nPrefix}
      languages={languages}
      currentLanguage={propCurrentLang}
      onLanguageChange={propOnLanguageChange}
      showNativeNames={showNativeNames}
      showLanguageCode={showLanguageCode}
      componentState={componentState}
      {...restProps}
    />;
  }
);

// Client-side implementation with full i18n functionality
const ClientI18nButton = React.forwardRef<HTMLDivElement, I18nButtonProps & { componentState: 'default' | 'loading' | 'disabled' }>(
  ({
    // UI variants
    variant = 'ghost',
    size = 'icon',
    className,
    
    // State props
    disabled = false,
    
    // Content props
    icon,
    
    // Dropdown props
    open,
    onOpenChange,
    align = 'end',
    
    // I18n props
    i18nNamespace,
    i18nPrefix = 'language',
    
    // Language props
    languages,
    currentLanguage: propCurrentLang,
    onLanguageChange: propOnLanguageChange,
    showNativeNames = false,
    showLanguageCode = false,
    
    // State
    componentState,
    
    // Rest props
    ...restProps
  }, ref) => {
    // Translation hooks (client-side only)
    const { t, i18n } = useTranslation(i18nNamespace);
    
    // Use our unified language change hook
    const { currentLanguage, changeLanguage } = useFullLanguageChange();
    
    // State
    const [isOpen, setIsOpen] = useState(open ?? false);
    const [isLoaded, setIsLoaded] = useState(i18n.isInitialized);
    
    // Use the current language from props, hook, or i18n instance
    const displayLang = propCurrentLang || currentLanguage || i18n.language || 'en';
    
    // Initialize the component state after i18n is loaded
    useEffect(() => {
      if (i18n.isInitialized) {
        setIsLoaded(true);
      } else {
        const handleLoaded = () => {
          setIsLoaded(true);
        };

        i18n.on('initialized', handleLoaded);
        
        return () => {
          i18n.off('initialized', handleLoaded);
        };
      }
    }, [i18n]);
    
    // Handle dropdown state changes
    useEffect(() => {
      if (open !== undefined) {
        setIsOpen(open);
      }
    }, [open]);
    
    // Event handlers
    const handleOpenChange = useCallback((newOpen: boolean) => {
      setIsOpen(newOpen);
      onOpenChange?.(newOpen);
    }, [onOpenChange]);
    
    const handleLanguageChange = useCallback((lng: string) => {
      if (propOnLanguageChange) {
        propOnLanguageChange(lng);
      } else {
        changeLanguage(lng);
      }
      setIsOpen(false);
    }, [propOnLanguageChange, changeLanguage]);
    
    // Prepare languages data
    const languageItems = useMemo<LanguageItem[]>(() => {
      if (languages) return languages;
      
      return languageCodes.map(code => {
        // Safely check if the language code exists in supportedLngs
        const langConfig = typeof supportedLngs === 'object' && 
                          supportedLngs !== null && 
                          code in supportedLngs ? 
                          supportedLngs[code as keyof typeof supportedLngs] : 
                          { rtl: false };
        
        return {
          code,
          rtl: langConfig.rtl,
        };
      });
    }, [languages]);
    
    // Don't render the menu until i18n is loaded
    if (!isLoaded) {
      return (
        <div ref={ref} className={cn(i18nButtonVariants({ variant, size, state: componentState }), className)} {...restProps}>
          <Button 
            variant={variant} 
            size={size} 
            disabled={disabled}
            aria-label="Switch Language"
          >
            {icon || <Globe className="h-[1.2rem] w-[1.2rem]" />}
            {showLanguageCode && <span className="ml-1">{displayLang}</span>}
          </Button>
        </div>
      );
    }
    
    // Language labels with translations
    const getLanguageLabel = (code: string): string => {
      const key = `${i18nPrefix}.${code}`;
      // If showNativeNames is true, use the translation in the target language
      const label = showNativeNames 
        ? i18n.getFixedT(code)(key)
        : t(key, code);
      return label;
    };
    
    return (
      <div ref={ref} className={cn(i18nButtonVariants({ variant, size, state: componentState }), className)} {...restProps}>
        <DropdownMenu open={isOpen} onOpenChange={handleOpenChange}>
          <DropdownMenuTrigger asChild>
            <Button
              variant={variant}
              size={size}
              disabled={disabled}
              aria-label="Switch Language"
            >
              {icon || <Globe className="h-[1.2rem] w-[1.2rem]" />}
              {showLanguageCode && <span className="ml-1">{displayLang}</span>}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align={align}>
            {languageItems.map((item) => (
              <DropdownMenuItem
                key={item.code}
                onClick={() => handleLanguageChange(item.code)}
                className={cn(
                  displayLang === item.code ? 'bg-accent' : '',
                  item.rtl ? 'text-right' : ''
                )}
                dir={item.rtl ? 'rtl' : 'ltr'}
              >
                {item.icon && <span className="mr-2">{item.icon}</span>}
                {item.label || getLanguageLabel(item.code)}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        
        {/* Current language indicator for debugging */}
        <div className="sr-only text-xs text-muted-foreground mt-2">
          {t('language.current')}: {displayLang}
        </div>
      </div>
    );
  }
);

I18nButton.displayName = 'I18nButton';
ClientI18nButton.displayName = 'ClientI18nButton';

export { I18nButton };
export default I18nButton; 