// components/Pagination/pagination.tsx
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface PaginationProps {
  /**
   * Total number of pages
   */
  totalPages: number;
  /**
   * Current active page (1-based indexing)
   */
  currentPage: number;
  /**
   * Called when page is changed
   */
  onPageChange: (page: number) => void;
  /**
   * Number of page buttons to show (not including prev/next buttons)
   * @default 5
   */
  siblingCount?: number;
  /**
   * Custom CSS class for the pagination container
   */
  className?: string;
  /**
   * Variant of pagination (numbered or simple)
   * @default "numbered"
   */
  variant?: "numbered" | "simple";
  /**
   * Show ellipsis (...) when there are many pages
   * @default true
   */
  showEllipsis?: boolean;
  /**
   * Text for page indicator (only used in 'simple' variant)
   * @default "หน้า"
   */
  pageText?: string;
  /**
   * Disable the pagination
   * @default false
   */
  disabled?: boolean;
}

export function Pagination({
  totalPages,
  currentPage,
  onPageChange,
  siblingCount = 5,
  className = "",
  variant = "numbered",
  showEllipsis = true,
  pageText = "หน้า",
  disabled = false,
}: PaginationProps) {
  // Ensure currentPage is within valid bounds
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));

  // Calculate the range of page numbers to display (for numbered variant)
  const getPageRange = () => {
    // For simple variant, we don't need page range
    if (variant === "simple") return [];

    // For small number of pages, show all
    if (totalPages <= siblingCount) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    
    // Calculate center pages
    const halfSiblingCount = Math.floor(siblingCount / 2);
    
    let startPage = Math.max(1, validCurrentPage - halfSiblingCount);
    let endPage = Math.min(totalPages, startPage + siblingCount - 1);
    
    // Adjust if we hit the upper bound
    if (endPage - startPage + 1 < siblingCount && startPage > 1) {
      startPage = Math.max(1, endPage - siblingCount + 1);
    }
    
    const pageRange: (number | string)[] = [];

    // Add first page
    pageRange.push(1);
    
    // Add ellipsis after first page if needed
    if (showEllipsis && startPage > 2) {
      pageRange.push("...");
    }
    
    // Add middle pages (skip first and last page as they're always shown)
    for (let i = startPage; i <= endPage; i++) {
      if (i !== 1 && i !== totalPages) {
        pageRange.push(i);
      }
    }
    
    // Add ellipsis before last page if needed
    if (showEllipsis && endPage < totalPages - 1) {
      pageRange.push("...");
    }
    
    // Add last page if not already included
    if (totalPages > 1) {
      pageRange.push(totalPages);
    }
    
    return pageRange;
  };

  const handlePageChange = (page: number) => {
    if (page !== validCurrentPage && page >= 1 && page <= totalPages && !disabled) {
      onPageChange(page);
    }
  };

  // If there's only 1 page or less, don't render pagination
  if (totalPages <= 1) {
    return null;
  }

  const pageRange = getPageRange();

  // Simple variant (just shows "Page X / Total" with prev/next buttons)
  if (variant === "simple") {
    return (
      <div className={cn("flex items-center", className)}>
        <div className="mr-4 text-sm">
          {pageText} {validCurrentPage} / {totalPages}
        </div>
        <div className="flex">
          <Button
            variant="outline"
            size="icon"
            className="rounded-r-none"
            onClick={() => handlePageChange(validCurrentPage - 1)}
            disabled={validCurrentPage === 1 || disabled}
            aria-label="Previous page"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="rounded-l-none border-l-0"
            onClick={() => handlePageChange(validCurrentPage + 1)}
            disabled={validCurrentPage === totalPages || disabled}
            aria-label="Next page"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }

  // Numbered variant (shows page numbers with prev/next)
  return (
    <nav
      className={cn("flex items-center justify-center space-x-1", className)}
      aria-label="Pagination"
    >
      {/* Previous page button */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => handlePageChange(validCurrentPage - 1)}
        disabled={validCurrentPage === 1 || disabled}
        aria-label="Previous page"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>

      {/* Page numbers */}
      {pageRange.map((page, index) => {
        // If it's an ellipsis
        if (page === "...") {
          return (
            <span 
              key={`ellipsis-${index}`} 
              className="px-2 text-gray-400"
            >
              ...
            </span>
          );
        }

        // Otherwise it's a number
        const pageNum = page as number;
        return (
          <Button
            key={pageNum}
            variant={pageNum === validCurrentPage ? "default" : "outline"}
            size="icon"
            onClick={() => handlePageChange(pageNum)}
            disabled={disabled}
            aria-label={`Page ${pageNum}`}
            aria-current={pageNum === validCurrentPage ? "page" : undefined}
            className={cn(
              pageNum === validCurrentPage 
                ? "bg-indigo-900 hover:bg-indigo-800 text-white" 
                : "text-gray-500"
            )}
          >
            {pageNum}
          </Button>
        );
      })}

      {/* Next page button */}
      <Button
        variant="outline"
        size="icon"
        onClick={() => handlePageChange(validCurrentPage + 1)}
        disabled={validCurrentPage === totalPages || disabled}
        aria-label="Next page"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </nav>
  );
}