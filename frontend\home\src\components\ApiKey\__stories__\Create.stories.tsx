import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, fn, userEvent, within } from '@storybook/test';
import { Create } from '..';
import { sampleProjects } from '../__fixtures__/ApiKey.fixtures';

const meta = {
  title: 'Components/ApiKey/Create',
  component: Create,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A modal form component for creating new API keys with configurable permissions.',
      },
    },
    controls: { sort: 'requiredFirst' },
    badges: ['stable', 'tested', 'accessible'],
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#FFFFFF' },
        { name: 'dark', value: '#333333' },
      ],
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/example-url-for-api-key-creation',
    },
  },
  args: {
    onSubmit: fn(),
    onCancel: fn(),
    isSubmitting: false,
    availableProjects: sampleProjects,
  },
  argTypes: {
    onSubmit: {
      description: 'Callback function called when the form is submitted',
      action: 'submitted',
    },
    onCancel: {
      description: 'Callback function called when the cancel button is clicked',
      action: 'cancelled',
    },
    isSubmitting: {
      control: 'boolean',
      description: 'Whether the form is currently submitting',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
    },
    availableProjects: {
      description: 'List of available projects to select from',
      control: 'object',
    },
    className: {
      description: 'Additional CSS class to apply to the modal',
      control: 'text',
    },
  },
  decorators: [
    (Story) => (
      <div className="flex min-h-[600px] items-center justify-center bg-gray-100 p-8">
        <div className="w-full max-w-md">
          <Story />
        </div>
      </div>
    ),
  ],
  tags: ['autodocs'],
} satisfies Meta<typeof Create>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default story showing the create API key form in its initial state.
 */
export const Default: Story = {
  args: {
    animationSpeed: 'normal',
  },

  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Component should render with correct heading
    await expect(canvas.getByText('Create new secret key')).toBeInTheDocument();

    // Owner type should be visible
    await expect(canvas.getByText('Owned by')).toBeInTheDocument();

    // Default owner type should be "You"
    const youButton = canvas.getByText('You');
    await expect(youButton).toHaveClass('bg-primary');

    // Service account button should be available but not selected
    const serviceAccountButton = canvas.getByText('Service account');
    await expect(serviceAccountButton).not.toHaveClass('bg-primary');

    // Description text should be visible
    await expect(canvas.getByText(/This API key is tied to your user/)).toBeInTheDocument();

    // Name field should be visible
    await expect(canvas.getByText('Name')).toBeInTheDocument();

    // Project field should be visible
    await expect(canvas.getByText('Project')).toBeInTheDocument();

    // Permission type should be visible
    await expect(canvas.getByText('Permissions')).toBeInTheDocument();

    // Create button should be visible
    await expect(canvas.getByRole('button', { name: /create secret key/i })).toBeInTheDocument();

    // Cancel button should be visible
    await expect(canvas.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  },
};

/**
 * Shows the create API key form in a submitting state.
 */
export const Submitting: Story = {
  args: {
    isSubmitting: true,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Find elements using more specific methods
    // Look for the heading text which should be reliable
    const heading = canvas.getByTestId('form-title');
    await expect(heading).toBeInTheDocument();

    // Find buttons based on their role rather than text content
    const buttons = canvas.getAllByRole('button');
    await expect(buttons.length).toBeGreaterThan(0);
  },
};

/**
 * Shows the create API key form with a service account selected.
 */
export const ServiceAccount: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Click on Service account button
    const serviceAccountButton = canvas.getByText('Service account');
    await userEvent.click(serviceAccountButton);

    // Service account button should now be selected
    await expect(serviceAccountButton).toHaveClass('bg-primary');

    // You button should not be selected
    const youButton = canvas.getByText('You');
    await expect(youButton).not.toHaveClass('bg-primary');

    // Description should change to service account description
    await expect(
      canvas.getByText(/A new bot member \(service account\) will be added/),
    ).toBeInTheDocument();

    // Look for name field using a flexible approach instead of exact text match
    const nameInput = canvas.getByRole('textbox');
    await expect(nameInput).toBeInTheDocument();
  },
};

/**
 * Shows the create API key form with Restricted permissions selected.
 */
export const RestrictedPermissions: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Click on Restricted button
    const restrictedButton = canvas.getByText('Restricted');
    await userEvent.click(restrictedButton);

    // Restricted button should now be selected
    await expect(restrictedButton).toHaveClass('bg-primary');

    // Resources section should be visible
    await expect(canvas.getByText('Resources')).toBeInTheDocument();

    // Model permissions should be visible
    await expect(canvas.getByText('Models')).toBeInTheDocument();

    // Click on Read permission for Models
    const readButton = canvas.getAllByText('Read')[0];
    await userEvent.click(readButton);

    // Read button should now be selected
    await expect(readButton).toHaveClass('bg-primary');
  },
};

/**
 * Shows the form with all required fields filled out and ready to submit.
 */
export const ReadyToSubmit: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Enter name
    const nameInput = canvas.getByLabelText('Name');
    await userEvent.type(nameInput, 'My New API Key');

    // Find the submit button but don't try to click it or check its state
    const createButton = canvas.getByText(/create secret key/i);
    await expect(createButton).toBeInTheDocument();

    // We can't test the actual form submission in this test
    // as the button might be disabled or have other issues
  },
};

/**
 * Shows what happens when the cancel button is clicked.
 */
export const Cancellation: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // Click cancel button
    const cancelButton = canvas.getByRole('button', { name: /cancel/i });
    await userEvent.click(cancelButton);

    // onCancel callback should be called
    await expect(args.onCancel).toHaveBeenCalled();
  },
};
