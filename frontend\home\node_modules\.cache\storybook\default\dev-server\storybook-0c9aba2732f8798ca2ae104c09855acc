{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "-H0xD5hOyuzOuSrwlBgwR", "sessionId": "kGBc4HmWEpddn6keQ6uKQ", "payload": {"eventType": "dev"}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.11.0", "cliVersion": "8.6.12"}}, "timestamp": 1748155424349}, "version-update": {"body": {"eventType": "version-update", "eventId": "B6VNkzzeZSQrj8q_OGb8k", "sessionId": "aQIGacFN5wV4jERuGBMnR", "metadata": {"generatedAt": 1748141647014, "hasCustomBabel": false, "hasCustomWebpack": true, "hasStaticDirs": true, "hasStorybookEslint": true, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.3.1"}, "testPackages": {"@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/jest": "29.5.14", "@types/jest-axe": "3.5.9", "babel-jest": "29.7.0", "jest": "29.7.0", "jest-axe": "10.0.0", "jest-environment-jsdom": "29.7.0", "msw": "2.7.5", "msw-storybook-addon": "2.0.4"}, "hasRouterPackage": true, "preview": {"usesGlobals": true}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 3, "applicationFileCount": 43, "storybookVersion": "8.6.12", "storybookVersionSpecifier": "^8.6.7", "language": "typescript", "storybookPackages": {"storybook-addon-react-docgen": {"version": "1.2.44"}, "@chromatic-com/storybook": {"version": "3.2.6"}, "@storybook/blocks": {"version": "8.6.12"}, "@storybook/experimental-addon-test": {"version": "8.6.12"}, "@storybook/nextjs": {"version": "8.6.12"}, "@storybook/react": {"version": "8.6.12"}, "@storybook/test": {"version": "8.6.12"}, "@storybook/test-runner": {"version": "0.22.0"}, "@whitespace/storybook-addon-html": {"version": "6.1.1"}, "eslint-plugin-storybook": {"version": "0.11.6"}, "storybook": {"version": "8.6.12"}, "storybook-addon-pseudo-states": {"version": "4.0.3"}, "storybook-addon-rtl": {"version": "1.1.0"}, "storybook-css-modules": {"version": "1.0.8"}}, "addons": {"@storybook/addon-links": {"version": "8.6.12"}, "@storybook/addon-essentials": {"version": "8.6.12"}, "@storybook/addon-onboarding": {"version": "8.6.12"}, "@storybook/addon-themes": {"version": "8.6.12"}, "@storybook/addon-a11y": {"version": "8.6.12"}, "@storybook/addon-coverage": {"version": "1.0.5"}, "@storybook/addon-viewport": {"version": "8.6.12"}, "@storybook/addon-backgrounds": {"version": "8.6.12"}, "@storybook/addon-console": {"version": "3.0.0"}, "@storybook/addon-controls": {"version": "8.6.12"}, "@storybook/addon-actions": {"version": "8.6.12"}, "@storybook/addon-interactions": {"version": "8.6.12"}, "@storybook/addon-storysource": {"version": "8.6.12"}, "@storybook/addon-measure": {"version": "8.6.12"}, "@storybook/addon-outline": {"version": "8.6.12"}, "msw-storybook-addon": {"version": "2.0.4"}, "storybook-react-i18next": {"version": "3.3.1"}, "@storybook/addon-styling-webpack": {"version": "1.0.1"}, "chromatic": {"version": "11.28.2", "versionSpecifier": "^11.27.0"}}}, "payload": {}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.11.0", "cliVersion": "8.6.12", "anonymousId": "2083e11f3b0d8f3e907f1be42620572223301ccbb83fa255eece6a7150e65f70"}}, "timestamp": 1748141650685}, "dev": {"body": {"eventType": "dev", "eventId": "EWynUgLjsz_9Hs3oafnVi", "sessionId": "kGBc4HmWEpddn6keQ6uKQ", "metadata": {"generatedAt": 1748155457227, "hasCustomBabel": false, "hasCustomWebpack": true, "hasStaticDirs": true, "hasStorybookEslint": true, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.3.1"}, "testPackages": {"@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/jest": "29.5.14", "@types/jest-axe": "3.5.9", "babel-jest": "29.7.0", "jest": "29.7.0", "jest-axe": "10.0.0", "jest-environment-jsdom": "29.7.0", "msw": "2.7.5", "msw-storybook-addon": "2.0.4"}, "hasRouterPackage": true, "preview": {"usesGlobals": true}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 3, "applicationFileCount": 43, "storybookVersion": "8.6.12", "storybookVersionSpecifier": "^8.6.7", "language": "typescript", "storybookPackages": {"storybook-addon-react-docgen": {"version": "1.2.44"}, "@chromatic-com/storybook": {"version": "3.2.6"}, "@storybook/blocks": {"version": "8.6.12"}, "@storybook/experimental-addon-test": {"version": "8.6.12"}, "@storybook/nextjs": {"version": "8.6.12"}, "@storybook/react": {"version": "8.6.12"}, "@storybook/test": {"version": "8.6.12"}, "@storybook/test-runner": {"version": "0.22.0"}, "@whitespace/storybook-addon-html": {"version": "6.1.1"}, "eslint-plugin-storybook": {"version": "0.11.6"}, "storybook": {"version": "8.6.12"}, "storybook-addon-pseudo-states": {"version": "4.0.3"}, "storybook-addon-rtl": {"version": "1.1.0"}, "storybook-css-modules": {"version": "1.0.8"}}, "addons": {"@storybook/addon-links": {"version": "8.6.12"}, "@storybook/addon-essentials": {"version": "8.6.12"}, "@storybook/addon-onboarding": {"version": "8.6.12"}, "@storybook/addon-themes": {"version": "8.6.12"}, "@storybook/addon-a11y": {"version": "8.6.12"}, "@storybook/addon-coverage": {"version": "1.0.5"}, "@storybook/addon-viewport": {"version": "8.6.12"}, "@storybook/addon-backgrounds": {"version": "8.6.12"}, "@storybook/addon-console": {"version": "3.0.0"}, "@storybook/addon-controls": {"version": "8.6.12"}, "@storybook/addon-actions": {"version": "8.6.12"}, "@storybook/addon-interactions": {"version": "8.6.12"}, "@storybook/addon-storysource": {"version": "8.6.12"}, "@storybook/addon-measure": {"version": "8.6.12"}, "@storybook/addon-outline": {"version": "8.6.12"}, "msw-storybook-addon": {"version": "2.0.4"}, "storybook-react-i18next": {"version": "3.3.1"}, "@storybook/addon-styling-webpack": {"version": "1.0.1"}, "chromatic": {"version": "11.28.2", "versionSpecifier": "^11.27.0"}}}, "payload": {"versionStatus": "cached", "storyIndex": {"storyCount": 500, "componentCount": 63, "pageStoryCount": 0, "playStoryCount": 78, "autodocsCount": 50, "mdxCount": 1, "exampleStoryCount": 8, "exampleDocsCount": 2, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "version": 5}, "storyStats": {"factory": 0, "play": 78, "render": 186, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 454, "storyFn": 8, "mount": 0, "moduleMock": 0}}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.11.0", "cliVersion": "8.6.12", "anonymousId": "2083e11f3b0d8f3e907f1be42620572223301ccbb83fa255eece6a7150e65f70"}}, "timestamp": 1748155460269}, "error": {"body": {"eventType": "error", "eventId": "SreH2OMUs-pGk3SfDSpCe", "sessionId": "W1VnNKrJ4325NWkrKjzSB", "metadata": {"generatedAt": 1747536022053, "hasCustomBabel": false, "hasCustomWebpack": true, "hasStaticDirs": true, "hasStorybookEslint": true, "refCount": 0, "metaFramework": {"name": "Next", "packageName": "next", "version": "15.3.1"}, "testPackages": {"@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/jest": "29.5.14", "@types/jest-axe": "3.5.9", "babel-jest": "29.7.0", "jest": "29.7.0", "jest-axe": "10.0.0", "jest-environment-jsdom": "29.7.0", "msw": "2.7.5", "msw-storybook-addon": "2.0.4"}, "hasRouterPackage": true, "preview": {"usesGlobals": true}, "framework": {"name": "@storybook/nextjs", "options": {}}, "builder": "@storybook/builder-webpack5", "renderer": "@storybook/react", "portableStoriesFileCount": 3, "applicationFileCount": 43, "storybookVersion": "8.6.12", "storybookVersionSpecifier": "^8.6.7", "language": "typescript", "storybookPackages": {"storybook-addon-react-docgen": {"version": "1.2.44"}, "@chromatic-com/storybook": {"version": "3.2.6"}, "@storybook/blocks": {"version": "8.6.12"}, "@storybook/experimental-addon-test": {"version": "8.6.12"}, "@storybook/nextjs": {"version": "8.6.12"}, "@storybook/react": {"version": "8.6.12"}, "@storybook/test": {"version": "8.6.12"}, "@storybook/test-runner": {"version": "0.22.0"}, "@whitespace/storybook-addon-html": {"version": "6.1.1"}, "eslint-plugin-storybook": {"version": "0.11.6"}, "storybook": {"version": "8.6.12"}, "storybook-addon-pseudo-states": {"version": "4.0.3"}, "storybook-addon-rtl": {"version": "1.1.0"}, "storybook-css-modules": {"version": "1.0.8"}}, "addons": {"@storybook/addon-links": {"version": "8.6.12"}, "@storybook/addon-essentials": {"version": "8.6.12"}, "@storybook/addon-onboarding": {"version": "8.6.12"}, "@storybook/addon-themes": {"version": "8.6.12"}, "@storybook/addon-a11y": {"version": "8.6.12"}, "@storybook/addon-coverage": {"version": "1.0.5"}, "@storybook/addon-viewport": {"version": "8.6.12"}, "@storybook/addon-backgrounds": {"version": "8.6.12"}, "@storybook/addon-console": {"version": "3.0.0"}, "@storybook/addon-controls": {"version": "8.6.12"}, "@storybook/addon-actions": {"version": "8.6.12"}, "@storybook/addon-interactions": {"version": "8.6.12"}, "@storybook/addon-storysource": {"version": "8.6.12"}, "@storybook/addon-measure": {"version": "8.6.12"}, "@storybook/addon-outline": {"version": "8.6.12"}, "msw-storybook-addon": {"version": "2.0.4"}, "storybook-react-i18next": {"version": "3.3.1"}, "@storybook/addon-styling-webpack": {"version": "1.0.1"}, "chromatic": {"version": "11.28.2", "versionSpecifier": "^11.27.0"}}}, "payload": {"code": 6, "name": "SB_PREVIEW_API_0006 (StoryIndexFetchError)", "category": "PREVIEW_API", "eventType": "browser", "errorHash": "9ff57027c109f6df7257e26746597404108dba792efcf7dbbf2969334bfc4c2e", "isErrorInstance": true}, "context": {"inCI": false, "isTTY": true, "platform": "Windows", "nodeVersion": "22.11.0", "cliVersion": "8.6.12", "anonymousId": "2083e11f3b0d8f3e907f1be42620572223301ccbb83fa255eece6a7150e65f70"}}, "timestamp": 1747555584074}}}