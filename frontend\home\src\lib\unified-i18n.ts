/**
 * Unified i18n implementation - combines client and server i18n functionalities
 * 
 * This file serves as the public API gateway for all i18n functionality,
 * unifying both the client-side and server-side implementations from private modules.
 */

// Import client-side i18n instance from private file
import clientI18n from './_private-i18n';

// Import shared types and constants from private shared file
import {
  supportedLngs,
  type SupportedLanguage,
  languageCodes,
  ns,
  defaultNS,
  isRTL,
  type TranslationObject
} from './_private-shared-i18n';

// Import client-side functions from private client implementation
import {
  changeLanguage,
  getLanguage
} from './_private-i18n';

/**
 * Unified i18n interface that combines client and server functionalities
 * This provides a clean, consistent API for all i18n operations
 */
const unifiedI18n = {
  // Client-side API
  client: {
    // The i18next instance for direct client-side use
    i18n: clientI18n,
    
    // Core language settings
    supportedLngs,
    languageCodes,
    ns,
    defaultNS,
    
    // Core functions
    isRTL,
    changeLanguage,
    getLanguage
  },
  
  // Server-side API - using dynamic imports to prevent client bundle issues
  server: {
    /**
     * Get a translation function for the given namespace and language on the server
     * Uses dynamic import to prevent client-side bundling of server code
     */
    getTranslation: async (namespace: string = 'common', language?: SupportedLanguage) => {
      // Only load server implementation on the server
      if (typeof window === 'undefined') {
        const serverModule = await import('./_private-server-i18n');
        return serverModule.getServerTranslation(namespace, language);
      } else {
        // Safe fallback for client-side
        console.warn('Attempted to use server-side translation on the client');
        return {
          t: (key: string) => key // Fallback that just returns the key
        };
      }
    }
  },
  
  // Shared settings accessible by both client and server
  shared: {
    supportedLngs,
    languageCodes,
    ns,
    defaultNS,
    isRTL
  },
  
  // Environment detection helpers
  isClient: typeof window !== 'undefined',
  isServer: typeof window === 'undefined',
  
  /**
   * Universal translation function factory that works in both client and server environments
   * This automatically detects environment and returns the appropriate implementation
   */
  getTranslationFunction: async (namespace: string = 'common', language?: SupportedLanguage) => {
    // Server-side implementation with dynamic import
    if (typeof window === 'undefined') {
      const serverModule = await import('./_private-server-i18n');
      return serverModule.getServerTranslation(namespace, language);
    }
    
    // Client-side implementation with consistent API shape
    return {
      t: (key: string, options?: any) => clientI18n.t(key, { ns: namespace, ...options })
    };
  }
};

// Export the unified i18n object as the default export
export default unifiedI18n;

// Named exports for convenience and direct imports
export {
  // Language configuration
  supportedLngs,
  languageCodes,
  ns,
  defaultNS,
  
  // Core functions
  isRTL,
  changeLanguage,
  getLanguage,
};

// Server-side only function with runtime check
export async function getServerTranslation(namespace?: string, language?: SupportedLanguage) {
  if (typeof window === 'undefined') {
    const serverModule = await import('./_private-server-i18n');
    return serverModule.getServerTranslation(namespace, language);
  }
  throw new Error('getServerTranslation should only be called on the server');
}

// Type exports for API consumers
export type { SupportedLanguage, TranslationObject }; 