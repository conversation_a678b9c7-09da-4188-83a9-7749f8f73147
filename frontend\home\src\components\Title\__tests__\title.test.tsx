import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Title } from '../title';
import { allFixtures } from '../__fixtures__/title.fixtures';

describe('Title Component', () => {
  test('renders basic title correctly', () => {
    render(<Title {...allFixtures.base} />);
    expect(screen.getByRole('heading')).toHaveTextContent('หัวข้อตัวอย่าง');
    expect(screen.getByRole('link')).toHaveTextContent('ดูทั้งหมด');
  });

  test('renders with description', () => {
    render(<Title {...allFixtures.withDescription} />);
    expect(screen.getByRole('heading')).toHaveTextContent('โคมไฟและหลอดไฟ');
    expect(screen.getByText(allFixtures.withDescription.description!)).toBeInTheDocument();
  });

  test('renders with highlighted text', () => {
    render(<Title {...allFixtures.withHighlight} />);
    expect(screen.getByRole('heading')).toHaveTextContent('คูปองส่วนลด #เก็บแล้วใช้เลย!');
    expect(screen.getByText('#เก็บแล้วใช้เลย!')).toHaveClass('bg-yellow-200');
  });

  test('handles view all button click', async () => {
    const user = userEvent.setup();
    const onViewAllClick = jest.fn();
    
    render(
      <Title 
        {...allFixtures.base} 
        onViewAllClick={onViewAllClick} 
      />
    );
    
    const viewAllButton = screen.getByRole('link', { name: /ดูทั้งหมด/i });
    await user.click(viewAllButton);
    
    expect(onViewAllClick).toHaveBeenCalledTimes(1);
  });

  test('not renders view all button when no link provided', () => {
    render(<Title title="หัวข้อเท่านั้น" />);
    expect(screen.queryByRole('link', { name: /ดูทั้งหมด/i })).not.toBeInTheDocument();
  });

  test('renders with custom view all text', () => {
    render(
      <Title 
        {...allFixtures.base}
        viewAllText="ดูเพิ่มเติม" 
      />
    );
    expect(screen.getByRole('link')).toHaveTextContent('ดูเพิ่มเติม');
  });

  // Test สำหรับคุณสมบัติใหม่
  test('not renders view all button when showViewAllButton is false', () => {
    render(<Title {...allFixtures.withoutButton} />);
    expect(screen.queryByRole('link', { name: /ดูทั้งหมด/i })).not.toBeInTheDocument();
  });
  
  test('renders with h1 heading when as is set to h1', () => {
    render(<Title {...allFixtures.headingH1} />);
    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toHaveTextContent('หัวข้อระดับ H1');
  });
  
  test('renders with h3 heading when as is set to h3', () => {
    render(<Title {...allFixtures.headingH3} />);
    const heading = screen.getByRole('heading', { level: 3 });
    expect(heading).toHaveTextContent('หัวข้อระดับ H3');
  });

  // A11y tests
  test('passes basic accessibility checks', () => {
    const { container } = render(<Title {...allFixtures.withDescription} />);
    // ใช้ jest-axe หรือวิธีทดสอบ a11y ที่เหมาะสมในโปรเจค
    // ตัวอย่างเช่น: 
    // const results = await axe(container);
    // expect(results).toHaveNoViolations();
    
    // แต่ในที่นี้จะทดสอบโดยตรงว่า component มีโครงสร้างที่ถูกต้องสำหรับ a11y
    expect(screen.getByRole('heading')).toBeInTheDocument(); // ตรวจสอบว่ามี heading
    expect(screen.getByRole('link')).toHaveAttribute('href'); // ตรวจสอบว่าลิงก์มี href attribute
  });

  test('uses correct heading hierarchy', () => {
    render(<Title {...allFixtures.base} />);
    const heading = screen.getByRole('heading');
    expect(heading.tagName).toBe('H2'); // ตรวจสอบว่าใช้ H2 ตามที่ออกแบบไว้
  });

  test('button has accessible name', () => {
    render(<Title {...allFixtures.base} />);
    const button = screen.getByRole('link');
    expect(button).toHaveAccessibleName('ดูทั้งหมด');
  });

  // State tests
  test('applies custom className to root element', () => {
    const { container } = render(
      <Title 
        {...allFixtures.base}
        className="custom-class" 
      />
    );
    const rootElement = container.firstChild;
    expect(rootElement).toHaveClass('custom-class');
  });

  test('applies custom titleClassName to title element', () => {
    render(
      <Title 
        {...allFixtures.base}
        titleClassName="custom-title-class" 
      />
    );
    const titleElement = screen.getByRole('heading');
    expect(titleElement).toHaveClass('custom-title-class');
  });

  test('applies custom descriptionClassName to description element', () => {
    render(
      <Title 
        {...allFixtures.withDescription}
        descriptionClassName="custom-description-class" 
      />
    );
    const descriptionElement = screen.getByText(allFixtures.withDescription.description!);
    expect(descriptionElement).toHaveClass('custom-description-class');
  });
});