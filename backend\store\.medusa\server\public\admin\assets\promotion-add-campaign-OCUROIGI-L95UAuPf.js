import{a as l}from"./chunk-TJY3W2YI-BX13usPR.js";import{R as x,b as j,dh as C,da as P,j as i,H as f}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import{b as p}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./chunk-UGE5SYTC-lkiSEaTI.js";import"./chunk-MWVM4TYO-bKUcYSnf.js";import"./radio-group-FK6NWg-k.js";import"./index-DX0YxfHa.js";import"./textarea-CcKuCLDy.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./index-DP5bcQyU.js";import"./x-mark-mini-DvSTI7zK.js";import"./triangle-left-mini-Bu6679Aa.js";import"./select-BRJtadHb.js";import"./triangles-mini-DPBC_td5.js";import"./check-BGSYwiWc.js";import"./currency-input-Yr7vS0SV.js";import"./index.esm-3G2Z4eQ8.js";import"./Trans-VWqfqpAH.js";import"./prompt-BsR9zKsn.js";var M=()=>{var t,m;const{id:e}=x(),{t:s}=j(),{promotion:r,isPending:n,isError:d,error:c}=C(e);let o={};(t=r==null?void 0:r.application_method)!=null&&t.currency_code&&(o={budget:{currency_code:(m=r==null?void 0:r.application_method)==null?void 0:m.currency_code}});const{campaigns:a,isPending:g,isError:u,error:h}=P(o);if(d||u)throw c||h;return i.jsxs(p,{children:[i.jsx(p.Header,{children:i.jsx(f,{children:s("promotions.campaign.edit.header")})}),!n&&!g&&r&&a&&i.jsx(l,{promotion:r,campaigns:a})]})};export{M as Component};
