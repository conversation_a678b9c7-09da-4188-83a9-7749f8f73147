import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import Link from 'next/link';

// Interface for advanced analytics data
interface AdvancedAnalytics {
  pageViews: {
    today: number;
    weekly: number;
    monthly: number;
  };
  userSessions: {
    averageDuration: string;
    bounceRate: string;
    newUsers: number;
  };
  serverLoad: {
    current: string;
    average: string;
    peak: string;
  };
  geographicData: {
    region: string;
    visitors: number;
    percentage: number;
  }[];
  topReferrers: {
    source: string;
    visitors: number;
    conversionRate: string;
  }[];
  generatedAt: string;
}

// Advanced analytics data generation
async function generateAdvancedAnalytics(): Promise<AdvancedAnalytics> {
  // Artificial delay to simulate data fetching
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Generate mock data for ISR
  return {
    pageViews: {
      today: Math.floor(Math.random() * 10000),
      weekly: Math.floor(Math.random() * 50000),
      monthly: Math.floor(Math.random() * 200000),
    },
    userSessions: {
      averageDuration: Math.floor(Math.random() * 500) + ' seconds',
      bounceRate: Math.floor(Math.random() * 100) + '%',
      newUsers: Math.floor(Math.random() * 5000),
    },
    serverLoad: {
      current: Math.floor(Math.random() * 100) + '%',
      average: Math.floor(Math.random() * 100) + '%',
      peak: Math.floor(Math.random() * 100) + '%',
    },
    geographicData: [
      {
        region: 'North America',
        visitors: Math.floor(Math.random() * 50000),
        percentage: Math.floor(Math.random() * 40) + 20,
      },
      {
        region: 'Europe',
        visitors: Math.floor(Math.random() * 40000),
        percentage: Math.floor(Math.random() * 30) + 15,
      },
      {
        region: 'Asia',
        visitors: Math.floor(Math.random() * 60000),
        percentage: Math.floor(Math.random() * 35) + 25,
      },
      {
        region: 'South America',
        visitors: Math.floor(Math.random() * 20000),
        percentage: Math.floor(Math.random() * 15) + 5,
      },
      {
        region: 'Africa',
        visitors: Math.floor(Math.random() * 10000),
        percentage: Math.floor(Math.random() * 10) + 2,
      },
    ],
    topReferrers: [
      {
        source: 'Google',
        visitors: Math.floor(Math.random() * 30000),
        conversionRate: Math.floor(Math.random() * 10) + '%',
      },
      {
        source: 'Direct',
        visitors: Math.floor(Math.random() * 20000),
        conversionRate: Math.floor(Math.random() * 15) + '%',
      },
      {
        source: 'Twitter',
        visitors: Math.floor(Math.random() * 10000),
        conversionRate: Math.floor(Math.random() * 8) + '%',
      },
      {
        source: 'Facebook',
        visitors: Math.floor(Math.random() * 8000),
        conversionRate: Math.floor(Math.random() * 6) + '%',
      },
      {
        source: 'LinkedIn',
        visitors: Math.floor(Math.random() * 5000),
        conversionRate: Math.floor(Math.random() * 12) + '%',
      },
    ],
    generatedAt: new Date().toISOString(),
  };
}

// Set revalidation period to 60 seconds for this advanced page
export const revalidate = 60;

export default async function ISRAdvancedPage() {
  // Get analytics data with ISR
  const data = await generateAdvancedAnalytics();

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="isr" />
      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">Advanced ISR Techniques</h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">Data-Heavy ISR Implementation</h2>
          <p className="mb-4">
            This example demonstrates using ISR for a data-heavy dashboard that would typically
            require significant server resources to generate on each request. With ISR, we can
            pre-render this complex page and periodically refresh the data (every 60 seconds in this
            case).
          </p>
          <p>
            Unlike CSR dashboards that require JavaScript to load data and show loading states, this
            ISR dashboard is immediately viewable with complete data.
          </p>
        </div>

        <div className="bg-card mb-8 rounded-lg border p-6">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-xl font-semibold">Analytics Dashboard</h2>
            <div className="text-muted-foreground text-sm">
              Last updated: {new Date(data.generatedAt).toLocaleString()}
            </div>
          </div>

          {/* Summary metrics grid */}
          <div className="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
              <h3 className="text-muted-foreground mb-1 text-sm">Today&apos;s Visitors</h3>
              <p className="text-3xl font-bold">{data.pageViews.today.toLocaleString()}</p>
            </div>
            <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
              <h3 className="text-muted-foreground mb-1 text-sm">Weekly Visitors</h3>
              <p className="text-3xl font-bold">{data.pageViews.weekly.toLocaleString()}</p>
            </div>
            <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
              <h3 className="text-muted-foreground mb-1 text-sm">New Users</h3>
              <p className="text-3xl font-bold">{data.userSessions.newUsers.toLocaleString()}</p>
            </div>
            <div className="bg-primary/5 border-primary/20 rounded-lg border p-4">
              <h3 className="text-muted-foreground mb-1 text-sm">Bounce Rate</h3>
              <p className="text-3xl font-bold">{data.userSessions.bounceRate}</p>
            </div>
          </div>

          {/* Geographic data and referrers in a 2-column layout */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <div>
              <h3 className="mb-4 text-lg font-medium">Geographic Distribution</h3>
              <div className="bg-muted overflow-hidden rounded-lg">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-2 text-left">Region</th>
                      <th className="px-4 py-2 text-left">Visitors</th>
                      <th className="px-4 py-2 text-left">Percentage</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.geographicData.map((region) => (
                      <tr key={region.region} className="border-b">
                        <td className="px-4 py-2">{region.region}</td>
                        <td className="px-4 py-2">{region.visitors.toLocaleString()}</td>
                        <td className="px-4 py-2">
                          <div className="flex items-center">
                            <div className="bg-muted mr-2 h-2 w-20 rounded-full">
                              <div
                                className="bg-primary h-2 rounded-full"
                                style={{ width: `${region.percentage}%` }}
                              ></div>
                            </div>
                            <span>{region.percentage}%</span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <h3 className="mb-4 text-lg font-medium">Top Referrers</h3>
              <div className="bg-muted overflow-hidden rounded-lg">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-4 py-2 text-left">Source</th>
                      <th className="px-4 py-2 text-left">Visitors</th>
                      <th className="px-4 py-2 text-left">Conversion</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.topReferrers.map((referrer) => (
                      <tr key={referrer.source} className="border-b">
                        <td className="px-4 py-2">{referrer.source}</td>
                        <td className="px-4 py-2">{referrer.visitors.toLocaleString()}</td>
                        <td className="px-4 py-2">{referrer.conversionRate}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div className="mt-8 rounded-lg border p-4">
            <h3 className="mb-2 text-lg font-medium">System Performance</h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
              <div>
                <p className="text-muted-foreground text-sm">Current Load</p>
                <p className="text-xl font-semibold">{data.serverLoad.current}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm">Average Load</p>
                <p className="text-xl font-semibold">{data.serverLoad.average}</p>
              </div>
              <div>
                <p className="text-muted-foreground text-sm">Peak Load</p>
                <p className="text-xl font-semibold">{data.serverLoad.peak}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-muted rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">ISR Advanced Techniques</h2>
          <ul className="list-disc space-y-2 pl-5">
            <li>
              <strong>On-demand Revalidation:</strong> Trigger revalidation when data changes via
              webhooks or API endpoints
            </li>
            <li>
              <strong>Per-request Revalidation:</strong> Control revalidation periods for different
              data sources within the same page
            </li>
            <li>
              <strong>Stale-while-revalidate:</strong> Serve stale content while fetching new data
              in the background
            </li>
            <li>
              <strong>CDN Cache Invalidation:</strong> Automatically invalidate CDN caches when
              content is revalidated
            </li>
            <li>
              <strong>Hybrid Rendering:</strong> Combine ISR with CSR for dynamic components that
              need client-side updates
            </li>
          </ul>
        </div>

        <div className="mt-8 flex justify-center">
          <Link href="/rendering-examples/isr" className="text-primary font-medium hover:underline">
            ← Back to Basic ISR Example
          </Link>
        </div>
      </main>
    </div>
  );
}
