import{r as N}from"./index-Bwql5Dzz.js";/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function ln(){return{accessor:(e,o)=>typeof e=="function"?{...o,accessorFn:e}:{...o,accessorKey:e},display:e=>e,group:e=>e}}function P(e,o){return typeof e=="function"?e(o):e}function $(e,o){return n=>{o.setState(t=>({...t,[e]:P(n,t[e])}))}}function A(e){return e instanceof Function}function Ce(e){return Array.isArray(e)&&e.every(o=>typeof o=="number")}function me(e,o){const n=[],t=r=>{r.forEach(i=>{n.push(i);const l=o(i);l!=null&&l.length&&t(l)})};return t(e),n}function C(e,o,n){let t=[],r;return i=>{let l;n.key&&n.debug&&(l=Date.now());const u=e(i);if(!(u.length!==t.length||u.some((f,S)=>t[S]!==f)))return r;t=u;let a;if(n.key&&n.debug&&(a=Date.now()),r=o(...u),n==null||n.onChange==null||n.onChange(r),n.key&&n.debug&&n!=null&&n.debug()){const f=Math.round((Date.now()-l)*100)/100,S=Math.round((Date.now()-a)*100)/100,d=S/16,g=(c,p)=>{for(c=String(c);c.length<p;)c=" "+c;return c};console.info(`%c⏱ ${g(S,5)} /${g(f,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,n==null?void 0:n.key)}return r}}function m(e,o,n,t){return{debug:()=>{var r;return(r=e==null?void 0:e.debugAll)!=null?r:e[o]},key:n,onChange:t}}function Re(e,o,n,t){const r=()=>{var l;return(l=i.getValue())!=null?l:e.options.renderFallbackValue},i={id:`${o.id}_${n.id}`,row:o,column:n,getValue:()=>o.getValue(t),renderValue:r,getContext:C(()=>[e,n,o,i],(l,u,s,a)=>({table:l,column:u,row:s,cell:a,getValue:a.getValue,renderValue:a.renderValue}),m(e.options,"debugCells","cell.getContext"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(i,n,o,e)},{}),i}function we(e,o,n,t){var r,i;const u={...e._getDefaultColumnDef(),...o},s=u.accessorKey;let a=(r=(i=u.id)!=null?i:s?typeof String.prototype.replaceAll=="function"?s.replaceAll(".","_"):s.replace(/\./g,"_"):void 0)!=null?r:typeof u.header=="string"?u.header:void 0,f;if(u.accessorFn?f=u.accessorFn:s&&(s.includes(".")?f=d=>{let g=d;for(const p of s.split(".")){var c;g=(c=g)==null?void 0:c[p],g===void 0&&console.warn(`"${p}" in deeply nested key "${s}" returned undefined.`)}return g}:f=d=>d[u.accessorKey]),!a)throw new Error(u.accessorFn?"Columns require an id when using an accessorFn":"Columns require an id when using a non-string header");let S={id:`${String(a)}`,accessorFn:f,parent:t,depth:n,columnDef:u,columns:[],getFlatColumns:C(()=>[!0],()=>{var d;return[S,...(d=S.columns)==null?void 0:d.flatMap(g=>g.getFlatColumns())]},m(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:C(()=>[e._getOrderColumnsFn()],d=>{var g;if((g=S.columns)!=null&&g.length){let c=S.columns.flatMap(p=>p.getLeafColumns());return d(c)}return[S]},m(e.options,"debugColumns","column.getLeafColumns"))};for(const d of e._features)d.createColumn==null||d.createColumn(S,e);return S}const _="debugHeaders";function te(e,o,n){var t;let i={id:(t=n.id)!=null?t:o.id,column:o,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],u=s=>{s.subHeaders&&s.subHeaders.length&&s.subHeaders.map(u),l.push(s)};return u(i),l},getContext:()=>({table:e,header:i,column:o})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(i,e)}),i}const he={createTable:e=>{e.getHeaderGroups=C(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,n,t,r)=>{var i,l;const u=(i=t==null?void 0:t.map(S=>n.find(d=>d.id===S)).filter(Boolean))!=null?i:[],s=(l=r==null?void 0:r.map(S=>n.find(d=>d.id===S)).filter(Boolean))!=null?l:[],a=n.filter(S=>!(t!=null&&t.includes(S.id))&&!(r!=null&&r.includes(S.id)));return H(o,[...u,...a,...s],e)},m(e.options,_,"getHeaderGroups")),e.getCenterHeaderGroups=C(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,n,t,r)=>(n=n.filter(i=>!(t!=null&&t.includes(i.id))&&!(r!=null&&r.includes(i.id))),H(o,n,e,"center")),m(e.options,_,"getCenterHeaderGroups")),e.getLeftHeaderGroups=C(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(o,n,t)=>{var r;const i=(r=t==null?void 0:t.map(l=>n.find(u=>u.id===l)).filter(Boolean))!=null?r:[];return H(o,i,e,"left")},m(e.options,_,"getLeftHeaderGroups")),e.getRightHeaderGroups=C(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(o,n,t)=>{var r;const i=(r=t==null?void 0:t.map(l=>n.find(u=>u.id===l)).filter(Boolean))!=null?r:[];return H(o,i,e,"right")},m(e.options,_,"getRightHeaderGroups")),e.getFooterGroups=C(()=>[e.getHeaderGroups()],o=>[...o].reverse(),m(e.options,_,"getFooterGroups")),e.getLeftFooterGroups=C(()=>[e.getLeftHeaderGroups()],o=>[...o].reverse(),m(e.options,_,"getLeftFooterGroups")),e.getCenterFooterGroups=C(()=>[e.getCenterHeaderGroups()],o=>[...o].reverse(),m(e.options,_,"getCenterFooterGroups")),e.getRightFooterGroups=C(()=>[e.getRightHeaderGroups()],o=>[...o].reverse(),m(e.options,_,"getRightFooterGroups")),e.getFlatHeaders=C(()=>[e.getHeaderGroups()],o=>o.map(n=>n.headers).flat(),m(e.options,_,"getFlatHeaders")),e.getLeftFlatHeaders=C(()=>[e.getLeftHeaderGroups()],o=>o.map(n=>n.headers).flat(),m(e.options,_,"getLeftFlatHeaders")),e.getCenterFlatHeaders=C(()=>[e.getCenterHeaderGroups()],o=>o.map(n=>n.headers).flat(),m(e.options,_,"getCenterFlatHeaders")),e.getRightFlatHeaders=C(()=>[e.getRightHeaderGroups()],o=>o.map(n=>n.headers).flat(),m(e.options,_,"getRightFlatHeaders")),e.getCenterLeafHeaders=C(()=>[e.getCenterFlatHeaders()],o=>o.filter(n=>{var t;return!((t=n.subHeaders)!=null&&t.length)}),m(e.options,_,"getCenterLeafHeaders")),e.getLeftLeafHeaders=C(()=>[e.getLeftFlatHeaders()],o=>o.filter(n=>{var t;return!((t=n.subHeaders)!=null&&t.length)}),m(e.options,_,"getLeftLeafHeaders")),e.getRightLeafHeaders=C(()=>[e.getRightFlatHeaders()],o=>o.filter(n=>{var t;return!((t=n.subHeaders)!=null&&t.length)}),m(e.options,_,"getRightLeafHeaders")),e.getLeafHeaders=C(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(o,n,t)=>{var r,i,l,u,s,a;return[...(r=(i=o[0])==null?void 0:i.headers)!=null?r:[],...(l=(u=n[0])==null?void 0:u.headers)!=null?l:[],...(s=(a=t[0])==null?void 0:a.headers)!=null?s:[]].map(f=>f.getLeafHeaders()).flat()},m(e.options,_,"getLeafHeaders"))}};function H(e,o,n,t){var r,i;let l=0;const u=function(d,g){g===void 0&&(g=1),l=Math.max(l,g),d.filter(c=>c.getIsVisible()).forEach(c=>{var p;(p=c.columns)!=null&&p.length&&u(c.columns,g+1)},0)};u(e);let s=[];const a=(d,g)=>{const c={depth:g,id:[t,`${g}`].filter(Boolean).join("_"),headers:[]},p=[];d.forEach(w=>{const R=[...p].reverse()[0],F=w.column.depth===c.depth;let h,V=!1;if(F&&w.column.parent?h=w.column.parent:(h=w.column,V=!0),R&&(R==null?void 0:R.column)===h)R.subHeaders.push(w);else{const I=te(n,h,{id:[t,g,h.id,w==null?void 0:w.id].filter(Boolean).join("_"),isPlaceholder:V,placeholderId:V?`${p.filter(L=>L.column===h).length}`:void 0,depth:g,index:p.length});I.subHeaders.push(w),p.push(I)}c.headers.push(w),w.headerGroup=c}),s.push(c),g>0&&a(p,g-1)},f=o.map((d,g)=>te(n,d,{depth:l,index:g}));a(f,l-1),s.reverse();const S=d=>d.filter(c=>c.column.getIsVisible()).map(c=>{let p=0,w=0,R=[0];c.subHeaders&&c.subHeaders.length?(R=[],S(c.subHeaders).forEach(h=>{let{colSpan:V,rowSpan:I}=h;p+=V,R.push(I)})):p=1;const F=Math.min(...R);return w=w+F,c.colSpan=p,c.rowSpan=w,{colSpan:p,rowSpan:w}});return S((r=(i=s[0])==null?void 0:i.headers)!=null?r:[]),s}const _e=(e,o,n,t,r,i,l)=>{let u={id:o,index:t,original:n,depth:r,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:s=>{if(u._valuesCache.hasOwnProperty(s))return u._valuesCache[s];const a=e.getColumn(s);if(a!=null&&a.accessorFn)return u._valuesCache[s]=a.accessorFn(u.original,t),u._valuesCache[s]},getUniqueValues:s=>{if(u._uniqueValuesCache.hasOwnProperty(s))return u._uniqueValuesCache[s];const a=e.getColumn(s);if(a!=null&&a.accessorFn)return a.columnDef.getUniqueValues?(u._uniqueValuesCache[s]=a.columnDef.getUniqueValues(u.original,t),u._uniqueValuesCache[s]):(u._uniqueValuesCache[s]=[u.getValue(s)],u._uniqueValuesCache[s])},renderValue:s=>{var a;return(a=u.getValue(s))!=null?a:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>me(u.subRows,s=>s.subRows),getParentRow:()=>u.parentId?e.getRow(u.parentId,!0):void 0,getParentRows:()=>{let s=[],a=u;for(;;){const f=a.getParentRow();if(!f)break;s.push(f),a=f}return s.reverse()},getAllCells:C(()=>[e.getAllLeafColumns()],s=>s.map(a=>Re(e,u,a,a.id)),m(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:C(()=>[u.getAllCells()],s=>s.reduce((a,f)=>(a[f.column.id]=f,a),{}),m(e.options,"debugRows","getAllCellsByColumnId"))};for(let s=0;s<e._features.length;s++){const a=e._features[s];a==null||a.createRow==null||a.createRow(u,e)}return u},Fe={createColumn:(e,o)=>{e._getFacetedRowModel=o.options.getFacetedRowModel&&o.options.getFacetedRowModel(o,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():o.getPreFilteredRowModel(),e._getFacetedUniqueValues=o.options.getFacetedUniqueValues&&o.options.getFacetedUniqueValues(o,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=o.options.getFacetedMinMaxValues&&o.options.getFacetedMinMaxValues(o,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},re=(e,o,n)=>{var t,r;const i=n==null||(t=n.toString())==null?void 0:t.toLowerCase();return!!(!((r=e.getValue(o))==null||(r=r.toString())==null||(r=r.toLowerCase())==null)&&r.includes(i))};re.autoRemove=e=>v(e);const ie=(e,o,n)=>{var t;return!!(!((t=e.getValue(o))==null||(t=t.toString())==null)&&t.includes(n))};ie.autoRemove=e=>v(e);const le=(e,o,n)=>{var t;return((t=e.getValue(o))==null||(t=t.toString())==null?void 0:t.toLowerCase())===(n==null?void 0:n.toLowerCase())};le.autoRemove=e=>v(e);const ue=(e,o,n)=>{var t;return(t=e.getValue(o))==null?void 0:t.includes(n)};ue.autoRemove=e=>v(e)||!(e!=null&&e.length);const se=(e,o,n)=>!n.some(t=>{var r;return!((r=e.getValue(o))!=null&&r.includes(t))});se.autoRemove=e=>v(e)||!(e!=null&&e.length);const ge=(e,o,n)=>n.some(t=>{var r;return(r=e.getValue(o))==null?void 0:r.includes(t)});ge.autoRemove=e=>v(e)||!(e!=null&&e.length);const ae=(e,o,n)=>e.getValue(o)===n;ae.autoRemove=e=>v(e);const de=(e,o,n)=>e.getValue(o)==n;de.autoRemove=e=>v(e);const W=(e,o,n)=>{let[t,r]=n;const i=e.getValue(o);return i>=t&&i<=r};W.resolveFilterValue=e=>{let[o,n]=e,t=typeof o!="number"?parseFloat(o):o,r=typeof n!="number"?parseFloat(n):n,i=o===null||Number.isNaN(t)?-1/0:t,l=n===null||Number.isNaN(r)?1/0:r;if(i>l){const u=i;i=l,l=u}return[i,l]};W.autoRemove=e=>v(e)||v(e[0])&&v(e[1]);const M={includesString:re,includesStringSensitive:ie,equalsString:le,arrIncludes:ue,arrIncludesAll:se,arrIncludesSome:ge,equals:ae,weakEquals:de,inNumberRange:W};function v(e){return e==null||e===""}const $e={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:$("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,o)=>{e.getAutoFilterFn=()=>{const n=o.getCoreRowModel().flatRows[0],t=n==null?void 0:n.getValue(e.id);return typeof t=="string"?M.includesString:typeof t=="number"?M.inNumberRange:typeof t=="boolean"||t!==null&&typeof t=="object"?M.equals:Array.isArray(t)?M.arrIncludes:M.weakEquals},e.getFilterFn=()=>{var n,t;return A(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(t=o.options.filterFns)==null?void 0:t[e.columnDef.filterFn])!=null?n:M[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,t,r;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((t=o.options.enableColumnFilters)!=null?t:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=o.getState().columnFilters)==null||(n=n.find(t=>t.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,t;return(n=(t=o.getState().columnFilters)==null?void 0:t.findIndex(r=>r.id===e.id))!=null?n:-1},e.setFilterValue=n=>{o.setColumnFilters(t=>{const r=e.getFilterFn(),i=t==null?void 0:t.find(f=>f.id===e.id),l=P(n,i?i.value:void 0);if(oe(r,l,e)){var u;return(u=t==null?void 0:t.filter(f=>f.id!==e.id))!=null?u:[]}const s={id:e.id,value:l};if(i){var a;return(a=t==null?void 0:t.map(f=>f.id===e.id?s:f))!=null?a:[]}return t!=null&&t.length?[...t,s]:[s]})}},createRow:(e,o)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=o=>{const n=e.getAllLeafColumns(),t=r=>{var i;return(i=P(o,r))==null?void 0:i.filter(l=>{const u=n.find(s=>s.id===l.id);if(u){const s=u.getFilterFn();if(oe(s,l.value,u))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(t)},e.resetColumnFilters=o=>{var n,t;e.setColumnFilters(o?[]:(n=(t=e.initialState)==null?void 0:t.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function oe(e,o,n){return(e&&e.autoRemove?e.autoRemove(o,n):!1)||typeof o>"u"||typeof o=="string"&&!o}const ve=(e,o,n)=>n.reduce((t,r)=>{const i=r.getValue(e);return t+(typeof i=="number"?i:0)},0),Me=(e,o,n)=>{let t;return n.forEach(r=>{const i=r.getValue(e);i!=null&&(t>i||t===void 0&&i>=i)&&(t=i)}),t},Ve=(e,o,n)=>{let t;return n.forEach(r=>{const i=r.getValue(e);i!=null&&(t<i||t===void 0&&i>=i)&&(t=i)}),t},Pe=(e,o,n)=>{let t,r;return n.forEach(i=>{const l=i.getValue(e);l!=null&&(t===void 0?l>=l&&(t=r=l):(t>l&&(t=l),r<l&&(r=l)))}),[t,r]},xe=(e,o)=>{let n=0,t=0;if(o.forEach(r=>{let i=r.getValue(e);i!=null&&(i=+i)>=i&&(++n,t+=i)}),n)return t/n},Ie=(e,o)=>{if(!o.length)return;const n=o.map(i=>i.getValue(e));if(!Ce(n))return;if(n.length===1)return n[0];const t=Math.floor(n.length/2),r=n.sort((i,l)=>i-l);return n.length%2!==0?r[t]:(r[t-1]+r[t])/2},ye=(e,o)=>Array.from(new Set(o.map(n=>n.getValue(e))).values()),Ee=(e,o)=>new Set(o.map(n=>n.getValue(e))).size,He=(e,o)=>o.length,z={sum:ve,min:Me,max:Ve,extent:Pe,mean:xe,median:Ie,unique:ye,uniqueCount:Ee,count:He},De={getDefaultColumnDef:()=>({aggregatedCell:e=>{var o,n;return(o=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?o:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:$("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,o)=>{e.toggleGrouping=()=>{o.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(t=>t!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,t;return((n=e.columnDef.enableGrouping)!=null?n:!0)&&((t=o.options.enableGrouping)!=null?t:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return(n=o.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=o.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=o.getCoreRowModel().flatRows[0],t=n==null?void 0:n.getValue(e.id);if(typeof t=="number")return z.sum;if(Object.prototype.toString.call(t)==="[object Date]")return z.extent},e.getAggregationFn=()=>{var n,t;if(!e)throw new Error;return A(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(t=o.options.aggregationFns)==null?void 0:t[e.columnDef.aggregationFn])!=null?n:z[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=o=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(o),e.resetGrouping=o=>{var n,t;e.setGrouping(o?[]:(n=(t=e.initialState)==null?void 0:t.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,o)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const t=o.getColumn(n);return t!=null&&t.columnDef.getGroupingValue?(e._groupingValuesCache[n]=t.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,o,n,t)=>{e.getIsGrouped=()=>o.getIsGrouped()&&o.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&o.getIsGrouped(),e.getIsAggregated=()=>{var r;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((r=n.subRows)!=null&&r.length)}}};function Ge(e,o,n){if(!(o!=null&&o.length)||!n)return e;const t=e.filter(i=>!o.includes(i.id));return n==="remove"?t:[...o.map(i=>e.find(l=>l.id===i)).filter(Boolean),...t]}const Ae={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:$("columnOrder",e)}),createColumn:(e,o)=>{e.getIndex=C(n=>[E(o,n)],n=>n.findIndex(t=>t.id===e.id),m(o.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var t;return((t=E(o,n)[0])==null?void 0:t.id)===e.id},e.getIsLastColumn=n=>{var t;const r=E(o,n);return((t=r[r.length-1])==null?void 0:t.id)===e.id}},createTable:e=>{e.setColumnOrder=o=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(o),e.resetColumnOrder=o=>{var n;e.setColumnOrder(o?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=C(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(o,n,t)=>r=>{let i=[];if(!(o!=null&&o.length))i=r;else{const l=[...o],u=[...r];for(;u.length&&l.length;){const s=l.shift(),a=u.findIndex(f=>f.id===s);a>-1&&i.push(u.splice(a,1)[0])}i=[...i,...u]}return Ge(i,n,t)},m(e.options,"debugTable","_getOrderColumnsFn"))}},O=()=>({left:[],right:[]}),Le={getInitialState:e=>({columnPinning:O(),...e}),getDefaultOptions:e=>({onColumnPinningChange:$("columnPinning",e)}),createColumn:(e,o)=>{e.pin=n=>{const t=e.getLeafColumns().map(r=>r.id).filter(Boolean);o.setColumnPinning(r=>{var i,l;if(n==="right"){var u,s;return{left:((u=r==null?void 0:r.left)!=null?u:[]).filter(S=>!(t!=null&&t.includes(S))),right:[...((s=r==null?void 0:r.right)!=null?s:[]).filter(S=>!(t!=null&&t.includes(S))),...t]}}if(n==="left"){var a,f;return{left:[...((a=r==null?void 0:r.left)!=null?a:[]).filter(S=>!(t!=null&&t.includes(S))),...t],right:((f=r==null?void 0:r.right)!=null?f:[]).filter(S=>!(t!=null&&t.includes(S)))}}return{left:((i=r==null?void 0:r.left)!=null?i:[]).filter(S=>!(t!=null&&t.includes(S))),right:((l=r==null?void 0:r.right)!=null?l:[]).filter(S=>!(t!=null&&t.includes(S)))}})},e.getCanPin=()=>e.getLeafColumns().some(t=>{var r,i,l;return((r=t.columnDef.enablePinning)!=null?r:!0)&&((i=(l=o.options.enableColumnPinning)!=null?l:o.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(u=>u.id),{left:t,right:r}=o.getState().columnPinning,i=n.some(u=>t==null?void 0:t.includes(u)),l=n.some(u=>r==null?void 0:r.includes(u));return i?"left":l?"right":!1},e.getPinnedIndex=()=>{var n,t;const r=e.getIsPinned();return r?(n=(t=o.getState().columnPinning)==null||(t=t[r])==null?void 0:t.indexOf(e.id))!=null?n:-1:0}},createRow:(e,o)=>{e.getCenterVisibleCells=C(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left,o.getState().columnPinning.right],(n,t,r)=>{const i=[...t??[],...r??[]];return n.filter(l=>!i.includes(l.column.id))},m(o.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=C(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left],(n,t)=>(t??[]).map(i=>n.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),m(o.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=C(()=>[e._getAllVisibleCells(),o.getState().columnPinning.right],(n,t)=>(t??[]).map(i=>n.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),m(o.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=o=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(o),e.resetColumnPinning=o=>{var n,t;return e.setColumnPinning(o?O():(n=(t=e.initialState)==null?void 0:t.columnPinning)!=null?n:O())},e.getIsSomeColumnsPinned=o=>{var n;const t=e.getState().columnPinning;if(!o){var r,i;return!!((r=t.left)!=null&&r.length||(i=t.right)!=null&&i.length)}return!!((n=t[o])!=null&&n.length)},e.getLeftLeafColumns=C(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(o,n)=>(n??[]).map(t=>o.find(r=>r.id===t)).filter(Boolean),m(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=C(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(o,n)=>(n??[]).map(t=>o.find(r=>r.id===t)).filter(Boolean),m(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=C(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,n,t)=>{const r=[...n??[],...t??[]];return o.filter(i=>!r.includes(i.id))},m(e.options,"debugColumns","getCenterLeafColumns"))}},D={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},B=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),ze={getDefaultColumnDef:()=>D,getInitialState:e=>({columnSizing:{},columnSizingInfo:B(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:$("columnSizing",e),onColumnSizingInfoChange:$("columnSizingInfo",e)}),createColumn:(e,o)=>{e.getSize=()=>{var n,t,r;const i=o.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:D.minSize,(t=i??e.columnDef.size)!=null?t:D.size),(r=e.columnDef.maxSize)!=null?r:D.maxSize)},e.getStart=C(n=>[n,E(o,n),o.getState().columnSizing],(n,t)=>t.slice(0,e.getIndex(n)).reduce((r,i)=>r+i.getSize(),0),m(o.options,"debugColumns","getStart")),e.getAfter=C(n=>[n,E(o,n),o.getState().columnSizing],(n,t)=>t.slice(e.getIndex(n)+1).reduce((r,i)=>r+i.getSize(),0),m(o.options,"debugColumns","getAfter")),e.resetSize=()=>{o.setColumnSizing(n=>{let{[e.id]:t,...r}=n;return r})},e.getCanResize=()=>{var n,t;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((t=o.options.enableColumnResizing)!=null?t:!0)},e.getIsResizing=()=>o.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,o)=>{e.getSize=()=>{let n=0;const t=r=>{if(r.subHeaders.length)r.subHeaders.forEach(t);else{var i;n+=(i=r.column.getSize())!=null?i:0}};return t(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=n=>{const t=o.getColumn(e.column.id),r=t==null?void 0:t.getCanResize();return i=>{if(!t||!r||(i.persist==null||i.persist(),T(i)&&i.touches&&i.touches.length>1))return;const l=e.getSize(),u=e?e.getLeafHeaders().map(R=>[R.column.id,R.column.getSize()]):[[t.id,t.getSize()]],s=T(i)?Math.round(i.touches[0].clientX):i.clientX,a={},f=(R,F)=>{typeof F=="number"&&(o.setColumnSizingInfo(h=>{var V,I;const L=o.options.columnResizeDirection==="rtl"?-1:1,b=(F-((V=h==null?void 0:h.startOffset)!=null?V:0))*L,ee=Math.max(b/((I=h==null?void 0:h.startSize)!=null?I:0),-.999999);return h.columnSizingStart.forEach(pe=>{let[Se,ne]=pe;a[Se]=Math.round(Math.max(ne+ne*ee,0)*100)/100}),{...h,deltaOffset:b,deltaPercentage:ee}}),(o.options.columnResizeMode==="onChange"||R==="end")&&o.setColumnSizing(h=>({...h,...a})))},S=R=>f("move",R),d=R=>{f("end",R),o.setColumnSizingInfo(F=>({...F,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},g=n||typeof document<"u"?document:null,c={moveHandler:R=>S(R.clientX),upHandler:R=>{g==null||g.removeEventListener("mousemove",c.moveHandler),g==null||g.removeEventListener("mouseup",c.upHandler),d(R.clientX)}},p={moveHandler:R=>(R.cancelable&&(R.preventDefault(),R.stopPropagation()),S(R.touches[0].clientX),!1),upHandler:R=>{var F;g==null||g.removeEventListener("touchmove",p.moveHandler),g==null||g.removeEventListener("touchend",p.upHandler),R.cancelable&&(R.preventDefault(),R.stopPropagation()),d((F=R.touches[0])==null?void 0:F.clientX)}},w=Oe()?{passive:!1}:!1;T(i)?(g==null||g.addEventListener("touchmove",p.moveHandler,w),g==null||g.addEventListener("touchend",p.upHandler,w)):(g==null||g.addEventListener("mousemove",c.moveHandler,w),g==null||g.addEventListener("mouseup",c.upHandler,w)),o.setColumnSizingInfo(R=>({...R,startOffset:s,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:u,isResizingColumn:t.id}))}}},createTable:e=>{e.setColumnSizing=o=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(o),e.setColumnSizingInfo=o=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(o),e.resetColumnSizing=o=>{var n;e.setColumnSizing(o?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=o=>{var n;e.setColumnSizingInfo(o?B():(n=e.initialState.columnSizingInfo)!=null?n:B())},e.getTotalSize=()=>{var o,n;return(o=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((t,r)=>t+r.getSize(),0))!=null?o:0},e.getLeftTotalSize=()=>{var o,n;return(o=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((t,r)=>t+r.getSize(),0))!=null?o:0},e.getCenterTotalSize=()=>{var o,n;return(o=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((t,r)=>t+r.getSize(),0))!=null?o:0},e.getRightTotalSize=()=>{var o,n;return(o=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((t,r)=>t+r.getSize(),0))!=null?o:0}}};let G=null;function Oe(){if(typeof G=="boolean")return G;let e=!1;try{const o={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,o),window.removeEventListener("test",n)}catch{e=!1}return G=e,G}function T(e){return e.type==="touchstart"}const Be={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:$("columnVisibility",e)}),createColumn:(e,o)=>{e.toggleVisibility=n=>{e.getCanHide()&&o.setColumnVisibility(t=>({...t,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,t;const r=e.columns;return(n=r.length?r.some(i=>i.getIsVisible()):(t=o.getState().columnVisibility)==null?void 0:t[e.id])!=null?n:!0},e.getCanHide=()=>{var n,t;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((t=o.options.enableHiding)!=null?t:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,o)=>{e._getAllVisibleCells=C(()=>[e.getAllCells(),o.getState().columnVisibility],n=>n.filter(t=>t.column.getIsVisible()),m(o.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=C(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,t,r)=>[...n,...t,...r],m(o.options,"debugRows","getVisibleCells"))},createTable:e=>{const o=(n,t)=>C(()=>[t(),t().filter(r=>r.getIsVisible()).map(r=>r.id).join("_")],r=>r.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),m(e.options,"debugColumns",n));e.getVisibleFlatColumns=o("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=o("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=o("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=o("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=o("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var t;e.setColumnVisibility(n?{}:(t=e.initialState.columnVisibility)!=null?t:{})},e.toggleAllColumnsVisible=n=>{var t;n=(t=n)!=null?t:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((r,i)=>({...r,[i.id]:n||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var t;e.toggleAllColumnsVisible((t=n.target)==null?void 0:t.checked)}}};function E(e,o){return o?o==="center"?e.getCenterVisibleLeafColumns():o==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const Te={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},qe={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:$("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:o=>{var n;const t=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[o.id])==null?void 0:n.getValue();return typeof t=="string"||typeof t=="number"}}),createColumn:(e,o)=>{e.getCanGlobalFilter=()=>{var n,t,r,i;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((t=o.options.enableGlobalFilter)!=null?t:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&((i=o.options.getColumnCanGlobalFilter==null?void 0:o.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>M.includesString,e.getGlobalFilterFn=()=>{var o,n;const{globalFilterFn:t}=e.options;return A(t)?t:t==="auto"?e.getGlobalAutoFilterFn():(o=(n=e.options.filterFns)==null?void 0:n[t])!=null?o:M[t]},e.setGlobalFilter=o=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(o)},e.resetGlobalFilter=o=>{e.setGlobalFilter(o?void 0:e.initialState.globalFilter)}}},ke={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:$("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let o=!1,n=!1;e._autoResetExpanded=()=>{var t,r;if(!o){e._queue(()=>{o=!0});return}if((t=(r=e.options.autoResetAll)!=null?r:e.options.autoResetExpanded)!=null?t:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{t??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var r,i;e.setExpanded(t?{}:(r=(i=e.initialState)==null?void 0:i.expanded)!=null?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(t=>t.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{t.persist==null||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const t=e.getState().expanded;return t===!0||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{const t=e.getState().expanded;return typeof t=="boolean"?t===!0:!(!Object.keys(t).length||e.getRowModel().flatRows.some(r=>!r.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const l=i.split(".");t=Math.max(t,l.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,o)=>{e.toggleExpanded=n=>{o.setExpanded(t=>{var r;const i=t===!0?!0:!!(t!=null&&t[e.id]);let l={};if(t===!0?Object.keys(o.getRowModel().rowsById).forEach(u=>{l[u]=!0}):l=t,n=(r=n)!=null?r:!i,!i&&n)return{...l,[e.id]:!0};if(i&&!n){const{[e.id]:u,...s}=l;return s}return t})},e.getIsExpanded=()=>{var n;const t=o.getState().expanded;return!!((n=o.options.getIsRowExpanded==null?void 0:o.options.getIsRowExpanded(e))!=null?n:t===!0||t!=null&&t[e.id])},e.getCanExpand=()=>{var n,t,r;return(n=o.options.getRowCanExpand==null?void 0:o.options.getRowCanExpand(e))!=null?n:((t=o.options.enableExpanding)!=null?t:!0)&&!!((r=e.subRows)!=null&&r.length)},e.getIsAllParentsExpanded=()=>{let n=!0,t=e;for(;n&&t.parentId;)t=o.getRow(t.parentId,!0),n=t.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},U=0,X=10,q=()=>({pageIndex:U,pageSize:X}),je={getInitialState:e=>({...e,pagination:{...q(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:$("pagination",e)}),createTable:e=>{let o=!1,n=!1;e._autoResetPageIndex=()=>{var t,r;if(!o){e._queue(()=>{o=!0});return}if((t=(r=e.options.autoResetAll)!=null?r:e.options.autoResetPageIndex)!=null?t:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>{const r=i=>P(t,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(r)},e.resetPagination=t=>{var r;e.setPagination(t?q():(r=e.initialState.pagination)!=null?r:q())},e.setPageIndex=t=>{e.setPagination(r=>{let i=P(t,r.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,l)),{...r,pageIndex:i}})},e.resetPageIndex=t=>{var r,i;e.setPageIndex(t?U:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?r:U)},e.resetPageSize=t=>{var r,i;e.setPageSize(t?X:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?r:X)},e.setPageSize=t=>{e.setPagination(r=>{const i=Math.max(1,P(t,r.pageSize)),l=r.pageSize*r.pageIndex,u=Math.floor(l/i);return{...r,pageIndex:u,pageSize:i}})},e.setPageCount=t=>e.setPagination(r=>{var i;let l=P(t,(i=e.options.pageCount)!=null?i:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...r,pageCount:l}}),e.getPageOptions=C(()=>[e.getPageCount()],t=>{let r=[];return t&&t>0&&(r=[...new Array(t)].fill(null).map((i,l)=>l)),r},m(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:t}=e.getState().pagination,r=e.getPageCount();return r===-1?!0:r===0?!1:t<r-1},e.previousPage=()=>e.setPageIndex(t=>t-1),e.nextPage=()=>e.setPageIndex(t=>t+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var t;return(t=e.options.pageCount)!=null?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return(t=e.options.rowCount)!=null?t:e.getPrePaginationRowModel().rows.length}}},k=()=>({top:[],bottom:[]}),Ne={getInitialState:e=>({rowPinning:k(),...e}),getDefaultOptions:e=>({onRowPinningChange:$("rowPinning",e)}),createRow:(e,o)=>{e.pin=(n,t,r)=>{const i=t?e.getLeafRows().map(s=>{let{id:a}=s;return a}):[],l=r?e.getParentRows().map(s=>{let{id:a}=s;return a}):[],u=new Set([...l,e.id,...i]);o.setRowPinning(s=>{var a,f;if(n==="bottom"){var S,d;return{top:((S=s==null?void 0:s.top)!=null?S:[]).filter(p=>!(u!=null&&u.has(p))),bottom:[...((d=s==null?void 0:s.bottom)!=null?d:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)]}}if(n==="top"){var g,c;return{top:[...((g=s==null?void 0:s.top)!=null?g:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)],bottom:((c=s==null?void 0:s.bottom)!=null?c:[]).filter(p=>!(u!=null&&u.has(p)))}}return{top:((a=s==null?void 0:s.top)!=null?a:[]).filter(p=>!(u!=null&&u.has(p))),bottom:((f=s==null?void 0:s.bottom)!=null?f:[]).filter(p=>!(u!=null&&u.has(p)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:t,enablePinning:r}=o.options;return typeof t=="function"?t(e):(n=t??r)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:t,bottom:r}=o.getState().rowPinning,i=n.some(u=>t==null?void 0:t.includes(u)),l=n.some(u=>r==null?void 0:r.includes(u));return i?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var n,t;const r=e.getIsPinned();if(!r)return-1;const i=(n=r==="top"?o.getTopRows():o.getBottomRows())==null?void 0:n.map(l=>{let{id:u}=l;return u});return(t=i==null?void 0:i.indexOf(e.id))!=null?t:-1}},createTable:e=>{e.setRowPinning=o=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(o),e.resetRowPinning=o=>{var n,t;return e.setRowPinning(o?k():(n=(t=e.initialState)==null?void 0:t.rowPinning)!=null?n:k())},e.getIsSomeRowsPinned=o=>{var n;const t=e.getState().rowPinning;if(!o){var r,i;return!!((r=t.top)!=null&&r.length||(i=t.bottom)!=null&&i.length)}return!!((n=t[o])!=null&&n.length)},e._getPinnedRows=(o,n,t)=>{var r;return((r=e.options.keepPinnedRows)==null||r?(n??[]).map(l=>{const u=e.getRow(l,!0);return u.getIsAllParentsExpanded()?u:null}):(n??[]).map(l=>o.find(u=>u.id===l))).filter(Boolean).map(l=>({...l,position:t}))},e.getTopRows=C(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(o,n)=>e._getPinnedRows(o,n,"top"),m(e.options,"debugRows","getTopRows")),e.getBottomRows=C(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(o,n)=>e._getPinnedRows(o,n,"bottom"),m(e.options,"debugRows","getBottomRows")),e.getCenterRows=C(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(o,n,t)=>{const r=new Set([...n??[],...t??[]]);return o.filter(i=>!r.has(i.id))},m(e.options,"debugRows","getCenterRows"))}},Ue={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:$("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=o=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(o),e.resetRowSelection=o=>{var n;return e.setRowSelection(o?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=o=>{e.setRowSelection(n=>{o=typeof o<"u"?o:!e.getIsAllRowsSelected();const t={...n},r=e.getPreGroupedRowModel().flatRows;return o?r.forEach(i=>{i.getCanSelect()&&(t[i.id]=!0)}):r.forEach(i=>{delete t[i.id]}),t})},e.toggleAllPageRowsSelected=o=>e.setRowSelection(n=>{const t=typeof o<"u"?o:!e.getIsAllPageRowsSelected(),r={...n};return e.getRowModel().rows.forEach(i=>{K(r,i.id,t,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=C(()=>[e.getState().rowSelection,e.getCoreRowModel()],(o,n)=>Object.keys(o).length?j(e,n):{rows:[],flatRows:[],rowsById:{}},m(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=C(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(o,n)=>Object.keys(o).length?j(e,n):{rows:[],flatRows:[],rowsById:{}},m(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=C(()=>[e.getState().rowSelection,e.getSortedRowModel()],(o,n)=>Object.keys(o).length?j(e,n):{rows:[],flatRows:[],rowsById:{}},m(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{const o=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let t=!!(o.length&&Object.keys(n).length);return t&&o.some(r=>r.getCanSelect()&&!n[r.id])&&(t=!1),t},e.getIsAllPageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows.filter(r=>r.getCanSelect()),{rowSelection:n}=e.getState();let t=!!o.length;return t&&o.some(r=>!n[r.id])&&(t=!1),t},e.getIsSomeRowsSelected=()=>{var o;const n=Object.keys((o=e.getState().rowSelection)!=null?o:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:o.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>o=>{e.toggleAllRowsSelected(o.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>o=>{e.toggleAllPageRowsSelected(o.target.checked)}},createRow:(e,o)=>{e.toggleSelected=(n,t)=>{const r=e.getIsSelected();o.setRowSelection(i=>{var l;if(n=typeof n<"u"?n:!r,e.getCanSelect()&&r===n)return i;const u={...i};return K(u,e.id,n,(l=t==null?void 0:t.selectChildren)!=null?l:!0,o),u})},e.getIsSelected=()=>{const{rowSelection:n}=o.getState();return Y(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=o.getState();return J(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=o.getState();return J(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof o.options.enableRowSelection=="function"?o.options.enableRowSelection(e):(n=o.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof o.options.enableSubRowSelection=="function"?o.options.enableSubRowSelection(e):(n=o.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof o.options.enableMultiRowSelection=="function"?o.options.enableMultiRowSelection(e):(n=o.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return t=>{var r;n&&e.toggleSelected((r=t.target)==null?void 0:r.checked)}}}},K=(e,o,n,t,r)=>{var i;const l=r.getRow(o,!0);n?(l.getCanMultiSelect()||Object.keys(e).forEach(u=>delete e[u]),l.getCanSelect()&&(e[o]=!0)):delete e[o],t&&(i=l.subRows)!=null&&i.length&&l.getCanSelectSubRows()&&l.subRows.forEach(u=>K(e,u.id,n,t,r))};function j(e,o){const n=e.getState().rowSelection,t=[],r={},i=function(l,u){return l.map(s=>{var a;const f=Y(s,n);if(f&&(t.push(s),r[s.id]=s),(a=s.subRows)!=null&&a.length&&(s={...s,subRows:i(s.subRows)}),f)return s}).filter(Boolean)};return{rows:i(o.rows),flatRows:t,rowsById:r}}function Y(e,o){var n;return(n=o[e.id])!=null?n:!1}function J(e,o,n){var t;if(!((t=e.subRows)!=null&&t.length))return!1;let r=!0,i=!1;return e.subRows.forEach(l=>{if(!(i&&!r)&&(l.getCanSelect()&&(Y(l,o)?i=!0:r=!1),l.subRows&&l.subRows.length)){const u=J(l,o);u==="all"?i=!0:(u==="some"&&(i=!0),r=!1)}}),r?"all":i?"some":!1}const Q=/([0-9]+)/gm,Xe=(e,o,n)=>fe(x(e.getValue(n)).toLowerCase(),x(o.getValue(n)).toLowerCase()),Ke=(e,o,n)=>fe(x(e.getValue(n)),x(o.getValue(n))),Je=(e,o,n)=>Z(x(e.getValue(n)).toLowerCase(),x(o.getValue(n)).toLowerCase()),Qe=(e,o,n)=>Z(x(e.getValue(n)),x(o.getValue(n))),We=(e,o,n)=>{const t=e.getValue(n),r=o.getValue(n);return t>r?1:t<r?-1:0},Ye=(e,o,n)=>Z(e.getValue(n),o.getValue(n));function Z(e,o){return e===o?0:e>o?1:-1}function x(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function fe(e,o){const n=e.split(Q).filter(Boolean),t=o.split(Q).filter(Boolean);for(;n.length&&t.length;){const r=n.shift(),i=t.shift(),l=parseInt(r,10),u=parseInt(i,10),s=[l,u].sort();if(isNaN(s[0])){if(r>i)return 1;if(i>r)return-1;continue}if(isNaN(s[1]))return isNaN(l)?-1:1;if(l>u)return 1;if(u>l)return-1}return n.length-t.length}const y={alphanumeric:Xe,alphanumericCaseSensitive:Ke,text:Je,textCaseSensitive:Qe,datetime:We,basic:Ye},Ze={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:$("sorting",e),isMultiSortEvent:o=>o.shiftKey}),createColumn:(e,o)=>{e.getAutoSortingFn=()=>{const n=o.getFilteredRowModel().flatRows.slice(10);let t=!1;for(const r of n){const i=r==null?void 0:r.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return y.datetime;if(typeof i=="string"&&(t=!0,i.split(Q).length>1))return y.alphanumeric}return t?y.text:y.basic},e.getAutoSortDir=()=>{const n=o.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,t;if(!e)throw new Error;return A(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(t=o.options.sortingFns)==null?void 0:t[e.columnDef.sortingFn])!=null?n:y[e.columnDef.sortingFn]},e.toggleSorting=(n,t)=>{const r=e.getNextSortingOrder(),i=typeof n<"u"&&n!==null;o.setSorting(l=>{const u=l==null?void 0:l.find(g=>g.id===e.id),s=l==null?void 0:l.findIndex(g=>g.id===e.id);let a=[],f,S=i?n:r==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&t?u?f="toggle":f="add":l!=null&&l.length&&s!==l.length-1?f="replace":u?f="toggle":f="replace",f==="toggle"&&(i||r||(f="remove")),f==="add"){var d;a=[...l,{id:e.id,desc:S}],a.splice(0,a.length-((d=o.options.maxMultiSortColCount)!=null?d:Number.MAX_SAFE_INTEGER))}else f==="toggle"?a=l.map(g=>g.id===e.id?{...g,desc:S}:g):f==="remove"?a=l.filter(g=>g.id!==e.id):a=[{id:e.id,desc:S}];return a})},e.getFirstSortDir=()=>{var n,t;return((n=(t=e.columnDef.sortDescFirst)!=null?t:o.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var t,r;const i=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==i&&((t=o.options.enableSortingRemoval)==null||t)&&(!(n&&(r=o.options.enableMultiRemove)!=null)||r)?!1:l==="desc"?"asc":"desc":i},e.getCanSort=()=>{var n,t;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((t=o.options.enableSorting)!=null?t:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,t;return(n=(t=e.columnDef.enableMultiSort)!=null?t:o.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const t=(n=o.getState().sorting)==null?void 0:n.find(r=>r.id===e.id);return t?t.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,t;return(n=(t=o.getState().sorting)==null?void 0:t.findIndex(r=>r.id===e.id))!=null?n:-1},e.clearSorting=()=>{o.setSorting(n=>n!=null&&n.length?n.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return t=>{n&&(t.persist==null||t.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?o.options.isMultiSortEvent==null?void 0:o.options.isMultiSortEvent(t):!1))}}},createTable:e=>{e.setSorting=o=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(o),e.resetSorting=o=>{var n,t;e.setSorting(o?[]:(n=(t=e.initialState)==null?void 0:t.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},be=[he,Be,Ae,Le,Fe,$e,Te,qe,Ze,De,ke,je,Ne,Ue,ze];function en(e){var o,n;(e.debugAll||e.debugTable)&&console.info("Creating Table Instance...");const t=[...be,...(o=e._features)!=null?o:[]];let r={_features:t};const i=r._features.reduce((d,g)=>Object.assign(d,g.getDefaultOptions==null?void 0:g.getDefaultOptions(r)),{}),l=d=>r.options.mergeOptions?r.options.mergeOptions(i,d):{...i,...d};let s={...{},...(n=e.initialState)!=null?n:{}};r._features.forEach(d=>{var g;s=(g=d.getInitialState==null?void 0:d.getInitialState(s))!=null?g:s});const a=[];let f=!1;const S={_features:t,options:{...i,...e},initialState:s,_queue:d=>{a.push(d),f||(f=!0,Promise.resolve().then(()=>{for(;a.length;)a.shift()();f=!1}).catch(g=>setTimeout(()=>{throw g})))},reset:()=>{r.setState(r.initialState)},setOptions:d=>{const g=P(d,r.options);r.options=l(g)},getState:()=>r.options.state,setState:d=>{r.options.onStateChange==null||r.options.onStateChange(d)},_getRowId:(d,g,c)=>{var p;return(p=r.options.getRowId==null?void 0:r.options.getRowId(d,g,c))!=null?p:`${c?[c.id,g].join("."):g}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(d,g)=>{let c=(g?r.getPrePaginationRowModel():r.getRowModel()).rowsById[d];if(!c&&(c=r.getCoreRowModel().rowsById[d],!c))throw new Error(`getRow could not find row with ID: ${d}`);return c},_getDefaultColumnDef:C(()=>[r.options.defaultColumn],d=>{var g;return d=(g=d)!=null?g:{},{header:c=>{const p=c.header.column.columnDef;return p.accessorKey?p.accessorKey:p.accessorFn?p.id:null},cell:c=>{var p,w;return(p=(w=c.renderValue())==null||w.toString==null?void 0:w.toString())!=null?p:null},...r._features.reduce((c,p)=>Object.assign(c,p.getDefaultColumnDef==null?void 0:p.getDefaultColumnDef()),{}),...d}},m(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>r.options.columns,getAllColumns:C(()=>[r._getColumnDefs()],d=>{const g=function(c,p,w){return w===void 0&&(w=0),c.map(R=>{const F=we(r,R,w,p),h=R;return F.columns=h.columns?g(h.columns,F,w+1):[],F})};return g(d)},m(e,"debugColumns","getAllColumns")),getAllFlatColumns:C(()=>[r.getAllColumns()],d=>d.flatMap(g=>g.getFlatColumns()),m(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:C(()=>[r.getAllFlatColumns()],d=>d.reduce((g,c)=>(g[c.id]=c,g),{}),m(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:C(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(d,g)=>{let c=d.flatMap(p=>p.getLeafColumns());return g(c)},m(e,"debugColumns","getAllLeafColumns")),getColumn:d=>{const g=r._getAllFlatColumnsById()[d];return g||console.error(`[Table] Column with id '${d}' does not exist.`),g}};Object.assign(r,S);for(let d=0;d<r._features.length;d++){const g=r._features[d];g==null||g.createTable==null||g.createTable(r)}return r}function un(){return e=>C(()=>[e.options.data],o=>{const n={rows:[],flatRows:[],rowsById:{}},t=function(r,i,l){i===void 0&&(i=0);const u=[];for(let a=0;a<r.length;a++){const f=_e(e,e._getRowId(r[a],a,l),r[a],a,i,void 0,l==null?void 0:l.id);if(n.flatRows.push(f),n.rowsById[f.id]=f,u.push(f),e.options.getSubRows){var s;f.originalSubRows=e.options.getSubRows(r[a],a),(s=f.originalSubRows)!=null&&s.length&&(f.subRows=t(f.originalSubRows,i+1,f))}}return u};return n.rows=t(o),n},m(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function sn(){return e=>C(()=>[e.getState().expanded,e.getPreExpandedRowModel(),e.options.paginateExpandedRows],(o,n,t)=>!n.rows.length||o!==!0&&!Object.keys(o??{}).length||!t?n:ce(n),m(e.options,"debugTable","getExpandedRowModel"))}function ce(e){const o=[],n=t=>{var r;o.push(t),(r=t.subRows)!=null&&r.length&&t.getIsExpanded()&&t.subRows.forEach(n)};return e.rows.forEach(n),{rows:o,flatRows:e.flatRows,rowsById:e.rowsById}}function gn(e){return o=>C(()=>[o.getState().pagination,o.getPrePaginationRowModel(),o.options.paginateExpandedRows?void 0:o.getState().expanded],(n,t)=>{if(!t.rows.length)return t;const{pageSize:r,pageIndex:i}=n;let{rows:l,flatRows:u,rowsById:s}=t;const a=r*i,f=a+r;l=l.slice(a,f);let S;o.options.paginateExpandedRows?S={rows:l,flatRows:u,rowsById:s}:S=ce({rows:l,flatRows:u,rowsById:s}),S.flatRows=[];const d=g=>{S.flatRows.push(g),g.subRows.length&&g.subRows.forEach(d)};return S.rows.forEach(d),S},m(o.options,"debugTable","getPaginationRowModel"))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function an(e,o){return e?nn(e)?N.createElement(e,o):e:null}function nn(e){return tn(e)||typeof e=="function"||on(e)}function tn(e){return typeof e=="function"&&(()=>{const o=Object.getPrototypeOf(e);return o.prototype&&o.prototype.isReactComponent})()}function on(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function dn(e){const o={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=N.useState(()=>({current:en(o)})),[t,r]=N.useState(()=>n.current.initialState);return n.current.setOptions(i=>({...i,...e,state:{...t,...e.state},onStateChange:l=>{r(l),e.onStateChange==null||e.onStateChange(l)}})),n.current}export{gn as a,sn as b,ln as c,an as f,un as g,dn as u};
