import{D as _}from"./chunk-7I5DQGWY-CwOWioty.js";import{u as N}from"./chunk-6CNRNROJ-vBT9ZY0G.js";import{C as w}from"./chunk-3LLQ6F7F-DvOxFHHw.js";import{a2 as C,a4 as p,b,R as y,aS as P,j as e,H as M,a8 as W,a9 as v,d4 as O,t as j,w as r,x as F,B as g,s as S}from"./index-Bwql5Dzz.js";import{K as T}from"./chunk-6HTZNHPT-N4svn6ad.js";import{b as n,u as D}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./x-mark-mini-DvSTI7zK.js";import"./triangles-mini-DPBC_td5.js";import"./plus-mini-C5sDHkH8.js";import"./prompt-BsR9zKsn.js";function E(){return e.jsxs("svg",{width:"200",height:"128",viewBox:"0 0 200 128",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.00428286",y:"-0.742904",width:"33.5",height:"65.5",rx:"6.75",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 149.756 60.938)",className:"stroke-ui-fg-subtle fill-ui-bg-base",strokeWidth:"1.5"}),e.jsx("rect",{x:"0.00428286",y:"-0.742904",width:"33.5",height:"65.5",rx:"6.75",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 149.756 57.9383)",className:"stroke-ui-fg-subtle fill-ui-bg-base",strokeWidth:"1.5"}),e.jsxs("g",{clipPath:"url(#clip0_20787_38934)",children:[e.jsx("path",{d:"M140.579 79.6421L139.126 80.4592",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{opacity:"0.88",d:"M142.305 82.046L140.257 82.0342",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{opacity:"0.75",d:"M140.552 84.4297L139.108 83.5959",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{opacity:"0.63",d:"M136.347 85.3975L136.354 84.23",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{opacity:"0.5",d:"M132.154 84.3813L133.606 83.5642",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{opacity:"0.38",d:"M130.428 81.9775L132.476 81.9893",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{opacity:"0.25",d:"M132.181 79.5938L133.625 80.4275",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{opacity:"0.13",d:"M136.386 78.626L136.379 79.7935",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),e.jsx("rect",{width:"12",height:"3",rx:"1.5",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 156.447 64.7927)",className:"fill-ui-fg-muted"}),e.jsx("rect",{x:"0.00428286",y:"-0.742904",width:"33.5",height:"65.5",rx:"6.75",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 77.0232 18.9148)",className:"stroke-ui-fg-subtle fill-ui-fg-muted",strokeWidth:"1.5"}),e.jsx("rect",{x:"0.00428286",y:"-0.742904",width:"33.5",height:"65.5",rx:"6.75",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 77.0232 15.9148)",className:"stroke-ui-fg-subtle fill-ui-bg-base",strokeWidth:"1.5"}),e.jsx("rect",{width:"12",height:"3",rx:"1.5",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 83.7141 22.7693)",className:"fill-ui-fg-muted"}),e.jsx("rect",{width:"17",height:"3",rx:"1.5",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 57.5554 39.458)",className:"fill-ui-fg-muted"}),e.jsx("rect",{width:"12",height:"3",rx:"1.5",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 53.1975 41.9094)",className:"fill-ui-fg-muted"}),e.jsx("g",{clipPath:"url(#clip1_20787_38934)",children:e.jsx("path",{d:"M52.3603 36.4564C50.9277 35.6287 48.59 35.6152 47.148 36.4264C45.7059 37.2375 45.6983 38.5703 47.1308 39.398C48.5634 40.2257 50.9011 40.2392 52.3432 39.428C53.7852 38.6169 53.7929 37.2841 52.3603 36.4564ZM48.4382 38.6626C47.7221 38.2488 47.726 37.5822 48.4468 37.1768C49.1676 36.7713 50.3369 36.7781 51.0529 37.1918C51.769 37.6055 51.7652 38.2722 51.0444 38.6776C50.3236 39.083 49.1543 39.0763 48.4382 38.6626Z",className:"fill-ui-fg-subtle"})}),e.jsx("rect",{width:"17",height:"3",rx:"1.5",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 69.7573 32.5945)",className:"fill-ui-fg-muted"}),e.jsx("rect",{width:"12",height:"3",rx:"1.5",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 65.3994 35.0459)",className:"fill-ui-fg-muted"}),e.jsx("g",{clipPath:"url(#clip2_20787_38934)",children:e.jsx("path",{d:"M64.5622 29.5929C63.1296 28.7652 60.7919 28.7517 59.3499 29.5628C57.9079 30.374 57.9002 31.7067 59.3327 32.5344C60.7653 33.3622 63.103 33.3756 64.5451 32.5645C65.9871 31.7534 65.9948 30.4206 64.5622 29.5929ZM63.8581 31.3974L60.8148 31.6267C60.6827 31.6368 60.5495 31.6135 60.4486 31.5632C60.4399 31.5587 60.4321 31.5547 60.4244 31.5502C60.3386 31.5006 60.2899 31.4337 60.2903 31.3639L60.2933 30.6203C60.2937 30.4754 60.5012 30.3587 60.7557 30.3602C61.0102 30.3616 61.2163 30.4802 61.2155 30.6258L61.2138 31.0671L63.7317 30.8771C63.9833 30.858 64.2168 30.9586 64.2512 31.1032C64.286 31.247 64.1101 31.379 63.8581 31.3978L63.8581 31.3974Z",className:"fill-ui-fg-subtle"})}),e.jsx("g",{clipPath:"url(#clip3_20787_38934)",children:e.jsx("path",{d:"M93.106 54.3022L100.49 54.3448L100.514 50.135",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("g",{clipPath:"url(#clip4_20787_38934)",children:e.jsx("path",{d:"M103.496 60.3056L110.881 60.3482L110.905 56.1384",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("g",{clipPath:"url(#clip5_20787_38934)",children:e.jsx("path",{d:"M113.887 66.3088L121.271 66.3514L121.295 62.1416",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("g",{clipPath:"url(#clip6_20787_38934)",children:e.jsx("path",{d:"M86.1135 61.6911L78.7294 61.6486L78.7051 65.8583",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("g",{clipPath:"url(#clip7_20787_38934)",children:e.jsx("path",{d:"M96.5039 67.6945L89.1198 67.652L89.0955 71.8618",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("g",{clipPath:"url(#clip8_20787_38934)",children:e.jsx("path",{d:"M106.894 73.6977L99.5102 73.6551L99.4859 77.8649",className:"stroke-ui-fg-subtle",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsxs("defs",{children:[e.jsx("clipPath",{id:"clip0_20787_38934",children:e.jsx("rect",{width:"12",height:"12",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 136.401 76.0686)"})}),e.jsx("clipPath",{id:"clip1_20787_38934",children:e.jsx("rect",{width:"6",height:"6",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 49.7627 34.9556)"})}),e.jsx("clipPath",{id:"clip2_20787_38934",children:e.jsx("rect",{width:"6",height:"6",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 61.9646 28.092)"})}),e.jsx("clipPath",{id:"clip3_20787_38934",children:e.jsx("rect",{width:"12",height:"12",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 98.3596 47.1509)"})}),e.jsx("clipPath",{id:"clip4_20787_38934",children:e.jsx("rect",{width:"12",height:"12",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 108.75 53.1543)"})}),e.jsx("clipPath",{id:"clip5_20787_38934",children:e.jsx("rect",{width:"12",height:"12",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 119.14 59.1575)"})}),e.jsx("clipPath",{id:"clip6_20787_38934",children:e.jsx("rect",{width:"12",height:"12",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 80.9282 56.9561)"})}),e.jsx("clipPath",{id:"clip7_20787_38934",children:e.jsx("rect",{width:"12",height:"12",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 91.3186 62.9595)"})}),e.jsx("clipPath",{id:"clip8_20787_38934",children:e.jsx("rect",{width:"12",height:"12",className:"fill-ui-bg-base",transform:"matrix(0.865865 0.500278 -0.871576 0.490261 101.709 68.9626)"})})]})]})}var $=C({customer_id:p().min(1),current_customer_details:p().min(1)});function R({order:t}){var u,h,m,x,f;const{t:s}=b(),{handleSuccess:c}=D(),a=W({defaultValues:{customer_id:"",current_customer_details:(u=t.customer)!=null&&u.first_name?`${(h=t.customer)==null?void 0:h.first_name} ${(m=t.customer)==null?void 0:m.last_name} (${(x=t.customer)==null?void 0:x.email}) `:(f=t.customer)==null?void 0:f.email},resolver:v($)}),l=N({queryKey:["customers"],queryFn:i=>S.admin.customer.list({...i,has_account:!0}),getOptions:i=>i.customers.map(o=>({label:`${o.first_name||""} ${o.last_name||""} (${o.email})`,value:o.id}))}),{mutateAsync:d,isPending:k}=O(t.id),L=a.handleSubmit(async i=>{try{await d({customer_id:i.customer_id}),j.success(s("orders.transfer.requestSuccess",{email:t.email})),c()}catch(o){j.error(o.message)}});return e.jsx(n.Form,{form:a,children:e.jsxs(T,{onSubmit:L,className:"flex size-full flex-col overflow-hidden",children:[e.jsx(n.Body,{className:"flex-1 overflow-auto",children:e.jsxs("div",{className:"flex flex-col gap-y-8",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx(E,{})}),e.jsx(r.Field,{control:a.control,name:"current_customer_details",render:({field:i})=>e.jsxs(r.Item,{children:[e.jsx(r.Label,{children:s("orders.transfer.currentOwner")}),e.jsx("span",{className:"txt-small text-ui-fg-muted",children:s("orders.transfer.currentOwnerDescription")}),e.jsx(r.Control,{children:e.jsx(F,{type:"email",...i,disabled:!0})}),e.jsx(r.ErrorMessage,{})]})}),e.jsx(r.Field,{control:a.control,name:"customer_id",render:({field:i})=>e.jsxs(r.Item,{children:[e.jsx(r.Label,{children:s("orders.transfer.newOwner")}),e.jsx("span",{className:"txt-small text-ui-fg-muted",children:s("orders.transfer.newOwnerDescription")}),e.jsx(r.Control,{children:e.jsx(w,{...i,options:l.options,searchValue:l.searchValue,onSearchValueChange:l.onSearchValueChange,fetchNextPage:l.fetchNextPage,className:"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover",placeholder:s("actions.select")})}),e.jsx(r.ErrorMessage,{})]})})]})}),e.jsx(n.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(n.Close,{asChild:!0,children:e.jsx(g,{variant:"secondary",size:"small",children:s("actions.cancel")})}),e.jsx(g,{isLoading:k,type:"submit",variant:"primary",size:"small",disabled:!!Object.keys(a.formState.errors||{}).length,children:s("actions.save")})]})})]})})}var G=()=>{const{t}=b(),s=y(),c=s.order_id||s.id,{order:a,isPending:l,isError:d}=P(c,{fields:_});if(!l&&d)throw new Error("Order not found");return e.jsxs(n,{children:[e.jsx(n.Header,{children:e.jsx(M,{children:t("orders.transfer.title")})}),a&&e.jsx(R,{order:a})]})};export{G as Component};
