import{u as te,a as ie,b as ae}from"./chunk-NEZX6265-Dnnail08.js";import{r as x,q as Q,d as ne,g as oe,a as ce,j as e,dv as le,eb as de,ec as ue,k as me,l as pe,ed as Y,b,H as K,A as E,W as ge,e8 as xe,h as he,a_ as fe,T as l,Y as y,L as P,t as f}from"./index-Bwql5Dzz.js";import{S as _e}from"./chunk-ADOCJB6L-fVr5Yqi0.js";import{u as je,_ as ve}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as ye}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as $}from"./use-prompt-pbDx0Sfe.js";import{P as Ce}from"./pencil-square-6wRbnn1C.js";import{X as be}from"./x-circle-CKMdlKvN.js";import{T as Se}from"./trash-BBylvTAG.js";import{C as G}from"./container-Dqi2woPF.js";import{C as j}from"./command-bar-Cyd2ymXA.js";import{C as B}from"./checkbox-B4pL6X49.js";import{c as we}from"./index-BxZ1678G.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./index-DP5bcQyU.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var Pe=Object.defineProperty,C=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable,H=(s,r,t)=>r in s?Pe(s,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[r]=t,ke=(s,r)=>{for(var t in r)J.call(r,t)&&H(s,t,r[t]);if(C)for(var t of C(r))U.call(r,t)&&H(s,t,r[t]);return s},Te=(s,r)=>{var t={};for(var i in s)J.call(s,i)&&r.indexOf(i)<0&&(t[i]=s[i]);if(s!=null&&C)for(var i of C(s))r.indexOf(i)<0&&U.call(s,i)&&(t[i]=s[i]);return t};const V=x.forwardRef((s,r)=>{var t=s,{color:i="currentColor"}=t,o=Te(t,["color"]);return x.createElement("svg",ke({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:r},o),x.createElement("path",{stroke:i,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M7.5 13.945a6.444 6.444 0 1 0 0-12.89 6.444 6.444 0 0 0 0 12.89"}),x.createElement("path",{stroke:i,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"m4.611 7.722 2 2.223 3.778-4.89"}))});V.displayName="CheckCircle";var Ee=()=>({queryKey:de.details(),queryFn:async()=>ue()}),hs=async()=>{const s=Ee();return Q.getQueryData(s.queryKey)??await Q.fetchQuery(s)},k=10,ze=({store:s})=>{var I,O,R,D,L,A,q,M;const[r,t]=x.useState({}),{searchParams:i,raw:o}=te({pageSize:k}),{currencies:c,count:a,isPending:m,isError:h,error:_}=ie({code:(I=s.supported_currencies)==null?void 0:I.map(n=>n.currency_code),...i},{placeholderData:me,enabled:!!((O=s.supported_currencies)!=null&&O.length)}),{price_preferences:g,isPending:v,isError:p,error:u}=pe({attribute:"currency_code",value:(R=s.supported_currencies)==null?void 0:R.map(n=>n.currency_code)},{enabled:!!((D=s.supported_currencies)!=null&&D.length)}),z=Ie(),N=x.useMemo(()=>new Map(g==null?void 0:g.map(n=>[n.value,n])),[g]),X=c==null?void 0:c.map(n=>{var S;return{...n,is_tax_inclusive:(S=N.get(n.code))==null?void 0:S.is_tax_inclusive}}),{table:F}=je({data:X??[],columns:z,count:a,getRowId:n=>n.code,rowSelection:{state:r,updater:t},enablePagination:!0,enableRowSelection:!0,pageSize:k,meta:{storeId:s.id,supportedCurrencies:s.supported_currencies,defaultCurrencyCode:(A=(L=s.supported_currencies)==null?void 0:L.find(n=>n.is_default))==null?void 0:A.currency_code,preferencesMap:N}}),{mutateAsync:Z}=Y(s.id),{t:d}=b(),ee=$(),se=async()=>{var W;const n=Object.keys(r);await ee({title:d("general.areYouSure"),description:d("store.removeCurrencyWarning",{count:n.length}),confirmText:d("actions.remove"),cancelText:d("actions.cancel")})&&await Z({supported_currencies:((W=s.supported_currencies)==null?void 0:W.filter(w=>!n.includes(w.currency_code)))??[]},{onSuccess:()=>{t({}),f.success(d("store.toast.currenciesRemoved"))},onError:w=>{f.error(w.message)}})};if(h)throw _;if(p)throw u;const re=m||v;return e.jsxs(G,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(K,{level:"h2",children:d("store.currencies")}),e.jsx(E,{groups:[{actions:[{icon:e.jsx(ge,{}),label:d("actions.add"),to:"currencies"}]}]})]}),e.jsx(ve,{orderBy:[{key:"name",label:d("fields.name")},{key:"code",label:d("fields.code")}],search:!0,pagination:!0,table:F,pageSize:k,columns:z,count:(q=s.supported_currencies)!=null&&q.length?a:0,isLoading:(M=s.supported_currencies)!=null&&M.length?re:!1,queryObject:o}),e.jsx(j,{open:!!Object.keys(r).length,children:e.jsxs(j.Bar,{children:[e.jsx(j.Value,{children:d("general.countSelected",{count:Object.keys(r).length})}),e.jsx(j.Seperator,{}),e.jsx(j.Command,{action:se,shortcut:"r",label:d("actions.remove")})]})})]})},Ne=({storeId:s,currency:r,supportedCurrencies:t,defaultCurrencyCode:i,preferencesMap:o})=>{var g,v;const{mutateAsync:c}=Y(s),{t:a}=b(),m=$(),h=async()=>{await m({title:a("general.areYouSure"),description:a("store.removeCurrencyWarning",{count:1}),verificationInstruction:a("general.typeToConfirm"),verificationText:r.name,confirmText:a("actions.remove"),cancelText:a("actions.cancel")})&&await c({supported_currencies:t.filter(u=>u.currency_code!==r.code)},{onSuccess:()=>{f.success(a("store.toast.currenciesRemoved"))},onError:u=>{f.error(u.message)}})},_=async()=>{await c({supported_currencies:t.map(p=>{const u=o.get(p.currency_code);return{...p,is_tax_inclusive:p.currency_code===r.code?!(u!=null&&u.is_tax_inclusive):void 0}})},{onSuccess:()=>{f.success(a("store.toast.updatedTaxInclusivitySuccessfully"))},onError:p=>{f.error(p.message)}})};return e.jsx(E,{groups:[{actions:[{icon:(g=o.get(r.code))!=null&&g.is_tax_inclusive?e.jsx(be,{}):e.jsx(V,{}),label:(v=o.get(r.code))!=null&&v.is_tax_inclusive?a("store.disableTaxInclusivePricing"):a("store.enableTaxInclusivePricing"),onClick:_}]},{actions:[{icon:e.jsx(Se,{}),label:a("actions.remove"),onClick:h,disabled:r.code===i}]}]})},T=we(),Ie=()=>{const s=ae(),{t:r}=b();return x.useMemo(()=>[T.display({id:"select",header:({table:t})=>e.jsx(B,{checked:t.getIsSomePageRowsSelected()?"indeterminate":t.getIsAllPageRowsSelected(),onCheckedChange:i=>t.toggleAllPageRowsSelected(!!i)}),cell:({row:t})=>e.jsx(B,{checked:t.getIsSelected(),onCheckedChange:i=>t.toggleSelected(!!i),onClick:i=>{i.stopPropagation()}})}),...s,T.accessor("is_tax_inclusive",{header:r("fields.taxInclusivePricing"),cell:({getValue:t})=>{const i=t();return e.jsx(_e,{color:i?"green":"grey",children:r(i?"fields.true":"fields.false")})}}),T.display({id:"actions",cell:({row:t,table:i})=>{const{supportedCurrencies:o,storeId:c,defaultCurrencyCode:a,preferencesMap:m}=i.options.meta;return e.jsx(Ne,{storeId:c,currency:t.original,supportedCurrencies:o,defaultCurrencyCode:a,preferencesMap:m})}})],[s,r])},Oe=({store:s})=>{var a,m,h;const{t:r}=b(),{region:t}=xe(s.default_region_id,void 0,{enabled:!!s.default_region_id}),i=(a=s.supported_currencies)==null?void 0:a.find(_=>_.is_default),{sales_channel:o}=he(s.default_sales_channel_id,{enabled:!!s.default_sales_channel_id}),{stock_location:c}=fe(s.default_location_id,{fields:"id,name"},{enabled:!!s.default_location_id});return e.jsxs(G,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsxs("div",{children:[e.jsx(K,{children:r("store.domain")}),e.jsx(l,{className:"text-ui-fg-subtle",size:"small",children:r("store.manageYourStoresDetails")})]}),e.jsx(E,{groups:[{actions:[{icon:e.jsx(Ce,{}),label:r("actions.edit"),to:"edit"}]}]})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 px-6 py-4",children:[e.jsx(l,{size:"small",leading:"compact",weight:"plus",children:r("fields.name")}),e.jsx(l,{size:"small",leading:"compact",children:s.name})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 px-6 py-4",children:[e.jsx(l,{size:"small",leading:"compact",weight:"plus",children:r("store.defaultCurrency")}),i?e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx(y,{size:"2xsmall",children:(m=i.currency_code)==null?void 0:m.toUpperCase()}),e.jsx(l,{size:"small",leading:"compact",children:(h=i.currency)==null?void 0:h.name})]}):e.jsx(l,{size:"small",leading:"compact",children:"-"})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 px-6 py-4",children:[e.jsx(l,{size:"small",leading:"compact",weight:"plus",children:r("store.defaultRegion")}),e.jsx("div",{className:"flex items-center gap-x-2",children:t?e.jsx(y,{size:"2xsmall",asChild:!0,children:e.jsx(P,{to:`/settings/regions/${t.id}`,children:t.name})}):e.jsx(l,{size:"small",leading:"compact",children:"-"})})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 px-6 py-4",children:[e.jsx(l,{size:"small",leading:"compact",weight:"plus",children:r("store.defaultSalesChannel")}),e.jsx("div",{className:"flex items-center gap-x-2",children:o?e.jsx(y,{size:"2xsmall",asChild:!0,children:e.jsx(P,{to:`/settings/sales-channels/${o.id}`,children:o.name})}):e.jsx(l,{size:"small",leading:"compact",children:"-"})})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 px-6 py-4",children:[e.jsx(l,{size:"small",leading:"compact",weight:"plus",children:r("store.defaultLocation")}),e.jsx("div",{className:"flex items-center gap-x-2",children:c?e.jsx(y,{size:"2xsmall",asChild:!0,children:e.jsx(P,{to:`/settings/locations/${c.id}`,children:c.name})}):e.jsx(l,{size:"small",leading:"compact",children:"-"})})]})]})},fs=()=>{const s=ne(),{store:r,isPending:t,isError:i,error:o}=oe(void 0,{initialData:s}),{getWidgets:c}=ce();if(t||!r)return e.jsx(le,{sections:2,showJSON:!0,showMetadata:!0});if(i)throw o;return e.jsxs(ye,{widgets:{before:c("store.details.before"),after:c("store.details.after")},data:r,hasOutlet:!0,showMetadata:!0,showJSON:!0,children:[e.jsx(Oe,{store:r}),e.jsx(ze,{store:r})]})};export{fs as Component,hs as loader};
