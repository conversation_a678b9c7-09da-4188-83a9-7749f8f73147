{"version": 3, "file": "components-Banner-__stories__-banner-flash-sale-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACvDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AC3CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACzBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AChBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAlBA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/hBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;;AACA;AAEA;AAAA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAGA;AAGA;AAGA;AACA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;AACA;AAAA;AAAA;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AAEA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AACA;AAAA;AAAA;;;;;AACA;AAAA;AACA;AAAA;AAAA;AAEA;AAAA;;;;;AAAA;;;;;;;;;;;;;;;;;;;;AAQA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;;;;;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAAA;;;AAnGA;AAqGA;AAAA", "sources": ["webpack://shadcn-timeline/./node_modules/class-variance-authority/dist/index.mjs", "webpack://shadcn-timeline/./node_modules/lucide-react/dist/esm/Icon.js", "webpack://shadcn-timeline/./node_modules/lucide-react/dist/esm/createLucideIcon.js", "webpack://shadcn-timeline/./node_modules/lucide-react/dist/esm/defaultAttributes.js", "webpack://shadcn-timeline/./node_modules/lucide-react/dist/esm/icons/clock.js", "webpack://shadcn-timeline/./node_modules/lucide-react/dist/esm/shared/src/utils.js", "webpack://shadcn-timeline/./src/components/Banner/__fixtures__/banner-flash-sale.fixtures.tsx", "webpack://shadcn-timeline/./src/components/Banner/__stories__/banner-flash-sale.stories.tsx", "webpack://shadcn-timeline/./src/components/Banner/banner-flash-sale.tsx"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n", "/**\n * @license lucide-react v0.483.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nimport { mergeClasses } from './shared/src/utils.js';\n\nconst Icon = forwardRef(\n  ({\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth,\n    className = \"\",\n    children,\n    iconNode,\n    ...rest\n  }, ref) => {\n    return createElement(\n      \"svg\",\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: mergeClasses(\"lucide\", className),\n        ...rest\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...Array.isArray(children) ? children : [children]\n      ]\n    );\n  }\n);\n\nexport { Icon as default };\n//# sourceMappingURL=Icon.js.map\n", "/**\n * @license lucide-react v0.483.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport { mergeClasses, toKebabCase } from './shared/src/utils.js';\nimport Icon from './Icon.js';\n\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef(\n    ({ className, ...props }, ref) => createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(`lucide-${toKebabCase(iconName)}`, className),\n      ...props\n    })\n  );\n  Component.displayName = `${iconName}`;\n  return Component;\n};\n\nexport { createLucideIcon as default };\n//# sourceMappingURL=createLucideIcon.js.map\n", "/**\n * @license lucide-react v0.483.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: 2,\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\"\n};\n\nexport { defaultAttributes as default };\n//# sourceMappingURL=defaultAttributes.js.map\n", "/**\n * @license lucide-react v0.483.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"polyline\", { points: \"12 6 12 12 16 14\", key: \"68esgv\" }]\n];\nconst Clock = createLucideIcon(\"Clock\", __iconNode);\n\nexport { __iconNode, Clock as default };\n//# sourceMappingURL=clock.js.map\n", "/**\n * @license lucide-react v0.483.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nconst toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst mergeClasses = (...classes) => classes.filter((className, index, array) => {\n  return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n}).join(\" \").trim();\n\nexport { mergeClasses, toKebabCase };\n//# sourceMappingURL=utils.js.map\n", "import FlashSaleBanner from \"../banner-flash-sale\";\r\n\r\n// Create fixture date that is 2 hours from when the component is rendered\r\nconst createEndTime = (hoursFromNow: number = 2) => {\r\n  return new Date(Date.now() + 1000 * 60 * 60 * hoursFromNow);\r\n};\r\n\r\nexport const defaultFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(),\r\n    title: \"FLASH SALE\",\r\n    subtitle: \"แสงอรุณนครินทร์\",\r\n  },\r\n};\r\n\r\nexport const withDiscount = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(),\r\n    title: \"FLASH SALE\",\r\n    subtitle: \"แสงอรุณนครินทร์\",\r\n    discount: \"-11%\",\r\n    showDiscount: true,\r\n  },\r\n};\r\n\r\nexport const almostEndingFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(0.01), // 36 seconds from now\r\n    title: \"HURRY UP!\",\r\n    subtitle: \"โปรโมชั่นกำลังจะหมด\",\r\n    variant: \"danger\" as const,\r\n  },\r\n};\r\n\r\nexport const primaryVariantFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(),\r\n    title: \"MEMBERS SALE\",\r\n    subtitle: \"สำหรับสมาชิก\",\r\n    variant: \"primary\" as const,\r\n    discount: \"-15%\",\r\n    showDiscount: true,\r\n  },\r\n};\r\n\r\nexport const secondaryVariantFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(6), // 6 hours\r\n    title: \"WEEKEND SALE\",\r\n    subtitle: \"เฉพาะสุดสัปดาห์\",\r\n    variant: \"secondary\" as const,\r\n  },\r\n};\r\n\r\nexport const largeFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(),\r\n    title: \"BIG FLASH SALE\",\r\n    subtitle: \"ลดราคาพิเศษ\",\r\n    size: \"lg\" as const,\r\n    discount: \"-20%\",\r\n    showDiscount: true,\r\n  },\r\n};\r\n\r\nexport const customIconFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(24), // 24 hours\r\n    title: \"24-HOUR SALE\",\r\n    subtitle: \"ลดราคา 24 ชั่วโมง\",\r\n    // This would be replaced by an actual icon in real implementation\r\n    icon: \"⏰\",\r\n    iconPosition: \"left\" as const,\r\n  },\r\n};\r\n\r\nexport const allFixtures = [\r\n  { name: \"Default\", ...defaultFixture },\r\n  { name: \"With Discount\", ...withDiscount },\r\n  { name: \"Almost Ending\", ...almostEndingFixture },\r\n  { name: \"Primary Variant\", ...primaryVariantFixture },\r\n  { name: \"Secondary Variant\", ...secondaryVariantFixture },\r\n  { name: \"Large Size\", ...largeFixture },\r\n  { name: \"Custom Icon\", ...customIconFixture },\r\n];", "\n    /* eslint-disable */\n    // @ts-nocheck\n    // @ts-expect-error (Converted from ts-ignore)\n    var __STORY__ = \"import type { Meta, StoryObj } from \\\"@storybook/react\\\";\\r\\nimport FlashSaleBanner from \\\"../banner-flash-sale\\\";\\r\\nimport { \\r\\n  defaultFixture,\\r\\n  withDiscount,\\r\\n  almostEndingFixture,\\r\\n  primaryVariantFixture,\\r\\n  secondaryVariantFixture,\\r\\n  largeFixture,\\r\\n  customIconFixture\\r\\n} from \\\"../__fixtures__/banner-flash-sale.fixtures\\\";\\r\\nimport { userEvent, within, expect } from \\\"@storybook/test\\\";\\r\\nimport { Clock } from \\\"lucide-react\\\";\\r\\n\\r\\n/**\\r\\n * Flash Sale Banner component displays a promotional banner with a countdown timer.\\r\\n * \\r\\n * This component is commonly used for limited-time promotions, such as flash sales,\\r\\n * temporary discounts, or special offers.\\r\\n */\\r\\nconst meta: Meta<typeof FlashSaleBanner> = {\\r\\n  title: \\\"UI/Banner/BannerFlashSale\\\",\\r\\n  component: FlashSaleBanner,\\r\\n  parameters: {\\r\\n    layout: \\\"centered\\\",\\r\\n    docs: {\\r\\n      description: {\\r\\n        component: \\\"A flash sale banner that displays countdown information for time-sensitive promotions.\\\"\\r\\n      }\\r\\n    },\\r\\n    a11y: {\\r\\n      config: {\\r\\n        // A11y audit rules\\r\\n        rules: [\\r\\n          {\\r\\n            id: \\\"color-contrast\\\",\\r\\n            reviewOnFail: true,\\r\\n          },\\r\\n          {\\r\\n            id: \\\"aria-valid-attr\\\",\\r\\n            reviewOnFail: true,\\r\\n          }\\r\\n        ],\\r\\n      },\\r\\n    },\\r\\n  },\\r\\n  argTypes: {\\r\\n    endTime: {\\r\\n      control: {\\r\\n        type: 'date'\\r\\n      },\\r\\n      description: \\\"The end time for the sale countdown\\\",\\r\\n      transform: (value: string) => {\\r\\n        if (!value) return new Date();\\r\\n        return new Date(value);\\r\\n      }\\r\\n    },\\r\\n    title: {\\r\\n      control: \\\"text\\\",\\r\\n      description: \\\"Main title text displayed in the banner\\\",\\r\\n    },\\r\\n    subtitle: {\\r\\n      control: \\\"text\\\",\\r\\n      description: \\\"Subtitle displayed above the main title\\\",\\r\\n    },\\r\\n    discount: {\\r\\n      control: \\\"text\\\",\\r\\n      description: \\\"Discount text to display (e.g., '-11%')\\\",\\r\\n    },\\r\\n    showDiscount: {\\r\\n      control: \\\"boolean\\\",\\r\\n      description: \\\"Whether to show the discount badge\\\",\\r\\n    },\\r\\n    variant: {\\r\\n      options: [\\\"default\\\", \\\"primary\\\", \\\"secondary\\\", \\\"danger\\\"],\\r\\n      control: { type: \\\"select\\\" },\\r\\n      description: \\\"Visual style variant of the banner\\\",\\r\\n    },\\r\\n    size: {\\r\\n      options: [\\\"sm\\\", \\\"default\\\", \\\"lg\\\"],\\r\\n      control: { type: \\\"select\\\" },\\r\\n      description: \\\"Size variant of the banner\\\",\\r\\n    },\\r\\n    iconPosition: {\\r\\n      options: [\\\"left\\\", \\\"right\\\"],\\r\\n      control: { type: \\\"radio\\\" },\\r\\n      description: \\\"Position of the icon relative to the title\\\",\\r\\n    },\\r\\n    onTimeEnd: {\\r\\n      action: \\\"Timer ended\\\",\\r\\n      description: \\\"Callback function when timer reaches zero\\\",\\r\\n    },\\r\\n    icon: {\\r\\n      control: { disable: true },\\r\\n      description: \\\"Custom icon element to display\\\",\\r\\n    },\\r\\n  },\\r\\n  args: {\\r\\n    ...defaultFixture.props,\\r\\n  },\\r\\n  tags: ['autodocs'],\\r\\n  decorators: [\\r\\n    (Story) => (\\r\\n      <div className=\\\"w-full max-w-2xl\\\">\\r\\n        <Story />\\r\\n      </div>\\r\\n    ),\\r\\n  ],\\r\\n};\\r\\n\\r\\nexport default meta;\\r\\ntype Story = StoryObj<typeof FlashSaleBanner>;\\r\\n\\r\\n/**\\r\\n * Default Flash Sale Banner with standard styling and configuration.\\r\\n */\\r\\nexport const Default: Story = {\\r\\n  args: {\\r\\n    ...defaultFixture.props,\\r\\n  },\\r\\n};\\r\\n\\r\\n/**\\r\\n * Flash Sale Banner with a discount badge.\\r\\n */\\r\\nexport const WithDiscount: Story = {\\r\\n  args: {\\r\\n    ...withDiscount.props,\\r\\n  },\\r\\n};\\r\\n\\r\\n/**\\r\\n * Flash Sale Banner with \\\"danger\\\" styling for urgency when time is almost up.\\r\\n */\\r\\nexport const AlmostEnding: Story = {\\r\\n  args: {\\r\\n    ...almostEndingFixture.props,\\r\\n  },\\r\\n};\\r\\n\\r\\n/**\\r\\n * Primary variant of the Flash Sale Banner.\\r\\n */\\r\\nexport const PrimaryVariant: Story = {\\r\\n  args: {\\r\\n    ...primaryVariantFixture.props,\\r\\n  },\\r\\n};\\r\\n\\r\\n/**\\r\\n * Secondary variant of the Flash Sale Banner.\\r\\n */\\r\\nexport const SecondaryVariant: Story = {\\r\\n  args: {\\r\\n    ...secondaryVariantFixture.props,\\r\\n  },\\r\\n};\\r\\n\\r\\n/**\\r\\n * Large size variant of the Flash Sale Banner.\\r\\n */\\r\\nexport const LargeSize: Story = {\\r\\n  args: {\\r\\n    ...largeFixture.props,\\r\\n  },\\r\\n};\\r\\n\\r\\n/**\\r\\n * Flash Sale Banner with custom icon and left positioning.\\r\\n */\\r\\nexport const CustomIcon: Story = {\\r\\n  args: {\\r\\n    ...customIconFixture.props,\\r\\n    icon: <Clock className=\\\"h-6 w-6 fill-current\\\" />,\\r\\n  },\\r\\n};\\r\\n\\r\\n/**\\r\\n * Interactive example showing the callback when timer ends.\\r\\n */\\r\\nexport const WithTimeEndCallback: Story = {\\r\\n  args: {\\r\\n    endTime: new Date(Date.now() + 5000), // 5 seconds from now\\r\\n    title: \\\"ENDING SOON\\\",\\r\\n    subtitle: \\\"โปรโมชั่นกำลังจะหมด\\\",\\r\\n    variant: \\\"danger\\\",\\r\\n  },\\r\\n  play: async ({ canvasElement, args }) => {\\r\\n    const canvas = within(canvasElement);\\r\\n    \\r\\n    // Wait for the timer to end (6 seconds to be safe)\\r\\n    await new Promise(resolve => setTimeout(resolve, 6000));\\r\\n    \\r\\n    // Verify that the callback was called\\r\\n    await expect(args.onTimeEnd).toHaveBeenCalled();\\r\\n  }\\r\\n};\\r\\n\\r\\n/**\\r\\n * Accessibility and keyboard navigation test.\\r\\n */\\r\\nexport const AccessibilityTest: Story = {\\r\\n  args: {\\r\\n    ...defaultFixture.props,\\r\\n  },\\r\\n  play: async ({ canvasElement }) => {\\r\\n    const canvas = within(canvasElement);\\r\\n    \\r\\n    // Focus the element\\r\\n    const banner = canvas.getByTestId(\\\"flash-sale-banner\\\");\\r\\n    await userEvent.tab();\\r\\n    \\r\\n    // Verify accessibility attributes\\r\\n    expect(banner).toHaveAttribute(\\\"role\\\", \\\"alert\\\");\\r\\n    expect(banner).toHaveAttribute(\\\"aria-live\\\", \\\"polite\\\");\\r\\n  }\\r\\n};\";\n    // @ts-expect-error (Converted from ts-ignore)\n    var __LOCATIONS_MAP__ = {\n  \"Default\": {\n    \"startLoc\": {\n      \"col\": 27,\n      \"line\": 132\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 136\n    },\n    \"startBody\": {\n      \"col\": 27,\n      \"line\": 132\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 136\n    }\n  },\n  \"WithDiscount\": {\n    \"startLoc\": {\n      \"col\": 32,\n      \"line\": 139\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 143\n    },\n    \"startBody\": {\n      \"col\": 32,\n      \"line\": 139\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 143\n    }\n  },\n  \"AlmostEnding\": {\n    \"startLoc\": {\n      \"col\": 32,\n      \"line\": 146\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 150\n    },\n    \"startBody\": {\n      \"col\": 32,\n      \"line\": 146\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 150\n    }\n  },\n  \"PrimaryVariant\": {\n    \"startLoc\": {\n      \"col\": 34,\n      \"line\": 153\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 157\n    },\n    \"startBody\": {\n      \"col\": 34,\n      \"line\": 153\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 157\n    }\n  },\n  \"SecondaryVariant\": {\n    \"startLoc\": {\n      \"col\": 36,\n      \"line\": 160\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 164\n    },\n    \"startBody\": {\n      \"col\": 36,\n      \"line\": 160\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 164\n    }\n  },\n  \"LargeSize\": {\n    \"startLoc\": {\n      \"col\": 29,\n      \"line\": 167\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 171\n    },\n    \"startBody\": {\n      \"col\": 29,\n      \"line\": 167\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 171\n    }\n  },\n  \"CustomIcon\": {\n    \"startLoc\": {\n      \"col\": 30,\n      \"line\": 174\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 185\n    },\n    \"startBody\": {\n      \"col\": 30,\n      \"line\": 174\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 185\n    }\n  },\n  \"WithTimeEndCallback\": {\n    \"startLoc\": {\n      \"col\": 39,\n      \"line\": 188\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 202\n    },\n    \"startBody\": {\n      \"col\": 39,\n      \"line\": 188\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 202\n    }\n  },\n  \"AccessibilityTest\": {\n    \"startLoc\": {\n      \"col\": 37,\n      \"line\": 205\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 218\n    },\n    \"startBody\": {\n      \"col\": 37,\n      \"line\": 205\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 218\n    }\n  }\n};\n    \nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport FlashSaleBanner from \"../banner-flash-sale\";\nimport { defaultFixture, withDiscount, almostEndingFixture, primaryVariantFixture, secondaryVariantFixture, largeFixture, customIconFixture } from \"../__fixtures__/banner-flash-sale.fixtures\";\nimport { userEvent, within, expect } from \"@storybook/test\";\nimport { Clock } from \"__barrel_optimize__?names=Clock!=!lucide-react\";\n/**\r\n * Flash Sale Banner component displays a promotional banner with a countdown timer.\r\n * \r\n * This component is commonly used for limited-time promotions, such as flash sales,\r\n * temporary discounts, or special offers.\r\n */ const meta = {\n    title: \"UI/Banner/BannerFlashSale\",\n    component: FlashSaleBanner,\n    parameters: {\n  \"storySource\": {\n    \"source\": \"import { jsxDEV as _jsxDEV } from \\\"react/jsx-dev-runtime\\\";\\nimport FlashSaleBanner from \\\"../banner-flash-sale\\\";\\nimport { defaultFixture, withDiscount, almostEndingFixture, primaryVariantFixture, secondaryVariantFixture, largeFixture, customIconFixture } from \\\"../__fixtures__/banner-flash-sale.fixtures\\\";\\nimport { userEvent, within, expect } from \\\"@storybook/test\\\";\\nimport { Clock } from \\\"__barrel_optimize__?names=Clock!=!lucide-react\\\";\\n/**\\r\\n * Flash Sale Banner component displays a promotional banner with a countdown timer.\\r\\n * \\r\\n * This component is commonly used for limited-time promotions, such as flash sales,\\r\\n * temporary discounts, or special offers.\\r\\n */ const meta = {\\n    title: \\\"UI/Banner/BannerFlashSale\\\",\\n    component: FlashSaleBanner,\\n    parameters: {\\n        layout: \\\"centered\\\",\\n        docs: {\\n            description: {\\n                component: \\\"A flash sale banner that displays countdown information for time-sensitive promotions.\\\"\\n            }\\n        },\\n        a11y: {\\n            config: {\\n                // A11y audit rules\\n                rules: [\\n                    {\\n                        id: \\\"color-contrast\\\",\\n                        reviewOnFail: true\\n                    },\\n                    {\\n                        id: \\\"aria-valid-attr\\\",\\n                        reviewOnFail: true\\n                    }\\n                ]\\n            }\\n        }\\n    },\\n    argTypes: {\\n        endTime: {\\n            control: {\\n                type: 'date'\\n            },\\n            description: \\\"The end time for the sale countdown\\\",\\n            transform: (value)=>{\\n                if (!value) return new Date();\\n                return new Date(value);\\n            }\\n        },\\n        title: {\\n            control: \\\"text\\\",\\n            description: \\\"Main title text displayed in the banner\\\"\\n        },\\n        subtitle: {\\n            control: \\\"text\\\",\\n            description: \\\"Subtitle displayed above the main title\\\"\\n        },\\n        discount: {\\n            control: \\\"text\\\",\\n            description: \\\"Discount text to display (e.g., '-11%')\\\"\\n        },\\n        showDiscount: {\\n            control: \\\"boolean\\\",\\n            description: \\\"Whether to show the discount badge\\\"\\n        },\\n        variant: {\\n            options: [\\n                \\\"default\\\",\\n                \\\"primary\\\",\\n                \\\"secondary\\\",\\n                \\\"danger\\\"\\n            ],\\n            control: {\\n                type: \\\"select\\\"\\n            },\\n            description: \\\"Visual style variant of the banner\\\"\\n        },\\n        size: {\\n            options: [\\n                \\\"sm\\\",\\n                \\\"default\\\",\\n                \\\"lg\\\"\\n            ],\\n            control: {\\n                type: \\\"select\\\"\\n            },\\n            description: \\\"Size variant of the banner\\\"\\n        },\\n        iconPosition: {\\n            options: [\\n                \\\"left\\\",\\n                \\\"right\\\"\\n            ],\\n            control: {\\n                type: \\\"radio\\\"\\n            },\\n            description: \\\"Position of the icon relative to the title\\\"\\n        },\\n        onTimeEnd: {\\n            action: \\\"Timer ended\\\",\\n            description: \\\"Callback function when timer reaches zero\\\"\\n        },\\n        icon: {\\n            control: {\\n                disable: true\\n            },\\n            description: \\\"Custom icon element to display\\\"\\n        }\\n    },\\n    args: {\\n        ...defaultFixture.props\\n    },\\n    tags: [\\n        'autodocs'\\n    ],\\n    decorators: [\\n        (Story)=>/*#__PURE__*/ _jsxDEV(\\\"div\\\", {\\n                className: \\\"w-full max-w-2xl\\\",\\n                children: /*#__PURE__*/ _jsxDEV(Story, {}, void 0, false, {\\n                    fileName: \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Desktop\\\\\\\\fristJob\\\\\\\\sangaroon-nakharin-ecommerce\\\\\\\\frontend\\\\\\\\home\\\\\\\\src\\\\\\\\components\\\\\\\\Banner\\\\\\\\__stories__\\\\\\\\banner-flash-sale.stories.tsx\\\",\\n                    lineNumber: 105,\\n                    columnNumber: 9\\n                }, this)\\n            }, void 0, false, {\\n                fileName: \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Desktop\\\\\\\\fristJob\\\\\\\\sangaroon-nakharin-ecommerce\\\\\\\\frontend\\\\\\\\home\\\\\\\\src\\\\\\\\components\\\\\\\\Banner\\\\\\\\__stories__\\\\\\\\banner-flash-sale.stories.tsx\\\",\\n                lineNumber: 104,\\n                columnNumber: 7\\n            }, this)\\n    ]\\n};\\nexport default meta;\\n/**\\r\\n * Default Flash Sale Banner with standard styling and configuration.\\r\\n */ export const Default = {\\n    args: {\\n        ...defaultFixture.props\\n    }\\n};\\n/**\\r\\n * Flash Sale Banner with a discount badge.\\r\\n */ export const WithDiscount = {\\n    args: {\\n        ...withDiscount.props\\n    }\\n};\\n/**\\r\\n * Flash Sale Banner with \\\"danger\\\" styling for urgency when time is almost up.\\r\\n */ export const AlmostEnding = {\\n    args: {\\n        ...almostEndingFixture.props\\n    }\\n};\\n/**\\r\\n * Primary variant of the Flash Sale Banner.\\r\\n */ export const PrimaryVariant = {\\n    args: {\\n        ...primaryVariantFixture.props\\n    }\\n};\\n/**\\r\\n * Secondary variant of the Flash Sale Banner.\\r\\n */ export const SecondaryVariant = {\\n    args: {\\n        ...secondaryVariantFixture.props\\n    }\\n};\\n/**\\r\\n * Large size variant of the Flash Sale Banner.\\r\\n */ export const LargeSize = {\\n    args: {\\n        ...largeFixture.props\\n    }\\n};\\n/**\\r\\n * Flash Sale Banner with custom icon and left positioning.\\r\\n */ export const CustomIcon = {\\n    args: {\\n        ...customIconFixture.props,\\n        icon: /*#__PURE__*/ _jsxDEV(Clock, {\\n            className: \\\"h-6 w-6 fill-current\\\"\\n        }, void 0, false, {\\n            fileName: \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Desktop\\\\\\\\fristJob\\\\\\\\sangaroon-nakharin-ecommerce\\\\\\\\frontend\\\\\\\\home\\\\\\\\src\\\\\\\\components\\\\\\\\Banner\\\\\\\\__stories__\\\\\\\\banner-flash-sale.stories.tsx\\\",\\n            lineNumber: 174,\\n            columnNumber: 11\\n        }, this)\\n    }\\n};\\n/**\\r\\n * Interactive example showing the callback when timer ends.\\r\\n */ export const WithTimeEndCallback = {\\n    args: {\\n        endTime: new Date(Date.now() + 5000),\\n        title: \\\"ENDING SOON\\\",\\n        subtitle: \\\"โปรโมชั่นกำลังจะหมด\\\",\\n        variant: \\\"danger\\\"\\n    },\\n    play: async ({ canvasElement, args })=>{\\n        const canvas = within(canvasElement);\\n        // Wait for the timer to end (6 seconds to be safe)\\n        await new Promise((resolve)=>setTimeout(resolve, 6000));\\n        // Verify that the callback was called\\n        await expect(args.onTimeEnd).toHaveBeenCalled();\\n    }\\n};\\n/**\\r\\n * Accessibility and keyboard navigation test.\\r\\n */ export const AccessibilityTest = {\\n    args: {\\n        ...defaultFixture.props\\n    },\\n    play: async ({ canvasElement })=>{\\n        const canvas = within(canvasElement);\\n        // Focus the element\\n        const banner = canvas.getByTestId(\\\"flash-sale-banner\\\");\\n        await userEvent.tab();\\n        // Verify accessibility attributes\\n        expect(banner).toHaveAttribute(\\\"role\\\", \\\"alert\\\");\\n        expect(banner).toHaveAttribute(\\\"aria-live\\\", \\\"polite\\\");\\n    }\\n};\\n\",\n    \"locationsMap\": {\n      \"default\": {\n        \"startLoc\": {\n          \"col\": 27,\n          \"line\": 132\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 136\n        },\n        \"startBody\": {\n          \"col\": 27,\n          \"line\": 132\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 136\n        }\n      },\n      \"with-discount\": {\n        \"startLoc\": {\n          \"col\": 32,\n          \"line\": 139\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 143\n        },\n        \"startBody\": {\n          \"col\": 32,\n          \"line\": 139\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 143\n        }\n      },\n      \"almost-ending\": {\n        \"startLoc\": {\n          \"col\": 32,\n          \"line\": 146\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 150\n        },\n        \"startBody\": {\n          \"col\": 32,\n          \"line\": 146\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 150\n        }\n      },\n      \"primary-variant\": {\n        \"startLoc\": {\n          \"col\": 34,\n          \"line\": 153\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 157\n        },\n        \"startBody\": {\n          \"col\": 34,\n          \"line\": 153\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 157\n        }\n      },\n      \"secondary-variant\": {\n        \"startLoc\": {\n          \"col\": 36,\n          \"line\": 160\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 164\n        },\n        \"startBody\": {\n          \"col\": 36,\n          \"line\": 160\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 164\n        }\n      },\n      \"large-size\": {\n        \"startLoc\": {\n          \"col\": 29,\n          \"line\": 167\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 171\n        },\n        \"startBody\": {\n          \"col\": 29,\n          \"line\": 167\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 171\n        }\n      },\n      \"custom-icon\": {\n        \"startLoc\": {\n          \"col\": 30,\n          \"line\": 174\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 185\n        },\n        \"startBody\": {\n          \"col\": 30,\n          \"line\": 174\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 185\n        }\n      },\n      \"with-time-end-callback\": {\n        \"startLoc\": {\n          \"col\": 39,\n          \"line\": 188\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 202\n        },\n        \"startBody\": {\n          \"col\": 39,\n          \"line\": 188\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 202\n        }\n      },\n      \"accessibility-test\": {\n        \"startLoc\": {\n          \"col\": 37,\n          \"line\": 205\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 218\n        },\n        \"startBody\": {\n          \"col\": 37,\n          \"line\": 205\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 218\n        }\n      }\n    }\n  }\n,\n        layout: \"centered\",\n        docs: {\n            description: {\n                component: \"A flash sale banner that displays countdown information for time-sensitive promotions.\"\n            }\n        },\n        a11y: {\n            config: {\n                // A11y audit rules\n                rules: [\n                    {\n                        id: \"color-contrast\",\n                        reviewOnFail: true\n                    },\n                    {\n                        id: \"aria-valid-attr\",\n                        reviewOnFail: true\n                    }\n                ]\n            }\n        }\n    },\n    argTypes: {\n        endTime: {\n            control: {\n                type: 'date'\n            },\n            description: \"The end time for the sale countdown\",\n            transform: (value)=>{\n                if (!value) return new Date();\n                return new Date(value);\n            }\n        },\n        title: {\n            control: \"text\",\n            description: \"Main title text displayed in the banner\"\n        },\n        subtitle: {\n            control: \"text\",\n            description: \"Subtitle displayed above the main title\"\n        },\n        discount: {\n            control: \"text\",\n            description: \"Discount text to display (e.g., '-11%')\"\n        },\n        showDiscount: {\n            control: \"boolean\",\n            description: \"Whether to show the discount badge\"\n        },\n        variant: {\n            options: [\n                \"default\",\n                \"primary\",\n                \"secondary\",\n                \"danger\"\n            ],\n            control: {\n                type: \"select\"\n            },\n            description: \"Visual style variant of the banner\"\n        },\n        size: {\n            options: [\n                \"sm\",\n                \"default\",\n                \"lg\"\n            ],\n            control: {\n                type: \"select\"\n            },\n            description: \"Size variant of the banner\"\n        },\n        iconPosition: {\n            options: [\n                \"left\",\n                \"right\"\n            ],\n            control: {\n                type: \"radio\"\n            },\n            description: \"Position of the icon relative to the title\"\n        },\n        onTimeEnd: {\n            action: \"Timer ended\",\n            description: \"Callback function when timer reaches zero\"\n        },\n        icon: {\n            control: {\n                disable: true\n            },\n            description: \"Custom icon element to display\"\n        }\n    },\n    args: {\n        ...defaultFixture.props\n    },\n    tags: [\n        'autodocs'\n    ],\n    decorators: [\n        (Story)=>/*#__PURE__*/ _jsxDEV(\"div\", {\n                className: \"w-full max-w-2xl\",\n                children: /*#__PURE__*/ _jsxDEV(Story, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Banner\\\\__stories__\\\\banner-flash-sale.stories.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Banner\\\\__stories__\\\\banner-flash-sale.stories.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this)\n    ]\n};\nexport default meta;\n/**\r\n * Default Flash Sale Banner with standard styling and configuration.\r\n */ export const Default = {\n    args: {\n        ...defaultFixture.props\n    }\n};;\n/**\r\n * Flash Sale Banner with a discount badge.\r\n */ export const WithDiscount = {\n    args: {\n        ...withDiscount.props\n    }\n};;\n/**\r\n * Flash Sale Banner with \"danger\" styling for urgency when time is almost up.\r\n */ export const AlmostEnding = {\n    args: {\n        ...almostEndingFixture.props\n    }\n};;\n/**\r\n * Primary variant of the Flash Sale Banner.\r\n */ export const PrimaryVariant = {\n    args: {\n        ...primaryVariantFixture.props\n    }\n};;\n/**\r\n * Secondary variant of the Flash Sale Banner.\r\n */ export const SecondaryVariant = {\n    args: {\n        ...secondaryVariantFixture.props\n    }\n};;\n/**\r\n * Large size variant of the Flash Sale Banner.\r\n */ export const LargeSize = {\n    args: {\n        ...largeFixture.props\n    }\n};;\n/**\r\n * Flash Sale Banner with custom icon and left positioning.\r\n */ export const CustomIcon = {\n    args: {\n        ...customIconFixture.props,\n        icon: /*#__PURE__*/ _jsxDEV(Clock, {\n            className: \"h-6 w-6 fill-current\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Banner\\\\__stories__\\\\banner-flash-sale.stories.tsx\",\n            lineNumber: 174,\n            columnNumber: 11\n        }, this)\n    }\n};;\n/**\r\n * Interactive example showing the callback when timer ends.\r\n */ export const WithTimeEndCallback = {\n    args: {\n        endTime: new Date(Date.now() + 5000),\n        title: \"ENDING SOON\",\n        subtitle: \"โปรโมชั่นกำลังจะหมด\",\n        variant: \"danger\"\n    },\n    play: async ({ canvasElement, args })=>{\n        const canvas = within(canvasElement);\n        // Wait for the timer to end (6 seconds to be safe)\n        await new Promise((resolve)=>setTimeout(resolve, 6000));\n        // Verify that the callback was called\n        await expect(args.onTimeEnd).toHaveBeenCalled();\n    }\n};;\n/**\r\n * Accessibility and keyboard navigation test.\r\n */ export const AccessibilityTest = {\n    args: {\n        ...defaultFixture.props\n    },\n    play: async ({ canvasElement })=>{\n        const canvas = within(canvasElement);\n        // Focus the element\n        const banner = canvas.getByTestId(\"flash-sale-banner\");\n        await userEvent.tab();\n        // Verify accessibility attributes\n        expect(banner).toHaveAttribute(\"role\", \"alert\");\n        expect(banner).toHaveAttribute(\"aria-live\", \"polite\");\n    }\n};\n;export const __namedExportsOrder = [\"Default\",\"WithDiscount\",\"AlmostEnding\",\"PrimaryVariant\",\"SecondaryVariant\",\"LargeSize\",\"CustomIcon\",\"WithTimeEndCallback\",\"AccessibilityTest\"];", "import { useState, useEffect, ReactNode } from 'react';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\nimport { cn } from '@/lib/utils';\r\nimport Image from 'next/image';\r\n\r\n// Define variants using class-variance-authority\r\nconst flashSaleBannerVariants = cva('flex items-center rounded-card border gap-2 w-full', {\r\n  variants: {\r\n    variant: {\r\n      default: 'border-secondary bg-secondary-bg text-black',\r\n      primary: 'border-blue-400 bg-blue-50 text-black',\r\n      secondary: 'border-purple-400 bg-purple-50 text-black',\r\n      danger: 'border-red-400 bg-red-50 text-black',\r\n    },\r\n    size: {\r\n      sm: 'text-sm min-h-[80px]',\r\n      default: 'text-base sm:h-[96px] sm:w-[548px]', // Fixed size only on sm and above\r\n      lg: 'text-lg min-h-[112px]',\r\n    },\r\n  },\r\n  defaultVariants: {\r\n    variant: 'default',\r\n    size: 'default',\r\n  },\r\n});\r\n\r\n// Timer box\r\nconst timerBoxVariants = cva('font-bold px-3 py-2 text-xl rounded-sm', {\r\n  variants: {\r\n    variant: {\r\n      default: 'bg-[#FFD904] text-black',\r\n      primary: 'bg-blue-400 text-white',\r\n      secondary: 'bg-purple-400 text-white',\r\n      danger: 'bg-red-400 text-white',\r\n    },\r\n  },\r\n  defaultVariants: {\r\n    variant: 'default',\r\n  },\r\n});\r\n\r\n// Discount badge\r\nconst discountBadgeVariants = cva('px-2 py-1 rounded-full text-xs font-bold', {\r\n  variants: {\r\n    variant: {\r\n      default: 'bg-red-500 text-white',\r\n      primary: 'bg-blue-600 text-white',\r\n      secondary: 'bg-purple-600 text-white',\r\n      danger: 'bg-red-600 text-white',\r\n    },\r\n  },\r\n  defaultVariants: {\r\n    variant: 'default',\r\n  },\r\n});\r\n\r\n// Props\r\nexport interface FlashSaleBannerProps extends VariantProps<typeof flashSaleBannerVariants> {\r\n  className?: string;\r\n  endTime: Date | string;\r\n  title?: string;\r\n  subtitle?: string;\r\n  discount?: string;\r\n  showDiscount?: boolean;\r\n  onTimeEnd?: () => void;\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  testId?: string;\r\n}\r\n\r\nconst FlashSaleBanner = ({\r\n  className,\r\n  variant,\r\n  size,\r\n  endTime,\r\n  title = 'FLASH SALE',\r\n  subtitle = 'แสงอรุณนครินทร์',\r\n  discount = '-11%',\r\n  showDiscount = false,\r\n  onTimeEnd,\r\n  icon = (\r\n    <Image\r\n      src=\"/images/lightning.png\"\r\n      alt=\"Flash sale icon\"\r\n      width={52}\r\n      height={52}\r\n      className=\"h-7 w-7 object-contain sm:h-[52px] sm:w-[52px]\"\r\n    />\r\n  ),\r\n  iconPosition = 'right',\r\n  testId = 'flash-sale-banner',\r\n}: FlashSaleBannerProps) => {\r\n  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());\r\n\r\n  function calculateTimeLeft() {\r\n    const endDateTime = endTime instanceof Date ? endTime : new Date(endTime);\r\n    const difference = endDateTime.getTime() - new Date().getTime();\r\n\r\n    if (difference <= 0) {\r\n      if (onTimeEnd) {\r\n        onTimeEnd();\r\n      }\r\n      return { hours: '00', minutes: '00', seconds: '00' };\r\n    }\r\n\r\n    return {\r\n      hours: Math.floor((difference / (1000 * 60 * 60)) % 24)\r\n        .toString()\r\n        .padStart(2, '0'),\r\n      minutes: Math.floor((difference / 1000 / 60) % 60)\r\n        .toString()\r\n        .padStart(2, '0'),\r\n      seconds: Math.floor((difference / 1000) % 60)\r\n        .toString()\r\n        .padStart(2, '0'),\r\n    };\r\n  }\r\n\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      setTimeLeft(calculateTimeLeft());\r\n    }, 1000);\r\n\r\n    return () => clearInterval(timer);\r\n  }, [endTime]);\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        flashSaleBannerVariants({ variant, size, className }),\r\n        'mx-auto flex-col sm:flex-row',\r\n      )}\r\n      data-testid={testId}\r\n      role=\"alert\"\r\n      aria-live=\"polite\"\r\n    >\r\n      <div className=\"flex h-full w-full flex-col items-center gap-3 px-4 text-center sm:flex-row sm:justify-between sm:px-4 sm:text-left\">\r\n        {/* Text & icon */}\r\n        <div className=\"flex flex-col items-center sm:flex-row sm:items-center\">\r\n          {iconPosition === 'left' && <div className=\"text-current\">{icon}</div>}\r\n          <div className=\"font-bold sm:ml-2\">\r\n            <span className=\"block text-sm font-medium sm:text-base\">{subtitle}</span>\r\n            <div className=\"flex items-center justify-center sm:justify-start\">\r\n              <span className=\"text-text-lg-bold sm:text-text-3xl flex items-center\">\r\n                FLASH\r\n                <div className=\"mx-1 inline-flex\">{icon}</div>\r\n                SALE\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Timer & discount */}\r\n        <div className=\"flex flex-col items-center gap-2 sm:flex-row sm:items-center\">\r\n          {showDiscount && <div className={cn(discountBadgeVariants({ variant }))}>{discount}</div>}\r\n          <div\r\n            className=\"flex\"\r\n            aria-label={`Time remaining: ${timeLeft.hours} hours, ${timeLeft.minutes} minutes, and ${timeLeft.seconds} seconds`}\r\n          >\r\n            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.hours}</div>\r\n            <div className=\"flex items-center px-1 text-xl font-bold text-current\">:</div>\r\n            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.minutes}</div>\r\n            <div className=\"flex items-center px-1 text-xl font-bold text-current\">:</div>\r\n            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.seconds}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FlashSaleBanner;\r\n"], "names": [], "sourceRoot": ""}