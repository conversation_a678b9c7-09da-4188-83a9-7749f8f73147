import{r as c,aM as I,aL as Te}from"./index-Bwql5Dzz.js";var Nt=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[Nt.Translate.toString(e),Nt.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}});function Nn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return c.useMemo(()=>r=>{t.forEach(o=>o(r))},t)}const rt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function we(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function yt(e){return"nodeType"in e}function z(e){var t,n;return e?we(e)?e:yt(e)&&(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window:window}function wt(e){const{Document:t}=z(e);return e instanceof t}function Fe(e){return we(e)?!1:e instanceof z(e).HTMLElement}function Vt(e){return e instanceof z(e).SVGElement}function xe(e){return e?we(e)?e.document:yt(e)?wt(e)?e:Fe(e)||Vt(e)?e.ownerDocument:document:document:document}const V=rt?c.useLayoutEffect:c.useEffect;function ot(e){const t=c.useRef(e);return V(()=>{t.current=e}),c.useCallback(function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return t.current==null?void 0:t.current(...r)},[])}function Ln(){const e=c.useRef(null),t=c.useCallback((r,o)=>{e.current=setInterval(r,o)},[]),n=c.useCallback(()=>{e.current!==null&&(clearInterval(e.current),e.current=null)},[]);return[t,n]}function Pe(e,t){t===void 0&&(t=[e]);const n=c.useRef(e);return V(()=>{n.current!==e&&(n.current=e)},t),n}function $e(e,t){const n=c.useRef();return c.useMemo(()=>{const r=e(n.current);return n.current=r,r},[...t])}function _e(e){const t=ot(e),n=c.useRef(null),r=c.useCallback(o=>{o!==n.current&&(t==null||t(o,n.current)),n.current=o},[]);return[n,r]}function Qe(e){const t=c.useRef();return c.useEffect(()=>{t.current=e},[e]),t.current}let ft={};function Xe(e,t){return c.useMemo(()=>{if(t)return t;const n=ft[e]==null?0:ft[e]+1;return ft[e]=n,e+"-"+n},[e,t])}function qt(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce((i,s)=>{const a=Object.entries(s);for(const[l,u]of a){const f=i[l];f!=null&&(i[l]=f+e*u)}return i},{...t})}}const ye=qt(1),ze=qt(-1);function kn(e){return"clientX"in e&&"clientY"in e}function it(e){if(!e)return!1;const{KeyboardEvent:t}=z(e.target);return t&&e instanceof t}function Pn(e){if(!e)return!1;const{TouchEvent:t}=z(e.target);return t&&e instanceof t}function Ze(e){if(Pn(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return kn(e)?{x:e.clientX,y:e.clientY}:null}const fe=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[fe.Translate.toString(e),fe.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),Lt="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function zn(e){return e.matches(Lt)?e:e.querySelector(Lt)}const Bn={display:"none"};function Fn(e){let{id:t,value:n}=e;return I.createElement("div",{id:t,style:Bn},n)}function $n(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const o={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return I.createElement("div",{id:t,style:o,role:"status","aria-live":r,"aria-atomic":!0},n)}function Xn(){const[e,t]=c.useState("");return{announce:c.useCallback(r=>{r!=null&&t(r)},[]),announcement:e}}const Gt=c.createContext(null);function jn(e){const t=c.useContext(Gt);c.useEffect(()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}function Yn(){const[e]=c.useState(()=>new Set),t=c.useCallback(r=>(e.add(r),()=>e.delete(r)),[e]);return[c.useCallback(r=>{let{type:o,event:i}=r;e.forEach(s=>{var a;return(a=s[o])==null?void 0:a.call(s,i)})},[e]),t]}const Kn={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},Un={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function Wn(e){let{announcements:t=Un,container:n,hiddenTextDescribedById:r,screenReaderInstructions:o=Kn}=e;const{announce:i,announcement:s}=Xn(),a=Xe("DndLiveRegion"),[l,u]=c.useState(!1);if(c.useEffect(()=>{u(!0)},[]),jn(c.useMemo(()=>({onDragStart(d){let{active:h}=d;i(t.onDragStart({active:h}))},onDragMove(d){let{active:h,over:g}=d;t.onDragMove&&i(t.onDragMove({active:h,over:g}))},onDragOver(d){let{active:h,over:g}=d;i(t.onDragOver({active:h,over:g}))},onDragEnd(d){let{active:h,over:g}=d;i(t.onDragEnd({active:h,over:g}))},onDragCancel(d){let{active:h,over:g}=d;i(t.onDragCancel({active:h,over:g}))}}),[i,t])),!l)return null;const f=I.createElement(I.Fragment,null,I.createElement(Fn,{id:r,value:o.draggable}),I.createElement($n,{id:a,announcement:s}));return n?Te.createPortal(f,n):f}var N;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(N||(N={}));function et(){}function xo(e,t){return c.useMemo(()=>({sensor:e,options:t??{}}),[e,t])}function Co(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return c.useMemo(()=>[...t].filter(r=>r!=null),[...t])}const q=Object.freeze({x:0,y:0});function Jt(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Hn(e,t){const n=Ze(e);if(!n)return"0 0";const r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function _t(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function Vn(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function kt(e){let{left:t,top:n,height:r,width:o}=e;return[{x:t,y:n},{x:t+o,y:n},{x:t,y:n+r},{x:t+o,y:n+r}]}function Qt(e,t){if(!e||e.length===0)return null;const[n]=e;return n[t]}function Pt(e,t,n){return t===void 0&&(t=e.left),n===void 0&&(n=e.top),{x:t+e.width*.5,y:n+e.height*.5}}const Do=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=Pt(t,t.left,t.top),i=[];for(const s of r){const{id:a}=s,l=n.get(a);if(l){const u=Jt(Pt(l),o);i.push({id:a,data:{droppableContainer:s,value:u}})}}return i.sort(_t)},qn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=kt(t),i=[];for(const s of r){const{id:a}=s,l=n.get(a);if(l){const u=kt(l),f=o.reduce((h,g,x)=>h+Jt(u[x],g),0),d=Number((f/4).toFixed(4));i.push({id:a,data:{droppableContainer:s,value:d}})}}return i.sort(_t)};function Gn(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),s=o-r,a=i-n;if(r<o&&n<i){const l=t.width*t.height,u=e.width*e.height,f=s*a,d=f/(l+u-f);return Number(d.toFixed(4))}return 0}const Jn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const i of r){const{id:s}=i,a=n.get(s);if(a){const l=Gn(a,t);l>0&&o.push({id:s,data:{droppableContainer:i,value:l}})}}return o.sort(Vn)};function _n(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function Zt(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:q}function Qn(e){return function(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return o.reduce((s,a)=>({...s,top:s.top+e*a.y,bottom:s.bottom+e*a.y,left:s.left+e*a.x,right:s.right+e*a.x}),{...n})}}const Zn=Qn(1);function en(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function er(e,t,n){const r=en(t);if(!r)return e;const{scaleX:o,scaleY:i,x:s,y:a}=r,l=e.left-s-(1-o)*parseFloat(n),u=e.top-a-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),f=o?e.width/o:e.width,d=i?e.height/i:e.height;return{width:f,height:d,top:u,right:l+f,bottom:u+d,left:l}}const tr={ignoreTransform:!1};function Ce(e,t){t===void 0&&(t=tr);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:u,transformOrigin:f}=z(e).getComputedStyle(e);u&&(n=er(n,u,f))}const{top:r,left:o,width:i,height:s,bottom:a,right:l}=n;return{top:r,left:o,width:i,height:s,bottom:a,right:l}}function zt(e){return Ce(e,{ignoreTransform:!0})}function nr(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function rr(e,t){return t===void 0&&(t=z(e).getComputedStyle(e)),t.position==="fixed"}function or(e,t){t===void 0&&(t=z(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(o=>{const i=t[o];return typeof i=="string"?n.test(i):!1})}function st(e,t){const n=[];function r(o){if(t!=null&&n.length>=t||!o)return n;if(wt(o)&&o.scrollingElement!=null&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!Fe(o)||Vt(o)||n.includes(o))return n;const i=z(e).getComputedStyle(o);return o!==e&&or(o,i)&&n.push(o),rr(o,i)?n:r(o.parentNode)}return e?r(e):n}function tn(e){const[t]=st(e,1);return t??null}function gt(e){return!rt||!e?null:we(e)?e:yt(e)?wt(e)||e===xe(e).scrollingElement?window:Fe(e)?e:null:null}function nn(e){return we(e)?e.scrollX:e.scrollLeft}function rn(e){return we(e)?e.scrollY:e.scrollTop}function vt(e){return{x:nn(e),y:rn(e)}}var L;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})(L||(L={}));function on(e){return!rt||!e?!1:e===document.scrollingElement}function sn(e){const t={x:0,y:0},n=on(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},o=e.scrollTop<=t.y,i=e.scrollLeft<=t.x,s=e.scrollTop>=r.y,a=e.scrollLeft>=r.x;return{isTop:o,isLeft:i,isBottom:s,isRight:a,maxScroll:r,minScroll:t}}const ir={x:.2,y:.2};function sr(e,t,n,r,o){let{top:i,left:s,right:a,bottom:l}=n;r===void 0&&(r=10),o===void 0&&(o=ir);const{isTop:u,isBottom:f,isLeft:d,isRight:h}=sn(e),g={x:0,y:0},x={x:0,y:0},p={height:t.height*o.y,width:t.width*o.x};return!u&&i<=t.top+p.height?(g.y=L.Backward,x.y=r*Math.abs((t.top+p.height-i)/p.height)):!f&&l>=t.bottom-p.height&&(g.y=L.Forward,x.y=r*Math.abs((t.bottom-p.height-l)/p.height)),!h&&a>=t.right-p.width?(g.x=L.Forward,x.x=r*Math.abs((t.right-p.width-a)/p.width)):!d&&s<=t.left+p.width&&(g.x=L.Backward,x.x=r*Math.abs((t.left+p.width-s)/p.width)),{direction:g,speed:x}}function ar(e){if(e===document.scrollingElement){const{innerWidth:i,innerHeight:s}=window;return{top:0,left:0,right:i,bottom:s,width:i,height:s}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function an(e){return e.reduce((t,n)=>ye(t,vt(n)),q)}function lr(e){return e.reduce((t,n)=>t+nn(n),0)}function cr(e){return e.reduce((t,n)=>t+rn(n),0)}function ln(e,t){if(t===void 0&&(t=Ce),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);tn(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const ur=[["x",["left","right"],lr],["y",["top","bottom"],cr]];class xt{constructor(t,n){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const r=st(n),o=an(r);this.rect={...t},this.width=t.width,this.height=t.height;for(const[i,s,a]of ur)for(const l of s)Object.defineProperty(this,l,{get:()=>{const u=a(r),f=o[i]-u;return this.rect[l]+f},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class Ne{constructor(t){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(n=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...n)})},this.target=t}add(t,n,r){var o;(o=this.target)==null||o.addEventListener(t,n,r),this.listeners.push([t,n,r])}}function dr(e){const{EventTarget:t}=z(e);return e instanceof t?e:xe(e)}function ht(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return typeof t=="number"?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t?r>t.y:!1}var U;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(U||(U={}));function Bt(e){e.preventDefault()}function fr(e){e.stopPropagation()}var D;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"})(D||(D={}));const cn={start:[D.Space,D.Enter],cancel:[D.Esc],end:[D.Space,D.Enter,D.Tab]},gr=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case D.Right:return{...n,x:n.x+25};case D.Left:return{...n,x:n.x-25};case D.Down:return{...n,y:n.y+25};case D.Up:return{...n,y:n.y-25}}};class un{constructor(t){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=t;const{event:{target:n}}=t;this.props=t,this.listeners=new Ne(xe(n)),this.windowListeners=new Ne(z(n)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(U.Resize,this.handleCancel),this.windowListeners.add(U.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(U.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:t,onStart:n}=this.props,r=t.node.current;r&&ln(r),n(q)}handleKeyDown(t){if(it(t)){const{active:n,context:r,options:o}=this.props,{keyboardCodes:i=cn,coordinateGetter:s=gr,scrollBehavior:a="smooth"}=o,{code:l}=t;if(i.end.includes(l)){this.handleEnd(t);return}if(i.cancel.includes(l)){this.handleCancel(t);return}const{collisionRect:u}=r.current,f=u?{x:u.left,y:u.top}:q;this.referenceCoordinates||(this.referenceCoordinates=f);const d=s(t,{active:n,context:r.current,currentCoordinates:f});if(d){const h=ze(d,f),g={x:0,y:0},{scrollableAncestors:x}=r.current;for(const p of x){const v=t.code,{isTop:m,isRight:y,isLeft:b,isBottom:S,maxScroll:R,minScroll:E}=sn(p),w=ar(p),C={x:Math.min(v===D.Right?w.right-w.width/2:w.right,Math.max(v===D.Right?w.left:w.left+w.width/2,d.x)),y:Math.min(v===D.Down?w.bottom-w.height/2:w.bottom,Math.max(v===D.Down?w.top:w.top+w.height/2,d.y))},O=v===D.Right&&!y||v===D.Left&&!b,T=v===D.Down&&!S||v===D.Up&&!m;if(O&&C.x!==d.x){const M=p.scrollLeft+h.x,W=v===D.Right&&M<=R.x||v===D.Left&&M>=E.x;if(W&&!h.y){p.scrollTo({left:M,behavior:a});return}W?g.x=p.scrollLeft-M:g.x=v===D.Right?p.scrollLeft-R.x:p.scrollLeft-E.x,g.x&&p.scrollBy({left:-g.x,behavior:a});break}else if(T&&C.y!==d.y){const M=p.scrollTop+h.y,W=v===D.Down&&M<=R.y||v===D.Up&&M>=E.y;if(W&&!h.x){p.scrollTo({top:M,behavior:a});return}W?g.y=p.scrollTop-M:g.y=v===D.Down?p.scrollTop-R.y:p.scrollTop-E.y,g.y&&p.scrollBy({top:-g.y,behavior:a});break}}this.handleMove(t,ye(ze(d,this.referenceCoordinates),g))}}}handleMove(t,n){const{onMove:r}=this.props;t.preventDefault(),r(n)}handleEnd(t){const{onEnd:n}=this.props;t.preventDefault(),this.detach(),n()}handleCancel(t){const{onCancel:n}=this.props;t.preventDefault(),this.detach(),n()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}un.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=cn,onActivation:o}=t,{active:i}=n;const{code:s}=e.nativeEvent;if(r.start.includes(s)){const a=i.activatorNode.current;return a&&e.target!==a?!1:(e.preventDefault(),o==null||o({event:e.nativeEvent}),!0)}return!1}}];function Ft(e){return!!(e&&"distance"in e)}function $t(e){return!!(e&&"delay"in e)}class Ct{constructor(t,n,r){var o;r===void 0&&(r=dr(t.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=t,this.events=n;const{event:i}=t,{target:s}=i;this.props=t,this.events=n,this.document=xe(s),this.documentListeners=new Ne(this.document),this.listeners=new Ne(r),this.windowListeners=new Ne(z(s)),this.initialCoordinates=(o=Ze(i))!=null?o:q,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:t,props:{options:{activationConstraint:n,bypassActivationConstraint:r}}}=this;if(this.listeners.add(t.move.name,this.handleMove,{passive:!1}),this.listeners.add(t.end.name,this.handleEnd),t.cancel&&this.listeners.add(t.cancel.name,this.handleCancel),this.windowListeners.add(U.Resize,this.handleCancel),this.windowListeners.add(U.DragStart,Bt),this.windowListeners.add(U.VisibilityChange,this.handleCancel),this.windowListeners.add(U.ContextMenu,Bt),this.documentListeners.add(U.Keydown,this.handleKeydown),n){if(r!=null&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if($t(n)){this.timeoutId=setTimeout(this.handleStart,n.delay),this.handlePending(n);return}if(Ft(n)){this.handlePending(n);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(t,n){const{active:r,onPending:o}=this.props;o(r,t,this.initialCoordinates,n)}handleStart(){const{initialCoordinates:t}=this,{onStart:n}=this.props;t&&(this.activated=!0,this.documentListeners.add(U.Click,fr,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(U.SelectionChange,this.removeTextSelection),n(t))}handleMove(t){var n;const{activated:r,initialCoordinates:o,props:i}=this,{onMove:s,options:{activationConstraint:a}}=i;if(!o)return;const l=(n=Ze(t))!=null?n:q,u=ze(o,l);if(!r&&a){if(Ft(a)){if(a.tolerance!=null&&ht(u,a.tolerance))return this.handleCancel();if(ht(u,a.distance))return this.handleStart()}if($t(a)&&ht(u,a.tolerance))return this.handleCancel();this.handlePending(a,u);return}t.cancelable&&t.preventDefault(),s(l)}handleEnd(){const{onAbort:t,onEnd:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleCancel(){const{onAbort:t,onCancel:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleKeydown(t){t.code===D.Esc&&this.handleCancel()}removeTextSelection(){var t;(t=this.document.getSelection())==null||t.removeAllRanges()}}const hr={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class dn extends Ct{constructor(t){const{event:n}=t,r=xe(n.target);super(t,hr,r)}}dn.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!n.isPrimary||n.button!==0?!1:(r==null||r({event:n}),!0)}}];const pr={move:{name:"mousemove"},end:{name:"mouseup"}};var bt;(function(e){e[e.RightClick=2]="RightClick"})(bt||(bt={}));class vr extends Ct{constructor(t){super(t,pr,xe(t.event.target))}}vr.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button===bt.RightClick?!1:(r==null||r({event:n}),!0)}}];const pt={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class br extends Ct{constructor(t){super(t,pt)}static setup(){return window.addEventListener(pt.move.name,t,{capture:!1,passive:!1}),function(){window.removeEventListener(pt.move.name,t)};function t(){}}}br.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return o.length>1?!1:(r==null||r({event:n}),!0)}}];var Le;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(Le||(Le={}));var tt;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(tt||(tt={}));function mr(e){let{acceleration:t,activator:n=Le.Pointer,canScroll:r,draggingRect:o,enabled:i,interval:s=5,order:a=tt.TreeOrder,pointerCoordinates:l,scrollableAncestors:u,scrollableAncestorRects:f,delta:d,threshold:h}=e;const g=wr({delta:d,disabled:!i}),[x,p]=Ln(),v=c.useRef({x:0,y:0}),m=c.useRef({x:0,y:0}),y=c.useMemo(()=>{switch(n){case Le.Pointer:return l?{top:l.y,bottom:l.y,left:l.x,right:l.x}:null;case Le.DraggableRect:return o}},[n,o,l]),b=c.useRef(null),S=c.useCallback(()=>{const E=b.current;if(!E)return;const w=v.current.x*m.current.x,C=v.current.y*m.current.y;E.scrollBy(w,C)},[]),R=c.useMemo(()=>a===tt.TreeOrder?[...u].reverse():u,[a,u]);c.useEffect(()=>{if(!i||!u.length||!y){p();return}for(const E of R){if((r==null?void 0:r(E))===!1)continue;const w=u.indexOf(E),C=f[w];if(!C)continue;const{direction:O,speed:T}=sr(E,C,y,t,h);for(const M of["x","y"])g[M][O[M]]||(T[M]=0,O[M]=0);if(T.x>0||T.y>0){p(),b.current=E,x(S,s),v.current=T,m.current=O;return}}v.current={x:0,y:0},m.current={x:0,y:0},p()},[t,S,r,p,i,s,JSON.stringify(y),JSON.stringify(g),x,u,R,f,JSON.stringify(h)])}const yr={x:{[L.Backward]:!1,[L.Forward]:!1},y:{[L.Backward]:!1,[L.Forward]:!1}};function wr(e){let{delta:t,disabled:n}=e;const r=Qe(t);return $e(o=>{if(n||!r||!o)return yr;const i={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[L.Backward]:o.x[L.Backward]||i.x===-1,[L.Forward]:o.x[L.Forward]||i.x===1},y:{[L.Backward]:o.y[L.Backward]||i.y===-1,[L.Forward]:o.y[L.Forward]||i.y===1}}},[n,t,r])}function xr(e,t){const n=t!=null?e.get(t):void 0,r=n?n.node.current:null;return $e(o=>{var i;return t==null?null:(i=r??o)!=null?i:null},[r,t])}function Cr(e,t){return c.useMemo(()=>e.reduce((n,r)=>{const{sensor:o}=r,i=o.activators.map(s=>({eventName:s.eventName,handler:t(s.handler,r)}));return[...n,...i]},[]),[e,t])}var Be;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(Be||(Be={}));var mt;(function(e){e.Optimized="optimized"})(mt||(mt={}));const Xt=new Map;function Dr(e,t){let{dragging:n,dependencies:r,config:o}=t;const[i,s]=c.useState(null),{frequency:a,measure:l,strategy:u}=o,f=c.useRef(e),d=v(),h=Pe(d),g=c.useCallback(function(m){m===void 0&&(m=[]),!h.current&&s(y=>y===null?m:y.concat(m.filter(b=>!y.includes(b))))},[h]),x=c.useRef(null),p=$e(m=>{if(d&&!n)return Xt;if(!m||m===Xt||f.current!==e||i!=null){const y=new Map;for(let b of e){if(!b)continue;if(i&&i.length>0&&!i.includes(b.id)&&b.rect.current){y.set(b.id,b.rect.current);continue}const S=b.node.current,R=S?new xt(l(S),S):null;b.rect.current=R,R&&y.set(b.id,R)}return y}return m},[e,i,n,d,l]);return c.useEffect(()=>{f.current=e},[e]),c.useEffect(()=>{d||g()},[n,d]),c.useEffect(()=>{i&&i.length>0&&s(null)},[JSON.stringify(i)]),c.useEffect(()=>{d||typeof a!="number"||x.current!==null||(x.current=setTimeout(()=>{g(),x.current=null},a))},[a,d,g,...r]),{droppableRects:p,measureDroppableContainers:g,measuringScheduled:i!=null};function v(){switch(u){case Be.Always:return!1;case Be.BeforeDragging:return n;default:return!n}}}function Dt(e,t){return $e(n=>e?n||(typeof t=="function"?t(e):e):null,[t,e])}function Sr(e,t){return Dt(e,t)}function Rr(e){let{callback:t,disabled:n}=e;const r=ot(t),o=c.useMemo(()=>{if(n||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:i}=window;return new i(r)},[r,n]);return c.useEffect(()=>()=>o==null?void 0:o.disconnect(),[o]),o}function at(e){let{callback:t,disabled:n}=e;const r=ot(t),o=c.useMemo(()=>{if(n||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:i}=window;return new i(r)},[n]);return c.useEffect(()=>()=>o==null?void 0:o.disconnect(),[o]),o}function Er(e){return new xt(Ce(e),e)}function jt(e,t,n){t===void 0&&(t=Er);const[r,o]=c.useState(null);function i(){o(l=>{if(!e)return null;if(e.isConnected===!1){var u;return(u=l??n)!=null?u:null}const f=t(e);return JSON.stringify(l)===JSON.stringify(f)?l:f})}const s=Rr({callback(l){if(e)for(const u of l){const{type:f,target:d}=u;if(f==="childList"&&d instanceof HTMLElement&&d.contains(e)){i();break}}}}),a=at({callback:i});return V(()=>{i(),e?(a==null||a.observe(e),s==null||s.observe(document.body,{childList:!0,subtree:!0})):(a==null||a.disconnect(),s==null||s.disconnect())},[e]),r}function Ar(e){const t=Dt(e);return Zt(e,t)}const Yt=[];function Mr(e){const t=c.useRef(e),n=$e(r=>e?r&&r!==Yt&&e&&t.current&&e.parentNode===t.current.parentNode?r:st(e):Yt,[e]);return c.useEffect(()=>{t.current=e},[e]),n}function Or(e){const[t,n]=c.useState(null),r=c.useRef(e),o=c.useCallback(i=>{const s=gt(i.target);s&&n(a=>a?(a.set(s,vt(s)),new Map(a)):null)},[]);return c.useEffect(()=>{const i=r.current;if(e!==i){s(i);const a=e.map(l=>{const u=gt(l);return u?(u.addEventListener("scroll",o,{passive:!0}),[u,vt(u)]):null}).filter(l=>l!=null);n(a.length?new Map(a):null),r.current=e}return()=>{s(e),s(i)};function s(a){a.forEach(l=>{const u=gt(l);u==null||u.removeEventListener("scroll",o)})}},[o,e]),c.useMemo(()=>e.length?t?Array.from(t.values()).reduce((i,s)=>ye(i,s),q):an(e):q,[e,t])}function Kt(e,t){t===void 0&&(t=[]);const n=c.useRef(null);return c.useEffect(()=>{n.current=null},t),c.useEffect(()=>{const r=e!==q;r&&!n.current&&(n.current=e),!r&&n.current&&(n.current=null)},[e]),n.current?ze(e,n.current):q}function Ir(e){c.useEffect(()=>{if(!rt)return;const t=e.map(n=>{let{sensor:r}=n;return r.setup==null?void 0:r.setup()});return()=>{for(const n of t)n==null||n()}},e.map(t=>{let{sensor:n}=t;return n}))}function Tr(e,t){return c.useMemo(()=>e.reduce((n,r)=>{let{eventName:o,handler:i}=r;return n[o]=s=>{i(s,t)},n},{}),[e,t])}function fn(e){return c.useMemo(()=>e?nr(e):null,[e])}const Ut=[];function Nr(e,t){t===void 0&&(t=Ce);const[n]=e,r=fn(n?z(n):null),[o,i]=c.useState(Ut);function s(){i(()=>e.length?e.map(l=>on(l)?r:new xt(t(l),l)):Ut)}const a=at({callback:s});return V(()=>{a==null||a.disconnect(),s(),e.forEach(l=>a==null?void 0:a.observe(l))},[e]),o}function gn(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return Fe(t)?t:e}function Lr(e){let{measure:t}=e;const[n,r]=c.useState(null),o=c.useCallback(u=>{for(const{target:f}of u)if(Fe(f)){r(d=>{const h=t(f);return d?{...d,width:h.width,height:h.height}:h});break}},[t]),i=at({callback:o}),s=c.useCallback(u=>{const f=gn(u);i==null||i.disconnect(),f&&(i==null||i.observe(f)),r(f?t(f):null)},[t,i]),[a,l]=_e(s);return c.useMemo(()=>({nodeRef:a,rect:n,setRef:l}),[n,a,l])}const kr=[{sensor:dn,options:{}},{sensor:un,options:{}}],Pr={current:{}},Je={draggable:{measure:zt},droppable:{measure:zt,strategy:Be.WhileDragging,frequency:mt.Optimized},dragOverlay:{measure:Ce}};class ke extends Map{get(t){var n;return t!=null&&(n=super.get(t))!=null?n:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(t=>{let{disabled:n}=t;return!n})}getNodeFor(t){var n,r;return(n=(r=this.get(t))==null?void 0:r.node.current)!=null?n:void 0}}const zr={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new ke,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:et},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Je,measureDroppableContainers:et,windowRect:null,measuringScheduled:!1},hn={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:et,draggableNodes:new Map,over:null,measureDroppableContainers:et},je=c.createContext(hn),pn=c.createContext(zr);function Br(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new ke}}}function Fr(e,t){switch(t.type){case N.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case N.DragMove:return e.draggable.active==null?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case N.DragEnd:case N.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case N.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new ke(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case N.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const s=new ke(e.droppable.containers);return s.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:s}}}case N.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new ke(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function $r(e){let{disabled:t}=e;const{active:n,activatorEvent:r,draggableNodes:o}=c.useContext(je),i=Qe(r),s=Qe(n==null?void 0:n.id);return c.useEffect(()=>{if(!t&&!r&&i&&s!=null){if(!it(i)||document.activeElement===i.target)return;const a=o.get(s);if(!a)return;const{activatorNode:l,node:u}=a;if(!l.current&&!u.current)return;requestAnimationFrame(()=>{for(const f of[l.current,u.current]){if(!f)continue;const d=zn(f);if(d){d.focus();break}}})}},[r,t,o,s,i]),null}function vn(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce((o,i)=>i({transform:o,...r}),n):n}function Xr(e){return c.useMemo(()=>({draggable:{...Je.draggable,...e==null?void 0:e.draggable},droppable:{...Je.droppable,...e==null?void 0:e.droppable},dragOverlay:{...Je.dragOverlay,...e==null?void 0:e.dragOverlay}}),[e==null?void 0:e.draggable,e==null?void 0:e.droppable,e==null?void 0:e.dragOverlay])}function jr(e){let{activeNode:t,measure:n,initialRect:r,config:o=!0}=e;const i=c.useRef(!1),{x:s,y:a}=typeof o=="boolean"?{x:o,y:o}:o;V(()=>{if(!s&&!a||!t){i.current=!1;return}if(i.current||!r)return;const u=t==null?void 0:t.node.current;if(!u||u.isConnected===!1)return;const f=n(u),d=Zt(f,r);if(s||(d.x=0),a||(d.y=0),i.current=!0,Math.abs(d.x)>0||Math.abs(d.y)>0){const h=tn(u);h&&h.scrollBy({top:d.y,left:d.x})}},[t,s,a,r,n])}const lt=c.createContext({...q,scaleX:1,scaleY:1});var ue;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(ue||(ue={}));const So=c.memo(function(t){var n,r,o,i;let{id:s,accessibility:a,autoScroll:l=!0,children:u,sensors:f=kr,collisionDetection:d=Jn,measuring:h,modifiers:g,...x}=t;const p=c.useReducer(Fr,void 0,Br),[v,m]=p,[y,b]=Yn(),[S,R]=c.useState(ue.Uninitialized),E=S===ue.Initialized,{draggable:{active:w,nodes:C,translate:O},droppable:{containers:T}}=v,M=w!=null?C.get(w):null,W=c.useRef({initial:null,translated:null}),H=c.useMemo(()=>{var P;return w!=null?{id:w,data:(P=M==null?void 0:M.data)!=null?P:Pr,rect:W}:null},[w,M]),G=c.useRef(null),[De,Ye]=c.useState(null),[F,Ke]=c.useState(null),Z=Pe(x,Object.values(x)),Se=Xe("DndDescribedBy",s),Ue=c.useMemo(()=>T.getEnabled(),[T]),B=Xr(h),{droppableRects:ee,measureDroppableContainers:de,measuringScheduled:Re}=Dr(Ue,{dragging:E,dependencies:[O.x,O.y],config:B.droppable}),Y=xr(C,w),We=c.useMemo(()=>F?Ze(F):null,[F]),oe=Tn(),te=Sr(Y,B.draggable.measure);jr({activeNode:w!=null?C.get(w):null,config:oe.layoutShiftCompensation,initialRect:te,measure:B.draggable.measure});const A=jt(Y,B.draggable.measure,te),Ee=jt(Y?Y.parentElement:null),J=c.useRef({activatorEvent:null,active:null,activeNode:Y,collisionRect:null,collisions:null,droppableRects:ee,draggableNodes:C,draggingNode:null,draggingNodeRect:null,droppableContainers:T,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ge=T.getNodeFor((n=J.current.over)==null?void 0:n.id),ne=Lr({measure:B.dragOverlay.measure}),he=(r=ne.nodeRef.current)!=null?r:Y,pe=E?(o=ne.rect)!=null?o:A:null,St=!!(ne.nodeRef.current&&ne.rect),Rt=Ar(St?null:A),ct=fn(he?z(he):null),ie=Mr(E?ge??Y:null),He=Nr(ie),Ve=vn(g,{transform:{x:O.x-Rt.x,y:O.y-Rt.y,scaleX:1,scaleY:1},activatorEvent:F,active:H,activeNodeRect:A,containerNodeRect:Ee,draggingNodeRect:pe,over:J.current.over,overlayNodeRect:ne.rect,scrollableAncestors:ie,scrollableAncestorRects:He,windowRect:ct}),Et=We?ye(We,O):null,At=Or(ie),Sn=Kt(At),Rn=Kt(At,[A]),ve=ye(Ve,Sn),be=pe?Zn(pe,Ve):null,Ae=H&&be?d({active:H,collisionRect:be,droppableRects:ee,droppableContainers:Ue,pointerCoordinates:Et}):null,Mt=Qt(Ae,"id"),[se,Ot]=c.useState(null),En=St?Ve:ye(Ve,Rn),An=_n(En,(i=se==null?void 0:se.rect)!=null?i:null,A),ut=c.useRef(null),It=c.useCallback((P,$)=>{let{sensor:X,options:ae}=$;if(G.current==null)return;const K=C.get(G.current);if(!K)return;const j=P.nativeEvent,_=new X({active:G.current,activeNode:K,event:j,options:ae,context:J,onAbort(k){if(!C.get(k))return;const{onDragAbort:Q}=Z.current,re={id:k};Q==null||Q(re),y({type:"onDragAbort",event:re})},onPending(k,le,Q,re){if(!C.get(k))return;const{onDragPending:Oe}=Z.current,ce={id:k,constraint:le,initialCoordinates:Q,offset:re};Oe==null||Oe(ce),y({type:"onDragPending",event:ce})},onStart(k){const le=G.current;if(le==null)return;const Q=C.get(le);if(!Q)return;const{onDragStart:re}=Z.current,Me={activatorEvent:j,active:{id:le,data:Q.data,rect:W}};Te.unstable_batchedUpdates(()=>{re==null||re(Me),R(ue.Initializing),m({type:N.DragStart,initialCoordinates:k,active:le}),y({type:"onDragStart",event:Me}),Ye(ut.current),Ke(j)})},onMove(k){m({type:N.DragMove,coordinates:k})},onEnd:me(N.DragEnd),onCancel:me(N.DragCancel)});ut.current=_;function me(k){return async function(){const{active:Q,collisions:re,over:Me,scrollAdjustedTranslate:Oe}=J.current;let ce=null;if(Q&&Oe){const{cancelDrop:Ie}=Z.current;ce={activatorEvent:j,active:Q,collisions:re,delta:Oe,over:Me},k===N.DragEnd&&typeof Ie=="function"&&await Promise.resolve(Ie(ce))&&(k=N.DragCancel)}G.current=null,Te.unstable_batchedUpdates(()=>{m({type:k}),R(ue.Uninitialized),Ot(null),Ye(null),Ke(null),ut.current=null;const Ie=k===N.DragEnd?"onDragEnd":"onDragCancel";if(ce){const dt=Z.current[Ie];dt==null||dt(ce),y({type:Ie,event:ce})}})}}},[C]),Mn=c.useCallback((P,$)=>(X,ae)=>{const K=X.nativeEvent,j=C.get(ae);if(G.current!==null||!j||K.dndKit||K.defaultPrevented)return;const _={active:j};P(X,$.options,_)===!0&&(K.dndKit={capturedBy:$.sensor},G.current=ae,It(X,$))},[C,It]),Tt=Cr(f,Mn);Ir(f),V(()=>{A&&S===ue.Initializing&&R(ue.Initialized)},[A,S]),c.useEffect(()=>{const{onDragMove:P}=Z.current,{active:$,activatorEvent:X,collisions:ae,over:K}=J.current;if(!$||!X)return;const j={active:$,activatorEvent:X,collisions:ae,delta:{x:ve.x,y:ve.y},over:K};Te.unstable_batchedUpdates(()=>{P==null||P(j),y({type:"onDragMove",event:j})})},[ve.x,ve.y]),c.useEffect(()=>{const{active:P,activatorEvent:$,collisions:X,droppableContainers:ae,scrollAdjustedTranslate:K}=J.current;if(!P||G.current==null||!$||!K)return;const{onDragOver:j}=Z.current,_=ae.get(Mt),me=_&&_.rect.current?{id:_.id,rect:_.rect.current,data:_.data,disabled:_.disabled}:null,k={active:P,activatorEvent:$,collisions:X,delta:{x:K.x,y:K.y},over:me};Te.unstable_batchedUpdates(()=>{Ot(me),j==null||j(k),y({type:"onDragOver",event:k})})},[Mt]),V(()=>{J.current={activatorEvent:F,active:H,activeNode:Y,collisionRect:be,collisions:Ae,droppableRects:ee,draggableNodes:C,draggingNode:he,draggingNodeRect:pe,droppableContainers:T,over:se,scrollableAncestors:ie,scrollAdjustedTranslate:ve},W.current={initial:pe,translated:be}},[H,Y,Ae,be,C,he,pe,ee,T,se,ie,ve]),mr({...oe,delta:O,draggingRect:be,pointerCoordinates:Et,scrollableAncestors:ie,scrollableAncestorRects:He});const On=c.useMemo(()=>({active:H,activeNode:Y,activeNodeRect:A,activatorEvent:F,collisions:Ae,containerNodeRect:Ee,dragOverlay:ne,draggableNodes:C,droppableContainers:T,droppableRects:ee,over:se,measureDroppableContainers:de,scrollableAncestors:ie,scrollableAncestorRects:He,measuringConfiguration:B,measuringScheduled:Re,windowRect:ct}),[H,Y,A,F,Ae,Ee,ne,C,T,ee,se,de,ie,He,B,Re,ct]),In=c.useMemo(()=>({activatorEvent:F,activators:Tt,active:H,activeNodeRect:A,ariaDescribedById:{draggable:Se},dispatch:m,draggableNodes:C,over:se,measureDroppableContainers:de}),[F,Tt,H,A,m,Se,C,se,de]);return I.createElement(Gt.Provider,{value:b},I.createElement(je.Provider,{value:In},I.createElement(pn.Provider,{value:On},I.createElement(lt.Provider,{value:An},u)),I.createElement($r,{disabled:(a==null?void 0:a.restoreFocus)===!1})),I.createElement(Wn,{...a,hiddenTextDescribedById:Se}));function Tn(){const P=(De==null?void 0:De.autoScrollEnabled)===!1,$=typeof l=="object"?l.enabled===!1:l===!1,X=E&&!P&&!$;return typeof l=="object"?{...l,enabled:X}:{enabled:X}}}),Yr=c.createContext(null),Wt="button",Kr="Draggable";function Ur(e){let{id:t,data:n,disabled:r=!1,attributes:o}=e;const i=Xe(Kr),{activators:s,activatorEvent:a,active:l,activeNodeRect:u,ariaDescribedById:f,draggableNodes:d,over:h}=c.useContext(je),{role:g=Wt,roleDescription:x="draggable",tabIndex:p=0}=o??{},v=(l==null?void 0:l.id)===t,m=c.useContext(v?lt:Yr),[y,b]=_e(),[S,R]=_e(),E=Tr(s,t),w=Pe(n);V(()=>(d.set(t,{id:t,key:i,node:y,activatorNode:S,data:w}),()=>{const O=d.get(t);O&&O.key===i&&d.delete(t)}),[d,t]);const C=c.useMemo(()=>({role:g,tabIndex:p,"aria-disabled":r,"aria-pressed":v&&g===Wt?!0:void 0,"aria-roledescription":x,"aria-describedby":f.draggable}),[r,g,p,v,x,f.draggable]);return{active:l,activatorEvent:a,activeNodeRect:u,attributes:C,isDragging:v,listeners:r?void 0:E,node:y,over:h,setNodeRef:b,setActivatorNodeRef:R,transform:m}}function bn(){return c.useContext(pn)}const Wr="Droppable",Hr={timeout:25};function Vr(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:o}=e;const i=Xe(Wr),{active:s,dispatch:a,over:l,measureDroppableContainers:u}=c.useContext(je),f=c.useRef({disabled:n}),d=c.useRef(!1),h=c.useRef(null),g=c.useRef(null),{disabled:x,updateMeasurementsFor:p,timeout:v}={...Hr,...o},m=Pe(p??r),y=c.useCallback(()=>{if(!d.current){d.current=!0;return}g.current!=null&&clearTimeout(g.current),g.current=setTimeout(()=>{u(Array.isArray(m.current)?m.current:[m.current]),g.current=null},v)},[v]),b=at({callback:y,disabled:x||!s}),S=c.useCallback((C,O)=>{b&&(O&&(b.unobserve(O),d.current=!1),C&&b.observe(C))},[b]),[R,E]=_e(S),w=Pe(t);return c.useEffect(()=>{!b||!R.current||(b.disconnect(),d.current=!1,b.observe(R.current))},[R,b]),c.useEffect(()=>(a({type:N.RegisterDroppable,element:{id:r,key:i,disabled:n,node:R,rect:h,data:w}}),()=>a({type:N.UnregisterDroppable,key:i,id:r})),[r]),c.useEffect(()=>{n!==f.current.disabled&&(a({type:N.SetDroppableDisabled,id:r,key:i,disabled:n}),f.current.disabled=n)},[r,i,n,a]),{active:s,rect:h,isOver:(l==null?void 0:l.id)===r,node:R,over:l,setNodeRef:E}}function qr(e){let{animation:t,children:n}=e;const[r,o]=c.useState(null),[i,s]=c.useState(null),a=Qe(n);return!n&&!r&&a&&o(a),V(()=>{if(!i)return;const l=r==null?void 0:r.key,u=r==null?void 0:r.props.id;if(l==null||u==null){o(null);return}Promise.resolve(t(u,i)).then(()=>{o(null)})},[t,r,i]),I.createElement(I.Fragment,null,n,r?c.cloneElement(r,{ref:s}):null)}const Gr={x:0,y:0,scaleX:1,scaleY:1};function Jr(e){let{children:t}=e;return I.createElement(je.Provider,{value:hn},I.createElement(lt.Provider,{value:Gr},t))}const _r={position:"fixed",touchAction:"none"},Qr=e=>it(e)?"transform 250ms ease":void 0,Zr=c.forwardRef((e,t)=>{let{as:n,activatorEvent:r,adjustScale:o,children:i,className:s,rect:a,style:l,transform:u,transition:f=Qr}=e;if(!a)return null;const d=o?u:{...u,scaleX:1,scaleY:1},h={..._r,width:a.width,height:a.height,top:a.top,left:a.left,transform:fe.Transform.toString(d),transformOrigin:o&&r?Hn(r,a):void 0,transition:typeof f=="function"?f(r):f,...l};return I.createElement(n,{className:s,style:h,ref:t},i)}),eo=e=>t=>{let{active:n,dragOverlay:r}=t;const o={},{styles:i,className:s}=e;if(i!=null&&i.active)for(const[a,l]of Object.entries(i.active))l!==void 0&&(o[a]=n.node.style.getPropertyValue(a),n.node.style.setProperty(a,l));if(i!=null&&i.dragOverlay)for(const[a,l]of Object.entries(i.dragOverlay))l!==void 0&&r.node.style.setProperty(a,l);return s!=null&&s.active&&n.node.classList.add(s.active),s!=null&&s.dragOverlay&&r.node.classList.add(s.dragOverlay),function(){for(const[l,u]of Object.entries(o))n.node.style.setProperty(l,u);s!=null&&s.active&&n.node.classList.remove(s.active)}},to=e=>{let{transform:{initial:t,final:n}}=e;return[{transform:fe.Transform.toString(t)},{transform:fe.Transform.toString(n)}]},no={duration:250,easing:"ease",keyframes:to,sideEffects:eo({styles:{active:{opacity:"0"}}})};function ro(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:o}=e;return ot((i,s)=>{if(t===null)return;const a=n.get(i);if(!a)return;const l=a.node.current;if(!l)return;const u=gn(s);if(!u)return;const{transform:f}=z(s).getComputedStyle(s),d=en(f);if(!d)return;const h=typeof t=="function"?t:oo(t);return ln(l,o.draggable.measure),h({active:{id:i,data:a.data,node:l,rect:o.draggable.measure(l)},draggableNodes:n,dragOverlay:{node:s,rect:o.dragOverlay.measure(u)},droppableContainers:r,measuringConfiguration:o,transform:d})})}function oo(e){const{duration:t,easing:n,sideEffects:r,keyframes:o}={...no,...e};return i=>{let{active:s,dragOverlay:a,transform:l,...u}=i;if(!t)return;const f={x:a.rect.left-s.rect.left,y:a.rect.top-s.rect.top},d={scaleX:l.scaleX!==1?s.rect.width*l.scaleX/a.rect.width:1,scaleY:l.scaleY!==1?s.rect.height*l.scaleY/a.rect.height:1},h={x:l.x-f.x,y:l.y-f.y,...d},g=o({...u,active:s,dragOverlay:a,transform:{initial:l,final:h}}),[x]=g,p=g[g.length-1];if(JSON.stringify(x)===JSON.stringify(p))return;const v=r==null?void 0:r({active:s,dragOverlay:a,...u}),m=a.node.animate(g,{duration:t,easing:n,fill:"forwards"});return new Promise(y=>{m.onfinish=()=>{v==null||v(),y()}})}}let Ht=0;function io(e){return c.useMemo(()=>{if(e!=null)return Ht++,Ht},[e])}const Ro=I.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:o,transition:i,modifiers:s,wrapperElement:a="div",className:l,zIndex:u=999}=e;const{activatorEvent:f,active:d,activeNodeRect:h,containerNodeRect:g,draggableNodes:x,droppableContainers:p,dragOverlay:v,over:m,measuringConfiguration:y,scrollableAncestors:b,scrollableAncestorRects:S,windowRect:R}=bn(),E=c.useContext(lt),w=io(d==null?void 0:d.id),C=vn(s,{activatorEvent:f,active:d,activeNodeRect:h,containerNodeRect:g,draggingNodeRect:v.rect,over:m,overlayNodeRect:v.rect,scrollableAncestors:b,scrollableAncestorRects:S,transform:E,windowRect:R}),O=Dt(h),T=ro({config:r,draggableNodes:x,droppableContainers:p,measuringConfiguration:y}),M=O?v.setRef:void 0;return I.createElement(Jr,null,I.createElement(qr,{animation:T},d&&w?I.createElement(Zr,{key:w,id:d.id,ref:M,as:a,activatorEvent:f,adjustScale:t,className:l,transition:i,rect:O,style:{zIndex:u,...o},transform:C},n):null))});function mn(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function so(e,t){return e.reduce((n,r,o)=>{const i=t.get(r);return i&&(n[o]=i),n},Array(e.length))}function qe(e){return e!==null&&e>=0}function ao(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function lo(e){return typeof e=="boolean"?{draggable:e,droppable:e}:e}const yn=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=mn(t,r,n),s=t[o],a=i[o];return!a||!s?null:{x:a.left-s.left,y:a.top-s.top,scaleX:a.width/s.width,scaleY:a.height/s.height}},Ge={scaleX:1,scaleY:1},Eo=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:i,overIndex:s}=e;const a=(t=i[n])!=null?t:r;if(!a)return null;if(o===n){const u=i[s];return u?{x:0,y:n<s?u.top+u.height-(a.top+a.height):u.top-a.top,...Ge}:null}const l=co(i,o,n);return o>n&&o<=s?{x:0,y:-a.height-l,...Ge}:o<n&&o>=s?{x:0,y:a.height+l,...Ge}:{x:0,y:0,...Ge}};function co(e,t,n){const r=e[t],o=e[t-1],i=e[t+1];return r?n<t?o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0:0}const wn="Sortable",xn=I.createContext({activeIndex:-1,containerId:wn,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:yn,disabled:{draggable:!1,droppable:!1}});function Ao(e){let{children:t,id:n,items:r,strategy:o=yn,disabled:i=!1}=e;const{active:s,dragOverlay:a,droppableRects:l,over:u,measureDroppableContainers:f}=bn(),d=Xe(wn,n),h=a.rect!==null,g=c.useMemo(()=>r.map(E=>typeof E=="object"&&"id"in E?E.id:E),[r]),x=s!=null,p=s?g.indexOf(s.id):-1,v=u?g.indexOf(u.id):-1,m=c.useRef(g),y=!ao(g,m.current),b=v!==-1&&p===-1||y,S=lo(i);V(()=>{y&&x&&f(g)},[y,g,x,f]),c.useEffect(()=>{m.current=g},[g]);const R=c.useMemo(()=>({activeIndex:p,containerId:d,disabled:S,disableTransforms:b,items:g,overIndex:v,useDragOverlay:h,sortedRects:so(g,l),strategy:o}),[p,d,S.draggable,S.droppable,b,g,v,l,h,o]);return I.createElement(xn.Provider,{value:R},t)}const uo=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return mn(n,r,o).indexOf(t)},fo=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:s,previousItems:a,previousContainerId:l,transition:u}=e;return!u||!r||a!==i&&o===s?!1:n?!0:s!==o&&t===l},go={duration:200,easing:"ease"},Cn="transform",ho=fe.Transition.toString({property:Cn,duration:0,easing:"linear"}),po={roleDescription:"sortable"};function vo(e){let{disabled:t,index:n,node:r,rect:o}=e;const[i,s]=c.useState(null),a=c.useRef(n);return V(()=>{if(!t&&n!==a.current&&r.current){const l=o.current;if(l){const u=Ce(r.current,{ignoreTransform:!0}),f={x:l.left-u.left,y:l.top-u.top,scaleX:l.width/u.width,scaleY:l.height/u.height};(f.x||f.y)&&s(f)}}n!==a.current&&(a.current=n)},[t,n,r,o]),c.useEffect(()=>{i&&s(null)},[i]),i}function Mo(e){let{animateLayoutChanges:t=fo,attributes:n,disabled:r,data:o,getNewIndex:i=uo,id:s,strategy:a,resizeObserverConfig:l,transition:u=go}=e;const{items:f,containerId:d,activeIndex:h,disabled:g,disableTransforms:x,sortedRects:p,overIndex:v,useDragOverlay:m,strategy:y}=c.useContext(xn),b=bo(r,g),S=f.indexOf(s),R=c.useMemo(()=>({sortable:{containerId:d,index:S,items:f},...o}),[d,o,S,f]),E=c.useMemo(()=>f.slice(f.indexOf(s)),[f,s]),{rect:w,node:C,isOver:O,setNodeRef:T}=Vr({id:s,data:R,disabled:b.droppable,resizeObserverConfig:{updateMeasurementsFor:E,...l}}),{active:M,activatorEvent:W,activeNodeRect:H,attributes:G,setNodeRef:De,listeners:Ye,isDragging:F,over:Ke,setActivatorNodeRef:Z,transform:Se}=Ur({id:s,data:R,attributes:{...po,...n},disabled:b.draggable}),Ue=Nn(T,De),B=!!M,ee=B&&!x&&qe(h)&&qe(v),de=!m&&F,Re=de&&ee?Se:null,We=ee?Re??(a??y)({rects:p,activeNodeRect:H,activeIndex:h,overIndex:v,index:S}):null,oe=qe(h)&&qe(v)?i({id:s,items:f,activeIndex:h,overIndex:v}):S,te=M==null?void 0:M.id,A=c.useRef({activeId:te,items:f,newIndex:oe,containerId:d}),Ee=f!==A.current.items,J=t({active:M,containerId:d,isDragging:F,isSorting:B,id:s,index:S,items:f,newIndex:A.current.newIndex,previousItems:A.current.items,previousContainerId:A.current.containerId,transition:u,wasDragging:A.current.activeId!=null}),ge=vo({disabled:!J,index:S,node:C,rect:w});return c.useEffect(()=>{B&&A.current.newIndex!==oe&&(A.current.newIndex=oe),d!==A.current.containerId&&(A.current.containerId=d),f!==A.current.items&&(A.current.items=f)},[B,oe,d,f]),c.useEffect(()=>{if(te===A.current.activeId)return;if(te&&!A.current.activeId){A.current.activeId=te;return}const he=setTimeout(()=>{A.current.activeId=te},50);return()=>clearTimeout(he)},[te]),{active:M,activeIndex:h,attributes:G,data:R,rect:w,index:S,newIndex:oe,items:f,isOver:O,isSorting:B,isDragging:F,listeners:Ye,node:C,overIndex:v,over:Ke,setNodeRef:Ue,setActivatorNodeRef:Z,setDroppableNodeRef:T,setDraggableNodeRef:De,transform:ge??We,transition:ne()};function ne(){if(ge||Ee&&A.current.newIndex===S)return ho;if(!(de&&!it(W)||!u)&&(B||J))return fe.Transition.toString({...u,property:Cn})}}function bo(e,t){var n,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}function nt(e){if(!e)return!1;const t=e.data.current;return!!(t&&"sortable"in t&&typeof t.sortable=="object"&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable)}const mo=[D.Down,D.Right,D.Up,D.Left],Oo=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:o,droppableContainers:i,over:s,scrollableAncestors:a}}=t;if(mo.includes(e.code)){if(e.preventDefault(),!n||!r)return;const l=[];i.getEnabled().forEach(d=>{if(!d||d!=null&&d.disabled)return;const h=o.get(d.id);if(h)switch(e.code){case D.Down:r.top<h.top&&l.push(d);break;case D.Up:r.top>h.top&&l.push(d);break;case D.Left:r.left>h.left&&l.push(d);break;case D.Right:r.left<h.left&&l.push(d);break}});const u=qn({active:n,collisionRect:r,droppableRects:o,droppableContainers:l,pointerCoordinates:null});let f=Qt(u,"id");if(f===(s==null?void 0:s.id)&&u.length>1&&(f=u[1].id),f!=null){const d=i.get(n.id),h=i.get(f),g=h?o.get(h.id):null,x=h==null?void 0:h.node.current;if(x&&g&&d&&h){const v=st(x).some((E,w)=>a[w]!==E),m=Dn(d,h),y=yo(d,h),b=v||!m?{x:0,y:0}:{x:y?r.width-g.width:0,y:y?r.height-g.height:0},S={x:g.left,y:g.top};return b.x&&b.y?S:ze(S,b)}}}};function Dn(e,t){return!nt(e)||!nt(t)?!1:e.data.current.sortable.containerId===t.data.current.sortable.containerId}function yo(e,t){return!nt(e)||!nt(t)||!Dn(e,t)?!1:e.data.current.sortable.index<t.data.current.sortable.index}export{Nt as C,So as D,un as K,Be as M,dn as P,Ao as S,xo as a,Ro as b,Mo as c,mn as d,eo as e,D as f,Do as g,qn as h,Qt as i,no as j,yn as r,Oo as s,Co as u,Eo as v};
