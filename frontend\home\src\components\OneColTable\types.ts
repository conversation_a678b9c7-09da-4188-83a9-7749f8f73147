import { HTMLAttributes, ReactNode } from 'react';
import { Row, ColumnDef, SortingState } from '@tanstack/react-table';

/**
 * Base props extending HTML attributes
 */
export interface BaseProps extends HTMLAttributes<HTMLDivElement> {
  /** Optional CSS class to be merged */
  className?: string;
  /** Children components/elements */
  children?: ReactNode;
}

/**
 * OneColTable variant props from CVA
 */
export interface VariantProps {
  /** Table size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Table style variant */
  variant?: 'primary' | 'secondary' | 'outline';
}

/**
 * State-related props
 */
export interface StateProps {
  /** Whether the component is in a loading state */
  loading?: boolean;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Error message or component if any */
  error?: string | ReactNode;
}

/**
 * Event handler props
 */
export interface EventHandlerProps {
  /** Row click event handler */
  onRowClick?: (item: TableItem, index: number) => void;
  /** Row hover event handler */
  onRowHover?: (item: TableItem, index: number) => void;
}

/**
 * Animation-related props
 */
export interface AnimationProps {
  /** Whether to animate the component */
  animate?: boolean;
  /** Animation delay in seconds */
  animationDelay?: number;
}

/**
 * Virtualization props
 */
export interface VirtualizationProps {
  /** Enable virtualization for large datasets */
  virtualized?: boolean;
  /** Height for virtualized table container */
  height?: number;
  /** Width for virtualized table container */
  width?: number | string;
  /** Number of items to overscan (render off-screen) */
  overscan?: number;
}

/**
 * Skeleton loader props
 */
export interface SkeletonProps {
  /** Props to pass to the skeleton component */
  skeletonProps?: {
    /** Whether to use simplified skeleton version */
    simplified?: boolean;
    /** Number of rows to show in skeleton */
    rowCount?: number;
  };
}

/**
 * Data item structure for table
 */
export interface TableItem {
  /** Unique identifier for the item */
  id: string;
  /** Main content to display */
  content: ReactNode;
  /** Optional secondary content */
  secondaryContent?: ReactNode;
  /** Optional timestamp */
  timestamp?: string | Date;
  /** Additional data that may be passed to event handlers */
  [key: string]: unknown;
}

/**
 * TanStack Table column definition type
 */
export type TableColumnDef<T extends TableItem = TableItem> = ColumnDef<T>;

/**
 * TanStack Table row type
 */
export type TableRow<T extends TableItem = TableItem> = Row<T>;

/**
 * Sorting configuration for TanStack Table
 */
export interface SortingConfig {
  /** Initial sorting state */
  initialSorting?: SortingState;
  /** Available fields for sorting */
  sortableFields?: Array<{
    field: string;
    label: string;
  }>;
}

/**
 * Combined props interface for main component
 */
export interface OneColTableProps
  extends BaseProps,
    VariantProps,
    StateProps,
    EventHandlerProps,
    AnimationProps,
    VirtualizationProps,
    SkeletonProps {
  /** Items to display in the table */
  items: TableItem[];
  /** Custom header content */
  header?: ReactNode;
  /** Custom empty state */
  emptyState?: ReactNode;
  /** Limit the number of visible items */
  limit?: number;
  /** Whether to show the "Load more" button */
  showLoadMore?: boolean;
  /** Handler for the "Load more" button */
  onLoadMore?: () => void;
  /** Whether there are more items to load */
  hasMore?: boolean;
}
