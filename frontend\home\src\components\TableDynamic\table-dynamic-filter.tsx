'use client';

import * as React from 'react';
import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { Search, X, Filter, SlidersHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTableContext } from './table-dynamic-context';

/**
 * TableDynamicFilter props
 */
export interface TableDynamicFilterProps {
  /** Whether to show the global filter */
  showGlobalFilter?: boolean;
  /** Whether to show column filters */
  showColumnFilters?: boolean;
  /** Whether to debounce filter input */
  debounce?: boolean;
  /** Debounce time in ms */
  debounceTime?: number;
  /** Custom filter placeholder */
  placeholder?: string;
  /** Custom filter className */
  className?: string;
  /** Translation namespace */
  i18nNamespace?: string;
  /** Translation prefix for keys */
  i18nPrefix?: string;
}

/**
 * TableDynamicFilter component
 */
export const TableDynamicFilter: React.FC<TableDynamicFilterProps> = ({
  showGlobalFilter = true,
  showColumnFilters = true,
  debounce = true,
  debounceTime = 300,
  placeholder,
  className,
  i18nNamespace,
  i18nPrefix = 'tableDynamic.filter',
}) => {
  const { t } = useTranslation(i18nNamespace);

  const { table, globalFilter, setGlobalFilter } = useTableContext();

  // Local state for debounced filtering
  const [filterValue, setFilterValue] = useState(globalFilter);

  // Update filter value when globalFilter changes externally
  useEffect(() => {
    setFilterValue(globalFilter);
  }, [globalFilter]);

  // Debounced filtering
  useEffect(() => {
    if (!debounce) {
      setGlobalFilter(filterValue);
      return;
    }

    const handler = setTimeout(() => {
      setGlobalFilter(filterValue);
    }, debounceTime);

    return () => {
      clearTimeout(handler);
    };
  }, [filterValue, debounce, debounceTime, setGlobalFilter]);

  // Filter change handler
  const handleFilterChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFilterValue(e.target.value);
      if (!debounce) {
        setGlobalFilter(e.target.value);
      }
    },
    [debounce, setGlobalFilter],
  );

  // Clear filter
  const handleClearFilter = useCallback(() => {
    setFilterValue('');
    setGlobalFilter('');
  }, [setGlobalFilter]);

  // Active filter count
  const activeFiltersCount = table.getState().columnFilters.length;

  // Render nothing if no filters are shown
  if (!showGlobalFilter && !showColumnFilters) {
    return null;
  }

  return (
    <div className={cn('mb-4 flex items-center gap-2', className)}>
      {showGlobalFilter && (
        <div className="relative max-w-sm flex-1">
          <Search className="text-muted-foreground absolute top-1/2 left-2 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder={placeholder || t(`${i18nPrefix}.searchPlaceholder`)}
            value={filterValue || ''}
            onChange={handleFilterChange}
            className="pr-8 pl-8"
          />
          {filterValue && (
            <Button
              variant="ghost"
              size="icon"
              className="text-muted-foreground absolute top-0 right-0 h-full px-2"
              onClick={handleClearFilter}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">{t(`${i18nPrefix}.clearSearch`)}</span>
            </Button>
          )}
        </div>
      )}

      {showColumnFilters && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="ml-auto">
              <SlidersHorizontal className="mr-2 h-4 w-4" />
              {t(`${i18nPrefix}.filters`)}
              {activeFiltersCount > 0 && (
                <span className="bg-primary text-primary-foreground ml-1 rounded-full px-1.5 py-0.5 text-xs">
                  {activeFiltersCount}
                </span>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[200px]">
            <DropdownMenuLabel>{t(`${i18nPrefix}.filterColumns`)}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              {table
                .getAllColumns()
                .filter((column) => column.getCanFilter())
                .map((column) => {
                  const isFiltered = column.getIsFiltered();

                  return (
                    <DropdownMenuItem
                      key={column.id}
                      className="flex items-center justify-between space-x-2"
                    >
                      <span className="truncate">
                        {typeof column.columnDef.header === 'string'
                          ? column.columnDef.header
                          : column.id}
                      </span>
                      {isFiltered && <Filter className="text-primary h-4 w-4" />}
                    </DropdownMenuItem>
                  );
                })}
            </DropdownMenuGroup>

            {activeFiltersCount > 0 && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-destructive focus:text-destructive"
                  onClick={() => table.resetColumnFilters()}
                >
                  {t(`${i18nPrefix}.clearAllFilters`)}
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
};

TableDynamicFilter.displayName = 'TableDynamicFilter';
