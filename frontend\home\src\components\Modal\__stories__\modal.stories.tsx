import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Modal } from '../modal';
import { Button } from '@/components/ui/button';

const meta: Meta<typeof Modal> = {
  title: 'UI/Modal/Modal',
  component: Modal,
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg', 'xl', 'full'],
      description: 'Controls the size of the modal'
    },
    position: {
      control: 'select',
      options: ['default', 'top', 'center'],
      description: 'Controls the position and animation of the modal'
    },
    showCloseButton: {
      control: 'boolean',
      description: 'Whether to show the close button in the top right'
    },
    isOpen: {
      control: 'boolean',
      description: 'Controls whether the modal is open or closed'
    },
    title: {
      control: 'text',
      description: 'The title of the modal'
    },
    description: {
      control: 'text',
      description: 'The description text below the title'
    }
  },
  parameters: {
    docs: {
      description: {
        component: 'A reusable and accessible modal component built on shadcn/ui Dialog.'
      }
    },
    a11y: {
      config: {
        rules: [
          {
            id: 'aria-dialog-name',
            enabled: true
          },
          {
            id: 'role-dialog-name',
            enabled: true
          }
        ]
      }
    },
    actions: {
      handles: ['click .close', 'click .cancel', 'click .confirm']
    }
  }
};

export default meta;
type Story = StoryObj<typeof Modal>;

// Wrapper component to handle state
const ModalWrapper = (args: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  // Footer logic is handled internally in ModalWrapper
  const footer = args.footer !== false ? (
    <>
      <Button variant="outline" onClick={handleClose} className="cancel">Cancel</Button>
      <Button onClick={handleClose} className="confirm">Confirm</Button>
    </>
  ) : undefined;

  return (
    <div>
      <Button onClick={handleOpen}>Open Modal</Button>
      <Modal
        {...args}
        isOpen={isOpen}
        onClose={handleClose}
        footer={footer}
      >
        {args.children}
      </Modal>
    </div>
  );
};

// Base story
export const Default: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: 'Example Modal',
    description: 'This is a description of the modal content',
    size: 'default',
    position: 'default',
    showCloseButton: true,
    children: <p>This is the content of the modal. You can put any React components here.</p>,
  }
};

// Size variants
export const Small: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    title: 'Small Modal',
    size: 'sm'
  }
};

export const Large: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    title: 'Large Modal',
    size: 'lg'
  }
};

export const ExtraLarge: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    title: 'Extra Large Modal',
    size: 'xl'
  }
};

export const FullWidth: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    title: 'Full Width Modal',
    size: 'full'
  }
};

// Position variants
export const TopPosition: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    title: 'Top Position Modal',
    position: 'top'
  }
};

export const CenterPosition: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    title: 'Center Position Modal',
    position: 'center'
  }
};

// No header
export const NoHeader: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    title: undefined,
    description: undefined,
    children: <p>This modal has no title or description.</p>,
  }
};

// No footer
export const NoFooter: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
  }
};

// No close button
export const NoCloseButton: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    showCloseButton: false
  }
};

// Long content 
export const LongContent: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    children: (
      <div>
        <p>This modal contains a lot of content to demonstrate scrolling behavior.</p>
        {Array(10).fill(0).map((_, i) => (
          <p key={i}>
            This is paragraph {i + 1}. Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
            Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, eget aliquam
            nisl nunc eget nisl.
          </p>
        ))}
      </div>
    )
  }
};

// Form content
export const FormContent: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    ...Default.args,
    title: 'Form Modal',
    children: (
      <form className="space-y-4">
        <div className="grid gap-2">
          <label htmlFor="name" className="text-sm font-medium">Name</label>
          <input 
            id="name" 
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            placeholder="Enter your name" 
          />
        </div>
        <div className="grid gap-2">
          <label htmlFor="email" className="text-sm font-medium">Email</label>
          <input 
            id="email" 
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            placeholder="Enter your email" 
            type="email" 
          />
        </div>
      </form>
    )
  }
};