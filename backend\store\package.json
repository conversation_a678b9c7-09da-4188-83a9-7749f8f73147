{"author": "Medusa (https://medusajs.com)", "dependencies": {"@dnd-kit/core": "^6.3.1", "@medusajs/admin-sdk": "2.6.1", "@medusajs/cache-redis": "^2.7.0", "@medusajs/cli": "2.6.1", "@medusajs/framework": "2.6.1", "@medusajs/medusa": "2.6.1", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "awilix": "^8.0.1", "lucide-react": "^0.487.0", "pg": "^8.13.0", "react-dropzone": "^14.3.8"}, "description": "A starter for Medusa projects.", "devDependencies": {"@medusajs/test-utils": "2.6.1", "@mikro-orm/cli": "6.4.3", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/jest": "^29.5.13", "@types/node": "^20.0.0", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "jest": "^29.7.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.2", "typescript": "^5.6.2", "vite": "^5.2.11", "yalc": "^1.0.0-pre.53"}, "engines": {"node": ">=20"}, "keywords": ["sqlite", "postgres", "typescript", "ecommerce", "headless", "medusa"], "license": "MIT", "name": "store", "scripts": {"build": "medusa build", "dev": "medusa develop", "predeploy": "medusa db:migrate", "seed": "medusa exec ./src/scripts/seed.ts", "start": "medusa start", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit"}, "version": "0.0.1"}