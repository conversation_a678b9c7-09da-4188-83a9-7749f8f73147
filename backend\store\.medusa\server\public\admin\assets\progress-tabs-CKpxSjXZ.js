import{r as o,bB as $,bZ as w,b_ as H,bQ as z,j as u,bS as B,bF as v,b$ as K,c0 as q,bG as T,bH as Q,m,aU as U,cI as W}from"./index-Bwql5Dzz.js";var Z=Object.defineProperty,p=Object.getOwnPropertySymbols,C=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable,_=(e,t,a)=>t in e?Z(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,J=(e,t)=>{for(var a in t)C.call(t,a)&&_(e,a,t[a]);if(p)for(var a of p(t))E.call(t,a)&&_(e,a,t[a]);return e},X=(e,t)=>{var a={};for(var r in e)C.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&p)for(var r of p(e))t.indexOf(r)<0&&E.call(e,r)&&(a[r]=e[r]);return a};const x=o.forwardRef((e,t)=>{var a=e,{color:r="currentColor"}=a,n=X(a,["color"]);return o.createElement("svg",J({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},n),o.createElement("g",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},o.createElement("path",{d:"M3.933 2.132a6.4 6.4 0 0 1 2.322-.956M1.17 6.283a6.4 6.4 0 0 1 .97-2.362M3.933 12.868c.691.46 1.477.791 2.322.956M1.17 8.717c.165.86.5 1.66.97 2.362M8.745 1.176c.845.165 1.63.496 2.322.956M12.86 3.921a6.4 6.4 0 0 1 .97 2.362M8.745 13.824a6.4 6.4 0 0 0 2.322-.956M12.86 11.079c.47-.703.805-1.502.97-2.362"})),o.createElement("defs",null,o.createElement("clipPath",{id:"a"},o.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});x.displayName="CircleDottedLine";var h="Tabs",[Y,se]=$(h,[w]),y=w(),[ee,P]=Y(h),I=o.forwardRef((e,t)=>{const{__scopeTabs:a,value:r,onValueChange:n,defaultValue:l,orientation:s="horizontal",dir:d,activationMode:f="automatic",...g}=e,c=H(d),[i,b]=z({prop:r,onChange:n,defaultProp:l});return u.jsx(ee,{scope:a,baseId:B(),value:i,onValueChange:b,orientation:s,dir:c,activationMode:f,children:u.jsx(v.div,{dir:c,"data-orientation":s,...g,ref:t})})});I.displayName=h;var N="TabsList",M=o.forwardRef((e,t)=>{const{__scopeTabs:a,loop:r=!0,...n}=e,l=P(N,a),s=y(a);return u.jsx(K,{asChild:!0,...s,orientation:l.orientation,dir:l.dir,loop:r,children:u.jsx(v.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:t})})});M.displayName=N;var R="TabsTrigger",S=o.forwardRef((e,t)=>{const{__scopeTabs:a,value:r,disabled:n=!1,...l}=e,s=P(R,a),d=y(a),f=O(s.baseId,r),g=L(s.baseId,r),c=r===s.value;return u.jsx(q,{asChild:!0,...d,focusable:!n,active:c,children:u.jsx(v.button,{type:"button",role:"tab","aria-selected":c,"aria-controls":g,"data-state":c?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:f,...l,ref:t,onMouseDown:T(e.onMouseDown,i=>{!n&&i.button===0&&i.ctrlKey===!1?s.onValueChange(r):i.preventDefault()}),onKeyDown:T(e.onKeyDown,i=>{[" ","Enter"].includes(i.key)&&s.onValueChange(r)}),onFocus:T(e.onFocus,()=>{const i=s.activationMode!=="manual";!c&&!n&&i&&s.onValueChange(r)})})})});S.displayName=R;var j="TabsContent",A=o.forwardRef((e,t)=>{const{__scopeTabs:a,value:r,forceMount:n,children:l,...s}=e,d=P(j,a),f=O(d.baseId,r),g=L(d.baseId,r),c=r===d.value,i=o.useRef(c);return o.useEffect(()=>{const b=requestAnimationFrame(()=>i.current=!1);return()=>cancelAnimationFrame(b)},[]),u.jsx(Q,{present:n||c,children:({present:b})=>u.jsx(v.div,{"data-state":c?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":f,hidden:!b,id:g,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:i.current?"0s":void 0},children:b&&l})})});A.displayName=j;function O(e,t){return`${e}-trigger-${t}`}function L(e,t){return`${e}-content-${t}`}var te=I,ae=M,re=S,oe=A;const F=e=>o.createElement(te,{...e});F.displayName="ProgressTabs";const D=({status:e,className:t,...a})=>{const r=o.useMemo(()=>{switch(e){case"not-started":return x;case"in-progress":return W;case"completed":return U;default:return x}},[e]);return o.createElement("span",{className:m("text-ui-fg-muted group-data-[state=active]/trigger:text-ui-fg-interactive",t),...a},o.createElement(r,null))};D.displayName="ProgressTabs.ProgressIndicator";const V=o.forwardRef(({className:e,children:t,status:a="not-started",...r},n)=>o.createElement(re,{ref:n,className:m("txt-compact-small-plus transition-fg text-ui-fg-muted bg-ui-bg-subtle border-r-ui-border-base inline-flex h-[52px] w-full max-w-[200px] flex-1 items-center gap-x-2 border-r px-4 text-left outline-none","group/trigger overflow-hidden text-ellipsis whitespace-nowrap","disabled:bg-ui-bg-disabled disabled:text-ui-fg-muted","hover:bg-ui-bg-subtle-hover","focus-visible:bg-ui-bg-base focus:z-[1]","data-[state=active]:text-ui-fg-base data-[state=active]:bg-ui-bg-base",e),...r},o.createElement(D,{status:a}),t));V.displayName="ProgressTabs.Trigger";const G=o.forwardRef(({className:e,...t},a)=>o.createElement(ae,{ref:a,className:m("flex items-center",e),...t}));G.displayName="ProgressTabs.List";const k=o.forwardRef(({className:e,...t},a)=>o.createElement(oe,{ref:a,className:m("outline-none",e),...t}));k.displayName="ProgressTabs.Content";const ie=Object.assign(F,{Trigger:V,List:G,Content:k});export{ie as P};
