'use client';

import * as React from 'react';
import { useCallback, useRef, useMemo } from 'react';
import { flexRender, type Row, type Cell } from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useTableContext } from './table-dynamic-context';

const ROW_HEIGHTS = {
  sm: 36,
  md: 48,
  lg: 64,
};

/**
 * TableDynamicBody props
 */
export interface TableDynamicBodyProps {
  /** Whether to virtualize the table rows for large datasets */
  virtualized?: boolean;
  /** Height of the table body for virtualization */
  height?: number;
  /** Width of the table body for virtualization */
  width?: number | string;
  /** Number of items to overscan in virtualization */
  overscan?: number;
  /** Whether to show alternating row colors */
  striped?: boolean;
  /** Whether to show row borders */
  showBorders?: boolean;
  /** Table size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Animation delay between rows */
  animationDelay?: number;
  /** Whether to animate rows */
  animate?: boolean;
}

/**
 * TableDynamicBody component
 */
export function TableDynamicBody({
  virtualized = false,
  height = 400,
  width = '100%',
  overscan = 10,
  striped = false,
  showBorders = true,
  size = 'md',
  animate = false,
  animationDelay = 0.05,
}: TableDynamicBodyProps) {
  const { table, hoveredRowId, setHoveredRowId, handleRowClick, handleCellClick } =
    useTableContext();

  const rows = table.getRowModel().rows;
  const rowVirtualizerRef = useRef<HTMLDivElement>(null);

  // Set up virtualizer without conditional hooks
  const virtualizer = useVirtualizer({
    count: virtualized ? rows.length : 0, // Only active if virtualized=true
    getScrollElement: () => rowVirtualizerRef.current,
    estimateSize: () => ROW_HEIGHTS[size],
    overscan: overscan,
    // The virtualizer will be disabled when virtualized=false because count=0
  });

  // Memoized animation variants
  const containerVariants = useMemo(
    () => ({
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: animationDelay,
          delayChildren: 0.1,
        },
      },
    }),
    [animationDelay],
  );

  const itemVariants = useMemo(
    () => ({
      hidden: { opacity: 0, y: 10 },
      visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
    }),
    [],
  );

  const handleMouseEnter = useCallback(
    (rowId: string) => {
      setHoveredRowId(rowId);
    },
    [setHoveredRowId],
  );

  const handleMouseLeave = useCallback(() => {
    setHoveredRowId(null);
  }, [setHoveredRowId]);

  const renderRow = useCallback(
    (row: Row<Record<string, unknown>>) => {
      const isHovered = hoveredRowId === row.id;

      return (
        <motion.tr
          key={row.id}
          className={cn(
            'transition-colors',
            showBorders && 'border-border border-b last:border-b-0',
            striped && row.index % 2 === 1 && 'bg-muted/30',
            isHovered && 'bg-muted/60',
            row.getIsSelected() && 'bg-primary/10',
          )}
          variants={animate ? itemVariants : undefined}
          initial={animate ? 'hidden' : undefined}
          animate={animate ? 'visible' : undefined}
          onMouseEnter={() => handleMouseEnter(row.id)}
          onMouseLeave={handleMouseLeave}
          onClick={() => handleRowClick(row)}
        >
          {row.getVisibleCells().map((cell: Cell<Record<string, unknown>, unknown>) => {
            const cellMeta = cell.column.columnDef.meta as Record<string, unknown> | undefined;

            return (
              <td
                key={cell.id}
                className={cn(
                  'p-2',
                  size === 'sm' && 'py-1',
                  size === 'lg' && 'py-3',
                  typeof cellMeta?.className === 'string' && cellMeta.className,
                  cellMeta?.align === 'right' && 'text-right',
                  cellMeta?.align === 'center' && 'text-center',
                  typeof cellMeta?.truncate === 'boolean' &&
                    cellMeta.truncate &&
                    'max-w-[200px] truncate',
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCellClick(row, cell.column.id);
                }}
              >
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </td>
            );
          })}
        </motion.tr>
      );
    },
    [
      animate,
      itemVariants,
      hoveredRowId,
      showBorders,
      striped,
      size,
      handleMouseEnter,
      handleMouseLeave,
      handleRowClick,
      handleCellClick,
    ],
  );

  // Render virtualized table body
  if (virtualized && virtualizer) {
    return (
      <div
        ref={rowVirtualizerRef}
        className="overflow-auto"
        style={{
          height,
          width,
        }}
      >
        <table
          className="w-full table-auto"
          style={{
            height: `${virtualizer.getTotalSize()}px`,
            width: '100%',
            tableLayout: 'fixed',
          }}
        >
          <tbody className="relative">
            {virtualizer.getVirtualItems().map((virtualRow) => {
              const row = rows[virtualRow.index];
              return renderRow(row);
            })}
          </tbody>
        </table>
      </div>
    );
  }

  // Standard non-virtualized table body
  return (
    <motion.tbody
      className="relative"
      variants={animate ? containerVariants : undefined}
      initial={animate ? 'hidden' : undefined}
      animate={animate ? 'visible' : undefined}
    >
      {rows.length > 0 ? (
        rows.map((row) => renderRow(row))
      ) : (
        <tr>
          <td
            colSpan={table.getAllColumns().length}
            className="text-muted-foreground py-6 text-center"
          >
            No data available
          </td>
        </tr>
      )}
    </motion.tbody>
  );
}

TableDynamicBody.displayName = 'TableDynamicBody';
