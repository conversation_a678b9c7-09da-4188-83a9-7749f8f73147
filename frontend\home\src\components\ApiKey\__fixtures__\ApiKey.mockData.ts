import { sampleApiKeyAll, sampleApiKeyRestricted, sampleApiKeyReadOnly } from './ApiKey.fixtures';

/**
 * Mock API response for fetching API keys
 */
export const mockFetchApiKeysResponse = {
  data: [
    sampleApiKeyAll,
    sampleApiKeyRestricted,
    sampleApiKeyReadOnly,
    {
      id: 'key-789012',
      name: 'production-api',
      permissionType: 'Restricted',
      resourcePermissions: {
        models: 'Read',
        modelCapabilities: 'Write',
        assistants: 'None',
        threads: 'None',
        evals: 'None',
        fineTuning: 'Read',
        files: 'Write',
      },
      createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days ago
      lastUsed: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
    },
    {
      id: 'key-890123',
      permissionType: 'All',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
      lastUsed: undefined,
    },
  ],
  total: 5,
  page: 1,
  pageSize: 10,
};

/**
 * Mock API response for creating an API key
 */
export const mockCreateApiKeyResponse = {
  id: 'key-new-123456',
  name: 'new-api-key',
  permissionType: 'All',
  key: 'sk-mock-api-key-123456789012345678901234567890', // Secret key (only returned on creation)
  createdAt: new Date().toISOString(),
  lastUsed: undefined,
};

/**
 * Mock API response for updating an API key
 */
export const mockUpdateApiKeyResponse = {
  id: sampleApiKeyRestricted.id,
  name: 'updated-api-key-name',
  permissionType: 'Restricted',
  resourcePermissions: {
    models: 'Read',
    modelCapabilities: 'Write',
    assistants: 'Read',
    threads: 'Read',
    evals: 'None',
    fineTuning: 'None',
    files: 'Write',
  },
  createdAt: sampleApiKeyRestricted.createdAt,
  lastUsed: sampleApiKeyRestricted.lastUsed,
};

/**
 * Mock API response for deleting an API key
 */
export const mockDeleteApiKeyResponse = {
  success: true,
  message: 'API key deleted successfully',
  deletedId: sampleApiKeyAll.id,
};

/**
 * Mock API error responses
 */
export const mockApiErrors = {
  unauthorized: {
    error: {
      message: 'Unauthorized: Invalid or expired authentication token',
      code: 'unauthorized',
      status: 401,
    },
  },
  notFound: {
    error: {
      message: 'API key not found',
      code: 'not_found',
      status: 404,
    },
  },
  validation: {
    error: {
      message: 'Validation error: Invalid permission type',
      code: 'validation_error',
      status: 400,
      details: [
        {
          field: 'permissionType',
          message: 'Must be one of: All, Restricted, Read only',
        },
      ],
    },
  },
  server: {
    error: {
      message: 'Internal server error',
      code: 'server_error',
      status: 500,
    },
  },
};
