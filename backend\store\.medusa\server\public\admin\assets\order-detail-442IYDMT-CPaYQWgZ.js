import{B as J}from"./chunk-IC72XMDJ-DstjncGH.js";import{f as $e}from"./chunk-IR5DHEKS-aVJcUHa1.js";import{O as Be,g as Fe,a as Re}from"./chunk-4V4KZIY3-BJJgzRUp.js";import{r as Ue}from"./chunk-K7S5TX6I-B2rGhTKl.js";import{f as de}from"./chunk-OV5NMSY6-CzJZ0JaW.js";import{u as We,a as je,b as He}from"./chunk-PZK777PT-CNK-L8ff.js";import{u as Ve,a as ye,b as Ye}from"./chunk-XV6525OF-PjpYu8bP.js";import{g as Qe}from"./chunk-PXZ7QYKX-DZ_CHK12.js";import{u as Ke,a as ie,b as Xe}from"./chunk-VLT6UQCY-_FaxbAuo.js";import{u as Ge,a as Je}from"./chunk-NAAFKRUS-DzBFrGez.js";import{D as ne}from"./chunk-7I5DQGWY-CwOWioty.js";import{g as Ze,a as Pe,b as es}from"./chunk-7DXVXBSA-CCclXhoB.js";import{a as A,i as ss,g as k}from"./chunk-PDWBYQOW-BedvhUOI.js";import{r as v,aS as ve,j as e,q as ts,aT as as,aM as oe,b as g,V as $,B as z,aU as is,d as ns,R as ls,a as cs,aV as le,S as rs,aW as ds,s as os,u as B,aX as be,H as E,T as d,ah as _e,aY as ms,L as U,aZ as us,A as F,aE as xs,_ as ps,a_ as fs,a$ as hs,b0 as gs,az as js,b1 as ys,b2 as vs,b3 as bs,b4 as te,m as Ne,t as S,aB as W,b5 as _s,b6 as Ns,b7 as Cs,b8 as zs,b9 as Os,ba as Ss,bb as ks,Y as X,bc as Ce}from"./index-Bwql5Dzz.js";import{i as As,a as D}from"./chunk-OIAPXGI2-Bd-HAC-t.js";import{a as Es,T as Z}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as R}from"./chunk-DV5RB7II-B2VrP-dr.js";import{A as ce}from"./arrow-path-Bonjx2lP.js";import{u as L}from"./use-prompt-pbDx0Sfe.js";import{X as ze}from"./x-circle-CKMdlKvN.js";import{P as ws}from"./pencil-square-6wRbnn1C.js";import{A as G}from"./arrow-down-right-mini-JX0Up0zM.js";import{D as Oe}from"./document-text-CRz1_ULn.js";import{C as w}from"./container-Dqi2woPF.js";import{C as I}from"./copy-L2SdU4rs.js";import{S as M}from"./status-badge-B-sIb9s0.js";import{f as qs}from"./format-Cpg7FCX8.js";import{P as q}from"./popover-B2TSwh5F.js";import"./chunk-3UEMCYR5-Cnpb3wd3.js";import"./Trans-VWqfqpAH.js";import"./chunk-MWVM4TYO-bKUcYSnf.js";import"./x-mark-mini-DvSTI7zK.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";import"./index-DP5bcQyU.js";var Ts=Object.defineProperty,Y=Object.getOwnPropertySymbols,Se=Object.prototype.hasOwnProperty,ke=Object.prototype.propertyIsEnumerable,me=(s,t,i)=>t in s?Ts(s,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[t]=i,Is=(s,t)=>{for(var i in t)Se.call(t,i)&&me(s,i,t[i]);if(Y)for(var i of Y(t))ke.call(t,i)&&me(s,i,t[i]);return s},Ms=(s,t)=>{var i={};for(var a in s)Se.call(s,a)&&t.indexOf(a)<0&&(i[a]=s[a]);if(s!=null&&Y)for(var a of Y(s))t.indexOf(a)<0&&ke.call(s,a)&&(i[a]=s[a]);return i};const Ae=v.forwardRef((s,t)=>{var i=s,{color:a="currentColor"}=i,n=Ms(i,["color"]);return v.createElement("svg",Is({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},n),v.createElement("path",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13.056 7.5H1.944M9.278 3.722 13.056 7.5l-3.778 3.778"}))});Ae.displayName="ArrowLongRight";var Ls=Object.defineProperty,Q=Object.getOwnPropertySymbols,Ee=Object.prototype.hasOwnProperty,we=Object.prototype.propertyIsEnumerable,ue=(s,t,i)=>t in s?Ls(s,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[t]=i,Ds=(s,t)=>{for(var i in t)Ee.call(t,i)&&ue(s,i,t[i]);if(Q)for(var i of Q(t))we.call(t,i)&&ue(s,i,t[i]);return s},$s=(s,t)=>{var i={};for(var a in s)Ee.call(s,a)&&t.indexOf(a)<0&&(i[a]=s[a]);if(s!=null&&Q)for(var a of Q(s))t.indexOf(a)<0&&we.call(s,a)&&(i[a]=s[a]);return i};const qe=v.forwardRef((s,t)=>{var i=s,{color:a="currentColor"}=i,n=$s(i,["color"]);return v.createElement("svg",Ds({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},n),v.createElement("g",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},v.createElement("path",{d:"M1.056 4.611 7.07 7.93a.89.89 0 0 0 .858 0l6.015-3.318"}),v.createElement("path",{d:"M2.833 12.611h9.334c.982 0 1.777-.796 1.777-1.778V4.167c0-.982-.796-1.778-1.777-1.778H2.833c-.981 0-1.777.796-1.777 1.778v6.666c0 .982.796 1.778 1.777 1.778"})),v.createElement("defs",null,v.createElement("clipPath",{id:"a"},v.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});qe.displayName="Envelope";var Bs=Object.defineProperty,K=Object.getOwnPropertySymbols,Te=Object.prototype.hasOwnProperty,Ie=Object.prototype.propertyIsEnumerable,xe=(s,t,i)=>t in s?Bs(s,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):s[t]=i,Fs=(s,t)=>{for(var i in t)Te.call(t,i)&&xe(s,i,t[i]);if(K)for(var i of K(t))Ie.call(t,i)&&xe(s,i,t[i]);return s},Rs=(s,t)=>{var i={};for(var a in s)Te.call(s,a)&&t.indexOf(a)<0&&(i[a]=s[a]);if(s!=null&&K)for(var a of K(s))t.indexOf(a)<0&&Ie.call(s,a)&&(i[a]=s[a]);return i};const Me=v.forwardRef((s,t)=>{var i=s,{color:a="currentColor"}=i,n=Rs(i,["color"]);return v.createElement("svg",Fs({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},n),v.createElement("path",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M7.5 1.5v5.333M11.278 4.167H3.722c-.982 0-1.778.796-1.778 1.778v5.333c0 .982.796 1.778 1.778 1.778h7.556c.982 0 1.778-.796 1.778-1.778V5.945c0-.982-.796-1.778-1.778-1.778"}),v.createElement("path",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"m2.167 5.086 1.288-2.598c.3-.605.917-.988 1.593-.988h4.904c.676 0 1.293.383 1.593.988l1.288 2.599M4.167 10.833h1.777"}))});Me.displayName="FlyingBox";var ya=s=>{const{id:t}=s.params||{},{order:i}=ve(t,{fields:ne},{initialData:s.data,enabled:!!t});return i?e.jsxs("span",{children:["#",i.display_id]}):null},Us=s=>({queryKey:ds.detail(s),queryFn:async()=>os.admin.order.retrieve(s,{fields:ne})}),va=async({params:s})=>{const t=s.id,i=Us(t);return ts.ensureQueryData(i)},Ws=({orderPreview:s})=>{var o;const{t}=g(),i=(o=s==null?void 0:s.order_change)==null?void 0:o.claim_id,{mutateAsync:a}=We(i,s.id),n=B(),l=async()=>{n(`/orders/${s.id}/claims`)},r=async()=>{await a(void 0,{onSuccess:()=>{S.success(t("orders.claims.toast.canceledSuccessfully"))},onError:u=>{S.error(u.message)}})};if(i)return e.jsx("div",{style:{background:"repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"},className:"-m-4 mb-1 border-b border-l p-4",children:e.jsx(w,{className:"flex items-center justify-between p-0",children:e.jsxs("div",{className:"flex w-full flex-row justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2 px-6 pt-4",children:[e.jsx(be,{className:"text-ui-fg-subtle"}),e.jsx(E,{level:"h2",children:t("orders.claims.panel.title")})]}),e.jsx("div",{className:"gap-2 px-6 pb-4",children:e.jsx(d,{children:t("orders.claims.panel.description")})})]}),e.jsxs("div",{className:"flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4",children:[e.jsx(z,{size:"small",variant:"secondary",onClick:r,children:t("orders.claims.cancel.title")}),e.jsx(z,{size:"small",variant:"secondary",onClick:l,children:t("actions.continue")})]})]})})})},Hs=({orderPreview:s})=>{var o;const{t}=g(),i=(o=s==null?void 0:s.order_change)==null?void 0:o.exchange_id,{mutateAsync:a}=Ve(i,s.id),n=B(),l=async()=>{n(`/orders/${s.id}/exchanges`)},r=async()=>{await a(void 0,{onSuccess:()=>{S.success(t("orders.exchanges.toast.canceledSuccessfully"))},onError:u=>{S.error(u.message)}})};if(i)return e.jsx("div",{style:{background:"repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"},className:"-m-4 mb-1 border-b border-l p-4",children:e.jsx(w,{className:"flex items-center justify-between p-0",children:e.jsxs("div",{className:"flex w-full flex-row justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2 px-6 pt-4",children:[e.jsx(ce,{className:"text-ui-fg-subtle"}),e.jsx(E,{level:"h2",children:t("orders.exchanges.panel.title")})]}),e.jsx("div",{className:"gap-2 px-6 pb-4",children:e.jsx(d,{children:t("orders.exchanges.panel.description")})})]}),e.jsxs("div",{className:"flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4",children:[e.jsx(z,{size:"small",variant:"secondary",onClick:r,children:t("orders.exchanges.cancel.title")}),e.jsx(z,{size:"small",variant:"secondary",onClick:l,children:t("actions.continue")})]})]})})})},Vs=({orderPreview:s})=>{const{t}=g(),i=s==null?void 0:s.order_change,a=i==null?void 0:i.return_id,n=(i==null?void 0:i.change_type)==="return_request"&&!!i.return_id,{mutateAsync:l}=Ke(a,s.id),r=B(),o=async()=>{r(`/orders/${s.id}/returns`)},u=async()=>{await l(void 0,{onSuccess:()=>{S.success(t("orders.returns.toast.canceledSuccessfully"))},onError:x=>{S.error(x.message)}})};if(!(!a||!n))return e.jsx("div",{style:{background:"repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"},className:"-m-4 mb-1 border-b border-l p-4",children:e.jsx(w,{className:"flex items-center justify-between p-0",children:e.jsxs("div",{className:"flex w-full flex-row justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2 px-6 pt-4",children:[e.jsx(_e,{className:"text-ui-fg-subtle"}),e.jsx(E,{level:"h2",children:t("orders.returns.panel.title")})]}),e.jsx("div",{className:"gap-2 px-6 pb-4",children:e.jsx(d,{children:t("orders.returns.panel.description")})})]}),e.jsxs("div",{className:"flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4",children:[e.jsx(z,{size:"small",variant:"secondary",onClick:u,children:t("orders.returns.cancel.title")}),e.jsx(z,{size:"small",variant:"secondary",onClick:o,children:t("actions.continue")})]})]})})})};function pe({item:s,quantity:t}){return e.jsx("div",{className:"text-ui-fg-subtle items-center gap-x-2",children:e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsxs("div",{className:"w-fit min-w-[27px]",children:[e.jsx("span",{className:"txt-small tabular-nums",children:t}),"x"]}),e.jsx(W,{src:s.thumbnail}),e.jsx("span",{className:"txt-small text-ui-fg-subtle font-medium",children:s.title}),s.variant_sku&&" · ",s.variant_sku&&e.jsxs("div",{className:"flex items-center gap-x-1",children:[e.jsx("span",{className:"txt-small",children:s.variant_sku}),e.jsx(I,{content:s.variant_sku,className:"text-ui-fg-muted"})]})]})},s.id)}var Ys=({order:s})=>{var f,h;const{t}=g(),i=B(),{order:a}=le(s.id),{mutateAsync:n}=Ge(s.id),{mutateAsync:l}=Je(s.id),r=((f=a.order_change)==null?void 0:f.status)==="pending",[o,u]=v.useMemo(()=>{const m=[],b=[],y=new Map(s.items.map(_=>[_.id,_]));return((a==null?void 0:a.items)||[]).forEach(_=>{const O=y.get(_.id);if(!O){m.push({item:_,quantity:_.quantity});return}O.quantity>_.quantity&&b.push({item:_,quantity:O.quantity-_.quantity}),O.quantity<_.quantity&&m.push({item:_,quantity:_.quantity-O.quantity})}),[m,b]},[a]),x=async()=>{try{await l(),S.success(t("orders.edits.toast.confirmedSuccessfully"))}catch(m){S.error(m.message)}},j=async()=>{try{await n(),S.success(t("orders.edits.toast.canceledSuccessfully"))}catch(m){S.error(m.message)}};return!a||((h=a.order_change)==null?void 0:h.change_type)!=="edit"?null:e.jsx("div",{style:{background:"repeating-linear-gradient(-45deg, rgb(212, 212, 216, 0.15), rgb(212, 212, 216,.15) 10px, transparent 10px, transparent 20px)"},className:"-m-4 mb-1 border-b border-l p-4",children:e.jsx(w,{className:"flex items-center justify-between p-0",children:e.jsxs("div",{className:"flex w-full flex-col divide-y divide-dashed",children:[e.jsxs("div",{className:"flex items-center gap-2 px-6 py-4",children:[e.jsx(ms,{className:"text-blue-500"}),e.jsx(E,{level:"h2",children:t(r?"orders.edits.panel.titlePending":"orders.edits.panel.title")})]}),!!o.length&&e.jsxs("div",{className:"txt-small text-ui-fg-subtle flex flex-row px-6 py-4",children:[e.jsx("span",{className:"flex-1 font-medium",children:t("labels.added")}),e.jsx("div",{className:"flex flex-1 flex-col gap-y-2",children:o.map(({item:m,quantity:b})=>e.jsx(pe,{item:m,quantity:b},m.id))})]}),!!u.length&&e.jsxs("div",{className:"txt-small text-ui-fg-subtle flex flex-row px-6 py-4",children:[e.jsx("span",{className:"flex-1 font-medium",children:t("labels.removed")}),e.jsx("div",{className:"flex flex-1 flex-col gap-y-2",children:u.map(({item:m,quantity:b})=>e.jsx(pe,{item:m,quantity:b},m.id))})]}),e.jsxs("div",{className:"bg-ui-bg-subtle flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4",children:[r?e.jsx(z,{size:"small",variant:"secondary",onClick:()=>i(`/orders/${s.id}/edits`),children:t("actions.continueEdit")}):e.jsx(z,{size:"small",variant:"secondary",onClick:x,children:t("actions.forceConfirm")}),e.jsx(z,{size:"small",variant:"secondary",onClick:j,children:t("actions.cancel")})]})]})})})};function Qs(s){const{t}=g(),[i,a]=v.useState(!1),n=s.itemsToSend,l=s.itemsToReturn,r=s.itemsMap,o=s.title,u=()=>{a(!0)},x=()=>{a(!1)};if(!(!(n!=null&&n.length)&&!(l!=null&&l.length)))return e.jsxs(q,{open:i,children:[e.jsx(q.Trigger,{onMouseEnter:u,onMouseLeave:x,autoFocus:!1,className:"focus-visible:outline-none",children:e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:o})}),e.jsx(q.Content,{align:"center",side:"top",className:"bg-ui-bg-component max-w-[200px] p-0 focus-visible:outline-none",children:e.jsxs("div",{className:"flex flex-col",children:[!!(n!=null&&n.length)&&e.jsxs("div",{className:"p-3",children:[e.jsx("div",{className:"txt-compact-small-plus mb-1",children:t("orders.activity.events.common.toSend")}),e.jsxs("div",{className:"flex flex-col",children:[n==null?void 0:n.map(j=>{const f=r==null?void 0:r.get(j.item_id);return e.jsxs("div",{className:"flex items-center gap-x-3",children:[e.jsxs(d,{size:"small",className:"text-ui-fg-subtle",children:[j.quantity,"x"]}),e.jsx(W,{src:f==null?void 0:f.thumbnail}),e.jsx(d,{className:"txt-compact-small text-ui-fg-subtle truncate",children:`${f==null?void 0:f.variant_title} · ${f==null?void 0:f.product_title}`})]},j.id)}),e.jsx("div",{className:"flex flex-1 flex-row items-center gap-2"})]})]}),!!(l!=null&&l.length)&&e.jsxs("div",{className:"border-t-2 border-dotted p-3",children:[e.jsx("div",{className:"txt-compact-small-plus mb-1",children:t("orders.activity.events.common.toReturn")}),e.jsxs("div",{className:"flex flex-col",children:[l==null?void 0:l.map(j=>{const f=r==null?void 0:r.get(j.item_id);return e.jsxs("div",{className:"flex items-center gap-x-3",children:[e.jsxs(d,{size:"small",className:"text-ui-fg-subtle",children:[j.quantity,"x"]}),e.jsx(W,{src:f==null?void 0:f.thumbnail}),e.jsx(d,{className:"txt-compact-small text-ui-fg-subtle truncate",children:`${f==null?void 0:f.variant_title} · ${f==null?void 0:f.product_title}`})]},j.id)}),e.jsx("div",{className:"flex flex-1 flex-row items-center gap-2"})]})]})]})})]})}var Ks=Qs;function Xs(s){const{t}=g(),[i,a]=v.useState(!1),n=s.previous,l=s.next,r=s.title,o=()=>{a(!0)},u=()=>{a(!1)};return!n&&!l?null:e.jsxs(q,{open:i,children:[e.jsx(q.Trigger,{onMouseEnter:o,onMouseLeave:u,autoFocus:!1,className:"focus-visible:outline-none",children:e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:r})}),e.jsx(q.Content,{align:"center",side:"top",className:"bg-ui-bg-component max-w-[200px] p-0 focus-visible:outline-none",children:e.jsxs("div",{className:"flex flex-col",children:[!!n&&e.jsxs("div",{className:"p-3",children:[e.jsx("div",{className:"txt-compact-small-plus mb-1",children:t("labels.from")}),e.jsx("p",{className:"txt-compact-small text-ui-fg-subtle",children:n})]}),!!l&&e.jsxs("div",{className:"border-t-2 border-dotted p-3",children:[e.jsx("div",{className:"txt-compact-small-plus mb-1",children:t("labels.to")}),e.jsx("p",{className:"txt-compact-small text-ui-fg-subtle",children:l})]})]})})]})}var P=Xs,Gs=["transfer","update_order"],Js=({order:s})=>{const t=Zs(s);if(t.length<=3)return e.jsx("div",{className:"flex flex-col gap-y-0.5",children:t.map((l,r)=>e.jsx(V,{title:l.title,timestamp:l.timestamp,isFirst:r===t.length-1,itemsToSend:l.itemsToSend,itemsToReturn:l.itemsToReturn,itemsMap:l.itemsMap,children:l.children},r))});const i=t.slice(0,2),a=t.slice(2,t.length-1),n=t[t.length-1];return e.jsxs("div",{className:"flex flex-col gap-y-0.5",children:[i.map((l,r)=>e.jsx(V,{title:l.title,timestamp:l.timestamp,itemsToSend:l.itemsToSend,itemsToReturn:l.itemsToReturn,itemsMap:l.itemsMap,children:l.children},r)),e.jsx(Ps,{activities:a}),e.jsx(V,{title:n.title,timestamp:n.timestamp,isFirst:!0,itemsToSend:n.itemsToSend,itemsToReturn:n.itemsToReturn,itemsMap:n.itemsMap,children:n.children})]})},Zs=s=>{const{t}=g(),{order_changes:i=[]}=_s(s.id,{change_type:["edit","claim","exchange","return","transfer","update_order"]}),a=i.filter(m=>!Gs.includes(m.change_type)),n=nt(s,a),{order_items:l=[]}=Ns(s.id,{fields:"+quantity",item_id:n},{enabled:!!a.length}),r=v.useMemo(()=>{var b;const m=new Map((b=s==null?void 0:s.items)==null?void 0:b.map(y=>[y.id,y]));for(const y of n){const _=l.find(O=>O.item.id===y);_&&m.set(y,{..._.item,quantity:_.quantity})}return m},[s.items,l,n]),{returns:o=[]}=ie({order_id:s.id,fields:"+received_at,*items"}),{claims:u=[]}=je({order_id:s.id,fields:"*additional_items"}),{exchanges:x=[]}=ye({order_id:s.id,fields:"*additional_items"}),j=Re(s),f=[];return v.useMemo(()=>{var O,N;const m=[];for(const c of j){const p=c.amount;m.push({title:t("orders.activity.events.payment.awaiting"),timestamp:c.created_at,children:e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:A(p,c.currency_code)})}),c.canceled_at&&m.push({title:t("orders.activity.events.payment.canceled"),timestamp:c.canceled_at,children:e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:A(p,c.currency_code)})}),c.captured_at&&m.push({title:t("orders.activity.events.payment.captured"),timestamp:c.captured_at,children:e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:A(p,c.currency_code)})});for(const C of c.refunds||[])m.push({title:t("orders.activity.events.payment.refunded"),timestamp:C.created_at,children:e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:A(C.amount,c.currency_code)})})}for(const c of s.fulfillments||[])m.push({title:t("orders.activity.events.fulfillment.created"),timestamp:c.created_at,children:e.jsx(ee,{fulfillment:c})}),c.delivered_at&&m.push({title:t("orders.activity.events.fulfillment.delivered"),timestamp:c.delivered_at,children:e.jsx(ee,{fulfillment:c})}),c.shipped_at&&m.push({title:t("orders.activity.events.fulfillment.shipped"),timestamp:c.shipped_at,children:e.jsx(ee,{fulfillment:c,isShipment:!0})}),c.canceled_at&&m.push({title:t("orders.activity.events.fulfillment.canceled"),timestamp:c.canceled_at});const b=new Map;for(const c of o)b.set(c.id,c),!(c.claim_id||c.exchange_id)&&(m.push({title:t("orders.activity.events.return.created",{returnId:c.id.slice(-7)}),timestamp:c.created_at,itemsToReturn:c==null?void 0:c.items,itemsMap:r,children:e.jsx(fe,{orderReturn:c,isCreated:!c.canceled_at})}),c.canceled_at&&m.push({title:t("orders.activity.events.return.canceled",{returnId:c.id.slice(-7)}),timestamp:c.canceled_at}),(c.status==="received"||c.status==="partially_received")&&m.push({title:t("orders.activity.events.return.received",{returnId:c.id.slice(-7)}),timestamp:c.received_at,itemsToReturn:c==null?void 0:c.items,itemsMap:r,children:e.jsx(fe,{orderReturn:c,isReceived:!0})}));for(const c of u){const p=b.get(c.return_id);m.push({title:t(c.canceled_at?"orders.activity.events.claim.canceled":"orders.activity.events.claim.created",{claimId:c.id.slice(-7)}),timestamp:c.canceled_at||c.created_at,itemsToSend:c.additional_items,itemsToReturn:p==null?void 0:p.items,itemsMap:r,children:e.jsx(et,{claim:c,claimReturn:p})})}for(const c of x){const p=b.get(c.return_id);m.push({title:t(c.canceled_at?"orders.activity.events.exchange.canceled":"orders.activity.events.exchange.created",{exchangeId:c.id.slice(-7)}),timestamp:c.canceled_at||c.created_at,itemsToSend:c.additional_items,itemsToReturn:p==null?void 0:p.items,itemsMap:r,children:e.jsx(st,{exchange:c,exchangeReturn:p})})}for(const c of i.filter(p=>p.change_type==="edit")){const p=c.status==="confirmed";c.status!=="pending"&&m.push({title:t(`orders.activity.events.edit.${c.status}`,{editId:c.id.slice(-7)}),timestamp:c.status==="requested"?c.requested_at:c.status==="confirmed"?c.confirmed_at:c.status==="declined"?c.declined_at:c.status==="canceled"?c.canceled_at:c.created_at,children:p?e.jsx(tt,{edit:c}):null})}for(const c of i.filter(p=>p.change_type==="transfer"))c.requested_at&&m.push({title:t("orders.activity.events.transfer.requested",{transferId:c.id.slice(-7)}),timestamp:c.requested_at,children:e.jsx(at,{transfer:c})}),c.confirmed_at&&m.push({title:t("orders.activity.events.transfer.confirmed",{transferId:c.id.slice(-7)}),timestamp:c.confirmed_at}),c.declined_at&&m.push({title:t("orders.activity.events.transfer.declined",{transferId:c.id.slice(-7)}),timestamp:c.declined_at});for(const c of i.filter(p=>p.change_type==="update_order")){const p=(N=(O=c.actions[0])==null?void 0:O.details)==null?void 0:N.type;p==="shipping_address"&&m.push({title:e.jsx(P,{title:t("orders.activity.events.update_order.shipping_address"),previous:D({address:c.actions[0].details.old}).join(", "),next:D({address:c.actions[0].details.new}).join(", ")}),timestamp:c.created_at,children:e.jsxs("div",{className:"text-ui-fg-subtle mt-2 flex gap-x-2 text-sm",children:[t("fields.by")," ",e.jsx(J,{id:c.created_by})]})}),p==="billing_address"&&m.push({title:e.jsx(P,{title:t("orders.activity.events.update_order.billing_address"),previous:D({address:c.actions[0].details.old}).join(", "),next:D({address:c.actions[0].details.new}).join(", ")}),timestamp:c.created_at,children:e.jsxs("div",{className:"text-ui-fg-subtle mt-2 flex gap-x-2 text-sm",children:[t("fields.by")," ",e.jsx(J,{id:c.created_by})]})}),p==="email"&&m.push({title:e.jsx(P,{title:t("orders.activity.events.update_order.email"),previous:c.actions[0].details.old,next:c.actions[0].details.new}),timestamp:c.created_at,children:e.jsxs("div",{className:"text-ui-fg-subtle mt-2 flex gap-x-2 text-sm",children:[t("fields.by")," ",e.jsx(J,{id:c.created_by})]})})}s.canceled_at&&m.push({title:t("orders.activity.events.canceled.title"),timestamp:s.canceled_at});const y=m.sort((c,p)=>new Date(p.timestamp).getTime()-new Date(c.timestamp).getTime()),_={title:t("orders.activity.events.placed.title"),timestamp:s.created_at,children:e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:A(s.total,s.currency_code)})};return[...y,_]},[s,j,o,x,i,f,!1,r])},V=({title:s,timestamp:t,isFirst:i=!1,children:a,itemsToSend:n,itemsToReturn:l,itemsMap:r})=>{const{getFullDate:o,getRelativeDate:u}=R();return e.jsxs("div",{className:"grid grid-cols-[20px_1fr] items-start gap-2",children:[e.jsxs("div",{className:"flex size-full flex-col items-center gap-y-0.5",children:[e.jsx("div",{className:"flex size-5 items-center justify-center",children:e.jsx("div",{className:"bg-ui-bg-base shadow-borders-base flex size-2.5 items-center justify-center rounded-full",children:e.jsx("div",{className:"bg-ui-tag-neutral-icon size-1.5 rounded-full"})})}),!i&&e.jsx("div",{className:"bg-ui-border-base w-px flex-1"})]}),e.jsxs("div",{className:Ne({"pb-4":!i}),children:[e.jsxs("div",{className:"flex items-center justify-between",children:[n!=null&&n.length||l!=null&&l.length?e.jsx(Ks,{title:s,itemsToSend:n,itemsToReturn:l,itemsMap:r},s):e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:s}),t&&e.jsx($,{content:o({date:t,includeTime:!0}),children:e.jsx(d,{size:"small",leading:"compact",className:"text-ui-fg-subtle text-right",children:u(t)})})]}),e.jsx("div",{children:a})]})]})},Ps=({activities:s})=>{const[t,i]=v.useState(!1),{t:a}=g();return s.length?e.jsxs(Cs,{open:t,onOpenChange:i,children:[!t&&e.jsxs("div",{className:"grid grid-cols-[20px_1fr] items-start gap-2",children:[e.jsx("div",{className:"flex size-full flex-col items-center",children:e.jsx("div",{className:"border-ui-border-strong w-px flex-1 bg-[linear-gradient(var(--border-strong)_33%,rgba(255,255,255,0)_0%)] bg-[length:1px_3px] bg-right bg-repeat-y"})}),e.jsx("div",{className:"pb-4",children:e.jsx(zs,{className:"text-left",children:e.jsx(d,{size:"small",leading:"compact",weight:"plus",className:"text-ui-fg-muted",children:a("orders.activity.showMoreActivities",{count:s.length})})})})]}),e.jsx(Os,{children:e.jsx("div",{className:"flex flex-col gap-y-0.5",children:s.map((n,l)=>e.jsx(V,{title:n.title,timestamp:n.timestamp,itemsToSend:n.itemsToSend,itemsToReturn:n.itemsToReturn,itemsMap:n.itemsMap,children:n.children},l))})})]}):null},ee=({fulfillment:s})=>{const{t}=g(),i=s.items.reduce((a,n)=>a+n.quantity,0);return e.jsx("div",{children:e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:t("orders.activity.events.fulfillment.items",{count:i})})})},fe=({orderReturn:s,isCreated:t,isReceived:i})=>{const a=L(),{t:n}=g(),{mutateAsync:l}=Xe(s.id,s.order_id),r=async()=>{await a({title:n("orders.returns.cancel.title"),description:n("orders.returns.cancel.description"),confirmText:n("actions.confirm"),cancelText:n("actions.cancel")})&&await l()},o=s.items.reduce((u,x)=>u+(i?x.received_quantity:x.quantity),0);return e.jsxs("div",{className:"flex items-start gap-1",children:[e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:n("orders.activity.events.return.items",{count:o})}),t&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"mt-[2px] flex items-center leading-none",children:"⋅"}),e.jsx(z,{onClick:r,className:"text-ui-fg-subtle h-auto px-0 leading-none hover:bg-transparent",variant:"transparent",size:"small",children:n("actions.cancel")})]})]})},et=({claim:s,claimReturn:t})=>{const i=L(),{t:a}=g(),n=!!s.created_at,{mutateAsync:l}=He(s.id,s.order_id),r=async()=>{await i({title:a("orders.claims.cancel.title"),description:a("orders.claims.cancel.description"),confirmText:a("actions.confirm"),cancelText:a("actions.cancel")})&&await l()},o=(s.additional_items||[]).reduce((x,j)=>x+j.quantity,0),u=((t==null?void 0:t.items)||[]).reduce((x,j)=>x+j.quantity,0);return e.jsxs("div",{children:[o>0&&e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:a("orders.activity.events.claim.itemsInbound",{count:o})}),u>0&&e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:a("orders.activity.events.claim.itemsOutbound",{count:u})}),!n&&e.jsx(z,{onClick:r,className:"text-ui-fg-subtle h-auto px-0 leading-none hover:bg-transparent",variant:"transparent",size:"small",children:a("actions.cancel")})]})},st=({exchange:s,exchangeReturn:t})=>{const i=L(),{t:a}=g(),n=!!s.canceled_at,{mutateAsync:l}=Ye(s.id,s.order_id),r=async()=>{await i({title:a("orders.exchanges.cancel.title"),description:a("orders.exchanges.cancel.description"),confirmText:a("actions.confirm"),cancelText:a("actions.cancel")})&&await l()},o=(s.additional_items||[]).reduce((x,j)=>x+j.quantity,0),u=((t==null?void 0:t.items)||[]).reduce((x,j)=>x+j.quantity,0);return e.jsxs("div",{children:[o>0&&e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:a("orders.activity.events.exchange.itemsInbound",{count:o})}),u>0&&e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:a("orders.activity.events.exchange.itemsOutbound",{count:u})}),!n&&e.jsx(z,{onClick:r,className:"text-ui-fg-subtle h-auto px-0 leading-none hover:bg-transparent",variant:"transparent",size:"small",children:a("actions.cancel")})]})},tt=({edit:s})=>{const{t}=g(),[i,a]=v.useMemo(()=>it(s.actions),[s]);return e.jsxs("div",{children:[i>0&&e.jsxs(d,{size:"small",className:"text-ui-fg-subtle",children:[t("labels.added"),": ",i]}),a>0&&e.jsxs(d,{size:"small",className:"text-ui-fg-subtle",children:[t("labels.removed"),": ",a]})]})},at=({transfer:s})=>{var u;const t=L(),{t:i}=g(),a=s.actions[0],{customer:n}=Ss(a.reference_id),l=!!s.confirmed_at,{mutateAsync:r}=ks(s.order_id),o=async()=>{await t({title:i("general.areYouSure"),description:i("actions.cannotUndo"),confirmText:i("actions.delete"),cancelText:i("actions.cancel")})&&await r()};return e.jsxs("div",{children:[e.jsxs(d,{size:"small",className:"text-ui-fg-subtle",children:[i("orders.activity.from"),": ",(u=a.details)==null?void 0:u.original_email]}),e.jsxs(d,{size:"small",className:"text-ui-fg-subtle",children:[i("orders.activity.to"),":"," ",n!=null&&n.first_name?`${n==null?void 0:n.first_name} ${n==null?void 0:n.last_name}`:n==null?void 0:n.email]}),!l&&e.jsx(z,{onClick:o,className:"text-ui-fg-subtle h-auto px-0 leading-none hover:bg-transparent",variant:"transparent",size:"small",children:i("actions.cancel")})]})};function it(s){let t=0,i=0;return s.forEach(a=>{if(a.action==="ITEM_ADD"&&(t+=a.details.quantity),a.action==="ITEM_UPDATE"){const n=a.details.quantity_diff;n>0?t+=n:i+=Math.abs(n)}}),[t,i]}function nt(s,t){if(!(t!=null&&t.length))return[];const i=new Set,a=new Map(s.items.map(n=>[n.id,!0]));return t.forEach(n=>{n.actions.forEach(l=>{var r;(r=l.details)!=null&&r.reference_id&&l.details.reference_id.startsWith("ordli_")&&!a.has(l.details.reference_id)&&i.add(l.details.reference_id)})}),Array.from(i)}var lt=({order:s})=>{const{t}=g();return e.jsxs(w,{className:"flex flex-col gap-y-8 px-6 py-4",children:[e.jsx("div",{className:"flex flex-col gap-y-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(E,{level:"h2",children:t("orders.activity.header")})})}),e.jsx(Js,{order:s})]})},ct=({data:s})=>{const{t}=g(),i=s.customer_id,a=mt(s),n=s.email,l=(a||n||"").charAt(0).toUpperCase();return e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:t("fields.id")}),e.jsx(U,{to:`/customers/${i}`,className:"focus:shadow-borders-focus rounded-[4px] outline-none transition-shadow",children:e.jsxs("div",{className:"flex items-center gap-x-2 overflow-hidden",children:[e.jsx(us,{size:"2xsmall",fallback:l}),e.jsx(d,{size:"small",leading:"compact",className:"text-ui-fg-subtle hover:text-ui-fg-base transition-fg truncate",children:a||n})]})})]})},rt=({data:s})=>{var a,n;const{t}=g(),i=((a=s.shipping_address)==null?void 0:a.company)||((n=s.billing_address)==null?void 0:n.company);return i?e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:t("fields.company")}),e.jsx(d,{size:"small",leading:"compact",className:"truncate",children:i})]}):null},dt=({data:s})=>{var n,l;const{t}=g(),i=((n=s.shipping_address)==null?void 0:n.phone)||((l=s.billing_address)==null?void 0:l.phone),a=s.email||"";return e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:t("orders.customer.contactLabel")}),e.jsxs("div",{className:"flex flex-col gap-y-2",children:[e.jsxs("div",{className:"grid grid-cols-[1fr_20px] items-start gap-x-2",children:[e.jsx(d,{size:"small",leading:"compact",className:"text-pretty break-all",children:a}),e.jsx("div",{className:"flex justify-end",children:e.jsx(I,{content:a,className:"text-ui-fg-muted"})})]}),i&&e.jsxs("div",{className:"grid grid-cols-[1fr_20px] items-start gap-x-2",children:[e.jsx(d,{size:"small",leading:"compact",className:"text-pretty break-all",children:i}),e.jsx("div",{className:"flex justify-end",children:e.jsx(I,{content:a,className:"text-ui-fg-muted"})})]})]})]})},he=({address:s,type:t})=>{const{t:i}=g();return e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:i(t==="shipping"?"addresses.shippingAddress.label":"addresses.billingAddress.label")}),s?e.jsxs("div",{className:"grid grid-cols-[1fr_20px] items-start gap-x-2",children:[e.jsx(d,{size:"small",leading:"compact",children:D({address:s}).map((a,n)=>e.jsxs("span",{className:"break-words",children:[a,e.jsx("br",{})]},n))}),e.jsx("div",{className:"flex justify-end",children:e.jsx(I,{content:D({address:s}).join(`
`),className:"text-ui-fg-muted"})})]}):e.jsx(d,{size:"small",leading:"compact",children:"-"})]})},ot=({data:s})=>{const{t}=g();return e.jsxs("div",{className:"divide-y",children:[e.jsx(he,{address:s.shipping_address,type:"shipping"}),As(s.shipping_address,s.billing_address)?e.jsxs("div",{className:"grid grid-cols-2 items-center px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",className:"text-ui-fg-subtle",children:t("addresses.billingAddress.label")}),e.jsx(d,{size:"small",leading:"compact",className:"text-ui-fg-muted",children:t("addresses.billingAddress.sameAsShipping")})]}):e.jsx(he,{address:s.billing_address,type:"billing"})]})},H=Object.assign({},{ID:ct,Company:rt,Contact:dt,Addresses:ot}),mt=s=>{const{first_name:t,last_name:i}=s.shipping_address||{},{first_name:a,last_name:n}=s.billing_address||{},{first_name:l,last_name:r}=s.customer||{},o=[l,r].filter(Boolean).join(" "),u=[t,i].filter(Boolean).join(" "),x=[a,n].filter(Boolean).join(" ");return o||u||x},ut=({order:s})=>e.jsxs(w,{className:"divide-y p-0",children:[e.jsx(xt,{}),e.jsx(H.ID,{data:s}),e.jsx(H.Contact,{data:s}),e.jsx(H.Company,{data:s}),e.jsx(H.Addresses,{data:s})]}),xt=()=>{const{t:s}=g();return e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(E,{level:"h2",children:s("fields.customer")}),e.jsx(F,{groups:[{actions:[{label:s("transferOwnership.label"),to:"transfer",icon:e.jsx(ce,{})}]},{actions:[{label:s("addresses.shippingAddress.editLabel"),to:"shipping-address",icon:e.jsx(Me,{})},{label:s("addresses.billingAddress.editLabel"),to:"billing-address",icon:e.jsx(xs,{})}]},{actions:[{label:s("email.editLabel"),to:"email",icon:e.jsx(qe,{})}]}]})]})},pt=({order:s})=>{const t=s.fulfillments||[];return e.jsxs("div",{className:"flex flex-col gap-y-3",children:[e.jsx(ht,{order:s}),t.map((i,a)=>e.jsx(gt,{index:a,fulfillment:i,order:s},i.id))]})},ft=({item:s,currencyCode:t})=>{var i;return e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4",children:[e.jsxs("div",{className:"flex items-start gap-x-4",children:[e.jsx(W,{src:s.thumbnail}),e.jsxs("div",{children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",className:"text-ui-fg-base",children:s.title}),s.variant_sku&&e.jsxs("div",{className:"flex items-center gap-x-1",children:[e.jsx(d,{size:"small",children:s.variant_sku}),e.jsx(I,{content:s.variant_sku,className:"text-ui-fg-muted"})]}),e.jsx(d,{size:"small",children:(i=s.variant)==null?void 0:i.options.map(a=>a.value).join(" · ")})]})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-x-4",children:[e.jsx("div",{className:"flex items-center justify-end",children:e.jsx(d,{size:"small",children:k(s.unit_price,t)})}),e.jsx("div",{className:"flex items-center justify-end",children:e.jsxs(d,{children:[e.jsx("span",{className:"tabular-nums",children:s.quantity-s.detail.fulfilled_quantity}),"x"]})}),e.jsx("div",{className:"flex items-center justify-end",children:e.jsx(d,{size:"small",children:k(s.subtotal||0,t)})})]})]},s.id)},ht=({order:s})=>{const t=s.items.filter(a=>a.requires_shipping&&a.detail.fulfilled_quantity<a.quantity),i=s.items.filter(a=>!a.requires_shipping&&a.detail.fulfilled_quantity<a.quantity);return e.jsxs(e.Fragment,{children:[!!t.length&&e.jsx(ge,{order:s,unfulfilledItems:t,requiresShipping:!0}),!!i.length&&e.jsx(ge,{order:s,unfulfilledItems:i,requiresShipping:!1})]})},ge=({order:s,unfulfilledItems:t,requiresShipping:i=!1})=>{const{t:a}=g();if(s.status!=="canceled")return e.jsxs(w,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(E,{level:"h2",children:a("orders.fulfillment.unfulfilledItems")}),e.jsxs("div",{className:"flex items-center gap-x-4",children:[i&&e.jsx(M,{color:"red",className:"text-nowrap",children:a("orders.fulfillment.requiresShipping")}),e.jsx(M,{color:"red",className:"text-nowrap",children:a("orders.fulfillment.awaitingFulfillmentBadge")}),e.jsx(F,{groups:[{actions:[{label:a("orders.fulfillment.fulfillItems"),icon:e.jsx(ps,{}),to:`/orders/${s.id}/fulfillment?requires_shipping=${i}`}]}]})]})]}),e.jsx("div",{children:t.map(n=>e.jsx(ft,{item:n,currencyCode:s.currency_code},n.id))})]})},gt=({fulfillment:s,order:t,index:i})=>{var p;const{t:a}=g(),n=L(),l=B(),r=!!s.location_id,o=((p=s.shipping_option)==null?void 0:p.service_zone.fulfillment_set.type)==="pickup",{stock_location:u,isError:x,error:j}=fs(s.location_id,void 0,{enabled:r});let f=s.requires_shipping?o?"Awaiting pickup":"Awaiting shipping":"Awaiting delivery",h="blue",m=s.created_at;s.canceled_at?(f="Canceled",h="red",m=s.canceled_at):s.delivered_at?(f="Delivered",h="green",m=s.delivered_at):s.shipped_at&&(f="Shipped",h="green",m=s.shipped_at);const{mutateAsync:b}=hs(t.id,s.id),{mutateAsync:y}=gs(t.id,s.id),_=!s.canceled_at&&!s.shipped_at&&!s.delivered_at&&s.requires_shipping&&!o,O=!s.canceled_at&&!s.delivered_at,N=async()=>{await n({title:a("general.areYouSure"),description:a("orders.fulfillment.markAsDeliveredWarning"),confirmText:a("actions.continue"),cancelText:a("actions.cancel"),variant:"confirmation"})&&await y(void 0,{onSuccess:()=>{S.success(a(o?"orders.fulfillment.toast.fulfillmentPickedUp":"orders.fulfillment.toast.fulfillmentDelivered"))},onError:T=>{S.error(T.message)}})},c=async()=>{if(s.shipped_at){S.warning(a("orders.fulfillment.toast.fulfillmentShipped"));return}await n({title:a("general.areYouSure"),description:a("orders.fulfillment.cancelWarning"),confirmText:a("actions.continue"),cancelText:a("actions.cancel")})&&await b(void 0,{onSuccess:()=>{S.success(a("orders.fulfillment.toast.canceled"))},onError:T=>{S.error(T.message)}})};if(x)throw j;return e.jsxs(w,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(E,{level:"h2",children:a("orders.fulfillment.number",{number:i+1})}),e.jsxs("div",{className:"flex items-center gap-x-4",children:[e.jsx($,{content:qs(new Date(m),"dd MMM, yyyy, HH:mm:ss"),children:e.jsx(M,{color:h,className:"text-nowrap",children:f})}),e.jsx(F,{groups:[{actions:[{label:a("actions.cancel"),icon:e.jsx(ze,{}),onClick:c,disabled:!!s.canceled_at||!!s.shipped_at||!!s.delivered_at}]}]})]})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:a("orders.fulfillment.itemsLabel")}),e.jsx("ul",{children:s.items.map(C=>e.jsx("li",{children:e.jsxs(d,{size:"small",leading:"compact",children:[C.quantity,"x ",C.title]})},C.line_item_id))})]}),r&&e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:a("orders.fulfillment.shippingFromLabel")}),u?e.jsx(U,{to:`/settings/locations/${u.id}`,className:"text-ui-fg-interactive hover:text-ui-fg-interactive-hover transition-fg",children:e.jsx(d,{size:"small",leading:"compact",children:u.name})}):e.jsx(js,{className:"w-16"})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:a("fields.provider")}),e.jsx(d,{size:"small",leading:"compact",children:$e(s.provider_id)})]}),e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-start px-6 py-4",children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",children:a("orders.fulfillment.trackingLabel")}),e.jsx("div",{children:s.labels&&s.labels.length>0?e.jsx("ul",{children:s.labels.map(C=>C.url&&C.url.length>0&&C.url!=="#"?e.jsx("li",{children:e.jsx("a",{href:C.url,target:"_blank",rel:"noopener noreferrer",className:"text-ui-fg-interactive hover:text-ui-fg-interactive-hover transition-fg",children:e.jsx(d,{size:"small",leading:"compact",children:C.tracking_number})})},C.tracking_number):e.jsx("li",{children:e.jsx(d,{size:"small",leading:"compact",children:C.tracking_number})},C.tracking_number))}):e.jsx(d,{size:"small",leading:"compact",children:"-"})})]}),(_||O)&&e.jsxs("div",{className:"bg-ui-bg-subtle flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4",children:[O&&e.jsx(z,{onClick:N,variant:"secondary",children:a(o?"orders.fulfillment.markAsPickedUp":"orders.fulfillment.markAsDelivered")}),_&&e.jsx(z,{onClick:()=>l(`./${s.id}/create-shipment`),variant:"secondary",children:a("orders.fulfillment.markAsShipped")})]})]})},jt=({order:s})=>{var r;const{t}=g(),i=L(),{getFullDate:a}=R(),{mutateAsync:n}=ys(s.id),l=async()=>{await i({title:t("general.areYouSure"),description:t("orders.cancelWarning",{id:`#${s.display_id}`}),confirmText:t("actions.continue"),cancelText:t("actions.cancel")})&&await n(void 0,{onSuccess:()=>{S.success(t("orders.orderCanceled"))},onError:u=>{S.error(u.message)}})};return e.jsxs(w,{className:"flex items-center justify-between px-6 py-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-x-1",children:[e.jsxs(E,{children:["#",s.display_id]}),e.jsx(I,{content:`#${s.display_id}`,className:"text-ui-fg-muted"})]}),e.jsx(d,{size:"small",className:"text-ui-fg-subtle",children:t("orders.onDateFromSalesChannel",{date:a({date:s.created_at,includeTime:!0}),salesChannel:(r=s.sales_channel)==null?void 0:r.name})})]}),e.jsxs("div",{className:"flex items-center gap-x-4",children:[e.jsxs("div",{className:"flex items-center gap-x-1.5",children:[e.jsx(bt,{order:s}),e.jsx(vt,{order:s}),e.jsx(yt,{order:s})]}),e.jsx(F,{groups:[{actions:[{label:t("actions.cancel"),onClick:l,disabled:!!s.canceled_at,icon:e.jsx(ze,{})}]}]})]})]})},yt=({order:s})=>{const{t}=g(),{label:i,color:a}=Ze(t,s.fulfillment_status);return e.jsx(M,{color:a,className:"text-nowrap",children:i})},vt=({order:s})=>{const{t}=g(),{label:i,color:a}=Pe(t,s.payment_status);return e.jsx(M,{color:a,className:"text-nowrap",children:i})},bt=({order:s})=>{const{t}=g(),i=es(t,s.status);return i?e.jsx(M,{color:i.color,className:"text-nowrap",children:i.label}):null},_t=as(Ue()),Nt="",Le=oe.forwardRef(({paymentCollection:s,order:t},i)=>{const[a,n]=v.useState(!1),[l,r]=v.useState(!1),[o,u]=v.useState("CopyPaymentLink"),{t:x}=g(),j=async f=>{f.stopPropagation(),n(!0),(0,_t.default)(`${Nt}/payment-collection/${s.id}`),setTimeout(()=>{n(!1)},2e3)};return oe.useEffect(()=>{if(a){u(x("actions.copied"));return}setTimeout(()=>{u(x("actions.copy"))},500)},[a]),e.jsx($,{content:o,open:a||l,onOpenChange:r,children:e.jsxs(z,{ref:i,variant:"secondary",size:"small","aria-label":"CopyPaymentLink code snippet",onClick:j,children:[a?e.jsx(is,{className:"inline"}):e.jsx(Es,{className:"inline"}),x("orders.payment.paymentLink",{amount:A(s.amount,t==null?void 0:t.currency_code)})]})})});Le.displayName="CopyPaymentLink";function Ct({orderReturn:s}){const{t}=g(),[i,a]=v.useState(!1),{getFullDate:n}=R(),l=()=>{a(!0)},r=()=>{a(!1)};let o="Return",u=s.id;if(s.claim_id&&(o="Claim",u=s.claim_id),s.exchange_id&&(o="Exchange",u=s.exchange_id),typeof s=="object")return e.jsxs(q,{open:i,children:[e.jsx(q.Trigger,{onMouseEnter:l,onMouseLeave:r,autoFocus:!1,className:"align-sub focus-visible:outline-none",children:e.jsx(Ce,{})}),e.jsx(q.Content,{align:"center",side:"top",className:"bg-ui-bg-component p-2 focus-visible:outline-none",children:e.jsxs("div",{className:"",children:[e.jsxs(X,{size:"2xsmall",className:"mb-2",rounded:"full",children:[o,": #",u.slice(-7)]}),e.jsxs(d,{size:"xsmall",children:[e.jsx("span",{className:"text-ui-fg-subtle",children:t("orders.returns.returnRequested")})," · ",n({date:s.requested_at,includeTime:!0})]}),e.jsxs(d,{size:"xsmall",children:[e.jsx("span",{className:"text-ui-fg-subtle",children:t("orders.returns.itemReceived")})," · ",s.received_at?n({date:s.received_at,includeTime:!0}):"-"]})]})})]})}var ae=Ct;function zt({shippingMethod:s}){const{t}=g(),i=s==null?void 0:s.detail;if(!i)return;let a=t("orders.return"),n=i.return_id;if(i.claim_id&&(a=t("orders.claim"),n=i.claim_id),i.exchange_id&&(a=t("orders.exchange"),n=i.exchange_id),!!n)return e.jsx($,{content:e.jsxs(X,{size:"2xsmall",rounded:"full",children:[a,": #",n.slice(-7)]}),children:e.jsx(Ce,{className:"inline-block text-ui-fg-muted ml-1"})})}var Ot=zt,St=({order:s})=>{var _,O;const{t}=g();B();const i=L(),{reservations:a}=vs({line_item_id:(_=s==null?void 0:s.items)==null?void 0:_.map(N=>N.id)},{enabled:Array.isArray(s==null?void 0:s.items)}),{order:n}=le(s.id),{returns:l=[]}=ie({status:"requested",order_id:s.id,fields:"+received_at"}),r=v.useMemo(()=>l.filter(N=>!N.canceled_at),[l]),o=!!r.length,u=v.useMemo(()=>{var c;if(!a)return!1;const N=new Map(a.map(p=>[p.line_item_id,p.id]));for(const p of s.items)if((c=p.variant)!=null&&c.manage_inventory&&p.quantity-p.detail.fulfilled_quantity>0&&!N.has(p.id))return!0;return!1},[a]),x=s.payment_collections.find(N=>N.status==="not_paid"),{mutateAsync:j}=bs(s.id,x==null?void 0:x.id),f=((O=s.summary)==null?void 0:O.pending_difference)||0,h=!ss(f,s.currency_code),m=x&&f>0&&h,b=x&&f<0&&h,y=async N=>{await i({title:t("orders.payment.markAsPaid"),description:t("orders.payment.markAsPaidPayment",{amount:de(N.amount,s.currency_code)}),confirmText:t("actions.confirm"),cancelText:t("actions.cancel"),variant:"confirmation"})&&await j({order_id:s.id},{onSuccess:()=>{S.success(t("orders.payment.markAsPaidPaymentSuccess",{amount:de(N.amount,s.currency_code)}))},onError:p=>{S.error(p.message)}})};return e.jsxs(w,{className:"divide-y divide-dashed p-0",children:[e.jsx(kt,{order:s,orderPreview:n}),e.jsx(Et,{order:s,reservations:a}),e.jsx(wt,{order:s}),e.jsx(Dt,{order:s}),(u||o||m||b)&&e.jsxs("div",{className:"bg-ui-bg-subtle flex items-center justify-end gap-x-2 rounded-b-xl px-4 py-4",children:[o&&(r.length===1?e.jsx(z,{asChild:!0,variant:"secondary",size:"small",children:e.jsx(U,{to:`/orders/${s.id}/returns/${r[0].id}/receive`,children:t("orders.returns.receive.action")})}):e.jsx(F,{groups:[{actions:r.map(N=>{let c=N.id,p="Return";return N.exchange_id&&(c=N.exchange_id,p="Exchange"),N.claim_id&&(c=N.claim_id,p="Claim"),{label:t("orders.returns.receive.receiveItems",{id:`#${c.slice(-7)}`,returnType:p}),icon:e.jsx(Ae,{}),to:`/orders/${s.id}/returns/${N.id}/receive`}})}],children:e.jsx(z,{variant:"secondary",size:"small",children:t("orders.returns.receive.action")})})),u&&e.jsx(z,{asChild:!0,variant:"secondary",size:"small",children:e.jsx(U,{to:"allocate-items",children:t("orders.allocateItems.action")})}),m&&e.jsx(Le,{paymentCollection:x,order:s}),m&&e.jsx(z,{size:"small",variant:"secondary",onClick:()=>y(x),children:t("orders.payment.markAsPaid")}),b&&e.jsx(z,{size:"small",variant:"secondary",asChild:!0,children:e.jsx(U,{to:`/orders/${s.id}/refund`,children:t("orders.payment.refundAmount",{amount:A(f*-1,s==null?void 0:s.currency_code)})})})]})]})},kt=({order:s,orderPreview:t})=>{var r,o,u,x,j,f,h,m,b,y,_,O,N,c,p,C,T,re;const{t:i}=g(),a=s.items.every(De=>!(Qe(De)>0)),n=((r=t==null?void 0:t.order_change)==null?void 0:r.change_type)==="edit",l=((o=t==null?void 0:t.order_change)==null?void 0:o.change_type)==="edit"&&((u=t==null?void 0:t.order_change)==null?void 0:u.status)==="pending";return e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(E,{level:"h2",children:i("fields.summary")}),e.jsx(F,{groups:[{actions:[{label:i(l?"orders.summary.editOrderContinue":"orders.summary.editOrder"),to:`/orders/${s.id}/edits`,icon:e.jsx(ws,{}),disabled:s.status==="canceled"||(t==null?void 0:t.order_change)&&((x=t==null?void 0:t.order_change)==null?void 0:x.change_type)!=="edit"||((j=t==null?void 0:t.order_change)==null?void 0:j.change_type)==="edit"&&((f=t==null?void 0:t.order_change)==null?void 0:f.status)==="requested"}]},{actions:[{label:i("orders.returns.create"),to:`/orders/${s.id}/returns`,icon:e.jsx(_e,{}),disabled:a||n||!!((h=t==null?void 0:t.order_change)!=null&&h.exchange_id)||!!((m=t==null?void 0:t.order_change)!=null&&m.claim_id)},{label:(b=t==null?void 0:t.order_change)!=null&&b.id&&((y=t==null?void 0:t.order_change)!=null&&y.exchange_id)?i("orders.exchanges.manage"):i("orders.exchanges.create"),to:`/orders/${s.id}/exchanges`,icon:e.jsx(ce,{}),disabled:a||n||!!((_=t==null?void 0:t.order_change)!=null&&_.return_id)&&!((O=t==null?void 0:t.order_change)!=null&&O.exchange_id)||!!((N=t==null?void 0:t.order_change)!=null&&N.claim_id)},{label:(c=t==null?void 0:t.order_change)!=null&&c.id&&((p=t==null?void 0:t.order_change)!=null&&p.claim_id)?i("orders.claims.manage"):i("orders.claims.create"),to:`/orders/${s.id}/claims`,icon:e.jsx(be,{}),disabled:a||n||!!((C=t==null?void 0:t.order_change)!=null&&C.return_id)&&!((T=t==null?void 0:t.order_change)!=null&&T.claim_id)||!!((re=t==null?void 0:t.order_change)!=null&&re.exchange_id)}]}]})]})},At=({item:s,currencyCode:t,reservation:i,returns:a,claims:n,exchanges:l})=>{var j,f,h,m,b;const{t:r}=g(),o=(j=s.variant)==null?void 0:j.manage_inventory,u=o&&(((h=(f=s.variant)==null?void 0:f.inventory_items)==null?void 0:h.length)||0)>1,x=s.quantity-s.detail.fulfilled_quantity>0;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center gap-x-4 px-6 py-4",children:[e.jsxs("div",{className:"flex items-start gap-x-4",children:[e.jsx(W,{src:s.thumbnail}),e.jsxs("div",{children:[e.jsx(d,{size:"small",leading:"compact",weight:"plus",className:"text-ui-fg-base",children:s.title}),s.variant_sku&&e.jsxs("div",{className:"flex items-center gap-x-1",children:[e.jsx(d,{size:"small",children:s.variant_sku}),e.jsx(I,{content:s.variant_sku,className:"text-ui-fg-muted"})]}),e.jsx(d,{size:"small",children:(b=(m=s.variant)==null?void 0:m.options)==null?void 0:b.map(y=>y.value).join(" · ")})]})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-x-4",children:[e.jsx("div",{className:"flex items-center justify-end gap-x-4",children:e.jsx(d,{size:"small",children:k(s.unit_price,t)})}),e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx("div",{className:"w-fit min-w-[27px]",children:e.jsxs(d,{size:"small",children:[e.jsx("span",{className:"tabular-nums",children:s.quantity}),"x"]})}),e.jsx("div",{className:"overflow-visible",children:o&&x&&e.jsx(M,{color:i?"green":"orange",className:"text-nowrap",children:r(i?"orders.reservations.allocatedLabel":"orders.reservations.notAllocatedLabel")})})]}),e.jsx("div",{className:"flex items-center justify-end",children:e.jsx(d,{size:"small",className:"pt-[1px]",children:k(s.subtotal||0,t)})})]})]},s.id),u&&e.jsx(qt,{item:s}),a.map(y=>e.jsx(It,{orderReturn:y,itemId:s.id},y.id)),n.map(y=>e.jsx(Mt,{claim:y,itemId:s.id},y.id)),l.map(y=>e.jsx(Lt,{exchange:y,itemId:s.id},y.id))]})},Et=({order:s,reservations:t})=>{var r;const{claims:i=[]}=je({order_id:s.id,fields:"*additional_items"}),{exchanges:a=[]}=ye({order_id:s.id,fields:"*additional_items"}),{returns:n=[]}=ie({order_id:s.id,fields:"*items,*items.reason"}),l=v.useMemo(()=>new Map((t||[]).map(o=>[o.line_item_id,o])),[t]);return e.jsx("div",{children:(r=s.items)==null?void 0:r.map(o=>{const u=l.get(o.id);return e.jsx(At,{item:o,currencyCode:s.currency_code,reservation:u,returns:n,exchanges:a,claims:i},o.id)})})},se=({label:s,value:t,secondaryValue:i,tooltip:a})=>e.jsxs("div",{className:"grid grid-cols-3 items-center",children:[e.jsxs(d,{size:"small",leading:"compact",children:[s," ",a]}),e.jsx("div",{className:"text-right",children:e.jsx(d,{size:"small",leading:"compact",children:i})}),e.jsx("div",{className:"text-right",children:e.jsx(d,{size:"small",leading:"compact",children:t})})]}),wt=({order:s})=>{var f;const{t}=g(),[i,a]=v.useState(!1),[n,l]=v.useState(!1),r=v.useMemo(()=>{const h=new Set;return s.items.forEach(m=>{var b;return(b=m.adjustments)==null?void 0:b.forEach(y=>{h.add(y.code)})}),Array.from(h).sort()},[s]),o=v.useMemo(()=>{const h={};return s.items.forEach(m=>{var b;(b=m.tax_lines)==null||b.forEach(y=>{h[y.code]=(h[y.code]||0)+y.total})}),s.shipping_methods.forEach(m=>{var b;(b=m.tax_lines)==null||b.forEach(y=>{h[y.code]=(h[y.code]||0)+y.total})}),h},[s]),u=!!((f=s.region)!=null&&f.automatic_taxes),x=!!Object.keys(o).length,j=u?s.discount_total:s.discount_subtotal;return e.jsxs("div",{className:"text-ui-fg-subtle flex flex-col gap-y-2 px-6 py-4",children:[e.jsx(se,{label:t(u?"orders.summary.itemTotal":"orders.summary.itemSubtotal"),value:k(s.item_total,s.currency_code)}),e.jsx(se,{label:e.jsxs("div",{onClick:()=>l(h=>!h),className:"flex cursor-pointer items-center gap-1",children:[e.jsx("span",{children:t(u?"orders.summary.shippingTotal":"orders.summary.shippingSubtotal")}),e.jsx(te,{style:{transform:`rotate(${n?0:-90}deg)`}})]}),value:k(u?s.shipping_total:s.shipping_subtotal,s.currency_code)}),n&&e.jsx("div",{className:"flex flex-col gap-1 pl-5",children:(s.shipping_methods||[]).sort((h,m)=>h.created_at.localeCompare(m.created_at)).map((h,m)=>e.jsxs("div",{className:"flex items-center justify-between gap-x-2",children:[e.jsx("div",{children:e.jsxs("span",{className:"txt-small text-ui-fg-subtle font-medium",children:[h.name,h.detail.return_id&&` (${t("fields.returnShipping")})`," ",e.jsx(Ot,{shippingMethod:h},m)]})}),e.jsx("div",{className:"relative flex-1",children:e.jsx("div",{className:"bottom-[calc(50% - 2px)] absolute h-[1px] w-full border-b border-dashed"})}),e.jsx("span",{className:"txt-small text-ui-fg-muted",children:k(u?h.total:h.subtotal,s.currency_code)})]},h.id))}),e.jsx(se,{label:t(u?"orders.summary.discountTotal":"orders.summary.discountSubtotal"),secondaryValue:r.join(", "),value:j>0?`- ${k(j,s.currency_code)}`:"-"}),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("div",{onClick:()=>x&&a(h=>!h),className:Ne("flex items-center gap-1",{"cursor-pointer":x}),children:[e.jsx("span",{className:"txt-small select-none",children:t(u?"orders.summary.taxTotalIncl":"orders.summary.taxTotal")}),x&&e.jsx(te,{style:{transform:`rotate(${i?0:-90}deg)`}})]}),e.jsx("div",{className:"text-right",children:e.jsx(d,{size:"small",leading:"compact",children:k(s.tax_total,s.currency_code)})})]}),i&&e.jsx("div",{className:"flex flex-col gap-1 pl-5",children:Object.entries(o).map(([h,m])=>e.jsxs("div",{className:"flex items-center justify-between gap-x-2",children:[e.jsx("div",{children:e.jsx("span",{className:"txt-small text-ui-fg-subtle font-medium",children:h})}),e.jsx("div",{className:"relative flex-1",children:e.jsx("div",{className:"bottom-[calc(50% - 2px)] absolute h-[1px] w-full border-b border-dashed"})}),e.jsx("span",{className:"txt-small text-ui-fg-muted",children:k(m,s.currency_code)})]},h))})]})]})},qt=({item:s})=>{var l;const{t}=g(),[i,a]=v.useState(!1),n=((l=s.variant)==null?void 0:l.inventory_items)||[];return e.jsxs(e.Fragment,{children:[e.jsxs("div",{onClick:()=>a(r=>!r),className:"flex cursor-pointer items-center gap-2 border-t border-dashed px-6 py-4",children:[e.jsx(te,{style:{transform:`rotate(${i?0:-90}deg)`}}),e.jsx("span",{className:"text-ui-fg-muted txt-small select-none",children:t("orders.summary.inventoryKit",{count:n.length})})]}),i&&e.jsx("div",{className:"flex flex-col gap-1 px-6 pb-4",children:n.map(r=>e.jsxs("div",{className:"flex items-center justify-between gap-x-2",children:[e.jsx("div",{children:e.jsxs("span",{className:"txt-small text-ui-fg-subtle font-medium",children:[r.inventory.title,r.inventory.sku&&e.jsxs("span",{className:"text-ui-fg-subtle font-normal",children:[" ","⋅ ",r.inventory.sku]})]})}),e.jsx("div",{className:"relative flex-1",children:e.jsx("div",{className:"bottom-[calc(50% - 2px)] absolute h-[1px] w-full border-b border-dashed"})}),e.jsxs("span",{className:"txt-small text-ui-fg-muted",children:[r.required_quantity,"x"]})]},r.inventory.id))})]})},Tt=({orderReturn:s,itemId:t})=>{var l,r;const{t:i}=g(),a=(l=s==null?void 0:s.items)==null?void 0:l.find(o=>o.item_id===t),n=(a==null?void 0:a.damaged_quantity)||0;return a&&e.jsxs("div",{className:"txt-compact-small-plus text-ui-fg-subtle bg-ui-bg-subtle flex flex-row justify-between gap-y-2 border-t-2 border-dotted px-6 py-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(G,{className:"text-ui-fg-muted"}),e.jsx(d,{size:"small",children:i("orders.returns.damagedItemsReturned",{quantity:n})}),(a==null?void 0:a.note)&&e.jsx($,{content:a.note,children:e.jsx(Oe,{className:"text-ui-tag-neutral-icon ml-1 inline"})}),(a==null?void 0:a.reason)&&e.jsx(X,{size:"2xsmall",className:"cursor-default select-none capitalize",rounded:"full",children:(r=a==null?void 0:a.reason)==null?void 0:r.label})]}),e.jsxs(d,{size:"small",leading:"compact",className:"text-ui-fg-muted",children:[i("orders.returns.damagedItemReceived"),e.jsx("span",{className:"ml-2",children:e.jsx(ae,{orderReturn:s})})]})]},s.id)},It=({orderReturn:s,itemId:t})=>{var o,u;const{t:i}=g(),{getRelativeDate:a}=R();if(!["requested","received","partially_received"].includes(s.status||""))return null;const n=s.status==="requested",l=(o=s==null?void 0:s.items)==null?void 0:o.find(x=>x.item_id===t),r=(l==null?void 0:l.damaged_quantity)||0;return l&&e.jsxs(e.Fragment,{children:[r>0&&e.jsx(Tt,{orderReturn:s,itemId:t}),e.jsxs("div",{className:"txt-compact-small-plus text-ui-fg-subtle bg-ui-bg-subtle flex flex-row justify-between gap-y-2 border-t-2 border-dotted px-6 py-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(G,{className:"text-ui-fg-muted"}),e.jsx(d,{size:"small",children:i(`orders.returns.${n?"returnRequestedInfo":"returnReceivedInfo"}`,{requestedItemsCount:l==null?void 0:l[n?"quantity":"received_quantity"]})}),(l==null?void 0:l.note)&&e.jsx($,{content:l.note,children:e.jsx(Oe,{className:"text-ui-tag-neutral-icon ml-1 inline"})}),(l==null?void 0:l.reason)&&e.jsx(X,{size:"2xsmall",className:"cursor-default select-none capitalize",rounded:"full",children:(u=l==null?void 0:l.reason)==null?void 0:u.label})]}),s&&n&&e.jsxs(d,{size:"small",leading:"compact",className:"text-ui-fg-muted",children:[a(s.created_at),e.jsx("span",{className:"ml-2",children:e.jsx(ae,{orderReturn:s})})]}),s&&!n&&e.jsxs(d,{size:"small",leading:"compact",className:"text-ui-fg-muted",children:[i("orders.returns.itemReceived"),e.jsx("span",{className:"ml-2",children:e.jsx(ae,{orderReturn:s})})]})]},l.id)]})},Mt=({claim:s,itemId:t})=>{const{t:i}=g(),{getRelativeDate:a}=R(),n=s.additional_items.filter(l=>{var r;return((r=l.item)==null?void 0:r.id)===t});return!!n.length&&e.jsxs("div",{className:"txt-compact-small-plus text-ui-fg-subtle bg-ui-bg-subtle flex flex-row justify-between gap-y-2 border-b-2 border-t-2 border-dotted px-6 py-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(G,{className:"text-ui-fg-muted"}),e.jsx(d,{size:"small",children:i("orders.claims.outboundItemAdded",{itemsCount:n.reduce((l,r)=>l=l+r.quantity,0)})})]}),e.jsx(d,{size:"small",leading:"compact",className:"text-ui-fg-muted",children:a(s.created_at)})]},s.id)},Lt=({exchange:s,itemId:t})=>{const{t:i}=g(),{getRelativeDate:a}=R(),n=s.additional_items.filter(l=>{var r;return((r=l==null?void 0:l.item)==null?void 0:r.id)===t});return!!n.length&&e.jsxs("div",{className:"txt-compact-small-plus text-ui-fg-subtle bg-ui-bg-subtle flex flex-row justify-between gap-y-2 border-b-2 border-t-2 border-dotted px-6 py-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(G,{className:"text-ui-fg-muted"}),e.jsx(d,{size:"small",children:i("orders.exchanges.outboundItemAdded",{itemsCount:n.reduce((l,r)=>l=l+r.quantity,0)})})]}),e.jsx(d,{size:"small",leading:"compact",className:"text-ui-fg-muted",children:a(s.created_at)})]},s.id)},Dt=({order:s})=>{const{t}=g();return e.jsxs("div",{className:" flex flex-col gap-y-2 px-6 py-4",children:[e.jsxs("div",{className:"text-ui-fg-base flex items-center justify-between",children:[e.jsx(d,{weight:"plus",className:"text-ui-fg-subtle",size:"small",leading:"compact",children:t("fields.total")}),e.jsx(d,{weight:"plus",className:"text-ui-fg-subtle",size:"small",leading:"compact",children:A(s.total,s.currency_code)})]}),e.jsxs("div",{className:"text-ui-fg-base flex items-center justify-between",children:[e.jsx(d,{weight:"plus",className:"text-ui-fg-subtle",size:"small",leading:"compact",children:t("fields.paidTotal")}),e.jsx(d,{weight:"plus",className:"text-ui-fg-subtle",size:"small",leading:"compact",children:A(Fe(s.payment_collections||[]),s.currency_code)})]}),e.jsxs("div",{className:"text-ui-fg-base flex items-center justify-between",children:[e.jsx(d,{className:"text-ui-fg-subtle text-semibold",size:"small",leading:"compact",weight:"plus",children:t("orders.returns.outstandingAmount")}),e.jsx(d,{className:"text-ui-fg-subtle text-bold",size:"small",leading:"compact",weight:"plus",children:A(s.summary.pending_difference||0,s.currency_code)})]})]})},ba=()=>{const s=ns(),{id:t}=ls(),{getWidgets:i}=cs(),{order:a,isLoading:n,isError:l,error:r}=ve(t,{fields:ne},{initialData:s});a&&(a.items=a.items.sort((x,j)=>x.created_at>j.created_at?1:x.created_at<j.created_at?-1:0));const{order:o,isLoading:u}=le(t);if(n||!a||u)return e.jsx(rs,{mainSections:4,sidebarSections:2,showJSON:!0});if(l)throw r;return e.jsxs(Z,{widgets:{after:i("order.details.after"),before:i("order.details.before"),sideAfter:i("order.details.side.after"),sideBefore:i("order.details.side.before")},data:a,showJSON:!0,showMetadata:!0,hasOutlet:!0,children:[e.jsxs(Z.Main,{children:[e.jsx(Ys,{order:a}),e.jsx(Ws,{orderPreview:o}),e.jsx(Hs,{orderPreview:o}),e.jsx(Vs,{orderPreview:o}),e.jsx(jt,{order:a}),e.jsx(St,{order:a}),e.jsx(Be,{order:a}),e.jsx(pt,{order:a})]}),e.jsxs(Z.Sidebar,{children:[e.jsx(ut,{order:a}),e.jsx(lt,{order:a})]})]})};export{ya as Breadcrumb,ba as Component,va as loader};
