import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { InputNumber } from '../input-number';

describe('InputNumber', () => {
  // Basic rendering tests
  it('renders correctly with default props', () => {
    render(<InputNumber data-testid="test-input" />);
    
    const inputElement = screen.getByTestId('test-input');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveAttribute('type', 'number');
    expect(inputElement).toHaveValue(0); // Default value
    
    // Increment and decrement buttons should be visible
    const incrementButton = screen.getByTestId('increment-button');
    const decrementButton = screen.getByTestId('decrement-button');
    expect(incrementButton).toBeInTheDocument();
    expect(decrementButton).toBeInTheDocument();
  });
  
  it('renders with label and helper text', () => {
    render(
      <InputNumber 
        id="test-input"
        label="Test Label"
        helperText="Test helper text"
      />
    );
    
    expect(screen.getByText('Test Label')).toBeInTheDocument();
    expect(screen.getByText('Test helper text')).toBeInTheDocument();
  });
  
  // Functionality tests
  it('allows direct input of numbers', async () => {
    const handleChange = jest.fn();
    
    render(
      <InputNumber 
        onChange={handleChange}
        data-testid="test-input"
      />
    );
    
    const input = screen.getByTestId('test-input');
    
    // Clear and type a new value
    fireEvent.change(input, { target: { value: '42' } });
    
    expect(input).toHaveValue(42);
    expect(handleChange).toHaveBeenCalledWith(42);
  });
  
  it('increments value when increment button is clicked', () => {
    const handleChange = jest.fn();
    
    render(
      <InputNumber 
        defaultValue={10}
        onChange={handleChange}
      />
    );
    
    const incrementButton = screen.getByTestId('increment-button');
    fireEvent.click(incrementButton);
    
    expect(screen.getByRole('spinbutton')).toHaveValue(11);
    expect(handleChange).toHaveBeenCalledWith(11);
  });
  
  it('decrements value when decrement button is clicked', () => {
    const handleChange = jest.fn();
    
    render(
      <InputNumber 
        defaultValue={10}
        onChange={handleChange}
      />
    );
    
    const decrementButton = screen.getByTestId('decrement-button');
    fireEvent.click(decrementButton);
    
    expect(screen.getByRole('spinbutton')).toHaveValue(9);
    expect(handleChange).toHaveBeenCalledWith(9);
  });
  
  it('respects min and max constraints', () => {
    const handleChange = jest.fn();
    
    render(
      <InputNumber 
        defaultValue={5}
        min={0}
        max={10}
        onChange={handleChange}
      />
    );
    
    const input = screen.getByRole('spinbutton');
    const incrementButton = screen.getByTestId('increment-button');
    const decrementButton = screen.getByTestId('decrement-button');
    
    // Test max constraint
    for (let i = 0; i < 10; i++) {
      fireEvent.click(incrementButton);
    }
    
    expect(input).toHaveValue(10); // Should not exceed max
    
    // Test min constraint
    for (let i = 0; i < 20; i++) {
      fireEvent.click(decrementButton);
    }
    
    expect(input).toHaveValue(0); // Should not go below min
  });
  
  it('respects step value', () => {
    render(
      <InputNumber 
        defaultValue={10}
        step={5}
      />
    );
    
    const input = screen.getByRole('spinbutton');
    const incrementButton = screen.getByTestId('increment-button');
    
    fireEvent.click(incrementButton);
    expect(input).toHaveValue(15); // Should increment by step value (5)
  });
  
  // Keyboard accessibility tests
  it('supports keyboard arrow controls', () => {
    const handleChange = jest.fn();
    
    render(
      <InputNumber 
        defaultValue={10}
        onChange={handleChange}
      />
    );
    
    const input = screen.getByRole('spinbutton');
    
    // Test arrow up
    fireEvent.keyDown(input, { key: 'ArrowUp' });
    expect(input).toHaveValue(11);
    expect(handleChange).toHaveBeenCalledWith(11);
    
    // Test arrow down
    fireEvent.keyDown(input, { key: 'ArrowDown' });
    expect(input).toHaveValue(10);
    expect(handleChange).toHaveBeenCalledWith(10);
  });
  
  // Controlled component test
  it('works as a controlled component', () => {
    const TestComponent = () => {
      const [value, setValue] = React.useState(42);
      return (
        <>
          <InputNumber 
            value={value}
            onChange={setValue}
            data-testid="controlled-input"
          />
          <div data-testid="value-display">{value}</div>
        </>
      );
    };
    
    render(<TestComponent />);
    
    const input = screen.getByTestId('controlled-input');
    const display = screen.getByTestId('value-display');
    
    expect(input).toHaveValue(42);
    expect(display).toHaveTextContent('42');
    
    const incrementButton = screen.getByTestId('increment-button');
    fireEvent.click(incrementButton);
    
    expect(input).toHaveValue(43);
    expect(display).toHaveTextContent('43');
  });
  
  // A11y tests
  it('has proper ARIA attributes', () => {
    render(
      <InputNumber 
        id="a11y-test"
        min={0}
        max={100}
        defaultValue={50}
      />
    );
    
    const input = screen.getByRole('spinbutton');
    
    expect(input).toHaveAttribute('aria-valuemin', '0');
    expect(input).toHaveAttribute('aria-valuemax', '100');
    expect(input).toHaveAttribute('aria-valuenow', '50');
    expect(input).toHaveAttribute('aria-valuetext', '50');
  });
});