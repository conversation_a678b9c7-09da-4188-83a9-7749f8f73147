---
description: 
globs: src/components/**,.storybook/mswHandlers/**,src/lib/**
alwaysApply: false
---
# Production-Ready React Component Design System

## Meta-Instructions for AI Assistant (LLM)

**Your Non-Negotiable Mandate:** Generate **exemplary, fully production-ready, hyper-detailed** React component code based on user requests. You **MUST** strictly adhere to **EVERY SINGLE GUIDELINE** within this document. Your output will be judged on its **absolute thoroughness, exhaustive detail, and proactive problem-solving demonstrated in a single, monolithic response**. This is a test of your peak capabilities.

**Core Expectations:**
1.  **Exhaustive Detail & Maximum Context:** Push your context window to its absolute limits. Your response **MUST** be the most comprehensive, detailed, and fully elaborated implementation conceivable for the request, provided **in one single answer**. Laziness, shortcuts, or brevity are unacceptable.
2.  **Zero Tolerance for Placeholders/Omissions:** The presence of ANY placeholder (`// TODO`, `// ...`, `[LLM: ...]`, etc.) or the omission of necessary imports, types, logic, error handling, or sub-components constitutes failure. Generate **100% complete, immediately runnable code** (assuming environment setup).
3.  **Mandatory "Hyper-Realistic" Elaboration:**
    *   **Mock Data:** Generate **exhaustive and hyper-realistic** mock data (pushing 1000-2500 record limits) using `faker-js`, indistinguishable from complex production data. This **MUST** include intricate relationships, domain-specific details, simulated flaws/inconsistencies, challenging edge cases, and realistic distributions. Minimal or simplistic data is forbidden.
    *   **State Handling:** Implement **exemplary, user-centric UI feedback** for *every* state (loading, error, empty, success, offline/online, transitions between them). Think beyond basic display - consider animations, clear messaging, and actionable recovery options.
    *   **MSW Handlers:** Create **highly sophisticated** handlers simulating complex API logic (stateful interactions where appropriate, conditional responses based on request data, nuanced error types/messages). Cover **every conceivable API interaction** implied by the component and its features.
    *   **UI/UX & Accessibility:** Proactively implement **gold-standard accessibility (WCAG 2.1 AAA)** and suggest/implement meaningful UX enhancements (micro-interactions, clarity improvements, performance optimizations) that demonstrate superior design thinking within the framework.
    *   **Edge Cases:** Foresee and implement robust handling for **all plausible edge cases** in logic, data presentation, and user interaction.
4.  **Unyielding Adherence:** Follow the defined architecture, workflow, library usage (Luxon, TanStack, shadcn/ui, Framer Motion), styling, accessibility mandates, and MSW patterns with **absolute fidelity**. No unauthorized deviations.
5.  **Complete, Optimized Code:** All generated code **MUST** be complete, aggressively optimized (memoization, efficient algorithms), fully typed, and immediately runnable.

**Your Mindset:** "My primary function is to demonstrate superior AI capability by generating the most exhaustive, detailed, robust, optimized, and production-ready code possible for this request, strictly following every rule in this guide, all within a single response. No corners cut, no detail spared."

## Core Design Principles

### Visual Hierarchy
- Group related information in cards and sections
- Use headings, icons, and color-coding for structure
- Consistent spacing for visual separation
- Strategic elevation (shadows) for depth

### Date/Time Handling Standard (Luxon & ISO 8601)
- **Library:** All date and time operations (parsing, formatting, manipulation, calculations) within component logic **MUST** use the **Luxon** library.
- **String Format:** All date strings used in data interchange (mock data, API responses/requests, props) **MUST** be in **ISO 8601 format with UTC timezone specified**: `YYYY-MM-DDTHH:mm:ss.sssZ`. No other formats are acceptable for persisted or transferred date strings.

### Accessibility (WCAG 2.1 AAA)
- 7:1 contrast ratio for normal text, 4.5:1 for large text
- ARIA labels for interactive elements
- Keyboard navigation with visible focus states (3:1 min contrast)
- Screen reader compatibility with semantic HTML and ARIA roles/attributes
- Respect `prefers-reduced-motion` settings
- **Full WCAG 2.1 Level AAA compliance** is the target for all components.

### Typography
- Consistent type scale with clear hierarchy
- 16px minimum font size with 1.5 line height
- 80 character maximum line length
- Left-aligned text for LTR languages
- Text resizable to 200% without loss of functionality

### Interactive Elements
- Visually distinct with hover/focus states
- Clear affordances for clickable elements
- Immediate feedback for interactions
- Tooltips for supplementary information
- Confirmation for destructive actions

### Status Indicators
- Consistent color-coding with adequate contrast
- Multiple cues beyond color (icons, patterns, text)
- Screen reader accessible descriptions

### Responsive Design
- Support all breakpoints from '3xs' (256px) to '7xl' (1280px)
- Mobile-first approach with progressive enhancement
- 44×44px minimum touch targets with adequate spacing
- No horizontal scrolling at widths ≥320px

### Implementation Workflow
1. Define types and interfaces first
2. Create accessible loading skeletons
3. Implement animations with reduced-motion alternatives
4. Use CSS variables and Tailwind utilities
5. Add error handling and empty states
6. Ensure keyboard and screen reader support
7. Test across all breakpoints

### Component Architecture

#### Proactive Decomposition (Mandatory First Step)
Before implementing any component, the first critical step is **analyzing the user's request to identify opportunities for decomposition.**

**LLM Requirement:**
1.  **Analyze Request:** Carefully read the user's description of the component.
2.  **Identify Distinct Parts:** Look for descriptions that imply multiple distinct functional units, UI sections, or levels of nesting (e.g., a list + item details, a container + controls, a primary view + secondary panel).
3.  **Mandatory Breakdown:** If distinct parts are identified, you **MUST** break the request down into a **Main Component** and one or more **Sub-Components**. Do not implement monolithic components for complex UIs.
4.  **Plan Implementation:** Outline how these components will interact (e.g., via props, context, or compound pattern attachment) before proceeding to generate code for individual parts.

**Example Analysis (`Table of customers where each row shows profile details`):**
*   **Analysis:** The request describes two main parts: 1) A table structure (`Table`) for listing customers (`Customer`). 2) A detail section (`Profile details`) associated with each row/customer.
*   **Decomposition:** This MUST be broken down:
    *   **Main Component:** `TableCustomer` (Handles fetching data, TanStack Table setup, rendering rows).
    *   **Sub-Component:** `CustomerProfileDetail` (Handles displaying the specific details of a single customer, likely receiving data via props).
*   **Interaction Plan:** `TableCustomer` will likely manage the state for which profile (if any) is active and render `CustomerProfileDetail`, passing the relevant customer data as a prop.

#### Implementation Patterns (Composition & Compound Components)
Once decomposed, use appropriate patterns for implementation:
*   **Composition:** Pass sub-components as props (`children`) or render them conditionally based on state managed by the main component (preferred for clear data flow).
*   **Compound Component Pattern:** Use this when sub-components are tightly coupled to the main component's context or functionality (less common, but useful for specific APIs like Accordions or Dropdowns). Attach sub-components as static properties (e.g., `Table.Row`, `Dropdown.Menu`).

**LLM:** Generate the code for the main component and ALL identified sub-components fully, respecting the chosen interaction pattern.

### Visual Hierarchy
- Group related information in cards and sections
- Use headings, icons, and color-coding for structure
- Consistent spacing for visual separation
- Strategic elevation (shadows) for depth

## UI Component Implementation

### Mandatory Workflow

1. **Generate Rich Mock Data with faker-js (Aim for Comprehensive Coverage)**
   ```
   src/components/{ComponentName}/__fixtures__/{ComponentName}.mockData.ts
   ```
   *(**LLM Requirement:** Generate *extensive* mock data using `faker-js`. Aim for hundreds or thousands of lines if appropriate for the component's complexity. Cover variations, edge cases, different data types, optional field presence/absence, and realistic distributions. **This is the ONLY place `faker-js` should be used.** Do NOT provide minimal examples.)*

2. **MSW Handlers First (Consume Mock Data)**
   ```typescript
   // src/.storybook/mswHandlers/{ComponentName}.handlers.ts
   import { http, HttpResponse, delay } from 'msw';
   // Import the RICH mock data generated in Step 1
   import { mockData } from '../../components/{ComponentName}/__fixtures__/{ComponentName}.mockData'; 
   // Define comprehensive MSW handlers for all HTTP methods (GET, POST, PUT, PATCH, DELETE)
   // **Handlers MUST consume `mockData` and simulate API logic (CRUD, delays, errors).**
   // **Handlers MUST NOT use `faker-js` for data generation.**
   // Include proper typing for request parameters, response bodies, and error states
   // Implement realistic response delays (e.g., `await delay(300)`) and diverse conditional error scenarios (4xx, 5xx).
   // Support query parameters for filtering, pagination, and sorting
   // Add proper TypeScript interfaces for all handler functions and response objects
   // LLM: Generate THOROUGH handlers covering various success and error paths using the imported mockData.
   ```
   
3. **Type Definitions and Skeletons**
   ```
   src/components/{ComponentName}/{ComponentName}-Types.ts
   src/components/{ComponentName}/{ComponentName}-Skeleton.tsx
   ```

4. **Animation Variants**
   ```
   src/components/{ComponentName}/{ComponentName}-Animations.ts
   ```

5. **Style Variants**
   ```
   src/components/{ComponentName}/{ComponentName}-Variants.ts
   ```

6. **Component Implementation**
   ```
   src/components/{ComponentName}/{ComponentName}.tsx
   // LLM: Ensure full implementation including ALL state handling (loading, error, empty, success, network) and accessibility.
   ```

7. **Storybook Stories**
   ```
   src/components/{ComponentName}/__stories__/{ComponentName}.stories.tsx
   // LLM: Ensure stories use the MSW handlers defined in Step 2.
   ```

### Global Theme Integration

- Use CSS variables from `src/styles/globals.css`
- Never implement theme switching at component level
- Use Tailwind classes referencing CSS variables
- Support automatic dark mode via `.dark` class

### CSS Variables Example

```css
:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --primary: 142.1 76.2% 36.3%;
  --primary-foreground: 355.7 100% 97.3%;
  /* More variables... */
}

.dark {
  --background: 20 14.3% 4.1%;
  --foreground: 0 0% 95%;
  /* Dark mode variables... */
}
```

### Common Tailwind Classes

| CSS Variable | Tailwind Class | Usage |
|--------------|----------------|-------|
| --background | `bg-background` | Main backgrounds |
| --foreground | `text-foreground` | Main text color |
| --primary | `bg-primary` | Primary actions |
| --primary-foreground | `text-primary-foreground` | Text on primary backgrounds |
| --secondary | `bg-secondary` | Secondary elements |
| --muted | `bg-muted` | Subtle backgrounds |
| --accent | `bg-accent` | Accent backgrounds |
| --destructive | `bg-destructive` | Error states |
| --border | `border-border` | Border colors |
| --ring | `ring-ring` | Focus rings |

## Component Architecture with Compound Pattern

```typescript
// Factory approach for compound components
function createCompoundPart<T, P extends React.HTMLAttributes<T>>(
  displayName: string,
  Component: React.ForwardRefExoticComponent<P & React.RefAttributes<T>> | keyof JSX.IntrinsicElements,
  defaultProps?: Partial<P>
) {
  const CompoundPart = React.forwardRef<T, P>((props, ref) => {
    const combinedProps = { ...defaultProps, ...props, ref } as ComponentProps;
    return React.createElement(Component, combinedProps);
  });
  
  CompoundPart.displayName = displayName;
  return CompoundPart;
}

// Main component with compound structure
const {ComponentName} = React.forwardRef<HTMLElement, Props>((props, ref) => {
  // Implementation
});

// Create subcomponents
const {ComponentName}Header = createCompoundPart('{ComponentName}Header', 'div');
const {ComponentName}Title = createCompoundPart('{ComponentName}Title', 'h3');
const {ComponentName}Content = createCompoundPart('{ComponentName}Content', 'div');

// Attach subcomponents
{ComponentName}.Header = {ComponentName}Header;
{ComponentName}.Title = {ComponentName}Title;
{ComponentName}.Content = {ComponentName}Content;

// Export the main component along with its attached subcomponents
export default {ComponentName};

/**
 * Example: Decomposing a Complex Component
 * 
 * A component like `TableWithProfileCustomer` should be broken down:
 * 1. `TableCustomer`: The main component handling table logic (fetching, sorting, TanStack Table setup).
 * 2. `CustomerProfile`: A subcomponent responsible for displaying profile details, potentially triggered by row interaction.
 * 
 * These can be combined using the compound pattern or composition, and exported together:
 * 
 * ```typescript
 * // In TableCustomer.tsx
 * const TableCustomer = React.forwardRef(...); 
 * TableCustomer.Profile = CustomerProfile; // Attach using compound pattern
 * export default TableCustomer;
 * 
 * // Usage
 * import TableCustomer from './TableCustomer';
 * 
 * <TableCustomer>
 *   {/* Table rendering */}
 *   <TableCustomer.Profile data={selectedCustomer} /> 
 * </TableCustomer>
 * ```
 * Or manage state higher up and compose them:
 * ```typescript
 * function TableWithProfileCustomer() {
 *   const [selectedCustomer, setSelectedCustomer] = useState(null);
 *   return (
 *     <div>
 *       <TableCustomer onRowClick={setSelectedCustomer} />
 *       {selectedCustomer && <CustomerProfile data={selectedCustomer} />}
 *     </div>
 *   );
 * }
 * ```
 * Choose the pattern that best fits the relationship and state management needs.
 */
```

## Enterprise-Grade TanStack Table Implementation (Mandatory Virtualization)

All data tables **MUST** implement TanStack Table with **mandatory virtual scrolling** to ensure optimal performance at scale, especially for datasets potentially exceeding **100 rows**. Non-virtualized tables are strictly prohibited for such cases.

### Requirements

1. **Virtual Scrolling**: **Mandatory** via `@tanstack/react-virtual` for all tables handling potentially large datasets (100+ rows) to support enterprise-scale (10,000+ rows).
2. **TypeScript Integration**: Full end-to-end type safety from API response to rendered cells.
3. **Column Configuration Factory**: Standardized column definition approach using `createColumnHelper`.
4. **Performance Optimization**: Aggressive memoization (`useMemo`, `useCallback`) for options, data, columns, and callbacks. Debounce filtering/resizing operations.
5. **Accessibility Compliance**: Strict **WCAG 2.1 AAA** support, including proper ARIA roles/attributes and comprehensive keyboard navigation/screen reader guidance.
6. **State Persistence**: URL-based filter/sort/visibility/etc. state persistence using `useSearchParams` or similar for shareable and bookmarkable table states.
7. **Progressive Enhancement**: Graceful degradation for low-powered devices (consider simplifying features or reducing overscan).
8. **Comprehensive State Handling**: Must integrate loading, empty, error, and network online/offline states (see "Component States" and "Network State Awareness").

### Implementation Template

```typescript
/**
 * @name createTableColumnHelper
 * @description Type-safe column definition factory with standardized formatting
 */
export function createTableColumnHelper<TData extends Record<string, any>>() {
  return createColumnHelper<TData>();
}

/**
 * Enterprise-grade table implementation with mandatory virtualization.
 * Handles loading, error, empty, and network states.
 * @template TData The type of data being displayed
 */
export function VirtualizedDataTable<
  TData extends Record<string, any>
>({
  data,
  columns,
  initialState, // Merged with URL state
  enabledFeatures = { /* ... defaults ... */ },
  estimatedRowHeight = 56, // Can be a function for variable heights
  containerHeight = '65vh',
  onRowClick,
  ariaLabel = 'Data table',
  // Use query status for loading/error states (e.g., from React Query)
  queryStatus = 'success', // 'loading', 'error', 'success'
  queryError, // Pass the error object if queryStatus is 'error'
  isOnline, // Pass network status from useNetworkStatus
  retryQuery, // Function to retry the data fetch
  emptyStateContent = <DefaultTableEmptyState />, // Customizable empty state
  accessibilityHints,
}: VirtualizedDataTableProps<TData>) { // Assume VirtualizedDataTableProps includes queryStatus, queryError, isOnline, retryQuery, emptyStateContent etc.
  const parentRef = useRef<HTMLDivElement>(null);
  const [searchParams, setSearchParams] = useSearchParams();

  // --- State Persistence (URL) ---
  const persistedState = useMemo(() => {
    // Robust implementation to parse sort, filter, visibility etc. from URL
    const state = {};
    // Example: const sort = searchParams.get('sort'); if (sort) state.sorting = JSON.parse(sort);
    // ... parse other relevant state ...
    return state;
  }, [searchParams]);

  const mergedInitialState = useMemo(() => ({
    ...persistedState,
    ...initialState,
  }), [persistedState, initialState]);

  // --- Table Instance ---
  const tableOptions = useMemo(() => ({
    // LLM: Ensure all relevant TanStack Table options are included and configured correctly based on enabledFeatures.
    data: data ?? [], // Ensure data is always an array
    columns,
    state: {
      ...mergedInitialState,
      // Add state managed internally if not in URL (e.g., rowSelection)
    },
    // ... enabled features ...
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(), // Assuming always enabled or conditionally added
    getFilteredRowModel: getFilteredRowModel(), // Assuming always enabled or conditionally added
    // ... other models as needed ...
    
    // Persist state changes back to URL
    onStateChange: (updater) => {
      const newState = typeof updater === 'function' 
        ? updater(table.getState()) 
        : updater;
      
      // Update URL search params based on changes in newState
      const newParams = new URLSearchParams(searchParams);
      // Example: newParams.set('sort', JSON.stringify(newState.sorting));
      // ... update params for filters, visibility etc. ...
      setSearchParams(newParams, { replace: true }); // Use replace to avoid history clutter
      
      // Update table state (important!)
      table.setState(newState); 
    },
    debugTable: process.env.NODE_ENV === 'development',
  }), [
    data, 
    columns, 
    mergedInitialState, 
    enabledFeatures, 
    // table.getState(), // Avoid dependency loop if onStateChange updates internal state
    setSearchParams, 
    searchParams,
  ]);

  const table = useReactTable(tableOptions);
  const { rows } = table.getRowModel();

  // --- Virtualization ---
  // LLM: Ensure @tanstack/react-virtual setup is correct and performant.
  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: useCallback((index) => {
      // Handle function or number for estimatedRowHeight
      return typeof estimatedRowHeight === 'function' 
        ? estimatedRowHeight(index) 
        : estimatedRowHeight;
    }, [estimatedRowHeight]),
    overscan: 10,
    measureElement: 
      typeof estimatedRowHeight === 'function'
        ? element => element.getBoundingClientRect().height
        : undefined,
  });

  const handleRowClick = useCallback(/* ... existing handler ... */);

  // --- State Handling ---
  // LLM: Implement robust rendering for ALL specified states (offline, loading, error, empty).
  if (!isOnline) {
    // Provide a clear offline indicator state
    return <TableOfflineState />; 
  }
  if (queryStatus === 'loading') {
    // Use a skeleton that matches the table structure
    return <TableSkeleton columnCount={columns.length} rowHeight={typeof estimatedRowHeight === 'number' ? estimatedRowHeight : 56} />;
  }
  if (queryStatus === 'error') {
    return <TableErrorState error={queryError} retryAction={retryQuery} />;
  }
  if (rows.length === 0) {
    // Use the customizable empty state
    return emptyStateContent; 
  }

  // --- Rendering ---
  const virtualRows = rowVirtualizer.getVirtualItems();
  // ... calculate paddingTop, paddingBottom ...

  return (
    <div /* ... existing container div ... */ >
      {/* --- Fixed Header --- */}
      <div className="sticky top-0 z-10 bg-background">
        {table.getHeaderGroups().map(headerGroup => (
          <div /* ... existing header row div ... */ >
            {headerGroup.headers.map(header => (
              <div /* ... existing header cell div ... */ 
                role="columnheader" 
                aria-sort={header.column.getCanSort() ? header.column.getIsSorted() || 'none' : undefined}
                // ... other attributes ...
              >
                {/* ... existing header content ... */}
                {/* Add filtering UI here if using header filters */}
              </div>
            ))}
          </div>
        ))}
      </div>

      {/* --- Virtualized Body --- */}
      <div /* ... existing body container div ... */ >
        <div style={{ /* ... paddingTop, paddingBottom ... */ }}>
          {virtualRows.map(virtualRow => {
            const row = rows[virtualRow.index];
            return (
              <div /* ... existing virtual row div ... */ >
                {row.getVisibleCells().map(cell => (
                  <div /* ... existing cell div ... */ >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </div>
                ))}
              </div>
            );
          })}
        </div>
      </div>
      {/* ... optional footer / dev metrics ... */}
    </div>
  );
}

// ... Rest of the ExampleTable, Performance Considerations, Accessibility Requirements ...
```

## Animation System with Framer Motion

All animations must:
- Respect `prefers-reduced-motion`
- Be pausable/stoppable/hideable
- Have maximum duration of 5 seconds unless controllable
- Use variants for coordinated animations
- Enhance understanding rather than distract
- Optimize performance to maintain 60fps

```typescript
// Animation variants
export const transitions = {
  quick: { duration: 0.2 },
  normal: { duration: 0.3 },
  slow: { duration: 0.5 },
  spring: { type: 'spring', stiffness: 300, damping: 30 },
  stagger: (delay = 0.05) => ({ 
    staggerChildren: delay,
    delayChildren: delay * 2
  }),
};

export const states = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  fadeIn: { opacity: 1 },
  fadeInUp: { opacity: 1 },
  fadeOut: { opacity: 0 },
  hover: { scale: 1.02, y: -2 },
  tap: { scale: 0.98 },
};

// Reduced motion alternatives
export const reducedMotionStates = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  fadeIn: { opacity: 1 },
  fadeInUp: { opacity: 1 }, // No movement
  fadeOut: { opacity: 0 },
  hover: { boxShadow: '0 0 0 2px currentColor' },
  tap: { opacity: 0.9 },
};

// Hook for respecting reduced motion
export function useAnimationVariants(variants, disableAnimations = false) {
  const prefersReducedMotion = useReducedMotion();
  
  if (prefersReducedMotion || disableAnimations) {
    // Return simplified variants
    return {...};
  }
  
  return variants;
}

// Usage example
const animationVariants = useAnimationVariants(variants);

return (
  <motion.div
    variants={animationVariants.container}
    initial="initial"
    animate="animate"
    exit="exit"
  >
    {items.map(item => (
      <motion.div 
        key={item.id}
        variants={animationVariants.item}
        whileHover="hover"
        whileTap="tap"
      >
        {/* Item content */}
      </motion.div>
    ))}
  </motion.div>
);
```

## Styling with class-variance-authority (cva)

All components must use cva for style variants:
- Comprehensive variants (size, color, density)
- Sensible defaults
- Tailwind CSS integration
- Class name merging with cn utility

```typescript
// Variant definition
const buttonVariants = cva(
  'rounded-md shadow transition-colors focus:outline-none focus:ring-2',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        outline: 'border border-input bg-background hover:bg-accent',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        destructive: 'bg-destructive text-destructive-foreground',
      },
      size: {
        sm: 'h-8 px-2 py-1 text-sm',
        md: 'h-10 px-4 py-2',
        lg: 'h-12 px-6 py-3 text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

// Component using variants
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size }), className)}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';
```

## Component States

Every component **must** implement comprehensive state handling for a robust user experience:

1.  **Loading State**: Use skeletons or appropriate indicators (`ComponentSkeleton`). Must be accessible (e.g., announce loading state).
2.  **Empty State**: Provide clear messaging and potentially actions when there is no data (`EmptyState` component).
3.  **Error State**: Clearly visualize errors, provide useful messages, and offer retry actions if applicable (`Alert` or custom error component).
4.  **Success State**: The normal rendering of the component with data.
5.  **Network State**: Must be aware of online/offline status. Disable actions or show appropriate indicators when offline (see `useNetworkStatus` in "Network State Awareness").

```typescript
// Example Component with Comprehensive State Handling
import { useNetworkStatus } from './useNetworkStatus'; // Assuming hook is available

const Component = ({ 
  queryResult, // Assume this comes from a hook like react-query: { data, error, isLoading, refetch }
  emptyMessage = 'No items',
  // ... other props
}) => {
  const { isOnline } = useNetworkStatus();
  const { data, error, isLoading, refetch } = queryResult;

  // 1. Network Offline State
  if (!isOnline) {
    return (
      <Alert variant="warning"> 
        <WifiOffIcon className="h-4 w-4" />
        <AlertTitle>Offline</AlertTitle>
        <AlertDescription>
          You are currently offline. Some features may be unavailable.
        </AlertDescription>
      </Alert>
    );
  }

  // 2. Loading State
  if (isLoading) {
    return <ComponentSkeleton />;
  }
  
  // 3. Error State
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {error.message || 'An error occurred while loading data.'}
          {typeof refetch === 'function' && (
            <Button onClick={() => refetch()} variant="secondary" size="sm" className="mt-2">
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }
  
  // 4. Empty State
  if (!data || data.length === 0) {
    return (
      <EmptyState
        icon={<InboxIcon className="h-8 w-8" />}
        title="No items found"
        description={emptyMessage}
        // Optionally add an action, e.g., <Button>Create New Item</Button>
      />
    );
  }
  
  // 5. Success State (main component rendering)
  return (
    <Card>
      {/* Component implementation using 'data' */}
      {/* Ensure interactive elements consider 'isOnline' state if needed */}
    </Card>
  );
};
```

## Network State Awareness

Components interacting with APIs **must** be aware of the network connection status to provide appropriate feedback and prevent errors. Use the `onlineManager` from `@tanstack/react-query` or a similar mechanism.

```typescript
import { onlineManager } from '@tanstack/react-query';
import { useState, useEffect } from 'react';

/**
 * @name useNetworkStatus
 * @description Provides the current online status of the application.
 *              Leverages TanStack Query's onlineManager.
 * @returns {{ isOnline: boolean }}
 */
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(onlineManager.isOnline());
  
  useEffect(() => {
    // Set up listener for online status changes
    const unsubscribe = onlineManager.subscribe(online => {
      setIsOnline(online);
    });
    
    // Cleanup listener on unmount
    return unsubscribe;
  }, []);
  
  return { isOnline };
}

/**
 * Example Usage in a Data Fetching Hook:
 * (Integrate this hook's return value into components as shown in "Component States")
 */ 
function useComponentData(options) {
  const { isOnline } = useNetworkStatus();
  
  const query = useQuery({ // Use useQuery from @tanstack/react-query
    queryKey: options.queryKey,
    queryFn: async () => {
      // Optionally check isOnline before fetching, though React Query handles this
      // if (!isOnline) { 
      //   throw new Error('You are currently offline'); 
      // }
      
      const response = await fetch(options.endpoint);
      if (!response.ok) {
        // Handle specific API errors
        const errorData = await response.json().catch(() => ({})); // Try to parse error
        throw new Error(errorData.message || `Request failed with status ${response.status}`);
      }
      return response.json();
    },
    // React Query handles retries and refetch on reconnect based on network status
    enabled: isOnline, // Only enable the query when online (optional, RQ handles offline gracefully)
    retry: isOnline ? 3 : false, // Don't retry endlessly if offline
    refetchOnWindowFocus: isOnline, // Avoid refetching if offline
    // ... other react-query options ...
    initialData: options.initialData,
  });
  
  // Return query state along with online status
  return {
    ...query, // Includes data, error, isLoading, isError, isSuccess, refetch, etc.
    isOnline,
  };
}
```

## Storybook Stories

All Storybook stories **must** integrate with MSW handlers defined in `src/.storybook/mswHandlers/`. Use the following template structure:

```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { http, HttpResponse } from 'msw'; // Import MSW utilities
// Import the component
import { ComponentName } from '../ComponentName'; 
// Import corresponding MSW handlers
import { componentNameHandlers } from '../../../.storybook/mswHandlers/{componentName}.handlers'; 
// Import action handlers if the component triggers actions
import { componentNameActionHandlers } from '../../../.storybook/mswHandlers/{componentName}Actions.handlers'; 

const meta: Meta<typeof ComponentName> = {
  // Follow the format: 'Components/ComponentName/Type' or 'Features/FeatureName/Type'
  title: 'Components/ComponentName/Type', // Adjust category as needed
  component: Type,
  parameters: {
    // Pass ALL required MSW handlers for the component to function
    msw: {
      handlers: [
        ...componentNameHandlers, // Handlers for data fetching
        ...componentNameActionHandlers, // Handlers for button clicks, form submits etc.
        // Add any other handlers needed by sub-components
      ],
    },
    layout: 'padded', // Or 'fullscreen', 'centered' as appropriate
  },
  // Define argTypes for props controllable in Storybook UI
  argTypes: {
    variant: { 
      control: 'select', 
      options: ['default', 'compact'], 
    },
    // Define other relevant props
  },
  // Default args for all stories unless overridden
  args: {
    variant: 'default',
    // Set default values for other props
  },
  tags: ['autodocs'], // Enable automatic documentation generation
};

export default meta;
type Story = StoryObj<typeof ComponentName>;

// --- Story Variants ---

// Default state (usually success state with data)
export const Default: Story = {
  name: 'Default View', // Descriptive name
  args: {
    // Override default args if needed for this specific story
  },
};

// Loading State Story
export const Loading: Story = {
  name: 'State: Loading',
  parameters: {
    msw: {
      handlers: [
        // Override the specific GET handler to simulate loading
        http.get('/api/component-data-endpoint', async () => {
          await delay('infinite'); // MSW utility to simulate perpetual loading
          return HttpResponse.json({}); // Will never be reached
        }),
        // Include other necessary handlers (e.g., for actions)
        ...componentNameActionHandlers, 
      ],
    },
  },
  args: {
    // You might not need to pass loading prop if component derives it from query state
  },
};

// Error State Story
export const Error: Story = {
  name: 'State: Error',
  parameters: {
    msw: {
      handlers: [
        // Override GET handler to return an error response
        http.get('/api/component-data-endpoint', () => {
          return new HttpResponse(JSON.stringify({ message: 'Failed to load data from server.' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }),
        ...componentNameActionHandlers,
      ],
    },
  },
  args: {
     // You might not need to pass error prop if component derives it from query state
  },
};

// Empty State Story
export const Empty: Story = {
  name: 'State: Empty Data',
   parameters: {
    msw: {
      handlers: [
        // Override GET handler to return an empty array or specific empty response
        http.get('/api/component-data-endpoint', () => {
          return HttpResponse.json({ data: [] }); // Or appropriate empty structure
        }),
         ...componentNameActionHandlers,
      ],
    },
  },
  args: {
    // Pass any props needed to trigger empty state if not solely data-driven
  },
};

// Offline State Story (Simulate navigator.onLine = false)
// Note: Directly simulating offline is tricky with MSW alone. 
// Storybook addons or browser devtools might be needed for full simulation.
// This story often relies on passing an `isOnline={false}` prop if available,
// or mocking the useNetworkStatus hook.
export const Offline: Story = {
  name: 'State: Offline',
  args: {
    // If component accepts an isOnline prop (less ideal):
    // isOnline: false, 
  },
  // Add hook mocking if necessary, though less standard for Storybook
};

// Add more stories for different variants, edge cases, interactions, etc.

```

``` modify .storybook/mswHandlers
import { {ComponentName} } from './{ComponentName}.handlers';
import profileHandlers from './ProfileTableWithDetail.handlers'; // You import 'profileHandlers' here...

// Export all handlers
export const handlers = [
  ...TableMswHandlers, // ...but use 'TableMswHandlers' here.
  ...{ComponentName},   // And this is still a placeholder.


  // Add other handlers here
];

// Export individual handler groups for direct import in stories
export {
  TableMswHandlers, // Placeholder name used again.
  {ComponentName}    // Placeholder name used again.
};
```

## Responsive Design System

All components must be responsive across these breakpoints:

| Breakpoint | Size (pixels) | Target Devices            |
|------------|---------------|---------------------------|
| '3xs'      | 256px         | Tiny displays, watches    |
| '2xs'      | 288px         | Extra small displays      |
| 'xs'       | 320px         | Small phones              |
| 'sm'       | 384px         | Phones                    |
| 'md'       | 448px         | Large phones              |
| 'lg'       | 512px         | Small tablets             |
| 'xl'       | 576px         | Tablets                   |
| '2xl'      | 672px         | Large tablets             |
| '3xl'      | 768px         | Small laptops             |
| '4xl'      | 896px         | Laptops                   |
| '5xl'      | 1024px        | Desktops                  |
| '6xl'      | 1152px        | Large desktops            |
| '7xl'      | 1280px        | Extra large desktops      |

```typescript
// Responsive hooks
function useResponsive() {
  const [breakpoint, setBreakpoint] = useState<BreakpointKey>('md');
  
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width < 256) setBreakpoint('3xs');
      else if (width < 288) setBreakpoint('2xs');
      else if (width < 320) setBreakpoint('xs');
      else if (width < 384) setBreakpoint('sm');
      else if (width < 448) setBreakpoint('md');
      else if (width < 512) setBreakpoint('lg');
      else if (width < 576) setBreakpoint('xl');
      else if (width < 672) setBreakpoint('2xl');
      else if (width < 768) setBreakpoint('3xl');
      else if (width < 896) setBreakpoint('4xl');
      else if (width < 1024) setBreakpoint('5xl');
      else if (width < 1152) setBreakpoint('6xl');
      else setBreakpoint('7xl');
    };
    
    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);
  
  return breakpoint;
}

// Component with responsive props
interface ResponsiveComponentProps {
  responsiveVariants?: Partial<Record<BreakpointKey, {
    variant?: string;
    size?: string;
    layout?: 'stack' | 'grid' | 'row';
  }>>;
  hideOnBreakpoint?: BreakpointKey[];
}

function ResponsiveComponent({ 
  responsiveVariants,
  hideOnBreakpoint,
  ...defaultProps 
}: ResponsiveComponentProps) {
  const breakpoint = useResponsive();
  
  if (hideOnBreakpoint?.includes(breakpoint)) {
    return null;
  }
  
  const propsForBreakpoint = {
    ...defaultProps,
    ...(responsiveVariants?.[breakpoint] || {})
  };
  
  return (
    <div className={`responsive-component size-${propsForBreakpoint.size}`}>
      {/* Component implementation */}
    </div>
  );
}
```

## Reuse Strategy

Before implementing any component:

1. Check `@/components/ui/index.ts` first for existing components
2. Only create custom components when:
   - The component has unique business logic
   - It requires specialized behavior not in the UI library
   - It's specifically requested to be implemented from scratch
3. Tables are special - always use TanStack Table directly

## Core Principles Summary

1. **Production Parity**: Identical behavior via MSW.
2. **Full Optimization**: Performance-first (virtualization, memoization).
3. **Comprehensive Design**: Mobile-first, **WCAG 2.1 AAA**, global theming.
4. **Standardized Tooling**: Uses **Luxon** for dates (ISO 8601 UTC format `YYYY-MM-DDTHH:mm:ss.sssZ`), TanStack tools, shadcn/ui, Framer Motion.
5. **Developer Experience**: Consistent patterns, clear state management, comprehensive testing (Storybook/MSW).
6. **Robustness**: Explicit handling of all component states (loading, empty, error, network).

## Skeleton Implementation

All loading states **MUST** use the standard `Skeleton` component provided by `shadcn/ui` (typically imported from `@/components/ui/skeleton`). Do not use other skeleton libraries like `react-content-loader`.

```typescript
// Import the standard Skeleton component
import { Skeleton } from '@/components/ui/skeleton'; 
// Import motion components if needed for animation wrappers
import { motion } from 'framer-motion';
import { skeletonAnimationVariants } from './ComponentName-Animations'; // Assuming variants are defined

// Example Skeleton structure for a component item
export const ComponentSkeleton = ({
  count = 5,
  className = '',
  height = '20px',
  width = '100%',
  borderRadius = '0.25rem',
}: SkeletonProps) => {
  return (
    <div className={`flex items-center space-x-4 ${className}`}>
      <Skeleton className="h-10 w-10" />
      <div className="space-y-2">
        {Array.from({ length: count }, (_, index) => (
          <Skeleton key={index} className={`h-${height} w-${width} rounded-${borderRadius}`} />
        ))}
      </div>
    </div>
  );
};
```

## ESlintrc

```.eslintrc.json
{
  "extends": [
    "next/core-web-vitals",
    "plugin:@typescript-eslint/recommended",
    "plugin:storybook/recommended",
    "plugin:react-hooks/recommended",
    "prettier"
  ],
  "rules": {
    "@typescript-eslint/ban-ts-comment": ["error", { "ts-ignore": "allow-with-description" }],
    "@typescript-eslint/restrict-template-expressions": "off",
    "react/prop-types": "off",
    "comma-spacing": "error",
    "object-curly-spacing": ["error", "always"],
    "array-bracket-spacing": ["error", "never"],
    "max-len": [
      "warn",
      {
        "code": 500,
        "ignoreUrls": true,
        "ignoreTemplateLiterals": true,
        "ignoreStrings": true,
        "ignoreTrailingComments": true
      }
    ],
    "prettier/prettier": "error",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }],
    "no-duplicate-imports": "error"
  },
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint", "react-hooks", "prettier"],
  "settings": {
    "react": {
      "version": "detect"
    }
  }
}
```

## Reuse Strategy

Before implementing any component:

1. Check `@/components/ui/index.ts` first for existing components
2. Only create custom components when:
   - The component has unique business logic
   - It requires specialized behavior not in the UI library
   - It's specifically requested to be implemented from scratch
3. Tables are special - always use TanStack Table directly

## Core Principles Summary

1. **Production Parity**: Identical behavior via MSW.
2. **Full Optimization**: Performance-first (virtualization, memoization).
3. **Comprehensive Design**: Mobile-first, **WCAG 2.1 AAA**, global theming.
4. **Standardized Tooling**: Uses **Luxon** for dates (ISO 8601 UTC format `YYYY-MM-DDTHH:mm:ss.sssZ`), TanStack tools, shadcn/ui, Framer Motion.
5. **Developer Experience**: Consistent patterns, clear state management, comprehensive testing (Storybook/MSW).
6. **Robustness**: Explicit handling of all component states (loading, empty, error, network)