'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { 
  i18n, 
  type SupportedLanguage, 
  languageCodes,
  isRTL 
} from '@/lib';

/**
 * Helper type for the language hook return values
 */
export interface LanguageHookReturnType {
  // Translation function and i18n instance
  t: (key: string, options?: any) => string;
  i18n: any;
  
  // Language state
  currentLanguage: string;
  changeLanguage: (lang: string) => Promise<unknown>;
  isRTL: boolean;
  
  // Language utilities
  getSupportedLanguages: () => SupportedLanguage[];
  
  // Advanced capabilities can be added here
  formatDate?: (date: Date, options?: any) => string;
  formatNumber?: (num: number, options?: any) => string;
}

/**
 * Simple debounce hook to prevent excessive updates
 */
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      const valueString = typeof value === 'object' ? JSON.stringify(value) : String(value);
      const debouncedString = typeof debouncedValue === 'object' ? JSON.stringify(debouncedValue) : String(debouncedValue);
      
      if (valueString !== debouncedString) {
        setDebouncedValue(value);
      }
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]); // Removed debouncedValue from dependencies to prevent loops

  return debouncedValue;
}

/**
 * Helper function to validate language codes
 */
function isValidLanguage(lang: string): boolean {
  return languageCodes.includes(lang as SupportedLanguage);
}

// Internal helper to handle language updates
function useLanguageListener(i18nInstance: any, updateLanguage: (lng: string) => void) {
  useEffect(() => {
    const handleLanguageChanged = (lng: string) => updateLanguage(lng);

    if (i18nInstance?.on) {
      i18nInstance.on('languageChanged', handleLanguageChanged);
      return () => i18nInstance.off?.('languageChanged', handleLanguageChanged);
    }
  }, [i18nInstance, updateLanguage]);
}

/**
 * Simplified hook returning only current language
 * This avoids the "Objects are not valid as React child" error
 * when the hook's return value is used directly in JSX
 * 
 * @param namespace - Optional namespace for translations (defaults to common)
 * @returns The current language code as a string (e.g., 'en', 'fr')
 */
export function useLanguageChange(namespace?: string): string {
  const { i18n: i18nInstance } = useTranslation(namespace);
  const [currentLang, setCurrentLang] = useState<string>(
    i18nInstance.language || i18n.client.getLanguage() || 'en'
  );

  const updateLanguage = useCallback((lng: string) => setCurrentLang(lng), []);

  useLanguageListener(i18nInstance, updateLanguage);

  return currentLang;
}

/**
 * Comprehensive hook with full i18n functionality
 * 
 * Comprehensive i18n utility hook for internationalization functionality.
 * 
 * @param namespace - Optional namespace for translations (defaults to common)
 * @returns A comprehensive object with all i18n functionality
 */
export function useFullLanguageChange(namespace?: string): LanguageHookReturnType {
  const { t, i18n: i18nInstance } = useTranslation(namespace);
  const [currentLang, setCurrentLang] = useState<string>(
    i18nInstance.language || i18n.client.getLanguage() || 'en'
  );

  const updateLanguage = useCallback((lng: string) => setCurrentLang(lng), []);

  const debouncedLanguage = useDebounce(i18nInstance.language, 200);

  useEffect(() => {
    if (debouncedLanguage && debouncedLanguage !== currentLang) {
      updateLanguage(debouncedLanguage);
    }
  }, [debouncedLanguage, currentLang, updateLanguage]);

  useLanguageListener(i18nInstance, updateLanguage);

  const changeLanguage = useCallback((lang: string) => {
    return isValidLanguage(lang)
      ? i18n.client.changeLanguage(lang as SupportedLanguage)
      : Promise.resolve(lang);
  }, []);

  const getSupportedLanguages = useCallback(() => {
    return i18nInstance.languages?.filter(isValidLanguage) as SupportedLanguage[] || languageCodes;
  }, [i18nInstance]);

  const isRTLValue = isValidLanguage(currentLang)
    ? isRTL(currentLang as SupportedLanguage)
    : false;

  const formatDate = useCallback((date: Date, options?: Intl.DateTimeFormatOptions) => {
    return new Intl.DateTimeFormat(currentLang, options).format(date);
  }, [currentLang]);

  const formatNumber = useCallback((num: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(currentLang, options).format(num);
  }, [currentLang]);

  return {
    t,
    i18n: i18nInstance,
    currentLanguage: currentLang,
    changeLanguage,
    isRTL: isRTLValue,
    getSupportedLanguages,
    formatDate,
    formatNumber,
  };
}

/**
 * Specialized hook for Storybook
 * This is a simplified version that doesn't depend on useLanguageChange
 */
export function useStorybookLanguage(): string {
  const [language, setLanguage] = useState<string>('en');

  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        const params = new URLSearchParams(window.location.search);
        const localeParam = params.get('globals');

        const localeMatch = localeParam?.match(/locale:([a-z]{2})/);
        if (localeMatch?.[1]) {
          setLanguage(localeMatch[1]);
          return;
        }

        const storedLang = localStorage.getItem('i18nextLng');
        if (storedLang?.length === 2) {
          setLanguage(storedLang);
        }
      }
    } catch (e) {
      console.error('[useStorybookLanguage] Error detecting language:', e);
    }
  }, []);

  return language;
}

// Default export is the simplified string-only version for backward compatibility
export default useLanguageChange; 