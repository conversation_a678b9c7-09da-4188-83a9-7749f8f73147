import{b as o,u as c,dA as l,t as s}from"./index-Bwql5Dzz.js";import{u}from"./use-prompt-pbDx0Sfe.js";var g=({priceList:t})=>{const{t:e}=o(),a=u(),r=c(),{mutateAsync:i}=l(t.id);return async()=>{await a({title:e("general.areYouSure"),description:e("priceLists.delete.confirmation",{title:t.title}),confirmText:e("actions.delete"),cancelText:e("actions.cancel")})&&await i(void 0,{onSuccess:()=>{s.success(e("priceLists.delete.successToast",{title:t.title})),r("/price-lists")},onError:n=>{s.error(n.message)}})}};export{g as u};
