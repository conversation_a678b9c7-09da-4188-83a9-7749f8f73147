'use client';

import { ReactNode, useEffect } from 'react';
import { useTranslation as useReactI18nextTranslation, I18nextProvider } from 'react-i18next';
import { i18n, languageCodes, isRTL, type SupportedLanguage } from '@/lib';

// Client-side translation hook that can be used in client components
export const useTranslation = useReactI18nextTranslation;

// Client-side i18n provider component
interface I18nProviderProps {
  children: ReactNode;
}

export function I18nProvider({ children }: I18nProviderProps) {
  useEffect(() => {
    // Ensure i18n is initialized and fully loaded
    const handleInitialized = () => {
      // Apply RTL direction if needed
      const currentLang = i18n.client.i18n.language;
      updateDocumentDirection(currentLang);
    };

    // Function to update document direction based on language
    const updateDocumentDirection = (lang: string) => {
      const isRTLLang = isRTL(lang as SupportedLanguage);
      document.documentElement.dir = isRTLLang ? 'rtl' : 'ltr';
      document.documentElement.lang = lang;
    };

    const clientI18n = i18n.client.i18n;

    if (clientI18n.isInitialized) {
      handleInitialized();
    } else {
      clientI18n.on('initialized', handleInitialized);
    }

    // Listen for language changes to update direction
    const handleLanguageChanged = (lang: string) => {
      updateDocumentDirection(lang);
    };

    clientI18n.on('languageChanged', handleLanguageChanged);

    // Use language from localStorage if available, or browser preference
    clientI18n.on('initialized', () => {
      const storedLang = localStorage.getItem('i18nextLng');
      const supportedLanguages = clientI18n.options.supportedLngs;

      if (storedLang && Array.isArray(supportedLanguages) && languageCodes.includes(storedLang as SupportedLanguage)) {
        clientI18n.changeLanguage(storedLang);
      }
    });

    return () => {
      clientI18n.off('initialized', handleInitialized);
      clientI18n.off('languageChanged', handleLanguageChanged);
    };
  }, []);

  return <I18nextProvider i18n={i18n.client.i18n}>{children}</I18nextProvider>;
}
