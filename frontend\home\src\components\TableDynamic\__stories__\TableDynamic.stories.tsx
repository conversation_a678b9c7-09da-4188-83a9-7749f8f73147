'use client';

import * as React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, fn, userEvent, within } from '@storybook/test';
import { Copy, Trash, Eye, Edit, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import TableDynamic from '../table-dynamic';
import { simpleData, simpleColumns } from '../__fixtures__/TableDynamic.fixtures';
import type { Row } from '@tanstack/react-table';
import type { ColumnConfig } from '../types';
import { TooltipProvider } from '@/components/ui/tooltip';

// Define ActionCell component to be used in the stories
export const ActionCell = ({ row }: { row: Row<Record<string, unknown>> }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => navigator.clipboard.writeText(row.getValue('id'))}>
          <Copy className="mr-2 h-4 w-4" />
          Copy ID
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Eye className="mr-2 h-4 w-4" />
          View
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem>
          <Trash className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

// Enhanced simple columns with renderers for stories
const getEnhancedSimpleColumns = (): ColumnConfig[] => {
  return simpleColumns.map((col) => {
    if (col.id === 'price') {
      return {
        ...col,
        cell: ({ row }) => <div className="text-right">${row.getValue('price')}</div>,
      } as ColumnConfig;
    }
    if (col.id === 'name') {
      return {
        ...col,
        cell: ({ row }) => <div className="font-medium">{row.getValue('name')}</div>,
      } as ColumnConfig;
    }
    return col as ColumnConfig;
  });
};

const meta = {
  title: 'Components/TableDynamic',
  component: TableDynamic,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A dynamic table component with virtualization, filtering, sorting, and more features.',
      },
    },
    controls: { sort: 'requiredFirst' },
    badges: ['stable', 'tested', 'accessible'],
    a11y: { disable: false },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#FFFFFF' },
        { name: 'dark', value: '#1F2937' },
      ],
    },
    viewport: {
      viewports: {
        mobile: { name: 'Mobile', styles: { width: '375px', height: '667px' } },
        tablet: { name: 'Tablet', styles: { width: '768px', height: '1024px' } },
        desktop: { name: 'Desktop', styles: { width: '1440px', height: '900px' } },
      },
      defaultViewport: 'desktop',
    },
    chromatic: { viewports: [375, 768, 1440] },
  },
  argTypes: {
    // Core data props
    data: {
      control: 'object',
      description: 'Table data array',
      table: {
        type: { summary: 'any[]' },
        category: 'Data',
      },
    },
    columns: {
      control: 'object',
      description: 'Table column configurations',
      table: {
        type: { summary: 'ColumnConfig[]' },
        category: 'Data',
      },
    },

    // Variant props
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline'],
      description: 'Table style variant',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'primary' },
        category: 'Appearance',
      },
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Table size variant',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'md' },
        category: 'Appearance',
      },
    },

    // State props
    loading: {
      control: 'boolean',
      description: 'Whether the table is in loading state',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
        category: 'State',
      },
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the table is disabled',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
        category: 'State',
      },
    },
    error: {
      control: 'text',
      description: 'Error message or component',
      table: {
        type: { summary: 'string | ReactNode' },
        category: 'State',
      },
    },

    // Feature configs
    pagination: {
      control: 'object',
      description: 'Pagination configuration',
      table: {
        type: { summary: 'PaginationConfig' },
        category: 'Features',
      },
    },
    sorting: {
      control: 'object',
      description: 'Sorting configuration',
      table: {
        type: { summary: 'SortingConfig' },
        category: 'Features',
      },
    },
    filtering: {
      control: 'object',
      description: 'Filtering configuration',
      table: {
        type: { summary: 'FilteringConfig' },
        category: 'Features',
      },
    },
    columnVisibility: {
      control: 'object',
      description: 'Column visibility configuration',
      table: {
        type: { summary: 'ColumnVisibilityConfig' },
        category: 'Features',
      },
    },

    // Appearance props
    striped: {
      control: 'boolean',
      description: 'Whether to show striped rows',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
        category: 'Appearance',
      },
    },
    showBorder: {
      control: 'boolean',
      description: 'Whether to show table border',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' },
        category: 'Appearance',
      },
    },
    showHeader: {
      control: 'boolean',
      description: 'Whether to show table header',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' },
        category: 'Appearance',
      },
    },
    showFooter: {
      control: 'boolean',
      description: 'Whether to show table footer',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' },
        category: 'Appearance',
      },
    },

    // Animation props
    animate: {
      control: 'boolean',
      description: 'Whether to animate table rows',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' },
        category: 'Animation',
      },
    },
    animationDelay: {
      control: 'number',
      description: 'Animation delay between rows in seconds',
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: '0.05' },
        category: 'Animation',
      },
    },

    // Virtualization props
    virtualized: {
      control: 'boolean',
      description: 'Whether to virtualize the table for large datasets',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
        category: 'Performance',
      },
    },
    height: {
      control: 'number',
      description: 'Height for virtualized table container',
      table: {
        type: { summary: 'number' },
        category: 'Performance',
      },
    },

    // Event handlers
    onRowClick: {
      action: 'rowClicked',
      description: 'Event handler for row click',
      table: {
        type: { summary: 'function' },
        category: 'Events',
      },
    },
    onCellClick: {
      action: 'cellClicked',
      description: 'Event handler for cell click',
      table: {
        type: { summary: 'function' },
        category: 'Events',
      },
    },
  },
  decorators: [
    (Story) => (
      <TooltipProvider>
        <div className="max-w-full p-4">
          <Story />
        </div>
      </TooltipProvider>
    ),
  ],
  tags: ['autodocs'],
} satisfies Meta<typeof TableDynamic>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default story showing a simple table with basic configuration.
 */
export const Default: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    // Verify that the table renders
    const table = canvas.getByRole('table');
    await expect(table).toBeInTheDocument();

    // Verify that it has the correct number of rows (data length + header)
    const rows = canvas.getAllByRole('row');
    await expect(rows.length).toBeGreaterThan(1);
  },
};

/**
 * Demonstrates the table in loading state, displaying a skeleton loader.
 */
export const Loading: Story = {
  args: {
    ...Default.args,
    loading: true,
  },
};

/**
 * Demonstrates the table with an error message.
 */
export const WithError: Story = {
  args: {
    ...Default.args,
    error: 'Failed to load table data. Please try again later.',
  },
};

/**
 * Shows an empty state when there is no data to display.
 */
export const EmptyState: Story = {
  args: {
    ...Default.args,
    data: [],
  },
};

/**
 * Demonstrates basic row interaction with click handlers.
 */
export const Interaction: Story = {
  args: {
    ...Default.args,
    onRowClick: fn(),
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const rows = canvas.getAllByRole('row');

    // Skip header row (0) and click on first data row (1)
    await userEvent.click(rows[1]);
  },
};

/**
 * Shows customized appearance with size and variant options.
 */
export const CustomAppearance: Story = {
  args: {
    ...Default.args,
    variant: 'outline',
    size: 'sm',
    striped: true,
  },
};

// ActionCell story
export const ActionCellStory: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    compact: false,
    // Add all required props to prevent undefined errors
    rowActions: {
      enabled: true,
      renderRowActions: () => (
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            Edit
          </Button>
          <Button size="sm" variant="outline">
            Delete
          </Button>
        </div>
      ),
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const table = canvas.getByRole('table');
    await expect(table).toBeInTheDocument();

    // Find action buttons
    const actionButtons = canvas.getAllByRole('button');
    await expect(actionButtons.length).toBeGreaterThan(0);
  },
};

export const ColumnWidth: Story = {
  name: 'Custom Column Width',
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    size: 'md',
  },
};
