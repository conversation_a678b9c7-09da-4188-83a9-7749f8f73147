'use client';

import * as React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import TableDynamic from '../table-dynamic';
import { simpleData, simpleColumns } from '../__fixtures__/TableDynamic.fixtures';
import type { ColumnConfig } from '../types';

// Enhanced simple columns for stories
const getEnhancedSimpleColumns = (): ColumnConfig[] => {
  return simpleColumns.map((col) => {
    if (col.id === 'price') {
      return {
        ...col,
        cell: ({ row }) => <div className="text-right">${row.getValue('price')}</div>,
      } as ColumnConfig;
    }
    if (col.id === 'name') {
      return {
        ...col,
        cell: ({ row }) => <div className="font-medium">{row.getValue('name')}</div>,
      } as ColumnConfig;
    }
    return col as ColumnConfig;
  });
};

// Mobile-optimized columns (fewer columns for smaller screens)
const getMobileColumns = (): ColumnConfig[] => {
  return simpleColumns
    .filter((col) => ['name', 'price'].includes(col.id as string))
    .map((col) => {
      if (col.id === 'price') {
        return {
          ...col,
          cell: ({ row }) => <div className="text-right">${row.getValue('price')}</div>,
        } as ColumnConfig;
      }
      if (col.id === 'name') {
        return {
          ...col,
          cell: ({ row }) => <div className="font-medium">{row.getValue('name')}</div>,
        } as ColumnConfig;
      }
      return col as ColumnConfig;
    });
};

// Tablet-optimized columns (medium number of columns)
const getTabletColumns = (): ColumnConfig[] => {
  return simpleColumns
    .filter((col) => ['name', 'category', 'price'].includes(col.id as string))
    .map((col) => {
      if (col.id === 'price') {
        return {
          ...col,
          cell: ({ row }) => <div className="text-right">${row.getValue('price')}</div>,
        } as ColumnConfig;
      }
      if (col.id === 'name') {
        return {
          ...col,
          cell: ({ row }) => <div className="font-medium">{row.getValue('name')}</div>,
        } as ColumnConfig;
      }
      return col as ColumnConfig;
    });
};

const meta = {
  title: 'Components/TableDynamic/Viewport',
  component: TableDynamic,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Responsive behavior of the TableDynamic component across different viewport sizes.',
      },
    },
    chromatic: {
      viewports: [375, 768, 1024, 1440],
      delay: 300,
    },
  },
} satisfies Meta<typeof TableDynamic>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shows the table rendered on a mobile viewport.
 */
export const Mobile: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
  args: {
    data: simpleData,
    columns: getMobileColumns(),
    variant: 'primary',
    size: 'sm', // Smaller size for mobile
    pagination: {
      enabled: true,
      pageSizeOptions: [5, 10],
      initialPageSize: 5,
      showPageSizeSelector: false, // Simplified UI for mobile
    },
  },
};

/**
 * Shows the table rendered on a tablet viewport.
 */
export const Tablet: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
  args: {
    data: simpleData,
    columns: getTabletColumns(),
    variant: 'primary',
    size: 'md',
    pagination: {
      enabled: true,
      pageSizeOptions: [5, 10, 20],
      initialPageSize: 10,
    },
  },
};

/**
 * Shows the table rendered on a desktop viewport.
 */
export const Desktop: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'desktop',
    },
  },
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    pagination: {
      enabled: true,
      pageSizeOptions: [10, 20, 50],
      initialPageSize: 10,
    },
  },
};

/**
 * A demo showing how the component behaves responsively with a
 * single implementation by adapting columns dynamically
 */
export const ResponsiveTable: Story = {
  args: {
    data: simpleData,
    // In a real app, you would use media queries or a useMediaQuery hook
    // to adapt columns based on screen size
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
  },
};
