/** @type {import('next').NextConfig} */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});
import os from 'os';

const nextConfig = {
  images: {
      remotePatterns: [
          {
            protocol: 'https',
            hostname: 'cdn.buymeacoffee.com',
            port: '',
            pathname: '/buttons/v2/**',
          }
      ],
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Support for all rendering methods
  reactStrictMode: true,
  // Support for Incremental Static Regeneration (ISR)
  staticPageGenerationTimeout: 120, // 2 minutes timeout for static generation
  // Improve build performance
  experimental: {
    // Increase build concurrency
    cpus: Math.max(1, Math.min(8, os.cpus().length - 1)),
    // CSS optimization with critters
    optimizeCss: true,
    // Optimize package imports
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
    // Serverless optimization
    serverMinification: true,
    // Configure Turbopack (used in dev with --turbo flag)
    turbo: {
      // Resolve aliases if needed
      resolveAlias: {
        // Add any custom aliases if needed
      }
    }
  },
  // Optimize output mode for different deployment environments
  output: process.env.NEXT_OUTPUT_MODE || undefined,
};

export default withBundleAnalyzer(nextConfig); 