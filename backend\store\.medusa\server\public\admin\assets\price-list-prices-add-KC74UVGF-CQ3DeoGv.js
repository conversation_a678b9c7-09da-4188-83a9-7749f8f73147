import{P as W,u as X,b as Z}from"./chunk-JXBMQ4SZ-DXsPGKz5.js";import{e as J,i as U}from"./chunk-G2J2T2QU-DYdW7b3B.js";import{u as Y}from"./chunk-G3QXMPRB-BXa5-FQN.js";import{a6 as E,R as ee,dD as te,j as e,r as S,b as z,a8 as re,a9 as se,dH as ie,t as O,B as I,v as F,e as K,k as oe,V}from"./index-Bwql5Dzz.js";import{u as ae,_ as ce}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{D as ne}from"./chunk-GE4APTT2-vaCdy0-f.js";import{u as de,a as le}from"./chunk-U6CSGYH6-BpcURsBT.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{K as ue}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as y,u as G}from"./chunk-JGQGO74V-DtHO1ucg.js";import{P as j}from"./progress-tabs-CKpxSjXZ.js";import{C as B}from"./checkbox-B4pL6X49.js";import{c as pe}from"./index-BxZ1678G.js";import"./chunk-6GU6IDUA-CDc7wW5L.js";import"./chunk-IQBAUTU5-D_4dFOf0.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-MWVM4TYO-bKUcYSnf.js";import"./_isIndex-bB1kTSVv.js";import"./index.esm-3G2Z4eQ8.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./prompt-BsR9zKsn.js";var me=({form:i,currencies:t,regions:a,pricePreferences:m})=>{const o=F({control:i.control,name:"product_ids"}),l=F({control:i.control,name:"products"}),{products:f,isLoading:n,isError:u,error:h}=K({id:o.map(d=>d.id),limit:o.length,fields:"title,thumbnail,*variants"}),{setValue:p}=i,{setCloseOnEscape:C}=G();S.useEffect(()=>{!n&&f&&f.forEach(d=>{l[d.id]||!d.variants||p(`products.${d.id}.variants`,{...d.variants.reduce((b,w)=>(b[w.id]={currency_prices:{},region_prices:{}},b),{})})})},[f,l,n,p]);const T=Z({currencies:t,regions:a,pricePreferences:m});if(u)throw h;return e.jsx("div",{className:"flex size-full flex-col divide-y overflow-hidden",children:e.jsx(ne,{isLoading:n,columns:T,data:f,getSubRows:d=>{if(U(d)&&d.variants)return d.variants},state:i,onEditingChange:d=>C(!d)})})},_=50,M="p";function fe(i){return i.reduce((t,a)=>(t[a.id]=!0,t),{})}var he=({priceList:i,form:t})=>{const{t:a}=z(),{control:m,setValue:o}=t,l=S.useMemo(()=>i.prices.reduce((r,x)=>(r[x.variant_id]=!0,r),{}),[i.prices]),f=F({control:m,name:"product_ids"}),n=F({control:m,name:"products"}),[u,h]=S.useState(fe(f)),{searchParams:p,raw:C}=de({pageSize:_,prefix:M}),{products:T,count:d,isLoading:b,isError:w,error:L}=K(p,{placeholderData:oe}),A=r=>{const x=typeof r=="function"?r(u):r,g=Object.keys(x),q=Object.keys(n).reduce((k,D)=>(g.includes(D)&&(k[D]=n[D]),k),{}),Q=g.map(k=>({id:k}));o("product_ids",Q,{shouldDirty:!0,shouldTouch:!0}),o("products",q,{shouldDirty:!0,shouldTouch:!0}),h(x)},s=Pe(),P=le(),{table:c}=ae({data:T||[],columns:s,count:d,enablePagination:!0,enableRowSelection:r=>{var x,g;return!!((x=r.original.variants)!=null&&x.length)&&!((g=r.original.variants)!=null&&g.some(R=>l[R.id]))},getRowId:r=>r.id,rowSelection:{state:u,updater:A},pageSize:_,meta:{variantIdMap:l}});if(w)throw L;return e.jsx("div",{className:"flex size-full flex-col",children:e.jsx(ce,{table:c,columns:s,filters:P,pageSize:_,prefix:M,count:d,isLoading:b,layout:"fill",orderBy:[{key:"title",label:a("fields.title")},{key:"status",label:a("fields.status")},{key:"created_at",label:a("fields.createdAt")},{key:"updated_at",label:a("fields.updatedAt")}],pagination:!0,search:!0,queryObject:C})})},xe=pe(),Pe=()=>{const i=Y();return S.useMemo(()=>[xe.display({id:"select",header:({table:t})=>e.jsx(B,{checked:t.getIsSomePageRowsSelected()?"indeterminate":t.getIsAllPageRowsSelected(),onCheckedChange:a=>t.toggleAllPageRowsSelected(!!a)}),cell:({row:t,table:a})=>{var u;const{variantIdMap:m}=a.options.meta,o=(u=t.original.variants)==null?void 0:u.some(h=>m[h.id]),l=!t.getCanSelect()||o,f=t.getIsSelected()||o,n=e.jsx(B,{checked:f,disabled:l,onCheckedChange:h=>t.toggleSelected(!!h),onClick:h=>{h.stopPropagation()}});return o?e.jsx(V,{content:"This product is already in the price list",children:n}):l?e.jsx(V,{content:"This product has no variants",children:n}):n}}),...i],[i])},N=E.object({product_ids:E.array(E.object({id:E.string()})).min(1),products:W}),$=N.pick({product_ids:!0}),H=Object.keys($.shape),ge=N.pick({products:!0}),ve=Object.keys(ge.shape),v=["product","price"],be={product:"in-progress",price:"not-started"},je=({priceList:i,regions:t,currencies:a,pricePreferences:m})=>{const[o,l]=S.useState("product"),[f,n]=S.useState(be),{t:u}=z(),{handleSuccess:h}=G(),p=re({defaultValues:{products:{},product_ids:[]},resolver:se(N)}),{mutateAsync:C,isPending:T}=ie(i.id),d=p.handleSubmit(async s=>{const{products:P}=s,c=J(P,t);await C({create:c},{onSuccess:()=>{O.success(u("priceLists.products.add.successToast")),h()},onError:r=>O.error(r.message)})}),b=(s,P)=>{p.clearErrors(s);const c=s.reduce((x,g)=>(x[g]=p.getValues(g),x),{}),r=P.safeParse(c);return r.success?!0:(r.error.errors.forEach(({path:x,message:g,code:R})=>{p.setError(x.join("."),{type:R,message:g})}),!1)},w=s=>{switch(s){case"product":return H.some(c=>p.getFieldState(c).isDirty);case"price":return ve.some(c=>p.getFieldState(c).isDirty)}},L=s=>{if(o===s)return;if(v.indexOf(s)<v.indexOf(o)){const c=w(o);n(r=>({...r,[o]:c?r[o]:"not-started",[s]:"in-progress"})),l(s);return}const P=v.slice(0,v.indexOf(s));for(const c of P)if(c==="product"){if(!b(H,$)){n(r=>({...r,[c]:"in-progress"})),l(c);return}n(r=>({...r,[c]:"completed"}))}n(c=>({...c,[o]:"completed",[s]:"in-progress"})),l(s)},A=s=>{if(v.indexOf(s)+1>=v.length)return;const P=v[v.indexOf(s)+1];L(P)};return e.jsx(y.Form,{form:p,children:e.jsx(j,{value:o,onValueChange:s=>L(s),className:"flex h-full flex-col overflow-hidden",children:e.jsxs(ue,{onSubmit:d,className:"flex h-full flex-col",children:[e.jsx(y.Header,{children:e.jsx("div",{className:"flex w-full items-center justify-between gap-x-4",children:e.jsx("div",{className:"-my-2 w-full max-w-[600px] border-l",children:e.jsxs(j.List,{className:"grid w-full grid-cols-3",children:[e.jsx(j.Trigger,{status:f.product,value:"product",children:u("priceLists.create.tabs.products")}),e.jsx(j.Trigger,{status:f.price,value:"price",children:u("priceLists.create.tabs.prices")})]})})})}),e.jsxs(y.Body,{className:"size-full overflow-hidden",children:[e.jsx(j.Content,{className:"size-full overflow-y-auto",value:"product",children:e.jsx(he,{form:p,priceList:i})}),e.jsx(j.Content,{className:"size-full overflow-hidden",value:"price",children:e.jsx(me,{form:p,regions:t,currencies:a,pricePreferences:m})})]}),e.jsx(y.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(y.Close,{asChild:!0,children:e.jsx(I,{variant:"secondary",size:"small",children:u("actions.cancel")})}),e.jsx(ye,{tab:o,next:A,isLoading:T})]})})]})})})},ye=({tab:i,next:t,isLoading:a})=>{const{t:m}=z();return i==="price"?e.jsx(I,{type:"submit",variant:"primary",size:"small",isLoading:a,children:m("actions.save")},"submit-button"):e.jsx(I,{type:"button",variant:"primary",size:"small",onClick:()=>t(i),children:m("actions.continue")},"next-button")},ct=()=>{const{id:i}=ee(),{price_list:t,isPending:a,isError:m,error:o}=te(i),{currencies:l,regions:f,pricePreferences:n,isReady:u}=X(),h=u&&!a&&!!t;if(m)throw o;return e.jsx(y,{children:h&&e.jsx(je,{priceList:t,currencies:l,regions:f,pricePreferences:n})})};export{ct as Component};
