{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:storybook/recommended", "plugin:react-hooks/recommended", "prettier"], "rules": {"@typescript-eslint/ban-ts-comment": ["error", {"ts-ignore": "allow-with-description"}], "@typescript-eslint/restrict-template-expressions": "off", "react/prop-types": "off", "comma-spacing": "error", "object-curly-spacing": ["error", "always"], "array-bracket-spacing": ["error", "never"], "max-len": ["warn", {"code": 500, "ignoreUrls": true, "ignoreTemplateLiterals": true, "ignoreStrings": true, "ignoreTrailingComments": true}], "prettier/prettier": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-duplicate-imports": "error"}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react-hooks", "prettier"], "settings": {"react": {"version": "detect"}}}