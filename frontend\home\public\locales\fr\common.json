{"app": {"title": "Chronologie", "description": "Un composant de chronologie personnalisable pour afficher des événements chronologiques."}, "nav": {"home": "Accueil", "about": "À propos", "settings": "Paramètres", "installation": "Installation", "usage": "Utilisation", "examples": "Exemples", "rendering_examples": "<PERSON><PERSON><PERSON> de Rendu"}, "buttons": {"submit": "So<PERSON><PERSON><PERSON>", "cancel": "Annuler", "save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "clickMe": "Cliquez-moi"}, "language": {"en": "<PERSON><PERSON><PERSON>", "fr": "Français", "ja": "Japonais", "ar": "<PERSON><PERSON>", "switchLanguage": "Changer de langue", "current": "Langue actuelle"}, "theme": {"current": "Thème actuel"}, "rendering": {"backToExamples": "Retour aux exemples", "backToCSR": "Retour à l'exemple CSR", "backToSPA": "Retour à l'exemple SPA", "backToSSR": "Retour à l'exemple SSR", "viewRouterExample": "Voir l'exemple de routeur avec prise en charge des thèmes et des langues", "viewFullSPAExample": "Voir l'exemple complet de SPA avec prise en charge des thèmes et des langues", "basicExample": "Exemple de base", "withDataExample": "Exemple avec données", "advancedExample": "Exemple avan<PERSON>", "ssr": "SSR", "ssg": "SSG", "csr": "CSR", "isr": "ISR", "spa": "SPA", "routes": {"basic": "Base", "withData": "Avec don<PERSON>", "advanced": "<PERSON><PERSON><PERSON>", "home": "Accueil", "about": "À propos", "products": "Produits", "contact": "Contact"}, "ssrTitle": "<PERSON><PERSON> (SSR)", "ssrHowWorks": "Comment fonctionne le SSR", "ssrDescription": "Avec le rendu côté serveur, le HTML de cette page est généré sur le serveur pour chaque requête. Cela signifie que le contenu est toujours à jour, car il est généré lorsque l'utilisateur demande la page.", "ssrNote": "Ceci est utile pour les pages où le contenu change fréquemment ou doit être personnalisé pour chaque utilisateur. Le SSR est également excellent pour le référencement, car les moteurs de recherche peuvent voir le contenu entièrement rendu.", "ssrServerData": "Données générées par le serveur", "ssrServerDataDescription": "Ces données ont été générées sur le serveur au moment de la requête :", "ssrRefreshNote": "Rafraîchissez la page pour voir ces valeurs changer, confirmant que la page est rendue à chaque requête.", "ssrImplementation": "Implémentation", "ssrImplementationDescription": "Dans Next.js 15, le SSR est le comportement par défaut pour les pages du Router d'application. Il suffit d'exporter un composant serveur qui récupère des données.", "ssrWithData": "SSR avec données détaillées", "ssrDataFetch": "Données utilisateur dynamiques", "ssrDataFetchDescription": "Cet exemple démontre la récupération de données plus complexes sur le serveur pour chaque requête. Vous pouvez voir ci-dessous les données utilisateur qui sont nouvellement générées à chaque chargement de page.", "userData": "<PERSON><PERSON><PERSON> utilisateur", "userId": "ID", "userName": "Nom", "userRole": "R<PERSON><PERSON>", "userLastActive": "Dernière activité", "systemStatus": "État du système", "cpuUsage": "Utilisation CPU", "memoryUsage": "Utilisation mémoire", "uptime": "Temps de fonctionnement", "generatedAt": "<PERSON><PERSON><PERSON><PERSON>", "ssrAdvanced": "Analyses SSR avancées", "realTimeAnalytics": "Tableau de bord d'analyses en temps réel", "ssrAdvancedDescription": "Cet exemple avancé démontre comment le SSR peut être utilisé pour générer des tableaux de bord complexes riches en données avec les dernières informations à chaque requête.", "pageViews": "Vues de page", "today": "<PERSON><PERSON><PERSON>'hui", "weekly": "Hebdomadaire", "monthly": "<PERSON><PERSON><PERSON>", "userSessions": "Sessions utilisateur", "avgDuration": "<PERSON><PERSON><PERSON> moyenne", "bounceRate": "<PERSON><PERSON>", "newUsers": "Nouveaux utilisateurs", "serverLoad": "Charge du serveur", "current": "Actuelle", "average": "<PERSON><PERSON><PERSON>", "peak": "Pic", "ssrAdvantages": "Avantages du SSR", "ssrAdvantage1": "Données toujours fraîches à chaque requête", "ssrAdvantage2": "Excellent référencement car les moteurs de recherche voient le HTML complet", "ssrAdvantage3": "Temps rapide d'affichage du contenu pour les utilisateurs", "ssrAdvantage4": "Fonctionne bien même avec JavaScript désactivé", "ssrRouterTitle": "Exemple de routeur avec rendu cô<PERSON> serveur (SSR)", "ssrRouterHowWorks": "Comment fonctionne le routage SSR", "ssrRouterDescription": "Avec le rendu côté serveur, chaque changement de route déclenche une nouvelle requête au serveur, qui génère le HTML pour cette route. Cela signifie que chaque page est nouvellement rendue sur le serveur avec les dernières données.", "ssrRouterNote": "Bien que cela vous donne toujours un contenu à jour, vous remarquerez des rechargements complets de page pendant la navigation. Les paramètres de thème et de langue sont conservés via des cookies ou le stockage local.", "csrTitle": "Rendu côté client (CSR)", "csrHowWorks": "Comment fonctionne le CSR", "csrDescription": "Avec le rendu côté client, le serveur envoie une page HTML minimale, et JavaScript construit la page dans le navigateur. Cela est visible dans la façon dont cette page commence avec un contenu minimal puis s'enrichit avec l'interface utilisateur complète une fois que JavaScript est chargé.", "csrNote": "Le CSR est bon pour les pages hautement interactives où la plupart du contenu est spécifique à l'utilisateur et où le référencement est moins important.", "clientState": "État côté client", "clientStateDescription": "Ces valeurs ne sont disponibles qu'après l'exécution de JavaScript dans le navigateur :", "clientStateLoading": "Chargement des données côté client...", "clientTime": "<PERSON><PERSON> du navigateur", "windowSize": "<PERSON><PERSON>enê<PERSON>", "refreshNote": "Rafraîchissez la page pour voir comment le CSR cause un flash de contenu non stylisé/minimal.", "csrWithData": "CSR avec récupération de données", "csrDataFetch": "Récupération de données côté client", "csrDataFetchDescription": "En CSR, les données sont récupérées par le navigateur après le chargement de JavaScript. Cela signifie qu'il y a toujours un délai avant l'apparition des données.", "fetchedData": "<PERSON><PERSON><PERSON> r<PERSON>", "loadingData": "Chargement des données...", "dataMessage": "Message", "dataTimestamp": "Horodatage", "csrAdvanced": "Techniques CSR avancées", "csrAdvancedTips": "Optimisation des applications CSR", "csrAdvancedDescription": "Les applications CSR avancées utilisent des techniques comme le fractionnement de code, le chargement paresseux et la mise en cache côté client pour améliorer les performances.", "csrBestPractices": "Meilleures pratiques", "csrBestPractice1": "Utiliser des états de chargement pour indiquer que le contenu est en cours de récupération", "csrBestPractice2": "Implémenter des limites d'erreur pour gérer les échecs avec élégance", "csrBestPractice3": "Envisager d'utiliser un service worker pour les fonctionnalités hors ligne", "csrBestPractice4": "Précharger les données critiques si possible", "csrRouterTitle": "Comment fonctionne le routage CSR", "csrRouterDescription": "Avec le rendu côté client, le HTML initial est minimal, et JavaScript construit la page dans le navigateur. La navigation entre les routes se fait entièrement dans le navigateur sans rechargements complets de page.", "csrRouterNote": "Cette approche offre une expérience fluide de type application avec un état préservé entre les changements de route. Les préférences de thème et de langue persistent naturellement pendant la navigation puisque la page ne se recharge jamais complètement.", "spaTitle": "Application à page unique (SPA)", "spaHowWorks": "Comment fonctionnent les SPA", "spaDescription": "Les applications à page unique chargent une seule page HTML puis mettent à jour dynamiquement le contenu lorsque l'utilisateur interagit avec l'application. La navigation entre les \"pages\" se fait entièrement côté client sans rafraîchir le navigateur.", "spaNote": "Les SPA offrent une expérience utilisateur plus fluide car elles ne nécessitent pas de rechargements de page, maintenant l'état de l'application et permettant des interactions plus proches d'une application native. L'URL actuelle est gérée à l'aide de l'API History du navigateur.", "currentUrl": "URL actuelle", "loadedAt": "<PERSON><PERSON><PERSON>", "spaFeatures": "Fonctionnalités clés des SPA", "spaFeature1": "Routage côté client sans rechargements de page", "spaFeature2": "État persistant de l'application entre les transitions de \"page\"", "spaFeature3": "Capacité à fonctionner hors ligne avec les service workers", "spaFeature4": "Transitions fluides et animations entre les vues", "spaRouterTitle": "Comment fonctionne le routage SPA", "spaRouterDescription": "Dans une application à page unique, tout le routage se fait côté client sans rafraîchir la page. L'application intercepte les changements d'URL et rend le contenu approprié sans demander de nouveau HTML au serveur.", "spaRouterNote": "Cela offre l'expérience utilisateur la plus fluide avec un état persistant et sans flashs de page. URL actuelle :"}, "timeConversion": {"title": "Conversion de l'heure", "utc": "UTC", "local": "Local", "timezone": "<PERSON><PERSON> ho<PERSON>", "date": "Date", "time": "<PERSON><PERSON>"}, "tableDynamic": {"empty": {"title": "<PERSON><PERSON><PERSON> donnée disponible", "description": "Il n'y a aucun élément à afficher."}, "error": {"generic": "Une erreur s'est produite lors du chargement des données."}, "filter": {"searchPlaceholder": "Rechercher...", "clearSearch": "Effacer la recherche", "filters": "Filtres", "filterColumns": "Filtrer les colonnes", "clearAllFilters": "Effacer tous les filtres"}, "footer": {"rowsPerPage": "<PERSON><PERSON><PERSON> par page :", "pageInfo": "Page {{current}} sur {{total}} ({{from}}-{{to}} sur {{totalRows}} éléments)", "firstPage": "Première page", "previousPage": "<PERSON> p<PERSON>", "nextPage": "<PERSON> suivante", "lastPage": "Dernière page"}, "skeleton": {"ariaLabel": "Chargement du contenu du tableau"}}, "userTable": {"headers": {"name": "Nom", "email": "E-mail", "role": "R<PERSON><PERSON>", "status": "Statut", "lastLogin": "Dernière Connexion"}, "status": {"active": "Actif", "inactive": "Inactif", "pending": "En attente"}, "empty": {"title": "Aucun Utilisa<PERSON>ur Trouv<PERSON>", "description": "Il n'y a aucun utilisateur à afficher."}, "error": {"title": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON><PERSON>"}, "footer": {"total": "Total: {{count}} utilisateurs"}}, "apiKey": {"name": {"label": "Nom", "placeholder": "Optionnel", "serviceKeyLabel": "Nom de la clé de service", "serviceKeyPlaceholder": "Ma clé de compte de service", "userKeyPlaceholder": "Ma clé de test"}, "ownerType": {"label": "Appartient à", "you": "Vous", "serviceAccount": "Compte de service"}, "permissionType": {"label": "Permissions", "all": "Toutes", "restricted": "Restreintes", "readOnly": "Lecture seule"}, "project": {"label": "Projet", "placeholder": "Sélectionner un projet"}, "resources": {"title": "Ressources", "permissions": "Permissions", "models": "<PERSON><PERSON><PERSON><PERSON>", "modelsPath": "/v1/models", "modelCapabilities": "Capacités du modèle", "modelCapabilitiesPath": "/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations", "assistants": "Assistants", "assistantsPath": "/v1/assistants", "assistantsModelsPath": "/v1/models (requis pour les Assistants)", "threads": "Fils de discussion", "threadsPath": "/v1/threads", "threadsModelsPath": "/v1/models (requis pour les Fils de discussion)", "evals": "Évaluations", "evalsPath": "/v1/evals", "fineTuning": "Ajustement fin", "fineTuningPath": "/v1/fine_tuning", "files": "Fichiers", "filesPath": "/v1/files"}, "resourcePermissions": {"none": "Aucune", "read": "Lecture", "write": "Écriture"}, "descriptions": {"serviceAccount": "Un nouveau membre bot (compte de service) sera ajouté à votre projet, et une clé API sera créée.", "userAccount": "Cette clé API est liée à votre utilisateur et peut effectuer des requêtes sur le projet sélectionné. Si vous êtes retiré de l'organisation ou du projet, cette clé sera désactivée.", "personal": "Cette clé API est liée à votre utilisateur et peut effectuer des requêtes sur le projet sélectionné. Si vous êtes retiré de l'organisation ou du projet, cette clé sera désactivée.", "permissionChange": "Les changements de permission peuvent prendre quelques minutes pour prendre effet."}, "actions": {"cancel": "Annuler", "save": "Enregistrer", "createKey": "<PERSON><PERSON><PERSON> une clé secrète", "create": "<PERSON><PERSON><PERSON> une clé secrète", "submitting": "Soumission en cours..."}, "titles": {"create": "<PERSON><PERSON>er une nouvelle clé secrète", "edit": "Modifier la clé secrète"}, "validation": {"required": "Ce champ est obligatoire", "name": "Le nom doit contenir uniquement des lettres, chiffres, tirets et underscores"}}, "batchDetailsCard": {"title": "LOT", "status": {"label": "Statut", "completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "pending": "En attente"}, "createdAt": {"label": "<PERSON><PERSON><PERSON>"}, "endpoint": {"label": "Point de terminaison"}, "completionWindow": {"label": "Fenêtre d'achèvement"}, "completionTime": {"label": "Temps d'achèvement"}, "requestCounts": {"label": "Nombre de requêtes", "info": "{{completed}} terminées, {{failed}} échouées sur {{total}} requêtes totales"}, "files": {"title": "Fichiers", "input": {"label": "<PERSON><PERSON>er d'entrée"}, "output": {"label": "<PERSON><PERSON><PERSON> de sortie"}, "error": {"label": "<PERSON><PERSON><PERSON> d'erreur"}, "download": "Télécharger {{file}}"}, "error": {"title": "Erreur de chargement du lot", "generic": "Échec du chargement des détails du lot"}, "tooltip": {"timeConversion": "Conversion de l'heure", "utc": "UTC", "local": "Local", "relative": "Relatif"}, "skeleton": {"ariaLabel": "Chargement des détails du lot"}, "demo": {"title": "Carte de Détails de Lot", "description": "Une carte complète affichant des informations détaillées sur un processus par lot", "showLoading": "Afficher l'état de chargement", "hideLoading": "Masquer l'état de chargement", "sizes": "<PERSON><PERSON><PERSON> de <PERSON>", "smallSize": "<PERSON>e taille", "mediumSize": "<PERSON><PERSON> m<PERSON>", "largeSize": "<PERSON> taille", "variants": "Variantes de style", "primaryVariant": "Variante principale", "secondaryVariant": "Variante secondaire", "outlineVariant": "Variante contour", "states": "États du composant", "disabledState": "État désactivé", "errorState": "État d'erreur", "customization": "Configurations personnalisées", "noTimeline": "Sans chronologie", "customTimeline": "Chronologie personnalisée", "fullWidth": "<PERSON><PERSON> complète", "languages": "V<PERSON>tes de langue"}}, "rtlDemo": {"title": "Démonstration RTL", "currentLanguage": "<PERSON>ue Actuelle", "direction": "Direction du Texte", "paragraph1": "Ce composant montre comment la direction du texte change en fonction de la langue sélectionnée. Le français est une langue de gauche à droite (LTR).", "paragraph2": "<PERSON><PERSON><PERSON> comment les boutons en bas maintiennent leurs positions par rapport à la direction du texte. En mode LTR, 'Précédent' est à gauche, tandis qu'en mode RTL, il apparaît à droite.", "previousButton": "Précédent", "nextButton": "Suivant", "formTitle": "Démo des Éléments de Formulaire", "nameLabel": "Nom", "namePlaceholder": "Entrez votre nom", "emailLabel": "Email", "emailPlaceholder": "<EMAIL>", "subscribeLabel": "S'abonner à la newsletter", "submitButton": "So<PERSON><PERSON><PERSON>", "textAlignmentTitle": "Démo d'Alignement de Texte", "textAlignStart": "Ce texte est aligné au début (gauche en LTR, droite en RTL).", "textAlignEnd": "Ce texte est aligné à la fin (droite en LTR, gauche en RTL).", "textAlignCenter": "Ce texte est centré quelle que soit la direction."}}