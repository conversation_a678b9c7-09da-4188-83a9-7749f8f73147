import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { axe, toHaveNoViolations } from 'jest-axe';
import { FormProvider, useForm } from 'react-hook-form';
import { Create, ApiKeyPermissionSection, PermissionNotification } from '../../index';
import { sampleProjects } from '../../__fixtures__/ApiKey.fixtures';

// Add jest-axe matcher
expect.extend(toHaveNoViolations);

// Wrapper component to provide form context for ApiKeyPermissionSection
const PermissionSectionWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      permissionType: 'Restricted' as const,
      resourcePermissions: {
        models: 'Read' as const,
        modelCapabilities: 'Write' as const,
        assistants: 'None' as const,
        threads: 'None' as const,
        evals: 'None' as const,
        fineTuning: 'None' as const,
        files: 'Read' as const,
      },
    },
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('ApiKey Components - Comprehensive WCAG 2.1 AA Compliance', () => {
  // WCAG 2.1 Success Criterion 1.1.1: Non-text Content
  describe('1.1.1 Non-text Content', () => {
    it('provides text alternatives for non-text content', async () => {
      const { container } = render(<PermissionNotification variant="info" />);

      // All SVG icons should have aria-hidden="true"
      const svgIcons = container.querySelectorAll('svg');
      svgIcons.forEach((icon) => {
        expect(icon).toHaveAttribute('aria-hidden', 'true');
      });

      // Run axe test to verify no accessibility violations
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  // WCAG 2.1 Success Criterion 1.3.1: Info and Relationships
  describe('1.3.1 Info and Relationships', () => {
    it('presents information and structure in a programmatically determinable way', async () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Check for proper form structure
      const form = container.querySelector('form');
      expect(form).toBeInTheDocument();

      // Check that inputs have associated labels
      const inputs = container.querySelectorAll('input, select');
      inputs.forEach((input) => {
        const id = input.getAttribute('id');
        if (id) {
          const label = container.querySelector(`label[for="${id}"]`);
          expect(label).toBeInTheDocument();
        }
      });

      // Run axe test to verify no accessibility violations
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  // WCAG 2.1 Success Criterion 1.3.2: Meaningful Sequence
  describe('1.3.2 Meaningful Sequence', () => {
    it('presents content in a meaningful sequence', () => {
      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Check that elements are in a logical DOM order
      const formElements = Array.from(document.querySelectorAll('label, input, select, button'));

      // Verify that the first element is a label or input
      expect(formElements[0]?.tagName.toLowerCase()).toMatch(/label|input/i);

      // Verify that buttons are at the end of the form
      const lastElements = formElements.slice(-3);
      const areButtons = lastElements.every(
        (el) => el.tagName.toLowerCase() === 'button' || el.getAttribute('role') === 'button',
      );
      expect(areButtons).toBe(true);
    });
  });

  // WCAG 2.1 Success Criterion 1.3.3: Sensory Characteristics
  describe('1.3.3 Sensory Characteristics', () => {
    it('does not rely solely on sensory characteristics for instructions', () => {
      const { container } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // Check that selected state is not conveyed by color alone
      const selectedButtons = container.querySelectorAll('.bg-primary');
      selectedButtons.forEach((button) => {
        // Should have aria-checked="true" for selection state
        expect(button).toHaveAttribute('aria-checked', 'true');
      });
    });
  });

  // WCAG 2.1 Success Criterion 1.4.3: Contrast (Minimum)
  describe('1.4.3 Contrast (Minimum)', () => {
    it('has sufficient contrast for text', () => {
      // In a real test, this would check actual contrast ratios
      // For this test, we're verifying the use of appropriate color classes
      const { container } = render(<PermissionNotification variant="info" />);

      // Selected elements use high contrast colors
      const primaryElements = container.querySelectorAll('.text-primary-foreground');
      expect(primaryElements.length).toBeGreaterThan(0);

      // Text on neutral backgrounds uses readable color
      const textElements = container.querySelectorAll('.text-muted-foreground');
      expect(textElements.length).toBeGreaterThan(0);
    });
  });

  // WCAG 2.1 Success Criterion 1.4.4: Resize Text
  describe('1.4.4 Resize Text', () => {
    it('allows text to be resized without loss of content or functionality', () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Simulate browser zoom to 200%
      const originalFontSize = window.getComputedStyle(document.body).fontSize;
      document.body.style.fontSize = `${parseFloat(originalFontSize) * 2}px`;

      // Verify all controls are still visible
      const formElements = container.querySelectorAll('input, select, button');
      formElements.forEach((el) => {
        expect(el).toBeVisible();
      });

      // Restore original font size
      document.body.style.fontSize = originalFontSize;
    });
  });

  // WCAG 2.1 Success Criterion 2.1.1: Keyboard
  describe('2.1.1 Keyboard', () => {
    it('makes all functionality available from a keyboard', async () => {
      const user = userEvent.setup();

      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Tab to first interactive element
      await user.tab();
      expect(document.activeElement).not.toBe(document.body);

      // Test we can tab through all focusable elements
      let elementCount = 0;
      let lastElement = null;

      while (elementCount < 30) {
        // Prevent infinite loop
        await user.tab();

        if (document.activeElement === lastElement) {
          break; // We've cycled through all elements
        }

        lastElement = document.activeElement;
        elementCount++;
      }

      // Should find multiple interactive elements
      expect(elementCount).toBeGreaterThan(3);
    });
  });

  // WCAG 2.1 Success Criterion 2.4.3: Focus Order
  describe('2.4.3 Focus Order', () => {
    it('provides a focus order that preserves meaning and operability', async () => {
      const user = userEvent.setup();
      const formSubmit = jest.fn();

      render(
        <Create onSubmit={formSubmit} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Focus the name field and enter a value
      await user.tab(); // First tab should focus the name field
      await user.keyboard('Test name');

      // Focus should be on the name field
      const nameInput = screen.getByLabelText(/Name/i);
      expect(document.activeElement).toBe(nameInput);

      // Tab to permission type
      await user.tab();
      // Should be able to interact with current focus
      await user.keyboard('{Space}');

      // Continue tabbing to the end
      for (let i = 0; i < 10; i++) {
        await user.tab();
      }

      // Submit should be one of the last focusable elements
      await user.keyboard('{Enter}');

      // Form should be submitted
      expect(formSubmit).toHaveBeenCalled();
    });
  });

  // WCAG 2.1 Success Criterion 2.4.6: Headings and Labels
  describe('2.4.6 Headings and Labels', () => {
    it('provides descriptive headings and labels', () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Check that all labels are descriptive
      const labels = container.querySelectorAll('label');
      labels.forEach((label) => {
        expect(label.textContent?.trim().length).toBeGreaterThan(0);
      });

      // Check that all headings are descriptive
      const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headings.forEach((heading) => {
        expect(heading.textContent?.trim().length).toBeGreaterThan(0);
      });
    });
  });

  // WCAG 2.1 Success Criterion 2.4.7: Focus Visible
  describe('2.4.7 Focus Visible', () => {
    it('provides a visible focus indicator', async () => {
      const user = userEvent.setup();

      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Focus the first element
      await user.tab();

      // Check for focus outline
      // In a real test, this would check for specific focus styles
      // Here we're simulating it by ensuring the outline property exists
      if (document.activeElement instanceof HTMLElement) {
        const style = window.getComputedStyle(document.activeElement);
        expect(style.outlineStyle).not.toBe('none');
      }
    });
  });

  // WCAG 2.1 Success Criterion 3.2.1: On Focus
  describe('3.2.1 On Focus', () => {
    it('does not change context on focus alone', async () => {
      const user = userEvent.setup();

      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Record initial page state
      const initialForm = document.querySelector('form');
      const initialElements = document.querySelectorAll('input, button, select').length;

      // Tab through all elements
      for (let i = 0; i < 10; i++) {
        await user.tab();
      }

      // Verify the form is still present and element count hasn't changed drastically
      const currentForm = document.querySelector('form');
      const currentElements = document.querySelectorAll('input, button, select').length;

      expect(currentForm).toBe(initialForm);
      expect(currentElements).toBe(initialElements);
    });
  });

  // WCAG 2.1 Success Criterion 3.2.2: On Input
  describe('3.2.2 On Input', () => {
    it('does not automatically change context on input without warning', async () => {
      const user = userEvent.setup();

      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Find input field
      const nameInput = screen.getByLabelText(/Name/i);

      // Focus and enter text
      await user.click(nameInput);
      await user.keyboard('Test input');

      // New context shouldn't be created automatically
      // The form should still be visible
      expect(screen.getByLabelText(/Name/i)).toBe(nameInput);
      expect(document.querySelector('form')).toBeInTheDocument();
    });
  });

  // WCAG 2.1 Success Criterion 3.3.1: Error Identification
  describe('3.3.1 Error Identification', () => {
    it('identifies input errors', async () => {
      const user = userEvent.setup();

      render(
        <Create
          onSubmit={() => {}}
          onCancel={() => {}}
          availableProjects={sampleProjects}
          validateForm={() => ({ name: 'This field is required' })}
        />,
      );

      // Try to submit the form to trigger validation
      const submitButton = screen.getByRole('button', { name: /Create/i });
      await user.click(submitButton);

      // Error message should be displayed
      const errorMessage = screen.getByText('This field is required');
      expect(errorMessage).toBeInTheDocument();

      // Input should have error state
      const nameInput = screen.getByLabelText(/Name/i);
      expect(nameInput).toHaveAttribute('aria-invalid', 'true');
    });
  });

  // WCAG 2.1 Success Criterion 3.3.2: Labels or Instructions
  describe('3.3.2 Labels or Instructions', () => {
    it('provides labels and instructions', () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Check that all inputs have associated labels
      const inputs = container.querySelectorAll('input, select');
      inputs.forEach((input) => {
        const id = input.getAttribute('id');
        if (id) {
          const label = container.querySelector(`label[for="${id}"]`);
          expect(label).toBeInTheDocument();
        }
      });

      // Required fields should be marked
      const requiredMarkers = Array.from(container.querySelectorAll('label')).filter((label) =>
        label.textContent?.includes('*'),
      );
      expect(requiredMarkers.length).toBeGreaterThan(0);
    });
  });

  // WCAG 2.1 Success Criterion 3.3.3: Error Suggestion
  describe('3.3.3 Error Suggestion', () => {
    it('provides error suggestions', async () => {
      const user = userEvent.setup();

      render(
        <Create
          onSubmit={() => {}}
          onCancel={() => {}}
          availableProjects={sampleProjects}
          validateForm={() => ({
            name: 'Name is required and must be at least 3 characters long',
          })}
        />,
      );

      // Try to submit the form to trigger validation
      const submitButton = screen.getByRole('button', { name: /Create/i });
      await user.click(submitButton);

      // Error message should be descriptive
      const errorMessage = screen.getByText(/must be at least 3 characters/i);
      expect(errorMessage).toBeInTheDocument();
    });
  });

  // WCAG 2.1 Success Criterion 3.3.4: Error Prevention
  describe('3.3.4 Error Prevention', () => {
    it('allows form submission to be reversible, verified, or confirmed', async () => {
      const user = userEvent.setup();
      const onSubmitMock = jest.fn();
      const onCancelMock = jest.fn();

      render(
        <Create
          onSubmit={onSubmitMock}
          onCancel={onCancelMock}
          availableProjects={sampleProjects}
        />,
      );

      // Find cancel button
      const cancelButton = screen.getByRole('button', { name: /Cancel/i });
      expect(cancelButton).toBeInTheDocument();

      // Test that cancel works
      await user.click(cancelButton);
      expect(onCancelMock).toHaveBeenCalled();

      // In a real app, we would also test that submission can be verified
      // or confirmed before final submission
    });
  });

  // WCAG 2.1 Success Criterion 4.1.2: Name, Role, Value
  describe('4.1.2 Name, Role, Value', () => {
    it('provides name, role, and value for all UI components', async () => {
      const { container } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // Check standard form controls
      const standardControls = container.querySelectorAll('button, input, select');
      standardControls.forEach((control) => {
        // Each control should have a name (via content, aria-label, or labelledby)
        const hasName =
          control.textContent?.trim().length ||
          control.getAttribute('aria-label') ||
          control.getAttribute('aria-labelledby');
        expect(hasName).toBeTruthy();

        // Role is implicit for standard controls
        expect(control.getAttribute('role') || control.tagName.toLowerCase()).toBeTruthy();
      });

      // Check custom controls
      const customControls = container.querySelectorAll('[role]');
      customControls.forEach((control) => {
        // Custom controls should have explicit roles
        expect(control.getAttribute('role')).toBeTruthy();

        // Custom controls should have states and properties
        if (control.getAttribute('role') === 'radio') {
          expect(control).toHaveAttribute('aria-checked');
        }
      });

      // Run axe test to verify no accessibility violations
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });
});
