import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, fn, userEvent, within } from '@storybook/test';
import { Edit } from '..';
import {
  sampleApiKeyAll,
  sampleApiKeyRestricted,
  sampleApiKeyReadOnly,
} from '../__fixtures__/ApiKey.fixtures';

const meta = {
  title: 'Components/ApiKey/Edit',
  component: Edit,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A modal form component for editing existing API keys, allowing users to modify permissions and other settings.',
      },
    },
    controls: { sort: 'requiredFirst' },
    badges: ['stable', 'tested', 'accessible'],
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#FFFFFF' },
        { name: 'dark', value: '#333333' },
      ],
    },
    design: {
      type: 'figma',
      url: 'https://www.figma.com/file/example-url-for-api-key-editing',
    },
  },
  args: {
    apiKey: sampleApiKeyRestricted,
    onSubmit: fn(),
    onCancel: fn(),
    isSubmitting: false,
  },
  argTypes: {
    apiKey: {
      description: 'The API key object to edit',
      control: 'object',
    },
    onSubmit: {
      description: 'Callback function called when the form is submitted',
      action: 'submitted',
    },
    onCancel: {
      description: 'Callback function called when the cancel button is clicked',
      action: 'cancelled',
    },
    isSubmitting: {
      control: 'boolean',
      description: 'Whether the form is currently submitting',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
    },
    className: {
      description: 'Additional CSS class to apply to the modal',
      control: 'text',
    },
  },
  decorators: [
    (Story) => (
      <div className="flex min-h-[600px] items-center justify-center bg-gray-100 p-8">
        <div className="w-full max-w-md">
          <Story />
        </div>
      </div>
    ),
  ],
  tags: ['autodocs'],
} satisfies Meta<typeof Edit>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default story showing the edit API key form with restricted permissions.
 */
export const Default: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Component should render with correct heading
    await expect(canvas.getByText('Edit secret key')).toBeInTheDocument();

    // Name field should be visible and pre-filled
    const nameInput = canvas.getByLabelText('Name');
    await expect(nameInput).toHaveValue(sampleApiKeyRestricted.name);

    // Permission notification should be visible
    await expect(
      canvas.getByText('Permission changes may take a few minutes to take effect.'),
    ).toBeInTheDocument();

    // Restricted permission should be selected
    const restrictedButton = canvas.getByText('Restricted');
    await expect(restrictedButton).toHaveClass('bg-primary');

    // Resource permissions section should be visible
    await expect(canvas.getByText('Resources')).toBeInTheDocument();

    // Models permission should be set to Read
    const readButtons = canvas.getAllByText('Read');
    await expect(readButtons.length).toBeGreaterThan(0);

    // Save and Cancel buttons should be visible
    await expect(canvas.getByRole('button', { name: /save/i })).toBeInTheDocument();
    await expect(canvas.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  },
};

/**
 * Shows the edit API key form with All permissions.
 */
export const EditAllPermissions: Story = {
  args: {
    apiKey: sampleApiKeyAll,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Component should render with correct heading
    await expect(canvas.getByText('Edit secret key')).toBeInTheDocument();

    // Name field should be visible and pre-filled
    const nameInput = canvas.getByLabelText('Name');
    await expect(nameInput).toHaveValue(sampleApiKeyAll.name);

    // All permission should be selected
    const allButton = canvas.getByText('All');
    await expect(allButton).toHaveClass('bg-primary');

    // Resource permissions section should not be visible
    await expect(canvas.queryByText('Resources')).not.toBeInTheDocument();
  },
};

/**
 * Shows the edit API key form with Read Only permissions.
 */
export const EditReadOnlyPermissions: Story = {
  args: {
    apiKey: sampleApiKeyReadOnly,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Component should render with correct heading
    await expect(canvas.getByText('Edit secret key')).toBeInTheDocument();

    // Name field should be visible and pre-filled
    const nameInput = canvas.getByLabelText('Name');
    await expect(nameInput).toHaveValue(sampleApiKeyReadOnly.name);

    // Read only permission should be selected
    const readOnlyButton = canvas.getByText('Read only');
    await expect(readOnlyButton).toHaveClass('bg-primary');

    // Resource permissions section should not be visible
    await expect(canvas.queryByText('Resources')).not.toBeInTheDocument();
  },
};

/**
 * Shows the edit API key form while submitting.
 */
export const Submitting: Story = {
  args: {
    apiKey: sampleApiKeyRestricted,
    isSubmitting: true,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Look for submit button using a more flexible selector
    const submitButton = canvas.getByRole('button', { name: /save|update|submit/i });
    await expect(submitButton).toBeDisabled();

    // Cancel button should be disabled
    const cancelButton = canvas.getByRole('button', { name: /cancel/i });
    await expect(cancelButton).toBeDisabled();
  },
};

/**
 * Shows changing permissions from one type to another.
 */
export const ChangePermissionType: Story = {
  args: {
    apiKey: sampleApiKeyRestricted,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Initially Resource permissions section should be visible
    await expect(canvas.getByText('Resources')).toBeInTheDocument();

    // Change to All permissions
    const allButton = canvas.getByText('All');
    await userEvent.click(allButton);

    // All permission should now be selected
    await expect(allButton).toHaveClass('bg-primary');

    // Resource permissions section should no longer be visible
    await expect(canvas.queryByText('Resources')).not.toBeInTheDocument();
  },
};

/**
 * Shows validation when submitting form.
 */
export const SubmitForm: Story = {
  args: {
    apiKey: sampleApiKeyRestricted,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Verify form is present
    const nameInput = canvas.getByLabelText('Name');
    await expect(nameInput).toBeInTheDocument();

    // Check for buttons
    const saveButton = canvas.getByRole('button', { name: /save/i });
    await expect(saveButton).toBeInTheDocument();

    const cancelButton = canvas.getByRole('button', { name: /cancel/i });
    await expect(cancelButton).toBeInTheDocument();
  },
};

/**
 * Shows canceling the edit.
 */
export const CancelEdit: Story = {
  args: {
    apiKey: sampleApiKeyRestricted,
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);

    // Click the cancel button
    const cancelButton = canvas.getByRole('button', { name: /cancel/i });
    await userEvent.click(cancelButton);

    // onCancel should have been called
    await expect(args.onCancel).toHaveBeenCalled();
  },
};
