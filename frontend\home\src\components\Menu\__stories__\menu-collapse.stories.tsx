import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { MenuCollapse } from '../menu-collapse';
import { 
  productCategories, 
  brandItems, 
  ratingOptions, 
  priceRangeValues,
  faqItems 
} from '../__fixtures__/menu-collapse.fixtures';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { X } from 'lucide-react';
import { action } from '@storybook/addon-actions';

const meta: Meta<typeof MenuCollapse> = {
  title: 'UI/Menu/MenuCollapse',
  component: MenuCollapse,
  parameters: {
    layout: 'centered',
    a11y: {
      config: {
        rules: [
          {
            id: 'button-name',
            enabled: true
          }
        ]
      }
    }
  },
  argTypes: {
    title: { control: 'text' },
    defaultOpen: { control: 'boolean' },
    className: { control: 'text' },
    headerClassName: { control: 'text' },
    contentClassName: { control: 'text' },
    ariaLabel: { control: 'text' },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof MenuCollapse>;

// Basic story with text content
export const Basic: Story = {
  args: {
    title: 'หมวดหมู่สินค้า',
    defaultOpen: true,
    children: (
      <div className="space-y-2">
        <p>This is a basic example with text content.</p>
        <p>You can put any content inside the menu collapse.</p>
      </div>
    ),
  },
};

// Category list example (like image 1)
export const CategoryList: Story = {
  args: {
    title: 'หมวดหมู่สินค้า',
    defaultOpen: true,
    children: (
      <div className="space-y-2 -mx-4">
        {productCategories.map((category) => (
          <div 
            key={category.id}
            className="px-4 py-2 hover:bg-slate-100 cursor-pointer"
          >
            {category.label}
          </div>
        ))}
      </div>
    ),
  },
};

// Checkbox list example (like image 2)
export const CheckboxList = () => {
  const [items, setItems] = useState([...brandItems]);
  
  const handleItemChange = (id: string, checked: boolean) => {
    setItems(items.map(item => 
      item.id === id ? { ...item, checked } : item
    ));
    action('checkbox changed')({ id, checked });
  };
  
  const handleClearAll = () => {
    setItems(items.map(item => ({ ...item, checked: false })));
    action('cleared all checkboxes')();
  };
  
  // Header action with clear button
  const headerActionElement = items.some(item => item.checked) ? (
    <button 
      onClick={handleClearAll}
      className="flex items-center text-gray-500 hover:text-gray-700"
      aria-label="Clear all selections"
    >
      <X className="h-4 w-4" />
    </button>
  ) : null;
  
  return (
    <div className="w-80">
      <MenuCollapse 
        title="แบรนด์" 
        defaultOpen={true}
        headerAction={headerActionElement}
      >
        <div className="space-y-3">
          {items.map((item) => (
            <div key={item.id} className="flex items-center space-x-2">
              <Checkbox 
                id={`brand-${item.id}`} 
                checked={item.checked}
                onCheckedChange={(checked) => 
                  handleItemChange(item.id, checked as boolean)
                }
              />
              <Label 
                htmlFor={`brand-${item.id}`}
                className="text-sm font-normal cursor-pointer"
              >
                {item.label}
              </Label>
            </div>
          ))}
        </div>
      </MenuCollapse>
    </div>
  );
};

// Rating stars example (like image 3)
export const RatingList = () => {
  const [selectedRating, setSelectedRating] = useState<string | null>(null);
  
  return (
    <div className="w-80">
      <MenuCollapse title="คะแนนรีวิว" defaultOpen={true}>
        <div className="space-y-2">
          {ratingOptions.map((option) => (
            <div key={option.id} className="flex items-center space-x-2">
              <Checkbox 
                id={`rating-${option.id}`}
                checked={selectedRating === option.id}
                onCheckedChange={(checked) => {
                  setSelectedRating(checked ? option.id : null);
                  action('rating changed')(option);
                }}
              />
              <Label 
                htmlFor={`rating-${option.id}`}
                className="flex items-center cursor-pointer"
              >
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <span key={i}>
                      {i < option.value ? (
                        <span className="text-yellow-400">★</span>
                      ) : (
                        <span className="text-gray-300">★</span>
                      )}
                    </span>
                  ))}
                </div>
              </Label>
            </div>
          ))}
        </div>
      </MenuCollapse>
    </div>
  );
};

// Price range slider example (like image 4)
export const PriceRangeSlider = () => {
  const [range, setRange] = useState({
    min: priceRangeValues.min,
    max: priceRangeValues.max,
    values: [priceRangeValues.currentMin, priceRangeValues.currentMax]
  });
  
  const handleSliderChange = (values: number[]) => {
    setRange({ ...range, values });
    action('slider changed')(values);
  };
  
  return (
    <div className="w-80">
      <MenuCollapse title="ช่วงราคา" defaultOpen={true}>
        <div className="space-y-6">
          <p className="text-center text-sm mb-4">ระบุช่วงราคาที่คุณต้องการ</p>
          
          <Slider 
            defaultValue={[range.values[0], range.values[1]]}
            min={range.min}
            max={range.max}
            step={100}
            value={[range.values[0], range.values[1]]}
            onValueChange={handleSliderChange}
            className="my-6"
          />
          
          <div className="flex justify-between gap-4">
            <div>
              <p className="text-xs mb-1 text-gray-500">ราคาต่ำสุด</p>
              <Input 
                type="number" 
                value={range.values[0]} 
                onChange={(e) => {
                  const value = Number(e.target.value);
                  if (value >= range.min && value <= range.values[1]) {
                    setRange({
                      ...range,
                      values: [value, range.values[1]]
                    });
                  }
                }}
                className="h-10"
              />
            </div>
            <div>
              <p className="text-xs mb-1 text-gray-500">ราคาสูงสุด</p>
              <Input 
                type="number" 
                value={range.values[1]} 
                onChange={(e) => {
                  const value = Number(e.target.value);
                  if (value <= range.max && value >= range.values[0]) {
                    setRange({
                      ...range,
                      values: [range.values[0], value]
                    });
                  }
                }}
                className="h-10"
              />
            </div>
          </div>
        </div>
      </MenuCollapse>
    </div>
  );
};

// FAQ example (like image 5)
export const FAQ = () => {
  return (
    <div className="w-full max-w-2xl space-y-1">
      {faqItems.map((faq) => (
        <MenuCollapse 
          key={faq.id}
          title={faq.title}
          defaultOpen={faq.id === "1"}
          className="rounded-md bg-white my-1 border border-gray-100 shadow-sm"
        >
          <div className="prose prose-sm max-w-none">
            <p>{faq.content}</p>
          </div>
        </MenuCollapse>
      ))}
    </div>
  );
};

// Controlled component example
export const Controlled = () => {
  const [openMenu, setOpenMenu] = useState<string | null>("menu1");
  
  return (
    <div className="w-80 space-y-2">
      <p className="text-sm text-gray-500 mb-2">
        Only one menu can be open at a time in this example
      </p>
      
      <MenuCollapse 
        title="Menu 1"
        open={openMenu === "menu1"}
        onOpenChange={(isOpen) => setOpenMenu(isOpen ? "menu1" : null)}
      >
        <p>Content for menu 1</p>
      </MenuCollapse>
      
      <MenuCollapse 
        title="Menu 2"
        open={openMenu === "menu2"}
        onOpenChange={(isOpen) => setOpenMenu(isOpen ? "menu2" : null)}
      >
        <p>Content for menu 2</p>
      </MenuCollapse>
      
      <MenuCollapse 
        title="Menu 3"
        open={openMenu === "menu3"}
        onOpenChange={(isOpen) => setOpenMenu(isOpen ? "menu3" : null)}
      >
        <p>Content for menu 3</p>
      </MenuCollapse>
    </div>
  );
};