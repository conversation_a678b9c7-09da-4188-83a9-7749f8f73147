// card-product.fixtures.tsx
import { CardProductProps } from '../card-product';

/**
 * Sample product data for tests and stories
 */
export const sampleProducts: CardProductProps[] = [
  {
    id: '1',
    imageUrl: '/images/wire-sample.png',
    brand: 'BCC',
    name: 'สายไฟ IEC(IV) 1x1 ตร.มม. ยาว 100เมตร สีเขียว',
    price: 597,
    originalPrice: 670,
    discountPercentage: 10,
    unit: 'บาท/ม้วน',
    soldCount: 1338,
    imageAlt: 'สายไฟ IEC(IV) สีเขียว',
  },
  {
    id: '2',
    imageUrl: '/images/light-fixture.png',
    brand: 'PHILIPS',
    name: 'ดาวน์ไลท์ LED 13 วัตต์ COOL WHITE 5 นิ้ว',
    price: 175,
    originalPrice: 250,
    discountPercentage: 30,
    unit: 'บาท/ชิ้น',
    soldCount: 1338,
    imageAlt: 'ดาวน์ไลท์ LED PHILIPS',
  },
  {
    id: '3',
    imageUrl: '/images/desk-lamp.png',
    brand: 'SILLICONS',
    name: 'โพดีงโต๊ะ LED Light bar V.2 สีดำ [รับประกัน 1 ปี]',
    price: 2990,
    unit: 'บาท/ชิ้น',
    soldCount: 3,
    imageAlt: 'โพดีงโต๊ะ LED Light bar',
  },
];

/**
 * Individual sample products for specific tests
 */
export const wireProduct = sampleProducts[0];
export const lightFixtureProduct = sampleProducts[1];
export const deskLampProduct = sampleProducts[2];

/**
 * Mock function for adding to cart
 */
export const mockAddToCart = () => {
  // Simple mock function that can be used in tests
  return function(id: string) {
    console.log(`Product ${id} added to cart`);
    return id;
  };
};