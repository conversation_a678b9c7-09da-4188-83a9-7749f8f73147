// card-coupon.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { CardCoupon, CardCouponSkeleton } from '../card-coupon';
import { 
  amountCoupon,
  percentageCoupon,
  maxPercentageCoupon,
  claimedCoupon,
  termsOnlyCoupon,
  claimingCoupon
} from '../__fixtures__/card-coupon.fixtures';

// Mock next/image 
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} />
  },
}));

describe('CardCoupon', () => {
  // Test rendering for different discount types
  describe('Discount type rendering', () => {
    test('renders fixed amount discount correctly', () => {
      render(<CardCoupon {...amountCoupon} />);
      
      expect(screen.getByText('ส่วนลด')).toBeInTheDocument();
      expect(screen.getByText('2,500 บาท')).toBeInTheDocument();
      expect(screen.getByText(/เมื่อซื้อสินค้าขั้นต่ำ/)).toBeInTheDocument();
      expect(screen.getByText(/30,000/)).toBeInTheDocument();
    });

    test('renders percentage discount correctly', () => {
      render(<CardCoupon {...percentageCoupon} />);
      
      expect(screen.getByText('ส่วนลด')).toBeInTheDocument();
      expect(screen.getByText('75%')).toBeInTheDocument();
      expect(screen.getByText(/ซื้อขั้นต่ำ/)).toBeInTheDocument();
      expect(screen.getByText(/30,000/)).toBeInTheDocument();
      expect(screen.getByText(/1,000/)).toBeInTheDocument();
    });

    test('renders max percentage discount correctly', () => {
      render(<CardCoupon {...maxPercentageCoupon} />);
      
      expect(screen.getByText('ส่วนลด')).toBeInTheDocument();
      expect(screen.getByText('สูงสุด 50%')).toBeInTheDocument();
      expect(screen.getByText(/ซื้อขั้นต่ำ/)).toBeInTheDocument();
      expect(screen.getByText(/30,000/)).toBeInTheDocument();
      expect(screen.getByText(/500/)).toBeInTheDocument();
    });
  });

  // Test button states and callbacks
  describe('Button functionality', () => {
    test('calls onSeeDetails when terms button is clicked', () => {
      const onSeeDetailsMock = jest.fn();
      render(<CardCoupon {...amountCoupon} onSeeDetails={onSeeDetailsMock} />);
      
      const termsButton = screen.getByText('เงื่อนไข');
      fireEvent.click(termsButton);
      
      expect(onSeeDetailsMock).toHaveBeenCalledWith(amountCoupon.id);
    });

    test('calls onClaimCoupon when claim button is clicked', () => {
      const onClaimCouponMock = jest.fn();
      render(<CardCoupon {...amountCoupon} onClaimCoupon={onClaimCouponMock} />);
      
      const claimButton = screen.getByText('เก็บคูปองเลย!');
      fireEvent.click(claimButton);
      
      expect(onClaimCouponMock).toHaveBeenCalledWith(amountCoupon.id);
    });

    test('does not call onClaimCoupon when button is clicked if coupon is claimed', () => {
      const onClaimCouponMock = jest.fn();
      render(<CardCoupon {...claimedCoupon} onClaimCoupon={onClaimCouponMock} />);
      
      const claimedButton = screen.getByText('เก็บคูปองแล้ว');
      fireEvent.click(claimedButton);
      
      expect(onClaimCouponMock).not.toHaveBeenCalled();
    });

    test('does not call onClaimCoupon when button is clicked if coupon is being claimed', () => {
      const onClaimCouponMock = jest.fn();
      render(<CardCoupon {...claimingCoupon} onClaimCoupon={onClaimCouponMock} />);
      
      const claimButton = screen.getByText('เก็บคูปองเลย!');
      fireEvent.click(claimButton);
      
      expect(onClaimCouponMock).not.toHaveBeenCalled();
    });

    test('shows only terms button when showTermsOnly is true', () => {
      render(<CardCoupon {...termsOnlyCoupon} />);
      
      expect(screen.getByText('เงื่อนไข')).toBeInTheDocument();
      expect(screen.queryByText('เก็บคูปองเลย!')).not.toBeInTheDocument();
      expect(screen.queryByText('เก็บคูปองแล้ว')).not.toBeInTheDocument();
    });
  });

  // Test accessibility and visual states
  describe('Accessibility and visual states', () => {
    test('renders brand logo with correct alt text', () => {
      render(<CardCoupon {...amountCoupon} />);
      
      const logoImg = screen.getByTestId('coupon-brand-logo');
      expect(logoImg).toHaveAttribute('alt', amountCoupon.brandName);
    });

    test('renders loading skeleton correctly', () => {
      render(<CardCouponSkeleton />);
      
      // Check for skeleton elements
      const skeletonElements = document.querySelectorAll('[class*="skeleton"]');
      expect(skeletonElements.length).toBeGreaterThan(0);
    });

    test('applies additional className correctly', () => {
      const customClass = 'custom-card-class';
      render(<CardCoupon {...amountCoupon} className={customClass} />);
      
      const cardElement = screen.getByTestId('card-coupon');
      expect(cardElement).toHaveClass(customClass);
    });
  });

  // Test conditional rendering
  describe('Conditional rendering', () => {
    test('renders claimed button when coupon is claimed', () => {
      render(<CardCoupon {...claimedCoupon} />);
      
      expect(screen.getByText('เก็บคูปองแล้ว')).toBeInTheDocument();
      expect(screen.queryByText('เก็บคูปองเลย!')).not.toBeInTheDocument();
    });

    test('renders claim button disabled when coupon is being claimed', () => {
      render(<CardCoupon {...claimingCoupon} />);
      
      const claimButton = screen.getByText('เก็บคูปองเลย!');
      expect(claimButton).toBeDisabled();
    });
  });
});