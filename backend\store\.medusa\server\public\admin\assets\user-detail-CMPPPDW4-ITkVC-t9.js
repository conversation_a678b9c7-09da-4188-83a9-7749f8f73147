import{S as m}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{cS as d,j as a,q as p,d as f,R as x,a as g,dv as y,p as h,s as j,b as v,u as S,eh as b,H as w,A as D,T as l,t as c}from"./index-Bwql5Dzz.js";import{u as T}from"./use-prompt-pbDx0Sfe.js";import{P as C}from"./pencil-square-6wRbnn1C.js";import{T as U}from"./trash-BBylvTAG.js";import{C as P}from"./container-Dqi2woPF.js";import"./Trans-VWqfqpAH.js";import"./x-mark-mini-DvSTI7zK.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var K=e=>{const{id:s}=e.params||{},{user:t}=d(s,void 0,{initialData:e.data,enabled:!!s});if(!t)return null;const r=[t.first_name,t.last_name].filter(Boolean).join(" ")||t.email;return a.jsx("span",{children:r})},q=e=>({queryKey:h.detail(e),queryFn:async()=>j.admin.user.retrieve(e)}),O=async({params:e})=>{const s=e.id,t=q(s);return p.ensureQueryData(t)},N=({user:e})=>{const{t:s}=v(),t=S(),i=T(),{mutateAsync:r}=b(e.id),n=[e.first_name,e.last_name].filter(Boolean).join(" "),o=async()=>{await i({title:s("general.areYouSure"),description:s("users.deleteUserWarning",{name:n??e.email}),verificationText:n??e.email,verificationInstruction:s("general.typeToConfirm"),confirmText:s("actions.delete"),cancelText:s("actions.cancel")})&&await r(void 0,{onSuccess:()=>{c.success(s("users.deleteUserSuccess",{name:e.email})),t("..")},onError:u=>{c.error(u.message)}})};return a.jsxs(P,{className:"divide-y p-0",children:[a.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[a.jsx(w,{children:e.email}),a.jsx(D,{groups:[{actions:[{label:s("actions.edit"),to:"edit",icon:a.jsx(C,{})}]},{actions:[{label:s("actions.delete"),onClick:o,icon:a.jsx(U,{})}]}]})]}),a.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[a.jsx(l,{size:"small",leading:"compact",weight:"plus",children:s("fields.name")}),a.jsx(l,{size:"small",leading:"compact",children:n??"-"})]})]})},R=()=>{const e=f(),{id:s}=x(),{user:t,isPending:i,isError:r,error:n}=d(s,void 0,{initialData:e}),{getWidgets:o}=g();if(i||!t)return a.jsx(y,{sections:1,showJSON:!0,showMetadata:!0});if(r)throw n;return a.jsx(m,{data:t,showJSON:!0,showMetadata:!0,widgets:{after:o("user.details.after"),before:o("user.details.before")},children:a.jsx(N,{user:t})})};export{K as Breadcrumb,R as Component,O as loader};
