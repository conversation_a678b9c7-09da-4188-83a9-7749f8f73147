import{u as x,a as b,b as v}from"./chunk-VGFBGBUR-C0k6se_C.js";import"./chunk-6LRPF7MX-BV-FSPta.js";import{q as h,a as j,j as o,d6 as P,s as T,b as u,d as w,d7 as C,H as D,B as S,L as q,O as L,r as k,k as A,u as E,d8 as Q,A as _}from"./index-Bwql5Dzz.js";import{u as z,_ as H}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as B}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as I}from"./use-prompt-pbDx0Sfe.js";import{P as K}from"./pencil-square-6wRbnn1C.js";import{T as N}from"./trash-BBylvTAG.js";import{C as O}from"./container-Dqi2woPF.js";import{c as $}from"./index-BxZ1678G.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var p={limit:20,offset:0},F=()=>({queryKey:P.list(p),queryFn:async()=>T.admin.promotion.list(p)}),Le=e=>async()=>{const t=F();return h.getQueryData(t.queryKey)??await e.fetchQuery(t)},m=20,M=()=>{const{t:e}=u(),t=w(),{searchParams:a,raw:i}=x({pageSize:m}),{promotions:s,count:r,isLoading:c,isError:d,error:y}=C({...a},{initialData:t,placeholderData:A}),f=b(),l=G(),{table:g}=z({data:s??[],columns:l,count:r,enablePagination:!0,pageSize:m,getRowId:n=>n.id});if(d)throw y;return o.jsxs(O,{className:"divide-y p-0",children:[o.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[o.jsx(D,{level:"h2",children:e("promotions.domain")}),o.jsx(S,{size:"small",variant:"secondary",asChild:!0,children:o.jsx(q,{to:"create",children:e("actions.create")})})]}),o.jsx(H,{table:g,columns:l,count:r,pageSize:m,filters:f,search:!0,pagination:!0,isLoading:c,queryObject:i,navigateTo:n=>`${n.original.id}`,orderBy:[{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}]}),o.jsx(L,{})]})},R=({promotion:e})=>{const{t}=u(),a=I(),i=E(),{mutateAsync:s}=Q(e.id),r=async()=>{if(await a({title:t("general.areYouSure"),description:t("promotions.deleteWarning",{code:e.code}),confirmText:t("actions.delete"),cancelText:t("actions.cancel"),verificationInstruction:t("general.typeToConfirm"),verificationText:e.code}))try{await s(void 0,{onSuccess:()=>{i("/promotions",{replace:!0})}})}catch{throw new Error(`Promotion with code ${e.code} could not be deleted`)}};return o.jsx(_,{groups:[{actions:[{icon:o.jsx(K,{}),label:t("actions.edit"),to:`/promotions/${e.id}/edit`},{icon:o.jsx(N,{}),label:t("actions.delete"),onClick:r}]}]})},W=$(),G=()=>{const e=v();return k.useMemo(()=>[...e,W.display({id:"actions",cell:({row:t})=>o.jsx(R,{promotion:t.original})})],[e])},ke=()=>{const{getWidgets:e}=j();return o.jsx(B,{widgets:{before:e("promotion.list.before"),after:e("promotion.list.after")},children:o.jsx(M,{})})};export{ke as Component,Le as promotionsLoader};
