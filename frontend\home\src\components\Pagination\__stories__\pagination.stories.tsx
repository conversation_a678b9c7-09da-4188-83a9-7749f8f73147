// components/Pagination/_stories_/pagination.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Pagination } from '../pagination';
import { paginationFixtures } from '../__fixtures__/pagination.fixtures';

// This control component allows us to test state changes in Storybook
const PaginationWithState = (props: any) => {
  const [currentPage, setCurrentPage] = useState(props.currentPage || 1);

  return (
    <div className="flex flex-col items-center gap-4">
      <div className="text-sm text-gray-500">Current Page: {currentPage}</div>
      <Pagination
        {...props}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
      />
    </div>
  );
};

const meta: Meta<typeof Pagination> = {
  title: 'UI/Pagination/Pagination',
  component: Pagination,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A reusable pagination component with two variants: numbered and simple.',
      },
    },
    a11y: {
      config: {
        // WCAG 2.0 Level AA accessibility rules
        rules: [
          { id: 'aria-required-parent', enabled: true },
          { id: 'button-name', enabled: true },
          { id: 'color-contrast', enabled: true },
        ],
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    totalPages: {
      control: { type: 'number', min: 1, max: 100 },
      description: 'Total number of pages',
    },
    currentPage: {
      control: { type: 'number', min: 1 },
      description: 'Current active page (1-based indexing)',
    },
    onPageChange: {
      action: 'page changed',
      description: 'Called when page is changed',
    },
    siblingCount: {
      control: { type: 'number', min: 1, max: 10 },
      description: 'Number of page buttons to show',
    },
    className: {
      control: 'text',
      description: 'Custom CSS class for the component',
    },
    variant: {
      control: 'radio',
      options: ['numbered', 'simple'],
      description: 'Variant of pagination (numbered or simple)',
    },
    showEllipsis: {
      control: 'boolean',
      description: 'Show ellipsis (...) when there are many pages',
    },
    pageText: {
      control: 'text',
      description: 'Text for page indicator (only used in simple variant)',
    },
    disabled: {
      control: 'boolean',
      description: 'Disable the pagination component',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Pagination>;

// Numbered variant stories (Variant 1)
export const NumberedVariantFirstPage: Story = {
  args: paginationFixtures.numberedBasic,
  render: (args) => <Pagination {...args} />,
};

export const NumberedVariantMiddlePage: Story = {
  args: paginationFixtures.numberedMiddlePage,
  render: (args) => <Pagination {...args} />,
};

export const NumberedVariantLastPage: Story = {
  args: paginationFixtures.numberedLastPage,
  render: (args) => <Pagination {...args} />,
};

export const NumberedVariantFewPages: Story = {
  args: paginationFixtures.numberedFewPages,
  render: (args) => <Pagination {...args} />,
};

export const NumberedVariantDisabled: Story = {
  args: paginationFixtures.numberedDisabled,
  render: (args) => <Pagination {...args} />,
};

export const NumberedVariantNoEllipsis: Story = {
  args: paginationFixtures.numberedNoEllipsis,
  render: (args) => <Pagination {...args} />,
};

// Simple variant stories (Variant 2)
export const SimpleVariantFirstPage: Story = {
  args: paginationFixtures.simpleFirstPage,
  render: (args) => <Pagination {...args} />,
};

export const SimpleVariantMiddlePage: Story = {
  args: paginationFixtures.simpleMiddlePage,
  render: (args) => <Pagination {...args} />,
};

export const SimpleVariantLastPage: Story = {
  args: paginationFixtures.simpleLastPage,
  render: (args) => <Pagination {...args} />,
};

export const SimpleVariantCustomText: Story = {
  args: paginationFixtures.simpleCustomText,
  render: (args) => <Pagination {...args} />,
};

export const SimpleVariantDisabled: Story = {
  args: paginationFixtures.simpleDisabled,
  render: (args) => <Pagination {...args} />,
};

// Interactive examples with state
export const NumberedVariantInteractive: Story = {
  render: () => (
    <PaginationWithState
      totalPages={12}
      currentPage={5}
      variant="numbered"
    />
  ),
};

export const SimpleVariantInteractive: Story = {
  render: () => (
    <PaginationWithState
      totalPages={24}
      currentPage={12}
      variant="simple"
    />
  ),
};