'use client';

import * as React from 'react';
import { useState, useCallback, useMemo, useRef } from 'react';
import { cva } from 'class-variance-authority';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  createColumnHelper,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { TimeConversionTooltip } from '@/components/TimeConversionTooltip';
import { createDateConversionData } from '@/lib/dateFormatter';
import type { OneColTableProps, TableItem } from './types';
import { OneColTableSkeleton } from './one-col-table-skeleton';
import { formatTimestamp } from './utils';
import { ROW_HEIGHTS, ANIMATION_CONFIG, VIRTUALIZATION_CONFIG } from './constants';

// CVA variant definition for OneColTable
export const oneColTableVariants = cva('w-full rounded-md overflow-hidden', {
  variants: {
    variant: {
      primary: 'bg-background border border-border',
      secondary: 'bg-muted',
      outline: 'border border-border',
    },
    size: {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
    },
    state: {
      default: '',
      loading: 'opacity-70 cursor-not-allowed',
      disabled: 'opacity-50 cursor-not-allowed',
      error: 'border-destructive',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
    state: 'default',
  },
});

// CVA for individual rows
const rowVariants = cva('flex items-center justify-between w-full px-4 py-3 transition-colors', {
  variants: {
    variant: {
      primary: 'hover:bg-accent/50',
      secondary: 'hover:bg-background/50',
      outline: 'hover:bg-accent/50',
    },
    state: {
      default: 'cursor-pointer',
      loading: 'cursor-not-allowed',
      disabled: 'cursor-not-allowed',
      active: 'bg-accent',
    },
  },
  defaultVariants: {
    variant: 'primary',
    state: 'default',
  },
});

/**
 * OneColTable - A single column table/list component with virtualization support
 *
 * @example
 * // Basic usage
 * <OneColTable items={data} />
 *
 * // With virtualization
 * <OneColTable
 *   items={largeDataset}
 *   virtualized
 *   height={400}
 * />
 *
 * // With loading state
 * <OneColTable
 *   items={data}
 *   loading={isLoading}
 * />
 */
const OneColTable = React.forwardRef<HTMLDivElement, OneColTableProps>(
  (
    {
      // Variants
      variant = 'primary',
      size = 'md',
      className,

      // State props
      disabled = false,
      loading = false,
      error,

      // Data props
      items = [],
      header,
      emptyState,
      limit,

      // Event handlers
      onRowClick,
      onRowHover,

      // Pagination/infinite scroll
      showLoadMore = false,
      onLoadMore,
      hasMore = false,

      // Animation
      animate = true,
      animationDelay = ANIMATION_CONFIG.defaultDelay,

      // Virtualization
      virtualized = false,
      height = VIRTUALIZATION_CONFIG.defaultHeight,
      width = VIRTUALIZATION_CONFIG.defaultWidth,
      overscan = VIRTUALIZATION_CONFIG.defaultOverscan,

      // Skeleton props
      skeletonProps,

      // Other props
      ...restProps
    },
    ref,
  ) => {
    // State for tracking hovered row
    const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

    // Filter items if limit is specified
    const displayedItems = limit ? items.slice(0, limit) : items;

    // Reference for virtualization parent container
    const parentRef = useRef<HTMLDivElement>(null);

    // Component state determination (for variants)
    const componentState = useMemo(() => {
      if (loading) return 'loading';
      if (disabled) return 'disabled';
      if (error) return 'error';
      return 'default';
    }, [loading, disabled, error]);

    // Create column helper and define columns for TanStack Table
    const columnHelper = createColumnHelper<TableItem>();

    const columns = useMemo(
      () => [
        columnHelper.accessor('content', {
          id: 'content',
          cell: ({ row }) => {
            const item = row.original;
            return (
              <div className="flex-1">
                <div className="font-medium">{item.content}</div>
                {item.secondaryContent && (
                  <div className="text-muted-foreground text-sm">{item.secondaryContent}</div>
                )}
              </div>
            );
          },
        }),
        columnHelper.accessor('timestamp', {
          id: 'timestamp',
          cell: ({ row }) => {
            const item = row.original;
            if (!item.timestamp) return null;

            const tooltipData = createDateConversionData(
              item.timestamp instanceof Date
                ? item.timestamp.toISOString()
                : String(item.timestamp),
            );

            return (
              <div className="text-muted-foreground text-sm">
                <TimeConversionTooltip data={tooltipData} enabled={true}>
                  {formatTimestamp(item.timestamp)}
                </TimeConversionTooltip>
              </div>
            );
          },
        }),
      ],
      [columnHelper],
    );

    // Setup TanStack table
    const table = useReactTable({
      data: displayedItems,
      columns,
      getCoreRowModel: getCoreRowModel(),
    });

    // Setup virtualizer
    const virtualizer = useVirtualizer({
      count: displayedItems.length,
      getScrollElement: () => parentRef.current,
      estimateSize: () => ROW_HEIGHTS[size], // Use height constants
      overscan,
    });

    // Handle row click
    const handleRowClick = useCallback(
      (item: TableItem, index: number) => {
        if (disabled || loading) return;
        onRowClick?.(item, index);
      },
      [disabled, loading, onRowClick],
    );

    // Handle row hover
    const handleRowHover = useCallback(
      (item: TableItem, index: number) => {
        if (disabled || loading) return;
        setHoveredIndex(index);
        onRowHover?.(item, index);
      },
      [disabled, loading, onRowHover],
    );

    // Handle row hover exit
    const handleRowLeave = useCallback(() => {
      setHoveredIndex(null);
    }, []);

    // Handle load more click
    const handleLoadMore = useCallback(() => {
      if (disabled || loading) return;
      onLoadMore?.();
    }, [disabled, loading, onLoadMore]);

    // If loading, show skeleton
    if (loading) {
      return (
        <OneColTableSkeleton
          size={size}
          rowCount={skeletonProps?.rowCount || 5}
          simplified={skeletonProps?.simplified}
          className={className}
        />
      );
    }

    // Empty state
    if (displayedItems.length === 0) {
      return (
        <div
          ref={ref}
          className={cn(
            oneColTableVariants({ variant, size, state: componentState }),
            'flex flex-col items-center justify-center p-8',
            className,
          )}
          role="table"
          aria-busy={loading}
          data-disabled={disabled}
          {...restProps}
        >
          {emptyState || (
            <div className="text-muted-foreground text-center">
              <p>No items found</p>
            </div>
          )}
        </div>
      );
    }

    // Regular non-virtualized table
    if (!virtualized) {
      return (
        <div
          ref={ref}
          className={cn(oneColTableVariants({ variant, size, state: componentState }), className)}
          role="table"
          aria-busy={loading}
          data-disabled={disabled}
          {...restProps}
        >
          {header && <div className="border-b p-4">{header}</div>}

          <div className="flex flex-col divide-y" role="rowgroup">
            {table.getRowModel().rows.map((row, index) => (
              <motion.div
                key={row.original.id}
                className={cn(
                  rowVariants({ variant, state: hoveredIndex === index ? 'active' : 'default' }),
                )}
                role="row"
                onClick={() => handleRowClick(row.original, index)}
                onMouseEnter={() => handleRowHover(row.original, index)}
                onMouseLeave={handleRowLeave}
                initial={animate ? { opacity: 0, y: 10 } : undefined}
                animate={animate ? { opacity: 1, y: 0 } : undefined}
                transition={{ duration: ANIMATION_CONFIG.duration, delay: index * animationDelay }}
              >
                {row.getVisibleCells().map((cell) => (
                  <React.Fragment key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </React.Fragment>
                ))}
              </motion.div>
            ))}
          </div>

          {showLoadMore && hasMore && (
            <div className="flex items-center justify-center border-t p-2">
              <Button variant="ghost" onClick={handleLoadMore} disabled={disabled || loading}>
                Load more <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      );
    }

    // Virtualized table for large datasets
    return (
      <div
        ref={ref}
        className={cn(oneColTableVariants({ variant, size, state: componentState }), className)}
        role="table"
        aria-busy={loading}
        data-disabled={disabled}
        {...restProps}
      >
        {header && <div className="border-b p-4">{header}</div>}

        <div
          ref={parentRef}
          style={{
            height,
            width,
            overflow: 'auto',
          }}
        >
          <div
            style={{
              height: virtualizer.getTotalSize(),
              width: '100%',
              position: 'relative',
            }}
          >
            {virtualizer.getVirtualItems().map((virtualRow) => {
              const row = table.getRowModel().rows[virtualRow.index];
              if (!row) return null;

              return (
                <div
                  key={row.original.id}
                  className={cn(
                    rowVariants({
                      variant,
                      state: hoveredIndex === virtualRow.index ? 'active' : 'default',
                    }),
                  )}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: virtualRow.size,
                    transform: `translateY(${virtualRow.start}px)`,
                  }}
                  role="row"
                  onClick={() => handleRowClick(row.original, virtualRow.index)}
                  onMouseEnter={() => handleRowHover(row.original, virtualRow.index)}
                  onMouseLeave={handleRowLeave}
                >
                  {row.getVisibleCells().map((cell) => (
                    <React.Fragment key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </React.Fragment>
                  ))}
                </div>
              );
            })}
          </div>
        </div>

        {showLoadMore && hasMore && (
          <div className="flex items-center justify-center border-t p-2">
            <Button variant="ghost" onClick={handleLoadMore} disabled={disabled || loading}>
              Load more <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    );
  },
);

OneColTable.displayName = 'OneColTable';

export default OneColTable;
