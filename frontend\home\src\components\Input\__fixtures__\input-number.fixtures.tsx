import React from 'react';
import { InputNumber } from '../input-number';

// Basic fixture
export const BasicInputNumber = () => (
  <InputNumber 
    id="basic-fixture"
    label="จำนวน"
    defaultValue={3}
  />
);

// With constraints fixture
export const ConstrainedInputNumber = () => (
  <InputNumber 
    id="constrained-fixture"
    label="จำนวน"
    helperText="Value between 0 and 100, step 5"
    min={0}
    max={100}
    step={5}
    defaultValue={50}
  />
);

// No controls fixture
export const NoControlsInputNumber = () => (
  <InputNumber 
    id="no-controls-fixture"
    label="จำนวน"
    defaultValue={42}
  />
);

// Different sizes
export const SmallInputNumber = () => (
  <InputNumber 
    id="small-fixture"
    label="จำนวน (Small)"
    size="sm"
    defaultValue={10}
  />
);

export const LargeInputNumber = () => (
  <InputNumber 
    id="large-fixture"
    label="จำนวน (Large)"
    size="lg"
    defaultValue={10}
  />
);

// Controlled component fixture
export const ControlledInputNumber = () => {
  const [value, setValue] = React.useState<number>(25);
  
  return (
    <div className="space-y-4">
      <InputNumber 
        id="controlled-fixture"
        label="จำนวน (Controlled)"
        value={value}
        onChange={setValue}
      />
      <p className="text-sm">Current value: {value}</p>
    </div>
  );
};