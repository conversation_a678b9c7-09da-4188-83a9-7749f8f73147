import{c as en}from"./chunk-MWVM4TYO-bKUcYSnf.js";import{r as c,cA as Ae,bi as tn,bm as pt,cm as nn,cB as sn,cC as he,aL as rn,j as u,az as et,C as ve,m as q,V as Pe,Y as ln,b as oe,aX as gt,bu as ue,cD as on,B as de,cE as W,cl as an,cc as cn,ck as un,cd as dn,cf as fn,ch as hn,H as mn,ci as pn,bs as tt,cj as gn,I as bn,X as vn,x as wn,T as xn,v as Cn}from"./index-Bwql5Dzz.js";import{i as Te,b as yn,f as _n,e as Sn,c as En}from"./_isIndex-bB1kTSVv.js";import{f as Mn,C as In}from"./index.esm-3G2Z4eQ8.js";import{u as kn,g as jn,f as bt,c as Dn}from"./index-BxZ1678G.js";import{C as On}from"./checkbox-B4pL6X49.js";var zn=Object.defineProperty,me=Object.getOwnPropertySymbols,vt=Object.prototype.hasOwnProperty,wt=Object.prototype.propertyIsEnumerable,nt=(e,n,t)=>n in e?zn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Nn=(e,n)=>{for(var t in n)vt.call(n,t)&&nt(e,t,n[t]);if(me)for(var t of me(n))wt.call(n,t)&&nt(e,t,n[t]);return e},An=(e,n)=>{var t={};for(var s in e)vt.call(e,s)&&n.indexOf(s)<0&&(t[s]=e[s]);if(e!=null&&me)for(var s of me(e))n.indexOf(s)<0&&wt.call(e,s)&&(t[s]=e[s]);return t};const xt=c.forwardRef((e,n)=>{var t=e,{color:s="currentColor"}=t,r=An(t,["color"]);return c.createElement("svg",Nn({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:n},r),c.createElement("g",{clipPath:"url(#a)"},c.createElement("path",{fill:s,d:"M8.5 2.528h-.778a.75.75 0 1 0 0 1.5h1.08a4 4 0 0 1-.302-1.5M10.695 6.07a4 4 0 0 1-1.21-.94.75.75 0 0 0-.29.592V6.75H1.5a.75.75 0 0 0 0 1.5h7.694v1.028a.75.75 0 0 0 1.5 0zM5.806 1.5a.75.75 0 1 0-1.5 0v1.028H1.5a.75.75 0 0 0 0 1.5h2.806v1.028a.75.75 0 0 0 1.5 0V1.5M12.611 6.75a.75.75 0 0 0 0 1.5h.889a.75.75 0 0 0 0-1.5zM6.972 11.722a.75.75 0 0 1 .75-.75H13.5a.75.75 0 1 1 0 1.5H7.722a.75.75 0 0 1-.75-.75M1.5 10.972a.75.75 0 0 0 0 1.5h2.806V13.5a.75.75 0 0 0 1.5 0V9.944a.75.75 0 1 0-1.5 0v1.028z"}),c.createElement("circle",{cx:12.5,cy:2.5,r:2.5,fill:"#60A5FA"}),c.createElement("circle",{cx:12.5,cy:2.5,r:2,stroke:s,strokeOpacity:.12})),c.createElement("defs",null,c.createElement("clipPath",{id:"a"},c.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});xt.displayName="AdjustmentsDone";var Pn=Object.defineProperty,pe=Object.getOwnPropertySymbols,Ct=Object.prototype.hasOwnProperty,yt=Object.prototype.propertyIsEnumerable,st=(e,n,t)=>n in e?Pn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Tn=(e,n)=>{for(var t in n)Ct.call(n,t)&&st(e,t,n[t]);if(pe)for(var t of pe(n))yt.call(n,t)&&st(e,t,n[t]);return e},$n=(e,n)=>{var t={};for(var s in e)Ct.call(e,s)&&n.indexOf(s)<0&&(t[s]=e[s]);if(e!=null&&pe)for(var s of pe(e))n.indexOf(s)<0&&yt.call(e,s)&&(t[s]=e[s]);return t};const _t=c.forwardRef((e,n)=>{var t=e,{color:s="currentColor"}=t,r=$n(t,["color"]);return c.createElement("svg",Tn({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:n},r),c.createElement("g",{stroke:s,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},c.createElement("path",{d:"M12.611 7.5h.889M1.5 7.5h8.444M7.722 3.278H13.5M1.5 3.278h3.556M7.722 11.722H13.5M1.5 11.722h3.556M9.944 5.722v3.556M5.056 1.5v3.556M5.056 9.944V13.5"})),c.createElement("defs",null,c.createElement("clipPath",{id:"a"},c.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});_t.displayName="Adjustments";var Rn=Object.defineProperty,ge=Object.getOwnPropertySymbols,St=Object.prototype.hasOwnProperty,Et=Object.prototype.propertyIsEnumerable,rt=(e,n,t)=>n in e?Rn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Fn=(e,n)=>{for(var t in n)St.call(n,t)&&rt(e,t,n[t]);if(ge)for(var t of ge(n))Et.call(n,t)&&rt(e,t,n[t]);return e},Gn=(e,n)=>{var t={};for(var s in e)St.call(e,s)&&n.indexOf(s)<0&&(t[s]=e[s]);if(e!=null&&ge)for(var s of ge(e))n.indexOf(s)<0&&Et.call(e,s)&&(t[s]=e[s]);return t};const Mt=c.forwardRef((e,n)=>{var t=e,{color:s="currentColor"}=t,r=Gn(t,["color"]);return c.createElement("svg",Fn({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:n},r),c.createElement("path",{fill:s,fillRule:"evenodd",d:"M6.713.838a1.61 1.61 0 0 1 1.574 0l4.521 2.531h.001c.507.286.823.823.823 1.407v.356c0 .89-.722 1.611-1.611 1.611l-9.042.001c-.89 0-1.611-.722-1.611-1.611v-.357c0-.582.314-1.12.823-1.406zm.842 1.309a.11.11 0 0 0-.108 0L2.924 4.678a.11.11 0 0 0-.056.097v.357c0 .**************.111h9.042c.06 0 .11-.05.11-.112v-.356a.11.11 0 0 0-.056-.098zM2.979 7.396a.75.75 0 0 1 .75.75v3.986h1.514V8.146a.75.75 0 0 1 1.5 0v3.986h.41c.414 0 .78.344.78.759 0 .414-.366.741-.78.741H2.117a.75.75 0 0 1 0-1.5h.111V8.146a.75.75 0 0 1 .75-.75M9.338 8.72a.75.75 0 0 1 1.06 0l1.623 1.622 1.622-1.622a.75.75 0 1 1 1.06 1.06l-1.621 1.623 1.622 1.622a.75.75 0 0 1-1.06 1.061l-1.623-1.623-1.623 1.623a.75.75 0 0 1-1.06-1.06l1.622-1.623L9.338 9.78a.75.75 0 0 1 0-1.06",clipRule:"evenodd"}),c.createElement("path",{fill:s,d:"M7.5 4.701a.861.861 0 1 0 0-1.722.861.861 0 0 0 0 1.722"}))});Mt.displayName="TaxExclusive";var Vn=Object.defineProperty,be=Object.getOwnPropertySymbols,It=Object.prototype.hasOwnProperty,kt=Object.prototype.propertyIsEnumerable,lt=(e,n,t)=>n in e?Vn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Kn=(e,n)=>{for(var t in n)It.call(n,t)&&lt(e,t,n[t]);if(be)for(var t of be(n))kt.call(n,t)&&lt(e,t,n[t]);return e},Wn=(e,n)=>{var t={};for(var s in e)It.call(e,s)&&n.indexOf(s)<0&&(t[s]=e[s]);if(e!=null&&be)for(var s of be(e))n.indexOf(s)<0&&kt.call(e,s)&&(t[s]=e[s]);return t};const jt=c.forwardRef((e,n)=>{var t=e,{color:s="currentColor"}=t,r=Wn(t,["color"]);return c.createElement("svg",Kn({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:n},r),c.createElement("path",{fill:s,fillRule:"evenodd",d:"M6.713.838a1.61 1.61 0 0 1 1.574 0l4.521 2.531h.001c.507.286.823.823.823 1.407v.356c0 .89-.722 1.611-1.611 1.611l-9.042.001c-.89 0-1.611-.722-1.611-1.611v-.357c0-.582.314-1.12.824-1.406zm.842 1.309a.11.11 0 0 0-.108 0L2.925 4.678a.11.11 0 0 0-.057.097v.357c0 .**************.111h9.042c.06 0 .11-.05.11-.112v-.356a.11.11 0 0 0-.056-.098zM2.979 7.396a.75.75 0 0 1 .75.75v3.986h1.514V8.146a.75.75 0 0 1 1.5 0v3.986h.346c.414 0 .812.337.812.75 0 .415-.398.75-.812.75H2.118a.75.75 0 0 1 0-1.5h.111V8.146a.75.75 0 0 1 .75-.75m6.028 0a.75.75 0 0 1 .75.75v1.292a.75.75 0 0 1-1.5 0V8.146a.75.75 0 0 1 .75-.75m5.588.733a.75.75 0 0 1 .2 1.042l-3.215 4.736a.75.75 0 0 1-1.16.1l-1.522-1.579a.75.75 0 0 1 1.08-1.041l.88.913 2.695-3.971a.75.75 0 0 1 1.042-.2",clipRule:"evenodd"}),c.createElement("path",{fill:s,d:"M7.5 4.701a.861.861 0 1 0 0-1.722.861.861 0 0 0 0 1.722"}))});jt.displayName="TaxInclusive";var Bn=Te,Hn=Ae,Ln=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Un=/^\w*$/;function Yn(e,n){if(Bn(e))return!1;var t=typeof e;return t=="number"||t=="symbol"||t=="boolean"||e==null||Hn(e)?!0:Un.test(e)||!Ln.test(e)||n!=null&&e in Object(n)}var Zn=Yn,Dt=yn,Xn="Expected a function";function $e(e,n){if(typeof e!="function"||n!=null&&typeof n!="function")throw new TypeError(Xn);var t=function(){var s=arguments,r=n?n.apply(this,s):s[0],l=t.cache;if(l.has(r))return l.get(r);var i=e.apply(this,s);return t.cache=l.set(r,i)||l,i};return t.cache=new($e.Cache||Dt),t}$e.Cache=Dt;var Qn=$e,Jn=Qn,qn=500;function es(e){var n=Jn(e,function(s){return t.size===qn&&t.clear(),s}),t=n.cache;return n}var ts=es,ns=ts,ss=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,rs=/\\(\\)?/g,ls=ns(function(e){var n=[];return e.charCodeAt(0)===46&&n.push(""),e.replace(ss,function(t,s,r,l){n.push(r?l.replace(rs,"$1"):s||t)}),n}),os=ls;function is(e,n){for(var t=-1,s=e==null?0:e.length,r=Array(s);++t<s;)r[t]=n(e[t],t,e);return r}var as=is,ot=tn,cs=as,us=Te,ds=Ae,it=ot?ot.prototype:void 0,at=it?it.toString:void 0;function Ot(e){if(typeof e=="string")return e;if(us(e))return cs(e,Ot)+"";if(ds(e))return at?at.call(e):"";var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}var fs=Ot,hs=fs;function ms(e){return e==null?"":hs(e)}var ps=ms,gs=Te,bs=Zn,vs=os,ws=ps;function xs(e,n){return gs(e)?e:bs(e,n)?[e]:vs(ws(e))}var zt=xs,Cs=Ae;function ys(e){if(typeof e=="string"||Cs(e))return e;var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}var Nt=ys,_s=zt,Ss=Nt;function Es(e,n){n=_s(n,e);for(var t=0,s=n.length;e!=null&&t<s;)e=e[Ss(n[t++])];return t&&t==s?e:void 0}var Ms=Es,Is=Ms;function ks(e,n,t){var s=e==null?void 0:Is(e,n);return s===void 0?t:s}var js=ks;const Ds=pt(js);var Os=_n,zs=function(){try{var e=Os(Object,"defineProperty");return e({},"",{}),e}catch{}}(),Ns=zs,ct=Ns;function As(e,n,t){n=="__proto__"&&ct?ct(e,n,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[n]=t}var Ps=As,Ts=Ps,$s=Sn,Rs=Object.prototype,Fs=Rs.hasOwnProperty;function Gs(e,n,t){var s=e[n];(!(Fs.call(e,n)&&$s(s,t))||t===void 0&&!(n in e))&&Ts(e,n,t)}var Vs=Gs,Ks=Vs,Ws=zt,Bs=En,ut=nn,Hs=Nt;function Ls(e,n,t,s){if(!ut(e))return e;n=Ws(n,e);for(var r=-1,l=n.length,i=l-1,o=e;o!=null&&++r<l;){var a=Hs(n[r]),f=t;if(a==="__proto__"||a==="constructor"||a==="prototype")return e;if(r!=i){var d=o[a];f=s?s(d,a,o):void 0,f===void 0&&(f=ut(d)?d:Bs(n[r+1])?[]:{})}Ks(o,a,f),o=o[a]}return e}var Us=Ls,Ys=Us;function Zs(e,n,t){return e==null?e:Ys(e,n,t)}var Xs=Zs;const At=pt(Xs);var Qs=function(e){var n=e.as,t=e.errors,s=e.name,r=e.message,l=e.render,i=function(h,w){if(h==null)return{};var b,m,v={},_=Object.keys(h);for(m=0;m<_.length;m++)w.indexOf(b=_[m])>=0||(v[b]=h[b]);return v}(e,["as","errors","name","message","render"]),o=sn(),a=he(t||o.formState.errors,s);if(!a)return null;var f=a.message,d=a.types,g=Object.assign({},i,{children:f||r});return c.isValidElement(n)?c.cloneElement(n,g):l?l({message:f||r,messages:d}):c.createElement(n||c.Fragment,g)};function re(e,n,t){let s=t.initialDeps??[],r;return()=>{var l,i,o,a;let f;t.key&&((l=t.debug)!=null&&l.call(t))&&(f=Date.now());const d=e();if(!(d.length!==s.length||d.some((w,b)=>s[b]!==w)))return r;s=d;let h;if(t.key&&((i=t.debug)!=null&&i.call(t))&&(h=Date.now()),r=n(...d),t.key&&((o=t.debug)!=null&&o.call(t))){const w=Math.round((Date.now()-f)*100)/100,b=Math.round((Date.now()-h)*100)/100,m=b/16,v=(_,S)=>{for(_=String(_);_.length<S;)_=" "+_;return _};console.info(`%c⏱ ${v(b,5)} /${v(w,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*m,120))}deg 100% 31%);`,t==null?void 0:t.key)}return(a=t==null?void 0:t.onChange)==null||a.call(t,r),r}}function Oe(e,n){if(e===void 0)throw new Error("Unexpected undefined");return e}const Js=(e,n)=>Math.abs(e-n)<1,qs=(e,n,t)=>{let s;return function(...r){e.clearTimeout(s),s=e.setTimeout(()=>n.apply(this,r),t)}},er=e=>e,tr=e=>{const n=Math.max(e.startIndex-e.overscan,0),t=Math.min(e.endIndex+e.overscan,e.count-1),s=[];for(let r=n;r<=t;r++)s.push(r);return s},nr=(e,n)=>{const t=e.scrollElement;if(!t)return;const s=e.targetWindow;if(!s)return;const r=i=>{const{width:o,height:a}=i;n({width:Math.round(o),height:Math.round(a)})};if(r(t.getBoundingClientRect()),!s.ResizeObserver)return()=>{};const l=new s.ResizeObserver(i=>{const o=i[0];if(o!=null&&o.borderBoxSize){const a=o.borderBoxSize[0];if(a){r({width:a.inlineSize,height:a.blockSize});return}}r(t.getBoundingClientRect())});return l.observe(t,{box:"border-box"}),()=>{l.unobserve(t)}},dt={passive:!0},sr=typeof window>"u"?!0:"onscrollend"in window,rr=(e,n)=>{const t=e.scrollElement;if(!t)return;const s=e.targetWindow;if(!s)return;let r=0;const l=e.options.useScrollendEvent&&sr?()=>{}:qs(s,()=>{n(r,!1)},e.options.isScrollingResetDelay),i=f=>()=>{const{horizontal:d,isRtl:g}=e.options;r=d?t.scrollLeft*(g&&-1||1):t.scrollTop,l(),n(r,f)},o=i(!0),a=i(!1);return a(),t.addEventListener("scroll",o,dt),t.addEventListener("scrollend",a,dt),()=>{t.removeEventListener("scroll",o),t.removeEventListener("scrollend",a)}},lr=(e,n,t)=>{if(n!=null&&n.borderBoxSize){const s=n.borderBoxSize[0];if(s)return Math.round(s[t.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[t.options.horizontal?"width":"height"])},or=(e,{adjustments:n=0,behavior:t},s)=>{var r,l;const i=e+n;(l=(r=s.scrollElement)==null?void 0:r.scrollTo)==null||l.call(r,{[s.options.horizontal?"left":"top"]:i,behavior:t})};class ir{constructor(n){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let t=null;const s=()=>t||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:t=new this.targetWindow.ResizeObserver(r=>{r.forEach(l=>{this._measureElement(l.target,l)})}));return{disconnect:()=>{var r;(r=s())==null||r.disconnect(),t=null},observe:r=>{var l;return(l=s())==null?void 0:l.observe(r,{box:"border-box"})},unobserve:r=>{var l;return(l=s())==null?void 0:l.unobserve(r)}}})(),this.range=null,this.setOptions=t=>{Object.entries(t).forEach(([s,r])=>{typeof r>"u"&&delete t[s]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:er,rangeExtractor:tr,onChange:()=>{},measureElement:lr,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!0,...t}},this.notify=t=>{var s,r;(r=(s=this.options).onChange)==null||r.call(s,this,t)},this.maybeNotify=re(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),t=>{this.notify(t)},{key:"maybeNotify",debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(t=>t()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var t;const s=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==s){if(this.cleanup(),!s){this.maybeNotify();return}this.scrollElement=s,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=((t=this.scrollElement)==null?void 0:t.window)??null,this.elementsCache.forEach(r=>{this.observer.observe(r)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,r=>{this.scrollRect=r,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(r,l)=>{this.scrollAdjustments=0,this.scrollDirection=l?this.getScrollOffset()<r?"forward":"backward":null,this.scrollOffset=r,this.isScrolling=l,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??(typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(t,s)=>{const r=new Map,l=new Map;for(let i=s-1;i>=0;i--){const o=t[i];if(r.has(o.lane))continue;const a=l.get(o.lane);if(a==null||o.end>a.end?l.set(o.lane,o):o.end<a.end&&r.set(o.lane,!0),r.size===this.options.lanes)break}return l.size===this.options.lanes?Array.from(l.values()).sort((i,o)=>i.end===o.end?i.index-o.index:i.end-o.end)[0]:void 0},this.getMeasurementOptions=re(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(t,s,r,l,i)=>(this.pendingMeasuredCacheIndexes=[],{count:t,paddingStart:s,scrollMargin:r,getItemKey:l,enabled:i}),{key:!1}),this.getMeasurements=re(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:t,paddingStart:s,scrollMargin:r,getItemKey:l,enabled:i},o)=>{if(!i)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(d=>{this.itemSizeCache.set(d.key,d.size)}));const a=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const f=this.measurementsCache.slice(0,a);for(let d=a;d<t;d++){const g=l(d),h=this.options.lanes===1?f[d-1]:this.getFurthestMeasurement(f,d),w=h?h.end+this.options.gap:s+r,b=o.get(g),m=typeof b=="number"?b:this.options.estimateSize(d),v=w+m,_=h?h.lane:d%this.options.lanes;f[d]={index:d,start:w,size:m,end:v,key:g,lane:_}}return this.measurementsCache=f,f},{key:"getMeasurements",debug:()=>this.options.debug}),this.calculateRange=re(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset()],(t,s,r)=>this.range=t.length>0&&s>0?ar({measurements:t,outerSize:s,scrollOffset:r}):null,{key:"calculateRange",debug:()=>this.options.debug}),this.getIndexes=re(()=>[this.options.rangeExtractor,this.calculateRange(),this.options.overscan,this.options.count],(t,s,r,l)=>s===null?[]:t({startIndex:s.startIndex,endIndex:s.endIndex,overscan:r,count:l}),{key:"getIndexes",debug:()=>this.options.debug}),this.indexFromElement=t=>{const s=this.options.indexAttribute,r=t.getAttribute(s);return r?parseInt(r,10):(console.warn(`Missing attribute name '${s}={index}' on measured element.`),-1)},this._measureElement=(t,s)=>{const r=this.indexFromElement(t),l=this.measurementsCache[r];if(!l)return;const i=l.key,o=this.elementsCache.get(i);o!==t&&(o&&this.observer.unobserve(o),this.observer.observe(t),this.elementsCache.set(i,t)),t.isConnected&&this.resizeItem(r,this.options.measureElement(t,s,this))},this.resizeItem=(t,s)=>{const r=this.measurementsCache[t];if(!r)return;const l=this.itemSizeCache.get(r.key)??r.size,i=s-l;i!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(r,i,this):r.start<this.getScrollOffset()+this.scrollAdjustments)&&(this.options.debug&&console.info("correction",i),this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0})),this.pendingMeasuredCacheIndexes.push(r.index),this.itemSizeCache=new Map(this.itemSizeCache.set(r.key,s)),this.notify(!1))},this.measureElement=t=>{if(!t){this.elementsCache.forEach((s,r)=>{s.isConnected||(this.observer.unobserve(s),this.elementsCache.delete(r))});return}this._measureElement(t,void 0)},this.getVirtualItems=re(()=>[this.getIndexes(),this.getMeasurements()],(t,s)=>{const r=[];for(let l=0,i=t.length;l<i;l++){const o=t[l],a=s[o];r.push(a)}return r},{key:"getVirtualItems",debug:()=>this.options.debug}),this.getVirtualItemForOffset=t=>{const s=this.getMeasurements();if(s.length!==0)return Oe(s[Pt(0,s.length-1,r=>Oe(s[r]).start,t)])},this.getOffsetForAlignment=(t,s)=>{const r=this.getSize(),l=this.getScrollOffset();s==="auto"&&t>=l+r&&(s="end"),s==="end"&&(t-=r);const i=this.options.horizontal?"scrollWidth":"scrollHeight",a=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[i]:this.scrollElement[i]:0)-r;return Math.max(Math.min(a,t),0)},this.getOffsetForIndex=(t,s="auto")=>{t=Math.max(0,Math.min(t,this.options.count-1));const r=this.measurementsCache[t];if(!r)return;const l=this.getSize(),i=this.getScrollOffset();if(s==="auto")if(r.end>=i+l-this.options.scrollPaddingEnd)s="end";else if(r.start<=i+this.options.scrollPaddingStart)s="start";else return[i,s];const o=r.start-this.options.scrollPaddingStart+(r.size-l)/2;switch(s){case"center":return[this.getOffsetForAlignment(o,s),s];case"end":return[this.getOffsetForAlignment(r.end+this.options.scrollPaddingEnd,s),s];default:return[this.getOffsetForAlignment(r.start-this.options.scrollPaddingStart,s),s]}},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(t,{align:s="start",behavior:r}={})=>{this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(t,s),{adjustments:void 0,behavior:r})},this.scrollToIndex=(t,{align:s="auto",behavior:r}={})=>{t=Math.max(0,Math.min(t,this.options.count-1)),this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const l=this.getOffsetForIndex(t,s);if(!l)return;const[i,o]=l;this._scrollToOffset(i,{adjustments:void 0,behavior:r}),r!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(t))){const[f]=Oe(this.getOffsetForIndex(t,o));Js(f,this.getScrollOffset())||this.scrollToIndex(t,{align:o,behavior:r})}else this.scrollToIndex(t,{align:o,behavior:r})}))},this.scrollBy=(t,{behavior:s}={})=>{this.cancelScrollToIndex(),s==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+t,{adjustments:void 0,behavior:s})},this.getTotalSize=()=>{var t;const s=this.getMeasurements();let r;return s.length===0?r=this.options.paddingStart:r=this.options.lanes===1?((t=s[s.length-1])==null?void 0:t.end)??0:Math.max(...s.slice(-this.options.lanes).map(l=>l.end)),Math.max(r-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(t,{adjustments:s,behavior:r})=>{this.options.scrollToFn(t,{behavior:r,adjustments:s},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(n)}}const Pt=(e,n,t,s)=>{for(;e<=n;){const r=(e+n)/2|0,l=t(r);if(l<s)e=r+1;else if(l>s)n=r-1;else return r}return e>0?e-1:0};function ar({measurements:e,outerSize:n,scrollOffset:t}){const s=e.length-1,l=Pt(0,s,o=>e[o].start,t);let i=l;for(;i<s&&e[i].end<t+n;)i++;return{startIndex:l,endIndex:i}}const ft=typeof document<"u"?c.useLayoutEffect:c.useEffect;function cr(e){const n=c.useReducer(()=>({}),{})[1],t={...e,onChange:(r,l)=>{var i;l?rn.flushSync(n):n(),(i=e.onChange)==null||i.call(e,r,l)}},[s]=c.useState(()=>new ir(t));return s.setOptions(t),ft(()=>s._didMount(),[]),ft(()=>s._willUpdate()),s}function ht(e){return cr({observeElementRect:nr,observeElementOffset:rr,scrollToFn:or,...e})}var ur=({columns:e,rows:n=10})=>{const t=Array.from({length:n},(r,l)=>l),s=e.length;return u.jsxs("div",{className:"bg-ui-bg-subtle size-full",children:[u.jsx("div",{className:"bg-ui-bg-base border-b p-4",children:u.jsx("div",{className:"bg-ui-button-neutral h-7 w-[116px] animate-pulse rounded-md"})}),u.jsxs("div",{className:"bg-ui-bg-subtle size-full overflow-auto",children:[u.jsx("div",{className:"grid",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:e.map((r,l)=>u.jsx("div",{className:"bg-ui-bg-base flex h-10 w-[200px] items-center border-b border-r px-4 py-2.5 last:border-r-0",children:u.jsx(et,{className:"h-[14px] w-[164px]"})},l))}),u.jsx("div",{children:t.map((r,l)=>u.jsx("div",{className:"grid",style:{gridTemplateColumns:`repeat(${s}, 1fr)`},children:e.map((i,o)=>u.jsx("div",{className:"bg-ui-bg-base flex h-10 w-[200px] items-center border-b border-r px-4 py-2.5 last:border-r-0",children:u.jsx(et,{className:"h-[14px] w-[164px]"})},o))},l))})]})]})};function dr(e,n){typeof e=="function"?e(n):e&&"current"in e&&(e.current=n)}var we=(...e)=>n=>{e.forEach(t=>dr(t,n))},Tt=c.createContext(null),Re=()=>{const e=c.useContext(Tt);if(!e)throw new Error("useDataGridContext must be used within a DataGridContextProvider");return e};function $t(e){return`${e.row}:${e.col}`}function Rt(e,n){return n?e.row===n.row&&e.col===n.col:!1}var fr=[".",","];function Ft(e){return fr.includes(e.key)&&e.ctrlKey&&e.altKey}var hr=/^.$/u,mr=/^[0-9]$/u,xe=({context:e})=>{const{register:n,control:t,anchor:s,setIsEditing:r,setSingleRange:l,setIsSelecting:i,setRangeEnd:o,getWrapperFocusHandler:a,getWrapperMouseOverHandler:f,getInputChangeHandler:d,getIsCellSelected:g,getIsCellDragSelected:h,getCellMetadata:w}=Re(),{rowIndex:b,columnIndex:m}=e,v=c.useMemo(()=>({row:b,col:m}),[b,m]),{id:_,field:S,type:x,innerAttributes:D,inputAttributes:y}=c.useMemo(()=>w(v),[v,w]),[O,F]=c.useState(!0),R=c.useRef(null),T=c.useRef(null),U=c.useCallback(E=>{if(E.preventDefault(),E.stopPropagation(),E.detail===2&&T.current){F(!1),T.current.focus();return}if(E.shiftKey&&v.col===(s==null?void 0:s.col)){o(v);return}R.current&&(l(v),i(!0),R.current.focus())},[v,s,o,l,i]),Y=c.useCallback(E=>{var $;if(E.preventDefault(),E.stopPropagation(),E.detail===2){($=T.current)==null||$.focus();return}if(E.shiftKey){o(v);return}R.current&&(l(v),i(!0),R.current.focus())},[i,l,o,v]),ee=c.useCallback(()=>{F(!0),r(!1)},[r]),te=c.useCallback(()=>{F(!1),r(!0)},[r]),ne=c.useCallback(E=>{switch(x){case"togglable-number":case"number":return mr.test(E);case"text":return hr.test(E);default:return!1}},[x]),Z=c.useCallback(E=>{var $;if(!(!T.current||!ne(E.key)||!O)&&!(E.key.toLowerCase()==="z"&&(E.ctrlKey||E.metaKey))&&!(E.key.toLowerCase()==="c"&&(E.ctrlKey||E.metaKey))&&!(E.key.toLowerCase()==="v"&&(E.ctrlKey||E.metaKey))&&E.key!=="Enter"&&!Ft(E.nativeEvent)){if(T.current.focus(),F(!1),T.current instanceof HTMLInputElement){T.current.value="";const X=($=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value"))==null?void 0:$.set;X==null||X.call(T.current,E.key);const Q=new Event("input",{bubbles:!0});T.current.dispatchEvent(Q)}E.stopPropagation(),E.preventDefault()}},[O,ne]),H=c.useMemo(()=>s?Rt(v,s):!1,[s,v]),B=c.useMemo(()=>x==="boolean",[x]);c.useEffect(()=>{var E,$;H&&!((E=R.current)!=null&&E.contains(document.activeElement))&&(($=R.current)==null||$.focus())},[H]);const se={container:{field:S,isAnchor:H,isSelected:g(v),isDragSelected:h(v),showOverlay:B?!1:O,innerProps:{ref:R,onMouseOver:f(v),onMouseDown:x==="boolean"?Y:void 0,onKeyDown:Z,onFocus:a(v),...D},overlayProps:{onMouseDown:U}},input:{ref:T,onBlur:ee,onFocus:te,onChange:d(S),...y}};return{id:_,field:S,register:n,control:t,renderProps:se}},ie=({context:e})=>{const{errors:n,getCellErrorMetadata:t,navigateToField:s}=Re(),{rowIndex:r,columnIndex:l}=e,{accessor:i,field:o}=c.useMemo(()=>t({row:r,col:l}),[r,l,t]),a=i&&l===0?he(n,i):void 0,f=[];function d(h,w){if(h)if(pr(h)){const b=h.message,m=()=>s(w);b&&f.push({message:b,to:m})}else Object.keys(h).forEach(b=>{const m=h[b],v=`${w}.${b}`;m&&typeof m=="object"&&d(m,v)})}a&&i&&d(a,i);const g=o?he(n,o):void 0;return{errors:n,rowErrors:f,cellError:g}};function pr(e){return typeof e=="object"&&"message"in e&&"type"in e}var le=class{constructor({fields:e,prev:n,next:t,setter:s}){W(this,"_fields"),W(this,"_prev"),W(this,"_next"),W(this,"_setter"),this._fields=e,this._prev=n,this._next=t,this._setter=s}execute(e=!1){this._setter(this._fields,this._next,e)}undo(){this._setter(this._fields,this._prev,!0)}redo(){this.execute(!0)}},gr=class{constructor(e,n,t=!1){W(this,"multiColumnSelection"),W(this,"cells"),W(this,"rowAccessors",[]),W(this,"columnAccessors",[]),this.multiColumnSelection=t,this.cells=this._populateCells(e,n),this.rowAccessors=this._computeRowAccessors(),this.columnAccessors=this._computeColumnAccessors()}_computeRowAccessors(){return this.cells.map((e,n)=>this.getRowAccessor(n))}_computeColumnAccessors(){return this.cells.length===0?[]:this.cells[0].map((e,n)=>this.getColumnAccessor(n))}getFirstNavigableCell(){for(let e=0;e<this.cells.length;e++)for(let n=0;n<this.cells[0].length;n++)if(this.cells[e][n]!==null)return{row:e,col:n};return null}getFieldsInRow(e){const n=[];return e<0||e>=this.cells.length||this.cells[e].forEach(t=>{t!==null&&n.push(t.field)}),n}getFieldsInSelection(e,n){var o;const t=[];if(!e||!n)return t;if(!this.multiColumnSelection&&e.col!==n.col)throw new Error("Selection must be in the same column when multiColumnSelection is disabled");const s=Math.min(e.row,n.row),r=Math.max(e.row,n.row),l=this.multiColumnSelection?Math.min(e.col,n.col):e.col,i=this.multiColumnSelection?Math.max(e.col,n.col):e.col;for(let a=s;a<=r;a++)for(let f=l;f<=i;f++)this._isValidPosition(a,f)&&this.cells[a][f]!==null&&t.push((o=this.cells[a][f])==null?void 0:o.field);return t}getCellField(e){var n;return this._isValidPosition(e.row,e.col)&&((n=this.cells[e.row][e.col])==null?void 0:n.field)||null}getCellType(e){var n;return this._isValidPosition(e.row,e.col)&&((n=this.cells[e.row][e.col])==null?void 0:n.type)||null}getIsCellSelected(e,n,t){if(!e||!n||!t)return!1;if(!this.multiColumnSelection&&n.col!==t.col)throw new Error("Selection must be in the same column when multiColumnSelection is disabled");const s=Math.min(n.row,t.row),r=Math.max(n.row,t.row),l=this.multiColumnSelection?Math.min(n.col,t.col):n.col,i=this.multiColumnSelection?Math.max(n.col,t.col):n.col;return e.row>=s&&e.row<=r&&e.col>=l&&e.col<=i}toggleColumn(e,n){e<0||e>=this.cells[0].length||this.cells.forEach((t,s)=>{const r=t[e];r&&(this.cells[s][e]={...r,enabled:n})})}toggleRow(e,n){e<0||e>=this.cells.length||this.cells[e].forEach((t,s)=>{t&&(this.cells[e][s]={...t,enabled:n})})}getCoordinatesByField(e){if(this.rowAccessors.length===1){const n=this.columnAccessors.indexOf(e);return n===-1?null:{row:0,col:n}}for(let n=0;n<this.rowAccessors.length;n++){const t=this.rowAccessors[n];if(t!==null&&e.startsWith(t))for(let s=0;s<this.columnAccessors.length;s++){const r=this.columnAccessors[s];if(r===null)continue;if(`${t}.${r}`===e)return{row:n,col:s}}}return null}getRowAccessor(e){if(e<0||e>=this.cells.length)return null;const t=this.cells[e].filter(l=>l!==null).map(l=>l.field.split("."));if(t.length===0)return null;let s=t[0];for(const l of t)if(s=s.filter((i,o)=>l[o]===i),s.length===0)break;const r=s.join(".");return r||null}getColumnAccessor(e){if(e<0||e>=this.cells[0].length)return null;const n=this.cells.map((r,l)=>{const i=r[e];if(!i)return null;const o=this.getRowAccessor(l);return o&&i.field.startsWith(o+".")?i.field.slice(o.length+1):null}).filter(r=>r!==null);if(n.length===0)return null;const t=n[0];return n.every(r=>r===t)?t:null}getValidMovement(e,n,t,s=!1){var i;const[r,l]=this._getDirectionDeltas(t);if(s)return this._getLastValidCellInDirection(e,n,r,l);{let o=e+r,a=n+l;for(;this._isValidPosition(o,a);){if(this.cells[o][a]!==null&&((i=this.cells[o][a])==null?void 0:i.enabled)!==!1)return{row:o,col:a};o+=r,a+=l}return{row:e,col:n}}}_isValidPosition(e,n,t){return t||(t=this.cells),e>=0&&e<t.length&&n>=0&&n<t[0].length}_getDirectionDeltas(e){switch(e){case"ArrowUp":return[-1,0];case"ArrowDown":return[1,0];case"ArrowLeft":return[0,-1];case"ArrowRight":return[0,1];default:return[0,0]}}_getLastValidCellInDirection(e,n,t,s){let r=e,l=n,i=e,o=n;for(;this._isValidPosition(r+t,l+s);)r+=t,l+=s,this.cells[r][l]!==null&&(i=r,o=l);return{row:i,col:o}}_populateCells(e,n){const t=Array.from({length:e.length},()=>Array(n.length).fill(null));return e.forEach((s,r)=>{n.forEach((l,i)=>{if(!this._isValidPosition(r,i,t))return;const{name:o,field:a,type:f,...d}=l.meta,g={row:s,column:{...l,meta:d}},h=a?a(g):null;!h||!f||(t[r][i]={field:h,type:f,enabled:!0})})}),t}},br=class{constructor(e){W(this,"container"),this.container=e}getInput(e){var s;const n=this._getCellId(e),t=(s=this.container)==null?void 0:s.querySelector(`[data-cell-id="${n}"]`);return t||null}getInputByField(e){var t;const n=(t=this.container)==null?void 0:t.querySelector(`[data-field="${e}"]`);return n||null}getCoordinatesByField(e){var l;const n=(l=this.container)==null?void 0:l.querySelector(`[data-field="${e}"][data-cell-id]`);if(!n)return null;const t=n.getAttribute("data-cell-id");if(!t)return null;const[s,r]=t.split(":").map(i=>parseInt(i,10));return isNaN(s)||isNaN(r)?null:{row:s,col:r}}getContainer(e){var s;const n=this._getCellId(e),t=(s=this.container)==null?void 0:s.querySelector(`[data-container-id="${n}"]`);return t||null}_getCellId(e){return $t(e)}},fe=class{constructor({prev:e,next:n,setter:t}){W(this,"_prev"),W(this,"_next"),W(this,"_setter"),this._prev=e,this._next=n,this._setter=t}execute(){this._setter(this._next)}undo(){this._setter(this._prev)}redo(){this.execute()}},vr=({matrix:e,anchor:n,rangeEnd:t,setRangeEnd:s,isDragging:r,setIsDragging:l,isSelecting:i,setIsSelecting:o,setSingleRange:a,dragEnd:f,setDragEnd:d,setValue:g,execute:h,multiColumnSelection:w})=>{const b=c.useCallback(y=>O=>{a(y)},[a]),m=c.useCallback(y=>O=>{if(O.stopPropagation(),O.preventDefault(),O.shiftKey){s(y);return}o(!0),a(y)},[o,s,a]),v=c.useCallback(y=>{if(!(!r&&!i))return O=>{(n==null?void 0:n.col)!==y.col&&!w||(i?s(y):d(y))}},[n==null?void 0:n.col,r,i,d,s,w]),_=c.useCallback(y=>(O,F)=>{const R=new fe({next:O,prev:F,setter:T=>{g(y,T,{shouldDirty:!0,shouldTouch:!0})}});h(R)},[g,h]),S=c.useCallback(y=>{l(!0)},[l]),x=c.useCallback(y=>!y||!n||!t?!1:e.getIsCellSelected(y,n,t),[n,t,e]),D=c.useCallback(y=>!y||!n||!f?!1:e.getIsCellSelected(y,n,f),[n,f,e]);return{getWrapperFocusHandler:b,getOverlayMouseDownHandler:m,getWrapperMouseOverHandler:v,getInputChangeHandler:_,getIsCellSelected:x,getIsCellDragSelected:D,onDragToFillStart:S}},wr=({matrix:e})=>{const n=c.useCallback(s=>{const{row:r,col:l}=s,i=$t(s),o=e.getCellField(s),a=e.getCellType(s);if(!o||!a)throw new Error(`'field' or 'type' is null for cell ${i}`);return{id:i,field:o,type:a,inputAttributes:{"data-row":r,"data-col":l,"data-cell-id":i,"data-field":o},innerAttributes:{"data-container-id":i}}},[e]),t=c.useCallback(s=>{const r=e.getRowAccessor(s.row),l=e.getCellField(s);return{accessor:r,field:l}},[e]);return{getCellMetadata:n,getCellErrorMetadata:t}},xr=({matrix:e,form:n})=>{const[t,s]=c.useState(null),{getValues:r,setValue:l}=n,i=c.useCallback(a=>{if(!a)return null;const f=e.getCellField(a);if(!f)return null;const d=r(f);s(g=>(g==null?void 0:g.field)===f?g:{field:f,value:d})},[r,e]),o=c.useCallback(()=>{if(!t)return;const{field:a,value:f}=t;requestAnimationFrame(()=>{l(a,f)})},[l,t]);return{createSnapshot:i,restoreSnapshot:o}},Cr=({matrix:e,anchor:n,rangeEnd:t,isEditing:s,getSelectionValues:r,setSelectionValues:l,execute:i})=>{const o=c.useCallback(f=>{var w;if(s||!n||!t)return;f.preventDefault();const d=e.getFieldsInSelection(n,t),h=r(d).map(b=>typeof b=="object"&&b!==null?JSON.stringify(b):`${b}`??"").join("	");(w=f.clipboardData)==null||w.setData("text/plain",h)},[s,n,t,e,r]),a=c.useCallback(f=>{var m;if(s||!n||!t)return;f.preventDefault();const d=(m=f.clipboardData)==null?void 0:m.getData("text/plain");if(!d)return;const g=d.split("	"),h=e.getFieldsInSelection(n,t),w=r(h),b=new le({fields:h,next:g,prev:w,setter:l});i(b)},[s,n,t,e,r,l,i]);return{handleCopyEvent:o,handlePasteEvent:a}};function yr(e,n){const t=e.getAllLeafColumns(),s=t.map(a=>({id:a.id,name:_r(a),checked:a.getIsVisible(),disabled:!a.getCanHide()})),r=c.useCallback(a=>f=>{const d=t[a];d.getCanHide()&&(n.toggleColumn(a,f),d.toggleVisibility(f))},[t,n]),l=c.useCallback(()=>{e.setColumnVisibility({})},[e]),o=s.filter(a=>!a.disabled).length===0;return{columnOptions:s,handleToggleColumn:r,handleResetColumns:l,isDisabled:o}}function _r(e){const n=e.columnDef.id,t=e.columnDef.enableHiding,s=e==null?void 0:e.columnDef.meta;if(!n)throw new Error("Column is missing an id, which is a required field. Please provide an id for the column.");return!(s!=null&&s.name)&&t&&console.warn(`Column "${n}" does not have a name. You should add a name to the column definition. Falling back to the column id.`),(s==null?void 0:s.name)||n}var dl=({duplicateOf:e})=>{const{control:n}=Re();return{watchedValue:Cn({control:n,name:e})}},Sr=(e,n,t)=>{const[s,r]=c.useState(!1),[l,i]=c.useState(null),{flatRows:o}=n.getRowModel(),a=n.getAllFlatColumns(),f=Gt(t),d=f.length,{rowsWithErrors:g,columnsWithErrors:h}=c.useMemo(()=>{const b=new Set,m=new Set;return f.forEach(v=>{const _=e.rowAccessors.findIndex(x=>x&&(v===x||v.startsWith(`${x}.`)));_!==-1&&b.add(_);const S=e.columnAccessors.findIndex(x=>x&&(v===x||v.endsWith(`.${x}`)));S!==-1&&m.add(S)}),{rowsWithErrors:b,columnsWithErrors:m}},[f,e.rowAccessors,e.columnAccessors]),w=c.useCallback((b,m,v,_)=>{if(s)l&&(v(l.rows),_(l.columns));else{i({rows:{...b},columns:{...m}});const S=o.map((D,y)=>g.has(y)?void 0:y).filter(D=>D!==void 0),x=a.map((D,y)=>!h.has(y)&&y!==0?D.id:void 0).filter(D=>D!==void 0);v(S.reduce((D,y)=>({...D,[y]:!1}),{})),_(x.reduce((D,y)=>({...D,[y]:!1}),{}))}r(S=>!S)},[s,l,o,a,g,h]);return{errorCount:d,isHighlighted:s,toggleErrorHighlighting:w}};function Gt(e,n=[]){return typeof e!="object"||e===null?[]:"message"in e&&"type"in e?[n.join(".")]:Object.entries(e).flatMap(([t,s])=>Gt(s,[...n,t]))}var Er=({matrix:e,form:n,anchor:t})=>{const{getValues:s,reset:r}=n,l=c.useCallback(o=>{if(!o.length)return[];const a=s();return o.map(f=>f.split(".").reduce((d,g)=>d==null?void 0:d[g],a))},[s]),i=c.useCallback(async(o,a,f)=>{if(!o.length||!t)return;const d=e.getCellType(t);if(!d)return;const g=zr(a,d),h=s();o.forEach((w,b)=>{if(!w)return;const m=b%a.length,v=g[m];Dr(h,w,v,d,f)}),r(h,{keepDirty:!0,keepTouched:!0,keepDefaultValues:!0})},[e,t,s,r]);return{getSelectionValues:l,setSelectionValues:i}};function Mr(e){if(typeof e=="number")return e;const n=Number(e);if(isNaN(n))throw new Error(`String "${e}" cannot be converted to number.`);return n}function Ir(e){if(typeof e=="boolean")return e;if(typeof e>"u"||e===null)return!1;const n=e.toLowerCase();if(n==="true"||n==="false")return n==="true";throw new Error(`String "${e}" cannot be converted to boolean.`)}function kr(e){return typeof e>"u"||e===null?"":String(e)}function jr(e){let n=e;if(typeof n=="string")try{n=JSON.parse(n)}catch{throw new Error(`String "${e}" cannot be converted to object.`)}return n}function Dr(e,n,t,s,r){if(s!=="togglable-number"){At(e,n,t);return}Or(e,n,t,r)}function Or(e,n,t,s){const r=Ds(e,n),{disabledToggle:l}=r,i=d=>l&&d===""?0:d,o=d=>l?!0:d!==""&&d!=null,a=i(t.quantity),f=s?l?!0:t.checked:o(a);At(e,n,{...r,quantity:a,checked:f})}function zr(e,n){switch(n){case"number":return e.map(t=>t===""?t:t==null?"":Mr(t));case"togglable-number":return e.map(jr);case"boolean":return e.map(Ir);case"text":return e.map(kr);default:throw new Error(`Unsupported target type "${n}".`)}}var Nr=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],Ar=["ArrowUp","ArrowDown"],Pr=({containerRef:e,matrix:n,anchor:t,rangeEnd:s,isEditing:r,setTrapActive:l,scrollToCoordinates:i,setSingleRange:o,setRangeEnd:a,onEditingChangeHandler:f,getValues:d,setValue:g,execute:h,undo:w,redo:b,queryTool:m,getSelectionValues:v,setSelectionValues:_,restoreSnapshot:S,createSnapshot:x})=>{const D=c.useCallback(p=>{if(!t)return;const C=n.getCellType(t);if(r&&C!=="boolean")return;const k=Ar.includes(p.key)?"vertical":"horizontal",M=k==="horizontal"?t:p.shiftKey?s:t,j=k==="horizontal"?o:p.shiftKey?a:o;if(!M)return;const{row:z,col:P}=M,N=G=>{p.preventDefault(),p.stopPropagation(),i(G,k),j(G)},J=n.getValidMovement(z,P,p.key,p.metaKey||p.ctrlKey);N(J)},[r,t,s,i,o,a,n]),y=c.useCallback(p=>{if(!t)return;p.preventDefault(),p.stopPropagation();const{row:C,col:k}=t,M=p.shiftKey?"ArrowLeft":"ArrowRight",j="horizontal",z=n.getValidMovement(C,k,M,p.metaKey||p.ctrlKey);i(z,j),o(z)},[t,i,o,n]),O=c.useCallback(p=>{if(p.preventDefault(),p.shiftKey){b();return}w()},[b,w]),F=c.useCallback(p=>{const C=s??p,k=n.getFieldsInSelection(p,C),M=v(k),j=M.every(N=>N===!0),z=Array.from({length:M.length},()=>!j),P=new le({fields:k,next:z,prev:M,setter:_});h(P)},[s,n,v,_,h]),R=c.useCallback(p=>{const C=n.getCellField(p),k=m==null?void 0:m.getInput(p);if(!C||!k)return;x(p);const M=d(C),j="",z=new fe({next:j,prev:M,setter:P=>{g(C,P,{shouldDirty:!0,shouldTouch:!0})}});h(z),k.focus()},[n,m,d,h,g,x]),T=c.useCallback(p=>{const C=n.getCellField(p),k=m==null?void 0:m.getInput(p);if(!C||!k)return;x(p);const M=d(C);let j=M.checked;M.disabledToggle||(j=!1);const z={...M,quantity:"",checked:j},P=new fe({next:z,prev:M,setter:N=>{g(C,N,{shouldDirty:!0,shouldTouch:!0})}});h(P),k.focus()},[n,m,d,h,g,x]),U=c.useCallback(p=>{if(!t||r)return;p.preventDefault();const C=n.getCellType(t);if(C)switch(C){case"boolean":F(t);break;case"togglable-number":T(t);break;case"number":case"text":R(t);break}},[t,r,n,F,R,T]),Y=c.useCallback((p,C)=>{const k=p.shiftKey?"ArrowUp":"ArrowDown",M=n.getValidMovement(C.row,C.col,k,!1);if(C.row!==M.row||C.col!==M.col)o(M),i(M,"vertical");else{const j=m==null?void 0:m.getContainer(C);j==null||j.focus()}f(!1)},[m,n,i,o,f]),ee=c.useCallback(p=>{const C=m==null?void 0:m.getInput(p);C&&(C.focus(),f(!0))},[m,f]),te=c.useCallback((p,C)=>{if(r){Y(p,C);return}ee(C)},[Y,ee,r]),ne=c.useCallback((p,C)=>{const k=n.getCellField(C);if(!k)return;const M=d(k);let j;typeof M=="boolean"?j=!M:j=!0;const z=new fe({next:j,prev:M,setter:P=>{g(k,P,{shouldDirty:!0,shouldTouch:!0})}});h(z),Y(p,C)},[h,d,Y,n,g]),Z=c.useCallback(p=>{if(!t)return;switch(p.preventDefault(),n.getCellType(t)){case"togglable-number":case"text":case"number":te(p,t);break;case"boolean":{ne(p,t);break}}},[t,n,te,ne]),H=c.useCallback((p,C)=>{const k=n.getFieldsInSelection(p,C),M=v(k),j=M.map(P=>({...P,quantity:"",checked:P.disableToggle?P.checked:!1})),z=new le({fields:k,next:j,prev:M,setter:_});h(z)},[n,v,_,h]),B=c.useCallback((p,C)=>{const k=n.getFieldsInSelection(p,C),M=v(k),j=Array.from({length:M.length},()=>""),z=new le({fields:k,next:j,prev:M,setter:_});h(z)},[n,v,_,h]),se=c.useCallback((p,C)=>{const k=n.getFieldsInSelection(p,C),M=v(k),j=Array.from({length:M.length},()=>!1),z=new le({fields:k,next:j,prev:M,setter:_});h(z)},[h,v,n,_]),E=c.useCallback(p=>{if(!t||!s||r)return;p.preventDefault();const C=n.getCellType(t);if(C)switch(C){case"text":case"number":B(t,s);break;case"boolean":se(t,s);break;case"togglable-number":H(t,s);break}},[t,s,r,n,B,se,H]),$=c.useCallback(p=>{if(!t||!r)return;p.preventDefault(),p.stopPropagation(),S();const C=m==null?void 0:m.getContainer(t);C==null||C.focus()},[m,r,t,S]),X=c.useCallback(p=>{if(!e||r)return;const C=Tr(e),k=M=>{M&&(l(!1),M.focus())};switch(p.key){case".":k(C.cancel);break;case",":k(C.shortcuts);break}},[r,l,e]);return{handleKeyDownEvent:c.useCallback(p=>{if(Nr.includes(p.key)){D(p);return}if(p.key==="z"&&(p.metaKey||p.ctrlKey)){O(p);return}if(p.key===" "){U(p);return}if(p.key==="Delete"||p.key==="Backspace"){E(p);return}if(p.key==="Enter"){Z(p);return}if(p.key==="Escape"){$(p);return}if(p.key==="Tab"){y(p);return}},[$,D,O,U,Z,E,y]),handleSpecialFocusKeys:X}};function Tr(e){const n=Array.from(document.querySelectorAll("[tabindex], a, button, input, select, textarea")),t=n.indexOf(e.current),s=t>0?n[t-1]:null;let r=null;for(let l=t+1;l<n.length;l++)if(!e.current.contains(n[l])){r=n[l];break}return{shortcuts:s,cancel:r}}var $r=({matrix:e,anchor:n,dragEnd:t,setDragEnd:s,isDragging:r,setIsDragging:l,setRangeEnd:i,setIsSelecting:o,getSelectionValues:a,setSelectionValues:f,execute:d})=>{const g=c.useCallback(()=>{if(!r||!n||!t)return;const w=e.getFieldsInSelection(n,t),b=e.getCellField(n);if(!b||!w.length)return;const m=a([b]),v=w.filter(D=>D!==b),_=a(v),S=Array.from({length:_.length},()=>m[0]),x=new le({fields:v,prev:_,next:S,setter:f});d(x),l(!1),s(null),i(t)},[r,n,t,e,a,f,d,l,s,i]);return{handleMouseUpEvent:c.useCallback(()=>{g(),o(!1)},[g,o])}},Rr=({matrix:e,anchor:n,visibleColumns:t,visibleRows:s,columnVirtualizer:r,rowVirtualizer:l,setColumnVisibility:i,flatColumns:o,queryTool:a,setSingleRange:f})=>{const d=c.useCallback((h,w)=>{if(!n)return;const{row:b,col:m}=h,{row:v,col:_}=n,S=b>=v?"down":"up",x=m>=_?"right":"left";let D=S==="down"?b+1:b-1;s[D]===void 0&&(D=b);let y=x==="right"?m+1:m-1;t[y]===void 0&&(y=m);const O={align:"auto",behavior:"auto"};(w==="horizontal"||w==="both")&&r.scrollToIndex(y,O),(w==="vertical"||w==="both")&&l.scrollToIndex(D,O)},[n,r,s,l,t]),g=c.useCallback(h=>{const w=e.getCoordinatesByField(h);if(!w)return;const b=o[w.col];i(m=>({...m,[b.id]:!0})),requestAnimationFrame(()=>{d(w,"both"),f(w)}),requestAnimationFrame(()=>{const m=a==null?void 0:a.getInput(w);m&&m.focus()})},[e,o,i,d,f,a]);return{scrollToCoordinates:d,navigateToField:g}},Fr=e=>{const n=c.useRef(null);return c.useEffect(()=>{e.current&&(n.current=new br(e.current))},[e]),n.current},Vt=({rowErrors:e})=>{const n=e?e.length:0;return!e||n<=0?null:u.jsx(Pe,{content:u.jsx("ul",{className:"flex flex-col gap-y-3",children:e.map((t,s)=>u.jsx(Gr,{error:t},s))}),delayDuration:0,children:u.jsx(ln,{color:"red",size:"2xsmall",className:"cursor-default",children:n})})},Gr=({error:e})=>{const{t:n}=oe();return u.jsxs("li",{className:"txt-compact-small flex flex-col items-start",children:[e.message,u.jsx("button",{type:"button",onClick:e.to,className:"text-ui-fg-interactive hover:text-ui-fg-interactive-hover transition-fg",children:n("dataGrid.errors.fixError")})]})},Ce=({isAnchor:e,isSelected:n,isDragSelected:t,field:s,showOverlay:r,placeholder:l,innerProps:i,overlayProps:o,children:a,errors:f,rowErrors:d,outerComponent:g})=>{const w=!!he(f,s);return u.jsxs("div",{className:"group/container relative size-full",children:[u.jsxs("div",{className:q("bg-ui-bg-base group/cell relative flex size-full items-center gap-x-2 px-4 py-2.5 outline-none",{"bg-ui-tag-red-bg text-ui-tag-red-text":w&&!e&&!n&&!t,"ring-ui-bg-interactive ring-2 ring-inset":e,"bg-ui-bg-highlight [&:has([data-field]:focus)]:bg-ui-bg-base":n||e,"bg-ui-bg-subtle":t&&!e}),tabIndex:-1,...i,children:[u.jsx(Qs,{name:s,errors:f,render:({message:b})=>u.jsx("div",{className:"flex items-center justify-center",children:u.jsx(Pe,{content:b,delayDuration:0,children:u.jsx(gt,{className:"text-ui-tag-red-icon z-[3]"})})})}),u.jsx("div",{className:"relative z-[1] flex size-full items-center justify-center",children:u.jsx(Vr,{isAnchor:e,placeholder:l,children:a})}),u.jsx(Vt,{rowErrors:d}),r&&u.jsx("div",{...o,"data-cell-overlay":"true",className:"absolute inset-0 z-[2]"})]}),g]})},Vr=({isAnchor:e,placeholder:n,children:t})=>!e&&n?n:t,Kr=({context:e,disabled:n})=>{const{field:t,control:s,renderProps:r}=xe({context:e}),l=ie({context:e}),{container:i,input:o}=r;return u.jsx(ve,{control:s,name:t,render:({field:a})=>u.jsx(Ce,{...i,...l,children:u.jsx(Wr,{field:a,inputProps:o,disabled:n})})})},Wr=({field:e,inputProps:n,disabled:t})=>{const{ref:s,value:r,onBlur:l,name:i,disabled:o}=e,{ref:a,onBlur:f,onChange:d,onFocus:g,...h}=n,w=we(s,a);return u.jsx(On,{disabled:t||o,name:i,checked:r,onCheckedChange:b=>d(b===!0,r),onFocus:g,onBlur:()=>{l(),f()},ref:w,tabIndex:-1,...h})},ze=({context:e,code:n})=>{const{field:t,control:s,renderProps:r}=xe({context:e}),l=ie({context:e}),{container:i,input:o}=r,a=en[n.toUpperCase()];return u.jsx(ve,{control:s,name:t,render:({field:f})=>u.jsx(Ce,{...i,...l,children:u.jsx(Br,{field:f,inputProps:o,currencyInfo:a})})})},Br=({field:e,inputProps:n,currencyInfo:t})=>{const{value:s,onChange:r,onBlur:l,ref:i,...o}=e,{ref:a,onBlur:f,onFocus:d,onChange:g,...h}=n,w=c.useCallback(S=>{const x=typeof S=="number"?S.toString():S||"";return Mn({value:x,decimalScale:t.decimal_digits,disableGroupSeparators:!0,decimalSeparator:"."})},[t]),[b,m]=c.useState(s||""),v=(S,x,D)=>{if(!S){m("");return}m(S)};c.useEffect(()=>{let S=s;isNaN(Number(s))||(S=w(S)),m(S)},[s,w]);const _=we(a,i);return u.jsxs("div",{className:"relative flex size-full items-center",children:[u.jsx("span",{className:"txt-compact-small text-ui-fg-muted pointer-events-none absolute left-0 w-fit min-w-4","aria-hidden":!0,children:t.symbol_native}),u.jsx(In,{...o,...h,ref:_,className:"txt-compact-small w-full flex-1 cursor-default appearance-none bg-transparent pl-8 text-right outline-none",value:b||void 0,onValueChange:v,formatValueOnBlur:!0,onBlur:()=>{l(),f(),g(b,s)},onFocus:d,decimalScale:t.decimal_digits,decimalsLimit:t.decimal_digits,autoComplete:"off",tabIndex:-1})]})},Hr=({context:e,...n})=>{const{field:t,control:s,renderProps:r}=xe({context:e}),l=ie({context:e}),{container:i,input:o}=r;return u.jsx(ve,{control:s,name:t,render:({field:a})=>u.jsx(Ce,{...i,...l,children:u.jsx(Lr,{field:a,inputProps:o,...n})})})},Lr=({field:e,inputProps:n,...t})=>{const{ref:s,value:r,onChange:l,onBlur:i,...o}=e,{ref:a,onChange:f,onBlur:d,onFocus:g,...h}=n,[w,b]=c.useState(r);c.useEffect(()=>{b(r)},[r]);const m=we(a,s);return u.jsx("div",{className:"size-full",children:u.jsx("input",{ref:m,value:w,onChange:v=>b(v.target.value),onBlur:()=>{i(),d(),f(w,r)},onFocus:g,type:"number",inputMode:"decimal",className:q("txt-compact-small size-full bg-transparent outline-none","placeholder:text-ui-fg-muted"),tabIndex:-1,...t,...o,...h})})},Ne=({context:e,color:n="muted",children:t})=>{const{rowErrors:s}=ie({context:e});return u.jsxs("div",{className:q("txt-compact-small text-ui-fg-subtle flex size-full cursor-not-allowed items-center justify-between overflow-hidden px-4 py-2.5 outline-none",n==="muted"&&"bg-ui-bg-subtle",n==="normal"&&"bg-ui-bg-base"),children:[u.jsx("div",{className:"flex-1 truncate",children:t}),u.jsx(Vt,{rowErrors:s})]})},Ur=(e=20)=>{const[n,t]=c.useState([]),[s,r]=c.useState([]),l=n.length>0,i=s.length>0,o=c.useCallback(()=>{if(!l)return;const d=n[n.length-1],g=n.slice(0,n.length-1);d.undo(),t(g),r([d,...s.slice(0,e-1)])},[l,s,n,e]),a=c.useCallback(()=>{if(!i)return;const d=s[0],g=s.slice(1);d.redo(),t([...n,d].slice(0,e-1)),r(g)},[i,s,n,e]),f=c.useCallback(d=>{d.execute(),t(g=>[...g,d].slice(0,e-1)),r([])},[e]);return{undo:o,redo:a,execute:f,canUndo:l,canRedo:i}},Yr=()=>{const{t:e}=oe();return c.useMemo(()=>[{label:e("dataGrid.shortcuts.commands.undo"),keys:{Mac:["⌘","Z"],Windows:["Ctrl","Z"]}},{label:e("dataGrid.shortcuts.commands.redo"),keys:{Mac:["⇧","⌘","Z"],Windows:["Shift","Ctrl","Z"]}},{label:e("dataGrid.shortcuts.commands.copy"),keys:{Mac:["⌘","C"],Windows:["Ctrl","C"]}},{label:e("dataGrid.shortcuts.commands.paste"),keys:{Mac:["⌘","V"],Windows:["Ctrl","V"]}},{label:e("dataGrid.shortcuts.commands.edit"),keys:{Mac:["↵"],Windows:["Enter"]}},{label:e("dataGrid.shortcuts.commands.delete"),keys:{Mac:["⌫"],Windows:["Backspace"]}},{label:e("dataGrid.shortcuts.commands.clear"),keys:{Mac:["Space"],Windows:["Space"]}},{label:e("dataGrid.shortcuts.commands.moveUp"),keys:{Mac:["↑"],Windows:["↑"]}},{label:e("dataGrid.shortcuts.commands.moveDown"),keys:{Mac:["↓"],Windows:["↓"]}},{label:e("dataGrid.shortcuts.commands.moveLeft"),keys:{Mac:["←"],Windows:["←"]}},{label:e("dataGrid.shortcuts.commands.moveRight"),keys:{Mac:["→"],Windows:["→"]}},{label:e("dataGrid.shortcuts.commands.moveTop"),keys:{Mac:["⌘","↑"],Windows:["Ctrl","↑"]}},{label:e("dataGrid.shortcuts.commands.moveBottom"),keys:{Mac:["⌘","↓"],Windows:["Ctrl","↓"]}},{label:e("dataGrid.shortcuts.commands.selectDown"),keys:{Mac:["⇧","↓"],Windows:["Shift","↓"]}},{label:e("dataGrid.shortcuts.commands.selectUp"),keys:{Mac:["⇧","↑"],Windows:["Shift","↑"]}},{label:e("dataGrid.shortcuts.commands.selectColumnDown"),keys:{Mac:["⇧","⌘","↓"],Windows:["Shift","Ctrl","↓"]}},{label:e("dataGrid.shortcuts.commands.selectColumnUp"),keys:{Mac:["⇧","⌘","↑"],Windows:["Shift","Ctrl","↑"]}},{label:e("dataGrid.shortcuts.commands.focusToolbar"),keys:{Mac:["⌃","⌥",","],Windows:["Ctrl","Alt",","]}},{label:e("dataGrid.shortcuts.commands.focusCancel"),keys:{Mac:["⌃","⌥","."],Windows:["Ctrl","Alt","."]}}],[e])},Zr=({open:e,onOpenChange:n})=>{const{t}=oe(),[s,r]=c.useState(""),l=Yr(),i=c.useMemo(()=>l.filter(o=>o.label.toLowerCase().includes(s.toLowerCase())),[s,l]);return u.jsxs(an,{open:e,onOpenChange:n,children:[u.jsx(cn,{asChild:!0,children:u.jsx(de,{size:"small",variant:"secondary",children:t("dataGrid.shortcuts.label")})}),u.jsxs(un,{children:[u.jsx(dn,{className:q("bg-ui-bg-overlay fixed inset-0","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0")}),u.jsxs(fn,{className:"bg-ui-bg-subtle shadow-elevation-modal fixed left-[50%] top-[50%] flex h-full max-h-[612px] w-full max-w-[560px] translate-x-[-50%] translate-y-[-50%] flex-col divide-y overflow-hidden rounded-lg outline-none",children:[u.jsxs("div",{className:"flex flex-col gap-y-3 px-6 py-4",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx(hn,{asChild:!0,children:u.jsx(mn,{children:t("app.menus.user.shortcuts")})}),u.jsx(pn,{className:"sr-only"})]}),u.jsxs("div",{className:"flex items-center gap-x-2",children:[u.jsx(tt,{children:"esc"}),u.jsx(gn,{asChild:!0,children:u.jsx(bn,{variant:"transparent",size:"small",children:u.jsx(vn,{})})})]})]}),u.jsx("div",{children:u.jsx(wn,{type:"search",value:s,autoFocus:!0,onChange:o=>r(o.target.value)})})]}),u.jsx("div",{className:"flex flex-col divide-y overflow-y-auto",children:i.map((o,a)=>{var f;return u.jsxs("div",{className:"text-ui-fg-subtle flex items-center justify-between px-6 py-3",children:[u.jsx(xn,{size:"small",children:o.label}),u.jsx("div",{className:"flex items-center gap-x-1",children:(f=o.keys.Mac)==null?void 0:f.map((d,g)=>u.jsx("div",{className:"flex items-center gap-x-1",children:u.jsx(tt,{children:d})},g))})]},a)})})]})]})]})},Xr=40,Kt=e=>{const n=e.getIsPinned(),s=document.documentElement.classList.contains("dark")?"rgb(50,50,53)":"rgb(228,228,231)";return{position:n?"sticky":"relative",width:e.getSize(),zIndex:n?1:0,borderBottom:n?`1px solid ${s}`:void 0,borderRight:n?`1px solid ${s}`:void 0,left:n==="left"?`${e.getStart("left")}px`:void 0,right:n==="right"?`${e.getAfter("right")}px`:void 0}},Qr=({data:e=[],columns:n,state:t,getSubRows:s,onEditingChange:r,disableInteractions:l,multiColumnSelection:i=!1})=>{var Je,qe;const o=c.useRef(null),{redo:a,undo:f,execute:d}=Ur(),{register:g,control:h,getValues:w,setValue:b,formState:{errors:m}}=t,[v,_]=c.useState(!0),S=!l&&v,[x,D]=c.useState(null),[y,O]=c.useState(null),[F,R]=c.useState(null),[T,U]=c.useState(!1),[Y,ee]=c.useState(!1),[te,ne]=c.useState(!1),[Z,H]=c.useState({}),[B,se]=c.useState({}),E=kn({data:e,columns:n,initialState:{columnPinning:{left:[n[0].id]}},state:{columnVisibility:Z},onColumnVisibilityChange:H,getSubRows:s,getCoreRowModel:jn(),defaultColumn:{size:200,maxSize:400}}),{flatRows:$}=E.getRowModel(),X=E.getAllFlatColumns(),Q=c.useMemo(()=>$.filter((I,A)=>(B==null?void 0:B[A])!==!1),[$,B]),p=E.getVisibleLeafColumns(),C=ht({count:Q.length,estimateSize:()=>Xr,getScrollElement:()=>o.current,overscan:5,rangeExtractor:I=>{const A=new Set(Array.from({length:I.endIndex-I.startIndex+1},(V,K)=>I.startIndex+K));return x&&Q[x.row]&&A.add(x.row),y&&Q[y.row]&&A.add(y.row),Array.from(A).sort((V,K)=>V-K)}}),k=C.getVirtualItems(),M=ht({count:p.length,estimateSize:I=>p[I].getSize(),getScrollElement:()=>o.current,horizontal:!0,overscan:3,rangeExtractor:I=>{const A=I.startIndex,V=I.endIndex,K=new Set(Array.from({length:V-A+1},(ae,L)=>A+L));return x&&p[x.col]&&K.add(x.col),y&&p[y.col]&&K.add(y.col),K.add(0),Array.from(K).sort((ae,L)=>ae-L)}}),j=M.getVirtualItems();let z,P;M&&(j!=null&&j.length)&&(z=((Je=j[0])==null?void 0:Je.start)??0,P=M.getTotalSize()-(((qe=j[j.length-1])==null?void 0:qe.end)??0));const N=c.useMemo(()=>new gr($,n,i),[$,n,i]),J=Fr(o),G=c.useCallback(I=>{D(I),O(I)},[]),{errorCount:Wt,isHighlighted:Bt,toggleErrorHighlighting:Fe}=Sr(N,E,m),Ht=c.useCallback(()=>{Fe(B,Z,se,H)},[Fe,B,Z]),{columnOptions:Lt,handleToggleColumn:Ge,handleResetColumns:Ut,isDisabled:Yt}=yr(E,N),Zt=c.useCallback(I=>Ge(I),[Ge]),{navigateToField:Ve,scrollToCoordinates:ye}=Rr({matrix:N,queryTool:J,anchor:x,columnVirtualizer:M,rowVirtualizer:C,flatColumns:X,setColumnVisibility:H,setSingleRange:G,visibleColumns:p,visibleRows:Q}),{createSnapshot:_e,restoreSnapshot:Xt}=xr({matrix:N,form:t}),Se=c.useCallback(I=>{r&&r(I),I&&_e(x),ne(I)},[x,_e,r]),{getSelectionValues:Ee,setSelectionValues:Me}=Er({matrix:N,form:t,anchor:x}),{handleKeyDownEvent:Ie,handleSpecialFocusKeys:Ke}=Pr({containerRef:o,matrix:N,queryTool:J,anchor:x,rangeEnd:y,isEditing:te,setTrapActive:_,setRangeEnd:O,getSelectionValues:Ee,getValues:w,setSelectionValues:Me,onEditingChangeHandler:Se,restoreSnapshot:Xt,createSnapshot:_e,setSingleRange:G,scrollToCoordinates:ye,execute:d,undo:f,redo:a,setValue:b}),{handleMouseUpEvent:ke}=$r({matrix:N,anchor:x,dragEnd:F,setDragEnd:R,isDragging:Y,setIsDragging:ee,setRangeEnd:O,setIsSelecting:U,getSelectionValues:Ee,setSelectionValues:Me,execute:d}),{handleCopyEvent:je,handlePasteEvent:De}=Cr({matrix:N,isEditing:te,anchor:x,rangeEnd:y,getSelectionValues:Ee,setSelectionValues:Me,execute:d}),{getWrapperFocusHandler:We,getInputChangeHandler:Be,getOverlayMouseDownHandler:He,getWrapperMouseOverHandler:Le,getIsCellDragSelected:Ue,getIsCellSelected:Ye,onDragToFillStart:Qt}=vr({matrix:N,anchor:x,rangeEnd:y,setRangeEnd:O,isDragging:Y,setIsDragging:ee,isSelecting:T,setIsSelecting:U,setSingleRange:G,dragEnd:F,setDragEnd:R,setValue:b,execute:d,multiColumnSelection:i}),{getCellErrorMetadata:Ze,getCellMetadata:Xe}=wr({matrix:N});c.useEffect(()=>{if(S)return window.addEventListener("keydown",Ie),window.addEventListener("mouseup",ke),window.addEventListener("copy",je),window.addEventListener("paste",De),()=>{window.removeEventListener("keydown",Ie),window.removeEventListener("mouseup",ke),window.removeEventListener("copy",je),window.removeEventListener("paste",De)}},[S,Ie,ke,je,De]),c.useEffect(()=>{const I=A=>{if(Ft(A)){Ke(A);return}};return window.addEventListener("keydown",I),()=>{window.removeEventListener("keydown",I)}},[Ke]);const Jt=c.useCallback(I=>{I&&_(!1)},[]);c.useEffect(()=>{x&&(y||O(x))},[x,y]),c.useEffect(()=>{if(!x&&N){const I=N.getFirstNavigableCell();I&&G(I)}},[x,N,G]);const qt=c.useMemo(()=>({anchor:x,control:h,trapActive:S,errors:m,setTrapActive:_,setIsSelecting:U,setIsEditing:Se,setSingleRange:G,setRangeEnd:O,getWrapperFocusHandler:We,getInputChangeHandler:Be,getOverlayMouseDownHandler:He,getWrapperMouseOverHandler:Le,register:g,getIsCellSelected:Ye,getIsCellDragSelected:Ue,getCellMetadata:Xe,getCellErrorMetadata:Ze,navigateToField:Ve}),[x,h,S,m,_,U,Se,G,O,We,Be,He,Le,g,Ye,Ue,Xe,Ze,Ve]),Qe=c.useCallback(()=>{x&&!S&&(_(!0),G(x),ye(x,"both"),requestAnimationFrame(()=>{var I;(I=J==null?void 0:J.getContainer(x))==null||I.focus()}))},[x,S,G,ye,J]);return u.jsx(Tt.Provider,{value:qt,children:u.jsxs("div",{className:"bg-ui-bg-subtle flex size-full flex-col",children:[u.jsx(Jr,{columnOptions:Lt,isDisabled:Yt,onToggleColumn:Zt,errorCount:Wt,onToggleErrorHighlighting:Ht,onResetColumns:Ut,isHighlighted:Bt,onHeaderInteractionChange:Jt}),u.jsx("div",{className:"size-full overflow-hidden",children:u.jsx("div",{ref:o,autoFocus:!0,tabIndex:0,className:"relative h-full select-none overflow-auto outline-none",onFocus:Qe,onClick:Qe,"data-container":!0,role:"application",children:u.jsxs("div",{role:"grid",className:"text-ui-fg-subtle grid",children:[u.jsx("div",{role:"rowgroup",className:"txt-compact-small-plus bg-ui-bg-subtle sticky top-0 z-[1] grid",children:E.getHeaderGroups().map(I=>u.jsxs("div",{role:"row",className:"flex h-10 w-full",children:[z?u.jsx("div",{role:"presentation",style:{display:"flex",width:z}}):null,j.reduce((A,V,K,ae)=>{const L=I.headers[V.index],ce=ae[K-1];return ce&&V.index!==ce.index+1&&A.push(u.jsx("div",{role:"presentation",style:{display:"flex",width:`${V.start-ce.end}px`}},`padding-${ce.index}-${V.index}`)),A.push(u.jsx("div",{role:"columnheader","data-column-index":V.index,style:{width:L.getSize(),...Kt(L.column)},className:"bg-ui-bg-base txt-compact-small-plus flex items-center border-b border-r px-4 py-2.5",children:L.isPlaceholder?null:bt(L.column.columnDef.header,L.getContext())},L.id)),A},[]),P?u.jsx("div",{role:"presentation",style:{display:"flex",width:P}}):null]},I.id))}),u.jsx("div",{role:"rowgroup",className:"relative grid",style:{height:`${C.getTotalSize()}px`},children:k.map(I=>{const A=Q[I.index],V=$.findIndex(K=>K.id===A.id);return u.jsx(el,{row:A,rowIndex:V,virtualRow:I,flatColumns:X,virtualColumns:j,anchor:x,virtualPaddingLeft:z,virtualPaddingRight:P,onDragToFillStart:Qt,multiColumnSelection:i},A.id)})})]})})})]})})},Jr=({columnOptions:e,isDisabled:n,onToggleColumn:t,onResetColumns:s,isHighlighted:r,errorCount:l,onToggleErrorHighlighting:i,onHeaderInteractionChange:o})=>{const[a,f]=c.useState(!1),[d,g]=c.useState(!1),{t:h}=oe(),w=e.some(v=>!v.checked),b=v=>{o(v),f(v)},m=v=>{o(v),g(v)};return u.jsxs("div",{className:"bg-ui-bg-base flex items-center justify-between border-b p-4",children:[u.jsxs("div",{className:"flex items-center gap-x-2",children:[u.jsxs(ue,{open:d,onOpenChange:m,children:[u.jsx(on,{showTooltip:n,content:h("dataGrid.columns.disabled"),children:u.jsx(ue.Trigger,{asChild:!0,disabled:n,children:u.jsxs(de,{size:"small",variant:"secondary",children:[w?u.jsx(xt,{}):u.jsx(_t,{}),h("dataGrid.columns.view")]})})}),u.jsx(ue.Content,{children:e.map((v,_)=>{const{checked:S,disabled:x,id:D,name:y}=v;return x?null:u.jsx(ue.CheckboxItem,{checked:S,onCheckedChange:t(_),onSelect:O=>O.preventDefault(),children:y},D)})})]}),w&&u.jsx(de,{size:"small",variant:"transparent",type:"button",onClick:s,className:"text-ui-fg-muted hover:text-ui-fg-subtle","data-id":"reset-columns",children:h("dataGrid.columns.resetToDefault")})]}),u.jsxs("div",{className:"flex items-center gap-x-2",children:[l>0&&u.jsxs(de,{size:"small",variant:"secondary",type:"button",onClick:i,className:q({"bg-ui-button-neutral-pressed":r}),children:[u.jsx(gt,{className:"text-ui-fg-subtle"}),u.jsx("span",{children:h("dataGrid.errors.count",{count:l})})]}),u.jsx(Zr,{open:a,onOpenChange:b})]})]})},qr=({cell:e,columnIndex:n,rowIndex:t,anchor:s,onDragToFillStart:r,multiColumnSelection:l})=>{const o=Rt({row:t,col:n},s);return u.jsx("div",{role:"gridcell","aria-rowindex":t,"aria-colindex":n,style:{width:e.column.getSize(),...Kt(e.column)},"data-row-index":t,"data-column-index":n,className:q("relative flex items-center border-b border-r p-0 outline-none"),tabIndex:-1,children:u.jsxs("div",{className:"relative h-full w-full",children:[bt(e.column.columnDef.cell,{...e.getContext(),columnIndex:n,rowIndex:t}),o&&u.jsx("div",{onMouseDown:r,className:q("bg-ui-fg-interactive absolute bottom-0 right-0 z-[3] size-1.5 cursor-ns-resize",{"cursor-nwse-resize":l})})]})})},el=({row:e,rowIndex:n,virtualRow:t,virtualPaddingLeft:s,virtualPaddingRight:r,virtualColumns:l,flatColumns:i,anchor:o,onDragToFillStart:a,multiColumnSelection:f})=>{const d=e.getVisibleCells();return u.jsxs("div",{role:"row","aria-rowindex":t.index,style:{transform:`translateY(${t.start}px)`},className:"bg-ui-bg-subtle txt-compact-small absolute flex h-10 w-full",children:[s?u.jsx("div",{role:"presentation",style:{display:"flex",width:s}}):null,l.reduce((g,h,w,b)=>{const m=d[h.index],v=m.column,_=i.findIndex(x=>x.id===v.id),S=b[w-1];return S&&h.index!==S.index+1&&g.push(u.jsx("div",{role:"presentation",style:{display:"flex",width:`${h.start-S.end}px`}},`padding-${S.index}-${h.index}`)),g.push(u.jsx(qr,{cell:m,columnIndex:_,rowIndex:n,anchor:o,onDragToFillStart:a,multiColumnSelection:f},m.id)),g},[]),r?u.jsx("div",{role:"presentation",style:{display:"flex",width:r}}):null]})},tl=({context:e})=>{const{field:n,control:t,renderProps:s}=xe({context:e}),r=ie({context:e}),{container:l,input:i}=s;return u.jsx(ve,{control:t,name:n,render:({field:o})=>u.jsx(Ce,{...l,...r,children:u.jsx(nl,{field:o,inputProps:i})})})},nl=({field:e,inputProps:n})=>{const{onChange:t,onBlur:s,ref:r,value:l,...i}=e,{ref:o,onBlur:a,onChange:f,...d}=n,[g,h]=c.useState(l);c.useEffect(()=>{h(l)},[l]);const w=we(o,r);return u.jsx("input",{className:q("txt-compact-small text-ui-fg-subtle flex size-full cursor-pointer items-center justify-center bg-transparent outline-none","focus:cursor-text"),autoComplete:"off",tabIndex:-1,value:g,onChange:b=>h(b.target.value),ref:w,onBlur:()=>{s(),a(),f(g,l)},...d,...i})},sl=({isLoading:e,...n})=>{var t;return e?u.jsx(ur,{columns:n.columns,rows:(t=n.data)!=null&&t.length&&n.data.length>0?n.data.length:10}):u.jsx(Qr,{...n})},fl=Object.assign(sl,{BooleanCell:Kr,TextCell:tl,NumberCell:Hr,CurrencyCell:ze,ReadonlyCell:Ne});function rl(){const e=Dn();return{column:({id:n,name:t,header:s,cell:r,disableHiding:l=!1,field:i,type:o})=>e.display({id:n,header:s,cell:r,enableHiding:!l,meta:{name:t,field:i,type:o}})}}var mt=({includesTax:e})=>{const{t:n}=oe();return u.jsx(Pe,{maxWidth:999,content:n(e?"general.includesTaxTooltip":"general.excludesTaxTooltip"),children:e?u.jsx(jt,{className:"text-ui-fg-muted shrink-0"}):u.jsx(Mt,{className:"text-ui-fg-muted shrink-0"})})},hl=({currencies:e,regions:n,pricePreferences:t,isReadyOnly:s,getFieldName:r,t:l})=>{const i=rl();return[...(e==null?void 0:e.map(o=>{const a=t==null?void 0:t.find(d=>d.attribute==="currency_code"&&d.value===o),f=l("fields.priceTemplate",{regionOrCurrency:o.toUpperCase()});return i.column({id:`currency_prices.${o}`,name:l("fields.priceTemplate",{regionOrCurrency:o.toUpperCase()}),field:d=>(s==null?void 0:s(d))?null:r(d,o),type:"number",header:()=>u.jsxs("div",{className:"flex w-full items-center justify-between gap-3",children:[u.jsx("span",{className:"truncate",title:f,children:f}),u.jsx(mt,{includesTax:a==null?void 0:a.is_tax_inclusive})]}),cell:d=>s!=null&&s(d)?u.jsx(Ne,{context:d}):u.jsx(ze,{code:o,context:d})})}))??[],...(n==null?void 0:n.map(o=>{const a=t==null?void 0:t.find(d=>d.attribute==="region_id"&&d.value===o.id),f=l("fields.priceTemplate",{regionOrCurrency:o.name});return i.column({id:`region_prices.${o.id}`,name:l("fields.priceTemplate",{regionOrCurrency:o.name}),field:d=>(s==null?void 0:s(d))?null:r(d,o.id),type:"number",header:()=>u.jsxs("div",{className:"flex w-full items-center justify-between gap-3",children:[u.jsx("span",{className:"truncate",title:f,children:f}),u.jsx(mt,{includesTax:a==null?void 0:a.is_tax_inclusive})]}),cell:d=>s!=null&&s(d)?u.jsx(Ne,{context:d}):(e==null?void 0:e.find(h=>h===o.currency_code))?u.jsx(ze,{code:o.currency_code,context:d}):null})}))??[]]};export{fl as D,mt as I,rl as a,ur as b,hl as c,Ne as d,xe as e,ie as f,Ce as g,we as h,dl as u};
