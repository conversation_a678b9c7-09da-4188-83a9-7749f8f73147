---
description: 
globs: src/components/**,.storybook/mswHandlers/**,src/lib/**
alwaysApply: false
---
# Production-Ready React Component Design System

## Core Design Principles

### Visual Hierarchy
- Group related information in cards and sections
- Use headings, icons, and color-coding for structure
- Consistent spacing for visual separation
- Strategic elevation (shadows) for depth

### Accessibility (WCAG 2.1 AAA)
- 7:1 contrast ratio for normal text, 4.5:1 for large text
- ARIA labels for interactive elements
- Keyboard navigation with visible focus states (3:1 min contrast)
- Screen reader compatibility with semantic HTML
- Respect `prefers-reduced-motion` settings

### Typography
- Consistent type scale with clear hierarchy
- 16px minimum font size with 1.5 line height
- 80 character maximum line length
- Left-aligned text for LTR languages
- Text resizable to 200% without loss of functionality

### Interactive Elements
- Visually distinct with hover/focus states
- Clear affordances for clickable elements
- Immediate feedback for interactions
- Tooltips for supplementary information
- Confirmation for destructive actions

### Status Indicators
- Consistent color-coding with adequate contrast
- Multiple cues beyond color (icons, patterns, text)
- Screen reader accessible descriptions

### Responsive Design
- Support all breakpoints from '3xs' (256px) to '7xl' (1280px)
- Mobile-first approach with progressive enhancement
- 44×44px minimum touch targets with adequate spacing
- No horizontal scrolling at widths ≥320px

### Implementation Workflow
1. Define types and interfaces first
2. Create accessible loading skeletons
3. Implement animations with reduced-motion alternatives
4. Use CSS variables and Tailwind utilities
5. Add error handling and empty states
6. Ensure keyboard and screen reader support
7. Test across all breakpoints

### Component Architecture
- Use compound component pattern for complex UI
- Forward refs for all components
- Set explicit displayNames for debugging
- Use proper HTML semantics
- Separate presentation, logic, and data fetching

## UI Component Implementation

### Mandatory Workflow

1. **MSW Handlers First (Code Must MoreThan 700 Line++)**
   ```typescript
   // src/.storybook/mswHandlers/{ComponentName}.handlers.ts
   import { http, HttpResponse, delay } from 'msw';
   import { mockData } from '../../components/{ComponentName}/__fixtures__/{ComponentName}.mockData';
   // Define comprehensive MSW handlers for all HTTP methods (GET, POST, PUT, PATCH, DELETE)
   // Include proper typing for request parameters, response bodies, and error states
   // Implement realistic response delays and conditional error scenarios
   // Support query parameters for filtering, pagination, and sorting
   // Add proper TypeScript interfaces for all handler functions and response objects
   ```
   
2. **Mock Data with faker-js (Code Must MoreThan 700 Line++)**
   ```
   src/components/{ComponentName}/__fixtures__/{ComponentName}.mockData.ts
   ```
   
3. **Type Definitions and Skeletons**
   ```
   src/components/{ComponentName}/{ComponentName}-Types.ts
   src/components/{ComponentName}/{ComponentName}-Skeleton.tsx
   ```

4. **Animation Variants**
   ```
   src/components/{ComponentName}/{ComponentName}-Animations.ts
   ```

5. **Style Variants**
   ```
   src/components/{ComponentName}/{ComponentName}-Variants.ts
   ```

6. **Component Implementation**
   ```
   src/components/{ComponentName}/{ComponentName}.tsx
   ```

7. **Storybook Stories**
   ```
   src/components/{ComponentName}/__stories__/{ComponentName}.stories.tsx
   ```

### Global Theme Integration

- Use CSS variables from `src/styles/globals.css`
- Never implement theme switching at component level
- Use Tailwind classes referencing CSS variables
- Support automatic dark mode via `.dark` class

### CSS Variables Example

```css
:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --primary: 142.1 76.2% 36.3%;
  --primary-foreground: 355.7 100% 97.3%;
  /* More variables... */
}

.dark {
  --background: 20 14.3% 4.1%;
  --foreground: 0 0% 95%;
  /* Dark mode variables... */
}
```

### Common Tailwind Classes

| CSS Variable | Tailwind Class | Usage |
|--------------|----------------|-------|
| --background | `bg-background` | Main backgrounds |
| --foreground | `text-foreground` | Main text color |
| --primary | `bg-primary` | Primary actions |
| --primary-foreground | `text-primary-foreground` | Text on primary backgrounds |
| --secondary | `bg-secondary` | Secondary elements |
| --muted | `bg-muted` | Subtle backgrounds |
| --accent | `bg-accent` | Accent backgrounds |
| --destructive | `bg-destructive` | Error states |
| --border | `border-border` | Border colors |
| --ring | `ring-ring` | Focus rings |

## Component Architecture with Compound Pattern

```typescript
// Factory approach for compound components
function createCompoundPart<T, P extends React.HTMLAttributes<T>>(
  displayName: string,
  Component: React.ForwardRefExoticComponent<P & React.RefAttributes<T>> | keyof JSX.IntrinsicElements,
  defaultProps?: Partial<P>
) {
  const CompoundPart = React.forwardRef<T, P>((props, ref) => {
    const combinedProps = { ...defaultProps, ...props, ref } as ComponentProps;
    return React.createElement(Component, combinedProps);
  });
  
  CompoundPart.displayName = displayName;
  return CompoundPart;
}

// Main component with compound structure
const {ComponentName} = React.forwardRef<HTMLElement, Props>((props, ref) => {
  // Implementation
});

// Create subcomponents
const {ComponentName}Header = createCompoundPart('{ComponentName}Header', 'div');
const {ComponentName}Title = createCompoundPart('{ComponentName}Title', 'h3');
const {ComponentName}Content = createCompoundPart('{ComponentName}Content', 'div');

// Attach subcomponents
{ComponentName}.Header = {ComponentName}Header;
{ComponentName}.Title = {ComponentName}Title;
{ComponentName}.Content = {ComponentName}Content;
```

## TanStack Table Requirements

Always use TanStack Table with these features:
- Strongly typed column definitions
- Virtual scrolling for large datasets
- Sorting & filtering capabilities
- Controlled state management
- WCAG compliance
- Visual enhancements (badges, avatars, tooltips)

```typescript
// Column definition example
const columns = [
  columnHelper.accessor('avatar', {
    header: 'Profile',
    cell: info => (
      <Avatar>
        <AvatarImage src={info.row.original.avatarUrl} alt={`${info.row.original.name}'s profile`} />
        <AvatarFallback>{info.row.original.name.charAt(0).toUpperCase()}</AvatarFallback>
      </Avatar>
    ),
  }),
  
  columnHelper.accessor('status', {
    header: 'Status',
    cell: info => (
      <Badge className={statusStyles[info.getValue()]} aria-label={`Status: ${info.getValue()}`}>
        {info.getValue()}
      </Badge>
    ),
  }),
  
  // Actions column
  columnHelper.display({
    id: 'actions',
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => (
      <div className="flex items-center gap-2 justify-end">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="sm" aria-label={`View details for ${row.original.name}`}>
              <EyeIcon className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>View details</TooltipContent>
        </Tooltip>
        {/* More actions... */}
      </div>
    ),
  }),
];

// TanStack Table implementation
function DataTable({ data }) {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
  });
  
  // Virtual scrolling
  const { rows } = table.getRowModel();
  const virtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 56,
  });
  
  return (
    <div role="region" aria-label="Data table">
      {/* Table implementation */}
    </div>
  );
}
```

## Animation System with Framer Motion

All animations must:
- Respect `prefers-reduced-motion`
- Be pausable/stoppable/hideable
- Have maximum duration of 5 seconds unless controllable
- Use variants for coordinated animations
- Enhance understanding rather than distract
- Optimize performance to maintain 60fps

```typescript
// Animation variants
export const transitions = {
  quick: { duration: 0.2 },
  normal: { duration: 0.3 },
  slow: { duration: 0.5 },
  spring: { type: 'spring', stiffness: 300, damping: 30 },
  stagger: (delay = 0.05) => ({ 
    staggerChildren: delay,
    delayChildren: delay * 2
  }),
};

export const states = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  fadeIn: { opacity: 1 },
  fadeInUp: { opacity: 1 },
  fadeOut: { opacity: 0 },
  hover: { scale: 1.02, y: -2 },
  tap: { scale: 0.98 },
};

// Reduced motion alternatives
export const reducedMotionStates = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  fadeIn: { opacity: 1 },
  fadeInUp: { opacity: 1 }, // No movement
  fadeOut: { opacity: 0 },
  hover: { boxShadow: '0 0 0 2px currentColor' },
  tap: { opacity: 0.9 },
};

// Hook for respecting reduced motion
export function useAnimationVariants(variants, disableAnimations = false) {
  const prefersReducedMotion = useReducedMotion();
  
  if (prefersReducedMotion || disableAnimations) {
    // Return simplified variants
    return {...};
  }
  
  return variants;
}

// Usage example
const animationVariants = useAnimationVariants(variants);

return (
  <motion.div
    variants={animationVariants.container}
    initial="initial"
    animate="animate"
    exit="exit"
  >
    {items.map(item => (
      <motion.div 
        key={item.id}
        variants={animationVariants.item}
        whileHover="hover"
        whileTap="tap"
      >
        {/* Item content */}
      </motion.div>
    ))}
  </motion.div>
);
```

## Styling with class-variance-authority (cva)

All components must use cva for style variants:
- Comprehensive variants (size, color, density)
- Sensible defaults
- Tailwind CSS integration
- Class name merging with cn utility

```typescript
// Variant definition
const buttonVariants = cva(
  'rounded-md shadow transition-colors focus:outline-none focus:ring-2',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        outline: 'border border-input bg-background hover:bg-accent',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        destructive: 'bg-destructive text-destructive-foreground',
      },
      size: {
        sm: 'h-8 px-2 py-1 text-sm',
        md: 'h-10 px-4 py-2',
        lg: 'h-12 px-6 py-3 text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

// Component using variants
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size }), className)}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = 'Button';
```

## Component States

Every component must implement:
1. **Loading State** with skeletons
2. **Empty State** with helpful messaging
3. **Error State** with clear visualization
4. **Success State** with appropriate feedback

```typescript
// Component with state handling
const Component = ({ loading, error, data, emptyMessage = 'No items' }) => {
  if (loading) {
    return <ComponentSkeleton />;
  }
  
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error.message || 'An error occurred'}</AlertDescription>
      </Alert>
    );
  }
  
  if (!data || data.length === 0) {
    return (
      <EmptyState
        icon={<InboxIcon className="h-8 w-8" />}
        title="No items found"
        description={emptyMessage}
      />
    );
  }
  
  // Success state (main component rendering)
  return (
    <Card>
      {/* Component implementation */}
    </Card>
  );
};
```

## Component State Management

All components must follow these patterns for managing local state effectively, prioritizing performance, predictability, and maintainability.

### State Hook Selection Guidelines

Choose the appropriate React hook based on state complexity and usage patterns:

| Hook | When to Use | When to Avoid |
|------|-------------|---------------|
| `useState` | Simple, independent state values | Complex state with interdependent values |
| `useReducer` | Complex state logic with predictable transitions | Simple toggle states or counters |
| `useMemo` | Expensive calculations based on state | Simple derivations or infrequent renders |
| `useCallback` | Functions passed to optimized child components | Event handlers used only in the component |
| `useContext` | Deeply shared state without prop drilling | State only needed by one or two components |

### Basic State Implementation

```typescript
/**
 * Simple state management with useState
 * Suitable for: 
 * - Independent values like toggles, counters
 * - Form input values
 * - Simple flags or status indicators
 */
function SimpleStateComponent() {
  // Define each piece of state with appropriate type
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  
  // Use semantic naming conventions for state updaters
  const incrementCount = () => setCount(prev => prev + 1);
  const toggleOpen = () => setIsOpen(prev => !prev);
  const selectItem = (id: string) => setSelectedItem(id);
  
  return (
    <ComponentContainer>
      {/* Component UI using state values */}
    </ComponentContainer>
  );
}
```

### Complex State Management

For components with complex state transitions, use `useReducer` with TypeScript for type safety:

```typescript
// Define action types for type safety
type Action = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_DATA'; payload: DataItem[] }
  | { type: 'SET_ERROR'; payload: Error | null }
  | { type: 'SELECT_ITEM'; payload: string }
  | { type: 'FILTER_ITEMS'; payload: FilterOptions };

// Define state interface
interface State {
  loading: boolean;
  data: DataItem[];
  error: Error | null;
  selectedId: string | null;
  filters: FilterOptions;
}

// Create reducer with exhaustive type checking
function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_DATA':
      return { ...state, data: action.payload, loading: false };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SELECT_ITEM':
      return { ...state, selectedId: action.payload };
    case 'FILTER_ITEMS':
      return { ...state, filters: action.payload };
    default:
      // Exhaustive type checking ensures all cases are handled
      const _exhaustiveCheck: never = action;
      return state;
  }
}

function ComplexStateComponent() {
  const [state, dispatch] = useReducer(reducer, {
    loading: false,
    data: [],
    error: null,
    selectedId: null,
    filters: { status: 'all' }
  });
  
  // Dispatch actions with appropriate types
  const loadData = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    try {
      const data = await fetchData();
      dispatch({ type: 'SET_DATA', payload: data });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error as Error });
    }
  };
  
  return (
    <ComponentContainer>
      {/* Component UI using reducer state */}
    </ComponentContainer>
  );
}
```

### Performance Optimization

Every component must implement appropriate performance optimizations:

```typescript
function OptimizedComponent({ items, onItemSelect }) {
  // Memoize expensive calculations
  const sortedItems = useMemo(() => {
    return [...items].sort((a, b) => a.name.localeCompare(b.name));
  }, [items]);
  
  // Memoize callback functions passed to child components
  const handleItemSelect = useCallback((id: string) => {
    onItemSelect(id);
  }, [onItemSelect]);
  
  // Prevent layout thrashing with useLayoutEffect for DOM measurements
  const containerRef = useRef<HTMLDivElement>(null);
  useLayoutEffect(() => {
    if (containerRef.current) {
      // Measure and adjust layout if needed
      const { height } = containerRef.current.getBoundingClientRect();
      // Apply calculations based on measurements
    }
  }, [items]);
  
  // Cleanup side effects properly
  useEffect(() => {
    const subscription = subscribeToData();
    
    return () => {
      subscription.unsubscribe();
    };
  }, []);
  
  return (
    <div ref={containerRef}>
      {sortedItems.map(item => (
        <Item 
          key={item.id}
          data={item}
          onSelect={handleItemSelect}
        />
      ))}
    </div>
  );
}
```

### Context for Shared State

Use context for state that needs to be shared across multiple components:

```typescript
// Create context with proper typing
interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Context provider component
export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  
  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  }, []);
  
  // Memoize context value to prevent unnecessary re-renders
  const value = useMemo(() => ({ theme, toggleTheme }), [theme, toggleTheme]);
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// Custom hook for consuming the context
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
```

### State Management Best Practices

1. **Co-locate state** with the components that use it
2. **Lift state up** only as high as necessary in the component tree
3. **Use reducers for complex state** with multiple sub-values or transitions
4. **Memoize expensive calculations** and callback functions
5. **Implement proper cleanup** in useEffect hooks
6. **Use TypeScript** for type safety in all state operations
7. **Split context by domain** rather than having a single global context
8. **Consider component composition** instead of complex state management when possible

## MSW Handler Implementation

```typescript
/**
 * @name createApiHandlers
 * @description Factory function to generate comprehensive MSW handlers for REST API endpoints
 * 
 * This implementation supports:
 * - Full CRUD operations (GET, POST, PUT, PATCH, DELETE)
 * - Collection and single-item endpoints
 * - Query parameter parsing for pagination, filtering, sorting
 * - Simulated network conditions (latency, errors)
 * - Response transformation and enrichment
 * - Stateful mock data persistence during session
 * - Event callbacks for testing specific scenarios
 * 
 * @example
 * // Basic usage
 * export const userHandlers = createApiHandlers({
 *   endpoint: '/api/users',
 *   mockData: userMockData,
 *   entityName: 'user'
 * });
 * 
 * @example
 * // Advanced usage with all options
 * export const productHandlers = createApiHandlers({
 *   endpoint: '/api/products',
 *   mockData: productMockData,
 *   entityName: 'product',
 *   idField: 'productId',
 *   latency: {
 *     min: 150,
 *     max: 350
 *   },
 *   errorRate: 0.05,
 *   relationships: {
 *     categories: {
 *       endpoint: '/api/categories',
 *       idField: 'categoryId'
 *     }
 *   },
 *   queryParams: {
 *     custom: ['status', 'category', 'price_min', 'price_max']
 *   },
 *   callbacks: {
 *     onGetCollection: (data, request, params) => {
 *       // Transform collection data before sending response
 *     }
 *   }
 * });
 * 
 * LLMs: Feel free to extend this with your own features like:
 * - GraphQL support
 * - WebSocket simulation
 * - File upload handlers
 * - Authentication/authorization simulation
 * - Rate limiting
 * - Caching behavior simulation
 */
function createApiHandlers(config) {
  // [LLM: Destructure and set defaults for configuration]
  const { 
    endpoint, 
    mockData, 
    entityName = 'item',
    idField = 'id',
    // ... more config options with defaults
  } = config;
  
  // Private state for this handler set
  // [LLM: Add state management for CRUD operations persistence]
  let currentData = [...mockData];
  
  /**
   * Parse query parameters from URL
   * [LLM: Expand this function to handle custom sorting, filtering, etc.]
   */
  function parseQueryParams(url) {
    // Standard pagination params
    const page = Number(url.searchParams.get('page') || 1);
    const pageSize = Number(url.searchParams.get('perPage') || 25);
    
    // Search/filtering
    const search = url.searchParams.get('search') || '';
    
    // Sorting
    const sortBy = url.searchParams.get('sortBy') || idField;
    const sortOrder = url.searchParams.get('sortOrder') || 'asc';
    
    // Simulation controls
    const simulate = url.searchParams.get('simulate') || '';
    const delay = Number(url.searchParams.get('delay') || 0);
    
    // [LLM: Add your own custom parameter parsing logic]
    
    return { 
      page, 
      pageSize, 
      search, 
      sortBy, 
      sortOrder, 
      simulate,
      delay,
      // ... custom params
    };
  }
  
  /**
   * Apply consistent error simulation
   * [LLM: Enhance with more error types and configurable status codes]
   */
  function simulateErrors(request, simulate) {
    // [LLM: Add error simulation logic]
  }
  
  /**
   * Apply network condition simulation
   * [LLM: Add latency/bandwidth simulation]
   */
  function simulateNetworkConditions(delay, errorRate) {
    // [LLM: Add network condition simulation]
  }
  
  // [LLM: Add helper functions for filtering, sorting, relationships]
  
  return [
    // GET collection handler
    http.get(endpoint, async ({ request }) => {
      // [LLM: Implement GET collection with pagination, filtering, sorting]
      
      // Parse query parameters
      const params = parseQueryParams(new URL(request.url));
      
      // Optional error simulation
      if (params.simulate === 'error') {
        // [LLM: Return appropriate error response]
      }
      
      // Apply filtering
      let filteredData = [...currentData];
      if (params.search) {
        // [LLM: Add search implementation]
      }
      
      // Apply sorting
      // [LLM: Add sorting implementation]
      
      // Apply pagination
      const total = filteredData.length;
      const lastPage = Math.ceil(total / params.pageSize);
      const offset = (params.page - 1) * params.pageSize;
      const paginatedData = filteredData.slice(offset, offset + params.pageSize);
      
      // Add artificial delay
      if (params.delay) {
        // [LLM: Add delay implementation]
      }
      
      // Transform data if needed
      if (config.callbacks?.onGetCollection) {
        // [LLM: Apply transformation callback]
      }
      
      // Return response with pagination metadata
      return HttpResponse.json({
        data: paginatedData,
        meta: {
          total,
          page: params.page,
          lastPage,
          pageSize: params.pageSize
        }
      });
    }),
    
    // GET single item handler
    http.get(`${endpoint}/:id`, async ({ params, request }) => {
      // [LLM: Implement GET single item with relationship support]
      
      const id = params.id;
      const item = currentData.find(item => String(item[idField]) === String(id));
      
      // Parse query params and handle includes
      const queryParams = parseQueryParams(new URL(request.url));
      
      if (!item) {
        // [LLM: Return 404 response]
      }
      
      // Handle includes/relationships
      // [LLM: Add relationship handling]
      
      return HttpResponse.json({ data: item });
    }),
    
    // POST create handler
    http.post(endpoint, async ({ request }) => {
      // [LLM: Implement POST create with validation]
      
      // Parse request body
      const body = await request.json();
      
      // Validate required fields
      // [LLM: Add validation logic]
      
      // Create new item
      const newItem = {
        [idField]: generateId(), // [LLM: Implement ID generation]
        ...body,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Add to collection
      currentData.push(newItem);
      
      // [LLM: Apply any post-creation hooks]
      
      return HttpResponse.json(
        { data: newItem },
        { status: 201 }
      );
    }),
    
    // PUT update handler
    http.put(`${endpoint}/:id`, async ({ params, request }) => {
      // [LLM: Implement PUT update with validation and concurrency checks]
      
      const id = params.id;
      const index = currentData.findIndex(item => String(item[idField]) === String(id));
      
      if (index === -1) {
        // [LLM: Return 404 response]
      }
      
      // Parse request body
      const body = await request.json();
      
      // Update item
      const updatedItem = {
        ...currentData[index],
        ...body,
        updatedAt: new Date().toISOString()
      };
      
      // Replace in collection
      currentData[index] = updatedItem;
      
      return HttpResponse.json({ data: updatedItem });
    }),
    
    // PATCH partial update handler
    http.patch(`${endpoint}/:id`, async ({ params, request }) => {
      // [LLM: Implement PATCH update - similar to PUT but for partial updates]
    }),
    
    // DELETE handler
    http.delete(`${endpoint}/:id`, async ({ params, request }) => {
      // [LLM: Implement DELETE with cascading delete options]
      
      const id = params.id;
      const index = currentData.findIndex(item => String(item[idField]) === String(id));
      
      if (index === -1) {
        // [LLM: Return 404 response]
      }
      
      // Remove from collection
      const deletedItem = currentData[index];
      currentData.splice(index, 1);
      
      // [LLM: Handle cascading deletes for relationships if configured]
      
      return HttpResponse.json({ 
        success: true,
        deleted: deletedItem[idField]  
      });
    }),
    
    // [LLM: Add additional custom handlers as needed]
    // Examples:
    // - Bulk operations
    // - Export data
    // - Import data
    // - Special actions
  ];
}

/**
 * Example usage for a UserProfile component
 */
export const userProfileHandlers = createApiHandlers({
  endpoint: '/api/users',
  mockData: userProfileMockData,
  // [LLM: Add your configuration options]
});

/**
 * Advanced usage with complex configuration
 * [LLM: Create your own example with advanced features]
 */
```

## Mock Data with faker-js

```typescript
/**
 * @title Enterprise-Grade Mock Data Generation System
 * @description A comprehensive approach to generating realistic mock data for frontend development,
 * testing, and demonstrations. This system follows best practices from companies like Google, Meta,
 * and Microsoft for generating high-quality, consistent test data.
 * 
 * Key features:
 * - Deterministic generation with seedable randomness
 * - Configurable volume control (memory optimization)
 * - Realistic data distributions reflecting real-world patterns
 * - Industry-specific data generators
 * - Localization support for international testing
 * - Relationship modeling with referential integrity
 * - Schema validation of generated data
 * - Performance optimized for large datasets
 * 
 * @usage
 * 1. Define your schema and relationships
 * 2. Configure generators with appropriate constraints
 * 3. Generate initial dataset
 * 4. Export for MSW handlers or import directly
 */

// ----------------------------------------------------------------
// Configuration and Environment Settings
// ----------------------------------------------------------------

/**
 * @name MockDataConfig
 * @description Configuration settings for the mock data generation system
 * 
 * LLMs: Extend this interface with your own configuration needs. Consider:
 * - Specific industry requirements
 * - Performance constraints
 * - Special data distribution needs
 */
interface MockDataConfig {
  /** Control randomness with consistent seed for reproducible results */
  seed?: number;
  
  /** Control the volume of data generated to optimize memory usage */
  volume: {
    /** Minimum number of records to generate */
    min: number;
    /** Maximum number of records to generate */
    max: number;
    /** Target number of records (defaults to random between min-max) */
    target?: number;
  };
  
  /** Locale settings for internationalized data */
  locale?: string | string[];
  
  /** Control data fidelity and complexity */
  fidelity: 'basic' | 'standard' | 'high' | 'ultra';
  
  /** Domain-specific settings */
  domain?: 'general' | 'healthcare' | 'finance' | 'ecommerce' | 'education' | string;
  
  /** Performance optimization settings */
  performance?: {
    /** Use worker threads for generation (Node.js) */
    useWorkers?: boolean;
    /** Batch size for chunked generation */
    batchSize?: number;
  };
  
  // [LLM: Add additional configuration options relevant to your use case]
}

// ----------------------------------------------------------------
// Core Functions
// ----------------------------------------------------------------

/**
 * Initialize faker with consistent seed for reproducible results
 * [LLM: Enhance this with more sophisticated seeding strategies]
 */
function initializeMockDataGenerator(config: MockDataConfig) {
  // Set consistent seed if provided
  if (config.seed) {
    faker.seed(config.seed);
  }
  
  // Set locale(s) for internationalized data
  if (config.locale) {
    // [LLM: Implement locale configuration]
  }
  
  // [LLM: Add any additional initialization steps]
  
  return {
    // Return factory functions and utilities
    generateMockData,
    createFactory,
    relationshipHelpers,
    // [LLM: Add additional utilities]
  };
}

// ----------------------------------------------------------------
// Record Volume Control
// ----------------------------------------------------------------

// Calculate optimal record count based on config and environment
const RECORD_COUNT = (() => {
  // [LLM: Implement record count calculation logic]
  
  // Example implementation
  const minRecords = config.volume.min || 500;
  const maxRecords = config.volume.max || 2000;
  
  // If target is specified, use it directly
  if (config.volume.target) {
    return config.volume.target;
  }
  
  // Otherwise generate a random count between min and max
  return faker.number.int({ min: minRecords, max: maxRecords });
})();

// ----------------------------------------------------------------
// Factory Function System
// ----------------------------------------------------------------

/**
 * @name FactoryFunctionOptions
 * @description Options for factory functions to control data generation
 * 
 * LLMs: Extend this interface with options relevant to your specific entities
 */
interface FactoryFunctionOptions {
  /** Probability settings for optional fields */
  probabilities?: Record<string, number>;
  
  /** Constraints for specific fields */
  constraints?: Record<string, any>;
  
  /** Distribution patterns for numeric/date values */
  distributions?: Record<string, 'uniform' | 'normal' | 'exponential' | 'pareto' | Distribution>;
  
  /** Field overrides for specific test cases */
  overrides?: Record<string, any>;
  
  // [LLM: Add additional options relevant to your use case]
}

/**
 * Create a factory function for a specific entity type
 * [LLM: Enhance this with more sophisticated factory patterns]
 */
function createFactory<T>(entityName: string, generator: (options?: FactoryFunctionOptions) => T) {
  // [LLM: Implement factory function creation]
  return generator;
}

// ----------------------------------------------------------------
// Complex Nested Data Factories
// ----------------------------------------------------------------

/**
 * Factory registry for complex nested data
 * [LLM: Add your own specialized factories for your domain]
 */
const mockDataFactories = {
  /**
   * Generate realistic contact information
   * [LLM: Enhance with more sophisticated contact data generation]
   */
  createContact: (options?: FactoryFunctionOptions) => ({
    email: faker.internet.email(),
    phone: faker.phone.number(),
    address: {
      street: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      country: faker.location.country(),
      zipCode: faker.location.zipCode(),
    },
    // [LLM: Add additional contact fields relevant to your domain]
  }),
  
  /**
   * Generate realistic engagement statistics
   * [LLM: Enhance with domain-specific statistics generation]
   */
  createStatistics: (options?: FactoryFunctionOptions) => {
    // [LLM: Implement statistics generation with realistic distributions]
    
    // Example implementation
    return {
      views: faker.number.int({ min: 0, max: 10000 }),
      likes: faker.number.int({ min: 0, max: 1000 }),
      shares: faker.number.int({ min: 0, max: 500 }),
      rating: faker.number.float({ min: 0, max: 5, precision: 0.1 }),
      // [LLM: Add additional statistics relevant to your domain]
    };
  },
  
  /**
   * Generate realistic product information
   * [LLM: Implement product data generation]
   */
  createProduct: (options?: FactoryFunctionOptions) => {
    // [LLM: Implement product generation]
  },
  
  /**
   * Generate realistic user profile
   * [LLM: Implement user profile generation]
   */
  createUserProfile: (options?: FactoryFunctionOptions) => {
    // [LLM: Implement user profile generation]
  },
  
  // [LLM: Add additional factories for your domain entities]
};

// ----------------------------------------------------------------
// Relationship Modeling Helpers
// ----------------------------------------------------------------

/**
 * Utilities for managing entity relationships
 * [LLM: Enhance with more sophisticated relationship modeling]
 */
const relationshipHelpers = {
  /**
   * Create a one-to-many relationship
   * [LLM: Implement one-to-many relationship generation]
   */
  oneToMany: (parentEntity, childFactory, count, options) => {
    // [LLM: Implement one-to-many relationship generation]
  },
  
  /**
   * Create a many-to-many relationship
   * [LLM: Implement many-to-many relationship generation]
   */
  manyToMany: (entityA, entityB, options) => {
    // [LLM: Implement many-to-many relationship generation]
  },
  
  /**
   * Create a one-to-one relationship
   * [LLM: Implement one-to-one relationship generation]
   */
  oneToOne: (entityA, entityB, options) => {
    // [LLM: Implement one-to-one relationship generation]
  },
  
  // [LLM: Add additional relationship helpers]
};

// ----------------------------------------------------------------
// Primary Mock Item Generator
// ----------------------------------------------------------------

/**
 * Generate a single mock item with realistic properties
 * [LLM: Enhance this with domain-specific generation logic]
 * 
 * @param {FactoryFunctionOptions} options - Configuration options
 * @returns {MockItem} A generated mock item
 */
function generateMockItem(options: FactoryFunctionOptions = {}) {
  // Extract option values with defaults
  const {
    probabilities = {},
    constraints = {},
    distributions = {},
    overrides = {},
  } = options;
  
  // Default probability for optional fields
  const defaultProbability = probabilities._default || 0.7;
  
  // Base item with required fields
  const item = {
    // Unique identifier - always required
    id: overrides.id || faker.string.uuid(),
    
    // Basic information
    name: overrides.name || faker.person.fullName(),
    
    // Status with controlled distribution
    status: overrides.status || faker.helpers.arrayElement(['draft', 'published', 'archived']),
    
    // Timestamps with realistic sequence
    createdAt: overrides.createdAt || faker.date.past().toISOString(),
    updatedAt: overrides.updatedAt || faker.date.recent().toISOString(),
    
    // Media assets
    avatarUrl: overrides.avatarUrl || faker.image.avatar(),
    
    // [LLM: Add additional base fields relevant to your domain]
  };
  
  // Add optional fields with controlled probability
  if (faker.helpers.maybe(() => true, { probability: probabilities.description || defaultProbability })) {
    item.description = overrides.description || faker.lorem.paragraph();
  }
  
  // Generate tags with variable count
  item.tags = overrides.tags || Array.from(
    { length: faker.number.int({ min: 0, max: constraints.maxTags || 5 }) },
    () => faker.word.sample()
  );
  
  // Add complex nested objects
  item.contact = overrides.contact || mockDataFactories.createContact({
    probabilities: probabilities.contact,
    constraints: constraints.contact,
    overrides: overrides.contact,
  });
  
  item.statistics = overrides.statistics || mockDataFactories.createStatistics({
    probabilities: probabilities.statistics,
    constraints: constraints.statistics,
    overrides: overrides.statistics,
  });
  
  // [LLM: Add additional domain-specific fields and relationships]
  
  return item;
}

// ----------------------------------------------------------------
// Dataset Generation and Export
// ----------------------------------------------------------------

/**
 * Generate a complete dataset with specified options
 * [LLM: Enhance with more sophisticated dataset generation]
 */
function generateMockData(count = RECORD_COUNT, options: FactoryFunctionOptions = {}) {
  console.log(`Generating ${count} mock items...`);
  
  // Performance optimization for large datasets
  if (count > 10000 && options.performance?.useWorkers) {
    // [LLM: Implement worker-based generation for large datasets]
    console.log('Using worker threads for large dataset generation');
  }
  
  // Generate the dataset
  const startTime = performance.now();
  
  const mockData = Array.from(
    { length: count },
    (_, index) => generateMockItem({
      ...options,
      // Allow for index-based customization
      overrides: {
        ...options.overrides,
        _index: index,
      },
    })
  );
  
  const endTime = performance.now();
  console.log(`Generated ${count} items in ${((endTime - startTime) / 1000).toFixed(2)}s`);
  
  return mockData;
}

// ----------------------------------------------------------------
// Usage Examples
// ----------------------------------------------------------------

/**
 * Basic usage example
 * [LLM: Add your own usage examples]
 */
export const basicMockData = generateMockData();

/**
 * Domain-specific example with constraints
 * [LLM: Implement domain-specific examples]
 */
export const ecommerceMockData = generateMockData(1000, {
  probabilities: {
    description: 0.9,
    discount: 0.3,
  },
  constraints: {
    maxTags: 8,
    priceRange: { min: 5, max: 500 },
  },
  // [LLM: Add domain-specific options]
});

/**
 * Example with realistic distribution patterns
 * [LLM: Implement examples with realistic distributions]
 */
export const realisticUserData = generateMockData(5000, {
  distributions: {
    // Realistic age distribution follows normal distribution
    age: {
      type: 'normal',
      mean: 32,
      standardDeviation: 14,
      min: 18,
      max: 85,
    },
    // Revenue follows pareto distribution (80/20 rule)
    revenue: {
      type: 'pareto',
      alpha: 1.5,
      scale: 100,
    },
    // [LLM: Add more realistic distributions]
  },
  // [LLM: Add additional options]
});

// ----------------------------------------------------------------
// Export the generated data
// ----------------------------------------------------------------

export const mockData = generateMockData();
```

## Skeleton Implementation

```typescript
// Skeleton component
export const ComponentSkeleton = ({
  count = 5,
  className = '',
  disableAnimation = false,
}) => {
  const WrappedSkeleton = ({ children }) => {
    if (disableAnimation) {
      return <>{children}</>;
    }
    
    return (
      <motion.div
        initial="initial"
        animate="animate"
        variants={skeletonAnimationVariants}
      >
        {children}
      </motion.div>
    );
  };

  return (
    <div className={className}>
      {Array.from({ length: count }).map((_, index) => (
        <WrappedSkeleton key={index}>
          <div className="flex items-start gap-4 mb-4 p-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-[250px]" />
              <Skeleton className="h-3 w-[180px]" />
            </div>
          </div>
        </WrappedSkeleton>
      ))}
    </div>
  );
};
```

## Type Definitions

```typescript
export interface ComponentData {
  /** Unique identifier */
  id: string;
  
  /** Display name */
  name: string;
  
  /** Current status */
  status: ComponentStatus;
  
  /** Creation timestamp */
  createdAt: string;
  
  /** Last update timestamp */
  updatedAt: string;
  
  /** Optional description */
  description?: string;
  
  /** Associated tags */
  tags: string[];
  
  /** Configuration settings */
  settings: ComponentSettings;
}

export enum ComponentStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

export interface ComponentSettings {
  /** Visibility setting */
  isVisible: boolean;
  
  /** Feature flags */
  features: {
    /** Enable advanced features */
    advanced: boolean;
    /** Enable experimental features */
    experimental: boolean;
  };
}

export interface ComponentProps {
  /** Initial data to display */
  initialData?: ComponentData[];
  
  /** Loading state */
  isLoading?: boolean;
  
  /** Error state */
  error?: Error;
  
  /** Display variant */
  variant?: 'default' | 'compact' | 'expanded';
  
  /** Custom CSS class */
  className?: string;
}
```

## Network State Awareness

```typescript
// Network status hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(onlineManager.isOnline());
  
  useEffect(() => {
    const unsubscribe = onlineManager.setEventListener(online => {
      setIsOnline(online);
    });
    
    return unsubscribe;
  }, []);
  
  return { isOnline };
}

// Component with network awareness
function useComponentData(options) {
  const { isOnline } = useNetworkStatus();
  
  const query = useQuery(
    createNetworkAwareQueryOptions({
      queryKey: options.queryKey,
      queryFn: async () => {
        if (!isOnline) {
          throw new Error('You are offline');
        }
        
        const response = await fetch(options.endpoint);
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        
        return response.json();
      },
      retry: isOnline ? 3 : false,
      initialData: options.initialData,
      offlineFallbackData: options.offlineFallbackData,
      refetchOnReconnect: true,
    })
  );
  
  return {
    ...query,
    isOnline,
  };
}
```

## Storybook Stories

```typescript
// stories.tsx
const meta: Meta<typeof Component> = {
  title: 'Components/Component',
  component: Component,
  parameters: {
    msw: {
      handlers: ComponentHandlers,
    },
    layout: 'padded',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'compact', 'expanded'],
    },
  },
  args: {
    variant: 'default',
  },
  tags: ['autodocs']
};

export default meta;
type Story = StoryObj<typeof Component>;

// Default story
export const Default: Story = {
  name: 'Default',
  args: {}
};

// Loading state
export const Loading: Story = {
  name: 'Loading State',
  args: {
    isLoading: true
  }
};

// Error state
export const Error: Story = {
  name: 'Error State',
  args: {
    error: new Error('Failed to load data')
  }
};

// Empty state
export const Empty: Story = {
  name: 'Empty State',
  args: {
    initialData: []
  }
};
```

## Responsive Design System

All components must be responsive across these breakpoints:

| Breakpoint | Size (pixels) | Target Devices            |
|------------|---------------|---------------------------|
| '3xs'      | 256px         | Tiny displays, watches    |
| '2xs'      | 288px         | Extra small displays      |
| 'xs'       | 320px         | Small phones              |
| 'sm'       | 384px         | Phones                    |
| 'md'       | 448px         | Large phones              |
| 'lg'       | 512px         | Small tablets             |
| 'xl'       | 576px         | Tablets                   |
| '2xl'      | 672px         | Large tablets             |
| '3xl'      | 768px         | Small laptops             |
| '4xl'      | 896px         | Laptops                   |
| '5xl'      | 1024px        | Desktops                  |
| '6xl'      | 1152px        | Large desktops            |
| '7xl'      | 1280px        | Extra large desktops      |

```typescript
// Responsive hooks
function useResponsive() {
  const [breakpoint, setBreakpoint] = useState<BreakpointKey>('md');
  
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width < 256) setBreakpoint('3xs');
      else if (width < 288) setBreakpoint('2xs');
      else if (width < 320) setBreakpoint('xs');
      else if (width < 384) setBreakpoint('sm');
      else if (width < 448) setBreakpoint('md');
      else if (width < 512) setBreakpoint('lg');
      else if (width < 576) setBreakpoint('xl');
      else if (width < 672) setBreakpoint('2xl');
      else if (width < 768) setBreakpoint('3xl');
      else if (width < 896) setBreakpoint('4xl');
      else if (width < 1024) setBreakpoint('5xl');
      else if (width < 1152) setBreakpoint('6xl');
      else setBreakpoint('7xl');
    };
    
    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);
  
  return breakpoint;
}

// Component with responsive props
interface ResponsiveComponentProps {
  responsiveVariants?: Partial<Record<BreakpointKey, {
    variant?: string;
    size?: string;
    layout?: 'stack' | 'grid' | 'row';
  }>>;
  hideOnBreakpoint?: BreakpointKey[];
}

function ResponsiveComponent({ 
  responsiveVariants,
  hideOnBreakpoint,
  ...defaultProps 
}: ResponsiveComponentProps) {
  const breakpoint = useResponsive();
  
  if (hideOnBreakpoint?.includes(breakpoint)) {
    return null;
  }
  
  const propsForBreakpoint = {
    ...defaultProps,
    ...(responsiveVariants?.[breakpoint] || {})
  };
  
  return (
    <div className={`responsive-component size-${propsForBreakpoint.size}`}>
      {/* Component implementation */}
    </div>
  );
}
```

## Reuse Strategy

Before implementing any component:

1. Check `@/components/ui/index.ts` first for existing components
2. Only create custom components when:
   - The component has unique business logic
   - It requires specialized behavior not in the UI library
   - It's specifically requested to be implemented from scratch
3. Tables are special - always use TanStack Table directly

## Core Principles Summary

1. **Production Parity**: Identical behavior in development and production
2. **Full Optimization**: Performance-first with virtualization
3. **Comprehensive Design**: Mobile-first, accessible, with global theming
4. **Framework Integration**: Works with TanStack, shadcn/ui, Framer Motion
5. **Developer Experience**: Consistent patterns and comprehensive testing 