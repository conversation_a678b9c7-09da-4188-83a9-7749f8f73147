import{u as b}from"./chunk-G3QXMPRB-BXa5-FQN.js";import{q as v,a as P,j as t,p as C,s as T,b as f,c as D,d as L,e as S,k,H as q,B as c,L as l,O as w,r as A,f as E,A as z,t as m}from"./index-Bwql5Dzz.js";import{u as Q,_}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{u as H,a as R}from"./chunk-U6CSGYH6-BpcURsBT.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as B}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as K}from"./use-prompt-pbDx0Sfe.js";import{P as M}from"./pencil-square-6wRbnn1C.js";import{T as N}from"./trash-BBylvTAG.js";import{C as O}from"./container-Dqi2woPF.js";import{c as $}from"./index-BxZ1678G.js";import"./chunk-IQBAUTU5-D_4dFOf0.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var F=()=>({queryKey:C.list({limit:20,offset:0}),queryFn:async()=>T.admin.product.list({limit:20,offset:0})}),we=e=>async()=>{const s=F();return v.getQueryData(s.queryKey)??await e.fetchQuery(s)},d=20,I=()=>{const{t:e}=f(),s=D(),r=L(),{searchParams:o,raw:i}=H({pageSize:d}),{products:u,count:a,isLoading:x,isError:y,error:h}=S({...o},{initialData:r,placeholderData:k}),g=R(),p=Y(),{table:j}=Q({data:u??[],columns:p,count:a,enablePagination:!0,pageSize:d,getRowId:n=>n.id});if(y)throw h;return t.jsxs(O,{className:"divide-y p-0",children:[t.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[t.jsx(q,{level:"h2",children:e("products.domain")}),t.jsxs("div",{className:"flex items-center justify-center gap-x-2",children:[t.jsx(c,{size:"small",variant:"secondary",asChild:!0,children:t.jsx(l,{to:`export${s.search}`,children:e("actions.export")})}),t.jsx(c,{size:"small",variant:"secondary",asChild:!0,children:t.jsx(l,{to:"import",children:e("actions.import")})}),t.jsx(c,{size:"small",variant:"secondary",asChild:!0,children:t.jsx(l,{to:"create",children:e("actions.create")})})]})]}),t.jsx(_,{table:j,columns:p,count:a,pageSize:d,filters:g,search:!0,pagination:!0,isLoading:x,queryObject:i,navigateTo:n=>`${n.original.id}`,orderBy:[{key:"title",label:e("fields.title")},{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}],noRecords:{message:e("products.list.noRecordsMessage")}}),t.jsx(w,{})]})},W=({product:e})=>{const{t:s}=f(),r=K(),{mutateAsync:o}=E(e.id),i=async()=>{await r({title:s("general.areYouSure"),description:s("products.deleteWarning",{title:e.title}),confirmText:s("actions.delete"),cancelText:s("actions.cancel")})&&await o(void 0,{onSuccess:()=>{m.success(s("products.toasts.delete.success.header"),{description:s("products.toasts.delete.success.description",{title:e.title})})},onError:a=>{m.error(s("products.toasts.delete.error.header"),{description:a.message})}})};return t.jsx(z,{groups:[{actions:[{icon:t.jsx(M,{}),label:s("actions.edit"),to:`/products/${e.id}/edit`}]},{actions:[{icon:t.jsx(N,{}),label:s("actions.delete"),onClick:i}]}]})},G=$(),Y=()=>{const e=b();return A.useMemo(()=>[...e,G.display({id:"actions",cell:({row:r})=>t.jsx(W,{product:r.original})})],[e])},Ae=()=>{const{getWidgets:e}=P();return t.jsx(B,{widgets:{after:e("product.list.after"),before:e("product.list.before")},children:t.jsx(I,{})})};export{Ae as Component,we as productLoader};
