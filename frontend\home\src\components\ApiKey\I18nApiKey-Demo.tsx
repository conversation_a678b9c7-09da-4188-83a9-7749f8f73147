'use client';

import * as React from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { Button } from '@/components/ui/button';
import { I18nCreate } from './I18nCreate';
import { I18nEdit } from './I18nEdit';
import type { <PERSON><PERSON><PERSON><PERSON> } from './ApiKey-Types';
import type { I18nApiKeyFormValues } from './ApiKey-i18nSchema';
import type { ApiKeyFormValues } from './ApiKey-Schema';

const SAMPLE_PROJECTS = [
  { value: 'project1', label: 'ZoomThai' },
  { value: 'project2', label: 'Project Alpha' },
  { value: 'project3', label: 'Demo Project' },
];

const SAMPLE_API_KEY: ApiKey = {
  id: 'key-123456',
  name: 'songpra-parser',
  permissionType: 'Restricted',
  resourcePermissions: {
    models: 'Read',
    modelCapabilities: 'Write',
    assistants: 'None',
    threads: 'None',
    evals: 'None',
    fineTuning: 'None',
    files: 'Read',
  },
  createdAt: new Date().toISOString(),
  lastUsed: new Date().toISOString(),
};

/**
 * Internationalized Demo component for API key forms
 */
export const I18nApiKeyDemo: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [showCreate, setShowCreate] = React.useState(false);
  const [showEdit, setShowEdit] = React.useState(false);
  const [formData, setFormData] = React.useState<I18nApiKeyFormValues | null>(null);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleCreateSubmit = (data: ApiKeyFormValues) => {
    setIsSubmitting(true);
    try {
      // Simulate API request
      setTimeout(() => {
        setFormData(data as unknown as I18nApiKeyFormValues);
        setShowCreate(false);
        setIsSubmitting(false);
      }, 1500);
    } catch (error) {
      console.error('Error submitting form:', error);
      setIsSubmitting(false);
    }
  };

  const handleEditSubmit = (data: ApiKeyFormValues) => {
    setIsSubmitting(true);
    try {
      // Simulate API request
      setTimeout(() => {
        setFormData(data as unknown as I18nApiKeyFormValues);
        setShowEdit(false);
        setIsSubmitting(false);
      }, 1500);
    } catch (error) {
      console.error('Error submitting form:', error);
      setIsSubmitting(false);
    }
  };

  // Language switching
  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <div className="container mx-auto space-y-8 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">API Key Management Demo (i18n)</h1>
        <div className="flex items-center space-x-4">
          {/* Language switcher */}
          <div className="mr-6 flex space-x-2">
            <Button
              size="sm"
              variant={i18n.language === 'en' ? 'default' : 'outline'}
              onClick={() => changeLanguage('en')}
            >
              English
            </Button>
            <Button
              size="sm"
              variant={i18n.language === 'fr' ? 'default' : 'outline'}
              onClick={() => changeLanguage('fr')}
            >
              Français
            </Button>
            <Button
              size="sm"
              variant={i18n.language === 'ja' ? 'default' : 'outline'}
              onClick={() => changeLanguage('ja')}
            >
              日本語
            </Button>
          </div>

          {/* Action buttons */}
          <Button onClick={() => setShowCreate(true)}>{t('apiKey.actions.createKey')}</Button>
          <Button onClick={() => setShowEdit(true)}>{t('apiKey.titles.edit')}</Button>
        </div>
      </div>

      {/* Current language */}
      <div className="text-muted-foreground text-sm">Current language: {i18n.language}</div>

      {/* Form data display */}
      {formData && (
        <div className="bg-muted rounded-lg border p-4">
          <h2 className="mb-2 text-lg font-semibold">Submitted Form Data:</h2>
          <pre className="bg-background overflow-auto rounded p-2">
            {JSON.stringify(formData, null, 2)}
          </pre>
        </div>
      )}

      {/* Create form */}
      {showCreate && (
        <I18nCreate
          onSubmit={handleCreateSubmit}
          onCancel={() => setShowCreate(false)}
          isSubmitting={isSubmitting}
          availableProjects={SAMPLE_PROJECTS}
        />
      )}

      {/* Edit form */}
      {showEdit && (
        <I18nEdit
          apiKey={SAMPLE_API_KEY}
          onSubmit={handleEditSubmit}
          onCancel={() => setShowEdit(false)}
          isSubmitting={isSubmitting}
        />
      )}
    </div>
  );
};

export default I18nApiKeyDemo;
