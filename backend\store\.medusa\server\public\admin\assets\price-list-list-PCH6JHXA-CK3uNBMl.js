import{u as g}from"./chunk-UTVVA7WX-D3XS4FVr.js";import{g as b}from"./chunk-G2J2T2QU-DYdW7b3B.js";import{T as h,a as j}from"./chunk-MSDRGCRR-BLk8RuFZ.js";import{S as P}from"./chunk-ADOCJB6L-fVr5Yqi0.js";import{a as T,j as t,b as n,dz as L,H as v,T as y,B as C,L as S,r as k,k as D,A as _}from"./index-Bwql5Dzz.js";import{u as A,_ as w}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{u as z}from"./chunk-W7625H47-D4n0RxCV.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as E}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as q}from"./chunk-C76H5USB-ByRPKhW7.js";import{P as H}from"./pencil-square-6wRbnn1C.js";import{T as F}from"./trash-BBylvTAG.js";import{C as N}from"./container-Dqi2woPF.js";import{c as B}from"./index-BxZ1678G.js";import"./use-prompt-pbDx0Sfe.js";import"./prompt-BsR9zKsn.js";import"./chunk-6GU6IDUA-CDc7wW5L.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";var I=({priceList:e})=>{const{t:r}=n(),s=g({priceList:e});return t.jsx(_,{groups:[{actions:[{label:r("actions.edit"),to:`${e.id}/edit`,icon:t.jsx(H,{})}]},{actions:[{label:r("actions.delete"),onClick:s,icon:t.jsx(F,{})}]}]})},o=B(),M=()=>{const{t:e}=n();return k.useMemo(()=>[o.accessor("title",{header:()=>t.jsx(h,{text:e("fields.title")}),cell:r=>r.getValue()}),o.accessor("status",{header:e("priceLists.fields.status.label"),cell:({row:r})=>{const{color:s,text:a}=b(e,r.original);return t.jsx(P,{color:s,children:a})}}),o.accessor("prices",{header:e("priceLists.fields.priceOverrides.header"),cell:r=>{var s;return t.jsx(j,{text:`${((s=r.getValue())==null?void 0:s.length)||"-"}`})}}),o.display({id:"actions",cell:({row:r})=>t.jsx(I,{priceList:r.original})})],[e])},O=()=>z(),Q=({pageSize:e=20,prefix:r})=>{var i;const s=q(["offset","q","order","status"],r);return{searchParams:{limit:e,offset:s.offset?Number(s.offset):0,order:s.order,status:(i=s.status)==null?void 0:i.split(","),q:s.q},raw:s}},c=20,R=()=>{const{t:e}=n(),{searchParams:r,raw:s}=Q({pageSize:c}),{price_lists:a,count:i,isLoading:p,isError:d,error:u}=L(r,{placeholderData:D}),f=O(),m=M(),{table:x}=A({data:a||[],columns:m,count:i,enablePagination:!0,getRowId:l=>l.id,pageSize:c});if(d)throw u;return t.jsxs(N,{className:"divide-y p-0",children:[t.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[t.jsxs("div",{children:[t.jsx(v,{children:e("priceLists.domain")}),t.jsx(y,{className:"text-ui-fg-subtle",size:"small",children:e("priceLists.subtitle")})]}),t.jsx(C,{size:"small",variant:"secondary",asChild:!0,children:t.jsx(S,{to:"create",children:e("actions.create")})})]}),t.jsx(w,{table:x,columns:m,count:i,filters:f,orderBy:[{key:"title",label:e("fields.title")},{key:"status",label:e("fields.status")},{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}],queryObject:s,pageSize:c,navigateTo:l=>l.original.id,isLoading:p,pagination:!0,search:!0})]})},ke=()=>{const{getWidgets:e}=T();return t.jsx(E,{widgets:{after:e("price_list.list.after"),before:e("price_list.list.before")},children:t.jsx(R,{})})};export{ke as Component};
