import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, within } from '@storybook/test';
import { Create, Edit } from '..';
import I18nCreate from '../I18nCreate';
import I18nEdit from '../I18nEdit';
import { I18nextProvider } from 'react-i18next';
import { i18n } from '@/lib';
import {
  sampleProjects,
  sampleApiKeyRestricted,
  sampleApiKeyAll,
} from '../__fixtures__/ApiKey.fixtures';

// Ensure i18n is initialized
const i18nInstance = i18n.client.i18n;
i18nInstance.changeLanguage('en');

// Common wrapper for I18n stories
const I18nWrapper = ({ children, lang = 'en' }: { children: React.ReactNode; lang?: string }) => {
  // Set the language
  i18nInstance.changeLanguage(lang);

  return <I18nextProvider i18n={i18nInstance}>{children}</I18nextProvider>;
};

const meta = {
  title: 'Components/ApiKey/Viewport',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Responsive behavior of API Key components across different viewport sizes.',
      },
    },
    chromatic: {
      viewports: [375, 768, 1024, 1440],
      delay: 300, // Delay to ensure animations complete
    },
    viewport: {
      viewports: {
        mobile: {
          name: 'Mobile',
          styles: {
            width: '375px',
            height: '667px',
          },
        },
        tablet: {
          name: 'Tablet',
          styles: {
            width: '768px',
            height: '1024px',
          },
        },
        desktop: {
          name: 'Desktop',
          styles: {
            width: '1024px',
            height: '768px',
          },
        },
        largeDesktop: {
          name: 'Large Desktop',
          styles: {
            width: '1440px',
            height: '900px',
          },
        },
      },
      defaultViewport: 'desktop',
    },
  },
  decorators: [
    (Story) => (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 p-4 md:p-8">
        <div className="w-full max-w-md">
          <Story />
        </div>
      </div>
    ),
  ],
  tags: ['viewport', 'responsive'],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Demonstrates responsive behavior of the Create component across different viewports.
 */
export const CreateResponsive: Story = {
  render: () => (
    <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByText('Create new secret key')).toBeInTheDocument();
  },
};

/**
 * Mobile view of the Create component (375px width).
 */
export const CreateMobileView: Story = {
  render: () => (
    <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByText('Create new secret key')).toBeInTheDocument();
  },
};

/**
 * Tablet view of the Create component (768px width).
 */
export const CreateTabletView: Story = {
  render: () => (
    <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
  ),
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
};

/**
 * Desktop view of the Create component (1024px width).
 */
export const CreateDesktopView: Story = {
  render: () => (
    <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
  ),
  parameters: {
    viewport: {
      defaultViewport: 'desktop',
    },
  },
};

/**
 * Large desktop view of the Create component (1440px width).
 */
export const CreateLargeDesktopView: Story = {
  render: () => (
    <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
  ),
  parameters: {
    viewport: {
      defaultViewport: 'largeDesktop',
    },
  },
};

/**
 * Create component with Restricted permissions in mobile view.
 * Shows how resource permissions UI adapts to smaller screens.
 */
export const CreateWithRestrictedPermissionsMobile: Story = {
  render: () => (
    <div className="rounded-lg bg-white p-4 shadow-sm">
      <h3 className="text-muted-foreground mb-2 text-sm font-medium">
        Mobile View - Restricted Permissions
      </h3>
      <Create
        onSubmit={() => {}}
        onCancel={() => {}}
        availableProjects={sampleProjects.slice(0, 2)}
      />
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Click on Restricted button to show resource permissions
    const restrictedButton = canvas.getByText('Restricted');
    await restrictedButton.click();

    // Check that resources section appears
    await expect(canvas.getByText('Resources')).toBeInTheDocument();
  },
};

/**
 * Demonstrates responsive behavior of the Edit component across different viewports.
 */
export const EditResponsive: Story = {
  render: () => <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />,
};

/**
 * Mobile view of the Edit component (375px width).
 */
export const EditMobileView: Story = {
  render: () => <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />,
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
};

/**
 * Tablet view of the Edit component (768px width).
 */
export const EditTabletView: Story = {
  render: () => <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />,
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
};

/**
 * Internationalized Create API Key form with French language in mobile view.
 */
export const I18nCreateMobileView: Story = {
  render: () => (
    <I18nWrapper lang="fr">
      <I18nCreate onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
    </I18nWrapper>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const form = canvas.getByTestId('i18n-create-form');
    await expect(form).toBeInTheDocument();
  },
};

/**
 * Internationalized Edit API Key form with Japanese language in mobile view.
 */
export const I18nEditMobileView: Story = {
  render: () => (
    <I18nWrapper lang="ja">
      <I18nEdit apiKey={sampleApiKeyAll} onSubmit={() => {}} onCancel={() => {}} />
    </I18nWrapper>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const form = canvas.getByTestId('i18n-edit-form');
    await expect(form).toBeInTheDocument();
  },
};

/**
 * Side-by-side comparison of Create and Edit forms in tablet view.
 */
export const SideBySideTabletView: Story = {
  render: () => (
    <div className="flex w-full flex-col gap-4 md:flex-row">
      <div className="flex-1">
        <h3 className="mb-2 text-center text-sm font-medium">Create</h3>
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
      </div>
      <div className="flex-1">
        <h3 className="mb-2 text-center text-sm font-medium">Edit</h3>
        <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />
      </div>
    </div>
  ),
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
};
