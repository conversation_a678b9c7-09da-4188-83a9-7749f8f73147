import { f } from 'node_modules/msw/lib/core/HttpResponse-Cy7ytzUn';
import type { Config } from 'tailwindcss';

const config = {
  darkMode: ['class', "[data-mode='dark']"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    './src/components/*/.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      fontSize: {
        h1: [
          '36px',
          {
            fontWeight: '700',
          },
        ],
        h2: [
          '32px',
          {
            fontWeight: '600',
            magin: '0',
          },
        ],
        h3: [
          '24px',
          {
            fontWeight: '600',
          },
        ],
        h4: [
          '20px',
          {
            fontWeight: '600',
          },
        ],
        'body-16-reg': ['16px', { lineHeight: '24px', fontWeight: '400' }],
        'body-16-med': ['16px', { lineHeight: '24px', fontWeight: '500' }],
        'body-16-sem': ['16px', { lineHeight: '24px', fontWeight: '600' }],

        'body-18-reg': ['18px', { fontWeight: '400' }],
        'body-18-med': ['18px', { lineHeight: '24px', fontWeight: '500' }],
        'body-18-sem': ['18px', { lineHeight: '24px', fontWeight: '600' }],

        'subtitle-15-reg': ['15px', { fontWeight: '400' }],
        'subtitle-15-med': ['15px', { fontWeight: '500' }],

        'subtitle-14-reg': ['14px', { lineHeight: '20px', fontWeight: '400' }],
        'subtitle-14-med': ['14px', { lineHeight: '20px', fontWeight: '500' }],
        'subtitle-14-sem': ['14px', { lineHeight: '20px', fontWeight: '600' }],

        'button-14-med': ['14px', { fontWeight: '500' }],
        'button-12-med': ['12px', { fontWeight: '500' }],

        'text-base': ['20px', { fontWeight: '400' }],
        'text-lg': ['24px', { fontWeight: '400' }],
        'text-lg-bold': ['24px', { fontWeight: '700' }],
        'text-sm': ['16px', { fontWeight: '400' }],
        'text-md': ['18px', { fontWeight: '400' }],
        'text-xl-bold': ['28px', { fontWeight: '600' , lineHeight: '40px' }],
        'text-xl': ['32px', { fontWeight: '700' }],
        'text-2xl': ['36px', { fontWeight: '600' }],
        'text-3xl': ['40px', { fontWeight: '700' }],
        
        'text-placeholder': ['15px', { fontWeight: '400' }],
      },
      colors: {
        border: {
          DEFAULT: 'hsl(var(--border))', //#8D93A5
          foreground: 'hsl(var(--primary))',
          stroke: 'hsl(var(--color-stroke))', // #F0F0F0
        },
        placeholder: 'hsl(var(--placeholder))', //#8D93A5
        stroke: 'hsl(var(--color-stroke))', // #F0F0F0

        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: {
          DEFAULT: 'hsl(var(--background))', //#FFFFFF
          foreground: 'hsl(var(--background-BG))', //#F6F7F9
          Highlight: 'hsl(var(--Highlight))', //#F6F9FF
          Menu: 'hsl(var(--Menu))', //#DFEAF2
        },
        title: {
          DEFAULT: 'hsl(var(--title-main))', //#1F1F1F
          White: 'hsl(var(--White))', //#FFFFFF
          detail: 'hsl(var(--title-secondary))', //#494C6B
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))', //#121E72
          foreground: 'hsl(var(--primary-foreground))', //#FFFFFF
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))', //#FFAA00
          foreground: 'hsl(var(--secondary-foreground))',
          bg: 'hsl(var(--secondary-bg))', //#FFF6E5 >> #FFAA00 10%
        },
        discription: {
          DEFAULT: 'hsl(var(--discription))', //#5D6A85
          secondary: 'hsl(var(--discription-secondary))', //#494C6B
        },
        text: 'hsl(var(--primary))',
        foreground: 'hsl(var(--foreground))',
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },

        //success&error
        success: {
          DEFAULT: 'hsl(var(--success))', //#00BB7A
          foreground: 'hsl(var(--success-foreground))', //#E6F8F2
        },
        warning: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        error: {
          DEFAULT: 'hsl(var(--error))', //#FF3E40
          foreground: 'hsl(var(--error-foreground))', //#FFECEC
        },
      },

      //Main
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
        card: {
          DEFAULT: '12px',
          sm: '18px',
          md: '20px',
        },
        input: {
          DEFAULT: '8px',
          md: '12px',
        },
        banner: '24px',
        section: '16px',
      },

      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;
