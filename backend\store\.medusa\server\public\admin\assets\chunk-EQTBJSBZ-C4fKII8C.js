import{j as d,m as r}from"./index-Bwql5Dzz.js";var v=({size:e="small",children:i,className:s})=>d.jsx("div",{className:r("shadow-borders-base flex size-7 items-center justify-center","[&>div]:bg-ui-bg-field [&>div]:text-ui-fg-subtle [&>div]:flex [&>div]:size-6 [&>div]:items-center [&>div]:justify-center",{"size-7 rounded-md [&>div]:size-6 [&>div]:rounded-[4px]":e==="small","size-10 rounded-lg [&>div]:size-9 [&>div]:rounded-[6px]":e==="large","size-12 rounded-xl [&>div]:size-11 [&>div]:rounded-[10px]":e==="xlarge"},s),children:d.jsx("div",{children:i})});export{v as I};
