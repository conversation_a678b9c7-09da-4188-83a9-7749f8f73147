import{c as l}from"./chunk-6GU6IDUA-CDc7wW5L.js";import{ar as p}from"./index-Bwql5Dzz.js";var m=s=>{const r=s.starts_at,a=s.ends_at,n=a?new Date(a)<new Date:!1,i=r?new Date(r)>new Date:!1,o=s.status==="draft";return{isExpired:n,isScheduled:i,isDraft:o}},g=(s,r)=>{const{isExpired:a,isScheduled:n,isDraft:i}=m(r);let o=s("priceLists.fields.status.options.active"),e="green",t="active";return i&&(e="grey",o=s("priceLists.fields.status.options.draft"),t="draft"),a&&(e="red",o=s("priceLists.fields.status.options.expired"),t="expired"),n&&(e="orange",o=s("priceLists.fields.status.options.scheduled"),t="scheduled"),{color:e,text:o,status:t}},D=s=>"variants"in s,v=(s,r,a)=>{const n=(e,t,c)=>{var d;const u=t==="currency"?c:(d=a.find(f=>f.id===c))==null?void 0:d.currency_code;if(!u)throw p({message:"Currency code not found"},400);return{amount:l(e.amount),...t==="region"?{rules:{region_id:c}}:{},currency_code:u,variant_id:s}},i=Object.entries(r.currency_prices||{}).flatMap(([e,t])=>t!=null&&t.amount?[n(t,"currency",e)]:[]),o=Object.entries(r.region_prices||{}).flatMap(([e,t])=>t!=null&&t.amount?[n(t,"region",e)]:[]);return[...i,...o]},w=(s,r)=>Object.values(s).flatMap(({variants:a})=>Object.entries(a).flatMap(([n,i])=>v(n,i,r)));export{w as e,g,D as i};
