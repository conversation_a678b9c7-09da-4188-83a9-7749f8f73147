import { render, screen, fireEvent } from '@testing-library/react';
import { TimeConversionTooltip } from '../TimeConversionTooltip';
import { defaultTooltipData } from '../__fixtures__/TimeConversionTooltip.fixtures';

describe('TimeConversionTooltip', () => {
  it('renders children correctly', () => {
    render(<TimeConversionTooltip data={defaultTooltipData}>March 15, 2023</TimeConversionTooltip>);

    expect(screen.getByText('March 15, 2023')).toBeInTheDocument();
  });

  it('shows dotted underline on hover', () => {
    render(<TimeConversionTooltip data={defaultTooltipData}>March 15, 2023</TimeConversionTooltip>);

    const tooltipTrigger = screen.getByText('March 15, 2023');

    // Initially no dotted underline should be visible
    expect(document.querySelector('svg line')).not.toBeInTheDocument();

    // Hover over the tooltip trigger
    fireEvent.mouseEnter(tooltipTrigger);

    // Dotted underline should now be visible
    expect(document.querySelector('svg line')).toBeInTheDocument();

    // Move mouse away
    fireEvent.mouseLeave(tooltipTrigger);

    // Dotted underline should disappear
    expect(document.querySelector('svg line')).not.toBeInTheDocument();
  });

  it('disables tooltip functionality when enabled=false', () => {
    render(
      <TimeConversionTooltip data={defaultTooltipData} enabled={false}>
        March 15, 2023
      </TimeConversionTooltip>,
    );

    // Should still render the trigger
    expect(screen.getByText('March 15, 2023')).toBeInTheDocument();

    // HoverCard components should not be rendered
    expect(document.querySelector('[role="dialog"]')).not.toBeInTheDocument();
  });

  it('applies custom className to the component', () => {
    render(
      <TimeConversionTooltip data={defaultTooltipData} className="custom-class">
        March 15, 2023
      </TimeConversionTooltip>,
    );

    const tooltipTrigger = screen.getByText('March 15, 2023').closest('span');
    expect(tooltipTrigger).toHaveClass('custom-class');
  });

  it('forwards additional props to the root element', () => {
    render(
      <TimeConversionTooltip data={defaultTooltipData} data-testid="tooltip-test">
        March 15, 2023
      </TimeConversionTooltip>,
    );

    expect(screen.getByTestId('tooltip-test')).toBeInTheDocument();
  });
});
