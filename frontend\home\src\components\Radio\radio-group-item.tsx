'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { 
  RadioGroupItem as ShadcnRadioGroupItem 
} from '@/components/ui/radio-group';

const radioGroupItemVariants = cva(
  'border-primary text-primary focus-visible:ring-ring aspect-square h-4 w-4 rounded-full border shadow focus:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: '',
        colorful: 'before:absolute before:inset-0 before:rounded-full before:bg-current before:scale-0 data-[state=checked]:before:scale-100 before:transition-transform',
        branded: 'border-2',
        buttonCard: 'sr-only',
      },
      size: {
        default: 'h-4 w-4',
        sm: 'h-3 w-3',
        lg: 'h-5 w-5',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface RadioGroupItemProps 
  extends React.ComponentPropsWithoutRef<typeof ShadcnRadioGroupItem>,
    VariantProps<typeof radioGroupItemVariants> {
  label?: string;
  description?: string;
}

const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof ShadcnRadioGroupItem>,
  RadioGroupItemProps
>(({ 
  className, 
  variant, 
  size,
  label,
  description,
  children,
  ...props 
}, ref) => {
  if (variant === "buttonCard") {
    return (
      <div className="relative">
        <ShadcnRadioGroupItem
          ref={ref}
          className={cn(
            radioGroupItemVariants({ variant, size }),
            className
          )}
          {...props}
        />
        <label
          htmlFor={props.id}
          className={cn(
            "flex h-12 w-full cursor-pointer items-center justify-center rounded-md border-2 border-gray-200 bg-white px-6 py-4 text-center text-lg font-medium transition-all",
            "hover:border-gray-300",
            "data-[state=checked]:border-blue-600 data-[state=checked]:text-blue-600",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",
            "disabled:cursor-not-allowed disabled:opacity-50"
          )}
        >
          {label}
          {description && (
            <span className="block text-sm text-gray-500">
              {description}
            </span>
          )}
        </label>
        {children}
      </div>
    );
  }
  
  if (label || description) {
    return (
      <div className="flex items-start space-x-2">
        <ShadcnRadioGroupItem
          ref={ref}
          className={cn(
            radioGroupItemVariants({ variant, size }),
            className
          )}
          {...props}
        />
        <div className="grid gap-0.5">
          {label && (
            <label
              htmlFor={props.id}
              className="text-sm font-medium leading-none"
            >
              {label}
            </label>
          )}
          {description && (
            <p className="text-xs text-gray-500">
              {description}
            </p>
          )}
        </div>
        {children}
      </div>
    );
  }

  return (
    <ShadcnRadioGroupItem
      ref={ref}
      className={cn(
        radioGroupItemVariants({ variant, size }),
        className
      )}
      {...props}
    >
      {children}
    </ShadcnRadioGroupItem>
  );
});
RadioGroupItem.displayName = 'RadioGroupItem';

export { RadioGroupItem };