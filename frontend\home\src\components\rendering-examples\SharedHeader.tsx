'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { ThemeToggle } from '@/components/theme-toggle';
import { I18nButton } from '@/components/I18nButton';
import { useTranslation } from '@/components/Providers/i18n-provider';

interface NavItem {
  title: string;
  href: string;
}

export function SharedHeader({ currentExample }: { currentExample: string }) {
  const { t } = useTranslation();
  const pathname = usePathname();

  // Navigation items for rendering examples
  const renderingMethods: NavItem[] = [
    { title: t('rendering.ssr', 'SSR'), href: '/rendering-examples/ssr' },
    { title: t('rendering.ssg', 'SSG'), href: '/rendering-examples/ssg' },
    { title: t('rendering.csr', 'CSR'), href: '/rendering-examples/csr' },
    { title: t('rendering.isr', 'ISR'), href: '/rendering-examples/isr' },
    { title: t('rendering.spa', 'SPA'), href: '/rendering-examples/spa' },
  ];

  // Generate 3 sub-routes for each rendering method
  const generateSubRoutes = (baseMethod: string): NavItem[] => {
    return [
      {
        title: t('rendering.routes.basic', 'Basic'),
        href: `/rendering-examples/${baseMethod}`,
      },
      {
        title: t('rendering.routes.withData', 'With Data'),
        href: `/rendering-examples/${baseMethod}/with-data`,
      },
      {
        title: t('rendering.routes.advanced', 'Advanced'),
        href: `/rendering-examples/${baseMethod}/advanced`,
      },
    ];
  };

  // Get sub-routes for the current example
  const currentSubRoutes = generateSubRoutes(currentExample);

  return (
    <header className="container mx-auto px-4 py-4">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <Link href="/rendering-examples" className="text-primary font-semibold hover:underline">
            ← {t('rendering.backToExamples', 'Back to Examples')}
          </Link>

          <div className="flex items-center space-x-2">
            <ThemeToggle />
            <I18nButton />
          </div>
        </div>

        {/* Rendering methods navigation */}
        <nav className="flex flex-wrap gap-2 border-b py-2">
          {renderingMethods.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`rounded-md px-3 py-1.5 transition-colors ${
                pathname.includes(item.href.split('?')[0])
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              }`}
            >
              {item.title}
            </Link>
          ))}
        </nav>

        {/* Sub-routes for current method */}
        <nav className="flex flex-wrap gap-2 py-2">
          {currentSubRoutes.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`rounded-md px-3 py-1.5 text-sm transition-colors ${
                pathname === item.href ? 'bg-secondary text-secondary-foreground' : 'hover:bg-muted'
              }`}
            >
              {item.title}
            </Link>
          ))}
        </nav>
      </div>
    </header>
  );
}
