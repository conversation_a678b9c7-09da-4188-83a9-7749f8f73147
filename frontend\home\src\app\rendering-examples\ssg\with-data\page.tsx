import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import Link from 'next/link';

// This function will be executed at build time for SSG
async function generateDetailedUserData() {
  // No need for artificial delays in build-time data fetching
  return {
    users: [
      { id: 1, name: '<PERSON>', role: 'Developer', lastActive: new Date().toISOString() },
      { id: 2, name: '<PERSON>', role: 'Designer', lastActive: new Date().toISOString() },
      {
        id: 3,
        name: '<PERSON>',
        role: 'Product Manager',
        lastActive: new Date().toISOString(),
      },
    ],
    systemStatus: {
      cpu: Math.floor(Math.random() * 100) + '%',
      memory: Math.floor(Math.random() * 100) + '%',
      uptime: Math.floor(Math.random() * 10000) + ' minutes',
    },
    timestamp: new Date().toISOString(),
  };
}

export default async function SSGWithDataPage() {
  // Fetch static data at build time
  const detailedData = await generateDetailedUserData();

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="ssg" />
      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">SSG with Detailed Data</h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">Static User Data</h2>
          <p className="mb-4">
            This example demonstrates fetching more complex data at build time. Below you can see
            user data that was generated when the site was built. Unlike SSR, this data will not
            change on page refresh.
          </p>

          <div className="overflow-x-auto">
            <table className="bg-card w-full overflow-hidden rounded-md border">
              <thead className="bg-muted">
                <tr>
                  <th className="px-4 py-2 text-left">ID</th>
                  <th className="px-4 py-2 text-left">Name</th>
                  <th className="px-4 py-2 text-left">Role</th>
                  <th className="px-4 py-2 text-left">Last Active</th>
                </tr>
              </thead>
              <tbody>
                {detailedData.users.map((user) => (
                  <tr key={user.id} className="border-t">
                    <td className="px-4 py-2">{user.id}</td>
                    <td className="px-4 py-2">{user.name}</td>
                    <td className="px-4 py-2">{user.role}</td>
                    <td className="px-4 py-2">{new Date(user.lastActive).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">System Status</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">CPU Usage</h3>
              <p className="text-2xl">{detailedData.systemStatus.cpu}</p>
            </div>
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">Memory Usage</h3>
              <p className="text-2xl">{detailedData.systemStatus.memory}</p>
            </div>
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">Uptime</h3>
              <p className="text-2xl">{detailedData.systemStatus.uptime}</p>
            </div>
          </div>
          <p className="mt-4 text-xs">Generated at: {detailedData.timestamp}</p>
        </div>

        <div className="bg-muted rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">When to Use SSG with Complex Data</h2>
          <p className="mb-4">
            This pattern is ideal for content that is moderately complex but doesn&apos;t need to be
            updated frequently. The data is prepared at build time and then served statically to all
            users.
          </p>
          <p>
            Examples include product catalogs, documentation sites, or blog platforms where content
            changes through a new build rather than on each request.
          </p>
        </div>

        <div className="mt-8 flex justify-center">
          <Link href="/rendering-examples/ssg" className="text-primary font-medium hover:underline">
            ← Back to Basic SSG Example
          </Link>
        </div>
      </main>
    </div>
  );
}
