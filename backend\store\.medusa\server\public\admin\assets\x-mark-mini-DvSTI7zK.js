import{r as n}from"./index-Bwql5Dzz.js";var s=Object.defineProperty,o=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,i=(r,t,e)=>t in r?s(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,_=(r,t)=>{for(var e in t)l.call(t,e)&&i(r,e,t[e]);if(o)for(var e of o(t))f.call(t,e)&&i(r,e,t[e]);return r},v=(r,t)=>{var e={};for(var a in r)l.call(r,a)&&t.indexOf(a)<0&&(e[a]=r[a]);if(r!=null&&o)for(var a of o(r))t.indexOf(a)<0&&f.call(r,a)&&(e[a]=r[a]);return e};const m=n.forwardRef((r,t)=>{var e=r,{color:a="currentColor"}=e,p=v(e,["color"]);return n.createElement("svg",_({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),n.createElement("path",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"m11.25 3.75-7.5 7.5M3.75 3.75l7.5 7.5"}))});m.displayName="XMarkMini";export{m as X};
