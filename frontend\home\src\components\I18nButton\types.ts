import { HTMLAttributes, ReactNode } from 'react';
import type { VariantProps } from 'class-variance-authority';
import type { TFunction } from 'i18next';

/**
 * Base props extending HTML attributes
 */
export interface BaseProps extends HTMLAttributes<HTMLDivElement> {
  /** Optional CSS class to be merged */
  className?: string;
}

/**
 * Component variant props for CVA
 */
export interface VariantPropsInterface {
  /** Button size variant */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /** Button style variant */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}

/**
 * State-related props
 */
export interface StateProps {
  /** Whether the component is in a loading state */
  loading?: boolean;
  /** Whether the component is disabled */
  disabled?: boolean;
}

/**
 * Content-related props
 */
export interface ContentProps {
  /** Optional custom icon element */
  icon?: ReactNode;
}

/**
 * Dropdown-related props
 */
export interface DropdownProps {
  /** Whether the dropdown is open */
  open?: boolean;
  /** Callback when dropdown open state changes */
  onOpenChange?: (open: boolean) => void;
  /** Menu alignment */
  align?: 'start' | 'center' | 'end';
}

/**
 * Internationalization props
 */
export interface I18nProps {
  /** Custom translation namespace */
  i18nNamespace?: string;
  /** Translation prefix for component keys */
  i18nPrefix?: string;
}

/**
 * Language item structure
 */
export interface LanguageItem {
  /** Language code */
  code: string;
  /** Optional custom label for language */
  label?: string;
  /** Optional language icon */
  icon?: ReactNode;
  /** Whether this language uses RTL */
  rtl?: boolean;
}

/**
 * Combined props interface for I18nButton component
 */
export interface I18nButtonProps extends 
  BaseProps,
  VariantPropsInterface,
  StateProps,
  ContentProps,
  DropdownProps,
  I18nProps {
  /** List of supported languages (if not provided, uses default from i18n config) */
  languages?: LanguageItem[];
  
  /** 
   * Current language (if not provided, uses unified language system)
   * Works consistently across both Storybook and application
   */
  currentLanguage?: string;
  
  /** 
   * Function to handle language change (if not provided, uses unified language system)
   * Works consistently across both Storybook and application
   */
  onLanguageChange?: (languageCode: string) => void;
  
  /** Whether to show language names in their native language */
  showNativeNames?: boolean;
  
  /** Whether to show the current language code next to the icon */
  showLanguageCode?: boolean;
  
  /** 
   * @deprecated No longer needed as component works seamlessly in all contexts
   * Kept for backward compatibility
   */
  storybookMode?: boolean;
} 