import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { FilterTagGroup, FilterTag } from '../filter-tag-group';
import { sampleTags, availableFilters } from '../__fixtures__/filter-tag-group.fixtures';
import { action } from '@storybook/addon-actions';

const meta: Meta<typeof FilterTagGroup> = {
  title: 'UI/Filter/FilterTagGroup',
  component: FilterTagGroup,
  parameters: {
    layout: 'centered',
    a11y: {
      config: {
        rules: [
          {
            id: 'button-name',
            enabled: true
          }
        ]
      }
    }
  },
  argTypes: {
    onRemoveTag: { action: 'tag removed' },
    onClearAll: { action: 'all tags cleared' },
    className: { control: 'text' },
    tagClassName: { control: 'text' },
    showClearAll: { control: 'boolean' },
    clearAllLabel: { control: 'text' },
    ariaLabel: { control: 'text' },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof FilterTagGroup>;

// Static story example
export const Default: Story = {
  args: {
    tags: sampleTags,
    onRemoveTag: action('onRemoveTag'),
    onClearAll: action('onClearAll'),
    showClearAll: true,
    clearAllLabel: 'ล้างค่า',
  },
};

// Empty state example
export const Empty: Story = {
  args: {
    tags: [],
    onRemoveTag: action('onRemoveTag'),
    onClearAll: action('onClearAll'),
    emptyStateComponent: <p className="text-sm text-gray-500">ไม่มีตัวกรองที่เลือก</p>,
  },
};

// Custom styling example
export const CustomStyling: Story = {
  args: {
    tags: sampleTags,
    onRemoveTag: action('onRemoveTag'),
    onClearAll: action('onClearAll'),
    className: 'bg-blue-50 p-4 rounded-lg',
    tagClassName: 'bg-white border-blue-200 text-blue-800',
    clearAllLabel: 'Clear All Filters',
  },
};

// Interactive example with state
export const Interactive = () => {
  const [tags, setTags] = useState<FilterTag[]>([...sampleTags]);
  
  const handleRemoveTag = (tagToRemove: FilterTag) => {
    action('tag removed')(tagToRemove);
    setTags(tags.filter(tag => !(tag.id === tagToRemove.id && tag.type === tagToRemove.type)));
  };
  
  const handleClearAll = () => {
    action('all tags cleared')();
    setTags([]);
  };

  const addFilter = (type: string, item: any) => {
    // Check if filter already exists
    const exists = tags.some(tag => tag.type === type && tag.id === item.id);
    if (!exists) {
      setTags([...tags, { 
        id: item.id, 
        type: type === 'lightTypes' ? 'หลอดไฟ' : type === 'brands' ? 'แบรนด์' : 'รูปทรง',
        value: item.value, 
        label: item.label.replace(/^หลอดไฟ /, '')
      }]);
    }
  };
  
  return (
    <div className="w-full max-w-2xl">
      <h2 className="text-lg font-semibold mb-4">Interactive Filter Tags</h2>
      <FilterTagGroup
        tags={tags}
        onRemoveTag={handleRemoveTag}
        onClearAll={handleClearAll}
        className="p-3 bg-gray-50 rounded-md mb-6"
      />
      
      {/* Filter Selection Demo */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="border p-3 rounded">
          <h3 className="font-medium mb-2">ประเภทสินค้า</h3>
          <div className="space-y-1">
            {availableFilters.lightTypes.map(item => (
              <button 
                key={item.id}
                className="text-blue-600 hover:underline block"
                onClick={() => addFilter('lightTypes', item)}
              >
                {item.label}
              </button>
            ))}
          </div>
        </div>
        
        <div className="border p-3 rounded">
          <h3 className="font-medium mb-2">แบรนด์</h3>
          <div className="space-y-1">
            {availableFilters.brands.slice(0, 4).map(item => (
              <button 
                key={item.id}
                className="text-blue-600 hover:underline block"
                onClick={() => addFilter('brands', item)}
              >
                {item.label}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {tags.length === 0 && (
        <button 
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md"
          onClick={() => setTags([...sampleTags])}
        >
          Reset Filters
        </button>
      )}
    </div>
  );
};