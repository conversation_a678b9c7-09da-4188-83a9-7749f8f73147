import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { composeStories } from '@storybook/react';
import * as stories from '../__stories__/modal-content-coupon.stories';
import { ModalContentCoupon, CouponDetails } from '../modal-content-coupon';

// Compose stories for testing
const { Default, WithCustomIcon, WithoutLogo, WithoutAdditionalInfo, WithMultipleRestrictions } = composeStories(stories);

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} />;
  },
}));

describe('ModalContentCoupon', () => {
  const mockCoupon: CouponDetails = {
    title: 'คูปองส่วนลด',
    amount: 2500,
    currency: 'บาท',
    minimumPurchase: 30000,
    expiryDate: '31/03/2025',
    applicableProducts: 'สินค้าที่ร่วมรายการ',
    restrictions: [
      'บริษัทสามารถเปลี่ยนแปลงเงื่อนไขโดยที่ไม่ต้องแจ้งให้ทราบล่วงหน้า'
    ],
    canBeUsedTogether: false,
    companyLogo: '/philips-logo.png',
    additionalInfo: 'ท่านสามารถใช้คูปองส่วนลดนี้ได้ผ่านหน้าข้อมูลการชำระเงิน'
  };

  const mockCouponWithCustomIcon: CouponDetails = {
    ...mockCoupon,
    couponIcon: '/custom-coupon-icon.svg'
  };

  it('renders correctly with all props', () => {
    const handleClose = jest.fn();
    render(
      <ModalContentCoupon 
        coupon={mockCoupon} 
        onClose={handleClose} 
      />
    );
    
    // Check if all the expected elements are in the document
    expect(screen.getByTestId('coupon-modal-content')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-icon')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-title')).toHaveTextContent('คูปองส่วนลด');
    expect(screen.getByTestId('coupon-subtitle')).toHaveTextContent('คูปองส่วนลดสูงสุด');
    expect(screen.getByTestId('coupon-amount')).toHaveTextContent('2,500');
    expect(screen.getByTestId('coupon-currency')).toHaveTextContent('บาท');
    expect(screen.getByTestId('coupon-minimum-purchase')).toHaveTextContent('30,000');
    expect(screen.getByTestId('coupon-company-logo')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-details-list')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-expiry-date')).toHaveTextContent('31/03/2025');
    expect(screen.getByTestId('coupon-applicable-products')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-no-combined-use')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-restriction-0')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-additional-info')).toBeInTheDocument();
  });

  it('renders with custom icon when provided', () => {
    render(
      <ModalContentCoupon 
        coupon={mockCouponWithCustomIcon} 
        onClose={() => {}} 
      />
    );
    
    // Check if the custom icon is in the document
    const iconContainer = screen.getByTestId('coupon-icon');
    const customIcon = iconContainer.querySelector('img');
    expect(customIcon).toBeInTheDocument();
    expect(customIcon).toHaveAttribute('src', '/custom-coupon-icon.svg');
  });

  it('falls back to default icon when no custom icon is provided', () => {
    render(
      <ModalContentCoupon 
        coupon={mockCoupon} 
        onClose={() => {}} 
      />
    );
    
    // Check if the default icon is used
    const iconContainer = screen.getByTestId('coupon-icon');
    // Default icon should have the blue background and no img tag
    expect(iconContainer.querySelector('img')).toBeNull();
    expect(iconContainer.firstChild).toHaveClass('bg-blue-600');
  });

  it('calls onClose when close button is clicked', () => {
    const handleClose = jest.fn();
    render(
      <ModalContentCoupon 
        coupon={mockCoupon} 
        onClose={handleClose} 
      />
    );
    
    const closeButton = screen.getByTestId('coupon-close-button');
    fireEvent.click(closeButton);
    expect(handleClose).toHaveBeenCalledTimes(1);
  });

  it('does not render company logo when not provided', () => {
    render(
      <ModalContentCoupon 
        coupon={{...mockCoupon, companyLogo: undefined}} 
      />
    );
    
    expect(screen.queryByTestId('coupon-company-logo')).not.toBeInTheDocument();
  });

  it('does not render additional info section when not provided', () => {
    render(
      <ModalContentCoupon 
        coupon={{...mockCoupon, additionalInfo: undefined}} 
      />
    );
    
    expect(screen.queryByTestId('coupon-additional-info')).not.toBeInTheDocument();
  });

  it('formats numbers with thousand separators', () => {
    render(
      <ModalContentCoupon 
        coupon={mockCoupon} 
      />
    );
    
    expect(screen.getByTestId('coupon-amount')).toHaveTextContent('2,500');
    expect(screen.getByTestId('coupon-minimum-purchase')).toHaveTextContent('30,000');
  });

  it('renders multiple restrictions correctly', () => {
    const multipleRestrictions = {
      ...mockCoupon,
      restrictions: [
        'Restriction 1',
        'Restriction 2',
        'Restriction 3'
      ]
    };
    
    render(
      <ModalContentCoupon 
        coupon={multipleRestrictions} 
      />
    );
    
    expect(screen.getByTestId('coupon-restriction-0')).toHaveTextContent('Restriction 1');
    expect(screen.getByTestId('coupon-restriction-1')).toHaveTextContent('Restriction 2');
    expect(screen.getByTestId('coupon-restriction-2')).toHaveTextContent('Restriction 3');
  });

  // Test composed stories
  it('renders Default story correctly', () => {
    render(<Default />);
    expect(screen.getByTestId('coupon-title')).toHaveTextContent('คูปองส่วนลด');
    expect(screen.getByTestId('coupon-amount')).toHaveTextContent('2,500');
    expect(screen.getByTestId('coupon-icon')).toBeInTheDocument();
  });

  it('renders WithCustomIcon story correctly', () => {
    render(<WithCustomIcon />);
    const iconContainer = screen.getByTestId('coupon-icon');
    const customIcon = iconContainer.querySelector('img');
    expect(customIcon).toBeInTheDocument();
    expect(customIcon).toHaveAttribute('src', '/coupon-icon.svg');
  });

  it('renders WithoutLogo story correctly', () => {
    render(<WithoutLogo />);
    expect(screen.queryByTestId('coupon-company-logo')).not.toBeInTheDocument();
  });

  it('renders WithoutAdditionalInfo story correctly', () => {
    render(<WithoutAdditionalInfo />);
    expect(screen.queryByTestId('coupon-additional-info')).not.toBeInTheDocument();
  });

  it('renders WithMultipleRestrictions story correctly', () => {
    render(<WithMultipleRestrictions />);
    expect(screen.getByTestId('coupon-restriction-0')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-restriction-1')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-restriction-2')).toBeInTheDocument();
    expect(screen.getByTestId('coupon-restriction-3')).toBeInTheDocument();
  });
});