'use client';

import { useState, useEffect } from 'react';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import Link from 'next/link';
import { useTranslation } from '@/components/Providers/i18n-provider';

// Interface for user data
interface User {
  id: number;
  name: string;
  role: string;
  lastActive: string;
}

// Interface for system status
interface SystemStatus {
  cpu: string;
  memory: string;
  uptime: string;
}

// Interface for the detailed data
interface DetailedData {
  users: User[];
  systemStatus: SystemStatus;
  timestamp: string;
}

export default function CSRWithDataPage() {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<DetailedData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Simulate fetching data from an API on the client side
  useEffect(() => {
    // Simulate network delay
    const fetchData = async () => {
      try {
        // Artificial delay to simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1500));

        // Mock API response
        const mockData: DetailedData = {
          users: [
            {
              id: 1,
              name: '<PERSON>',
              role: 'Developer',
              lastActive: new Date().toISOString(),
            },
            { id: 2, name: 'Bob Smith', role: 'Designer', lastActive: new Date().toISOString() },
            {
              id: 3,
              name: 'Carol Williams',
              role: 'Product Manager',
              lastActive: new Date().toISOString(),
            },
          ],
          systemStatus: {
            cpu: Math.floor(Math.random() * 100) + '%',
            memory: Math.floor(Math.random() * 100) + '%',
            uptime: Math.floor(Math.random() * 10000) + ' minutes',
          },
          timestamp: new Date().toISOString(),
        };

        setData(mockData);
        setIsLoading(false);
      } catch {
        setError('Failed to fetch data');
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="csr" />
      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">
          {t('rendering.csrWithData', 'CSR with Detailed Data')}
        </h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">
            {t('rendering.clientSideDataFetching', 'Client-Side Data Fetching')}
          </h2>
          <p className="mb-4">
            {t(
              'rendering.csrWithDataDescription',
              'This example demonstrates fetching data on the client side after the component has mounted. This creates a noticeable loading state, as the browser must first download and execute JavaScript before it can start loading data.',
            )}
          </p>
          <p>
            {t(
              'rendering.csrWithDataNote',
              'Notice how the page initially shows loading indicators and then populates with data. Refresh to see this process again.',
            )}
          </p>
        </div>

        {isLoading ? (
          <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
            <h2 className="mb-4 text-xl font-semibold">
              {t('rendering.loadingData', 'Loading Data...')}
            </h2>
            <div className="flex items-center justify-center space-x-2 py-8">
              <div className="bg-primary/20 h-4 w-4 animate-pulse rounded-full"></div>
              <div className="bg-primary/20 h-4 w-4 animate-pulse rounded-full delay-75"></div>
              <div className="bg-primary/20 h-4 w-4 animate-pulse rounded-full delay-150"></div>
              <div className="bg-primary/20 h-4 w-4 animate-pulse rounded-full delay-300"></div>
            </div>
            <div className="animate-pulse space-y-4">
              <div className="bg-muted h-8 w-full rounded"></div>
              <div className="bg-muted h-8 w-full rounded"></div>
              <div className="bg-muted h-8 w-full rounded"></div>
            </div>
          </div>
        ) : error ? (
          <div className="bg-destructive/10 border-destructive/20 mb-6 rounded-lg border p-6">
            <h2 className="mb-4 text-xl font-semibold">{t('rendering.error', 'Error')}</h2>
            <p>{error}</p>
          </div>
        ) : (
          <>
            <div className="bg-muted mb-6 rounded-lg p-6">
              <h2 className="mb-4 text-xl font-semibold">{t('rendering.userData', 'User Data')}</h2>
              <p className="mb-4">
                {t(
                  'rendering.userDataDescription',
                  'The table below shows user data that was fetched client-side after the page loaded:',
                )}
              </p>

              <div className="overflow-x-auto">
                <table className="bg-card w-full overflow-hidden rounded-md border">
                  <thead className="bg-muted">
                    <tr>
                      <th className="px-4 py-2 text-left">ID</th>
                      <th className="px-4 py-2 text-left">Name</th>
                      <th className="px-4 py-2 text-left">Role</th>
                      <th className="px-4 py-2 text-left">Last Active</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data?.users.map((user) => (
                      <tr key={user.id} className="border-t">
                        <td className="px-4 py-2">{user.id}</td>
                        <td className="px-4 py-2">{user.name}</td>
                        <td className="px-4 py-2">{user.role}</td>
                        <td className="px-4 py-2">{new Date(user.lastActive).toLocaleString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
              <h2 className="mb-4 text-xl font-semibold">
                {t('rendering.systemStatus', 'System Status')}
              </h2>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="bg-card rounded border p-4">
                  <h3 className="mb-2 font-medium">{t('rendering.cpuUsage', 'CPU Usage')}</h3>
                  <p className="text-2xl">{data?.systemStatus.cpu}</p>
                </div>
                <div className="bg-card rounded border p-4">
                  <h3 className="mb-2 font-medium">{t('rendering.memoryUsage', 'Memory Usage')}</h3>
                  <p className="text-2xl">{data?.systemStatus.memory}</p>
                </div>
                <div className="bg-card rounded border p-4">
                  <h3 className="mb-2 font-medium">{t('rendering.uptime', 'Uptime')}</h3>
                  <p className="text-2xl">{data?.systemStatus.uptime}</p>
                </div>
              </div>
              <p className="mt-4 text-xs">
                {t('rendering.generatedAt', 'Generated at')}: {data?.timestamp}
              </p>
            </div>
          </>
        )}

        <div className="bg-muted rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">
            {t('rendering.clientRenderingConsiderations', 'Client Rendering Considerations')}
          </h2>
          <ul className="list-disc space-y-2 pl-5">
            <li>
              {t(
                'rendering.csrSEOConsideration',
                'SEO can be challenging as search engines might not execute JavaScript or wait for data to load',
              )}
            </li>
            <li>
              {t(
                'rendering.csrPerformanceConsideration',
                'Initial page load requires downloading JavaScript and then fetching data, causing sequential delays',
              )}
            </li>
            <li>
              {t(
                'rendering.csrUXConsideration',
                'Users see loading states which can affect perceived performance',
              )}
            </li>
            <li>
              {t(
                'rendering.csrAdvantage',
                'Great for dashboards or user-specific content that doesnt need SEO optimization',
              )}
            </li>
          </ul>
        </div>

        <div className="mt-8 flex justify-center">
          <Link href="/rendering-examples/csr" className="text-primary font-medium hover:underline">
            ← {t('rendering.backToBasicCSR', 'Back to Basic CSR Example')}
          </Link>
        </div>
      </main>
    </div>
  );
}
