// section-popular-products.fixtures.tsx
import { CardProductProps } from '@/components/Card/card-product';
import { SectionPopularProductsProps } from '../section-popular-products';

// Mock product data
export const mockProducts: CardProductProps[] = [
  {
    id: "1",
    imageUrl: "/images/products/light-fixture-1.jpg",
    brand: "LUZINO",
    name: "โคมไฟตั้งโต๊ะ LED รุ่น DL223-BN สีบรอนซ์แดง",
    price: 238.00,
    originalPrice: 280.00,
    discountPercentage: 15,
    unit: "บาท/ชิ้น",
    soldCount: 8,
    imageAlt: "โคมไฟตั้งโต๊ะ LED รุ่น DL223-BN สีบรอนซ์แดง"
  },
  {
    id: "2",
    imageUrl: "/images/products/light-fixture-2.jpg",
    brand: "LAMPTAN",
    name: "แทร็คไลท์ FIXTURE WHITE",
    price: 259.00,
    unit: "บาท/ชิ้น",
    soldCount: 29,
    imageAlt: "แทร็คไลท์ FIXTURE WHITE"
  },
  {
    id: "3",
    imageUrl: "/images/products/solar-light-1.jpg",
    brand: "LAMPTAN",
    name: "สปอตไลท์ SOLAR ARC 300 วัตต์ DAYLIGHT สีดำ",
    price: 4599.00,
    unit: "บาท/ชิ้น",
    soldCount: 42,
    imageAlt: "สปอตไลท์ SOLAR ARC 300 วัตต์ DAYLIGHT สีดำ"
  },
  {
    id: "4",
    imageUrl: "/images/products/led-bulb-1.jpg",
    brand: "PHILIPS",
    name: "หลอด LED A60 12 วัตต์ WARM WHITE E27 แพ็ค 3 ดวง",
    price: 399.00,
    unit: "บาท/แพ็ค",
    soldCount: 160,
    imageAlt: "หลอด LED A60 12 วัตต์ WARM WHITE E27 แพ็ค 3 ดวง"
  },
  {
    id: "5",
    imageUrl: "/images/products/led-downlight-1.jpg",
    brand: "PHILIPS",
    name: "ดาวน์ไลท์ LED 13 วัตต์ COOL WHITE 5 นิ้ว",
    price: 175.00,
    originalPrice: 250.00,
    discountPercentage: 30,
    unit: "บาท/ชิ้น",
    soldCount: 1338,
    imageAlt: "ดาวน์ไลท์ LED 13 วัตต์ COOL WHITE 5 นิ้ว"
  },
  {
    id: "6",
    imageUrl: "/images/products/solar-panel-1.jpg",
    brand: "LUMAX",
    name: "โซล่าเซลล์ SOLAR 58-LO023 200 วัตต์ แอลอีดี 200W",
    price: 2390.00,
    originalPrice: 4185.00,
    discountPercentage: 42,
    unit: "บาท/ชิ้น",
    soldCount: 10,
    imageAlt: "โซล่าเซลล์ SOLAR 58-LO023 200 วัตต์ แอลอีดี 200W"
  }
];

// Default fixture
export const defaultFixture: SectionPopularProductsProps = {
  title: "สินค้ายอดนิยม",
  products: mockProducts,
  columns: {
    mobile: 2,
    tablet: 3,
    desktop: 6
  },
  showViewAll: true,
  viewAllUrl: "/products/popular",
  addingToCartIds: []
};

// Empty state fixture
export const emptyFixture: SectionPopularProductsProps = {
  ...defaultFixture,
  products: []
};

// Loading state fixture (some products being added to cart)
export const loadingFixture: SectionPopularProductsProps = {
  ...defaultFixture,
  addingToCartIds: ["1", "3"]
};

// Custom title fixture
export const customTitleFixture: SectionPopularProductsProps = {
  ...defaultFixture,
  title: (
    <span className="flex items-center">
      <span className="mr-2">🔥</span>
      สินค้าขายดีประจำสัปดาห์
    </span>
  )
};

// Featured products fixture (fewer products, different columns)
export const featuredFixture: SectionPopularProductsProps = {
  ...defaultFixture,
  title: "สินค้าแนะนำ",
  products: mockProducts.slice(0, 4),
  columns: {
    mobile: 1,
    tablet: 2,
    desktop: 4
  },
  viewAllUrl: "/products/featured"
};

// Fixtures collection for easy access
export const fixtures = {
  default: defaultFixture,
  empty: emptyFixture,
  loading: loadingFixture,
  customTitle: customTitleFixture,
  featured: featuredFixture
};