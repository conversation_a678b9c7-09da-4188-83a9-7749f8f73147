import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { ModalContentCoupon, type CouponDetails } from '../modal-content-coupon';
import { Modal } from '../modal';
import { Button } from '@/components/ui/button';

const meta: Meta<typeof ModalContentCoupon> = {
  title: 'UI/Modal/ModalContentCoupon',
  component: ModalContentCoupon,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A reusable coupon content component that can be used inside modals.'
      }
    }
  },
  argTypes: {
    coupon: {
      control: 'object',
      description: 'Coupon details including title, amount, restrictions, etc.'
    },
    onClose: {
      action: 'closed',
      description: 'Function called when the close button is clicked'
    }
  }
};

export default meta;
type Story = StoryObj<typeof ModalContentCoupon>;

// Sample coupon data
const sampleCoupon: CouponDetails = {
  title: 'คูปองส่วนลด',
  amount: 2500,
  currency: 'บาท',
  minimumPurchase: 30000,
  expiryDate: '31/03/2025',
  applicableProducts: 'สินค้าที่ร่วมรายการ',
  restrictions: [
    'บริษัทสามารถเปลี่ยนแปลงเงื่อนไขโดยที่ไม่ต้องแจ้งให้ทราบล่วงหน้า'
  ],
  canBeUsedTogether: false,
  companyLogo: '/images/philips-logo.png',
  couponIcon: '/images/coupon-icon.png',
  additionalInfo: 'ท่านสามารถใช้คูปองส่วนลดนี้ได้ผ่านหน้าข้อมูลการชำระเงิน'
};

const philipsCoupon: CouponDetails = {
  title: 'คูปองส่วนลด',
  amount: 2500,
  currency: 'บาท',
  minimumPurchase: 30000,
  expiryDate: '31/03/2025',
  applicableProducts: 'สินค้าที่ร่วมรายการ',
  restrictions: [
    'ไม่สามารถใช้ได้คู่ส่วนลดนี้ซ้ำได้',
    'บริษัทสามารถเปลี่ยนแปลงเงื่อนไขโดยที่ไม่ต้องแจ้งให้ทราบล่วงหน้า'
  ],
  canBeUsedTogether: false,
  companyLogo: '/images/philips-logo.png',
  additionalInfo: 'ท่านสามารถใช้คูปองส่วนลดนี้ได้ผ่านหน้าข้อมูลการชำระเงิน'
};

// Coupon with custom icon
const customIconCoupon: CouponDetails = {
  ...sampleCoupon,
  couponIcon: '/images/coupon-icon.png', // Custom icon path
};

// Standalone Coupon Content Component
export const Default: Story = {
  args: {
    coupon: sampleCoupon,
    onClose: () => console.log('Closed')
  }
};

export const WithCustomIcon: Story = {
  args: {
    coupon: customIconCoupon,
    onClose: () => console.log('Closed')
  }
};

export const WithoutLogo: Story = {
  args: {
    coupon: {
      ...sampleCoupon,
      companyLogo: undefined
    },
    onClose: () => console.log('Closed')
  }
};

export const WithoutAdditionalInfo: Story = {
  args: {
    coupon: {
      ...sampleCoupon,
      additionalInfo: undefined
    },
    onClose: () => console.log('Closed')
  }
};

export const WithMultipleRestrictions: Story = {
  args: {
    coupon: {
      ...sampleCoupon,
      restrictions: [
        'ไม่สามารถใช้ได้คู่ส่วนลดนี้ซ้ำได้',
        'ใช้ได้เฉพาะสินค้าในรายการที่กำหนด',
        'ไม่สามารถแลกเปลี่ยนเป็นเงินสดได้',
        'บริษัทสามารถเปลี่ยนแปลงเงื่อนไขโดยที่ไม่ต้องแจ้งให้ทราบล่วงหน้า'
      ]
    },
    onClose: () => console.log('Closed')
  }
};

// Coupon inside a Modal Component
const ModalWithCouponWrapper = (args: any) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>View Coupon</Button>
      <Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        size="default"
        position="center"
        showCloseButton={false}
      >
        <ModalContentCoupon
          coupon={args.coupon}
          onClose={() => setIsOpen(false)}
        />
      </Modal>
    </div>
  );
};

export const InsideModal: Story = {
  render: (args) => <ModalWithCouponWrapper {...args} />,
  args: {
    coupon: philipsCoupon
  }
};

// Custom Icon inside Modal
export const CustomIconInsideModal: Story = {
  render: (args) => <ModalWithCouponWrapper {...args} />,
  args: {
    coupon: customIconCoupon
  }
};

// Different currency
export const DifferentCurrency: Story = {
  args: {
    coupon: {
      ...sampleCoupon,
      amount: 50,
      currency: 'USD',
      minimumPurchase: 500,
      companyLogo: '/images/philips-logo.png'
    },
    onClose: () => console.log('Closed')
  }
};

// Percentage discount
export const PercentageDiscount: Story = {
  args: {
    coupon: {
      title: 'ส่วนลด',
      amount: 15,
      currency: '%',
      minimumPurchase: 2000,
      expiryDate: '31/12/2025',
      applicableProducts: 'สินค้าทั้งหมด',
      restrictions: [
        'ไม่สามารถใช้ร่วมกับโปรโมชั่นอื่น',
        'ยกเว้นสินค้าลดราคาพิเศษ'
      ],
      canBeUsedTogether: false,
      additionalInfo: 'ส่วนลดสูงสุด 500 บาท'
    },
    onClose: () => console.log('Closed')
  }
};

// Multiple coupons carousel example
const MultiCouponExample = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const coupons: CouponDetails[] = [
    philipsCoupon,
    {
      title: 'ส่วนลด',
      amount: 15,
      currency: '%',
      minimumPurchase: 1000,
      expiryDate: '31/12/2025',
      applicableProducts: 'สินค้าทั้งหมด',
      restrictions: ['ยกเว้นสินค้าลดราคาพิเศษ'],
      companyLogo: '/sample-logo.png',
      couponIcon: '/special-discount-icon.svg'
    },
    {
      title: 'โค้ดส่วนลด',
      amount: 100,
      currency: 'บาท',
      expiryDate: '15/06/2025',
      restrictions: ['ใช้ได้ครั้งเดียวต่อการสั่งซื้อ'],
      additionalInfo: 'ใช้ได้กับทุกการสั่งซื้อครั้งแรก',
      couponIcon: '/first-order-icon.svg'
    }
  ];
  
  const nextCoupon = () => {
    setCurrentIndex((prev) => (prev + 1) % coupons.length);
  };
  
  const prevCoupon = () => {
    setCurrentIndex((prev) => (prev - 1 + coupons.length) % coupons.length);
  };
  
  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>View All Coupons ({coupons.length})</Button>
      <Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        size="default"
        position="center"
        showCloseButton={false}
        footer={
          <div className="flex justify-between w-full">
            <Button variant="outline" onClick={prevCoupon}>
              Previous
            </Button>
            <div className="text-sm text-gray-500">
              {currentIndex + 1} / {coupons.length}
            </div>
            <Button variant="outline" onClick={nextCoupon}>
              Next
            </Button>
          </div>
        }
      >
        <ModalContentCoupon
          coupon={coupons[currentIndex]}
          onClose={() => setIsOpen(false)}
        />
      </Modal>
    </div>
  );
};

export const MultiCouponCarousel: Story = {
  render: () => <MultiCouponExample />,
};