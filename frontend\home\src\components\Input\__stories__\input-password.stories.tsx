// InputPassword.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { expect, userEvent, within } from '@storybook/test';
import { InputPassword } from '../input-password';

const meta: Meta<typeof InputPassword> = {
  title: 'UI/Input/InputPassword',
  component: InputPassword,
  parameters: {
    layout: 'centered',
    // a11y testing parameters
    a11y: {
      config: {
        rules: [
          {
            // ตัวอย่างการปรับแต่งกฎ a11y เฉพาะ component นี้
            id: 'label',
            enabled: true,
          },
        ],
      },
    },
  },
  // ค่าเริ่มต้นที่ใช้กับทุก Story
  args: {
    id: 'password-field',
    placeholder: 'Enter password',
  },
  // Mock functions สำหรับใช้ในการทดสอบ
  argTypes: {
    onChange: { action: 'changed' },
    onValueChange: { action: 'valueChanged' },
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg'],
    },
  },
  // สร้าง documentation อัตโนมัติ
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof InputPassword>;

// Story พื้นฐาน
export const Default: Story = {
  args: {},
};

// Story ที่มี label
export const WithLabel: Story = {
  args: {
    label: 'Password',
    placeholder: 'Enter your password',
  },
};

// Story ขนาดต่างๆ
export const SizeVariants: Story = {
  render: () => (
    <div className="flex flex-col space-y-4">
      <InputPassword size="sm" placeholder="Small password field" />
      <InputPassword size="default" placeholder="Default password field" />
      <InputPassword size="lg" placeholder="Large password field" />
    </div>
  ),
};

// Story ที่มีข้อความช่วยเหลือ
export const WithHelperText: Story = {
  args: {
    label: 'Password',
    placeholder: '************',
    helperText: 'Password must be at least 8 characters',
  },
};

// Story กรณีเกิดข้อผิดพลาด
export const WithError: Story = {
  args: {
    label: 'Password',
    placeholder: 'Enter password',
    error: true,
    errorMessage: 'Password must contain at least one uppercase letter',
  },
};

// Story แสดงสถานะ disabled
export const Disabled: Story = {
  args: {
    label: 'Password',
    placeholder: 'Enter password',
    disabled: true,
    defaultValue: 'Unavailable',
  },
};

// Story สำหรับรหัสผ่านใหม่
export const NewPassword: Story = {
  args: {
    label: 'รหัสผ่านใหม่',
    placeholder: '************',
  },
};

// Story ยืนยันรหัสผ่าน
export const ConfirmPassword: Story = {
  args: {
    label: 'ยืนยันรหัสผ่าน',
    defaultValue: 'Qw1098304',
    readOnly: true,
  },
};

// Story ไม่แสดงปุ่มเปิด/ปิดรหัสผ่าน
export const WithoutToggle: Story = {
  args: {
    label: 'Password',
    placeholder: 'Enter password',
    showPasswordToggle: false,
  },
};

// Story ที่มีการทดสอบ interaction และ state
export const InteractionTest: Story = {
  args: {
    label: 'Test Password',
    placeholder: 'Type password...',
    onChange: fn(),
    onValueChange: fn(),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // ค้นหา input element
    const input = canvas.getByLabelText('Test Password');
    
    // ตรวจสอบว่า input มีอยู่จริง
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute('type', 'password');
    
    // จำลองการ focus
    await userEvent.click(input);
    
    // จำลองการพิมพ์ข้อความ
    await userEvent.type(input, 'SecurePassword123!');
    
    // ตรวจสอบว่าข้อความที่พิมพ์ถูกต้อง
    expect(input).toHaveValue('SecurePassword123!');
    
    // ตรวจสอบว่ามีการเรียกใช้ onChange
    expect(args.onChange).toHaveBeenCalled();
    
    // ตรวจสอบว่ามีการเรียกใช้ onValueChange
    expect(args.onValueChange).toHaveBeenCalled();
    
    // คลิกปุ่มแสดง/ซ่อนรหัสผ่าน
    const toggleButton = canvas.getByTestId('password-toggle');
    await userEvent.click(toggleButton);
    
    // ตรวจสอบว่า type เปลี่ยนเป็น text
    expect(input).toHaveAttribute('type', 'text');
    
    // คลิกปุ่มอีกครั้งเพื่อซ่อนรหัสผ่าน
    await userEvent.click(toggleButton);
    
    // ตรวจสอบว่า type เปลี่ยนกลับเป็น password
    expect(input).toHaveAttribute('type', 'password');
  },
};

// Story สำหรับทดสอบ a11y โดยเฉพาะ
export const AccessibilityTest: Story = {
  args: {
    label: 'Password',
    placeholder: 'Enter your password',
    required: true,
    helperText: 'Your password must be at least 8 characters long',
  },
};