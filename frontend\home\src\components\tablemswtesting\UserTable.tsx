'use client';

import React, { useState, useEffect } from 'react';
import { User } from './__fixtures__/TableMsw.mockData';
import {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { i18n, SupportedLanguage, getLanguage } from '@/lib';

export interface UserTableProps {
  endpoint?: string;
  loading?: boolean;
  error?: string | null;
  onUserSelect?: (user: User) => void;
  simulateCondition?: string;
  i18nNamespace?: string;
  i18nPrefix?: string;
}

export const UserTable: React.FC<UserTableProps> = ({
  endpoint = '/api/users',
  loading: controlledLoading,
  error: controlledError,
  onUserSelect,
  simulateCondition,
  i18nNamespace,
  i18nPrefix = 'userTable',
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { t } = useTranslation(i18nNamespace);
  const currentLang = getLanguage() as SupportedLanguage;

  // Fetch users from the API
  useEffect(() => {
    const fetchUsers = async () => {
      if (controlledLoading !== undefined) {
        return; // Don't fetch if loading is controlled externally
      }

      setLoading(true);
      setError(null);

      try {
        // Build URL with simulation parameter if provided
        const url = new URL(endpoint, window.location.origin);
        if (simulateCondition) {
          url.searchParams.append('simulate', simulateCondition);
        }

        const response = await fetch(url.toString());
        
        if (!response.ok) {
          throw new Error(`API Error: ${response.status}`);
        }
        
        const result = await response.json();
        setUsers(result.data || []);
      } catch (err) {
        console.error('Failed to fetch users:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch users');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [endpoint, controlledLoading, simulateCondition]);

  // Determine if we should show loading state
  const isLoading = controlledLoading !== undefined ? controlledLoading : loading;
  
  // Determine if we should show error state
  const errorMessage = controlledError !== undefined ? controlledError : error;

  // Handle row click
  const handleRowClick = (user: User) => {
    if (onUserSelect) {
      onUserSelect(user);
    }
  };

  // Render status badge with appropriate color
  const renderStatusBadge = (status: User['status']) => {
    const variants: Record<User['status'], 'default' | 'destructive' | 'outline'> = {
      active: 'default',
      inactive: 'destructive',
      pending: 'outline'
    };
    
    return (
      <Badge variant={variants[status]}>
        {t(`${i18nPrefix}.status.${status}`)}
      </Badge>
    );
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (err) {
      return 'Invalid date';
    }
  };

  if (isLoading) {
    return (
      <div className="w-full p-8 flex justify-center">
        <div className="animate-pulse flex flex-col w-full">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-12 bg-gray-100 dark:bg-gray-800 rounded w-full mb-2"></div>
          ))}
        </div>
      </div>
    );
  }

  if (errorMessage) {
    return (
      <div className="w-full p-8 flex flex-col items-center text-center">
        <h3 className="text-lg font-semibold text-red-500 mb-2">{t(`${i18nPrefix}.error.title`)}</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{errorMessage}</p>
        <Button 
          onClick={() => window.location.reload()}
          variant="outline"
        >
          {t(`${i18nPrefix}.error.retry`)}
        </Button>
      </div>
    );
  }

  if (users.length === 0) {
    return (
      <div className="w-full p-8 flex flex-col items-center text-center">
        <h3 className="text-lg font-semibold mb-2">{t(`${i18nPrefix}.empty.title`)}</h3>
        <p className="text-gray-600 dark:text-gray-400">{t(`${i18nPrefix}.empty.description`)}</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t(`${i18nPrefix}.headers.name`)}</TableHead>
            <TableHead>{t(`${i18nPrefix}.headers.email`)}</TableHead>
            <TableHead>{t(`${i18nPrefix}.headers.role`)}</TableHead>
            <TableHead>{t(`${i18nPrefix}.headers.status`)}</TableHead>
            <TableHead>{t(`${i18nPrefix}.headers.lastLogin`)}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow 
              key={user.id}
              onClick={() => handleRowClick(user)}
              className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <TableCell className="font-medium">{user.name}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>{user.role}</TableCell>
              <TableCell>{renderStatusBadge(user.status)}</TableCell>
              <TableCell>{formatDate(user.lastLogin)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
        <TableFooter>
          <TableRow>
            <TableCell colSpan={5} className="text-right">
              {t(`${i18nPrefix}.footer.total`, { count: users.length })}
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
      
      {/* Language indicator (hidden in production, useful for development) */}
      <div className="hidden">
        <div className="text-muted-foreground mt-2 text-xs">
          Current language: {currentLang}
        </div>
      </div>
    </div>
  );
}; 