'use client';

import * as React from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useLanguageChange } from '@/hooks/useLanguageChange';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ApiKeyNameField, PermissionTypeField } from './ApiKey-Fields';
import { ApiKeyPermissionSection, PermissionNotification } from './ApiKey-PermissionSection';
import { createI18nApiKeySchema, type I18nApiKeyFormValues } from './ApiKey-i18nSchema';
import type { EditApiKeyProps } from './ApiKey-Types';
import type { PermissionTypeValue, ApiKeyFormValues } from './ApiKey-Schema';

/**
 * Internationalized Edit API Key component
 */
export const I18nEdit: React.FC<EditApiKeyProps> = ({
  apiKey,
  onSubmit,
  onCancel,
  isSubmitting = false,
  className,
}) => {
  const { t } = useTranslation();
  const currentLang = useLanguageChange();

  // Create schema with translations
  const i18nApiKeySchema = React.useMemo(() => createI18nApiKeySchema(t), [t]);

  const [permissionType, setPermissionType] = React.useState<PermissionTypeValue>(
    apiKey.permissionType,
  );

  // Create default values from the API key
  const defaultValues: I18nApiKeyFormValues = {
    name: apiKey.name || '',
    permissionType: apiKey.permissionType,
    resourcePermissions: {
      models: apiKey.resourcePermissions?.models || 'None',
      modelCapabilities: apiKey.resourcePermissions?.modelCapabilities || 'None',
      assistants: apiKey.resourcePermissions?.assistants || 'None',
      threads: apiKey.resourcePermissions?.threads || 'None',
      evals: apiKey.resourcePermissions?.evals || 'None',
      fineTuning: apiKey.resourcePermissions?.fineTuning || 'None',
      files: apiKey.resourcePermissions?.files || 'None',
    },
    ownerType: 'You',
    project: '',
  };

  // Form setup with React Hook Form and Zod validation
  const methods = useForm<I18nApiKeyFormValues>({
    resolver: zodResolver(i18nApiKeySchema),
    defaultValues,
    mode: 'onBlur',
  });

  const {
    handleSubmit,
    formState: { isDirty },
  } = methods;

  // Handle form submission
  const handleFormSubmit = (data: I18nApiKeyFormValues) => {
    onSubmit(data as ApiKeyFormValues);
  };

  return (
    <FormProvider {...methods}>
      <div
        className={cn(
          'bg-background w-full max-w-md overflow-y-auto rounded-lg p-6 shadow-lg',
          className,
        )}
        data-testid="i18n-edit-form"
      >
        <h2 className="mb-4 text-xl font-semibold" data-testid="form-title">
          {t('apiKey.titles.edit', 'Edit secret key')}
        </h2>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Name field */}
          <ApiKeyNameField
            name="name"
            label={t('apiKey.name.label', 'Name')}
            placeholder={t('apiKey.name.placeholder', 'API Key Name')}
            i18nPrefix="apiKey"
          />

          {/* Permission Type field */}
          <PermissionTypeField
            name="permissionType"
            label={t('apiKey.permissionType.label', 'Permissions')}
            onChange={(value: PermissionTypeValue) => setPermissionType(value)}
            i18nPrefix="apiKey"
          />

          {/* Information message about permissions */}
          <PermissionNotification i18nPrefix="apiKey" />

          {/* Resource Permissions - using the reusable component */}
          <ApiKeyPermissionSection permissionType={permissionType} i18nPrefix="apiKey" />

          {/* Form actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              {t('apiKey.actions.cancel', 'Cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting || !isDirty}>
              {isSubmitting
                ? t('apiKey.actions.submitting', 'Saving...')
                : t('apiKey.actions.save', 'Save')}
            </Button>
          </div>

          {/* Language indicator (for dev/debug purposes) */}
          <div className="text-muted-foreground pt-2 text-xs">{currentLang}</div>
        </form>
      </div>
    </FormProvider>
  );
};

export default I18nEdit;
