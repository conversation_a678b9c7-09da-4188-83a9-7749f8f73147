import{M as i}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as p,dX as d,e1 as u,j as a}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import{b as I}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var v=()=>{const{id:r}=p(),{inventory_item:t,isPending:o,isError:e,error:s}=d(r),{mutateAsync:m,isPending:n}=u(r);if(e)throw s;return a.jsx(I,{children:a.jsx(i,{isPending:o,isMutating:n,hook:m,metadata:t==null?void 0:t.metadata})})};export{v as Component};
