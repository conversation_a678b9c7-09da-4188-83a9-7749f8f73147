import type { <PERSON>a, StoryObj } from '@storybook/react';
import CoverflowCarousel from '../carousel-coverflow';
import '../carousel.css';

const meta: Meta<typeof CoverflowCarousel> = {
  title: 'UI/Carousel/CarouselCoverflow',
  component: CoverflowCarousel,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {
    autoplay: {
      control: 'boolean',
      description: 'Enable autoplay',
    },
    autoplayDelay: {
      control: { type: 'number', min: 1000, max: 10000, step: 500 },
      description: 'Autoplay delay in milliseconds',
    },
  },
};

export default meta;
type Story = StoryObj<typeof CoverflowCarousel>;

// Sample brand slides that match the design from the Figma "Brand Slide" frame
const brandSlides = [
  {
    id: 1,
    title: 'Backup Power Set',
    image: 'https://via.placeholder.com/600x400/0088cc/ffffff?text=Backup+Power+Set',
    link: '#backup-power',
  },
  {
    id: 2,
    title: 'Safety Equipment',
    image: 'https://via.placeholder.com/600x400/2299dd/ffffff?text=Safety+Equipment',
    link: '#safety-equipment',
  },
  {
    id: 3,
    title: 'Lighting Big Bonus 2023',
    image: 'https://via.placeholder.com/600x400/44aaee/ffffff?text=Lighting+Bonus',
    link: '#lighting-bonus',
  },
  {
    id: 4,
    title: 'New Arrival',
    image: 'https://via.placeholder.com/600x400/cc0000/ffffff?text=New+Arrival',
    link: '#new-arrival',
  },
  {
    id: 5,
    title: 'Solar Panels',
    image: 'https://via.placeholder.com/600x400/66cc00/ffffff?text=Solar+Panels',
    link: '#solar-panels',
  },
];

// Default coverflow carousel example
export const Default: Story = {
  args: {
    items: brandSlides,
    autoplay: true,
    autoplayDelay: 3000,
  },
};

// Coverflow carousel with promotional design based on the Figma example
export const PromotionalBanner: Story = {
  args: {
    items: brandSlides,
    autoplay: true,
    autoplayDelay: 3000,
    swiperOptions: {
      effect: 'coverflow',
      grabCursor: true,
      centeredSlides: true,
      slidesPerView: 'auto',
      coverflowEffect: {
        rotate: 0,
        stretch: 0,
        depth: 200,
        modifier: 1.8,
        slideShadows: false,
      },
      initialSlide: 2,
      loop: true,
    },
  },
  render: (args) => (
    <div className="bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl font-bold mb-8 text-center">Featured Promotions</h2>
        <CoverflowCarousel {...args} />
      </div>
    </div>
  ),
};

// Coverflow carousel with custom styling for brand showcase
export const BrandShowcase: Story = {
  args: {
    items: brandSlides,
    autoplay: false,
    className: "brand-showcase-carousel",
  },
  render: (args) => (
    <div className="bg-white py-12">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-2 text-center">Our Products</h2>
        <p className="text-gray-600 text-center mb-8">Browse our featured product categories</p>
        <CoverflowCarousel {...args} />
      </div>
    </div>
  ),
};