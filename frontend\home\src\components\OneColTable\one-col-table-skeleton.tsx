'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { cva } from 'class-variance-authority';
import { ROW_HEIGHTS, DEFAULT_SKELETON_ROWS } from './constants';

// Define oneColTableVariants locally to avoid circular dependency
const oneColTableVariants = cva('w-full rounded-md overflow-hidden', {
  variants: {
    variant: {
      primary: 'bg-background border border-border',
      secondary: 'bg-muted',
      outline: 'border border-border',
    },
    size: {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
    },
    state: {
      default: '',
      loading: 'opacity-70 cursor-not-allowed',
      disabled: 'opacity-50 cursor-not-allowed',
      error: 'border-destructive',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
    state: 'default',
  },
});

export interface OneColTableSkeletonProps {
  /** Size variant matching the main component */
  size?: 'sm' | 'md' | 'lg';
  /** Number of skeleton rows to render */
  rowCount?: number;
  /** Whether to use simplified skeleton for performance */
  simplified?: boolean;
  /** Optional CSS class to be merged */
  className?: string;
}

/**
 * Skeleton loader for OneColTable
 *
 * Automatically adapts to the size prop and displays skeleton rows
 */
export const OneColTableSkeleton = React.forwardRef<HTMLDivElement, OneColTableSkeletonProps>(
  ({ size = 'md', rowCount = DEFAULT_SKELETON_ROWS, simplified = false, className }, ref) => {
    // Get row height based on size
    const rowHeight = ROW_HEIGHTS[size];

    // Simplified skeleton for performance-critical situations
    if (simplified) {
      return (
        <div
          ref={ref}
          className={cn(
            oneColTableVariants({ size, variant: 'primary', state: 'loading' }),
            className,
          )}
          aria-label="Loading items"
          role="status"
        >
          <Skeleton className={cn('w-full', `h-[${rowCount * rowHeight}px]`)} />
        </div>
      );
    }

    // Full detailed skeleton with multiple rows
    return (
      <div
        ref={ref}
        className={cn(
          oneColTableVariants({ size, variant: 'primary', state: 'loading' }),
          className,
        )}
        aria-label="Loading items"
        role="status"
      >
        <div className="flex flex-col divide-y">
          {Array.from({ length: rowCount }).map((_, index) => (
            <div key={index} className="flex items-center justify-between p-4">
              <div className="flex-1 space-y-2">
                <Skeleton
                  className={cn('h-4 w-2/3', size === 'sm' ? 'h-3' : size === 'lg' ? 'h-5' : 'h-4')}
                />
                <Skeleton
                  className={cn('h-3 w-1/2', size === 'sm' ? 'h-2' : size === 'lg' ? 'h-4' : 'h-3')}
                />
              </div>
              <Skeleton className="h-3 w-20" />
            </div>
          ))}
        </div>
      </div>
    );
  },
);

OneColTableSkeleton.displayName = 'OneColTableSkeleton';
