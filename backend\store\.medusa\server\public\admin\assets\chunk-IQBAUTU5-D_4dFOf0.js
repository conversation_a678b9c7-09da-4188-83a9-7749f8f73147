import{j as e,aB as l,b as a}from"./index-Bwql5Dzz.js";var t=({product:s})=>e.jsxs("div",{className:"flex h-full w-full max-w-[250px] items-center gap-x-3 overflow-hidden",children:[e.jsx("div",{className:"w-fit flex-shrink-0",children:e.jsx(l,{src:s.thumbnail})}),e.jsx("span",{title:s.title,className:"truncate",children:s.title})]}),i=()=>{const{t:s}=a();return e.jsx("div",{className:"flex h-full w-full items-center",children:e.jsx("span",{children:s("fields.product")})})};export{i as P,t as a};
