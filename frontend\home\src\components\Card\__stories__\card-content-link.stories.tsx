import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import Card<PERSON>ontentLink from '../card-content-link';
import { 
  lightingFixture,
  electricalSafetyFixture,
  electricalEquipmentFixture,
  noImageFixture,
  noDescriptionFixture,
  primaryImageOnlyFixture,
  secondaryImageOnlyFixture,
} from '../__fixtures__/card-content-link.fixtures';

const meta: Meta<typeof CardContentLink> = {
  title: 'UI/Card/CardContentLink',
  component: CardContentLink,
  parameters: {
    layout: 'centered',
    // Accessibility configuration
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'image-alt-text',
            enabled: true,
          },
        ],
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    onClick: {
      action: 'clicked',
      description: 'Optional click handler for links in the card',
    },
  },
};

export default meta;
type Story = StoryObj<typeof CardContentLink>;

// Content variations
export const WithoutImage: Story = {
  args: {
    ...noImageFixture,
    onClick: action('link clicked'),
  },
};

export const WithoutDescription: Story = {
  args: {
    ...noDescriptionFixture,
    onClick: action('link clicked'),
  },
};

export const WithPrimaryImageOnly: Story = {
  args: {
    ...primaryImageOnlyFixture,
    onClick: action('link clicked'),
  },
};

export const WithSecondaryImageOnly: Story = {
  args: {
    ...secondaryImageOnlyFixture,
    onClick: action('link clicked'),
  },
};

// Interactive stories with state
export const WithInteractiveLinks: Story = {
  args: {
    ...lightingFixture,
    onClick: action('link clicked with custom handler'),
  },
  parameters: {
    actions: {
      handles: ['click a[href]'],
    },
  },
};

// Accessibility test stories
export const AccessibilityTest: Story = {
  args: {
    ...lightingFixture,
  },
  parameters: {
    a11y: {
      config: {
        rules: [
          {
            id: 'color-contrast',
            enabled: true,
          },
          {
            id: 'image-alt-text',
            enabled: true,
          },
          {
            id: 'link-name',
            enabled: true,
          },
        ],
      },
    },
  },
};

// Base stories using fixtures
export const LightingCard: Story = {
  args: {
    ...lightingFixture,
    onClick: action('link clicked'),
  },
};

export const ElectricalSafetyCard: Story = {
  args: {
    ...electricalSafetyFixture,
    onClick: action('link clicked'),
  },
};

export const ElectricalEquipmentCard: Story = {
  args: {
    ...electricalEquipmentFixture,
    onClick: action('link clicked'),
  },
};