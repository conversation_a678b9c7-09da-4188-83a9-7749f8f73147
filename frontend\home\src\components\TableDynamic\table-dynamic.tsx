'use client';

import * as React from 'react';
import { useRef } from 'react';
import { cva } from 'class-variance-authority';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useLanguageChange } from '@/hooks/useLanguageChange';
import { cn } from '@/lib/utils';
import {
  DEFAULT_COLUMN_VISIBILITY,
  DEFAULT_FILTERING,
  DEFAULT_PAGINATION,
  DEFAULT_ROW_EXPANSION,
  DEFAULT_ROW_SELECTION,
  DEFAULT_SORTING,
  DEFAULT_HEADER,
  DEFAULT_FOOTER,
} from './constants';
import { TableContextProvider } from './table-dynamic-context';
import { TableDynamicHeader } from './table-dynamic-header';
import { TableDynamicBody } from './table-dynamic-body';
import { TableDynamicFooter } from './table-dynamic-footer';
import { TableDynamicFilter } from './table-dynamic-filter';
import { TableDynamicSkeleton } from './table-dynamic-skeleton';
import type { TableDynamicProps } from './types';

// CVA variant definition for TableDynamic
export const tableDynamicVariants = cva('w-full overflow-hidden', {
  variants: {
    variant: {
      primary: 'bg-background border border-border rounded-md',
      secondary: 'bg-muted rounded-md',
      outline: 'border border-border rounded-md',
    },
    size: {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
    },
    state: {
      default: '',
      loading: 'opacity-70 pointer-events-none',
      disabled: 'opacity-50 pointer-events-none',
      error: 'border-destructive',
    },
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md',
    state: 'default',
  },
});

/**
 * TableDynamic - A dynamic table component with virtualization, filtering, sorting, and more
 *
 * @example
 * // Basic usage
 * <TableDynamic
 *   data={data}
 *   columns={columns}
 * />
 *
 * // With virtualization for large datasets
 * <TableDynamic
 *   data={largeDataset}
 *   columns={columns}
 *   virtualized
 *   height={500}
 * />
 *
 * // With sorting and filtering
 * <TableDynamic
 *   data={data}
 *   columns={columns}
 *   sorting={{ enabled: true }}
 *   filtering={{ enabled: true, showGlobalFilter: true }}
 * />
 */
const TableDynamic = React.forwardRef<HTMLDivElement, TableDynamicProps>(
  (
    {
      // Variant props
      variant = 'primary',
      size = 'md',
      className,

      // State props
      loading = false,
      disabled = false,
      error,

      // Data props
      data = [],
      columns = [],

      // Feature props
      sorting = DEFAULT_SORTING,
      filtering = DEFAULT_FILTERING,
      pagination = DEFAULT_PAGINATION,
      columnVisibility = DEFAULT_COLUMN_VISIBILITY,
      header = DEFAULT_HEADER,
      footer = DEFAULT_FOOTER,
      rowSelection = DEFAULT_ROW_SELECTION,
      rowExpansion = DEFAULT_ROW_EXPANSION,

      // Event handlers
      onRowClick,
      onCellClick,

      // Customization
      emptyState,
      showBorder = true,
      striped = false,
      showHeader = true,
      showFooter = true,

      // Animation
      animate = true,
      animationDelay = 0.05,

      // Virtualization
      virtualized = false,
      height,
      width,
      overscan,

      // Skeleton props
      skeletonProps,

      // i18n props
      i18nNamespace,
      i18nPrefix = 'tableDynamic',

      // Other props
      tableRef,
      ...restProps
    },
    ref,
  ) => {
    const { t } = useTranslation(i18nNamespace);
    const currentLang = useLanguageChange();

    // Reference for the table container
    const containerRef = useRef<HTMLDivElement>(null);
    const internalRef = tableRef || containerRef;

    // Component state determination (for variants)
    const componentState = React.useMemo(() => {
      if (loading) return 'loading';
      if (disabled) return 'disabled';
      if (error) return 'error';
      return 'default';
    }, [loading, disabled, error]);

    // Loading state - render skeleton
    if (loading) {
      return (
        <TableDynamicSkeleton
          columnCount={columns.length}
          rowCount={skeletonProps?.rowCount || 10}
          simplified={skeletonProps?.simplified}
          size={size}
          showHeader={showHeader}
          showFooter={showFooter}
          showFilters={filtering?.enabled && filtering?.showGlobalFilter}
          height={height}
          className={className}
          i18nNamespace={i18nNamespace}
          i18nPrefix={`${i18nPrefix}.skeleton`}
        />
      );
    }

    // Error state
    if (error) {
      return (
        <div
          className={cn(
            tableDynamicVariants({ variant, size, state: 'error' }),
            'text-destructive flex items-center justify-center p-4',
            className,
          )}
        >
          {typeof error === 'string' ? error : t(`${i18nPrefix}.error.generic`)}
        </div>
      );
    }

    // Empty state
    if (data.length === 0 && !loading) {
      return (
        <div
          className={cn(
            tableDynamicVariants({ variant, size, state: componentState }),
            'text-muted-foreground flex flex-col items-center justify-center p-8',
            className,
          )}
        >
          {emptyState || (
            <>
              <h4 className="mb-2 text-lg font-medium">{t(`${i18nPrefix}.empty.title`)}</h4>
              <p>{t(`${i18nPrefix}.empty.description`)}</p>
            </>
          )}
        </div>
      );
    }

    return (
      <TableContextProvider
        data={data}
        columns={columns}
        sorting={sorting}
        filtering={filtering}
        pagination={pagination}
        columnVisibility={columnVisibility}
        rowSelection={rowSelection}
        rowExpansion={rowExpansion}
        onRowClick={onRowClick}
        onCellClick={onCellClick}
      >
        <div ref={ref} className={className} {...restProps}>
          {/* Filters */}
          {filtering?.enabled && filtering?.showGlobalFilter && (
            <TableDynamicFilter
              showGlobalFilter={true}
              showColumnFilters={true}
              debounce={filtering.debounce}
              debounceTime={filtering.debounceTime}
              i18nNamespace={i18nNamespace}
              i18nPrefix={`${i18nPrefix}.filter`}
            />
          )}

          {/* Table */}
          <div
            ref={internalRef}
            className={cn(
              tableDynamicVariants({ variant, size, state: componentState }),
              !showBorder && 'border-0',
            )}
          >
            {virtualized ? (
              /* Virtualized table */
              <TableDynamicBody
                virtualized={true}
                height={height}
                width={width}
                overscan={overscan}
                striped={striped}
                showBorders={true}
                size={size}
                animationDelay={animationDelay}
                animate={animate}
              />
            ) : (
              /* Standard table */
              <table className="w-full border-collapse">
                {showHeader && header?.show !== false && (
                  <TableDynamicHeader
                    sticky={header?.sticky}
                    showColumnSeparators={header?.showColumnSeparators}
                    height={header?.height}
                    size={size}
                  />
                )}

                <TableDynamicBody
                  virtualized={false}
                  striped={striped}
                  showBorders={true}
                  size={size}
                  animationDelay={animationDelay}
                  animate={animate}
                />

                {showFooter && footer?.show !== false && (
                  <TableDynamicFooter
                    sticky={footer?.sticky}
                    showColumnSeparators={footer?.showColumnSeparators}
                    footerContent={footer?.footerContent}
                    showSummary={footer?.showSummary}
                    height={footer?.height}
                    size={size}
                    i18nNamespace={i18nNamespace}
                    i18nPrefix={`${i18nPrefix}.footer`}
                  />
                )}
              </table>
            )}
          </div>

          {/* Language indicator (hidden in production) */}
          <div className="hidden">
            <div className="text-muted-foreground mt-2 text-xs">
              {t('language.current')}: {currentLang}
            </div>
          </div>
        </div>
      </TableContextProvider>
    );
  },
);

TableDynamic.displayName = 'TableDynamic';

export default TableDynamic;
