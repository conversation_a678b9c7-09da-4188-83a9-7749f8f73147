import{I as De}from"./chunk-PYIO3TDQ-D8Zv8hXV.js";import{g as T}from"./chunk-PDWBYQOW-BedvhUOI.js";import{D as Ve,I as ie,h as de,e as ze,f as $e,g as Fe,a as ue}from"./chunk-GE4APTT2-vaCdy0-f.js";import{c as qe}from"./chunk-MWVM4TYO-bKUcYSnf.js";import{c as P}from"./chunk-6GU6IDUA-CDc7wW5L.js";import{K as Ke}from"./chunk-6HTZNHPT-N4svn6ad.js";import{S as I}from"./chunk-JGQGO74V-DtHO1ucg.js";import{r as h,cO as Ge,bB as Be,eD as pe,aM as w,j as t,bQ as me,bE as He,b_ as Ue,bG as We,bF as fe,bS as Xe,b7 as Ye,b8 as Ze,b9 as Qe,a6 as f,bn as M,b as $,cB as Je,a8 as et,a9 as tt,y as nt,w as _,H as ot,T as F,B as W,m as he,I as Q,b4 as st,D as k,W as it,X as rt,v as z,V as at,bc as lt,z as X,Y as R,C as ct}from"./index-Bwql5Dzz.js";import{f as ee,C as dt}from"./index.esm-3G2Z4eQ8.js";import{X as ut}from"./x-mark-mini-DvSTI7zK.js";import{T as Y}from"./Trans-VWqfqpAH.js";import{C as D}from"./currency-input-Yr7vS0SV.js";var pt=Object.defineProperty,q=Object.getOwnPropertySymbols,ge=Object.prototype.hasOwnProperty,xe=Object.prototype.propertyIsEnumerable,re=(e,o,n)=>o in e?pt(e,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[o]=n,mt=(e,o)=>{for(var n in o)ge.call(o,n)&&re(e,n,o[n]);if(q)for(var n of q(o))xe.call(o,n)&&re(e,n,o[n]);return e},ft=(e,o)=>{var n={};for(var s in e)ge.call(e,s)&&o.indexOf(s)<0&&(n[s]=e[s]);if(e!=null&&q)for(var s of q(e))o.indexOf(s)<0&&xe.call(e,s)&&(n[s]=e[s]);return n};const ve=h.forwardRef((e,o)=>{var n=e,{color:s="currentColor"}=n,i=ft(n,["color"]);return h.createElement("svg",mt({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:o},i),h.createElement("path",{stroke:s,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.056 1.944h4v4M13.056 1.944l-4 4M1.944 9.056v4h4M1.944 13.056l4-4"}))});ve.displayName="ArrowsPointingOut";var ht=Object.defineProperty,K=Object.getOwnPropertySymbols,be=Object.prototype.hasOwnProperty,je=Object.prototype.propertyIsEnumerable,ae=(e,o,n)=>o in e?ht(e,o,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[o]=n,gt=(e,o)=>{for(var n in o)be.call(o,n)&&ae(e,n,o[n]);if(K)for(var n of K(o))je.call(o,n)&&ae(e,n,o[n]);return e},xt=(e,o)=>{var n={};for(var s in e)be.call(e,s)&&o.indexOf(s)<0&&(n[s]=e[s]);if(e!=null&&K)for(var s of K(e))o.indexOf(s)<0&&je.call(e,s)&&(n[s]=e[s]);return n};const Ce=h.forwardRef((e,o)=>{var n=e,{color:s="currentColor"}=n,i=xt(n,["color"]);return h.createElement("svg",gt({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:o},i),h.createElement("path",{stroke:s,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M7.5 13.945a6.444 6.444 0 1 0 0-12.89 6.444 6.444 0 0 0 0 12.89M8.167 5.5h2.666M4.167 5.5h1.777M5.944 3.944v3.111M9.056 9.5h1.777M4.167 9.5h2.666M9.056 7.944v3.111"}))});Ce.displayName="CircleSliders";var O="Accordion",vt=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[te,bt,jt]=Ge(O),[B,an]=Be(O,[jt,pe]),ne=pe(),ye=w.forwardRef((e,o)=>{const{type:n,...s}=e,i=s,a=s;return t.jsx(te.Provider,{scope:e.__scopeAccordion,children:n==="multiple"?t.jsx(wt,{...a,ref:o}):t.jsx(_t,{...i,ref:o})})});ye.displayName=O;var[_e,Ct]=B(O),[we,yt]=B(O,{collapsible:!1}),_t=w.forwardRef((e,o)=>{const{value:n,defaultValue:s,onValueChange:i=()=>{},collapsible:a=!1,...r}=e,[l,c]=me({prop:n,defaultProp:s,onChange:i});return t.jsx(_e,{scope:e.__scopeAccordion,value:l?[l]:[],onItemOpen:c,onItemClose:w.useCallback(()=>a&&c(""),[a,c]),children:t.jsx(we,{scope:e.__scopeAccordion,collapsible:a,children:t.jsx(Pe,{...r,ref:o})})})}),wt=w.forwardRef((e,o)=>{const{value:n,defaultValue:s,onValueChange:i=()=>{},...a}=e,[r=[],l]=me({prop:n,defaultProp:s,onChange:i}),c=w.useCallback(p=>l((u=[])=>[...u,p]),[l]),d=w.useCallback(p=>l((u=[])=>u.filter(m=>m!==p)),[l]);return t.jsx(_e,{scope:e.__scopeAccordion,value:r,onItemOpen:c,onItemClose:d,children:t.jsx(we,{scope:e.__scopeAccordion,collapsible:!0,children:t.jsx(Pe,{...a,ref:o})})})}),[Pt,H]=B(O),Pe=w.forwardRef((e,o)=>{const{__scopeAccordion:n,disabled:s,dir:i,orientation:a="vertical",...r}=e,l=w.useRef(null),c=He(l,o),d=bt(n),u=Ue(i)==="ltr",m=We(e.onKeyDown,g=>{var E;if(!vt.includes(g.key))return;const x=g.target,j=d().filter(L=>{var se;return!((se=L.ref.current)!=null&&se.disabled)}),y=j.findIndex(L=>L.ref.current===x),S=j.length;if(y===-1)return;g.preventDefault();let C=y;const v=0,N=S-1,b=()=>{C=y+1,C>N&&(C=v)},A=()=>{C=y-1,C<v&&(C=N)};switch(g.key){case"Home":C=v;break;case"End":C=N;break;case"ArrowRight":a==="horizontal"&&(u?b():A());break;case"ArrowDown":a==="vertical"&&b();break;case"ArrowLeft":a==="horizontal"&&(u?A():b());break;case"ArrowUp":a==="vertical"&&A();break}const U=C%S;(E=j[U].ref.current)==null||E.focus()});return t.jsx(Pt,{scope:n,disabled:s,direction:i,orientation:a,children:t.jsx(te.Slot,{scope:n,children:t.jsx(fe.div,{...r,"data-orientation":a,ref:c,onKeyDown:s?void 0:m})})})}),G="AccordionItem",[Ot,oe]=B(G),Oe=w.forwardRef((e,o)=>{const{__scopeAccordion:n,value:s,...i}=e,a=H(G,n),r=Ct(G,n),l=ne(n),c=Xe(),d=s&&r.value.includes(s)||!1,p=a.disabled||e.disabled;return t.jsx(Ot,{scope:n,open:d,disabled:p,triggerId:c,children:t.jsx(Ye,{"data-orientation":a.orientation,"data-state":Se(d),...l,...i,ref:o,disabled:p,open:d,onOpenChange:u=>{u?r.onItemOpen(s):r.onItemClose(s)}})})});Oe.displayName=G;var Ne="AccordionHeader",Nt=w.forwardRef((e,o)=>{const{__scopeAccordion:n,...s}=e,i=H(O,n),a=oe(Ne,n);return t.jsx(fe.h3,{"data-orientation":i.orientation,"data-state":Se(a.open),"data-disabled":a.disabled?"":void 0,...s,ref:o})});Nt.displayName=Ne;var J="AccordionTrigger",Ae=w.forwardRef((e,o)=>{const{__scopeAccordion:n,...s}=e,i=H(O,n),a=oe(J,n),r=yt(J,n),l=ne(n);return t.jsx(te.ItemSlot,{scope:n,children:t.jsx(Ze,{"aria-disabled":a.open&&!r.collapsible||void 0,"data-orientation":i.orientation,id:a.triggerId,...l,...s,ref:o})})});Ae.displayName=J;var Ie="AccordionContent",Re=w.forwardRef((e,o)=>{const{__scopeAccordion:n,...s}=e,i=H(O,n),a=oe(Ie,n),r=ne(n);return t.jsx(Qe,{role:"region","aria-labelledby":a.triggerId,"data-orientation":i.orientation,...r,...s,ref:o,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});Re.displayName=Ie;function Se(e){return e?"open":"closed"}var At=ye,It=Oe,Rt=Ae,St=Re,kt=(e,o,n)=>({attribute:e,operator:o,value:P(n)}),ln=e=>[{value:e.gte,operator:"gte"},{value:e.lte,operator:"lte"},{value:e.gt,operator:"gt"},{value:e.lt,operator:"lt"},{value:e.eq,operator:"eq"}].filter(({value:s})=>s).map(({operator:s,value:i})=>kt(De,s,i)),ke=f.object({amount:f.union([f.string(),f.number()]),gte:f.union([f.string(),f.number()]).nullish(),lte:f.union([f.string(),f.number()]).nullish(),lt:f.number().nullish(),gt:f.number().nullish(),eq:f.number().nullish()}).refine(e=>e.amount!=="",{message:M("stockLocations.shippingOptions.conditionalPrices.errors.amountRequired"),path:["amount"]}).refine(e=>e.eq!==void 0||e.lt!==void 0||e.gt!==void 0?!0:e.gte!==void 0&&e.gte!==""||e.lte!==void 0&&e.lte!=="",{message:M("stockLocations.shippingOptions.conditionalPrices.errors.minOrMaxRequired"),path:["gte"]}).refine(e=>{if(e.gte!=null&&e.gte!==""&&e.lte!=null&&e.lte!==""){const o=P(e.gte),n=P(e.lte);return o<=n}return!0},{message:M("stockLocations.shippingOptions.conditionalPrices.errors.minGreaterThanMax"),path:["gte"]}),Et=ke.and(f.object({id:f.string().optional()}));function Ee(e,o){const n=e.prices;for(let s=0;s<n.length;s++)for(let i=s+1;i<n.length;i++){const a=n[s],r=n[i];if(a.amount===""||r.amount==="")continue;const l=P(a.amount),c=P(r.amount);l===c&&Mt(o,i),[{value:a.gte,type:"gte"},{value:a.lte,type:"lte"},{value:a.eq,type:"eq"},{value:a.lt,type:"lt"},{value:a.gt,type:"gt"}].forEach(p=>{if(!p.value&&p.value!==0)return;[{value:r.gte,type:"gte"},{value:r.lte,type:"lte"},{value:r.eq,type:"eq"},{value:r.lt,type:"lt"},{value:r.gt,type:"gt"}].forEach(m=>{if(!m.value&&m.value!==0)return;const g=P(p.value),x=P(m.value);g===x&&Dt(o,i,m.type)})})}}var Lt=f.object({prices:f.array(ke)}).superRefine(Ee),Tt=f.object({prices:f.array(Et)}).superRefine(Ee),Mt=(e,o)=>{e.addIssue({code:f.ZodIssueCode.custom,message:M("stockLocations.shippingOptions.conditionalPrices.errors.duplicateAmount"),path:["prices",o,"amount"]})},Dt=(e,o,n)=>{e.addIssue({code:f.ZodIssueCode.custom,message:M("stockLocations.shippingOptions.conditionalPrices.errors.overlappingConditions"),path:["prices",o,n]})},Le=h.createContext(null),cn=({children:e,onOpenConditionalPricesModal:o,onCloseConditionalPricesModal:n})=>t.jsx(Le.Provider,{value:{onOpenConditionalPricesModal:o,onCloseConditionalPricesModal:n},children:e}),Te=()=>{const e=h.useContext(Le);if(!e)throw new Error("useShippingOptionPrice must be used within a ShippingOptionPriceProvider");return e},Me=(e,o)=>{const n=o==="region"?"region_prices":"currency_prices",s=o==="region"?"conditional_region_prices":"conditional_currency_prices";return e.replace(n,s)},Vt="rule-item",V=e=>`${Vt}-${e}`,dn=({info:e,variant:o})=>{const{t:n}=$(),{getValues:s,setValue:i}=Je(),{onCloseConditionalPricesModal:a}=Te(),[r,l]=h.useState([V(0)]),{field:c,type:d,currency:p,name:u}=e,m=Me(c,d),g=et({defaultValues:{prices:s(m)||[{amount:"",gte:"",lte:null}]},resolver:tt(o==="create"?Lt:Tt)}),{fields:x,append:j,remove:y}=nt({control:g.control,name:"prices"}),S=()=>{j({amount:"",gte:"",lte:null}),l([...r,V(x.length)])},C=b=>{y(b)},v=g.handleSubmit(b=>{i(m,b.prices,{shouldDirty:!0,shouldValidate:!0,shouldTouch:!0}),a()},b=>{const A=Object.keys(b.prices||{});l(U=>{const E=new Set(U);return A.forEach(L=>{E.add(V(Number(L)))}),Array.from(E)})}),N=b=>{b.key==="Enter"&&(b.metaKey||b.ctrlKey)&&(console.log("Fired"),b.preventDefault(),b.stopPropagation(),v())};return t.jsx(_,{...g,children:t.jsx(Ke,{onSubmit:v,onKeyDown:N,className:"flex h-full flex-col",children:t.jsxs(I.Content,{children:[t.jsx(I.Header,{}),t.jsx(I.Body,{className:"size-full overflow-hidden",children:t.jsx("div",{className:"flex size-full flex-1 flex-col items-center overflow-y-auto",children:t.jsx("div",{className:"flex w-full max-w-[720px] flex-col gap-y-8 px-6 py-16",children:t.jsxs("div",{className:"flex w-full flex-col gap-y-6",children:[t.jsxs("div",{children:[t.jsx(I.Title,{asChild:!0,children:t.jsx(ot,{children:n("stockLocations.shippingOptions.conditionalPrices.header",{name:u})})}),t.jsx(I.Description,{asChild:!0,children:t.jsx(F,{size:"small",className:"text-ui-fg-subtle",children:n("stockLocations.shippingOptions.conditionalPrices.description")})})]}),t.jsx(zt,{value:r,onValueChange:l,children:x.map((b,A)=>t.jsx($t,{index:A,onRemove:C,currency:p,control:g.control},b.id))}),t.jsx("div",{className:"flex items-center justify-end",children:t.jsx(W,{variant:"secondary",size:"small",type:"button",onClick:S,children:n("stockLocations.shippingOptions.conditionalPrices.actions.addPrice")})})]})})})}),t.jsx(I.Footer,{children:t.jsxs("div",{className:"flex items-center justify-end gap-2",children:[t.jsx(I.Close,{asChild:!0,children:t.jsx(W,{variant:"secondary",size:"small",type:"button",children:n("actions.cancel")})}),t.jsx(W,{size:"small",type:"button",onClick:v,children:n("actions.save")})]})})]})})})},zt=({children:e,value:o,onValueChange:n})=>t.jsx(At,{type:"multiple",defaultValue:[V(0)],value:o,onValueChange:n,className:"flex flex-col gap-y-3",children:e}),$t=({index:e,currency:o,onRemove:n,control:s})=>{const{t:i}=$(),a=r=>{r.stopPropagation(),n(e)};return t.jsxs(It,{value:V(e),className:he("bg-ui-bg-component shadow-elevation-card-rest rounded-lg"),children:[t.jsx(Rt,{asChild:!0,children:t.jsxs("div",{className:"group/trigger flex w-full cursor-pointer items-start justify-between gap-x-2 p-3",children:[t.jsxs("div",{className:"flex flex-1 flex-wrap items-center justify-between gap-2",children:[t.jsx("div",{className:"flex h-7 items-center",children:t.jsx(qt,{index:e,currency:o,control:s})}),t.jsx("div",{className:"flex min-h-7 items-center",children:t.jsx(Kt,{index:e,currency:o,control:s})})]}),t.jsxs("div",{className:"flex items-center gap-x-2",children:[t.jsx(Q,{size:"small",variant:"transparent",className:"text-ui-fg-muted hover:text-ui-fg-subtle focus-visible:text-ui-fg-subtle",onClick:a,children:t.jsx(ut,{})}),t.jsx(Q,{size:"small",variant:"transparent",className:"text-ui-fg-muted hover:text-ui-fg-subtle focus-visible:text-ui-fg-subtle",children:t.jsx(st,{className:"transition-transform group-data-[state=open]/trigger:rotate-180"})})]})]})}),t.jsxs(St,{className:"text-ui-fg-subtle",children:[t.jsx(k,{variant:"dashed"}),t.jsx(_.Field,{control:s,name:`prices.${e}.amount`,render:({field:{value:r,onChange:l,...c}})=>t.jsx(_.Item,{children:t.jsxs("div",{className:"grid grid-cols-2 items-start gap-x-2 p-3",children:[t.jsx("div",{className:"flex h-8 items-center",children:t.jsx(_.Label,{children:i("stockLocations.shippingOptions.conditionalPrices.rules.amount")})}),t.jsxs("div",{className:"flex flex-col gap-y-1",children:[t.jsx(_.Control,{children:t.jsx(D,{className:"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",placeholder:ee({value:"0",decimalScale:o.decimal_digits}),decimalScale:o.decimal_digits,symbol:o.symbol_native,code:o.code,value:r,onValueChange:(d,p,u)=>l(u!=null&&u.value?u==null?void 0:u.value:""),autoFocus:!0,...c})}),t.jsx(_.ErrorMessage,{})]})]})})}),t.jsx(k,{variant:"dashed"}),t.jsx(_.Field,{control:s,name:`prices.${e}.gte`,render:({field:r})=>t.jsx(le,{field:r,label:i("stockLocations.shippingOptions.conditionalPrices.rules.gte"),currency:o,placeholder:"1000"})}),t.jsx(k,{variant:"dashed"}),t.jsx(_.Field,{control:s,name:`prices.${e}.lte`,render:({field:r})=>t.jsx(le,{field:r,label:i("stockLocations.shippingOptions.conditionalPrices.rules.lte"),currency:o,placeholder:"1000"})}),t.jsx(Ft,{index:e,control:s,currency:o})]})]})},le=({field:e,label:o,currency:n,placeholder:s})=>{const i=h.useRef(null),{value:a,onChange:r,ref:l,...c}=e,d=de(i,l),p=()=>{if(a===null){r(""),requestAnimationFrame(()=>{var m;(m=i.current)==null||m.focus()});return}r(null)},u=a===null;return t.jsx(_.Item,{children:t.jsxs("div",{className:"grid grid-cols-2 items-start gap-x-2 p-3",children:[t.jsxs("div",{className:"flex h-8 items-center gap-x-1",children:[t.jsx(Q,{size:"2xsmall",variant:"transparent",onClick:p,children:u?t.jsx(it,{}):t.jsx(rt,{})}),t.jsx(_.Label,{children:o})]}),!u&&t.jsxs("div",{className:"flex flex-col gap-y-1",children:[t.jsx(_.Control,{children:t.jsx(D,{className:"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",placeholder:ee({value:s,decimalScale:n.decimal_digits}),decimalScale:n.decimal_digits,symbol:n.symbol_native,code:n.code,value:a,ref:d,onValueChange:(m,g,x)=>r(x!=null&&x.value?x==null?void 0:x.value:""),...c})}),t.jsx(_.ErrorMessage,{})]})]})})},Ft=({index:e,control:o,currency:n})=>{const{t:s}=$(),i=z({control:o,name:`prices.${e}`});return i.eq==null&&i.gt==null&&i.lt==null?null:t.jsxs("div",{children:[t.jsx(k,{variant:"dashed"}),t.jsxs("div",{className:"flex items-center gap-x-1 px-3 pt-3",children:[t.jsx(F,{size:"small",leading:"compact",weight:"plus",children:s("stockLocations.shippingOptions.conditionalPrices.customRules.label")}),t.jsx(at,{content:s("stockLocations.shippingOptions.conditionalPrices.customRules.tooltip"),children:t.jsx(lt,{className:"text-ui-fg-muted"})})]}),t.jsxs("div",{children:[i.eq!=null&&t.jsxs("div",{className:"grid grid-cols-2 items-start gap-x-2 p-3",children:[t.jsx("div",{className:"flex h-8 items-center",children:t.jsx(X,{weight:"plus",size:"small",children:s("stockLocations.shippingOptions.conditionalPrices.customRules.eq")})}),t.jsx(D,{className:"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",symbol:n.symbol_native,code:n.code,value:i.eq,disabled:!0})]}),i.gt!=null&&t.jsxs(h.Fragment,{children:[t.jsx(k,{variant:"dashed"}),t.jsxs("div",{className:"grid grid-cols-2 items-start gap-x-2 p-3",children:[t.jsx("div",{className:"flex h-8 items-center",children:t.jsx(X,{weight:"plus",size:"small",children:s("stockLocations.shippingOptions.conditionalPrices.customRules.gt")})}),t.jsx(D,{className:"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",symbol:n.symbol_native,code:n.code,value:i.gt,disabled:!0})]})]}),i.lt!=null&&t.jsxs(h.Fragment,{children:[t.jsx(k,{variant:"dashed"}),t.jsxs("div",{className:"grid grid-cols-2 items-start gap-x-2 p-3",children:[t.jsx("div",{className:"flex h-8 items-center",children:t.jsx(X,{weight:"plus",size:"small",children:s("stockLocations.shippingOptions.conditionalPrices.customRules.lt")})}),t.jsx(D,{className:"bg-ui-bg-field-component hover:bg-ui-bg-field-component-hover focus-visible:bg-ui-bg-field-component-hover",symbol:n.symbol_native,code:n.code,value:i.lt,disabled:!0})]})]})]})]})},qt=({index:e,currency:o,control:n})=>{const s=z({control:n,name:`prices.${e}.amount`});if(s===""||s===void 0)return t.jsx(F,{size:"small",weight:"plus",children:"-"});const i=P(s);return t.jsx(F,{size:"small",weight:"plus",children:T(i,o.code)})},Z=({children:e})=>t.jsx("div",{className:"text-ui-fg-subtle txt-small flex flex-wrap items-center gap-1.5",children:e}),Kt=({index:e,currency:o,control:n})=>{const{t:s,i18n:i}=$(),a=z({control:n,name:`prices.${e}.gte`}),r=z({control:n,name:`prices.${e}.lte`});return(()=>{const c=a?P(a):void 0,d=r?P(r):void 0;return!c&&!d?null:c&&!d?t.jsx(Z,{children:t.jsx(Y,{i18n:i,i18nKey:"stockLocations.shippingOptions.conditionalPrices.summaries.greaterThan",components:[t.jsx(R,{size:"2xsmall"},"attribute"),t.jsx(R,{size:"2xsmall"},"gte")],values:{attribute:s("stockLocations.shippingOptions.conditionalPrices.attributes.cartItemTotal"),gte:T(c,o.code)}})}):!c&&d?t.jsx(Z,{children:t.jsx(Y,{i18n:i,i18nKey:"stockLocations.shippingOptions.conditionalPrices.summaries.lessThan",components:[t.jsx(R,{size:"2xsmall"},"attribute"),t.jsx(R,{size:"2xsmall"},"lte")],values:{attribute:s("stockLocations.shippingOptions.conditionalPrices.attributes.cartItemTotal"),lte:T(d,o.code)}})}):c&&d?t.jsx(Z,{children:t.jsx(Y,{i18n:i,i18nKey:"stockLocations.shippingOptions.conditionalPrices.summaries.range",components:[t.jsx(R,{size:"2xsmall"},"attribute"),t.jsx(R,{size:"2xsmall"},"gte"),t.jsx(R,{size:"2xsmall"},"lte")],values:{attribute:s("stockLocations.shippingOptions.conditionalPrices.attributes.cartItemTotal"),gte:T(c,o.code),lte:T(d,o.code)}})}):null})()},ce=({context:e,code:o,header:n,type:s})=>{const[i,a]=h.useState(0),r=h.useCallback(j=>{if(j){const y=j.offsetWidth;a(y)}},[]),{field:l,control:c,renderProps:d}=ze({context:e}),p=$e({context:e}),{container:u,input:m}=d,{isAnchor:g}=u,x=qe[o.toUpperCase()];return t.jsx(ct,{control:c,name:l,render:({field:j})=>t.jsx(Fe,{...u,...p,outerComponent:t.jsx(Gt,{header:n,isAnchor:g,field:l,control:c,symbolWidth:i,type:s,currency:x}),children:t.jsx(Bt,{field:j,inputProps:m,currencyInfo:x,onMeasureSymbol:r})})})},Gt=({isAnchor:e,header:o,field:n,control:s,symbolWidth:i,type:a,currency:r})=>{const{onOpenConditionalPricesModal:l}=Te(),c=h.useRef(null),d=Me(n,a),p=z({control:s,name:d});return h.useEffect(()=>{const u=m=>{var g;e&&(m.metaKey||m.ctrlKey)&&m.key.toLowerCase()==="b"&&(m.preventDefault(),(g=c.current)==null||g.click())};return document.addEventListener("keydown",u),()=>document.removeEventListener("keydown",u)},[e]),t.jsxs("div",{className:"absolute inset-y-0 z-[3] flex w-fit items-center justify-center",style:{left:i?`${i+16+4}px`:void 0},children:[(p==null?void 0:p.length)>0&&!e&&t.jsx("div",{className:"flex size-[15px] items-center justify-center group-hover/container:hidden",children:t.jsx(Ce,{className:"text-ui-fg-interactive"})}),t.jsx("button",{ref:c,type:"button",className:he("hover:text-ui-fg-subtle text-ui-fg-muted transition-fg hidden size-[15px] items-center justify-center rounded-md bg-transparent group-hover/container:flex",{flex:e}),onClick:()=>l({type:a,field:n,currency:r,name:o}),children:t.jsx(ve,{})})]})},Bt=({field:e,onMeasureSymbol:o,inputProps:n,currencyInfo:s})=>{const{value:i,onChange:a,onBlur:r,ref:l,...c}=e,{ref:d,onBlur:p,onFocus:u,onChange:m,...g}=n,x=h.useCallback(v=>{const N=typeof v=="number"?v.toString():v||"";return ee({value:N,decimalScale:s.decimal_digits,disableGroupSeparators:!0,decimalSeparator:"."})},[s]),[j,y]=h.useState(i||""),S=(v,N,b)=>{if(!v){y("");return}y(v)};h.useEffect(()=>{let v=i;isNaN(Number(i))||(v=x(v)),y(v)},[i,x]);const C=de(d,l);return t.jsxs("div",{className:"relative flex size-full items-center",children:[t.jsx("span",{className:"txt-compact-small text-ui-fg-muted pointer-events-none absolute left-0 w-fit min-w-4","aria-hidden":!0,ref:o,children:s.symbol_native}),t.jsx(dt,{...c,...g,ref:C,className:"txt-compact-small w-full flex-1 cursor-default appearance-none bg-transparent pl-[60px] text-right outline-none",value:j||void 0,onValueChange:S,formatValueOnBlur:!0,onBlur:()=>{r(),p(),m(j,i)},onFocus:u,decimalScale:s.decimal_digits,decimalsLimit:s.decimal_digits,autoComplete:"off",tabIndex:-1})]})},Ht=ue(),un=({name:e,currencies:o=[],regions:n=[],pricePreferences:s=[]})=>{const{t:i}=$();return h.useMemo(()=>[Ht.column({id:"name",name:i("fields.name"),disableHiding:!0,header:i("fields.name"),cell:a=>t.jsx(Ve.ReadonlyCell,{context:a,children:e})}),...Ut({currencies:o,regions:n,pricePreferences:s,getFieldName:(a,r)=>{var l;return(l=a.column.id)!=null&&l.startsWith("currency_prices")?`currency_prices.${r}`:`region_prices.${r}`},t:i})],[i,o,n,s,e])},Ut=({currencies:e,regions:o,pricePreferences:n,getFieldName:s,t:i})=>{const a=ue();return[...(e==null?void 0:e.map(r=>{const l=n==null?void 0:n.find(d=>d.attribute==="currency_code"&&d.value===r),c=i("fields.priceTemplate",{regionOrCurrency:r.toUpperCase()});return a.column({id:`currency_prices.${r}`,name:i("fields.priceTemplate",{regionOrCurrency:r.toUpperCase()}),field:d=>s(d,r),type:"number",header:()=>t.jsxs("div",{className:"flex w-full items-center justify-between gap-3",children:[t.jsx("span",{className:"truncate",title:c,children:c}),t.jsx(ie,{includesTax:l==null?void 0:l.is_tax_inclusive})]}),cell:d=>t.jsx(ce,{type:"currency",header:c,code:r,context:d})})}))??[],...(o==null?void 0:o.map(r=>{const l=n==null?void 0:n.find(d=>d.attribute==="region_id"&&d.value===r.id),c=i("fields.priceTemplate",{regionOrCurrency:r.name});return a.column({id:`region_prices.${r.id}`,name:i("fields.priceTemplate",{regionOrCurrency:r.name}),field:d=>s(d,r.id),type:"number",header:()=>t.jsxs("div",{className:"flex w-full items-center justify-between gap-3",children:[t.jsx("span",{className:"truncate",title:c,children:c}),t.jsx(ie,{includesTax:l==null?void 0:l.is_tax_inclusive})]}),cell:d=>(e==null?void 0:e.find(u=>u===r.currency_code))?t.jsx(ce,{type:"region",header:c,code:r.currency_code,context:d}):null})}))??[]]};export{ke as C,cn as S,Et as U,dn as a,ln as b,un as u};
