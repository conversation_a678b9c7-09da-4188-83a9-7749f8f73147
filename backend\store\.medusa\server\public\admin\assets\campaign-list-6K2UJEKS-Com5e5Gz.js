import{a as x}from"./chunk-WYX5PIA3-DoOUp1ge.js";import{T as p,a as b}from"./chunk-MSDRGCRR-BLk8RuFZ.js";import{P as h}from"./chunk-P3UUX2T6-CnJzifYv.js";import{b as c,r as j,j as a,a as T,da as w,k as y,H as _,L as N,B as P,dl as D,A as S,t as g}from"./index-Bwql5Dzz.js";import{c as v}from"./index-BxZ1678G.js";import{u as H,_ as k}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{u as A}from"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as E}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as q}from"./use-prompt-pbDx0Sfe.js";import{P as L}from"./pencil-square-6wRbnn1C.js";import{T as O}from"./trash-BBylvTAG.js";import{C as z}from"./container-Dqi2woPF.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var B=({prefix:e,pageSize:t=20})=>{const r=A(["offset","q","order","created_at","updated_at"],e),{offset:s,q:i,order:l,created_at:n,updated_at:m}=r;return{searchParams:{limit:t,offset:s?Number(s):0,order:l,created_at:n?JSON.parse(n):void 0,updated_at:m?JSON.parse(m):void 0,q:i},raw:r}},I=({description:e})=>e?a.jsx("div",{className:"flex h-full w-full items-center overflow-hidden",children:a.jsx("span",{className:"truncate",children:e})}):a.jsx(h,{}),M=()=>{const{t:e}=c();return a.jsx("div",{className:"flex h-full w-full items-center",children:a.jsx("span",{className:"truncate",children:e("fields.description")})})},J=({name:e})=>e?a.jsx("div",{className:"flex h-full w-full items-center overflow-hidden",children:a.jsx("span",{className:"truncate",children:e})}):a.jsx(h,{}),Q=()=>{const{t:e}=c();return a.jsx("div",{className:"flex h-full w-full items-center",children:a.jsx("span",{className:"truncate",children:e("fields.name")})})},o=v(),R=()=>{const{t:e}=c();return j.useMemo(()=>[o.accessor("name",{header:()=>a.jsx(Q,{}),cell:({getValue:t})=>a.jsx(J,{name:t()})}),o.accessor("description",{header:()=>a.jsx(M,{}),cell:({getValue:t})=>a.jsx(I,{description:t()})}),o.accessor("campaign_identifier",{header:()=>a.jsx(p,{text:e("campaigns.fields.identifier")}),cell:({getValue:t})=>{const r=t();return a.jsx(b,{text:r})}}),o.accessor("starts_at",{header:()=>a.jsx(p,{text:e("campaigns.fields.start_date")}),cell:({getValue:t})=>{const r=t();if(!r)return;const s=new Date(r);return a.jsx(x,{date:s})}}),o.accessor("ends_at",{header:()=>a.jsx(p,{text:e("campaigns.fields.end_date")}),cell:({getValue:t})=>{const r=t();if(!r)return;const s=new Date(r);return a.jsx(x,{date:s})}})],[e])},f=20,W=()=>{const{t:e}=c(),{raw:t,searchParams:r}=B({pageSize:f}),{campaigns:s,count:i,isPending:l,isError:n,error:m}=w(r,{placeholderData:y}),d=Y(),{table:C}=H({data:s??[],columns:d,count:i,enablePagination:!0,getRowId:u=>u.id,pageSize:f});if(n)throw m;return a.jsxs(z,{className:"divide-y p-0",children:[a.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[a.jsx(_,{level:"h2",children:e("campaigns.domain")}),a.jsx(N,{to:"/campaigns/create",children:a.jsx(P,{size:"small",variant:"secondary",children:e("actions.create")})})]}),a.jsx(k,{table:C,columns:d,count:i,pageSize:f,pagination:!0,search:!0,navigateTo:u=>u.id,isLoading:l,queryObject:t,orderBy:[{key:"name",label:e("fields.name")},{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}]})]})},$=({campaign:e})=>{const{t}=c(),r=q(),{mutateAsync:s}=D(e.id),i=async()=>{await r({title:t("general.areYouSure"),description:t("campaigns.deleteCampaignWarning",{name:e.name}),verificationInstruction:t("general.typeToConfirm"),verificationText:e.name,confirmText:t("actions.delete"),cancelText:t("actions.cancel")})&&await s(void 0,{onSuccess:()=>{g.success(t("campaigns.delete.successToast",{name:e.name}))},onError:n=>{g.error(n.message)}})};return a.jsx(S,{groups:[{actions:[{icon:a.jsx(L,{}),label:t("actions.edit"),to:`/campaigns/${e.id}/edit`}]},{actions:[{icon:a.jsx(O,{}),label:t("actions.delete"),onClick:i}]}]})},G=v(),Y=()=>{const e=R();return j.useMemo(()=>[...e,G.display({id:"actions",cell:({row:t})=>a.jsx($,{campaign:t.original})})],[e])},Pe=()=>{const{getWidgets:e}=T();return a.jsx(E,{widgets:{after:e("campaign.list.after"),before:e("campaign.list.before")},hasOutlet:!0,children:a.jsx(W,{})})};export{Pe as Component};
