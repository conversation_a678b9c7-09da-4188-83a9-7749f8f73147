import type { <PERSON>a, StoryObj } from '@storybook/react';
import { I18nextProvider } from 'react-i18next';
import { i18n } from '@/lib';
import { Create, Edit } from '..';
import {
  sampleProjects,
  sampleEnvironments,
  sampleApiKeyAll,
  sampleApiKeyRestricted,
  sampleApiKeyReadOnly,
  sampleApiKeyNoName,
  sampleApiKeyRevoked,
  sampleApiKeyExpired,
} from '../__fixtures__/ApiKey.fixtures';

// Set a default language
i18n.client.i18n.changeLanguage('en');

const meta = {
  title: 'Components/ApiKey/Variants',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Showcases various variants and configurations of API key components.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="max-w-3xl rounded-lg bg-gray-50 p-4 shadow-sm">
        <Story />
      </div>
    ),
  ],
  tags: ['autodocs'],
} as Meta;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * The standard Create API Key form with default properties.
 */
export const DefaultCreateForm: Story = {
  render: () => (
    <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
  ),
};

/**
 * Create form with expanded advanced options.
 */
export const CreateWithAdvancedOptions: Story = {
  render: () => (
    <Create
      onSubmit={() => {}}
      onCancel={() => {}}
      availableProjects={sampleProjects}
      availableEnvironments={sampleEnvironments}
      showAdvancedOptions={true}
      showTags={true}
      showIpRestrictions={true}
      showDomainRestrictions={true}
      showRateLimit={true}
    />
  ),
};

/**
 * Create form with compact layout.
 */
export const CreateCompactVariant: Story = {
  render: () => (
    <Create
      onSubmit={() => {}}
      onCancel={() => {}}
      availableProjects={sampleProjects}
      variant="compact"
    />
  ),
};

/**
 * Create form with expanded layout.
 */
export const CreateExpandedVariant: Story = {
  render: () => (
    <Create
      onSubmit={() => {}}
      onCancel={() => {}}
      availableProjects={sampleProjects}
      variant="expanded"
      defaultExpirationDays={180}
      autoFocusNameField={true}
    />
  ),
};

/**
 * Create form with faster animations.
 */
export const CreateWithFastAnimations: Story = {
  render: () => (
    <Create
      onSubmit={() => {}}
      onCancel={() => {}}
      availableProjects={sampleProjects}
      animationSpeed="fast"
    />
  ),
};

/**
 * Edit form for an API key with all permissions.
 */
export const EditAllPermissions: Story = {
  render: () => <Edit apiKey={sampleApiKeyAll} onSubmit={() => {}} onCancel={() => {}} />,
};

/**
 * Edit form for an API key with restricted permissions.
 */
export const EditRestrictedPermissions: Story = {
  render: () => <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />,
};

/**
 * Edit form for an API key with read-only permissions.
 */
export const EditReadOnlyPermissions: Story = {
  render: () => <Edit apiKey={sampleApiKeyReadOnly} onSubmit={() => {}} onCancel={() => {}} />,
};

/**
 * Edit form for an API key without a name.
 */
export const EditNoName: Story = {
  render: () => <Edit apiKey={sampleApiKeyNoName} onSubmit={() => {}} onCancel={() => {}} />,
};

/**
 * Edit form for a revoked API key.
 */
export const EditRevokedKey: Story = {
  render: () => (
    <Edit
      apiKey={sampleApiKeyRevoked}
      onSubmit={() => {}}
      onCancel={() => {}}
      allowRevoke={false}
    />
  ),
};

/**
 * Edit form for an expired API key with ability to extend.
 */
export const EditExpiredKey: Story = {
  render: () => (
    <Edit
      apiKey={sampleApiKeyExpired}
      onSubmit={() => {}}
      onCancel={() => {}}
      allowExtendExpiration={true}
    />
  ),
};

/**
 * Edit form with advanced options and usage log.
 */
export const EditWithAdvancedOptions: Story = {
  render: () => (
    <Edit
      apiKey={sampleApiKeyRestricted}
      onSubmit={() => {}}
      onCancel={() => {}}
      showAdvancedOptions={true}
      showTags={true}
      showIpRestrictions={true}
      showDomainRestrictions={true}
      showRateLimit={true}
      showUsageLog={true}
    />
  ),
};

/**
 * Create form with internationalization.
 */
export const CreateWithI18n: Story = {
  render: () => (
    <I18nextProvider i18n={i18n.client.i18n}>
      <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
    </I18nextProvider>
  ),
};

/**
 * Edit form with internationalization.
 */
export const EditWithI18n: Story = {
  render: () => (
    <I18nextProvider i18n={i18n.client.i18n}>
      <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />
    </I18nextProvider>
  ),
};

/**
 * Create form with custom validation.
 */
export const CreateWithCustomValidation: Story = {
  render: () => (
    <Create
      onSubmit={() => {}}
      onCancel={() => {}}
      availableProjects={sampleProjects}
      validateForm={(data) => {
        const errors: Record<string, string> = {};

        if (data.name && data.name.length < 3) {
          errors.name = 'API key name must be at least 3 characters long';
        }

        if (
          data.permissionType === 'Restricted' &&
          data.resourcePermissions?.models === 'None' &&
          data.resourcePermissions?.files === 'Write'
        ) {
          errors['resourcePermissions.files'] = 'File write access requires model read access';
        }

        return Object.keys(errors).length > 0 ? errors : null;
      }}
    />
  ),
};

/**
 * Side-by-side comparison of different API key variants.
 */
export const SideBySideComparison: Story = {
  render: () => (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h3 className="mb-3 text-lg font-semibold">All Permissions</h3>
        <Edit apiKey={sampleApiKeyAll} onSubmit={() => {}} onCancel={() => {}} variant="compact" />
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h3 className="mb-3 text-lg font-semibold">Restricted Permissions</h3>
        <Edit
          apiKey={sampleApiKeyRestricted}
          onSubmit={() => {}}
          onCancel={() => {}}
          variant="compact"
        />
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h3 className="mb-3 text-lg font-semibold">Read-Only Permissions</h3>
        <Edit
          apiKey={sampleApiKeyReadOnly}
          onSubmit={() => {}}
          onCancel={() => {}}
          variant="compact"
        />
      </div>

      <div className="rounded-lg bg-white p-4 shadow-sm">
        <h3 className="mb-3 text-lg font-semibold">Revoked API Key</h3>
        <Edit
          apiKey={sampleApiKeyRevoked}
          onSubmit={() => {}}
          onCancel={() => {}}
          variant="compact"
        />
      </div>
    </div>
  ),
};
