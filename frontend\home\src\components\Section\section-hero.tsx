import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

export type SectionHeroVariant = 
  | 'default'
  | 'banner'
  | 'centered'
  | 'card'
  | 'split'   
  | 'features' 
  | 'custom';

export type ButtonProps = {
  text: string;
  href: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  icon?: ReactNode;
  onClick?: () => void;
};

export type StatItemProps = {
  value: string | number;
  label: string;
  icon?: ReactNode;
};

export interface SectionHeroProps {
  /**
   * ประเภทของ layout ที่ต้องการ
   * @default 'default'
   */
  variant?: SectionHeroVariant;
  
  /**
   * หัวข้อหลัก
   */
  title: string;
  
  /**
   * หัวข้อย่อย (อยู่ด้านบนหัวข้อหลักหรือด้านล่างขึ้นอยู่กับ variant)
   */
  subtitle?: string;
  
  /**
   * คำอธิบายหรือเนื้อหา
   */
  description?: string;
  
  /**
   * Breadcrumb path (ถ้ามี)
   */
  breadcrumbs?: Array<{
    text: string;
    href: string;
  }>;
  
  /**
   * รูปภาพหลัก
   */
  image?: string | ReactNode;
  
  /**
   * รูปภาพพื้นหลัง
   */
  backgroundImage?: string;
  
  /**
   * สีพื้นหลัง (ถ้าไม่มีรูปภาพ)
   */
  backgroundColor?: string;
  
  /**
   * ความสูงของ section
   * @default 'md'
   */
  height?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'auto';
  
  /**
   * ปุ่ม CTA
   */
  buttons?: ButtonProps[];
  
  /**
   * ข้อมูลสถิติ (สำหรับบาง variants เช่น 'card', 'features')
   */
  stats?: StatItemProps[];
  
  /**
   * ตำแหน่งของเนื้อหา
   * @default 'left'
   */
  contentAlignment?: 'left' | 'center' | 'right';
  
  /**
   * Custom class name
   */
  className?: string;
  
  /**
   * Logo หรือไอคอนที่แสดงใน variant 'card'
   */
  logo?: ReactNode;
  
  /**
   * กำหนดว่าต้องการแสดงเป็น container หรือ full width
   * @default true
   */
  container?: boolean;
  
  /**
   * Children components (เช่น custom content)
   */
  children?: ReactNode;
}

/**
 * Hero Section รองรับการแสดงผลหลายรูปแบบสำหรับหน้าต่างๆ
 * 
 * สามารถปรับแต่งได้หลากหลายรูปแบบตาม variant ที่เลือก
 */
const SectionHero: React.FC<SectionHeroProps> = ({
  variant = 'default',
  title,
  subtitle,
  description,
  breadcrumbs,
  image,
  backgroundImage,
  backgroundColor,
  height = 'md',
  buttons = [],
  stats = [],
  contentAlignment = 'left',
  className = '',
  logo,
  container = true,
  children,
}) => {
  // Height mapping
  const heightClasses = {
    xs: 'min-h-[150px]',
    sm: 'min-h-[250px]',
    md: 'min-h-[350px]',
    lg: 'min-h-[450px]',
    xl: 'min-h-[550px]',
    auto: 'min-h-0',
  };
  
  // Content alignment mapping
  const alignClasses = {
    left: 'text-left',
    center: 'text-center mx-auto',
    right: 'text-right ml-auto',
  };
  
  // Width for content based on alignment
  const contentWidthClasses = {
    left: contentAlignment === 'left' ? 'md:w-2/3' : '',
    center: contentAlignment === 'center' ? 'md:w-3/4 md:mx-auto' : '',
    right: contentAlignment === 'right' ? 'md:w-2/3 md:ml-auto' : '',
  };
  
  // Generate variant-specific classes and structure
  const getVariantContent = () => {
    switch (variant) {
      case 'banner':
        return (
          <div className={cn('relative w-full bg-background dark:bg-background', heightClasses.xs)}>
            {/* Background */}
            {renderBackground()}
            
            <div className={cn('w-full h-full flex items-center', container && 'container mx-auto px-4')}>
              <div className={cn('w-full flex flex-col md:flex-row md:items-center justify-between gap-4', alignClasses[contentAlignment])}>
                {/* Breadcrumbs */}
                {breadcrumbs && renderBreadcrumbs()}
                
                {/* Title */}
                <h1 className="text-xl font-bold text-foreground dark:text-foreground">
                  {title}
                </h1>
                
                {/* Right side content or image */}
                {(image || buttons.length > 0) && (
                  <div className="flex items-center gap-4">
                    {buttons.length > 0 && renderButtons()}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
        
      case 'centered':
        return (
          <div className={cn('relative w-full bg-background dark:bg-background', heightClasses[height])}>
            {/* Background */}
            {renderBackground()}
            
            <div className={cn('w-full h-full flex items-center', container && 'container mx-auto px-4')}>
              <div className={cn('w-full py-12 md:py-16', contentWidthClasses['center'])}>
                {/* Breadcrumbs */}
                {breadcrumbs && renderBreadcrumbs()}
                
                {/* Content */}
                <div className="text-center">
                  {subtitle && (
                    <div className="mb-2 text-sm md:text-base font-medium text-foreground/80 dark:text-foreground/80">
                      {subtitle}
                    </div>
                  )}
                  
                  <h1 className="text-3xl md:text-4xl font-bold mb-4 text-foreground dark:text-foreground">
                    {title}
                  </h1>
                  
                  {description && (
                    <div className="mb-6 max-w-2xl mx-auto text-foreground/90 dark:text-foreground/90">
                      {description}
                    </div>
                  )}
                  
                  {image && (
                    <div className="mt-8">
                      {typeof image === 'string' ? (
                        <img
                          src={image}
                          alt={title}
                          className="max-w-full mx-auto"
                        />
                      ) : (
                        image
                      )}
                    </div>
                  )}
                  
                  {buttons.length > 0 && (
                    <div className="mt-6 flex flex-wrap justify-center gap-4">
                      {renderButtons()}
                    </div>
                  )}
                  
                  {children}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'card':
        return (
          <div className="relative w-full py-8 md:py-12 bg-background dark:bg-background">
            {/* Background */}
            {renderBackground()}
            
            <div className={cn('w-full', container && 'container mx-auto px-4')}>
              <div className="bg-card dark:bg-card text-card-foreground dark:text-card-foreground rounded-lg shadow-lg p-6 md:p-8">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-6">
                  {/* Left side with logo and content */}
                  <div className="md:w-1/2">
                    <div className="flex items-center gap-4 mb-4">
                      {logo && (
                        <div className="flex-shrink-0">
                          {logo}
                        </div>
                      )}
                      
                      <div>
                        {subtitle && (
                          <div className="text-sm font-medium text-foreground/80 dark:text-foreground/80 mb-1">
                            {subtitle}
                          </div>
                        )}
                        
                        <h1 className="text-xl md:text-2xl font-bold text-foreground dark:text-foreground">
                          {title}
                        </h1>
                      </div>
                    </div>
                    
                    {description && (
                      <div className="mb-6 text-foreground/80 dark:text-foreground/80">
                        {description}
                      </div>
                    )}
                    
                    {buttons.length > 0 && (
                      <div className="flex flex-wrap gap-3">
                        {renderButtons()}
                      </div>
                    )}
                  </div>
                  
                  {/* Right side with stats */}
                  {stats.length > 0 && (
                    <div className="md:w-1/2 grid grid-cols-1 md:grid-cols-3 gap-4">
                      {stats.map((stat, index) => (
                        <div key={index} className="text-center p-3">
                          {stat.icon && (
                            <div className="text-primary dark:text-primary mb-2 flex justify-center">
                              {stat.icon}
                            </div>
                          )}
                          <div className="text-2xl md:text-3xl font-bold text-foreground dark:text-foreground">
                            {stat.value}
                          </div>
                          <div className="text-sm text-foreground/70 dark:text-foreground/70">
                            {stat.label}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'features':
        return (
          <div className={cn('relative w-full bg-background dark:bg-background', heightClasses[height])}>
            {/* Background */}
            {renderBackground()}
            
            <div className={cn('w-full h-full flex items-center py-8 md:py-12', container && 'container mx-auto px-4')}>
              <div className="w-full">
                {/* Breadcrumbs */}
                {breadcrumbs && renderBreadcrumbs()}
                
                {/* Content */}
                <div className={cn(alignClasses[contentAlignment])}>
                  {subtitle && (
                    <div className="mb-2 text-sm md:text-base font-medium text-foreground/80 dark:text-foreground/80">
                      {subtitle}
                    </div>
                  )}
                  
                  <h1 className="text-2xl md:text-3xl font-bold mb-4 text-foreground dark:text-foreground">
                    {title}
                  </h1>
                  
                  {description && (
                    <div className="mb-8 max-w-2xl mx-auto text-foreground/90 dark:text-foreground/90">
                      {description}
                    </div>
                  )}
                  
                  {/* Stats/features */}
                  {stats.length > 0 && (
                    <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                      {stats.map((stat, index) => (
                        <div key={index} className="bg-card/10 dark:bg-card/10 rounded-lg p-4 text-center">
                          {stat.icon && (
                            <div className="text-primary dark:text-primary mb-2 flex justify-center">
                              {stat.icon}
                            </div>
                          )}
                          <div className="text-2xl md:text-3xl font-bold text-foreground dark:text-foreground">
                            {stat.value}
                          </div>
                          <div className="text-sm text-foreground/70 dark:text-foreground/70">
                            {stat.label}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {buttons.length > 0 && (
                    <div className="mt-6 flex flex-wrap justify-center gap-4">
                      {renderButtons()}
                    </div>
                  )}
                  
                  {children}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'split':
        return (
          <div className={cn('relative w-full bg-background dark:bg-background', heightClasses[height])}>
            {/* Background */}
            {renderBackground()}
            
            <div className={cn('w-full h-full flex items-center', container && 'container mx-auto px-4')}>
              <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                {/* Content */}
                <div className={alignClasses[contentAlignment]}>
                  {/* Breadcrumbs */}
                  {breadcrumbs && renderBreadcrumbs()}
                  
                  {subtitle && (
                    <div className="mb-2 text-sm md:text-base font-medium text-foreground/80 dark:text-foreground/80">
                      {subtitle}
                    </div>
                  )}
                  
                  <h1 className="text-3xl md:text-4xl font-bold mb-4 text-foreground dark:text-foreground">
                    {title}
                  </h1>
                  
                  {description && (
                    <div className="mb-6 text-foreground/90 dark:text-foreground/90">
                      {description}
                    </div>
                  )}
                  
                  {buttons.length > 0 && (
                    <div className="mt-6 flex flex-wrap gap-4">
                      {renderButtons()}
                    </div>
                  )}
                  
                  {children}
                </div>
                
                {/* Image */}
                {image && (
                  <div className={cn("flex justify-center", contentAlignment === 'left' ? "md:order-2" : "md:order-1")}>
                    {typeof image === 'string' ? (
                      <img
                        src={image}
                        alt={title}
                        className="max-w-full rounded-lg"
                      />
                    ) : (
                      image
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
        
      case 'custom':
        return (
          <div className={cn('relative w-full bg-background dark:bg-background', heightClasses[height])}>
            {/* Background */}
            {renderBackground()}
            
            <div className={cn('w-full h-full flex items-center', container && 'container mx-auto px-4')}>
              {children || (
                <div className="w-full py-12">
                  {subtitle && (
                    <div className="mb-2 text-sm md:text-base font-medium text-foreground/80 dark:text-foreground/80">
                      {subtitle}
                    </div>
                  )}
                  
                  <h1 className="text-3xl md:text-4xl font-bold mb-4 text-foreground dark:text-foreground">
                    {title}
                  </h1>
                  
                  {description && (
                    <div className="mb-6 text-foreground/90 dark:text-foreground/90">
                      {description}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        );
        
      default: // 'default'
        return (
          <div className={cn('relative w-full bg-background dark:bg-background', heightClasses[height])}>
            {/* Background */}
            {renderBackground()}
            
            <div className={cn('w-full h-full flex items-center py-8 md:py-12', container && 'container mx-auto px-4')}>
              <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                {/* Content */}
                <div className={alignClasses[contentAlignment]}>
                  {/* Breadcrumbs */}
                  {breadcrumbs && renderBreadcrumbs()}
                  
                  {subtitle && (
                    <div className="mb-2 text-sm md:text-base font-medium text-foreground/80 dark:text-foreground/80">
                      {subtitle}
                    </div>
                  )}
                  
                  <h1 className="text-3xl md:text-4xl font-bold mb-4 text-foreground dark:text-foreground">
                    {title}
                  </h1>
                  
                  {description && (
                    <div className="mb-6 text-foreground/90 dark:text-foreground/90">
                      {description}
                    </div>
                  )}
                  
                  {buttons.length > 0 && (
                    <div className="mt-6 flex flex-wrap gap-4">
                      {renderButtons()}
                    </div>
                  )}
                  
                  {children}
                </div>
                
                {/* Image */}
                {image && (
                  <div className="flex justify-center">
                    {typeof image === 'string' ? (
                      <img
                        src={image}
                        alt={title}
                        className="max-w-full rounded-lg"
                      />
                    ) : (
                      image
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
    }
  };
  
  // Render background
  const renderBackground = () => {
    return (
      <>
        {/* Background Image */}
        {backgroundImage && (
          <div 
            className="absolute inset-0 bg-no-repeat bg-cover bg-center z-0" 
            style={{ backgroundImage: `url(${backgroundImage})` }}
          />
        )}
        
        {/* Background Color */}
        {backgroundColor && !backgroundImage && (
          <div 
            className="absolute inset-0 z-0" 
            style={{ backgroundColor }}
          />
        )}
        
        {/* Overlay */}
        {backgroundImage && (
          <div 
            className="absolute inset-0 bg-gray-100/50 -z-10"
          />
        )}
      </>
    );
  };
  
  // Render breadcrumbs
  const renderBreadcrumbs = () => {
    if (!breadcrumbs || breadcrumbs.length === 0) return null;
    
    return (
      <nav className="flex mb-4" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          <li className="inline-flex items-center">
            <Link href="/" className="inline-flex items-center text-sm text-foreground/70 dark:text-foreground/70 hover:text-foreground dark:hover:text-foreground">
              <svg className="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-9-9a1 1 0 0 0-1.414 0l-9 9a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
              </svg>
              หน้าแรก
            </Link>
          </li>
          
          {breadcrumbs.map((crumb, index) => (
            <li key={index}>
              <div className="flex items-center">
                <svg className="w-3 h-3 mx-1 text-foreground/70 dark:text-foreground/70" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                  <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4"/>
                </svg>
                {index === breadcrumbs.length - 1 ? (
                  <span className="ml-1 text-sm font-medium text-foreground/70 dark:text-foreground/70">{crumb.text}</span>
                ) : (
                  <Link href={crumb.href} className="ml-1 text-sm text-foreground/70 dark:text-foreground/70 hover:text-foreground dark:hover:text-foreground">
                    {crumb.text}
                  </Link>
                )}
              </div>
            </li>
          ))}
        </ol>
      </nav>
    );
  };
  
  // Render buttons
  const renderButtons = () => {
    if (!buttons || buttons.length === 0) return null;
    
    const getButtonClass = (variant: ButtonProps['variant'] = 'primary') => {
      const baseClass = "inline-flex items-center px-4 py-2 rounded-md transition-colors";
      
      switch (variant) {
        case 'primary':
          return cn(baseClass, "bg-primary text-primary-foreground hover:bg-primary/90");
        case 'secondary':
          return cn(baseClass, "bg-secondary text-secondary-foreground hover:bg-secondary/90");
        case 'outline':
          return cn(baseClass, "bg-transparent border border-current text-primary dark:text-primary hover:bg-primary/10");
        case 'text':
          return cn(baseClass, "bg-transparent text-primary dark:text-primary hover:underline px-2");
        default:
          return cn(baseClass, "bg-primary text-primary-foreground hover:bg-primary/90");
      }
    };
    
    return buttons.map((button, index) => (
      <Link
        key={index}
        href={button.href}
        className={getButtonClass(button.variant)}
        onClick={button.onClick}
      >
        {button.icon && (
          <span className="mr-2">{button.icon}</span>
        )}
        {button.text}
      </Link>
    ));
  };
  
  // Schema.org data for SEO
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": title,
    "description": description
  };
  
  return (
    <section className={cn('section-hero', className)}>
      {getVariantContent()}
      
      {/* Schema.org structured data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(schemaData)
        }}
      />
    </section>
  );
};

export default SectionHero; 