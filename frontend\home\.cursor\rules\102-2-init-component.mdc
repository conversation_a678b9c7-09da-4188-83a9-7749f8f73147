---
description: 
globs: src/components/**,.storybook/mswHandlers/**,src/lib/**
alwaysApply: false
---
# Platform Standard: Production-Ready React Component Implementation

This document defines the **platform-mandated standards** for implementing robust, scalable, and accessible React components within the **enterprise architecture**. Adherence ensures consistency, maintainability, and performance across the platform.

## Core Platform Design Principles

Strict adherence to these principles is mandatory for all components integrated into the platform.

### 1. Visual Hierarchy & Information Architecture
- Structure information logically using platform-defined layout patterns (cards, sections).
- Employ headings, icons, and standardized color-coding for clear structure and scannability.
- Maintain consistent spacing defined by the platform design system for visual separation and rhythm.
- Utilize strategic elevation (shadows) strictly according to platform guidelines for defining depth and interaction layers.

### 2. Accessibility (WCAG 2.1 AAA Mandate)
- **Contrast:** Achieve minimum 7:1 contrast for normal text, 4.5:1 for large text and graphical elements.
- **ARIA & Semantics:** Implement comprehensive ARIA attributes for all non-native interactive elements and ensure semantically correct HTML structure.
- **Keyboard Navigation:** Ensure full keyboard operability with logical focus order and highly visible focus states (minimum 3:1 contrast against background).
- **Screen Reader Compatibility:** Guarantee seamless screen reader navigation and comprehension.
- **Motion:** Respect `prefers-reduced-motion` by providing alternative, non-animated interactions and feedback. Disable non-essential animations.

### 3. Typography & Readability
- Adhere strictly to the platform's established typographic scale and hierarchy.
- Minimum font size: 16px (CSS `rem` equivalent) with a minimum line height of 1.5.
- Maximum line length: 80 characters for optimal readability in content blocks.
- Ensure proper text alignment based on language direction (LTR/RTL).
- Text MUST be resizable up to 200% via browser zoom without loss of content or functionality.

### 4. Interaction Design & Feedback
- Interactive elements must be visually distinct using platform-standard styles for hover, focus, active, and disabled states.
- Provide clear affordances indicating interactivity.
- Implement immediate and meaningful feedback (visual, haptic where appropriate) for all user interactions.
- Utilize platform-standard tooltips for supplementary information only, not critical instructions.
- Implement confirmation dialogs (using platform components) for all destructive actions.

### 5. Status Indication
- Use platform-defined color palettes consistently for status indicators (info, success, warning, error).
- **Never rely on color alone.** Provide redundant cues (icons, patterns, text labels) for accessibility.
- Ensure all status indicators have screen-reader accessible descriptions.

### 6. Responsive & Adaptive Design
- Components MUST function flawlessly across all platform-defined breakpoints (e.g., '3xs' to '7xl').
- Develop using a mobile-first approach with progressive enhancement for larger viewports.
- Minimum touch target size: 44×44px (CSS equivalent) with adequate spacing.
- **No horizontal scrolling** must occur at viewport widths ≥320px.

## UI Component Implementation

### Mandatory Workflow

**Prerequisite:** Platform-compliant MSW handlers and high-fidelity mock data MUST exist for the component's API interactions. This simulation layer is essential *before* starting component implementation to ensure accurate testing and development.

1.  **Type Definitions (Contracts & Props):**
    *   Define comprehensive TypeScript interfaces for component props (`{ComponentName}Props`).
    *   Include or import types representing the **exact data contracts** used in API interactions (request/response payloads), ensuring alignment with the simulation layer.
    *   Define specific types for complex internal state or variants.
        `src/components/{ComponentName}/{ComponentName}-Types.ts`

2.  **Accessible Skeleton State:**
    *   Implement a high-fidelity skeleton component that accurately reflects the structure and layout of the loaded component.
    *   Ensure skeleton performance is optimal and adheres to accessibility guidelines (e.g., proper ARIA attributes if needed).
        `src/components/{ComponentName}/{ComponentName}-Skeleton.tsx`

3.  **Animation System Integration:**
    *   Define animation variants using the platform's designated animation system (e.g., Framer Motion) and predefined tokens/transitions.
    *   Strictly adhere to accessibility principles: respect `prefers-reduced-motion`, ensure animations are non-essential or provide alternatives, limit durations, optimize performance.
        `src/components/{ComponentName}/{ComponentName}-Animations.ts`

4.  **Style Variants (CVA & Platform Tokens):**
    *   Utilize `class-variance-authority (cva)` to define all style variants (visual appearance, size, density, state).
    *   Map variants strictly to platform design tokens (CSS variables) via Tailwind utility classes.
    *   Ensure comprehensive coverage for all intended visual states and component variations.
        `src/components/{ComponentName}/{ComponentName}-Variants.ts`

5.  **Core Component Implementation:**
    *   Implement the component logic using React best practices and platform-approved state management patterns.
    *   Integrate defined types, skeleton, animations, and style variants.
    *   Implement robust error handling using platform Error Boundary components or patterns.
    *   Ensure clean separation of concerns (presentation, logic, state, effects).
    *   Implement necessary accessibility attributes (ARIA) and keyboard navigation logic.
        `src/components/{ComponentName}/{ComponentName}.tsx`

6.  **Comprehensive Storybook Stories:**
    *   Develop exhaustive stories covering all component variants, states (loading, error, empty, populated), and interactive states (hover, focus, active, disabled).
    *   Integrate MSW handlers to demonstrate component behavior against simulated API responses (leveraging variants defined in `*.mockData.ts` via handlers in `*.handlers.ts`).
    *   Include accessibility testing via Storybook addons.
    *   Cover all defined responsive breakpoints.
        `src/components/{ComponentName}/__stories__/{ComponentName}.stories.tsx`

### Global Theme Integration (Platform Standard)

-   Components **MUST** derive all colors, spacing, fonts, etc., from global CSS variables defined in the platform theme (`src/styles/globals.css` or equivalent).
-   **Prohibited:** Implementing theme switching logic *within* individual components. Theme is controlled globally.
-   Utilize Tailwind utility classes that directly reference the platform's CSS variables (e.g., `bg-background`, `text-primary`).
-   Ensure automatic dark mode support via the globally applied `.dark` class selector (or platform equivalent).

### CSS Variables Example (Conceptual Platform Standard)

```css
/* Platform Standard: src/styles/globals.css (or theme definition) */
:root {
  /* Core Palette */
  --background: 0 0% 100%;      /* Default page background */
  --foreground: 240 10% 3.9%;   /* Default text color */
  
  /* Primary Brand */
  --primary: 142.1 76.2% 36.3%; /* Primary interactive elements */
  --primary-foreground: 355.7 100% 97.3%; /* Text on primary */
  
  /* Secondary / Supporting */
  --secondary: 240 4.8% 95.9%; /* Subtle backgrounds, borders */
  --secondary-foreground: 240 5.9% 10%;  /* Text on secondary */
  
  /* Muted / Tertiary */
  --muted: 240 4.8% 95.9%;    /* Less prominent elements */
  --muted-foreground: 240 3.8% 46.1%; /* Text on muted */
  
  /* Accent / Highlight */
  --accent: 240 4.8% 95.9%;     /* Hover states, highlights */
  --accent-foreground: 240 5.9% 10%;   /* Text on accent */
  
  /* Destructive / Error */
  --destructive: 0 84.2% 60.2%; /* Error states, dangerous actions */
  --destructive-foreground: 0 0% 98%; /* Text on destructive */
  
  /* Borders & Rings */
  --border: 240 5.9% 90%;      /* Component borders */
  --input: 240 5.9% 90%;       /* Input borders */
  --ring: 142.1 76.2% 36.3%;   /* Focus rings (often primary) */
  
  /* Layout */
  --radius: 0.5rem;             /* Default border radius */
  /* ... other platform tokens: spacing, fonts, transitions ... */
}

.dark {
  /* Dark Mode Overrides - Mapped to the same variables */
  --background: 20 14.3% 4.1%;
  --foreground: 0 0% 95%;
  --primary: 142.1 70.6% 45.3%;
  --primary-foreground: 144.9 80.4% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  /* ... comprehensive dark mode variable overrides ... */
}
```

### Common Tailwind Class Mappings (Mandatory Usage)

Map platform CSS variables to Tailwind utilities consistently:

| CSS Variable            | Tailwind Utility         | Primary Usage Scenario                     |
|-------------------------|--------------------------|--------------------------------------------|
| `--background`          | `bg-background`          | Main surface/page backgrounds              |
| `--foreground`          | `text-foreground`        | Default text color                         |
| `--primary`             | `bg-primary`, `text-primary` | Key interactive elements, brand highlights |
| `--primary-foreground`  | `text-primary-foreground`| Text placed directly on primary background |
| `--secondary`           | `bg-secondary`           | Supporting element backgrounds, subtle cards|
| `--secondary-foreground`| `text-secondary-foreground`| Text on secondary background             |
| `--muted`               | `bg-muted`               | Less emphasis backgrounds, dividers      |
| `--muted-foreground`    | `text-muted-foreground`  | Placeholder text, less important info      |
| `--accent`              | `bg-accent`              | Hover states, active selections          |
| `--accent-foreground`   | `text-accent-foreground` | Text on accent background                  |
| `--destructive`         | `bg-destructive`         | Error messages, destructive action buttons |
| `--destructive-foreground`| `text-destructive-foreground`| Text on destructive background           |
| `--border`              | `border-border`          | Standard component borders                 |
| `--input`               | `border-input`           | Input field borders                        |
| `--ring`                | `ring-ring`              | Focus indicator outlines                   |
| `--radius`              | `rounded-radius`         | Applying standard border radius            |

*(This table is illustrative; the exact mapping depends on the platform's `tailwind.config.js`)*

## Platform Component Architecture Standards

### Compound Component Pattern (Preferred for Complex UI)

Utilize the compound component pattern for UI elements with multiple distinct parts that work together (e.g., Cards, Modals, Selects). Employ a factory or context-based approach for implementation.

```typescript
// Platform Standard: Compound Component Structure (Conceptual)

// Main component providing context or structure
const {ComponentName} = React.forwardRef<HTMLElement, {ComponentName}Props>((props, ref) => {
  // Implementation might use React.Children.map or Context
  // ... provide context if needed ...
  return <div ref={ref} className={/* base styles */}>{props.children}</div>;
});
{ComponentName}.displayName = '{ComponentName}';

// Define Subcomponents (using forwardRef, potentially a factory)
const {ComponentName}Header = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>((props, ref) => {
  return <div ref={ref} className={/* header styles */}>{props.children}</div>;
});
{ComponentName}Header.displayName = '{ComponentName}.Header';

const {ComponentName}Title = React.forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>((props, ref) => {
  // Use appropriate heading level based on context (h2-h6)
  return <h3 ref={ref} className={/* title styles */}>{props.children}</h3>;
});
{ComponentName}Title.displayName = '{ComponentName}.Title';

// ... other subcomponents (Content, Footer, Action)

// Attach subcomponents as static properties
{ComponentName}.Header = {ComponentName}Header;
{ComponentName}.Title = {ComponentName}Title;
// ...

// Usage:
// <{ComponentName}>
//   <{ComponentName}.Header>
//     <{ComponentName}.Title>Title</{ComponentName}.Title>
//   </{ComponentName}.Header>
//   {/* ... */}
// </{ComponentName}>
```

### General Component Requirements
- **Forward Refs:** All components **MUST** use `React.forwardRef` to allow parent components access to the underlying DOM node.
- **displayName:** All components (including subcomponents) **MUST** have an explicit `displayName` set for improved debugging.
- **HTML Semantics:** Use the most semantically appropriate HTML elements (e.g., `<button>`, `<nav>`, `<article>`). Avoid `div`-soup.
- **Separation of Concerns:** Maintain clear separation between presentation (JSX, CSS), logic (hooks, state), and data fetching/effects.

### Export Pattern & Module Organization

To prevent circular dependencies and maintain a clean import structure, adhere to these standards:

- **Main Component Export:** Export the primary component from an `index.ts` file to provide a clean public API.
- **Separate Type Exports:** Export types and interfaces from the component's type definition file.
- **Avoid Barrel Exports:** Do not create deep barrel exports that re-export multiple internal components.

```typescript
/**
 * {ComponentName}/index.ts
 * 
 * Only export the main component from here to prevent circular dependencies.
 * Other components should be imported directly from their respective files.
 */

// Main component
export { {ComponentName} } from './{ComponentName}';

// Types
export type {
  {ComponentName}Props,
  // Other public-facing types
} from './{ComponentName}-Types'; 
```

- **Direct Imports:** For internal components, always import directly from their source file, not through intermediary barrel files.
- **Component Organization:** Keep component files organized by function and maintain a shallow dependency tree.

## TanStack Table (Platform Standard for Data Tables)

When implementing data tables, **mandate** the use of `@tanstack/react-table` and `@tanstack/react-virtual` (via `useVirtualizer`) adhering to these requirements:

-   **Strong Typing:** Utilize `createColumnHelper` for fully typed column definitions, cells, and row data.
-   **Virtualization:** Implement row (and optionally column) virtualization using `useVirtualizer` for all tables potentially displaying large datasets (>50 items). Performance is paramount.
-   **Core Features:** Enable sorting, filtering (global and column-specific), and pagination (client-side or server-side driven via API simulation).
-   **Controlled State:** Manage table state (sorting, filtering, pagination, selection) explicitly using React state hooks (`useState`, `useReducer`) or platform state management.
-   **Accessibility:** Ensure generated table markup is WCAG compliant (correct `role` attributes, headers association, keyboard navigation).
-   **Visual Integration:** Integrate platform UI components (Badges, Avatars, Tooltips, Buttons) within table cells for rich data display and actions. Style according to platform design system.

```typescript
// Platform Standard: TanStack Table Column Definition (Conceptual)
import { createColumnHelper } from '@tanstack/react-table';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'; // Platform components
import { Badge } from '@/components/ui/badge';
// ... other platform imports

type DataSchema = { // Matches API contract type
  id: string;
  name: string;
  avatarUrl?: string;
  status: 'active' | 'inactive' | 'pending';
  // ... other fields
};

const columnHelper = createColumnHelper<DataSchema>();

export const platformTableColumns = [
  columnHelper.accessor('name', {
    header: 'Name',
    cell: info => (
      <div className="flex items-center gap-2">
        <Avatar>
          <AvatarImage src={info.row.original.avatarUrl} />
          <AvatarFallback>{info.getValue().charAt(0)}</AvatarFallback>
        </Avatar>
        <span>{info.getValue()}</span>
      </div>
    ),
    // Enable sorting/filtering based on platform requirements
    enableSorting: true,
    enableColumnFilter: true,
  }),

  columnHelper.accessor('status', {
    header: 'Status',
    cell: info => {
      const status = info.getValue();
      // Map status to platform-defined Badge variants
      const variant = status === 'active' ? 'success' : status === 'pending' ? 'warning' : 'secondary';
      return <Badge variant={variant}>{status}</Badge>;
    },
    enableSorting: true,
    filterFn: 'equals', // Example filter function
  }),

  // Platform Standard Actions Column
  columnHelper.display({
    id: 'actions',
    header: () => <span className="sr-only">Actions</span>, // Screen reader only
    cell: ({ row }) => (
      <div className="flex justify-end gap-1">
        {/* Use platform Button/Tooltip components */}
        <PlatformTooltip content="View Details">
          <PlatformButton variant="ghost" size="icon" aria-label={`View ${row.original.name}`}>
            {/* <EyeIcon /> */}
          </PlatformButton>
        </PlatformTooltip>
        {/* Other standard actions */}
      </div>
    ),
    enableSorting: false,
    enableColumnFilter: false,
  }),
];

// Platform Standard: Table Implementation Snippet (Conceptual)
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  // ... other imports like getPaginationRowModel
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import React from 'react'; // Ensure React is imported

function PlatformDataTable({ data, columns, isLoading /* ... other props */ }) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  // ... other controlled states (filtering, pagination)

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      // ... other states
    },
    onSortingChange: setSorting,
    // ... other handlers
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    // ... potentially pagination model
    // meta: { /* Pass platform utilities or handlers if needed */ }
  });

  const tableContainerRef = React.useRef<HTMLDivElement>(null);
  const { rows } = table.getRowModel();

  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 52, // Platform standard row height?
    overscan: 10,
  });

  if (isLoading) {
     // return <PlatformTableSkeleton columns={columns.length} rows={10} />;
  }

  return (
    <div ref={tableContainerRef} className="h-[600px] overflow-auto border rounded-md"> {/* Example container */}
      <table className="min-w-full divide-y divide-border">
        <thead className="bg-muted/50">
          {table.getHeaderGroups().map(headerGroup => (
            // ... render header rows/cells with sorting/filtering UI
          ))}
        </thead>
        <tbody className="divide-y divide-border bg-background" style={{ height: `${rowVirtualizer.getTotalSize()}px`, position: 'relative' }}>
          {rowVirtualizer.getVirtualItems().map(virtualRow => {
            const row = rows[virtualRow.index];
            return (
              <tr
                key={row.id}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: `${virtualRow.size}px`,
                  transform: `translateY(${virtualRow.start}px)`,
                }}
                className="hover:bg-muted/50"
              >
                {row.getVisibleCells().map(cell => (
                  // ... render cells using flexRender
                ))}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
```

## Animation System (Platform Standard: Framer Motion)

All animations **MUST** use the platform's designated system (e.g., Framer Motion) and adhere strictly to the following:

-   **Accessibility First:** MUST respect `prefers-reduced-motion`. Provide non-animated alternatives or disable animations entirely. Check via `useReducedMotion` hook.
-   **Meaningful & Performant:** Animations should enhance user understanding (state transitions, hierarchy) or provide feedback, **not** distract. Optimize rigorously to maintain 60fps; avoid layout thrashing. Use hardware acceleration (`transform`, `opacity`).
-   **Controllable & Brief:** Avoid animations longer than 500ms unless they are user-controlled (e.g., scrubbing a timeline). Ensure animations are pausable/stoppable where appropriate.
-   **Platform Variants:** Utilize predefined animation variants and transitions from the platform's animation system (`{ComponentName}-Animations.ts` or platform library) for consistency.

```typescript
// Platform Standard: Animation Variants (Conceptual)
// Defined in `{ComponentName}-Animations.ts` or platform library

// Shared transition tokens
export const platformTransitions = {
  quick: { duration: 0.15, ease: 'easeOut' },
  normal: { duration: 0.25, ease: 'easeInOut' },
  springy: { type: 'spring', stiffness: 400, damping: 35 },
};

// Base states
const baseStates = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
};

const slideUp = {
  hidden: { ...baseStates.hidden, y: 10 },
  visible: { ...baseStates.visible, y: 0, transition: platformTransitions.normal },
};

// Reduced motion alternative (fade only)
const reducedMotionSlideUp = {
  hidden: baseStates.hidden,
  visible: { ...baseStates.visible, transition: platformTransitions.normal },
};

// Container variant for staggered children
export const staggerContainer = (delay = 0.05) => ({
  hidden: {},
  visible: {
    transition: {
      staggerChildren: delay,
    }
  },
});

// Item variant used with staggerContainer
export const staggerItem = {
  hidden: { opacity: 0, y: 5 },
  visible: { opacity: 1, y: 0, transition: platformTransitions.quick },
};

// Platform Hook: usePlatformAnimations
import { useReducedMotion } from 'framer-motion';

export function usePlatformAnimations(variants: Record<string, any>) {
  const prefersReducedMotion = useReducedMotion();

  if (prefersReducedMotion) {
    // Return mapped variants with motion disabled or reduced
    // e.g., map slideUp to reducedMotionSlideUp
    const reduced = { ...variants }; // Deep clone/map needed
    if(reduced.slideUp) reduced.slideUp = reducedMotionSlideUp;
    // ... map other variants
    return reduced;
  }
  return variants;
}

// Usage in Component:
// import * as animationDefs from './{ComponentName}-Animations';
// const variants = usePlatformAnimations(animationDefs);
// <motion.div variants={variants.slideUp} initial="hidden" animate="visible" />
```

## Styling (Platform Standard: CVA & Tailwind)

Component styling **MUST** utilize `class-variance-authority (cva)` integrated with Tailwind CSS, leveraging platform design tokens (CSS Variables).

-   **Comprehensive Variants:** Define variants covering all intended visual states (size, color scheme, density, interaction states like disabled/active).
-   **Platform Tokens:** Map all styles to platform CSS variables via corresponding Tailwind utilities. **No hardcoded colors, spacing, or font sizes.**
-   **Defaults:** Provide sensible default variants.
-   **Composition:** Use the `cn` utility (or platform equivalent) for safely merging base classes, variant classes, and incoming `className` props.

```typescript
// Platform Standard: CVA Definition (Conceptual)
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils'; // Platform utility

// Define variants mapping to Tailwind utilities (which use CSS variables)
const platformButtonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-radius font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50', // Base styles
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2', // Use platform size tokens if available
        sm: 'h-9 rounded-radius px-3',
        lg: 'h-11 rounded-radius px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

// Apply to Component
export interface PlatformButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof platformButtonVariants> {}

const PlatformButton = React.forwardRef<HTMLButtonElement, PlatformButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={cn(platformButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
PlatformButton.displayName = 'PlatformButton';
```

## Component States (Mandatory Implementation)

Every component interacting with data or performing actions **MUST** visually represent and handle these core states:

1.  **Loading State:** Use the corresponding `<ComponentName>Skeleton` component. Ensure smooth transitions into/out of the loading state.
2.  **Empty State:** Display a clear, helpful message using a platform-standard empty state pattern (e.g., centered icon, title, description, optional action button). Provide context-specific messages via props.
3.  **Error State:** Clearly visualize errors using platform-standard components (e.g., `Alert` with `variant="destructive"`). Display meaningful error messages. Provide recovery actions if possible (e.g., retry button). Consider using Error Boundaries at appropriate levels.
4.  **Success State:** This is the default rendering when data is present and there are no errors. Ensure data is displayed clearly according to design principles. Provide subtle success feedback for actions where appropriate (e.g., toast notifications via platform service).

```typescript
// Platform Standard: State Handling within a Component (Conceptual)
import { PlatformSkeleton } from './Platform-Skeleton';
import { PlatformEmptyState } from '@/components/platform/empty-state'; // Example path
import { PlatformAlert, PlatformAlertTitle, PlatformAlertDescription } from '@/components/ui/alert'; // Example path
// ... platform icons

function ComponentWithStates({ 
    queryResult, // Assume a query hook result object { data, isLoading, error }
    emptyTitle = "No Data Available",
    emptyDescription = "There is currently no data to display.",
    // ... other props
}) {
  const { data, isLoading, error } = queryResult;

  if (isLoading) {
    // Use the specific, accurately structured skeleton
    return <PlatformSkeleton /* Props for size/variant */ />;
  }
  
  if (error) {
    // Use platform alert component for consistent error display
    return (
      <PlatformAlert variant="destructive">
        {/* <PlatformAlertIcon type="error" /> */}
        <PlatformAlertTitle>Error Loading Data</PlatformAlertTitle>
        <PlatformAlertDescription>
          {error?.message || 'An unexpected error occurred.'}
          {/* Optionally add a retry button */}
        </PlatformAlertDescription>
      </PlatformAlert>
    );
  }
  
  // Check for empty data *after* loading and error checks
  // Adjust condition based on data structure (array, object, etc.)
  const isEmpty = !data || (Array.isArray(data) && data.length === 0); 
  if (isEmpty) {
    // Use platform empty state component
    return (
      <PlatformEmptyState
        // icon={<PlatformIcon name="empty-box" />}
        title={emptyTitle}
        description={emptyDescription}
        // actions={<PlatformButton>Create New Item</PlatformButton>} 
      />
    );
  }
  
  // Success State: Render the actual component UI with the data
  return (
    <div className="component-wrapper">
      {/* ... component implementation using 'data' ... */}
    </div>
  );
}
```

## Component State Management (Platform Guidelines)

Manage local component state using standard React hooks (`useState`, `useReducer`), prioritizing clarity, performance, and adherence to platform patterns. For cross-component state, utilize the platform's designated state management library (e.g., Zustand, Redux Toolkit, Context API with clear guidelines).

### State Hook Selection (Platform Recommendation)

| Hook          | Primary Use Case                                     | Avoid When                                            |
|---------------|------------------------------------------------------|-------------------------------------------------------|
| `useState`    | Simple, independent values (booleans, strings, numbers)| Interdependent state; complex update logic           |
| `useReducer`  | Complex state objects; predictable state transitions | Very simple state (overkill)                          |
| `useMemo`     | Memoizing expensive derivations based on props/state | Simple calculations; memoizing stable values/objects |
| `useCallback` | Memoizing functions passed to `React.memo`-ized children | Functions defined and used only within the component |
| `useContext`  | Accessing **stable**, shared platform state (theme, auth)| Frequently changing state; state used locally        |
| `useRef`      | Accessing DOM nodes; storing mutable values *without* re-rendering | Storing component state that triggers renders |

*Refer to platform documentation for specific guidance on using the chosen global state management library.*

### Implementation Best Practices (Mandatory)

1.  **TypeScript:** Strictly type all state, actions, and reducers.
2.  **Co-location:** Keep state as close as possible to where it's used.
3.  **Lift State Prudently:** Only lift state when necessary for sharing between siblings. Avoid unnecessary prop drilling.
4.  **Reducer Purity:** Ensure reducers are pure functions with no side effects.
5.  **Memoization:** Apply `useMemo` and `useCallback` judiciously to optimize performance, especially when passing props to memoized children.
6.  **Effect Cleanup:** Always provide cleanup functions for `useEffect` subscriptions or timers.
7.  **Immutability:** Treat state as immutable. Use functional updates or immutable update patterns (e.g., `immer` if adopted by platform) when modifying state based on previous state.
8.  **Date/Time Handling:** **Mandate** the use of the `luxon` library for all date and time operations. Ensure consistent timezone handling and formatting according to platform requirements. Avoid using native `Date` objects directly for complex manipulations or formatting.
9.  **Readability:** Use descriptive names for state variables and update functions/actions.

*(Detailed examples for useState, useReducer, useMemo, useCallback, useContext from previous version can be referenced but should align with platform specifics)*

## Reuse Strategy (Platform Mandate)

Maximize reuse and maintain consistency:

1.  **Platform UI Library First:** **ALWAYS** check the platform's core UI library (`@/components/ui/index.ts` or equivalent) for existing components (`Button`, `Card`, `Input`, `Dialog`, etc.) before considering custom implementation.
2.  **Justify Custom Components:** Only create new, bespoke components if:
    *   The required functionality involves significant, unique business logic not separable from presentation.
    *   No existing platform component meets the specific behavioral or structural requirements after exploring configuration options.
    *   Visual requirements fundamentally differ from platform standards *and* this deviation is explicitly approved.
3.  **TanStack Table Exception:** Data tables **MUST** always be implemented directly using TanStack Table as specified in the guidelines above, composed with platform UI elements. Do not wrap TanStack Table in a custom generic `DataTable` component unless it provides significant, platform-approved abstraction.

## Core Principles Summary (Platform Commitment)

1.  **Production Parity**: Simulate production accurately with high-fidelity API simulation layer.
2.  **Optimized Performance**: Prioritize performance; mandate virtualization for lists/tables.
3.  **Universal Design**: Mobile-first, AAA accessibility, global theming are non-negotiable.
4.  **Platform Integration**: Seamlessly utilize TanStack, `cva`, Framer Motion (or platform equivalents).
5.  **Developer Rigor**: Enforce consistent patterns, exhaustive testing (Storybook), strict typing.
