// radio-group.fixtures.tsx
import * as React from "react";
import { RadioGroup, RadioGroupItem } from "../";

export const BasicRadioGroup = () => (
  <RadioGroup title="Favorite fruit">
    <RadioGroupItem value="apple" id="r-apple" label="Apple" />
    <RadioGroupItem value="banana" id="r-banana" label="Banana" />
    <RadioGroupItem value="orange" id="r-orange" label="Orange" />
  </RadioGroup>
);

export const RadioGroupWithDescriptions = () => (
  <RadioGroup title="Notification preferences">
    <RadioGroupItem
      value="all"
      id="r-all"
      label="All notifications"
      description="Receive notifications for all activity"
    />
    <RadioGroupItem
      value="important"
      id="r-important"
      label="Important only"
      description="Only receive notifications for important activity"
    />
    <RadioGroupItem
      value="none"
      id="r-none"
      label="None"
      description="Don't receive any notifications"
    />
  </RadioGroup>
);

export const CardVariant = () => (
  <RadioGroup title="Subscription plan" variant="card">
    <RadioGroupItem 
      value="basic" 
      id="r-basic" 
      label="Basic" 
      description="$9/month" 
    />
    <RadioGroupItem 
      value="pro" 
      id="r-pro" 
      label="Pro" 
      description="$19/month" 
    />
    <RadioGroupItem 
      value="enterprise" 
      id="r-enterprise" 
      label="Enterprise" 
      description="$49/month" 
    />
  </RadioGroup>
);

export const OutlineVariant = () => (
  <RadioGroup title="Visibility" variant="outline">
    <RadioGroupItem value="public" id="r-public" label="Public" />
    <RadioGroupItem value="private" id="r-private" label="Private" />
    <RadioGroupItem value="draft" id="r-draft" label="Draft" />
  </RadioGroup>
);

export const HorizontalRadioGroup = () => (
  <RadioGroup title="Gender" orientation="horizontal">
    <RadioGroupItem value="male" id="r-male" label="Male" />
    <RadioGroupItem value="female" id="r-female" label="Female" />
    <RadioGroupItem value="other" id="r-other" label="Other" />
  </RadioGroup>
);

export const DisabledRadioGroup = () => (
  <RadioGroup title="Availability" disabled>
    <RadioGroupItem value="morning" id="r-morning" label="Morning" />
    <RadioGroupItem value="afternoon" id="r-afternoon" label="Afternoon" />
    <RadioGroupItem value="evening" id="r-evening" label="Evening" />
  </RadioGroup>
);

export const RadioGroupWithState = () => {
  const [value, setValue] = React.useState("light");
  
  return (
    <div className="space-y-4">
      <RadioGroup title="Theme preference" value={value} onValueChange={setValue}>
        <RadioGroupItem value="light" id="r-light" label="Light" />
        <RadioGroupItem value="dark" id="r-dark" label="Dark" />
        <RadioGroupItem value="system" id="r-system" label="System" />
      </RadioGroup>
      <div className="text-sm">
        Selected value: <span className="font-medium">{value}</span>
      </div>
    </div>
  );
};

export const RadioItemVariants = () => (
  <RadioGroup title="Item variants" orientation="horizontal">
    <RadioGroupItem 
      value="default" 
      id="r-item-default" 
      label="Default" 
    />
    <RadioGroupItem 
      value="colorful" 
      id="r-item-colorful" 
      label="Colorful" 
      variant="colorful" 
    />
    <RadioGroupItem 
      value="branded" 
      id="r-item-branded" 
      label="Branded" 
      variant="branded" 
    />
  </RadioGroup>
);

export const PowerWattageButtonCard = () => (
  <RadioGroup title="กำลังไฟ" variant="buttonCard" orientation="horizontal" defaultValue="18" className="flex gap-2">
    <RadioGroupItem 
      value="8" 
      id="r-watt-8" 
      label="8 วัตต์" 
      variant="buttonCard" 
    />
    <RadioGroupItem 
      value="18" 
      id="r-watt-18" 
      label="18 วัตต์" 
      variant="buttonCard" 
    />
    <RadioGroupItem 
      value="24" 
      id="r-watt-24" 
      label="24 วัตต์" 
      variant="buttonCard" 
    />
  </RadioGroup>
);

export const BulbShapeButtonCard = () => (
  <RadioGroup title="รูปทรงหลอด" variant="buttonCard" orientation="horizontal" defaultValue="long" className="flex gap-2">
    <RadioGroupItem 
      value="short" 
      id="r-bulb-short" 
      label="หลอดสั้น" 
      variant="buttonCard" 
    />
    <RadioGroupItem 
      value="long" 
      id="r-bulb-long" 
      label="หลอดยาว" 
      variant="buttonCard" 
    />
  </RadioGroup>
);

export const RadioItemSizes = () => (
  <RadioGroup title="Item sizes" orientation="horizontal">
    <RadioGroupItem 
      value="small" 
      id="r-size-small" 
      label="Small" 
      size="sm" 
    />
    <RadioGroupItem 
      value="default" 
      id="r-size-default" 
      label="Default" 
    />
    <RadioGroupItem 
      value="large" 
      id="r-size-large" 
      label="Large" 
      size="lg" 
    />
  </RadioGroup>
);