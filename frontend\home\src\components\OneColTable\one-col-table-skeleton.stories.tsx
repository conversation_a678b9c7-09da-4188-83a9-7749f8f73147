import type { Meta, StoryObj } from '@storybook/react';
import { expect, within } from '@storybook/test';

import { OneColTableSkeleton } from './one-col-table-skeleton';

const meta = {
  title: 'Components/OneColTable/OneColTableSkeleton',
  component: OneColTableSkeleton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A skeleton loader component for OneColTable that displays placeholder content while data is loading. Automatically adapts to the size prop and displays skeleton rows.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      description: 'Size variant matching the main component',
      control: 'select',
      options: ['sm', 'md', 'lg'],
      defaultValue: 'md',
      table: {
        type: { summary: 'string' },
      },
    },
    rowCount: {
      description: 'Number of skeleton rows to render',
      control: 'number',
      defaultValue: 5,
      table: {
        type: { summary: 'number' },
      },
    },
    simplified: {
      description: 'Whether to use simplified skeleton for performance',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
    className: {
      description: 'Optional CSS class to be merged',
      control: 'text',
      table: {
        type: { summary: 'string' },
      },
    },
  },
} satisfies Meta<typeof OneColTableSkeleton>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    size: 'md',
    rowCount: 5,
    simplified: false,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const skeletonElement = await canvas.findByRole('status');
    expect(skeletonElement).toBeInTheDocument();
    expect(skeletonElement).toHaveAttribute('aria-label', 'Loading items');
  },
};

// Size variants
export const SizeVariants: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Small Size</h3>
        <OneColTableSkeleton size="sm" rowCount={3} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Medium Size (Default)</h3>
        <OneColTableSkeleton size="md" rowCount={3} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Large Size</h3>
        <OneColTableSkeleton size="lg" rowCount={3} />
      </div>
    </div>
  ),
  args: {
    rowCount: 3,
  },
};

// Row count variations
export const RowCountVariations: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Few Rows (3)</h3>
        <OneColTableSkeleton rowCount={3} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Medium Rows (5)</h3>
        <OneColTableSkeleton rowCount={5} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Many Rows (10)</h3>
        <OneColTableSkeleton rowCount={10} />
      </div>
    </div>
  ),
  args: {
    size: 'md',
  },
};

// Simplified variant (for performance)
export const SimplifiedVariant: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Detailed Skeleton (Default)</h3>
        <OneColTableSkeleton rowCount={5} simplified={false} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Simplified Skeleton</h3>
        <OneColTableSkeleton rowCount={5} simplified={true} />
      </div>
    </div>
  ),
  args: {
    size: 'md',
    rowCount: 5,
  },
};

// With custom class name
export const WithCustomClassName: Story = {
  args: {
    size: 'md',
    rowCount: 5,
    className: 'border-4 border-dashed border-primary/50 p-4',
  },
};
