import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Rating } from "../rating";
import { useState } from "react";

const meta: Meta<typeof Rating> = {
  title: "UI/Rating/Rating",
  component: Rating,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: "radio",
      options: ["sm", "md", "lg"],
    },
    direction: {
      control: "radio",
      options: ["horizontal", "vertical"],
    },
    interactive: {
      control: "boolean",
    },
    allowFraction: {
      control: "boolean",
    },
    onChange: { action: "rating changed" },
  },
};

export default meta;
type Story = StoryObj<typeof Rating>;

// Default rating display
export const Default: Story = {
  args: {
    value: 4,
    maxValue: 5,
    size: "md",
  },
};

// Large size
export const Large: Story = {
  args: {
    value: 3.5,
    maxValue: 5,
    size: "lg",
    allowFraction: true,
  },
};

// Small size
export const Small: Story = {
  args: {
    value: 5,
    maxValue: 5,
    size: "sm",
  },
};

// Custom colors
export const CustomColors: Story = {
  args: {
    value: 4,
    maxValue: 5,
    filledColor: "text-purple-500",
    emptyColor: "text-purple-100",
  },
};

// Vertical direction
export const Vertical: Story = {
  args: {
    value: 3,
    maxValue: 5,
    direction: "vertical",
  },
};

// Interactive rating with state
export const Interactive = () => {
  const [rating, setRating] = useState(3);
  
  return (
    <div className="flex flex-col items-center gap-4">
      <p>Selected rating: {rating}</p>
      <Rating 
        value={rating} 
        interactive={true} 
        onChange={setRating} 
        size="lg"
      />
    </div>
  );
};