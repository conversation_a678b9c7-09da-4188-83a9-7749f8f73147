import{a as v}from"./chunk-R2O6QX4D-CX21ziz_.js";import{S as k}from"./chunk-PYIO3TDQ-D8Zv8hXV.js";import{u as C}from"./chunk-6CNRNROJ-vBT9ZY0G.js";import{C as L}from"./chunk-3LLQ6F7F-DvOxFHHw.js";import{u as E,c as O}from"./chunk-BF3VCHXD-J5OiX7iF.js";import{a2 as T,a4 as j,eE as F,a5 as I,b as S,R as w,ar as P,j as e,H as N,s as R,a8 as V,a9 as B,t as b,w as o,x as z,D,B as y}from"./index-Bwql5Dzz.js";import{S as M}from"./chunk-CBJWO6K6-CH1Togga.js";import{K as $}from"./chunk-6HTZNHPT-N4svn6ad.js";import{b as m,u as q}from"./chunk-JGQGO74V-DtHO1ucg.js";import{R as _}from"./radio-group-FK6NWg-k.js";import"./x-mark-mini-DvSTI7zK.js";import"./triangles-mini-DPBC_td5.js";import"./plus-mini-C5sDHkH8.js";import"./prompt-BsR9zKsn.js";import"./index-DX0YxfHa.js";function H(p,n){const r={};return n.forEach(s=>{s in p&&(r[s]=p[s])}),r}var K=T({name:j().min(1),price_type:F(k),enabled_in_store:I().optional(),shipping_profile_id:j()}),A=({locationId:p,shippingOption:n,type:r})=>{const{t:s}=S(),{handleSuccess:h}=q(),u=r==="pickup",c=C({queryFn:i=>R.admin.shippingProfile.list(i),queryKey:["shipping_profiles"],getOptions:i=>i.shipping_profiles.map(d=>({label:d.name,value:d.id})),defaultValue:n.shipping_profile_id}),a=V({defaultValues:{name:n.name,price_type:n.price_type,enabled_in_store:v(n),shipping_profile_id:n.shipping_profile_id},resolver:B(K)}),{mutateAsync:t,isPending:f}=O(n.id),x=a.handleSubmit(async i=>{const d=n.rules.map(l=>({...H(l,["id","attribute","operator","value"])})),g=d.find(l=>l.attribute==="enabled_in_store");g?g.value=i.enabled_in_store?"true":"false":d.push({value:i.enabled_in_store?"true":"false",attribute:"enabled_in_store",operator:"eq"}),await t({name:i.name,price_type:i.price_type,shipping_profile_id:i.shipping_profile_id,rules:d},{onSuccess:({shipping_option:l})=>{b.success(s("stockLocations.shippingOptions.edit.successToast",{name:l.name})),h(`/settings/locations/${p}`)},onError:l=>{b.error(l.message)}})});return e.jsx(m.Form,{form:a,children:e.jsxs($,{onSubmit:x,className:"flex flex-1 flex-col",children:[e.jsx(m.Body,{children:e.jsx("div",{className:"flex flex-col gap-y-8",children:e.jsxs("div",{className:"flex flex-col gap-y-8",children:[!u&&e.jsx(o.Field,{control:a.control,name:"price_type",render:({field:i})=>e.jsxs(o.Item,{children:[e.jsx(o.Label,{children:s("stockLocations.shippingOptions.fields.priceType.label")}),e.jsx(o.Control,{children:e.jsxs(_,{...i,onValueChange:i.onChange,children:[e.jsx(_.ChoiceBox,{className:"flex-1",value:"flat",label:s("stockLocations.shippingOptions.fields.priceType.options.fixed.label"),description:s("stockLocations.shippingOptions.fields.priceType.options.fixed.hint")}),e.jsx(_.ChoiceBox,{className:"flex-1",value:"calculated",label:s("stockLocations.shippingOptions.fields.priceType.options.calculated.label"),description:s("stockLocations.shippingOptions.fields.priceType.options.calculated.hint")})]})}),e.jsx(o.ErrorMessage,{})]})}),e.jsxs("div",{className:"grid gap-y-4",children:[e.jsx(o.Field,{control:a.control,name:"name",render:({field:i})=>e.jsxs(o.Item,{children:[e.jsx(o.Label,{children:s("fields.name")}),e.jsx(o.Control,{children:e.jsx(z,{...i})}),e.jsx(o.ErrorMessage,{})]})}),e.jsx(o.Field,{control:a.control,name:"shipping_profile_id",render:({field:i})=>e.jsxs(o.Item,{children:[e.jsx(o.Label,{children:s("stockLocations.shippingOptions.fields.profile")}),e.jsx(o.Control,{children:e.jsx(L,{...i,options:c.options,searchValue:c.searchValue,onSearchValueChange:c.onSearchValueChange,disabled:c.disabled})}),e.jsx(o.ErrorMessage,{})]})})]}),e.jsx(D,{}),e.jsx(M,{control:a.control,name:"enabled_in_store",label:s("stockLocations.shippingOptions.fields.enableInStore.label"),description:s("stockLocations.shippingOptions.fields.enableInStore.hint")})]})})}),e.jsx(m.Footer,{children:e.jsxs("div",{className:"flex items-center gap-x-2",children:[e.jsx(m.Close,{asChild:!0,children:e.jsx(y,{size:"small",variant:"secondary",children:s("actions.cancel")})}),e.jsx(y,{size:"small",type:"submit",isLoading:f,children:s("actions.save")})]})})]})})},ae=()=>{const{t:p}=S(),{location_id:n,so_id:r}=w(),{shipping_options:s,isPending:h,isFetching:u,isError:c,error:a}=E({id:r,fields:"+service_zone.fulfillment_set.type"}),t=s==null?void 0:s.find(x=>x.id===r);if(!h&&!u&&!t)throw P({message:`Shipping option with ID ${r} was not found`},404);if(c)throw a;const f=(t==null?void 0:t.service_zone.fulfillment_set.type)==="pickup";return e.jsxs(m,{children:[e.jsx(m.Header,{children:e.jsx(N,{children:p(`stockLocations.${f?"pickupOptions":"shippingOptions"}.edit.header`)})}),t&&e.jsx(A,{shippingOption:t,locationId:n,type:t.service_zone.fulfillment_set.type})]})};export{ae as Component};
