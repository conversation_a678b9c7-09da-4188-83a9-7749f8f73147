'use client';

import { useState, useEffect } from 'react';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import Link from 'next/link';
import { useTranslation } from '@/components/Providers/i18n-provider';

// Define analytics data type
interface Analytics {
  pageViews: {
    today: number;
    weekly: number;
    monthly: number;
  };
  userSessions: {
    averageDuration: string;
    bounceRate: string;
    newUsers: number;
  };
  serverLoad: {
    current: string;
    average: string;
    peak: string;
  };
  generatedAt: string;
}

export default function CSRAdvancedPage() {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [showDashboard, setShowDashboard] = useState(false);
  const [dashboardLoading, setDashboardLoading] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<Analytics | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Simulate fetching analytics data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Mock data
        setAnalyticsData({
          pageViews: {
            today: Math.floor(Math.random() * 10000),
            weekly: Math.floor(Math.random() * 50000),
            monthly: Math.floor(Math.random() * 200000),
          },
          userSessions: {
            averageDuration: Math.floor(Math.random() * 500) + ' seconds',
            bounceRate: Math.floor(Math.random() * 100) + '%',
            newUsers: Math.floor(Math.random() * 5000),
          },
          serverLoad: {
            current: Math.floor(Math.random() * 100) + '%',
            average: Math.floor(Math.random() * 100) + '%',
            peak: Math.floor(Math.random() * 100) + '%',
          },
          generatedAt: new Date().toISOString(),
        });
        setIsLoading(false);
      } catch {
        setError('Failed to fetch analytics data');
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Function to simulate loading a heavy component
  const loadDashboard = () => {
    setDashboardLoading(true);
    // Simulate delay for component loading
    setTimeout(() => {
      setShowDashboard(true);
      setDashboardLoading(false);
    }, 1500);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="csr" />
      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">
          {t('rendering.csrAdvanced', 'Advanced CSR Techniques')}
        </h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">
            {t('rendering.csrAdvancedTechniques', 'Advanced Client-Side Rendering')}
          </h2>
          <p className="mb-4">
            {t(
              'rendering.csrAdvancedDescription',
              'This example demonstrates advanced CSR techniques like code splitting, lazy loading components, and optimizing client-side data fetching.',
            )}
          </p>
          <p>
            {t(
              'rendering.csrAdvancedNote',
              'These techniques help improve performance in large client-side applications by reducing the initial JavaScript bundle size and loading components only when needed.',
            )}
          </p>
        </div>

        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">
            {t('rendering.codeSplitting', 'Code Splitting & Lazy Loading')}
          </h2>
          <p className="mb-4">
            {t(
              'rendering.codeSplittingDescription',
              'The analytics dashboard below simulates the lazy loading of a complex component that would normally be split into a separate JavaScript bundle.',
            )}
          </p>

          {!showDashboard && !dashboardLoading ? (
            <div className="flex justify-center py-6">
              <button
                onClick={loadDashboard}
                className="bg-primary hover:bg-primary/90 rounded-md px-6 py-3 text-white transition-colors"
              >
                {t('rendering.loadDashboard', 'Load Analytics Dashboard')}
              </button>
            </div>
          ) : dashboardLoading ? (
            <div className="flex flex-col items-center justify-center space-y-4 py-12">
              <div className="flex items-center space-x-2">
                <div className="bg-primary/20 h-4 w-4 animate-pulse rounded-full"></div>
                <div className="bg-primary/20 h-4 w-4 animate-pulse rounded-full delay-75"></div>
                <div className="bg-primary/20 h-4 w-4 animate-pulse rounded-full delay-150"></div>
              </div>
              <p>{t('rendering.loadingDashboard', 'Loading dashboard component...')}</p>
            </div>
          ) : (
            <div className="rounded-lg border p-4">
              <h3 className="mb-4 text-lg font-medium">
                {t('rendering.analyticsDashboard', 'Analytics Dashboard')}
              </h3>
              {isLoading ? (
                <div className="animate-pulse space-y-4">
                  <div className="bg-muted h-8 w-full rounded"></div>
                  <div className="bg-muted h-24 w-full rounded"></div>
                  <div className="bg-muted h-24 w-full rounded"></div>
                </div>
              ) : error ? (
                <div className="bg-destructive/10 text-destructive rounded p-4">{error}</div>
              ) : analyticsData ? (
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                  <div className="rounded-lg border p-6">
                    <h4 className="mb-4 font-semibold">{t('rendering.pageViews', 'Page Views')}</h4>
                    <div className="space-y-4">
                      <div>
                        <div className="mb-1 flex justify-between">
                          <span className="text-sm">{t('rendering.today', 'Today')}</span>
                          <span className="text-sm font-medium">
                            {analyticsData.pageViews.today.toLocaleString()}
                          </span>
                        </div>
                        <div className="bg-muted h-2 w-full rounded-full">
                          <div
                            className="h-2 rounded-full bg-blue-500"
                            style={{ width: `${(analyticsData.pageViews.today / 10000) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="mb-1 flex justify-between">
                          <span className="text-sm">{t('rendering.weekly', 'Weekly')}</span>
                          <span className="text-sm font-medium">
                            {analyticsData.pageViews.weekly.toLocaleString()}
                          </span>
                        </div>
                        <div className="bg-muted h-2 w-full rounded-full">
                          <div
                            className="h-2 rounded-full bg-green-500"
                            style={{ width: `${(analyticsData.pageViews.weekly / 50000) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <div>
                        <div className="mb-1 flex justify-between">
                          <span className="text-sm">{t('rendering.monthly', 'Monthly')}</span>
                          <span className="text-sm font-medium">
                            {analyticsData.pageViews.monthly.toLocaleString()}
                          </span>
                        </div>
                        <div className="bg-muted h-2 w-full rounded-full">
                          <div
                            className="h-2 rounded-full bg-purple-500"
                            style={{
                              width: `${(analyticsData.pageViews.monthly / 200000) * 100}%`,
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-lg border p-6">
                    <h4 className="mb-4 font-semibold">
                      {t('rendering.userSessions', 'User Sessions')}
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <h5 className="text-sm font-medium">
                          {t('rendering.avgDuration', 'Average Duration')}
                        </h5>
                        <p className="text-2xl font-semibold">
                          {analyticsData.userSessions.averageDuration}
                        </p>
                      </div>
                      <div>
                        <h5 className="text-sm font-medium">
                          {t('rendering.bounceRate', 'Bounce Rate')}
                        </h5>
                        <p className="text-2xl font-semibold">
                          {analyticsData.userSessions.bounceRate}
                        </p>
                      </div>
                      <div>
                        <h5 className="text-sm font-medium">
                          {t('rendering.newUsers', 'New Users')}
                        </h5>
                        <p className="text-2xl font-semibold">
                          {analyticsData.userSessions.newUsers.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-lg border p-6">
                    <h4 className="mb-4 font-semibold">
                      {t('rendering.serverLoad', 'Server Load')}
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <h5 className="text-sm font-medium">{t('rendering.current', 'Current')}</h5>
                        <p className="text-2xl font-semibold">{analyticsData.serverLoad.current}</p>
                      </div>
                      <div>
                        <h5 className="text-sm font-medium">{t('rendering.average', 'Average')}</h5>
                        <p className="text-2xl font-semibold">{analyticsData.serverLoad.average}</p>
                      </div>
                      <div>
                        <h5 className="text-sm font-medium">{t('rendering.peak', 'Peak')}</h5>
                        <p className="text-2xl font-semibold">{analyticsData.serverLoad.peak}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
              <p className="mt-6 text-right text-xs">
                {t('rendering.dashboardGeneratedAt', 'Dashboard loaded at')}:{' '}
                {new Date().toLocaleString()}
              </p>
            </div>
          )}
        </div>

        <div className="bg-card mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">
            {t('rendering.csrBestPractices', 'Best Practices')}
          </h2>
          <ul className="list-disc space-y-2 pl-5">
            <li>
              {t(
                'rendering.csrBestPractice1',
                'Use loading states to indicate content is being fetched',
              )}
            </li>
            <li>
              {t(
                'rendering.csrBestPractice2',
                'Implement code splitting to reduce initial bundle size',
              )}
            </li>
            <li>
              {t('rendering.csrBestPractice3', 'Use lazy loading for non-critical components')}
            </li>
            <li>
              {t(
                'rendering.csrBestPractice4',
                'Consider implementing client-side caching for frequent data',
              )}
            </li>
            <li>
              {t(
                'rendering.csrBestPractice5',
                'Implement error boundaries to handle failures gracefully',
              )}
            </li>
          </ul>
        </div>

        <div className="mt-8 flex justify-center">
          <Link href="/rendering-examples/csr" className="text-primary font-medium hover:underline">
            ← {t('rendering.backToBasicCSR', 'Back to Basic CSR Example')}
          </Link>
        </div>
      </main>
    </div>
  );
}
