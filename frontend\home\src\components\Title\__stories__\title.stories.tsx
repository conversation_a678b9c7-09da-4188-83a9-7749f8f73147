import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Title } from '../title';
import React from 'react';
import { allFixtures } from '../__fixtures__/title.fixtures';
import { Lightbulb, Zap, Plug, Percent } from 'lucide-react';

const meta: Meta<typeof Title> = {
  title: 'UI/Title/Title',
  component: Title,
  parameters: {
    layout: 'centered',
    // เพิ่ม a11y parameter
    a11y: {
      config: {
        rules: [
          {
            // ตรวจสอบความคมชัดของสี
            id: 'color-contrast',
            enabled: true
          }
        ]
      }
    },
    // เพิ่ม autodocs
    docs: {
      description: {
        component: 'Title เป็น reusable component สำหรับใช้เป็นหัวข้อ section ในหน้าเว็บไซต์ ออกแบบให้มีความยืดหยุ่นสูงและสามารถปรับแต่งได้หลากหลายรูปแบบ'
      }
    }
  },
  // กำหนด tag สำหรับการจัดกลุ่มและกรองใน Storybook
  tags: ['autodocs', 'components', 'ui'],
  argTypes: {
    title: {
      control: 'text',
      description: 'หัวข้อหลัก',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'ไม่มี (required)' },
      }
    },
    description: {
      control: 'text',
      description: 'คำอธิบายใต้หัวข้อ',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
      }
    },
    prefixIcon: {
      control: {
        type: 'select',
        options: ['none', 'lightbulb', 'zap', 'plug', 'percent'],
      },
      mapping: {
        none: null,
        lightbulb: <Lightbulb className="h-5 w-5 text-yellow-500" />,
        zap: <Zap className="h-5 w-5 text-yellow-500" />,
        plug: <Plug className="h-5 w-5 text-gray-600" />,
        percent: <Percent className="h-5 w-5 text-blue-500" />,
      },
      description: 'ไอคอนนำหน้าหัวข้อ',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: 'undefined' },
      }
    },
    suffixIcon: {
      control: {
        type: 'select',
        options: ['none', 'emoji-light', 'emoji-electric', 'emoji-plug'],
      },
      mapping: {
        none: null,
        'emoji-light': <React.Fragment>💡✨</React.Fragment>,
        'emoji-electric': <React.Fragment>⚡🔧</React.Fragment>,
        'emoji-plug': <React.Fragment>⚡🔌</React.Fragment>,
      },
      description: 'ไอคอนท้ายหัวข้อ',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: 'undefined' },
      }
    },
    highlightText: {
      control: 'text',
      description: 'ข้อความที่ต้องการไฮไลท์ด้วยพื้นหลังสีเหลือง',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
      }
    },
    viewAllLink: {
      control: 'text',
      description: 'ลิงก์สำหรับปุ่มดูทั้งหมด',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
      }
    },
    viewAllText: {
      control: 'text',
      description: 'ข้อความบนปุ่มดูทั้งหมด',
      defaultValue: 'ดูทั้งหมด',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '"ดูทั้งหมด"' },
      }
    },
    onViewAllClick: {
      action: 'clicked',
      description: 'Event handler เมื่อคลิกปุ่มดูทั้งหมด',
      table: {
        type: { summary: '() => void' },
        defaultValue: { summary: 'undefined' },
      }
    },
    as: {
      control: {
        type: 'select',
        options: ['h1', 'h2', 'h3', 'h4', 'h5'],
      },
      description: 'ระดับของ heading ที่จะใช้',
      defaultValue: 'h2',
      table: {
        type: { summary: "'h1' | 'h2' | 'h3' | 'h4' | 'h5'" },
        defaultValue: { summary: "'h2'" },
      }
    },
    showViewAllButton: {
      control: 'boolean',
      description: 'แสดงปุ่ม "ดูทั้งหมด" หรือไม่',
      defaultValue: true,
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' },
      }
    },
    className: {
      control: 'text',
      description: 'className เพิ่มเติมสำหรับ root container',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
      }
    },
    titleClassName: {
      control: 'text',
      description: 'className เพิ่มเติมสำหรับ title',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
      }
    },
    descriptionClassName: {
      control: 'text',
      description: 'className เพิ่มเติมสำหรับ description',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
      }
    },
  },
};

export default meta;
type Story = StoryObj<typeof Title>;

// เรื่องต่างๆ พร้อมรายละเอียดเพิ่มเติม
export const Default: Story = {
  args: {
    ...allFixtures.base,
  },
  parameters: {
    docs: {
      description: {
        story: 'รูปแบบพื้นฐานของ Title component ที่มีหัวข้อและปุ่ม "ดูทั้งหมด"'
      }
    }
  }
};

export const WithDescription: Story = {
  args: {
    ...allFixtures.withDescription,
  },
  parameters: {
    docs: {
      description: {
        story: 'Title ที่มีคำอธิบายใต้หัวข้อและไอคอนต่อท้าย'
      }
    }
  }
};

export const WithPrefixIcon: Story = {
  args: {
    ...allFixtures.withPrefixIcon,
  },
  parameters: {
    docs: {
      description: {
        story: 'Title ที่มีไอคอนนำหน้าและต่อท้ายหัวข้อ พร้อมคำอธิบาย'
      }
    }
  }
};

export const WithHighlight: Story = {
  args: {
    ...allFixtures.withHighlight,
  },
  parameters: {
    docs: {
      description: {
        story: 'Title ที่มีการไฮไลท์ข้อความบางส่วนด้วยพื้นหลังสีเหลือง'
      }
    }
  }
};

export const Simple: Story = {
  args: {
    ...allFixtures.simple,
  },
  parameters: {
    docs: {
      description: {
        story: 'รูปแบบเรียบง่าย มีเพียงหัวข้อและปุ่ม "ดูทั้งหมด"'
      }
    }
  }
};

export const WithoutButton: Story = {
  args: {
    ...allFixtures.withoutButton,
  },
  parameters: {
    docs: {
      description: {
        story: 'รูปแบบที่ไม่มีปุ่ม "ดูทั้งหมด"'
      }
    }
  }
};

export const HeadingH1: Story = {
  args: {
    ...allFixtures.headingH1,
  },
  parameters: {
    docs: {
      description: {
        story: 'การใช้ heading ระดับ H1 สำหรับหัวข้อหลักของหน้า'
      }
    },
    a11y: {
      config: {
        rules: [
          {
            id: 'heading-order',
            enabled: true
          }
        ]
      }
    }
  }
};

export const HeadingH3: Story = {
  args: {
    ...allFixtures.headingH3,
  },
  parameters: {
    docs: {
      description: {
        story: 'การใช้ heading ระดับ H3 สำหรับหัวข้อย่อย'
      }
    }
  }
};

export const Responsive: Story = {
  args: {
    ...allFixtures.withDescription,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    docs: {
      description: {
        story: 'การแสดงผลบนหน้าจอขนาดเล็ก (mobile)'
      }
    }
  }
};