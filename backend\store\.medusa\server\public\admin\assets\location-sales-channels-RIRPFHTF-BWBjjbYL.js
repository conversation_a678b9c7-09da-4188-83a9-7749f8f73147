import{V as p}from"./chunk-F6ZOHZVB-CmkCPNYI.js";import{u as z,a as H,b as M,c as V}from"./chunk-UVGNHHSZ-DRARdeex.js";import{a2 as A,ad as B,a4 as N,R as K,a_ as $,j as s,b as G,a8 as I,a9 as J,r as j,J as O,eA as Q,t as x,B as S,k as U}from"./index-Bwql5Dzz.js";import{D as X,c as Z}from"./chunk-MGPZHEOT-CqdSNFtj.js";import{K as q}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as n,u as W}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./chunk-FFVOUYTF-DR1d4TPs.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./checkbox-B4pL6X49.js";import"./index-BxZ1678G.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./table-BDZtqXjX.js";import"./arrow-up-mini-D5bOKjDW.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./x-mark-mini-DvSTI7zK.js";import"./triangle-left-mini-Bu6679Aa.js";import"./prompt-BsR9zKsn.js";var Y=A({sales_channels:B(N()).optional()}),f=50,C="sc",ss=({location:e})=>{var m;const{t:a}=G(),{handleSuccess:i}=W(),r=I({defaultValues:{sales_channels:((m=e.sales_channels)==null?void 0:m.map(t=>t.id))??[]},resolver:J(Y)}),{setValue:c}=r,[u,y]=j.useState(ts(e)),b=t=>{const o=Object.keys(t);c("sales_channels",o,{shouldDirty:!0,shouldTouch:!0}),y(t)},v=z({pageSize:f,prefix:C}),{sales_channels:T,count:_,isPending:g,isError:E,error:w}=O({...v},{placeholderData:U}),k=H(),L=as(),R=M(),{mutateAsync:F,isPending:P}=Q(e.id),D=r.handleSubmit(async t=>{var h;const o=(h=e.sales_channels)==null?void 0:h.map(l=>l.id),d=t.sales_channels??[];await F({add:d.filter(l=>!(o!=null&&o.includes(l))),remove:o==null?void 0:o.filter(l=>!d.includes(l))},{onSuccess:()=>{x.success(a("stockLocations.salesChannels.successToast")),i(`/settings/locations/${e.id}`)},onError:l=>{x.error(l.message)}})});if(E)throw w;return s.jsx(n.Form,{form:r,children:s.jsxs(q,{onSubmit:D,className:"flex h-full flex-col",children:[s.jsxs(n.Header,{children:[s.jsx(n.Title,{asChild:!0,children:s.jsx(p,{children:a("stockLocations.salesChannels.header")})}),s.jsx(n.Description,{asChild:!0,children:s.jsx(p,{children:a("stockLocations.salesChannels.hint")})})]}),s.jsx(n.Body,{className:"flex flex-1 flex-col overflow-auto",children:s.jsx(X,{data:T,columns:L,filters:k,emptyState:R,prefix:C,rowSelection:{state:u,onRowSelectionChange:b},pageSize:f,isLoading:g,rowCount:_,layout:"fill",getRowId:t=>t.id})}),s.jsx(n.Footer,{children:s.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[s.jsx(n.Close,{asChild:!0,children:s.jsx(S,{size:"small",variant:"secondary",type:"button",children:a("actions.cancel")})}),s.jsx(S,{size:"small",isLoading:P,type:"submit",children:a("actions.save")})]})})]})})},es=Z(),as=()=>{const e=V();return j.useMemo(()=>[es.select(),...e],[e])};function ts(e){var a;return((a=e.sales_channels)==null?void 0:a.reduce((i,r)=>(i[r.id]=!0,i),{}))??{}}var Es=()=>{const{location_id:e}=K(),{stock_location:a,isPending:i,isError:r,error:c}=$(e,{fields:"id,*sales_channels"}),u=!i&&!!a;if(r)throw c;return s.jsx(n,{children:u&&s.jsx(ss,{location:a})})};export{Es as Component};
