import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { CodeBlock } from '@/components/code-block';

interface ExampleCardProps {
  name: string;
  code: string;
  component: React.ReactNode;
}

export function ExampleCard({ name, code, component }: ExampleCardProps) {
  return (
    <Card id={name}>
      <CardHeader>
        <CardTitle className="scroll-m-20 text-xl font-semibold tracking-tight">{name}</CardTitle>
      </CardHeader>
      <CardContent className="group relative my-4 flex flex-col space-y-2">
        <Tabs defaultValue="preview" className="mr-auto w-full">
          <TabsList className="text-muted-foreground inline-flex h-9 w-full items-center justify-start rounded-none border-b bg-transparent p-0">
            <TabsTrigger
              value="preview"
              className="text-muted-foreground hover:text-foreground data-[state=active]:border-b-primary data-[state=active]:text-foreground relative h-9 rounded-none border-b-2 border-b-transparent bg-transparent px-4 pt-2 pb-3 text-sm font-semibold"
            >
              Preview
            </TabsTrigger>
            <TabsTrigger
              value="code"
              className="text-muted-foreground hover:text-foreground data-[state=active]:border-b-primary data-[state=active]:text-foreground relative h-9 rounded-none border-b-2 border-b-transparent bg-transparent px-4 pt-2 pb-3 text-sm font-semibold"
            >
              Code
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="preview"
            className="focus-visible:ring-ring relative mt-2 min-h-[350px] w-full focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
          >
            <div className="flex h-full w-full items-center justify-center">{component}</div>
          </TabsContent>

          <TabsContent
            value="code"
            className="focus-visible:ring-ring mt-2 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
          >
            <CodeBlock code={code} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
