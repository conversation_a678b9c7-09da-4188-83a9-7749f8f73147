'use client';

/**
 * @private
 * PRIVATE - CLIENT-SIDE i18n IMPLEMENTATION
 * This file is intended for internal use only.
 * Use the unified i18n API from unified-i18n.ts instead.
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { supportedLngs, type SupportedLanguage, languageCodes, ns, defaultNS } from './_private-shared-i18n';

// Initialize client-side i18n
i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'en',
    defaultNS,
    ns,
    interpolation: {
      escapeValue: false, // not needed for React
    },
    react: {
      useSuspense: false,
    },
    supportedLngs: languageCodes,
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    detection: {
      order: ['localStorage', 'cookie', 'navigator'],
      caches: ['localStorage', 'cookie'],
    },
  });

/**
 * Helper function to check if a language is RTL
 */
export function isRTL(language: SupportedLanguage): boolean {
  return supportedLngs[language]?.rtl || false;
}

/**
 * Change the current language (client-side only)
 */
export function changeLanguage(language: SupportedLanguage): Promise<unknown> {
  return i18n.changeLanguage(language);
}

/**
 * Get the current language (client-side only)
 */
export function getLanguage(): SupportedLanguage {
  const currentLang = i18n.language;
  return languageCodes.includes(currentLang as SupportedLanguage) 
    ? currentLang as SupportedLanguage
    : 'en';
}

// Export common constants and types
export { supportedLngs, languageCodes, ns, defaultNS };
export type { SupportedLanguage };

// Export the i18n instance for client-side use
export default i18n; 