import type { Meta, StoryObj } from '@storybook/react';
import { TimeConversionTooltip } from '../TimeConversionTooltip';
import { defaultTooltipData } from '../__fixtures__/TimeConversionTooltip.fixtures';

/**
 * Variants showcase for TimeConversionTooltip component
 */
const meta = {
  title: 'Components/TimeConversionTooltip/Variants',
  component: TimeConversionTooltip,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        story: 'Different visual and functional variants of the TimeConversionTooltip component.',
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof TimeConversionTooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Comparison of all tooltip variants side by side
 */
export const AllVariants: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Hover over any text to see tooltip',
  },
  render: () => (
    <div className="flex flex-col space-y-8">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Enabled States</h3>
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-4">
            <div className="text-muted-foreground w-32 text-sm">Default:</div>
            <TimeConversionTooltip data={defaultTooltipData} enabled={true}>
              March 15, 2023
            </TimeConversionTooltip>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-muted-foreground w-32 text-sm">Disabled:</div>
            <TimeConversionTooltip data={defaultTooltipData} enabled={false}>
              March 15, 2023
            </TimeConversionTooltip>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Z-Index Variants</h3>
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-4">
            <div className="text-muted-foreground w-32 text-sm">Default (60):</div>
            <TimeConversionTooltip data={defaultTooltipData}>March 15, 2023</TimeConversionTooltip>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-muted-foreground w-32 text-sm">Higher (100):</div>
            <TimeConversionTooltip data={defaultTooltipData} zIndex={100}>
              March 15, 2023
            </TimeConversionTooltip>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Tooltip Timings</h3>
        <div className="flex flex-col space-y-2">
          <div className="flex items-center space-x-4">
            <div className="text-muted-foreground w-32 text-sm">Default:</div>
            <TimeConversionTooltip data={defaultTooltipData}>March 15, 2023</TimeConversionTooltip>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-muted-foreground w-32 text-sm">Slow:</div>
            <TimeConversionTooltip data={defaultTooltipData} openDelay={500} closeDelay={500}>
              March 15, 2023
            </TimeConversionTooltip>
          </div>

          <div className="flex items-center space-x-4">
            <div className="text-muted-foreground w-32 text-sm">Fast:</div>
            <TimeConversionTooltip data={defaultTooltipData} openDelay={0} closeDelay={0}>
              March 15, 2023
            </TimeConversionTooltip>
          </div>
        </div>
      </div>
    </div>
  ),
};

/**
 * Examples of TimeConversionTooltip with custom styling
 */
export const CustomStyles: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Custom style tooltip',
  },
  render: () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <div className="text-muted-foreground w-32 text-sm">Bold text:</div>
        <TimeConversionTooltip data={defaultTooltipData} className="font-bold">
          March 15, 2023
        </TimeConversionTooltip>
      </div>

      <div className="flex items-center space-x-4">
        <div className="text-muted-foreground w-32 text-sm">Primary color:</div>
        <TimeConversionTooltip data={defaultTooltipData} className="text-primary">
          March 15, 2023
        </TimeConversionTooltip>
      </div>

      <div className="flex items-center space-x-4">
        <div className="text-muted-foreground w-32 text-sm">Larger text:</div>
        <TimeConversionTooltip data={defaultTooltipData} className="text-lg">
          March 15, 2023
        </TimeConversionTooltip>
      </div>

      <div className="flex items-center space-x-4">
        <div className="text-muted-foreground w-32 text-sm">With underline:</div>
        <TimeConversionTooltip data={defaultTooltipData} className="underline">
          March 15, 2023
        </TimeConversionTooltip>
      </div>
    </div>
  ),
};
