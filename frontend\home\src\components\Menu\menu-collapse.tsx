import React, { useState } from "react";
import { ChevronUp, ChevronDown, X } from "lucide-react";
import { cn } from "@/lib/utils";

export interface MenuCollapseProps {
  /**
   * The title to display for the collapsible menu
   */
  title: string;
  
  /**
   * Children to render within the collapsible content area
   */
  children: React.ReactNode;
  
  /**
   * Optional custom className for the main container
   */
  className?: string;
  
  /**
   * Optional custom className for the header container
   */
  headerClassName?: string;
  
  /**
   * Optional custom className for the content container
   */
  contentClassName?: string;
  
  /**
   * Whether the menu should be expanded by default
   */
  defaultOpen?: boolean;
  
  /**
   * Controlled open state (if you want to control the component externally)
   */
  open?: boolean;
  
  /**
   * Callback fired when the open state changes
   */
  onOpenChange?: (open: boolean) => void;
  
  /**
   * Right side action element to render in the header (e.g. X button to clear)
   */
  headerAction?: React.ReactNode;
  
  /**
   * Aria label for the toggle button
   */
  ariaLabel?: string;
  
  /**
   * Custom icon to use for collapsed state (defaults to ChevronDown)
   */
  collapsedIcon?: React.ReactNode;
  
  /**
   * Custom icon to use for expanded state (defaults to ChevronUp)
   */
  expandedIcon?: React.ReactNode;
}

/**
 * MenuCollapse - A reusable collapsible menu component that can be used in various contexts
 * such as filters, accordion sections, FAQs, etc.
 */
export const MenuCollapse = ({
  title,
  children,
  className,
  headerClassName,
  contentClassName,
  defaultOpen = false,
  open: controlledOpen,
  onOpenChange,
  headerAction,
  ariaLabel,
  collapsedIcon = <ChevronDown className="h-5 w-5" />,
  expandedIcon = <ChevronUp className="h-5 w-5" />,
}: MenuCollapseProps) => {
  // Use internal state if component is uncontrolled
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  
  // Determine if component is controlled or uncontrolled
  const isControlled = controlledOpen !== undefined;
  const isOpen = isControlled ? controlledOpen : internalOpen;
  
  // Handle toggle
  const handleToggle = () => {
    if (isControlled) {
      // If controlled externally, call the callback
      onOpenChange?.(!controlledOpen);
    } else {
      // If uncontrolled, update internal state
      setInternalOpen(!internalOpen);
    }
  };

  return (
    <div 
      className={cn(
        "w-full bg-slate-50 border-b border-slate-100",
        className
      )}
    >
      {/* Header Section */}
      <div 
        className={cn(
          "flex items-center justify-between px-4 py-3",
          headerClassName
        )}
      >
        <button
          type="button"
          onClick={handleToggle}
          className="flex-1 flex items-center justify-between"
          aria-expanded={isOpen}
          aria-controls={`menu-collapse-content-${title.replace(/\s+/g, '-').toLowerCase()}`}
          aria-label={ariaLabel || `${isOpen ? 'Collapse' : 'Expand'} ${title}`}
        >
          <h3 className="font-medium text-navy-800">{title}</h3>
          <div className="text-navy-800">
            {isOpen ? expandedIcon : collapsedIcon}
          </div>
        </button>
        
        {headerAction && (
          <div className="ml-2">
            {headerAction}
          </div>
        )}
      </div>
      
      {/* Content Section - Conditionally rendered */}
      {isOpen && (
        <div 
          id={`menu-collapse-content-${title.replace(/\s+/g, '-').toLowerCase()}`}
          className={cn(
            "px-4 py-3 animate-in fade-in slide-in-from-top-1 duration-150",
            contentClassName
          )}
        >
          {children}
        </div>
      )}
    </div>
  );
};

export default MenuCollapse;