import{r as t,j as e,T as r,x as l}from"./index-Bwql5Dzz.js";var i=t.forwardRef((s,a)=>e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 z-10 flex w-8 items-center justify-center border-r",children:e.jsx(r,{className:"text-ui-fg-muted",size:"small",leading:"compact",weight:"plus",children:"/"})}),e.jsx(l,{ref:a,...s,className:"pl-10"})]}));i.displayName="HandleInput";export{i as H};
