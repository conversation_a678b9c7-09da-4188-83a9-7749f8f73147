import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { I18nEdit as Edit } from '../I18nEdit';
import { sampleApiKeyRestricted } from '../__fixtures__/ApiKey.fixtures';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { apiKeyTranslations } from '../ApiKey-i18n';
import type { PermissionTypeValue } from '../ApiKey-Schema';

// Initialize i18next for testing
i18n.use(initReactI18next).init({
  resources: {
    en: {
      common: apiKeyTranslations.en,
    },
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
  ns: ['common'],
  defaultNS: 'common',
});

describe('Edit API Key Component (Internationalized)', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  const defaultProps = {
    apiKey: sampleApiKeyRestricted,
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
    isSubmitting: false,
  };

  beforeEach(() => {
    mockOnSubmit.mockClear();
    mockOnCancel.mockClear();
  });

  it('renders correctly with default props', () => {
    render(<Edit {...defaultProps} />);

    // Check that the component title is rendered
    expect(screen.getByTestId('form-title')).toHaveTextContent('Edit secret key');

    // Check that the form fields are pre-filled with API key data
    expect(screen.getByLabelText('Name')).toHaveValue(sampleApiKeyRestricted.name);

    // Check permission type buttons
    const permissionTypeButtons = screen.getAllByText(sampleApiKeyRestricted.permissionType);
    expect(permissionTypeButtons[0]).toHaveClass('bg-primary');

    // Check that the buttons are rendered
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
  });

  it('shows resource permissions when "Restricted" is selected', async () => {
    // Set apiKey permission type to 'All'
    const props = {
      ...defaultProps,
      apiKey: {
        ...sampleApiKeyRestricted,
        permissionType: 'All' as PermissionTypeValue,
      },
    };

    render(<Edit {...props} />);

    // Initially, resource permissions should not be visible
    expect(screen.queryByText('Resources')).not.toBeInTheDocument();

    // Click on "Restricted" button
    const restrictedButton = screen.getByText('Restricted');
    await userEvent.click(restrictedButton);

    // Resource permissions should now be visible
    expect(screen.getByText('Resources')).toBeInTheDocument();
    expect(screen.getByText('Models')).toBeInTheDocument();

    // Permission options should be visible
    expect(screen.getAllByText('None').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Read').length).toBeGreaterThan(0);
    expect(screen.getAllByText('Write').length).toBeGreaterThan(0);
  });

  it('calls onCancel when Cancel button is clicked', async () => {
    render(<Edit {...defaultProps} />);

    // Click the Cancel button
    const cancelButton = screen.getByText('Cancel');
    await userEvent.click(cancelButton);

    // Check that onCancel was called
    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('disables buttons when submitting', () => {
    render(<Edit {...defaultProps} isSubmitting={true} />);

    // Both buttons should be disabled
    expect(screen.getByText('Cancel')).toBeDisabled();
    expect(screen.getByText('Saving...')).toBeDisabled();
  });

  it('submits the form with modified data', async () => {
    render(<Edit {...defaultProps} />);

    // Change the name
    const nameInput = screen.getByLabelText('Name');
    await userEvent.clear(nameInput);
    await userEvent.type(nameInput, 'Updated API Key');

    // Submit the form
    const submitButton = screen.getByText('Save');
    await userEvent.click(submitButton);

    // Check that onSubmit was called with the updated data
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledTimes(1);
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Updated API Key',
          permissionType: sampleApiKeyRestricted.permissionType,
        }),
      );
    });
  });
});
