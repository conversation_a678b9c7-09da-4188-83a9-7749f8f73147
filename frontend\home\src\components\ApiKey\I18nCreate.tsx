'use client';

import * as React from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useLanguageChange } from '@/hooks/useLanguageChange';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  ApiKeyNameField,
  PermissionTypeField,
  OwnerTypeField,
  ProjectField,
} from './ApiKey-Fields';
import { ApiKeyPermissionSection } from './ApiKey-PermissionSection';
import { createI18nApiKeySchema, type I18nApiKeyFormValues } from './ApiKey-i18nSchema';
import type { CreateApiKeyProps } from './ApiKey-Types';
import type { PermissionTypeValue, ApiKeyFormValues } from './ApiKey-Schema';

/**
 * Internationalized Create API Key component
 */
export const I18nCreate: React.FC<CreateApiKeyProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false,
  availableProjects,
  className,
}) => {
  const { t } = useTranslation();
  const currentLang = useLanguageChange();

  // Create schema with translations
  const i18nApiKeySchema = React.useMemo(() => createI18nApiKeySchema(t), [t]);

  const [permissionType, setPermissionType] = React.useState<PermissionTypeValue>('All');

  // Default values for the form
  const defaultValues: I18nApiKeyFormValues = {
    name: '',
    permissionType: 'All',
    ownerType: 'You',
    resourcePermissions: {
      models: 'None',
      modelCapabilities: 'None',
      assistants: 'None',
      threads: 'None',
      evals: 'None',
      fineTuning: 'None',
      files: 'None',
    },
    project: availableProjects[0]?.value || '',
  };

  // Form setup with React Hook Form and Zod validation
  const methods = useForm<I18nApiKeyFormValues>({
    resolver: zodResolver(i18nApiKeySchema),
    defaultValues,
    mode: 'onBlur',
  });

  const {
    handleSubmit,
    formState: { isValid },
    watch,
  } = methods;

  // Handle form submission
  const handleFormSubmit = (data: I18nApiKeyFormValues) => {
    onSubmit(data as ApiKeyFormValues);
  };

  // Show different sections based on form state
  const isServiceAccount = watch('ownerType') === 'Service account';

  return (
    <FormProvider {...methods}>
      <div
        className={cn(
          'bg-background w-full max-w-md overflow-y-auto rounded-lg p-6 shadow-lg',
          className,
        )}
        data-testid="i18n-create-form"
      >
        <h2 className="mb-4 text-xl font-semibold" data-testid="form-title">
          {t('apiKey.titles.create', 'Create new secret key')}
        </h2>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Owner Type */}
          <OwnerTypeField
            name="ownerType"
            label={t('apiKey.ownerType.label', 'Owned by')}
            onChange={(value) => {
              // This is needed to maintain the state for UI display purposes
              if (typeof value === 'string') {
                if (value === 'You' || value === 'Service account') {
                  // Empty function to fix the linting error while maintaining the prop
                }
              }
            }}
            i18nPrefix="apiKey"
          />

          {/* Description message based on owner type */}
          {isServiceAccount ? (
            <div className="text-foreground text-sm">
              {t(
                'apiKey.descriptions.serviceAccount',
                'A new bot member (service account) will be added to your project, and an API key will be created.',
              )}
            </div>
          ) : (
            <div className="text-foreground text-sm">
              {t(
                'apiKey.descriptions.personal',
                'This API key is tied to your user and can make requests against the selected project. If you are removed from the organization or project, this key will be disabled.',
              )}
            </div>
          )}

          {/* Name field - different label based on owner type */}
          <ApiKeyNameField
            name="name"
            label={
              isServiceAccount
                ? t('apiKey.name.serviceLabel', 'Service Key Name')
                : t('apiKey.name.label', 'Name')
            }
            placeholder={
              isServiceAccount
                ? t('apiKey.name.servicePlaceholder', 'My Service Account Key')
                : t('apiKey.name.placeholder', 'My Test Key')
            }
            i18nPrefix="apiKey"
          />

          {/* Project selection */}
          <ProjectField
            name="project"
            label={t('apiKey.project.label', 'Project')}
            options={availableProjects}
            placeholder={t('apiKey.project.placeholder', 'Select a project')}
            i18nPrefix="apiKey"
          />

          {/* Permission Type */}
          <PermissionTypeField
            name="permissionType"
            label={t('apiKey.permissionType.label', 'Permissions')}
            onChange={(value: PermissionTypeValue) => setPermissionType(value)}
            i18nPrefix="apiKey"
          />

          {/* Resource Permissions - using the reusable component */}
          <ApiKeyPermissionSection permissionType={permissionType} i18nPrefix="apiKey" />

          {/* Form actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              {t('apiKey.actions.cancel', 'Cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting || !isValid}>
              {isSubmitting
                ? t('apiKey.actions.submitting', 'Creating...')
                : t('apiKey.actions.create', 'Create secret key')}
            </Button>
          </div>

          {/* Language indicator (for dev/debug purposes) */}
          <div className="text-muted-foreground pt-2 text-xs">{currentLang}</div>
        </form>
      </div>
    </FormProvider>
  );
};

export default I18nCreate;
