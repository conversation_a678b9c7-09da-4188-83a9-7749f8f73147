import{u as F}from"./chunk-G3QXMPRB-BXa5-FQN.js";import{a2 as I,ad as z,a4 as D,R as B,h as H,j as e,b as C,a8 as M,a9 as N,en as L,r as b,e as V,k as K,t as S,E as O,B as y,V as q}from"./index-Bwql5Dzz.js";import{u as G,_ as Q}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{u as Z,a as $}from"./chunk-U6CSGYH6-BpcURsBT.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{K as J}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as l,u as U}from"./chunk-JGQGO74V-DtHO1ucg.js";import{C as j}from"./checkbox-B4pL6X49.js";import{c as W}from"./index-BxZ1678G.js";import"./chunk-IQBAUTU5-D_4dFOf0.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./prompt-BsR9zKsn.js";var X=I({product_ids:z(D()).min(1)}),f=50,Y=({salesChannel:r})=>{const{t:s}=C(),{handleSuccess:t}=U(),a=M({defaultValues:{product_ids:[]},resolver:N(X)}),{setValue:d}=a,{mutateAsync:c,isPending:p}=L(r.id),[u,m]=b.useState({}),i=o=>{const n=typeof o=="function"?o(u):o,h=Object.keys(n);d("product_ids",h,{shouldDirty:!0,shouldTouch:!0}),m(n)},{searchParams:P,raw:_}=Z({pageSize:f}),{products:v,count:g,isPending:k,isError:A,error:R}=V({fields:"*variants,*sales_channels",...P},{placeholderData:K}),x=se(),T=$(["sales_channel_id"]),{table:w}=G({data:v??[],columns:x,enableRowSelection:o=>{var n;return!((n=o.original.sales_channels)!=null&&n.map(h=>h.id).includes(r.id))},enablePagination:!0,getRowId:o=>o.id,pageSize:f,count:g,rowSelection:{state:u,updater:i},meta:{salesChannelId:r.id}}),E=a.handleSubmit(async o=>{await c(o.product_ids,{onSuccess:()=>{S.success(s("salesChannels.toast.update")),t()},onError:n=>S.error(n.message)})});if(A)throw R;return e.jsx(l.Form,{form:a,children:e.jsxs(J,{onSubmit:E,className:"flex h-full flex-col overflow-hidden",children:[e.jsx(l.Header,{children:e.jsx("div",{className:"flex items-center justify-end gap-x-2",children:a.formState.errors.product_ids&&e.jsx(O,{variant:"error",children:a.formState.errors.product_ids.message})})}),e.jsx(l.Body,{className:"flex size-full flex-col overflow-y-auto",children:e.jsx(Q,{table:w,count:g,columns:x,pageSize:f,isLoading:k,filters:T,orderBy:[{key:"title",label:s("fields.title")},{key:"status",label:s("fields.status")},{key:"created_at",label:s("fields.createdAt")},{key:"updated_at",label:s("fields.updatedAt")}],queryObject:_,layout:"fill",pagination:!0,search:"autofocus",noRecords:{message:s("salesChannels.products.add.list.noRecordsMessage")}})}),e.jsx(l.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(l.Close,{asChild:!0,children:e.jsx(y,{size:"small",variant:"secondary",children:s("actions.cancel")})}),e.jsx(y,{size:"small",type:"submit",isLoading:p,children:s("actions.save")})]})})]})})},ee=W(),se=()=>{const r=F(),{t:s}=C();return b.useMemo(()=>[ee.display({id:"select",header:({table:t})=>e.jsx(j,{checked:t.getIsSomePageRowsSelected()?"indeterminate":t.getIsAllPageRowsSelected(),onCheckedChange:a=>t.toggleAllPageRowsSelected(!!a)}),cell:({row:t,table:a})=>{var m;const{salesChannelId:d}=a.options.meta,c=(m=t.original.sales_channels)==null?void 0:m.map(i=>i.id).includes(d),p=t.getIsSelected()||c,u=e.jsx(j,{checked:p,disabled:c,onCheckedChange:i=>t.toggleSelected(!!i),onClick:i=>{i.stopPropagation()}});return c?e.jsx(q,{content:s("salesChannels.productAlreadyAdded"),side:"right",children:u}):u}}),...r],[s,r])},Ie=()=>{const{id:r}=B(),{sales_channel:s,isPending:t,isError:a,error:d}=H(r);if(a)throw d;return e.jsx(l,{children:!t&&s&&e.jsx(Y,{salesChannel:s})})};export{Ie as Component};
