'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useTheme } from 'next-themes';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';

// Types for our SPA data
interface PageData {
  content: string;
  timestamp: string;
}

// SPA main component that handles client-side routing and state
export default function SPAExample() {
  const { t } = useTranslation();
  const { theme: _theme } = useTheme();
  // State for the current "page" within our SPA
  const [currentPage, setCurrentPage] = useState('home');
  // State for the data we'll display
  const [data, setData] = useState<PageData | null>(null);
  // State for loading indicators
  const [isLoading, setIsLoading] = useState(false);
  // State for the URL to show how client-side routing works
  const [url, setUrl] = useState('');

  // Update URL to match our SPA "page" (simulating client-side routing)
  useEffect(() => {
    // Update the browser URL without a page reload (this is what SPAs do)
    const newUrl = `/rendering-examples/spa#${currentPage}`;
    window.history.pushState(null, '', newUrl);
    setUrl(newUrl);

    // Also handle browser back/forward navigation
    const handlePopState = () => {
      const hash = window.location.hash.replace('#', '');
      if (hash) {
        setCurrentPage(hash);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [currentPage]);

  // Initialize SPA by checking for a hash in the URL
  useEffect(() => {
    const hash = window.location.hash.replace('#', '');
    if (hash) {
      setCurrentPage(hash);
    }
    setUrl(window.location.href);
  }, []);

  // Simulate loading data when the page changes
  useEffect(() => {
    setIsLoading(true);

    // Simulate an API call with a delay
    const timer = setTimeout(() => {
      setData({
        content: `Content for ${currentPage} page`,
        timestamp: new Date().toISOString(),
      });
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [currentPage]);

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="spa" />

      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">
          {t('rendering.spaTitle', 'Single Page Application (SPA)')}
        </h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">
            {t('rendering.spaHowWorks', 'How SPAs Work')}
          </h2>
          <p className="mb-4">
            {t(
              'rendering.spaDescription',
              'Single Page Applications load a single HTML page and then dynamically update the content as the user interacts with the app. Navigation between "pages" happens entirely on the client-side without refreshing the browser.',
            )}
          </p>
          <p className="mb-4">
            {t(
              'rendering.spaNote',
              "SPAs provide a smoother user experience since they don't require page reloads, maintaining application state and allowing for more app-like interactions. The current URL is managed using the browser's History API.",
            )}
          </p>
          <p className="text-muted-foreground text-sm">
            {t('rendering.currentUrl', 'Current URL')}:{' '}
            <code className="bg-muted-foreground/20 rounded p-1">{url}</code>
          </p>
        </div>

        {/* SPA Navigation */}
        <nav className="bg-muted/40 mb-6 flex overflow-x-auto rounded-lg p-4">
          <ul className="flex gap-2">
            {['home', 'about', 'products', 'contact'].map((page) => (
              <li key={page}>
                <button
                  onClick={() => setCurrentPage(page)}
                  className={`rounded-md px-4 py-2 transition-colors ${
                    currentPage === page ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                  }`}
                >
                  {t(`rendering.routes.${page}`, page.charAt(0).toUpperCase() + page.slice(1))}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Content area changes based on current "page" */}
        <div className="bg-card mb-8 rounded-lg p-6 shadow-sm">
          <h2 className="mb-4 text-2xl font-bold capitalize">{currentPage}</h2>

          {isLoading ? (
            <div className="animate-pulse space-y-2">
              <div className="bg-muted h-4 w-3/4 rounded"></div>
              <div className="bg-muted h-4 w-1/2 rounded"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <p>{data?.content}</p>
              <p className="text-muted-foreground text-sm">
                {t('rendering.loadedAt', 'Loaded at')}: {data?.timestamp}
              </p>
            </div>
          )}
        </div>

        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">
            {t('rendering.spaFeatures', 'Key SPA Features')}
          </h2>
          <ul className="list-disc space-y-2 pl-5">
            <li>{t('rendering.spaFeature1', 'Client-side routing without page reloads')}</li>
            <li>
              {t(
                'rendering.spaFeature2',
                'Persistent application state between "page" transitions',
              )}
            </li>
            <li>{t('rendering.spaFeature3', 'Ability to work offline with service workers')}</li>
            <li>{t('rendering.spaFeature4', 'Smooth transitions and animations between views')}</li>
          </ul>
        </div>

        <div className="mt-8 flex flex-col gap-4 sm:flex-row">
          <Link
            href="/rendering-examples/spa/with-data"
            className="bg-primary/5 hover:bg-primary/10 border-primary/20 flex-1 rounded-lg border px-4 py-3 text-center font-medium"
          >
            {t('rendering.withDataExample', 'With Data Example')} →
          </Link>
          <Link
            href="/rendering-examples/spa/advanced"
            className="bg-primary hover:bg-primary/90 flex-1 rounded-lg px-4 py-3 text-center font-medium text-white"
          >
            {t('rendering.advancedExample', 'Advanced Example')} →
          </Link>
        </div>

        <div className="mt-6 flex justify-center">
          <Link
            href="/rendering-examples/spa/router-example"
            className="text-primary hover:underline"
          >
            {t(
              'rendering.viewFullSPAExample',
              'View Full SPA Router Example with Theme & Language Support',
            )}
          </Link>
        </div>
      </main>
    </div>
  );
}
