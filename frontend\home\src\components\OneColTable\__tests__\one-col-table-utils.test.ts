import {
  formatTimestamp,
  filterItems,
  sortItems,
  getStatusColorClass,
  paginateItems,
} from '../utils';
import { mockItems } from '../__fixtures__/one-col-table.fixtures';

describe('OneColTable Utils', () => {
  describe('formatTimestamp', () => {
    it('formats a Date object correctly', () => {
      const date = new Date('2025-03-17T11:42:00Z');
      const formatted = formatTimestamp(date);
      expect(formatted).toBeTruthy();
      expect(typeof formatted).toBe('string');
    });

    it('formats a date string correctly', () => {
      const dateString = '2025-03-17T11:42:00Z';
      const formatted = formatTimestamp(dateString);
      expect(formatted).toBeTruthy();
      expect(typeof formatted).toBe('string');
    });

    it('returns empty string for undefined timestamp', () => {
      const formatted = formatTimestamp(undefined);
      expect(formatted).toBe('');
    });

    it('handles invalid dates gracefully', () => {
      const invalidDate = 'not-a-date';
      const formatted = formatTimestamp(invalidDate);
      expect(formatted).toBe(invalidDate);
    });

    it('uses custom format pattern when provided', () => {
      const date = new Date('2025-03-17T11:42:00Z');
      const formatted = formatTimestamp(date, 'yyyy/MM/dd');
      expect(formatted).toBe('2025/03/17');
    });
  });

  describe('filterItems', () => {
    it('filters items based on content', () => {
      const searchTerm = 'Batch Processing #1';
      const filtered = filterItems(mockItems, searchTerm);
      expect(filtered.length).toBe(1);
      expect(filtered[0].id).toBe('batch_67d7a8282548819083507d13a254931');
    });

    it('filters items based on secondary content', () => {
      const searchTerm = 'Completed successfully';
      const filtered = filterItems(mockItems, searchTerm);
      expect(filtered.length).toBeGreaterThan(0);
      filtered.forEach((item) => {
        expect(item.secondaryContent).toContain('Completed successfully');
      });
    });

    it('returns all items when search term is empty', () => {
      const filtered = filterItems(mockItems, '');
      expect(filtered.length).toBe(mockItems.length);
    });

    it('returns empty array when no matches found', () => {
      const searchTerm = 'ThisTermDoesNotExistAnywhere';
      const filtered = filterItems(mockItems, searchTerm);
      expect(filtered.length).toBe(0);
    });

    it('is case insensitive', () => {
      const searchTerm = 'batch processing';
      const filtered = filterItems(mockItems, searchTerm);
      expect(filtered.length).toBeGreaterThan(0);
    });
  });

  describe('sortItems', () => {
    it('sorts items by timestamp in descending order by default', () => {
      const sorted = sortItems([...mockItems]);

      // Assume the timestamps are in a specific order in the fixture
      // Check that the first item has a timestamp that's greater than or equal to the last item
      const firstTimestamp = sorted[0].timestamp as Date;
      const lastTimestamp = sorted[sorted.length - 1].timestamp as Date;

      expect(firstTimestamp.getTime()).toBeGreaterThanOrEqual(lastTimestamp.getTime());
    });

    it('sorts items by timestamp in ascending order when specified', () => {
      const sorted = sortItems([...mockItems], 'timestamp', 'asc');

      // Check that the first item has a timestamp that's less than or equal to the last item
      const firstTimestamp = sorted[0].timestamp as Date;
      const lastTimestamp = sorted[sorted.length - 1].timestamp as Date;

      expect(firstTimestamp.getTime()).toBeLessThanOrEqual(lastTimestamp.getTime());
    });

    it('sorts by other fields when specified', () => {
      // Sort by content
      const sorted = sortItems([...mockItems], 'content', 'asc');

      // Check that items are sorted alphabetically by content
      for (let i = 0; i < sorted.length - 1; i++) {
        const current = sorted[i].content as string;
        const next = sorted[i + 1].content as string;
        expect(current.localeCompare(next)).toBeLessThanOrEqual(0);
      }
    });

    it('handles empty arrays gracefully', () => {
      const sorted = sortItems([]);
      expect(sorted).toEqual([]);
    });
  });

  describe('getStatusColorClass', () => {
    it('returns the correct color class for completed status', () => {
      const colorClass = getStatusColorClass('completed');
      expect(colorClass).toContain('text-green');
    });

    it('returns the correct color class for in-progress status', () => {
      const colorClass = getStatusColorClass('in-progress');
      expect(colorClass).toContain('text-blue');
    });

    it('returns the correct color class for pending status', () => {
      const colorClass = getStatusColorClass('pending');
      expect(colorClass).toContain('text-yellow');
    });

    it('returns the correct color class for warning status', () => {
      const colorClass = getStatusColorClass('warning');
      expect(colorClass).toContain('text-orange');
    });

    it('returns the correct color class for error status', () => {
      const colorClass = getStatusColorClass('error');
      expect(colorClass).toContain('text-red');
    });

    it('returns default class for unknown status', () => {
      const colorClass = getStatusColorClass('unknown-status');
      expect(colorClass).toContain('text-muted');
    });

    it('returns default class for undefined status', () => {
      const colorClass = getStatusColorClass(undefined);
      expect(colorClass).toContain('text-muted');
    });
  });

  describe('paginateItems', () => {
    it('returns the correct page of items', () => {
      const pageSize = 5;
      const page = 2;
      const paginated = paginateItems([...mockItems], page, pageSize);

      expect(paginated.length).toBe(pageSize);
      expect(paginated[0]).toEqual(mockItems[pageSize]);
    });

    it('returns fewer items if not enough items for full page', () => {
      const pageSize = 10;
      const page = 2;
      const paginated = paginateItems([...mockItems], page, pageSize);

      const expectedLength = Math.max(0, mockItems.length - pageSize);
      expect(paginated.length).toBe(expectedLength);
    });

    it('returns empty array for out of range page', () => {
      const pageSize = 5;
      const page = 100; // Way beyond the available items
      const paginated = paginateItems([...mockItems], page, pageSize);

      expect(paginated).toEqual([]);
    });

    it('uses default values when not provided', () => {
      const paginated = paginateItems([...mockItems]);

      // Default is page 1, pageSize 10
      expect(paginated.length).toBeLessThanOrEqual(10);
      expect(paginated[0]).toEqual(mockItems[0]);
    });

    it('handles empty arrays gracefully', () => {
      const paginated = paginateItems([]);
      expect(paginated).toEqual([]);
    });
  });
});
