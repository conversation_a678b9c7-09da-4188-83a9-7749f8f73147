// components/Pagination/_tests_/pagination.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Pagination } from '../pagination';
import { paginationFixtures } from '../__fixtures__/pagination.fixtures';

describe('Pagination Component', () => {
  const onPageChangeMock = jest.fn();

  beforeEach(() => {
    onPageChangeMock.mockClear();
  });

  // General tests
  test('renders null when totalPages is 1', () => {
    const { container } = render(
      <Pagination
        totalPages={1}
        currentPage={1}
        onPageChange={onPageChangeMock}
      />
    );
    expect(container.firstChild).toBeNull();
  });

  test('disables all buttons when disabled prop is true', () => {
    render(
      <Pagination
        totalPages={10}
        currentPage={5}
        onPageChange={onPageChangeMock}
        disabled={true}
      />
    );

    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  // Numbered variant tests
  describe('Numbered Variant', () => {
    test('renders pagination with correct elements', () => {
      render(
        <Pagination
          {...paginationFixtures.numberedMiddlePage}
          onPageChange={onPageChangeMock}
        />
      );

      // Should have next & prev buttons
      expect(screen.getByLabelText('Previous page')).toBeInTheDocument();
      expect(screen.getByLabelText('Next page')).toBeInTheDocument();
      
      // Current page should be highlighted
      const currentPageButton = screen.getByLabelText('Page 5');
      expect(currentPageButton).toHaveAttribute('aria-current', 'page');
    });

    test('calls onPageChange when page button is clicked', () => {
      render(
        <Pagination
          {...paginationFixtures.numberedMiddlePage}
          onPageChange={onPageChangeMock}
        />
      );

      // Click on page 6
      const pageButton = screen.getByText('6');
      fireEvent.click(pageButton);

      expect(onPageChangeMock).toHaveBeenCalledWith(6);
    });

    test('calls onPageChange when next button is clicked', () => {
      render(
        <Pagination
          {...paginationFixtures.numberedMiddlePage}
          onPageChange={onPageChangeMock}
        />
      );

      const nextButton = screen.getByLabelText('Next page');
      fireEvent.click(nextButton);

      expect(onPageChangeMock).toHaveBeenCalledWith(6);
    });

    test('calls onPageChange when previous button is clicked', () => {
      render(
        <Pagination
          {...paginationFixtures.numberedMiddlePage}
          onPageChange={onPageChangeMock}
        />
      );

      const prevButton = screen.getByLabelText('Previous page');
      fireEvent.click(prevButton);

      expect(onPageChangeMock).toHaveBeenCalledWith(4);
    });

    test('disables previous button on first page', () => {
      render(
        <Pagination
          {...paginationFixtures.numberedBasic}
          onPageChange={onPageChangeMock}
        />
      );

      const prevButton = screen.getByLabelText('Previous page');
      expect(prevButton).toBeDisabled();
    });

    test('disables next button on last page', () => {
      render(
        <Pagination
          {...paginationFixtures.numberedLastPage}
          onPageChange={onPageChangeMock}
        />
      );

      const nextButton = screen.getByLabelText('Next page');
      expect(nextButton).toBeDisabled();
    });

    test('renders without ellipsis when showEllipsis is false', () => {
      render(
        <Pagination
          {...paginationFixtures.numberedNoEllipsis}
          onPageChange={onPageChangeMock}
        />
      );

      // Should not contain ellipsis
      expect(screen.queryByText('...')).not.toBeInTheDocument();
    });
  });

  // Simple variant tests
  describe('Simple Variant', () => {
    test('renders simple pagination with correct elements', () => {
      render(
        <Pagination
          {...paginationFixtures.simpleMiddlePage}
          onPageChange={onPageChangeMock}
        />
      );

      // Should have next & prev buttons
      expect(screen.getByLabelText('Previous page')).toBeInTheDocument();
      expect(screen.getByLabelText('Next page')).toBeInTheDocument();
      
      // Should show current page / total pages
      expect(screen.getByText('หน้า 12 / 24')).toBeInTheDocument();
    });

    test('renders with custom pageText', () => {
      render(
        <Pagination
          {...paginationFixtures.simpleCustomText}
          onPageChange={onPageChangeMock}
        />
      );

      expect(screen.getByText('Page 1 / 24')).toBeInTheDocument();
    });

    test('calls onPageChange when next button is clicked', () => {
      render(
        <Pagination
          {...paginationFixtures.simpleMiddlePage}
          onPageChange={onPageChangeMock}
        />
      );

      const nextButton = screen.getByLabelText('Next page');
      fireEvent.click(nextButton);

      expect(onPageChangeMock).toHaveBeenCalledWith(13);
    });

    test('calls onPageChange when previous button is clicked', () => {
      render(
        <Pagination
          {...paginationFixtures.simpleMiddlePage}
          onPageChange={onPageChangeMock}
        />
      );

      const prevButton = screen.getByLabelText('Previous page');
      fireEvent.click(prevButton);

      expect(onPageChangeMock).toHaveBeenCalledWith(11);
    });

    test('disables previous button on first page', () => {
      render(
        <Pagination
          {...paginationFixtures.simpleFirstPage}
          onPageChange={onPageChangeMock}
        />
      );

      const prevButton = screen.getByLabelText('Previous page');
      expect(prevButton).toBeDisabled();
    });

    test('disables next button on last page', () => {
      render(
        <Pagination
          {...paginationFixtures.simpleLastPage}
          onPageChange={onPageChangeMock}
        />
      );

      const nextButton = screen.getByLabelText('Next page');
      expect(nextButton).toBeDisabled();
    });
  });
});