import * as React from 'react';
import type { TFunction } from 'i18next';

/**
 * Base props for TimeConversionTooltip
 */
export interface BaseProps {
  /** Optional CSS class to be merged */
  className?: string;
}

/**
 * Date conversion data format
 */
export interface DateConversionTooltip {
  /** Timezone offset (e.g. "+05:00" or "-03:00") */
  timezone: string;
  /** Formatted local time (e.g. "4:30 PM") */
  localTime: string;
  /** Formatted UTC time (e.g. "9:30 PM") */
  utcTime: string;
  /** Formatted date (e.g. "Mar 15, 2023") */
  date: string;
  /** Local timezone name (e.g. "America/New_York") */
  localTimezone?: string;
}

/**
 * Internationalization props
 */
export interface I18nProps {
  /** Custom translation namespace */
  i18nNamespace?: string;
  /** Translation prefix for component keys */
  i18nPrefix?: string;
}

/**
 * Props for TimeConversionTooltip component
 */
export interface TimeConversionTooltipProps
  extends Omit<React.HTMLAttributes<HTMLSpanElement>, 'children'>,
    BaseProps,
    I18nProps {
  /** Date conversion data to display in tooltip */
  data: DateConversionTooltip;
  /** Content to display with the tooltip */
  children: React.ReactNode | ((t: TFunction) => React.ReactNode);
  /** Whether the tooltip functionality is enabled */
  enabled?: boolean;
  /** Custom delay before showing the tooltip (in ms) */
  openDelay?: number;
  /** Custom delay before hiding the tooltip (in ms) */
  closeDelay?: number;
  /** Custom z-index for the tooltip */
  zIndex?: number;
  /** Language for date formatting */
  language?: string;
}

/**
 * Props for the DottedUnderline subcomponent
 */
export interface DottedUnderlineProps {
  /** Whether the underline is visible */
  isVisible: boolean;
  /** Optional CSS class */
  className?: string;
  /** Optional z-index */
  zIndex?: number;
}
