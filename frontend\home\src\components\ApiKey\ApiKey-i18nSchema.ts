import { z } from 'zod';
import { TFunction } from 'i18next';

// Regular expressions for validation
const NAME_REGEX = /^[a-zA-Z0-9_-]{1,100}$/;

/**
 * Creates a schema with internationalized error messages
 */
export const createI18nApiKeySchema = (t: TFunction) => {
  // Error messages using translation keys
  const errorMessages = {
    required: () => t('apiKey.validation.required'),
    name: () => t('apiKey.validation.name'),
  };

  // Permission type for API key
  const PermissionType = z.enum(['All', 'Restricted', 'Read only']);

  // Resource permission type
  const ResourcePermission = z.enum(['None', 'Read', 'Write']);

  // Resource permission schema
  const ResourcePermissionsSchema = z.object({
    models: ResourcePermission.optional().default('None'),
    modelCapabilities: ResourcePermission.optional().default('None'),
    assistants: ResourcePermission.optional().default('None'),
    threads: ResourcePermission.optional().default('None'),
    evals: ResourcePermission.optional().default('None'),
    fineTuning: ResourcePermission.optional().default('None'),
    files: ResourcePermission.optional().default('None'),
  });

  // Form schema with translated validation messages
  return z.object({
    // Optional name field with pattern validation
    name: z.string().regex(NAME_REGEX, { message: errorMessages.name() }).optional(),

    // Permission type
    permissionType: PermissionType,

    // Resource permissions (only used when permissionType is Restricted)
    resourcePermissions: ResourcePermissionsSchema.optional(),

    // Owner type for new keys
    ownerType: z.enum(['You', 'Service account']).optional(),

    // Project selection for new keys
    project: z.string().optional(),
  });
};

// Type export for form values
export type I18nApiKeyFormValues = z.infer<ReturnType<typeof createI18nApiKeySchema>>;

export default createI18nApiKeySchema;
