import React from 'react';
import { TitleProps } from '../title';
import { Zap, Percent } from 'lucide-react';

export const baseTitleProps: TitleProps = {
  title: 'หัวข้อตัวอย่าง',
  viewAllLink: '#',
};

export const withDescriptionProps: TitleProps = {
  ...baseTitleProps,
  title: 'โคมไฟและหลอดไฟ',
  description: 'จำหน่ายโคมไฟและหลอดไฟหลากสไตล์ เพิ่มแสงสว่างและบรรยากาศให้ทุกพื้นที่ ประหยัดพลังงาน คุ้มค่า กันสมัย!',
  suffixIcon: <React.Fragment>💡✨</React.Fragment>,
};

export const withPrefixIconProps: TitleProps = {
  ...baseTitleProps,
  title: 'ระบบไฟฟ้าและความปลอดภัย',
  description: 'จำหน่ายอุปกรณ์ระบบไฟฟ้าและความปลอดภัย ครบวงจร มั่นใจได้ในคุณภาพและมาตรฐานความปลอดภัย พร้อมให้คำแนะนำโดยผู้เชี่ยวชาญ',
  prefixIcon: <Zap className="h-5 w-5 text-yellow-500" />,
  suffixIcon: <React.Fragment>🔧</React.Fragment>,
};

export const withHighlightProps: TitleProps = {
  ...baseTitleProps,
  title: 'คูปองส่วนลด #เก็บแล้วใช้เลย!',
  prefixIcon: <Percent className="h-5 w-5 text-blue-500" />,
  highlightText: '#เก็บแล้วใช้เลย!',
};

export const simpleProps: TitleProps = {
  ...baseTitleProps,
  title: 'รวมเรื่องน่ารู้คู่คนรักบ้าน',
};

export const withoutButtonProps: TitleProps = {
  ...baseTitleProps,
  title: 'หัวข้อไม่มีปุ่ม',
  description: 'ตัวอย่างหัวข้อที่ไม่มีปุ่ม "ดูทั้งหมด"',
  showViewAllButton: false,
};

export const headingH1Props: TitleProps = {
  ...baseTitleProps,
  title: 'หัวข้อระดับ H1',
  description: 'ตัวอย่างหัวข้อที่ใช้ H1',
  as: 'h1',
};

export const headingH3Props: TitleProps = {
  ...baseTitleProps,
  title: 'หัวข้อระดับ H3',
  description: 'ตัวอย่างหัวข้อที่ใช้ H3',
  as: 'h3',
};

export const allFixtures = {
  base: baseTitleProps,
  withDescription: withDescriptionProps,
  withPrefixIcon: withPrefixIconProps,
  withHighlight: withHighlightProps,
  simple: simpleProps,
  withoutButton: withoutButtonProps,
  headingH1: headingH1Props,
  headingH3: headingH3Props,
};