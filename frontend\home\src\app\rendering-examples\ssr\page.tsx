import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import { ContentWrapper } from '@/components/rendering-examples/ContentWrapper';
import Link from 'next/link';

// This function will be executed on the server for each request
async function getServerTimeData() {
  // Artificial delay to simulate data fetching
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    serverTime: new Date().toISOString(),
    randomValue: Math.floor(Math.random() * 1000),
  };
}

// Function to fetch some data for the "with-data" route
async function getDetailedUserData() {
  await new Promise((resolve) => setTimeout(resolve, 800));

  return {
    users: [
      { id: 1, name: '<PERSON>', role: 'Developer', lastActive: new Date().toISOString() },
      { id: 2, name: '<PERSON>', role: 'Designer', lastActive: new Date().toISOString() },
      {
        id: 3,
        name: '<PERSON>',
        role: 'Product Manager',
        lastActive: new Date().toISOString(),
      },
    ],
    systemStatus: {
      cpu: Math.floor(Math.random() * 100) + '%',
      memory: Math.floor(Math.random() * 100) + '%',
      uptime: Math.floor(Math.random() * 10000) + ' minutes',
    },
    timestamp: new Date().toISOString(),
  };
}

// Function to fetch complex data for the "advanced" route
async function getAdvancedAnalytics() {
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return {
    pageViews: {
      today: Math.floor(Math.random() * 10000),
      weekly: Math.floor(Math.random() * 50000),
      monthly: Math.floor(Math.random() * 200000),
    },
    userSessions: {
      averageDuration: Math.floor(Math.random() * 500) + ' seconds',
      bounceRate: Math.floor(Math.random() * 100) + '%',
      newUsers: Math.floor(Math.random() * 5000),
    },
    serverLoad: {
      current: Math.floor(Math.random() * 100) + '%',
      average: Math.floor(Math.random() * 100) + '%',
      peak: Math.floor(Math.random() * 100) + '%',
    },
    generatedAt: new Date().toISOString(),
  };
}

// By default, all pages in the app directory are server components
// The entire page is rendered on the server for each request
export default async function ServerSideRenderedPage() {
  // Data is fetched on each request for each route
  const basicData = await getServerTimeData();
  const detailedData = await getDetailedUserData();
  const advancedData = await getAdvancedAnalytics();

  // Basic SSR content
  const BasicContent = (
    <>
      <h1 className="mb-6 text-3xl font-bold">Server-Side Rendering (SSR)</h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">How SSR Works</h2>
        <p className="mb-4">
          With Server-Side Rendering, the HTML for this page is generated on the server for{' '}
          <strong>each request</strong>. This means the content is always up-to-date, as it&apos;s
          generated when the user requests the page.
        </p>
        <p>
          This is useful for pages where the content changes frequently or needs to be personalized
          for each user. SSR is also great for SEO since search engines can see the fully rendered
          content.
        </p>
      </div>

      <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
        <h2 className="mb-4 text-xl font-semibold">Server-Generated Data</h2>
        <p className="mb-2">This data was generated on the server at request time:</p>

        <div className="bg-card mb-4 rounded border p-4">
          <p>
            <strong>Server Time:</strong> {basicData.serverTime}
          </p>
          <p>
            <strong>Random Value:</strong> {basicData.randomValue}
          </p>
        </div>

        <p className="text-sm">
          Refresh the page to see these values change, confirming that the page is rendered on each
          request.
        </p>
      </div>

      <div className="bg-muted rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">Implementation</h2>
        <p className="mb-4">
          In Next.js 15, SSR is the default for pages in the App Router. Simply export a server
          component that fetches data.
        </p>

        <pre className="bg-card overflow-x-auto rounded-lg p-4 text-sm">
          {`// This is a server component
export default async function Page() {
  // Data fetching happens on the server for each request
  const data = await fetchData();
  
  return (
    <div>
      <h1>Server-Side Rendered Page</h1>
      <p>Data: {data}</p>
    </div>
  );
}`}
        </pre>
      </div>
    </>
  );

  // With Data route content
  const WithDataContent = (
    <>
      <h1 className="mb-6 text-3xl font-bold">SSR with Detailed Data</h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">Dynamic User Data</h2>
        <p className="mb-4">
          This example demonstrates fetching more complex data on the server for each request. Below
          you can see user data that is freshly generated for each page load.
        </p>

        <div className="overflow-x-auto">
          <table className="bg-card w-full overflow-hidden rounded-md border">
            <thead className="bg-muted">
              <tr>
                <th className="px-4 py-2 text-left">ID</th>
                <th className="px-4 py-2 text-left">Name</th>
                <th className="px-4 py-2 text-left">Role</th>
                <th className="px-4 py-2 text-left">Last Active</th>
              </tr>
            </thead>
            <tbody>
              {detailedData.users.map((user) => (
                <tr key={user.id} className="border-t">
                  <td className="px-4 py-2">{user.id}</td>
                  <td className="px-4 py-2">{user.name}</td>
                  <td className="px-4 py-2">{user.role}</td>
                  <td className="px-4 py-2">{new Date(user.lastActive).toLocaleString()}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
        <h2 className="mb-4 text-xl font-semibold">System Status</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="bg-card rounded border p-4">
            <h3 className="mb-2 font-medium">CPU Usage</h3>
            <p className="text-2xl">{detailedData.systemStatus.cpu}</p>
          </div>
          <div className="bg-card rounded border p-4">
            <h3 className="mb-2 font-medium">Memory Usage</h3>
            <p className="text-2xl">{detailedData.systemStatus.memory}</p>
          </div>
          <div className="bg-card rounded border p-4">
            <h3 className="mb-2 font-medium">Uptime</h3>
            <p className="text-2xl">{detailedData.systemStatus.uptime}</p>
          </div>
        </div>
        <p className="mt-4 text-xs">Generated at: {detailedData.timestamp}</p>
      </div>
    </>
  );

  // Advanced route content
  const AdvancedContent = (
    <>
      <h1 className="mb-6 text-3xl font-bold">Advanced SSR Analytics</h1>

      <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
        <h2 className="mb-4 text-xl font-semibold">Real-time Analytics Dashboard</h2>
        <p className="mb-6">
          This advanced example demonstrates how SSR can be used to generate complex, data-heavy
          dashboards with the latest information on every request.
        </p>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="bg-card rounded-lg border p-6">
            <h3 className="mb-4 font-semibold">Page Views</h3>
            <div className="space-y-4">
              <div>
                <div className="mb-1 flex justify-between">
                  <span className="text-sm">Today</span>
                  <span className="text-sm font-medium">
                    {advancedData.pageViews.today.toLocaleString()}
                  </span>
                </div>
                <div className="bg-muted h-2 w-full rounded-full">
                  <div
                    className="h-2 rounded-full bg-blue-500"
                    style={{ width: `${(advancedData.pageViews.today / 10000) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="mb-1 flex justify-between">
                  <span className="text-sm">Weekly</span>
                  <span className="text-sm font-medium">
                    {advancedData.pageViews.weekly.toLocaleString()}
                  </span>
                </div>
                <div className="bg-muted h-2 w-full rounded-full">
                  <div
                    className="h-2 rounded-full bg-green-500"
                    style={{ width: `${(advancedData.pageViews.weekly / 50000) * 100}%` }}
                  ></div>
                </div>
              </div>

              <div>
                <div className="mb-1 flex justify-between">
                  <span className="text-sm">Monthly</span>
                  <span className="text-sm font-medium">
                    {advancedData.pageViews.monthly.toLocaleString()}
                  </span>
                </div>
                <div className="bg-muted h-2 w-full rounded-full">
                  <div
                    className="h-2 rounded-full bg-purple-500"
                    style={{ width: `${(advancedData.pageViews.monthly / 200000) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg border p-6">
            <h3 className="mb-4 font-semibold">User Sessions</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Avg. Duration:</span>
                <span className="font-medium">{advancedData.userSessions.averageDuration}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Bounce Rate:</span>
                <span className="font-medium">{advancedData.userSessions.bounceRate}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>New Users:</span>
                <span className="font-medium">
                  {advancedData.userSessions.newUsers.toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg border p-6">
            <h3 className="mb-4 font-semibold">Server Load</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Current:</span>
                <span className="font-medium">{advancedData.serverLoad.current}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Average:</span>
                <span className="font-medium">{advancedData.serverLoad.average}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Peak:</span>
                <span className="font-medium">{advancedData.serverLoad.peak}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-muted-foreground mt-6 text-xs">
          Data generated at: {advancedData.generatedAt}
        </div>
      </div>

      <div className="bg-muted rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">When to Use Advanced SSR</h2>
        <p className="mb-4">
          This pattern is ideal for dashboards and analytics pages that need to display the most
          up-to-date information for all users. Each visit gets freshly generated content directly
          from the server.
        </p>
        <p>
          SSR is perfect for data that changes frequently or needs to be personalized for each user,
          while still maintaining good SEO and initial load performance.
        </p>
      </div>
    </>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="ssr" />
      <main className="container mx-auto px-4 pb-12">
        <ContentWrapper
          defaultContent={BasicContent}
          withDataContent={WithDataContent}
          advancedContent={AdvancedContent}
          useDirectLinks={true}
        />

        <div className="mt-12 flex justify-center">
          <Link
            href="/rendering-examples/ssr/router-example"
            className="bg-primary hover:bg-primary/90 rounded-md px-6 py-3 text-white transition-colors"
          >
            View Router Example with Theme & Language Support
          </Link>
        </div>
      </main>
    </div>
  );
}
