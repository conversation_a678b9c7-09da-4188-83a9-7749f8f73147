import{r as n}from"./index-Bwql5Dzz.js";var s=Object.defineProperty,a=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,l=(r,t,e)=>t in r?s(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,_=(r,t)=>{for(var e in t)i.call(t,e)&&l(r,e,t[e]);if(a)for(var e of a(t))f.call(t,e)&&l(r,e,t[e]);return r},v=(r,t)=>{var e={};for(var o in r)i.call(r,o)&&t.indexOf(o)<0&&(e[o]=r[o]);if(r!=null&&a)for(var o of a(r))t.indexOf(o)<0&&f.call(r,o)&&(e[o]=r[o]);return e};const m=n.forwardRef((r,t)=>{var e=r,{color:o="currentColor"}=e,p=v(e,["color"]);return n.createElement("svg",_({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),n.createElement("path",{stroke:o,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"m3.036 7.679 2.857 3.571 6.071-7.5"}))});m.displayName="Check";export{m as C};
