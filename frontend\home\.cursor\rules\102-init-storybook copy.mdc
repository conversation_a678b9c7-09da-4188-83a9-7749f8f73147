---
description: 
globs: src/components/**,.storybook/mswHandlers/**,src/lib/**
alwaysApply: false
---
# Ultimate Production-Ready React Component System



## Production-Ready Implementation Patterns

For each component type, implement these patterns to ensure production readiness:

1. **Progressive Enhancement**
   - Base functionality works without JS
   - Enhanced features layered on top
   - Fallbacks for unsupported features

2. **Adaptive Rendering**
   - Server component compatibility
   - Client hydration optimization
   - Streaming friendly architecture

3. **Intelligent Caching**
   - Structured cache key generation
   - Optimistic updates
   - Background revalidation

4. **Mobile-First Implementation**
   - Touch target sizing (min 44x44px)
   - Gesture recognition
   - Battery/performance optimization

5. **Maximum Performance**
   - Code splitting
   - Tree-shakable architecture
   - Virtualization for large datasets
   - Dependency minimization

6. **Framework Agnostic Design**
   - Core logic separated from framework
   - Adapter pattern for multiple frameworks
   - Headless options for custom UI

7. **Accessibility (WCAG) Compliance**
   - Appropriate ARIA attributes
   - Keyboard navigation
   - Screen reader compatibility
   - Focus management

8. **Seamless Theme Support**
   - Light/dark mode consistency
   - Theme tokens integration
   - Color contrast compliance

9. **Internationalization (i18n)**
   - Automatic updating of locale files
   - RTL layout support
   - Translation context providers

10. **Component Documentation**
    - Comprehensive Storybook integration
    - Hyper-rich props documentation
    - Enterprise-grade usage examples
    - Accessibility guidelines compliance

## File Structure for Production Parity (Must Create Folder everytime !!!!)

```
.storybook/mswHandlers/
├── {ComponetName}.handlers.ts      # Production-identical API simulation
├── index.ts      # Export point
│
src/components/{ComponetNameFolder}/
├── {ComponetName}.tsx              # Production-ready component
├── {ComponetName}.ts               # Additional functionality
├── {ComponetName}-Types.ts         # Types matching production API
├── {ComponetName}-Utils.ts         # Utility functions
├── {ComponetName}-Variants.ts      # CVA variants definition
├── {ComponetName}-Animations.ts    # Framer motion variants
├── index.ts                        # Export point
├── __fixtures__/                   # Production-identical mock data
│   └── {ComponetName}.mockData.ts  # Mirrors production API responses exactly
├── __tests__/                      # Production parity tests
│   └── {ComponetName}.test.tsx     # Validates identical behavior
└── __stories__/                    # Production-like demo stories
    └── {ComponetName}.stories.tsx  # Shows production-identical behavior with comprehensive props documentation
```

## Implementation Checklist for Production Parity

- [ ] API interfaces match production exactly (headers, query params, auth)
- [ ] Response schemas match production exactly (data structure, errors, pagination)
- [ ] Callback signatures match production API events exactly
- [ ] Error handling mimics production environment exactly
- [ ] Loading states match production timing and behavior
- [ ] Component renders identically to how it would with production data
- [ ] MSW handlers replicate all production API behaviors
- [ ] Test coverage validates all production scenarios
- [ ] Network behavior (caching, invalidation) matches production
- [ ] ShadCN UI components from src/components/ui integrated correctly
- [ ] Tailwind 4.0.0 classes with container queries implemented
- [ ] CVA variants defined and used consistently
- [ ] Localization files updated in all supported languages
- [ ] TanStack Router implemented for navigation
- [ ] React Query used for all data fetching
- [ ] Framer Motion animations integrated
- [ ] Lucide icons used exclusively
- [ ] WCAG compliance verified and tested
- [ ] Dark/light mode support with user preference detection
- [ ] Skeleton loading states defined for all data-dependent components
- [ ] Error boundaries configured properly with fallback UI
- [ ] Component local state management working correctly
- [ ] Responsive designs working on all device sizes ('3xs', '2xs', 'xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl', '7xl')
- [ ] Storybook documentation complete with enterprise-grade examples

## Enterprise Production-Readiness Checklist

- [ ] **API & Data Handling**
  - [ ] API interfaces match production exactly (headers, query params, auth)
  - [ ] Response schemas match production exactly (data structure, errors, pagination)
  - [ ] Callback signatures match production API events exactly
  - [ ] Error handling mimics production environment exactly
  - [ ] Edge cases and rare conditions properly handled
  - [ ] Data transformation logic matches production exactly
  - [ ] Rate limiting and retry logic implemented

- [ ] **Mock Service Worker Integration**
  - [ ] MSW handlers replicate all production API behaviors with exact simulation
  - [ ] MSW configuration can be persisted across development sessions
  - [ ] Mock latency can be adjusted to match realistic network conditions
  - [ ] Mock error rates configurable for resilience testing
  - [ ] Mock response transformers match business logic exactly
  - [ ] Bypass cache options available for realistic testing
  - [ ] MSW handlers mirror all production edge cases

- [ ] **State Management**
  - [ ] Component state properly isolated from global application state
  - [ ] State persistence works correctly with proper cleanup
  - [ ] State adapters function with all supported state management libraries
  - [ ] State propagation controlled and predictable
  - [ ] Memory leaks prevented with proper cleanup
  - [ ] Complex state transitions tested and validated

- [ ] **Performance & Optimization**
  - [ ] Loading states match production timing and behavior
  - [ ] Component renders identically to how it would with production data
  - [ ] Network behavior (caching, invalidation) matches production exactly
  - [ ] Performance benchmarks defined and measured
  - [ ] Bundle size impact analyzed and optimized
  - [ ] Code splitting implemented for large component trees
  - [ ] Virtual rendering optimized for large datasets

- [ ] **UI Implementation**
  - [ ] ShadCN UI components from src/components/ui integrated correctly
  - [ ] Tailwind 4.0.0 classes with container queries implemented
  - [ ] CVA variants defined and used consistently
  - [ ] Animation performance optimized for lower-end devices
  - [ ] Print styles defined for relevant components
  - [ ] Custom component styling matches design system exactly
  - [ ] Vector graphics and icons optimized for performance

- [ ] **Internationalization & Accessibility**
  - [ ] Localization files updated in all supported languages (including RTL)
  - [ ] Text expansion/contraction handled for all languages
  - [ ] WCAG compliance (AA level) verified and tested
  - [ ] Keyboard navigation fully implemented and tested
  - [ ] Screen reader compatibility verified
  - [ ] Focus management handles all interaction paths
  - [ ] Color contrast ratios compliant with accessibility standards

- [ ] **Routing & Data Fetching**
  - [ ] TanStack Router implemented for navigation
  - [ ] React Query used for all data fetching with proper stale time configuration
  - [ ] Suspense boundaries placed strategically
  - [ ] Data prefetching implemented for optimal UX
  - [ ] Router transition animations smooth and consistent
  - [ ] Deep linking supported for all relevant states

- [ ] **Error Handling & Resilience**
  - [ ] Error boundaries configured properly with fallback UI
  - [ ] Error reporting telemetry integrated
  - [ ] Automatic retry mechanisms for transient failures
  - [ ] Graceful degradation paths for all critical features
  - [ ] Service unavailability handled elegantly
  - [ ] User recovery paths from error states clear and efficient

- [ ] **Responsive Design**
  - [ ] Responsive designs working on all device sizes ('3xs' through '7xl')
  - [ ] Touch targets sized appropriately (minimum 44×44px)
  - [ ] Interaction patterns appropriate for touch/mouse/keyboard
  - [ ] Portrait/landscape orientation changes handled gracefully
  - [ ] Content reflow tested on all breakpoints
  - [ ] Viewport meta tags configured correctly

- [ ] **Security & Compliance**
  - [ ] Security reviewed for client-side vulnerabilities
  - [ ] Sensitive data handling follows company policy
  - [ ] Input validation implemented for all user inputs
  - [ ] XSS prevention measures in place
  - [ ] Authorization checks implemented correctly
  - [ ] Compliance requirements documented and satisfied

- [ ] **Testing & Documentation**
  - [ ] Test coverage validates all production scenarios (>90% coverage)
  - [ ] Integration tests verify component in actual application context
  - [ ] Performance tests baseline established
  - [ ] Visual regression tests implemented
  - [ ] Storybook documentation complete with enterprise-grade examples
  - [ ] Technical documentation includes integration guidance

- [ ] **DevOps & Deployment**
  - [ ] CI/CD pipeline validates component integrity
  - [ ] Version compatibility documented and tested
  - [ ] Bundle analysis performed
  - [ ] Deployment rollback strategy defined
  - [ ] Feature flags implemented for staged rollout
  - [ ] Monitoring hooks added for production telemetry

## Core Architecture Framework

This system provides industrial-strength component architectures with:
- Full optimization for all display components
- Virtualized scrolling integration with `@tanstack/virtual`
- Touch-optimized mobile experience with precise breakpoint support
- Deep integration with `@tanstack/router` and `@tanstack/react-query`
- Support for 10+ component display patterns
- Immutable shadcn/ui primitives from `src/components/ui` (Radix UI wrappers)
- Tailwind CSS 4.0.0 styling with modern container queries
- Responsive styling with `class-variance-authority` for variant management
- Animation integration with `framer-motion`
- Standardized icons from `lucide-react`
- WCAG compliance and best UI/UX practices
- Dark/light mode support with persistent theming
- Skeleton loading with `react-content-loader`

## Component Base Architecture

Every component follows this enhanced architecture pattern:

```typescript
// {ComponetName}-Types.ts - Universal interface with specialized extensions
export interface BaseComponentProps<TData = any> {
  // Core configuration
  endpoint?: string;                      // API endpoint URL
  apiKey?: string;                        // Optional API authentication
  fetchOptions?: RequestInit;             // Fetch API options
  
  // React Query integration
  queryKey?: QueryKey;                    // Custom query key
  queryOptions?: Omit<UseQueryOptions<TData[]>, 'queryKey' | 'queryFn'>;
  transformResponse?: (data: any) => TData[];  // Custom data transformer
  initialData?: TData[];                  // Pre-loaded data
  
  // Router integration
  routeParams?: Record<string, string>;   // Route parameters
  navigateOnSelect?: boolean;             // Navigate on selection
  routeBuilder?: (item: TData) => string; // Route builder function
  
  // Performance optimization
  virtualizedOptions?: {                  // Tanstack Virtual configuration
    estimateSize?: (index: number) => number;
    overscan?: number;
    getScrollElement?: () => HTMLElement | null;
    horizontal?: boolean;
  };
  lazyLoadThreshold?: number;             // Intersection observer threshold
  cacheStrategy?: 'memory' | 'session' | 'persistent';
  
  // Local state management
  defaultState?: Record<string, any>;     // Initial component state
  stateReducer?: (state: any, action: any) => any; // Custom state reducer
  persistState?: boolean;                 // Persist state in localStorage/sessionStorage
  stateKey?: string;                      // Key for persistent state
  isolateState?: boolean;                 // Keep state isolated from global state
  stateScope?: 'component' | 'route' | 'shared'; // State scope for isolation boundaries
  stateAdapter?: StateAdapter<any>;       // Adapter for custom state management solutions
  
  // Global state integration (optional)
  globalStateSelector?: (state: any) => any; // Selector for global state (Redux/Zustand)
  globalStateDispatch?: (action: any) => void; // Global state dispatch function
  globalStateOptions?: {                 // Options for global state integration
    mapStateToProps?: (state: any) => any; // Map global state to props
    mapDispatchToProps?: (dispatch: any) => any; // Map dispatch to props
    mergeProps?: (stateProps: any, dispatchProps: any, ownProps: any) => any; // Merge props
    areStatesEqual?: (nextState: any, prevState: any) => boolean; // Custom state comparison
  };
  
  // Mobile optimization
  touchMode?: boolean;                    // Enable touch-optimized mode
  swipeActions?: SwipeActionConfig[];     // Swipe gesture actions
  tapActions?: TapActionConfig[];         // Tap gesture actions
  vibrationFeedback?: boolean;            // Haptic feedback on interactions
  
  // Responsive design breakpoints (Tailwind CSS container queries)
  responsiveVariants?: {                     // Device-specific variants
    '3xs'?: Record<string, any>;             // 16rem (256px) - Tiny displays
    '2xs'?: Record<string, any>;             // 18rem (288px) - Extra small displays
    'xs'?: Record<string, any>;              // 20rem (320px) - Small phone
    'sm'?: Record<string, any>;              // 24rem (384px) - Phone
    'md'?: Record<string, any>;              // 28rem (448px) - Large phone
    'lg'?: Record<string, any>;              // 32rem (512px) - Small tablet
    'xl'?: Record<string, any>;              // 36rem (576px) - Tablet
    '2xl'?: Record<string, any>;             // 42rem (672px) - Large tablet
    '3xl'?: Record<string, any>;             // 48rem (768px) - Small laptop
    '4xl'?: Record<string, any>;             // 56rem (896px) - Laptop
    '5xl'?: Record<string, any>;             // 64rem (1024px) - Desktop
    '6xl'?: Record<string, any>;             // 72rem (1152px) - Large desktop
    '7xl'?: Record<string, any>;             // 80rem (1280px) - Extra large desktop
  };
  hideOnBreakpoint?: ('3xs' | '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl')[];  // Hide on specific breakpoints
  
  // Callbacks
  onDataFetch?: (data: TData[]) => void;
  onError?: (error: Error) => void;
  onLoadingChange?: (isLoading: boolean) => void;
  onItemSelect?: (item: TData) => void;
  onItemAction?: (item: TData, action: string) => void;
  onVisibilityChange?: (items: TData[]) => void;
  onStateChange?: (newState: any, oldState: any) => void;
  
  // Presentation
  className?: string;                     // Container class
  variant?: {VARIANT_OPTIONS};            // Visual variant
  size?: {SIZE_OPTIONS};                  // Size variant
  density?: 'compact' | 'normal' | 'relaxed'; // Content density
  
  // Internationalization
  i18nNamespace?: string;                 // Translation namespace
  i18nPrefix?: string;                    // Translation prefix
  
  // Theme support
  colorMode?: 'light' | 'dark' | 'system'; // Color mode preference
  
  // Styling with class-variance-authority
  cvaVariants?: Record<string, string>;   // CVA variant config
  tailwindClasses?: string;               // Raw tailwind classes for custom styling
  containerQuery?: boolean;               // Use container queries for local responsive styling
  
  // Animation configuration
  motionVariants?: MotionVariants;        // Framer motion variants
  animationPreset?: 'fade' | 'slide' | 'scale' | 'custom';
  
  // Error handling & boundary integration
  errorBoundary?: boolean;                // Enable error boundary
  errorFallback?: React.ReactNode;        // Fallback UI when error occurs
  onErrorCapture?: (error: Error, info: React.ErrorInfo) => void;
  retryOnError?: boolean;                 // Attempt retry on error
  
  // Accessibility
  ariaLabel?: string;                     // Accessibility label
  ariaLabelledBy?: string;                // Reference to label element
  ariaDescribedBy?: string;               // Reference to description element
  
  // Loading states
  skeletonProps?: SkeletonLoaderProps;    // Props for react-content-loader
  loadingStrategy?: 'skeleton' | 'spinner' | 'progressive' | 'none';
  
  // Testing/development flags
  simulateCondition?: string;             // MSW simulation condition
  testId?: string;                        // For test selection

  // API configuration (MSW-compatible)
  endpoint?: string;                      // API endpoint URL
  apiKey?: string;                        // Optional API authentication
  fetchOptions?: RequestInit;             // Fetch API options
  mswOptions?: {                          // Mock Service Worker options
    mockEndpoint?: string;                // Override endpoint for MSW in dev/test env
    simulateLatency?: number;             // Simulate network latency in ms
    mockResponseTransformer?: (data: any) => any; // Transform mock response data
    mockResponseStatus?: number;          // Mock response status code
    mockErrorRate?: number;               // Error rate (0-1) for simulating failures
    persistMockConfig?: boolean;          // Persist mock configuration
    bypassCache?: boolean;                // Bypass browser cache for mock requests
    mockHeaders?: Record<string, string>; // Mock response headers
  };
  mockDataProvider?: () => Promise<any>;  // Custom mock data provider function
  mockNetworkCondition?: 'fast' | 'slow' | 'unreliable' | 'offline'; // Simulate network conditions
}
```

## Specialized Display Component Architectures

### 1. Data Table Component Architecture

```typescript
export interface TableComponentProps<TData = any> extends BaseComponentProps<TData> {
  // Table-specific features
  columns: TableColumn<TData>[];
  sortable?: boolean | string[];
  filterable?: boolean | string[];
  pagination?: {
    enabled?: boolean;
    pageSize?: number;
    pageSizeOptions?: number[];
    serverSide?: boolean;
  };
  rowSelection?: 'single' | 'multiple' | 'none';
  expandableRows?: boolean;
  expandRenderer?: (item: TData) => React.ReactNode;
  stickyHeader?: boolean;
  stickyColumns?: number;
  resizableColumns?: boolean;
  exportOptions?: ('csv' | 'excel' | 'pdf')[];
  
  // Virtual scrolling integration
  virtualized?: boolean;
  rowHeight?: number | ((index: number) => number);
  
  // Responsive behavior
  responsiveTable?: boolean;
  responsiveBreakpoints?: {
    '3xs'?: TableResponsiveConfig;  // Tiny displays (16rem - 256px) 
    '2xs'?: TableResponsiveConfig;  // Extra small displays (18rem - 288px)
    'xs'?: TableResponsiveConfig;   // Small phone (20rem - 320px)
    'sm'?: TableResponsiveConfig;   // Phone (24rem - 384px)
    'md'?: TableResponsiveConfig;   // Large phone (28rem - 448px)
    'lg'?: TableResponsiveConfig;   // Small tablet (32rem - 512px)
    'xl'?: TableResponsiveConfig;   // Tablet (36rem - 576px)
    '2xl'?: TableResponsiveConfig;  // Large tablet (42rem - 672px)
    '3xl'?: TableResponsiveConfig;  // Small laptop (48rem - 768px)
    '4xl'?: TableResponsiveConfig;  // Laptop (56rem - 896px)
    '5xl'?: TableResponsiveConfig;  // Desktop (64rem - 1024px)
    '6xl'?: TableResponsiveConfig;  // Large desktop (72rem - 1152px)
    '7xl'?: TableResponsiveConfig;  // Extra large desktop (80rem - 1280px)
  };
  mobileCardMode?: boolean;
  responsiveColumnPriorities?: Record<string, number>;
  
  // ShadCN integration
  shadcnVariant?: 'default' | 'bordered' | 'custom';
}
```

### 2. List/Grid Component Architecture

```typescript
export interface ListGridComponentProps<TData = any> extends BaseComponentProps<TData> {
  // List/Grid features
  layout?: 'list' | 'grid' | 'masonry' | 'auto';
  itemRenderer?: (item: TData) => React.ReactNode;
  gridColumns?: number | { 
    '3xs'?: number;  // Tiny displays (≥256px)
    '2xs'?: number;  // Extra small displays (≥288px)
    'xs'?: number;   // Small phone (≥320px)
    'sm'?: number;   // Phone (≥384px)
    'md'?: number;   // Large phone (≥448px)
    'lg'?: number;   // Small tablet (≥512px)
    'xl'?: number;   // Tablet (≥576px)
    '2xl'?: number;  // Large tablet (≥672px)
    '3xl'?: number;  // Small laptop (≥768px)
    '4xl'?: number;  // Laptop (≥896px)
    '5xl'?: number;  // Desktop (≥1024px)
    '6xl'?: number;  // Large desktop (≥1152px)
    '7xl'?: number;  // Extra large desktop (≥1280px)
  };
  gap?: number | string | {
    '3xs'?: number | string;  // Tiny displays
    '2xs'?: number | string;  // Extra small displays
    'xs'?: number | string;   // Small phone
    'sm'?: number | string;   // Phone
    'md'?: number | string;   // Large phone
    'lg'?: number | string;   // Small tablet
    'xl'?: number | string;   // Tablet
    '2xl'?: number | string;  // Large tablet
    '3xl'?: number | string;  // Small laptop
    '4xl'?: number | string;  // Laptop
    '5xl'?: number | string;  // Desktop
    '6xl'?: number | string;  // Large desktop
    '7xl'?: number | string;  // Extra large desktop
  };
  itemAspectRatio?: number;
  infiniteScroll?: boolean;
  loadMoreThreshold?: number;
  
  // Virtual scrolling optimization
  virtualized?: boolean;
  itemSize?: number | ((index: number) => number);
  
  // Mobile-specific behavior
  swipeToDelete?: boolean;
  pullToRefresh?: boolean;
  
  // Animation with framer-motion
  itemAnimationVariants?: MotionVariants;
  staggerChildren?: number;
  
  // ShadCN integration
  shadcnCardVariant?: 'default' | 'destructive' | 'custom';
}
```

### 3. Card Component Architecture

```typescript
export interface CardComponentProps extends BaseComponentProps {
  // Card display options
  cardLayout?: 'horizontal' | 'vertical' | 'overlay';
  headerRenderer?: (item: {DATA_TYPE}) => React.ReactNode;
  bodyRenderer?: (item: {DATA_TYPE}) => React.ReactNode;
  footerRenderer?: (item: {DATA_TYPE}) => React.ReactNode;
  mediaPosition?: 'top' | 'left' | 'right' | 'bottom' | 'background';
  mediaAspectRatio?: number;
  hoverEffect?: 'lift' | 'glow' | 'border' | 'zoom' | 'none';
  actionButtons?: CardAction[];
  selectable?: boolean;
  
  // Mobile optimization
  stackOnMobile?: boolean;
  touchGestures?: {
    swipeLeft?: CardAction;
    swipeRight?: CardAction;
    longPress?: CardAction;
  };
  
  // Theming
  accentColor?: string;
  
  // Animation with framer-motion
  hoverAnimationVariants?: MotionVariants;
  transitionProps?: MotionProps;
  
  // ShadCN integration
  shadcnCardProps?: ShadcnCardProps;
  shadcnVariant?: 'default' | 'bordered' | 'custom';
}
```

### 4. Dashboard Component Architecture

```typescript
export interface DashboardComponentProps extends BaseComponentProps {
  // Dashboard features
  layout?: 'fixed' | 'responsive' | 'grid';
  sections?: DashboardSection[];
  refreshInterval?: number;
  widgets?: DashboardWidget[];
  draggable?: boolean;
  resizable?: boolean;
  collapsible?: boolean;
  
  // KPI display
  kpiRenderers?: Record<string, (value: any, trend: any) => React.ReactNode>;
  kpiColorMapping?: Record<string, (value: any) => string>;
  
  // Device optimization
  breakpointLayouts?: Record<string, DashboardLayout>;
  autoAdjustForScreenSize?: boolean;
  
  // Animation with framer-motion
  widgetAnimationVariants?: MotionVariants;
  entranceAnimation?: 'staggered' | 'sequential' | 'parallel' | 'none';
  
  // ShadCN integration
  shadcnCardProps?: ShadcnCardProps;
  shadcnButtonProps?: ShadcnButtonProps;
}
```

### 5. Detail View Component Architecture

```typescript
export interface DetailViewComponentProps extends BaseComponentProps {
  // Detail view features
  sections?: DetailSection[];
  layout?: 'single' | 'tabs' | 'accordion' | 'wizard';
  relatedData?: RelatedDataConfig[];
  actionsPosition?: 'top' | 'bottom' | 'floating' | 'sticky';
  historyTracking?: boolean;
  editMode?: boolean;
  editModeControls?: boolean;
  
  // Integration hooks
  onEdit?: (field: string, value: any, item: {DATA_TYPE}) => void;
  onSave?: (updatedItem: {DATA_TYPE}) => Promise<void>;
  onCancel?: () => void;
  
  // Mobile optimization
  mobileLayout?: 'stacked' | 'swipeable' | 'modal';
  
  // Animation with framer-motion
  sectionTransitions?: MotionVariants;
  tabSwitchAnimation?: MotionVariants;
  
  // ShadCN integration
  shadcnCardProps?: ShadcnCardProps;
  shadcnButtonProps?: ShadcnButtonProps;
  shadcnFormProps?: ShadcnFormProps;
}
```

### 6. Media Display Component Architecture

```typescript
export interface MediaDisplayComponentProps extends BaseComponentProps {
  // Media display features
  mediaType?: 'image' | 'video' | 'audio' | 'mixed';
  displayMode?: 'gallery' | 'slider' | 'fullscreen' | 'carousel' | 'masonry';
  thumbnailsPosition?: 'none' | 'bottom' | 'left' | 'right' | 'top';
  lightboxEnabled?: boolean;
  zoomEnabled?: boolean;
  controls?: ('play' | 'pause' | 'volume' | 'seek' | 'fullscreen' | 'download' | 'share')[];
  autoplay?: boolean;
  loop?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
  
  // Image optimization
  imageSizes?: string;
  imageSrcSet?: Record<number, string>;
  lazyLoad?: boolean;
  
  // Touch optimization
  pinchToZoom?: boolean;
  swipeToNavigate?: boolean;
  doubleTapToZoom?: boolean;
  
  // Animation with framer-motion
  carouselAnimationVariants?: MotionVariants;
  transitionProps?: MotionProps;
  
  // ShadCN integration
  shadcnCarouselProps?: ShadcnCarouselProps;
  shadcnAspectRatioProps?: ShadcnAspectRatioProps;
}
```

### 7. Chart/Graph Component Architecture

```typescript
export interface ChartComponentProps extends BaseComponentProps {
  // Chart configuration
  chartType?: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'radar' | 'heatmap' | 'bubble';
  xAxis?: ChartAxisConfig;
  yAxis?: ChartAxisConfig;
  series?: ChartSeries[];
  stacked?: boolean;
  animated?: boolean;
  palette?: string[] | Record<string, string>;
  legend?: {
    position?: 'top' | 'right' | 'bottom' | 'left';
    enabled?: boolean;
  };
  tooltip?: {
    enabled?: boolean;
    custom?: (value: any) => React.ReactNode;
  };
  
  // Interactions
  zoomable?: boolean;
  clickable?: boolean;
  brushable?: boolean;
  onChartClick?: (point: any, event: React.MouseEvent) => void;
  
  // Responsive behavior
  responsiveRules?: ChartResponsiveRule[];
  maintainAspectRatio?: boolean;
  aspectRatio?: number;
  
  // Animation with framer-motion
  animationVariants?: MotionVariants;
  animationDuration?: number;
  
  // ShadCN integration
  shadcnCardProps?: ShadcnCardProps;
  shadcnTooltipProps?: ShadcnTooltipProps;
}
```

### 8. Status Indicator Component Architecture

```typescript
export interface StatusIndicatorComponentProps extends BaseComponentProps {
  // Status features
  statusType?: 'loading' | 'error' | 'empty' | 'success' | 'warning' | 'info';
  animation?: 'pulse' | 'spin' | 'bounce' | 'fade' | 'none';
  icon?: React.ReactNode | string;
  message?: string | React.ReactNode;
  secondaryMessage?: string | React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  timeout?: number;
  progress?: number;
  autoHide?: boolean;
  
  // Layout
  overlay?: boolean;
  position?: 'inline' | 'fixed' | 'absolute' | 'toast';
  dismissible?: boolean;
  
  // Animation with framer-motion
  motionVariants?: MotionVariants;
  transitionProps?: MotionProps;
  
  // ShadCN integration
  shadcnAlertProps?: ShadcnAlertProps;
  shadcnToastProps?: ShadcnToastProps;
  
  // Icon configuration
  lucideIconName?: string;   // Use lucide-react icons
  lucideIconProps?: LucideIconProps;
}
```

### 9. Timeline Component Architecture

```typescript
export interface TimelineComponentProps extends BaseComponentProps {
  // Timeline features
  timelineLayout?: 'vertical' | 'horizontal' | 'alternating';
  itemAlignment?: 'start' | 'center' | 'end';
  connector?: React.ReactNode | boolean;
  groupBy?: string | ((item: {DATA_TYPE}) => string);
  sortBy?: string | ((a: {DATA_TYPE}, b: {DATA_TYPE}) => number);
  dateFormat?: string | ((date: string | Date) => string);
  itemRenderer?: (item: {DATA_TYPE}) => React.ReactNode;
  
  // Interactive features
  expandableItems?: boolean;
  filterOptions?: TimelineFilter[];
  highlightCurrent?: boolean;
  
  // Virtual scrolling
  virtualized?: boolean;
  dynamicItemHeight?: boolean;
  
  // Mobile optimization
  collapsedOnMobile?: boolean;
  mobileTimelineView?: 'compact' | 'full' | 'cards';
  
  // Animation with framer-motion
  itemAnimationVariants?: MotionVariants;
  connectorAnimationVariants?: MotionVariants;
  
  // ShadCN integration
  shadcnCardProps?: ShadcnCardProps;
  shadcnButtonProps?: ShadcnButtonProps;
  
  // Icon configuration
  lucideIconName?: string;   // Use lucide-react icons
  lucideIconProps?: LucideIconProps;
}
```

### 10. Map Component Architecture

```typescript
export interface MapComponentProps extends BaseComponentProps {
  // Map configuration
  mapProvider?: 'leaflet' | 'google' | 'mapbox' | 'openstreetmap';
  initialView?: {
    center: [number, number];
    zoom: number;
  };
  markers?: MapMarker[] | ((data: {DATA_TYPE}[]) => MapMarker[]);
  polygons?: MapPolygon[];
  heatmap?: boolean;
  clustering?: boolean;
  clusterRadius?: number;
  
  // Interactions
  interactive?: boolean;
  draggable?: boolean;
  zoomable?: boolean;
  onMarkerClick?: (marker: MapMarker, event: any) => void;
  onViewportChange?: (viewport: any) => void;
  
  // Custom rendering
  markerRenderer?: (marker: MapMarker) => React.ReactNode;
  popupRenderer?: (data: {DATA_TYPE}) => React.ReactNode;
  
  // Mobile optimization
  mobileControls?: ('zoom' | 'rotate' | 'locate' | 'fullscreen')[];
  gestureHandling?: 'cooperative' | 'greedy' | 'auto' | 'none';
  
  // Animation with framer-motion
  markerAnimationVariants?: MotionVariants;
  popupAnimationVariants?: MotionVariants;
  
  // ShadCN integration
  shadcnCardProps?: ShadcnCardProps;
  shadcnButtonProps?: ShadcnButtonProps;
  
  // Icon configuration
  lucideIconName?: string;   // Use lucide-react icons for markers
  lucideIconProps?: LucideIconProps;
}
```

## Advanced Optimization Techniques

### Performance Optimization Blueprint

```typescript
// Component optimization implementation
export const {ComponetName} = React.memo<{ComponetName}Props<{DATA_TYPE}>>(({
  // All props destructured here
  defaultState,
  stateReducer,
  persistState,
  stateKey,
  isolateState,
  stateScope,
  stateAdapter,
  globalStateSelector,
  globalStateDispatch,
  globalStateOptions,
  errorBoundary,
  errorFallback,
  onErrorCapture,
  retryOnError,
  responsiveVariants,
  hideOnBreakpoint,
  tailwindClasses,
  containerQuery,
  endpoint,
  mswOptions,
  mockDataProvider,
  mockNetworkCondition,
  // Other props
}) => {
  // Import immutable shadcn components from src/components/ui
  const { 
    Button, 
    Card, 
    CardContent, 
    CardHeader, 
    CardFooter,
    Skeleton,
    Tooltip,
    DropdownMenu,
    Tabs
  } = useShadcnComponents();
  
  // Local state management with useReducer (isolated from global state)
  const [localState, localDispatch] = useReducer(
    stateReducer || ((s, a) => ({ ...s, ...a })),
    defaultState || {}
  );
  
  // Global state integration (optional)
  const globalState = isolateState !== true && globalStateSelector 
    ? globalStateSelector(useStore()) 
    : {};
    
  // Merged state based on isolation preferences
  const state = useMemo(() => {
    // If state is isolated, only use local state
    if (isolateState === true) {
      return localState;
    }
    
    // If using state adapter, delegate to adapter
    if (stateAdapter) {
      return stateAdapter.getState(localState, globalState);
    }
    
    // Based on scope, merge differently
    switch (stateScope) {
      case 'component':
        return localState;
      case 'route':
        return { ...localState, routeParams: globalState?.routeParams };
      case 'shared':
      default:
        return { ...globalState, ...localState };
    }
  }, [localState, globalState, isolateState, stateScope, stateAdapter]);

  // Enhanced dispatch function with isolation control
  const dispatch = useCallback((action) => {
    // Always update local state
    localDispatch(action);
    
    // If not isolated and global dispatch available, propagate to global
    if (!isolateState && globalStateDispatch && action.propagateToGlobal !== false) {
      globalStateDispatch({
        ...action,
        source: 'component',
        componentId: stateKey || id
      });
    }
    
    // If using adapter, let adapter handle
    if (stateAdapter) {
      stateAdapter.dispatch(action, localDispatch, globalStateDispatch);
    }
  }, [isolateState, globalStateDispatch, stateKey, id, stateAdapter]);
  
  // Persist state if enabled
  useEffect(() => {
    if (persistState && stateKey) {
      const savedState = localStorage.getItem(`component_state_${stateKey}`);
      if (savedState) {
        try {
          localDispatch({ 
            type: 'INIT_FROM_STORAGE', 
            payload: JSON.parse(savedState),
            propagateToGlobal: false // Don't propagate storage initialization
          });
        } catch (e) {
          console.error('Failed to parse saved state', e);
        }
      }
    }
  }, [persistState, stateKey]);
  
  // Save state changes
  useEffect(() => {
    if (persistState && stateKey && Object.keys(localState).length > 0) {
      localStorage.setItem(`component_state_${stateKey}`, JSON.stringify(localState));
    }
    
    // Clean up on unmount
    return () => {
      if (persistState && stateKey && state.persistOnlyInSession !== true) {
        // Keep it in localStorage
      } else if (persistState && stateKey) {
        localStorage.removeItem(`component_state_${stateKey}`);
      }
    };
  }, [localState, persistState, stateKey]);

  // Mock service worker integration for development and testing
  const effectiveEndpoint = useMemo(() => {
    // For development and testing environments only
    if (process.env.NODE_ENV !== 'production' && mswOptions?.mockEndpoint) {
      return mswOptions.mockEndpoint;
    }
    return endpoint;
  }, [endpoint, mswOptions]);
  
  // API request with MSW support
  const fetchData = useCallback(async () => {
    // Skip if no endpoint
    if (!effectiveEndpoint) return null;
    
    // If mock data provider exists and not in production, use it
    if (process.env.NODE_ENV !== 'production' && mockDataProvider) {
      try {
        // Simulate network conditions if specified
        if (mockNetworkCondition) {
          const delay = mockNetworkCondition === 'fast' ? 100 : 
                      mockNetworkCondition === 'slow' ? 2000 : 
                      mockNetworkCondition === 'unreliable' ? Math.random() * 5000 : 0;
                      
          if (mockNetworkCondition === 'offline') {
            throw new Error('Network offline');
          }
          
          if (delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
        
        // Apply custom latency if specified
        if (mswOptions?.simulateLatency) {
          await new Promise(resolve => setTimeout(resolve, mswOptions.simulateLatency));
        }
        
        // Apply error rate if specified
        if (mswOptions?.mockErrorRate && Math.random() < mswOptions.mockErrorRate) {
          throw new Error('Simulated fetch error based on mockErrorRate');
        }
        
        const data = await mockDataProvider();
        
        // Transform mock response if transformer specified
        return mswOptions?.mockResponseTransformer ? 
               mswOptions.mockResponseTransformer(data) : data;
      } catch (error) {
        // Handle mock errors
        console.error('Mock data provider error:', error);
        throw error;
      }
    }
    
    // Real API call
    const response = await fetch(effectiveEndpoint, {
      ...fetchOptions,
      headers: {
        ...fetchOptions?.headers,
        ...(apiKey ? { 'Authorization': `Bearer ${apiKey}` } : {})
      }
    });
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }
    
    return response.json();
  }, [effectiveEndpoint, fetchOptions, apiKey, mockDataProvider, mockNetworkCondition, mswOptions]);
  
  // Memoize complex calculations
  const processedData = useMemo(() => {
    // Only recompute when dependencies change
    return data?.map(transformFunction);
  }, [data, transformFunction]);
  
  // Responsive breakpoint detection (using Tailwind CSS 4.0.0 container queries)
  const { width } = useWindowSize();
  const breakpoint = useMemo(() => {
    if (width < 256) return '3xs';      // Tiny displays - 16rem
    if (width < 288) return '2xs';      // Extra-small displays - 18rem
    if (width < 320) return 'xs';       // Small phone - 20rem
    if (width < 384) return 'sm';       // Phone - 24rem
    if (width < 448) return 'md';       // Large phone - 28rem 
    if (width < 512) return 'lg';       // Small tablet - 32rem
    if (width < 576) return 'xl';       // Tablet - 36rem
    if (width < 672) return '2xl';      // Large tablet - 42rem
    if (width < 768) return '3xl';      // Small laptop - 48rem
    if (width < 896) return '4xl';      // Laptop - 56rem
    if (width < 1024) return '5xl';     // Desktop - 64rem
    if (width < 1152) return '6xl';     // Large desktop - 72rem
    return '7xl';                       // Extra large desktop - 80rem+
  }, [width]);
  
  // Apply responsive variants based on current breakpoint
  const responsiveProps = useMemo(() => {
    if (!responsiveVariants) return {};
    return responsiveVariants[breakpoint] || {};
  }, [responsiveVariants, breakpoint]);
  
  // Skip rendering on hidden breakpoints
  if (hideOnBreakpoint?.includes(breakpoint)) {
    return null;
  }
  
  // Optimized event handlers with proper typing
  const handleEvent = useCallback((param: any) => {
    // Update local state only, with propagation control
    dispatch({ 
      type: 'UPDATE', 
      payload: param,
      propagateToGlobal: !isolateState // Propagate only if not isolated
    });
  }, [dispatch, isolateState]);
  
  // Virtualization integration with TanStack Virtual
  const virtualizer = useVirtualizer({
    count: data?.length || 0,
    getScrollElement: () => scrollElementRef.current,
    estimateSize: useCallback((index) => {
      // Dynamic size calculation based on content
      return itemSizeEstimator(data[index]);
    }, [data, itemSizeEstimator]),
    overscan: breakpoint === '3xs' || breakpoint === '2xs' || breakpoint === 'xs' ? 1 : 
              breakpoint === 'sm' || breakpoint === 'md' ? 2 : 5,
  });
  
  // Dark/light mode integration
  const { theme, setTheme } = useTheme();
  
  // CVA styles integration with Tailwind 4.0.0
  const containerStyles = cva([
    'base-container-class',
    'responsive-class',
    tailwindClasses, // Custom Tailwind 4.0.0 classes
    containerQuery && '@container' // Tailwind 4.0.0 container query support
  ], {
    variants: {
      size: {
        sm: 'small-container',
        md: 'medium-container',
        lg: 'large-container'
      },
      variant: {
        primary: 'primary-container',
        secondary: 'secondary-container'
      },
      density: {
        compact: 'compact-container',
        normal: 'normal-container',
        relaxed: 'relaxed-container'
      },
      theme: {
        light: 'light-mode',
        dark: 'dark-mode'
      }
    },
    compoundVariants: [
      {
        variant: 'primary',
        theme: 'dark',
        className: 'primary-dark'
      },
      {
        variant: 'secondary',
        theme: 'dark',
        className: 'secondary-dark'
      }
    ],
    defaultVariants: {
      size: 'md',
      variant: 'primary',
      density: 'normal',
      theme: 'light'
    }
  })({ 
    ...responsiveProps, 
    size, 
    variant, 
    density, 
    theme, 
    className 
  });
  
  // i18n integration
  const { t } = useTranslation(i18nNamespace || 'common');
  
  // Skeleton loading state using shadcn/ui Skeleton
  if (isLoading && loadingStrategy === 'skeleton') {
    return (
      <div className={containerStyles}>
        <Skeleton className="h-[200px] w-full rounded-lg" />
      </div>
    );
  }
  
  // Error boundary wrapper
  const ComponentContent = () => (
    <motion.div
      className={containerStyles}
      variants={motionVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      data-testid={testId}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
      aria-describedby={ariaDescribedBy}
    >
      {/* Component UI using shadcn components */}
      <Card className="w-full">
        <CardHeader>
          <h3 className="text-lg font-medium">{t('component.title')}</h3>
        </CardHeader>
        <CardContent>
          {/* Virtualized rendering for large datasets */}
          <div 
            ref={scrollElementRef}
            className="h-[500px] overflow-auto"
          >
            <div
              style={{
                height: `${virtualizer.getTotalSize()}px`,
                width: '100%',
                position: 'relative',
              }}
              className={containerQuery ? '@container' : ''}
            >
              {virtualizer.getVirtualItems().map((virtualItem) => (
                <motion.div
                  key={virtualItem.key}
                  variants={itemAnimationVariants}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: `${virtualItem.size}px`,
                    transform: `translateY(${virtualItem.start}px)`,
                  }}
                  className="@xs:p-2 @sm:p-3 @md:p-4" // Tailwind 4.0.0 container queries
                >
                  {renderItem(data[virtualItem.index])}
                </motion.div>
              ))}
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleEvent} variant="outline">
            {t('component.action')}
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
  
  // Apply error boundary conditionally
  if (errorBoundary) {
    return (
      <ErrorBoundary 
        fallback={errorFallback}
        onError={onErrorCapture}
        resetKeys={[data]}
        onReset={() => {
          if (retryOnError) {
            // Retry logic
          }
        }}
      >
        <ComponentContent />
      </ErrorBoundary>
    );
  }
  
  return <ComponentContent />;
});

### Advanced Mobile Touch Optimization

```typescript
// Touch optimization hook
export function useTouchOptimization({
  element,
  onSwipeLeft,
  onSwipeRight,
  onLongPress,
  onDoubleTap,
  onPinch,
  vibrationFeedback,
}: TouchOptions) {
  useEffect(() => {
    if (!element.current) return;
    
    const target = element.current;
    
    // Gesture recognizers for mobile
    const hammerManager = new Hammer(target);
    
    // Configure recognizers
    hammerManager.add(new Hammer.Swipe({ direction: Hammer.DIRECTION_HORIZONTAL }));
    hammerManager.add(new Hammer.Press({ time: 500 }));
    hammerManager.add(new Hammer.Tap({ taps: 2 }));
    hammerManager.add(new Hammer.Pinch());
    
    // Event handlers
    hammerManager.on('swipeleft', (e) => {
      if (vibrationFeedback) navigator.vibrate?.(50);
      onSwipeLeft?.(e);
    });
    
    hammerManager.on('swiperight', (e) => {
      if (vibrationFeedback) navigator.vibrate?.(50);
      onSwipeRight?.(e);
    });
    
    hammerManager.on('press', (e) => {
      if (vibrationFeedback) navigator.vibrate?.(100);
      onLongPress?.(e);
    });
    
    hammerManager.on('doubletap', (e) => {
      onDoubleTap?.(e);
    });
    
    hammerManager.on('pinch', (e) => {
      onPinch?.(e);
    });
    
    return () => {
      hammerManager.destroy();
    };
  }, [element, onSwipeLeft, onSwipeRight, onLongPress, onDoubleTap, onPinch, vibrationFeedback]);
}
```

### React Query + Router Integration

```typescript
// Integrated fetch hook with router and query
export function useDataWithRouterIntegration<TData>({
  endpoint,
  queryKey,
  routeParams,
  navigateOnSelect,
  routeBuilder,
  transformResponse,
  ...queryOptions
}: IntegratedFetchOptions<TData>) {
  const router = useRouter();
  const queryClient = useQueryClient();
  
  // Build effective endpoint with route params
  const effectiveEndpoint = useMemo(() => {
    let url = endpoint;
    if (routeParams) {
      Object.entries(routeParams).forEach(([key, value]) => {
        url = url.replace(`:${key}`, encodeURIComponent(value));
      });
    }
    return url;
  }, [endpoint, routeParams]);
  
  // Handle selection with router integration
  const handleSelect = useCallback((item: TData) => {
    if (navigateOnSelect && routeBuilder) {
      const route = routeBuilder(item);
      
      // Pre-fetch data for the detail view
      queryClient.prefetchQuery({
        queryKey: ['detail', routeBuilder(item)],
        queryFn: () => fetch(`/api/${route}`).then(res => res.json()),
      });
      
      // Navigate with pre-loaded data
      router.navigate({
        to: route,
        preload: true,
      });
    }
  }, [navigateOnSelect, routeBuilder, router, queryClient]);
  
  // Use React Query with all optimizations
  const query = useQuery({
    queryKey: queryKey || ['data', effectiveEndpoint],
    queryFn: async () => {
      const response = await fetch(effectiveEndpoint);
      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }
      const result = await response.json();
      return transformResponse ? transformResponse(result) : result.data;
    },
    placeholderData: (previousData) => previousData,
    ...queryOptions,
  });
  
  return {
    ...query,
    handleSelect,
  };
}
```

This comprehensive component system is architected to handle any display pattern with production-grade quality, performance optimization, and developer experience, leveraging the specific technologies and standards required for enterprise applications.