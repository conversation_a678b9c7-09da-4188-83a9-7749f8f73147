// components/Card/CardBandStats/_tests_/card-band-stats.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { CardBandStats } from '../card-band-stats';
import { Package } from 'lucide-react';
import { 
  baseFixture, 
  orangeFixture, 
  blueFixture, 
  compactFixture,
  heroFixture,
  interactiveFixture 
} from '../__fixtures__/card-band-stats.fixtures';

describe('CardBandStats', () => {
  it('renders with required props from base fixture', () => {
    render(<CardBandStats {...baseFixture} />);
    
    expect(screen.getByText(baseFixture.value.toString())).toBeInTheDocument();
    expect(screen.getByText(baseFixture.description)).toBeInTheDocument();
  });

  it('renders with custom icon', () => {
    render(
      <CardBandStats 
        value="40,000+" 
        description="สินค้าในคลัง" 
        icon={<Package data-testid="test-icon" />} 
      />
    );
    
    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });

  it('applies default variant styling', () => {
    const { container } = render(<CardBandStats {...baseFixture} />);
    
    const valueElement = screen.getByText(baseFixture.value.toString());
    expect(valueElement).toHaveClass('text-3xl');
  });

  it('applies compact variant styling', () => {
    const { container } = render(<CardBandStats {...compactFixture} />);
    
    const valueElement = screen.getByText(compactFixture.value.toString());
    expect(valueElement).toHaveClass('text-2xl');
  });
  
  it('applies hero variant styling', () => {
    const { container } = render(<CardBandStats {...heroFixture} />);
    
    const valueElement = screen.getByText(heroFixture.value.toString());
    expect(valueElement).toHaveClass('text-5xl');
    
    const contentElement = container.querySelector('.py-10');
    expect(contentElement).toBeInTheDocument();
  });

  it('applies white color scheme', () => {
    const { container } = render(<CardBandStats {...baseFixture} />);
    
    const card = container.firstChild;
    expect(card).toHaveClass('bg-white');
  });

  it('applies orange color scheme', () => {
    const { container } = render(<CardBandStats {...orangeFixture} />);
    
    const card = container.firstChild;
    expect(card).toHaveClass('bg-orange-400');
  });

  it('applies blue color scheme', () => {
    const { container } = render(<CardBandStats {...blueFixture} />);
    
    const card = container.firstChild;
    expect(card).toHaveClass('bg-blue-900');
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    
    render(
      <CardBandStats 
        {...baseFixture}
        onClick={handleClick} 
      />
    );
    
    fireEvent.click(screen.getByText(baseFixture.value.toString()));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('adds cursor-pointer class when onClick is provided', () => {
    const { container } = render(<CardBandStats {...interactiveFixture} />);
    
    const card = container.firstChild;
    expect(card).toHaveClass('cursor-pointer');
  });

  it('renders with custom className', () => {
    const customClass = 'custom-test-class';
    
    const { container } = render(
      <CardBandStats 
        {...baseFixture}
        className={customClass} 
      />
    );
    
    const card = container.firstChild;
    expect(card).toHaveClass(customClass);
  });
});