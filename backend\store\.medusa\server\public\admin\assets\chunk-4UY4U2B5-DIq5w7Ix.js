import{f as C,M as Ee,u as Ie,a as q,K as be,P as ve,D as we,g as Se,S as Oe,v as _e,b as Ce,d as G,h as De,i as je,c as Me,C as V,j as Q}from"./sortable.esm-Cw5vqf5Y.js";import{r,j as c,m as $,I as te,aj as Ae,Y as ke,aL as Be}from"./index-Bwql5Dzz.js";import{D as Fe}from"./dots-six-CeeG2_9G.js";import{F as Ne}from"./folder-illustration-f9jTkVsE.js";var Re=Object.defineProperty,L=Object.getOwnPropertySymbols,ne=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable,X=(e,t,n)=>t in e?Re(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,$e=(e,t)=>{for(var n in t)ne.call(t,n)&&X(e,n,t[n]);if(L)for(var n of L(t))re.call(t,n)&&X(e,n,t[n]);return e},Le=(e,t)=>{var n={};for(var a in e)ne.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&L)for(var a of L(e))t.indexOf(a)<0&&re.call(e,a)&&(n[a]=e[a]);return n};const ae=r.forwardRef((e,t)=>{var n=e,a=Le(n,["color"]);return r.createElement("svg",$e({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},a),r.createElement("g",{clipPath:"url(#a)"},r.createElement("path",{fill:"#60A5FA",fillRule:"evenodd",d:"M13.935 11.15a2.32 2.32 0 0 1-2.318 2.32H3.383a2.32 2.32 0 0 1-2.318-2.32V3.785a2.32 2.32 0 0 1 2.318-2.318h1.691c.704 0 1.368.319 1.808.867l.348.433h4.387a2.32 2.32 0 0 1 2.318 2.318z",clipRule:"evenodd"}),r.createElement("path",{fill:"url(#b)",fillOpacity:.15,fillRule:"evenodd",d:"M13.935 11.15a2.32 2.32 0 0 1-2.318 2.32H3.383a2.32 2.32 0 0 1-2.318-2.32V3.785a2.32 2.32 0 0 1 2.318-2.318h1.691c.704 0 1.368.319 1.808.867l.348.433h4.387a2.32 2.32 0 0 1 2.318 2.318z",clipRule:"evenodd"}),r.createElement("path",{stroke:"#000",strokeLinecap:"round",strokeLinejoin:"round",strokeOpacity:.15,strokeWidth:.5,d:"M7.034 2.922a.25.25 0 0 0 .196.094h4.387c1.142 0 2.068.926 2.068 2.068v6.067a2.07 2.07 0 0 1-2.068 2.068H3.383a2.07 2.07 0 0 1-2.068-2.068V3.784c0-1.142.926-2.068 2.068-2.068h1.691c.628 0 1.22.284 1.613.773z"}),r.createElement("g",{fillRule:"evenodd",clipRule:"evenodd",filter:"url(#c)"},r.createElement("path",{fill:"#60A5FA",d:"M2.041 5.734h10.917a1.95 1.95 0 0 1 1.884 2.452l-.955 3.578a2.38 2.38 0 0 1-2.302 1.77h-8.17a2.38 2.38 0 0 1-2.303-1.77L.158 8.186A1.95 1.95 0 0 1 2.04 5.734z"}),r.createElement("path",{fill:"url(#d)",fillOpacity:.2,d:"M2.041 5.734h10.917a1.95 1.95 0 0 1 1.884 2.452l-.955 3.578a2.38 2.38 0 0 1-2.302 1.77h-8.17a2.38 2.38 0 0 1-2.303-1.77L.158 8.186A1.95 1.95 0 0 1 2.04 5.734z"}))),r.createElement("defs",null,r.createElement("linearGradient",{id:"b",x1:7.5,x2:7.5,y1:1.466,y2:13.469,gradientUnits:"userSpaceOnUse"},r.createElement("stop",null),r.createElement("stop",{offset:1,stopOpacity:0})),r.createElement("linearGradient",{id:"d",x1:7.5,x2:7.5,y1:5.734,y2:13.534,gradientUnits:"userSpaceOnUse"},r.createElement("stop",{stopColor:"#fff"}),r.createElement("stop",{offset:1,stopColor:"#fff",stopOpacity:0})),r.createElement("clipPath",{id:"a"},r.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"})),r.createElement("filter",{id:"c",width:14.817,height:7.8,x:.091,y:5.734,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),r.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),r.createElement("feOffset",{dy:-.5}),r.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}),r.createElement("feBlend",{in2:"shape",result:"effect1_innerShadow_6347_12110"}),r.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),r.createElement("feOffset",{dy:.5}),r.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),r.createElement("feColorMatrix",{values:"0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"}),r.createElement("feBlend",{in2:"effect1_innerShadow_6347_12110",result:"effect2_innerShadow_6347_12110"}))))});ae.displayName="FolderOpenIllustration";var Te=Object.defineProperty,T=Object.getOwnPropertySymbols,se=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable,W=(e,t,n)=>t in e?Te(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Ue=(e,t)=>{for(var n in t)se.call(t,n)&&W(e,n,t[n]);if(T)for(var n of T(t))le.call(t,n)&&W(e,n,t[n]);return e},Pe=(e,t)=>{var n={};for(var a in e)se.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&T)for(var a of T(e))t.indexOf(a)<0&&le.call(e,a)&&(n[a]=e[a]);return n};const oe=r.forwardRef((e,t)=>{var n=e,a=Pe(n,["color"]);return r.createElement("svg",Ue({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},a),r.createElement("g",{filter:"url(#a)"},r.createElement("path",{fill:"#818CF8",d:"M12.863 6.686 8.315 2.138A2.16 2.16 0 0 0 6.777 1.5H2.884c-.763 0-1.384.62-1.384 1.384v3.893c0 .581.226 1.127.638 1.538l4.548 4.548c.41.412.957.637 1.537.637s1.128-.226 1.538-.637l3.102-3.102c.412-.41.637-.957.637-1.538 0-.58-.226-1.127-.637-1.537"}),r.createElement("path",{fill:"url(#b)",fillOpacity:.2,d:"M12.863 6.686 8.315 2.138A2.16 2.16 0 0 0 6.777 1.5H2.884c-.763 0-1.384.62-1.384 1.384v3.893c0 .581.226 1.127.638 1.538l4.548 4.548c.41.412.957.637 1.537.637s1.128-.226 1.538-.637l3.102-3.102c.412-.41.637-.957.637-1.538 0-.58-.226-1.127-.637-1.537"})),r.createElement("path",{stroke:"#000",strokeOpacity:.15,strokeWidth:.5,d:"M6.863 12.687 2.313 8.137a1.9 1.9 0 0 1-.564-1.361V2.884c0-.625.509-1.134 1.134-1.134h3.893c.515 0 .997.2 1.361.564l4.549 4.548c.363.364.563.847.563 1.361 0 .515-.2.998-.563 1.361l-3.103 3.103a1.9 1.9 0 0 1-1.36.563c-.515 0-.998-.2-1.361-.563Z"}),r.createElement("g",{filter:"url(#c)"},r.createElement("path",{fill:"#818CF8",d:"M5.257 6.246a.99.99 0 0 1-.989-.989.99.99 0 0 1 .989-.989.99.99 0 0 1 .989.99.99.99 0 0 1-.989.988m3.385 3.979a.59.59 0 0 1-.838 0L6.222 8.643a.593.593 0 1 1 .84-.84l1.581 1.582a.593.593 0 0 1 0 .84m1.582-1.582a.59.59 0 0 1-.838 0L7.804 7.06a.593.593 0 1 1 .84-.84l1.581 1.582a.593.593 0 0 1 0 .84"}),r.createElement("path",{fill:"url(#d)",fillOpacity:.15,d:"M5.257 6.246a.99.99 0 0 1-.989-.989.99.99 0 0 1 .989-.989.99.99 0 0 1 .989.99.99.99 0 0 1-.989.988m3.385 3.979a.59.59 0 0 1-.838 0L6.222 8.643a.593.593 0 1 1 .84-.84l1.581 1.582a.593.593 0 0 1 0 .84m1.582-1.582a.59.59 0 0 1-.838 0L7.804 7.06a.593.593 0 1 1 .84-.84l1.581 1.582a.593.593 0 0 1 0 .84"})),r.createElement("defs",null,r.createElement("linearGradient",{id:"b",x1:7.5,x2:7.5,y1:1.5,y2:13.5,gradientUnits:"userSpaceOnUse"},r.createElement("stop",{stopColor:"#fff"}),r.createElement("stop",{offset:1,stopColor:"#fff",stopOpacity:0})),r.createElement("linearGradient",{id:"d",x1:7.333,x2:7.333,y1:4.268,y2:10.399,gradientUnits:"userSpaceOnUse"},r.createElement("stop",null),r.createElement("stop",{offset:1,stopOpacity:0})),r.createElement("filter",{id:"a",width:12,height:12,x:1.5,y:1.5,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),r.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),r.createElement("feOffset",{dy:-.5}),r.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"}),r.createElement("feBlend",{in2:"shape",result:"effect1_innerShadow_9148_1663"})),r.createElement("filter",{id:"c",width:6.13,height:6.13,x:4.268,y:4.268,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse"},r.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),r.createElement("feBlend",{in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),r.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),r.createElement("feOffset",{dy:-.5}),r.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),r.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"}),r.createElement("feBlend",{in2:"shape",result:"effect1_innerShadow_9148_1663"}),r.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),r.createElement("feOffset",{dy:.5}),r.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),r.createElement("feColorMatrix",{values:"0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"}),r.createElement("feBlend",{in2:"effect1_innerShadow_9148_1663",result:"effect2_innerShadow_9148_1663"}))))});oe.displayName="TagIllustration";var ze=/iPad|iPhone|iPod/.test(navigator.platform);function Ge(e,t){return Math.round(e/t)}function H(e,t,n,a,o){const l=e.findIndex(({id:g})=>g===n),u=e.findIndex(({id:g})=>g===t),m=e[u],E=G(e,u,l),d=E[l-1],_=E[l+1],w=Ge(a,o),x=m.depth+w,h=Ve({previousItem:d}),p=He({nextItem:_});let f=x;return x>=h?f=h:x<p&&(f=p),{depth:f,maxDepth:h,minDepth:p,parentId:b()};function b(){var I;return f===0||!d?null:f===d.depth?d.parentId:f>d.depth?d.id:((I=E.slice(0,l).reverse().find(j=>j.depth===f))==null?void 0:I.parentId)??null}}function Ve({previousItem:e}){return e?e.depth+1:0}function He({nextItem:e}){return e?e.depth:0}function ie(e,t=null,n=0,a){return e.reduce((o,l,u)=>{const m=l[a]||[];return[...o,{...l,parentId:t,depth:n,index:u},...ie(m,l.id,n+1,a)]},[])}function z(e,t){return ie(e,void 0,void 0,t)}function Je(e,t,n){const a={id:"root",[n]:[]},o={[a.id]:a},l=e.map(m=>({...m,[n]:[]}));let u={id:null,parentId:null,index:0};if(l.forEach((m,E)=>{const{id:d,index:_,depth:w,parentId:x,...h}=m,p=m[n]||[],f=x??a.id,b=o[f]??Ke(l,f);if(o[d]={id:d,[n]:p},b[n].push({id:d,...h,[n]:p}),E===t){const g=b[n];u={id:m.id,parentId:b.id==="root"?null:b.id,index:g.length-1}}}),!u.id)throw new Error("Could not find item");return{items:a[n],update:u}}function Ke(e,t){return e.find(({id:n})=>n===t)}function de(e,t,n){for(const a of e){const{id:o}=a,l=a[n]||[];if(o===t)return a;if(l.length){const u=de(l,t,n);if(u)return u}}}function ce(e,t=0,n){return e.reduce((a,o)=>{const l=o[n]||[];return l.length?ce(l,a+1,n):a+1},t)}function Ye(e,t,n){const a=de(e,t,n),o=(a==null?void 0:a[n])||[];return a?ce(o,0,n):0}function Ze(e,t,n){const a=[...t];return e.filter(o=>o.parentId&&a.includes(o.parentId)?((o[n]||[]).length&&a.push(o.id),!1):!0)}var qe=[C.Down,C.Right,C.Up,C.Left],Qe=[C.Left,C.Right],Xe=(e,t)=>(n,{currentCoordinates:a,context:{active:o,over:l,collisionRect:u,droppableRects:m,droppableContainers:E}})=>{if(qe.includes(n.code)){if(!o||!u)return;n.preventDefault();const{current:{items:d,offset:_}}=e;if(Qe.includes(n.code)&&(l!=null&&l.id)){const{depth:p,maxDepth:f,minDepth:b}=H(d,o.id,l.id,_,t);switch(n.code){case C.Left:if(p>b)return{...a,x:a.x-t};break;case C.Right:if(p<f)return{...a,x:a.x+t};break}return}const w=[];E.forEach(p=>{if(p!=null&&p.disabled||p.id===(l==null?void 0:l.id))return;const f=m.get(p.id);if(f)switch(n.code){case C.Down:u.top<f.top&&w.push(p);break;case C.Up:u.top>f.top&&w.push(p);break}});const x=De({active:o,collisionRect:u,pointerCoordinates:null,droppableRects:m,droppableContainers:w});let h=je(x,"id");if(h===(l==null?void 0:l.id)&&x.length>1&&(h=x[1].id),h&&(l!=null&&l.id)){const p=m.get(o.id),f=m.get(h),b=E.get(h);if(p&&f&&b){const g=d.findIndex(({id:M})=>M===h),I=d[g],j=d.findIndex(({id:M})=>M===o.id),N=d[j];if(I&&N){const{depth:M}=H(d,o.id,h,(I.depth-N.depth)*t,t),F=g>j?1:-1;return{x:f.left+M*t,y:f.top+F*0}}}}}},fe=r.forwardRef(({childCount:e,clone:t,depth:n,disableSelection:a,disableInteraction:o,ghost:l,handleProps:u,indentationWidth:m,collapsed:E,onCollapse:d,style:_,value:w,disabled:x,wrapperRef:h,...p},f)=>c.jsx("li",{ref:h,style:{paddingLeft:`${m*n}px`},className:$("-mb-px list-none",{"pointer-events-none":o,"select-none":a,"[&:first-of-type>div]:border-t-0":!t}),...p,children:c.jsxs("div",{ref:f,style:_,className:$("bg-ui-bg-base transition-fg relative flex items-center gap-x-3 border-y px-6 py-2.5",{"border-l":n>0,"shadow-elevation-flyout bg-ui-bg-base w-fit rounded-lg border-none pr-6 opacity-80":t,"bg-ui-bg-base-hover z-[1] opacity-50":l,"bg-ui-bg-disabled":x}),children:[c.jsx(We,{...u,disabled:x}),c.jsx(tt,{collapsed:E,onCollapse:d,clone:t}),c.jsx(et,{childrenCount:e,collapsed:E,clone:t}),c.jsx(nt,{value:w}),c.jsx(rt,{clone:t,childrenCount:e})]})}));fe.displayName="TreeItem";var We=({listeners:e,attributes:t,disabled:n})=>c.jsx(te,{size:"small",variant:"transparent",type:"button",className:$("cursor-grab",{"cursor-not-allowed":n}),disabled:n,...t,...e,children:c.jsx(Fe,{})}),et=({childrenCount:e,collapsed:t,clone:n})=>{const a=n?e&&e>1:e,o=n?!1:!t;return c.jsx("div",{className:"flex size-7 items-center justify-center",children:a?o?c.jsx(ae,{}):c.jsx(Ne,{}):c.jsx(oe,{})})},tt=({collapsed:e,onCollapse:t,clone:n})=>n?null:t?c.jsx(te,{size:"small",variant:"transparent",onClick:t,type:"button",children:c.jsx(Ae,{className:$("text-ui-fg-subtle transition-transform",{"rotate-90":!e})})}):c.jsx("div",{className:"size-7",role:"presentation"}),nt=({value:e})=>c.jsx("div",{className:"txt-compact-small text-ui-fg-subtle flex-grow truncate",children:e}),rt=({clone:e,childrenCount:t})=>!e||!t||e&&t<=1?null:c.jsx(ke,{size:"2xsmall",color:"blue",className:"absolute -right-2 -top-2",children:t}),at=({isSorting:e,wasDragging:t})=>!(e||t);function ee({id:e,depth:t,disabled:n,...a}){const{attributes:o,isDragging:l,isSorting:u,listeners:m,setDraggableNodeRef:E,setDroppableNodeRef:d,transform:_,transition:w}=Me({id:e,animateLayoutChanges:at,disabled:n}),x={transform:V.Translate.toString(_),transition:w};return c.jsx(fe,{ref:E,wrapperRef:d,style:x,depth:t,ghost:l,disableSelection:ze,disableInteraction:u,disabled:n,handleProps:{listeners:m,attributes:o},...a})}var st={droppable:{strategy:Ee.Always}},lt={keyframes({transform:e}){return[{opacity:1,transform:V.Transform.toString(e.initial)},{opacity:0,transform:V.Transform.toString({...e.final,x:e.final.x+5,y:e.final.y+5})}]},easing:"ease-out",sideEffects({active:e}){e.node.animate([{opacity:0},{opacity:1}],{duration:Q.duration,easing:Q.easing})}};function ot({collapsible:e=!0,childrenProp:t="children",enableDrag:n=!0,items:a=[],indentationWidth:o=40,onChange:l,renderValue:u}){const[m,E]=r.useState({}),[d,_]=r.useState(null),[w,x]=r.useState(null),[h,p]=r.useState(0),[f,b]=r.useState(null),g=r.useMemo(()=>{const s=z(a,t),i=s.reduce((S,O)=>{const{id:v}=O,A=O[t]||[];return m[v]&&A.length?[...S,v]:S},[]);return Ze(s,d?[d,...i]:i,t)},[d,a,t,m]),I=d&&w?H(g,d,w,h,o):null,j=r.useRef({items:g,offset:h}),[N]=r.useState(()=>Xe(j,o)),M=Ie(q(ve),q(be,{coordinateGetter:N})),J=r.useMemo(()=>g.map(({id:s})=>s),[g]),F=d?g.find(({id:s})=>s===d):null;r.useEffect(()=>{j.current={items:g,offset:h}},[g,h]);function K({active:{id:s}}){_(s),x(s);const i=g.find(({id:S})=>S===s);i&&b({parentId:i.parentId,overId:s}),document.body.style.setProperty("cursor","grabbing")}function Y({delta:s}){p(s.x)}function ue({over:s}){x((s==null?void 0:s.id)??null)}function pe({active:s,over:i}){if(Z(),I&&i){const{depth:S,parentId:O}=I,v=JSON.parse(JSON.stringify(z(a,t))),A=v.findIndex(({id:y})=>y===i.id),D=v.findIndex(({id:y})=>y===s.id),k=v[D];v[D]={...k,depth:S,parentId:O};const B=G(v,D,A),{items:R,update:P}=Je(B,A,t);l(P,R)}}function me(){Z()}function Z(){x(null),_(null),p(0),b(null),document.body.style.setProperty("cursor","")}function he(s){E(i=>({...i,[s]:!i[s]}))}function U(s,i,S){if(S&&I){if(s!=="onDragEnd"){if(f&&I.parentId===f.parentId&&S===f.overId)return;b({parentId:I.parentId,overId:S})}const O=JSON.parse(JSON.stringify(z(a,t))),v=O.findIndex(({id:y})=>y===S),A=O.findIndex(({id:y})=>y===i),D=G(O,A,v),k=D[v-1];let B;const R=s==="onDragEnd"?"dropped":"moved",P=s==="onDragEnd"?"dropped":"nested";if(k)if(I.depth>k.depth)B=`${i} was ${P} under ${k.id}.`;else{let y=k;for(;y&&I.depth<y.depth;){const xe=y.parentId;y=D.find(({id:ye})=>ye===xe)}y&&(B=`${i} was ${R} after ${y.id}.`)}else{const y=D[v+1];B=`${i} was ${R} before ${y.id}.`}return B}}const ge={onDragStart({active:s}){return`Picked up ${s.id}.`},onDragMove({active:s,over:i}){return U("onDragMove",s.id,i==null?void 0:i.id)},onDragOver({active:s,over:i}){return U("onDragOver",s.id,i==null?void 0:i.id)},onDragEnd({active:s,over:i}){return U("onDragEnd",s.id,i==null?void 0:i.id)},onDragCancel({active:s}){return`Moving was cancelled. ${s.id} was dropped in its original position.`}};return c.jsx(we,{accessibility:{announcements:ge},sensors:M,collisionDetection:Se,measuring:st,onDragStart:K,onDragMove:Y,onDragOver:ue,onDragEnd:pe,onDragCancel:me,children:c.jsxs(Oe,{items:J,strategy:_e,children:[g.map(s=>{const{id:i,depth:S}=s,O=s[t]||[],v=typeof n=="function"?!n(s):!n;return c.jsx(ee,{id:i,value:u(s),disabled:v,depth:i===d&&I?I.depth:S,indentationWidth:o,collapsed:!!(m[i]&&O.length),childCount:O.length,onCollapse:e&&O.length?()=>he(i):void 0},i)}),Be.createPortal(c.jsx(Ce,{dropAnimation:lt,children:d&&F?c.jsx(ee,{id:d,depth:F.depth,clone:!0,childCount:Ye(a,d,t)+1,value:u(F),indentationWidth:0}):null}),document.body)]})})}var pt=({value:e,onChange:t,renderValue:n,enableDrag:a=!0,isLoading:o=!1})=>o?c.jsx("div",{className:"txt-compact-small relative flex-1 overflow-y-auto",children:Array.from({length:10}).map((l,u)=>c.jsx(it,{},u))}):c.jsx(ot,{items:e,childrenProp:"category_children",collapsible:!0,enableDrag:a,onChange:t,renderValue:n}),it=()=>c.jsx("div",{className:"bg-ui-bg-base -mb-px flex h-12 animate-pulse items-center border-y px-6 py-2.5"});export{pt as C};
