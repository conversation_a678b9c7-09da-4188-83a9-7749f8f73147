import React from 'react';
import { Star } from 'lucide-react';
import { ProductSpecifications } from './__components__/product-specifications';
import { PriceDisplay } from './__components__/price-display';
import { ProductDetailProps, ProductRating } from './product-detail.types';

// คอมโพเนนต์สำหรับแสดงดาว
const StarRating: React.FC<{
  value: number;
  reviewCount?: number;
  badge?: string;
  className?: string;
}> = ({ value, reviewCount, badge, className = '' }) => {
  // Format number with commas
  const formatNumber = (num: number) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const stars = [];

  for (let i = 0; i < 5; i++) {
    stars.push(
      <Star
        key={i}
        size={24}
        fill={i < Math.floor(value) ? 'currentColor' : 'none'}
        className="text-[#FFAA00]"
      />,
    );
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      {/* แสดงกรอบสี่เหลี่ยมเฉพาะเมื่อมีการส่ง string เข้ามา */}
      {badge && (
        <div className="text-subtitle-15-reg text-primary mr-2 flex h-[32px] w-[84px] items-center justify-center rounded-md bg-[#EAF5FF]">
          {badge}
        </div>
      )}

      <div className="flex items-center">
        <div className="flex gap-1">{stars}</div>
        {reviewCount !== undefined && (
          <div className="text-subtitle-14-reg text-discription ml-1 flex items-center">
            ({formatNumber(reviewCount)})
          </div>
        )}
      </div>
    </div>
  );
};

export const ProductDetail: React.FC<ProductDetailProps> = ({
  product,
  variant = 'default',
  layout = 'standard',
  showRating = true,
  showSalesCount = true,
  showSpecifications = true,
  className = '',
}) => {
  const {
    brand,
    title,
    productCode,
    rating,
    reviewCount,
    salesCount,
    specifications,
    currentPrice,
    originalPrice,
    currency = 'บาท',
    priceUnit,
  } = product;

  const containerClasses = [
    'product-detail h-full max-w-[398px] md:max-w-[596px] px-2 md:px-6', // Changed px-4 to match 16px
    variant === 'bordered' ? 'border border-gray-200 rounded-lg ' : '',
    variant === 'default' ? ' rounded-lg' : '',
    variant === 'compact' ? 'p-2 md:p-3' : 'p-3 md:p-4',
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={containerClasses}>
      {/* ส่วนแสดงคะแนน */}
      {showRating && rating && (
        <div className="mb-4">
          <StarRating value={rating.value} reviewCount={reviewCount} badge={rating.badge} />
        </div>
      )}

      {/* แบรนด์ */}
      <div className="text-text-md text-discription leading-none">{brand}</div>

      {/* ข้อมูลสินค้า */}
      <div className="flex flex-col">
        {/* ชื่อสินค้า */}
        <h1 className="text-text-xl-bold  md:text-text-xl text-title border-b border-gray-200 pb-2  md:border-b-0 md:pb-0 md:leading-[48px]">
          {title}
        </h1>

        <div className="mt-[12px] flex flex-col md:mt-0 md:flex-row md:items-start md:justify-between">
          <div className="text-text-md text-border pb-[8px] md:pb-0 ">
            รหัสสินค้า:{' '}
            <span className="text-body-16-med text-discription-secondary">{productCode}</span>
          </div>

          {showSalesCount && salesCount !== undefined && (
            <div className="text-text-md text-border border-t border-gray-200 pt-[12px]  md:border-t-0 md:pt-0">
              ขายแล้ว:{' '}
              <span className="text-body-16-med text-discription-secondary">{salesCount} ชิ้น</span>
            </div>
          )}
        </div>
      </div>

      {/* รายละเอียดสินค้า */}
      {showSpecifications && specifications && specifications.length > 0 && (
        <div className="mt-2 md:mt-4">
          <hr className="border-gray-200" />
          <h3 className="text-h4 text-title mt-4 mb-4 md:mt-[26px] mb-[8px] leading-none">รายละเอียด</h3>
          <div className="text-body-16-reg text-discription-secondary">
            <ProductSpecifications specifications={specifications} layout={layout} />
          </div>
        </div>
      )}

      {/* ส่วนราคา */}
      <div className="mt-4">
        <hr className="mb-[19px] border-gray-200" />{' '}
        {/* Changed my-3 to mb-[19px] for exact spacing */}
        <h4 className="text-h4 text-title leading-none">ราคาสินค้า</h4>
        <PriceDisplay
          currentPrice={currentPrice}
          originalPrice={originalPrice}
          currency={currency}
          priceUnit={priceUnit}
          showDiscount={false}
        />
      </div>
    </div>
  );
};

export default ProductDetail;
