import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import Carousel from '../carousel';
import '../carousel.css';

const meta: Meta<typeof Carousel> = {
  title: 'UI/Carousel/Carousel',
  component: Carousel,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'multiple'],
      description: 'Carousel display variant',
    },
    autoplay: {
      control: 'boolean',
      description: 'Enable autoplay',
    },
    autoplayDelay: {
      control: { type: 'number', min: 1000, max: 10000, step: 500 },
      description: 'Autoplay delay in milliseconds',
    },
    navigation: {
      control: 'boolean',
      description: 'Show navigation arrows',
    },
    pagination: {
      control: 'boolean',
      description: 'Show pagination dots',
    },
  },
};

export default meta;
type Story = StoryObj<typeof Carousel>;

// Sample items for the carousel
const sampleItems = [
  {
    id: 1,
    title: 'Slide 1',
    description: 'This is the first slide',
    image: 'https://via.placeholder.com/600x400/3b82f6/ffffff?text=Slide+1',
  },
  {
    id: 2,
    title: 'Slide 2',
    description: 'This is the second slide',
    image: 'https://via.placeholder.com/600x400/22c55e/ffffff?text=Slide+2',
  },
  {
    id: 3,
    title: 'Slide 3',
    description: 'This is the third slide',
    image: 'https://via.placeholder.com/600x400/ef4444/ffffff?text=Slide+3',
  },
  {
    id: 4,
    title: 'Slide 4',
    description: 'This is the fourth slide',
    image: 'https://via.placeholder.com/600x400/f59e0b/ffffff?text=Slide+4',
  },
  {
    id: 5,
    title: 'Slide 5',
    description: 'This is the fifth slide',
    image: 'https://via.placeholder.com/600x400/8b5cf6/ffffff?text=Slide+5',
  },
];

// Standard render function for carousel items
const renderDefaultItem = (item: any) => (
  <div className="relative w-full overflow-hidden rounded-lg">
    <img 
      src={item.image} 
      alt={item.title} 
      className="w-full h-64 object-cover"
    />
    <div className="absolute inset-0 flex flex-col justify-end p-6 bg-gradient-to-t from-black/70 to-transparent">
      <h3 className="text-xl font-bold text-white">{item.title}</h3>
      <p className="mt-2 text-white/90">{item.description}</p>
    </div>
  </div>
);

// Default variant
export const Default: Story = {
  args: {
    variant: 'default',
    items: sampleItems.slice(0, 3),
    renderItem: renderDefaultItem,
    autoplay: false,
    navigation: true,
    pagination: true,
  },
};

// Multiple slides variant
export const Multiple: Story = {
  args: {
    variant: 'multiple',
    items: sampleItems,
    renderItem: (item: any) => (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <img 
          src={item.image} 
          alt={item.title} 
          className="w-full h-48 object-cover rounded-md"
        />
        <h3 className="mt-2 text-lg font-medium">{item.title}</h3>
        <p className="mt-1 text-gray-600">{item.description}</p>
      </div>
    ),
    autoplay: true,
    autoplayDelay: 4000,
  },
};