import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import OneColTable from '../one-col-table';
import { mockItems, virtualizedItems } from '../__fixtures__/one-col-table.fixtures';
import { formatTimestamp } from '../utils';

describe('OneColTable', () => {
  // Render tests
  describe('Render Tests', () => {
    it('renders without errors', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} />);
      expect(screen.getByRole('table')).toBeInTheDocument();
    });

    it('renders all provided items correctly', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} />);
      expect(screen.getByText('Batch Processing #1')).toBeInTheDocument();
      expect(screen.getByText('Batch Processing #2')).toBeInTheDocument();
      expect(screen.getByText('Batch Processing #3')).toBeInTheDocument();
    });

    it('renders secondary content when provided', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} />);
      expect(screen.getByText('Completed successfully with 34 items')).toBeInTheDocument();
      expect(screen.getByText('In progress, 12 items remaining')).toBeInTheDocument();
    });

    it('renders timestamps when provided', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} />);
      // Check for formatted timestamps
      const firstItemDate = formatTimestamp(mockItems[0].timestamp);
      expect(screen.getByText(firstItemDate)).toBeInTheDocument();
    });
  });

  // Prop tests
  describe('Prop Tests', () => {
    it('applies size variants correctly', () => {
      const { rerender } = render(<OneColTable items={mockItems.slice(0, 3)} size="sm" />);
      expect(screen.getByRole('table')).toHaveClass('text-sm');

      rerender(<OneColTable items={mockItems.slice(0, 3)} size="md" />);
      expect(screen.getByRole('table')).toHaveClass('text-base');

      rerender(<OneColTable items={mockItems.slice(0, 3)} size="lg" />);
      expect(screen.getByRole('table')).toHaveClass('text-lg');
    });

    it('applies style variants correctly', () => {
      const { rerender } = render(<OneColTable items={mockItems.slice(0, 3)} variant="primary" />);
      expect(screen.getByRole('table')).toHaveClass('bg-background');

      rerender(<OneColTable items={mockItems.slice(0, 3)} variant="secondary" />);
      expect(screen.getByRole('table')).toHaveClass('bg-muted');

      rerender(<OneColTable items={mockItems.slice(0, 3)} variant="outline" />);
      expect(screen.getByRole('table')).toHaveClass('border');
    });

    it('applies custom className correctly', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} className="custom-class" />);
      expect(screen.getByRole('table')).toHaveClass('custom-class');
    });

    it('limits the number of items when limit prop is provided', () => {
      render(<OneColTable items={mockItems} limit={2} />);
      expect(screen.getByText('Batch Processing #1')).toBeInTheDocument();
      expect(screen.getByText('Batch Processing #2')).toBeInTheDocument();
      expect(screen.queryByText('Batch Processing #3')).not.toBeInTheDocument();
    });

    it('renders custom header when provided', () => {
      const customHeader = <div data-testid="custom-header">Custom Table Header</div>;
      render(<OneColTable items={mockItems.slice(0, 3)} header={customHeader} />);
      expect(screen.getByTestId('custom-header')).toBeInTheDocument();
    });
  });

  // State tests
  describe('State Tests', () => {
    it('renders skeleton loader when loading is true', () => {
      render(<OneColTable items={mockItems} loading />);
      expect(screen.getByRole('status')).toBeInTheDocument();
      expect(screen.queryByText('Batch Processing #1')).not.toBeInTheDocument();
    });

    it('disables row interactions when disabled is true', async () => {
      const onRowClick = jest.fn();
      render(<OneColTable items={mockItems.slice(0, 3)} onRowClick={onRowClick} disabled />);

      const firstRow = screen.getByText('Batch Processing #1').closest('[role="row"]');
      expect(firstRow).toHaveAttribute('aria-disabled', 'true');

      if (firstRow) {
        await userEvent.click(firstRow);
        expect(onRowClick).not.toHaveBeenCalled();
      }
    });

    it('shows error message when error prop is provided', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} error="Failed to load data" />);
      expect(screen.getByText('Failed to load data')).toBeInTheDocument();
    });

    it('renders custom empty state when no items and emptyState is provided', () => {
      const customEmptyState = <div>Custom empty message</div>;
      render(<OneColTable items={[]} emptyState={customEmptyState} />);
      expect(screen.getByText('Custom empty message')).toBeInTheDocument();
    });

    it('renders default empty state when no items are provided', () => {
      render(<OneColTable items={[]} />);
      expect(screen.getByText('No items found')).toBeInTheDocument();
    });
  });

  // Interaction tests
  describe('Interaction Tests', () => {
    it('calls onRowClick when a row is clicked', async () => {
      const handleRowClick = jest.fn();
      render(<OneColTable items={mockItems.slice(0, 3)} onRowClick={handleRowClick} />);

      const firstRow = screen.getByText('Batch Processing #1').closest('[role="row"]');
      if (firstRow) {
        await userEvent.click(firstRow);
        expect(handleRowClick).toHaveBeenCalledWith(mockItems[0], 0);
      }
    });

    it('calls onRowHover when mouse enters a row', async () => {
      const handleRowHover = jest.fn();
      render(<OneColTable items={mockItems.slice(0, 3)} onRowHover={handleRowHover} />);

      const firstRow = screen.getByText('Batch Processing #1').closest('[role="row"]');
      if (firstRow) {
        fireEvent.mouseEnter(firstRow);
        expect(handleRowHover).toHaveBeenCalledWith(mockItems[0], 0);
      }
    });

    it('calls onLoadMore when "Load more" button is clicked', async () => {
      const handleLoadMore = jest.fn();
      render(
        <OneColTable
          items={mockItems.slice(0, 3)}
          showLoadMore
          hasMore
          onLoadMore={handleLoadMore}
        />,
      );

      const loadMoreButton = screen.getByRole('button', { name: /load more/i });
      await userEvent.click(loadMoreButton);
      expect(handleLoadMore).toHaveBeenCalled();
    });

    it('does not show "Load more" button when hasMore is false', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} showLoadMore hasMore={false} />);

      expect(screen.queryByRole('button', { name: /load more/i })).not.toBeInTheDocument();
    });
  });

  // Accessibility tests
  describe('Accessibility Tests', () => {
    it('has the correct ARIA attributes', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} />);

      // Table should have appropriate ARIA role
      expect(screen.getByRole('table')).toBeInTheDocument();

      // Check row accessibility attributes
      const rows = screen.getAllByRole('row');
      expect(rows.length).toBeGreaterThan(0);

      // Header row should have proper role
      expect(screen.getByRole('row', { name: /content timestamp/i })).toBeInTheDocument();
    });

    it('applies correct aria-disabled attribute when disabled', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} disabled />);
      expect(screen.getByRole('table')).toHaveAttribute('aria-disabled', 'true');
    });

    it('maintains keyboard navigability', async () => {
      render(<OneColTable items={mockItems.slice(0, 3)} />);

      const table = screen.getByRole('table');
      table.focus();
      expect(table).toHaveFocus();

      // Test keyboard navigation would be more robust with integration tests
    });
  });

  // Edge case tests
  describe('Edge Case Tests', () => {
    it('handles empty arrays gracefully', () => {
      render(<OneColTable items={[]} />);
      expect(screen.getByText('No items found')).toBeInTheDocument();
    });

    it('handles items without secondaryContent', () => {
      const itemsWithoutSecondaryContent = [
        {
          id: 'item-1',
          content: 'Content only item',
          timestamp: new Date(),
        },
      ];

      render(<OneColTable items={itemsWithoutSecondaryContent} />);
      expect(screen.getByText('Content only item')).toBeInTheDocument();
      // No secondaryContent should be displayed
    });

    it('handles items without timestamps', () => {
      const itemsWithoutTimestamps = [
        {
          id: 'item-1',
          content: 'No timestamp item',
          secondaryContent: 'Secondary content',
        },
      ];

      render(<OneColTable items={itemsWithoutTimestamps} />);
      expect(screen.getByText('No timestamp item')).toBeInTheDocument();
      // No timestamp should be displayed
    });
  });

  // Virtualization tests
  describe('Virtualization Tests', () => {
    // These tests are more limited as virtual lists are harder to test in jsdom
    it('renders with virtualization enabled', () => {
      render(<OneColTable items={virtualizedItems} virtualized height={400} />);

      // Should render some rows but not all 1000
      expect(screen.getByRole('table')).toBeInTheDocument();

      // Check if at least the first item is rendered
      expect(screen.getByText('Virtualized Item 1')).toBeInTheDocument();

      // Shouldn't render all 1000 items
      expect(screen.queryByText('Virtualized Item 1000')).not.toBeInTheDocument();
    });
  });
});
