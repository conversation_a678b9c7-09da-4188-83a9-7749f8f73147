import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, within } from '@storybook/test';
import { ApiKeyPermissionSection, PermissionNotification } from '..';
import { FormProvider, useForm } from 'react-hook-form';
// import { defaultCreateValues, sampleApiKeyRestricted } from '../__fixtures__/ApiKey.fixtures';

// Wrapper component to provide form context for stories
const ApiKeyPermissionSectionWrapper = (
  props: React.ComponentProps<typeof ApiKeyPermissionSection>,
) => {
  const methods = useForm({
    defaultValues: {
      permissionType: 'Restricted' as const,
      resourcePermissions: {
        models: 'Read' as const,
        modelCapabilities: 'Write' as const,
        assistants: 'None' as const,
        threads: 'None' as const,
        evals: 'None' as const,
        fineTuning: 'None' as const,
        files: 'Read' as const,
      },
    },
  });

  return (
    <FormProvider {...methods}>
      <div className="rounded-lg bg-white p-4 shadow">
        <ApiKeyPermissionSection {...props} />
      </div>
    </FormProvider>
  );
};

// Permission notification wrapper
const PermissionNotificationWrapper = (
  props: React.ComponentProps<typeof PermissionNotification>,
) => (
  <div className="rounded-lg bg-white p-4 shadow">
    <PermissionNotification {...props} />
  </div>
);

const meta = {
  title: 'Components/ApiKey/PermissionSection/Variants',
  component: ApiKeyPermissionSection,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'All available variants of the ApiKeyPermissionSection component',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="min-h-screen space-y-6 bg-gray-50 p-6">
        <Story />
      </div>
    ),
  ],
  tags: ['autodocs'],
} as Meta;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * The default permission section with no special configuration.
 */
export const Default: Story = {
  render: () => <ApiKeyPermissionSectionWrapper permissionType="Restricted" />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    await expect(canvas.getByText('Resources')).toBeInTheDocument();
    await expect(canvas.getByText('Permissions')).toBeInTheDocument();
  },
};

/**
 * Compact variant with reduced spacing for denser UIs.
 */
export const CompactVariant: Story = {
  render: () => <ApiKeyPermissionSectionWrapper permissionType="Restricted" variant="compact" />,
};

/**
 * Expanded variant with more generous spacing for enhanced readability.
 */
export const ExpandedVariant: Story = {
  render: () => <ApiKeyPermissionSectionWrapper permissionType="Restricted" variant="expanded" />,
};

/**
 * Demonstrates how the component can hide resource paths for a cleaner interface.
 */
export const WithoutResourcePaths: Story = {
  render: () => (
    <ApiKeyPermissionSectionWrapper permissionType="Restricted" showResourcePaths={false} />
  ),
};

/**
 * Adds custom styling to the permission section.
 */
export const WithCustomStyling: Story = {
  render: () => (
    <ApiKeyPermissionSectionWrapper
      permissionType="Restricted"
      className="rounded-xl border-blue-200 bg-blue-50"
    />
  ),
};

/**
 * Shows how the component can be configured with different animation speeds.
 */
export const WithSlowAnimation: Story = {
  render: () => (
    <ApiKeyPermissionSectionWrapper permissionType="Restricted" animationSpeed="slow" />
  ),
};

/**
 * Using fast animations for quicker transitions.
 */
export const WithFastAnimation: Story = {
  render: () => (
    <ApiKeyPermissionSectionWrapper permissionType="Restricted" animationSpeed="fast" />
  ),
};

/**
 * Demonstrates how resources can be grouped by category for better organization.
 */
export const WithGroupedResources: Story = {
  render: () => (
    <ApiKeyPermissionSectionWrapper permissionType="Restricted" groupResources={true} />
  ),
};

/**
 * Shows how to configure minimum required access levels for certain resources.
 */
export const WithRequiredMinimumAccess: Story = {
  render: () => (
    <ApiKeyPermissionSectionWrapper
      permissionType="Restricted"
      requiredMinimumAccess={{
        models: 'Read',
        assistants: 'None',
        files: 'Read',
      }}
    />
  ),
};

/**
 * Demonstrates adding custom resources to the permission section.
 */
export const WithCustomResources: Story = {
  render: () => (
    <ApiKeyPermissionSectionWrapper
      permissionType="Restricted"
      customResources={[
        {
          name: 'customEndpoint',
          label: 'Custom Endpoint',
          path: '/v1/custom',
        },
        {
          name: 'analytics',
          label: 'Analytics API',
          path: '/v1/analytics',
        },
      ]}
    />
  ),
};

/**
 * The default permission notification component.
 */
export const DefaultNotification: Story = {
  render: () => <PermissionNotificationWrapper />,
};

/**
 * Information variant of the permission notification.
 */
export const InfoNotification: Story = {
  render: () => <PermissionNotificationWrapper variant="info" />,
};

/**
 * Warning variant of the permission notification.
 */
export const WarningNotification: Story = {
  render: () => <PermissionNotificationWrapper variant="warning" />,
};

/**
 * Error variant of the permission notification.
 */
export const ErrorNotification: Story = {
  render: () => <PermissionNotificationWrapper variant="error" />,
};

/**
 * Notification without an icon.
 */
export const NotificationWithoutIcon: Story = {
  render: () => <PermissionNotificationWrapper showIcon={false} />,
};

/**
 * Notification with custom animation speed.
 */
export const NotificationWithAnimation: Story = {
  render: () => (
    <div className="space-y-4">
      <PermissionNotificationWrapper animationSpeed="slow" variant="info" />
      <PermissionNotificationWrapper animationSpeed="normal" variant="warning" />
      <PermissionNotificationWrapper animationSpeed="fast" variant="error" />
    </div>
  ),
};

/**
 * Notification with custom styling.
 */
export const NotificationWithCustomStyling: Story = {
  render: () => <PermissionNotificationWrapper className="border-primary border-2 border-dashed" />,
};

/**
 * Comprehensive example showing a complex permission section with all features.
 */
export const CompleteExample: Story = {
  render: () => (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">API Key Permissions</h2>
      <p className="text-muted-foreground text-sm">
        Configure access permissions for your API key. Different resources require different
        permission levels.
      </p>

      <ApiKeyPermissionSectionWrapper
        permissionType="Restricted"
        variant="expanded"
        groupResources={true}
        customResources={[
          {
            name: 'customEndpoint',
            label: 'Custom Endpoint',
            path: '/v1/custom',
          },
        ]}
        requiredMinimumAccess={{
          models: 'Read',
          assistants: 'None',
        }}
        className="border-primary/20"
        animationSpeed="normal"
      />

      <PermissionNotificationWrapper variant="info" className="mt-4" />
    </div>
  ),
};
