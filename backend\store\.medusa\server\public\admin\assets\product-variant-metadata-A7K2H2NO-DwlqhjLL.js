import{M as n}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as d,a7 as p,aa as u,j as c}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var h=()=>{const{id:a,variant_id:r}=d(),{variant:t,isPending:o,isError:i,error:s}=p(a,r),{mutateAsync:e,isPending:m}=u(a,r);if(i)throw s;return c.jsx(n,{metadata:t==null?void 0:t.metadata,hook:e,isPending:o,isMutating:m})};export{h as Component};
