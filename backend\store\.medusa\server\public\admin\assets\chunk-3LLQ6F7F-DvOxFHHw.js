import{r as c,cq as uo,j as O,bm as so,aL as St,cr as io,cs as ao,ct as co,cu as lo,cv as fo,cw as mo,cx as po,cy as vo,b as bo,cz as go,m as nt,T as gt,bp as ho,ai as xo}from"./index-Bwql5Dzz.js";import{X as xn}from"./x-mark-mini-DvSTI7zK.js";import{T as So}from"./triangles-mini-DPBC_td5.js";import{P as yo}from"./plus-mini-C5sDHkH8.js";var Eo=Object.defineProperty,Co=Object.defineProperties,wo=Object.getOwnPropertyDescriptors,yt=Object.getOwnPropertySymbols,Jn=Object.prototype.hasOwnProperty,Zn=Object.prototype.propertyIsEnumerable,Sn=(e,t,r)=>t in e?Eo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,C=(e,t)=>{for(var r in t||(t={}))Jn.call(t,r)&&Sn(e,r,t[r]);if(yt)for(var r of yt(t))Zn.call(t,r)&&Sn(e,r,t[r]);return e},j=(e,t)=>Co(e,wo(t)),Z=(e,t)=>{var r={};for(var n in e)Jn.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&yt)for(var n of yt(e))t.indexOf(n)<0&&Zn.call(e,n)&&(r[n]=e[n]);return r},Po=Object.defineProperty,Io=Object.defineProperties,Oo=Object.getOwnPropertyDescriptors,Et=Object.getOwnPropertySymbols,Qn=Object.prototype.hasOwnProperty,er=Object.prototype.propertyIsEnumerable,yn=(e,t,r)=>t in e?Po(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,oe=(e,t)=>{for(var r in t||(t={}))Qn.call(t,r)&&yn(e,r,t[r]);if(Et)for(var r of Et(t))er.call(t,r)&&yn(e,r,t[r]);return e},Ee=(e,t)=>Io(e,Oo(t)),tr=(e,t)=>{var r={};for(var n in e)Qn.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&Et)for(var n of Et(e))t.indexOf(n)<0&&er.call(e,n)&&(r[n]=e[n]);return r};function it(...e){}function Ao(e,t){if(Do(e)){const r=To(t)?t():t;return e(r)}return e}function Do(e){return typeof e=="function"}function To(e){return typeof e=="function"}function Ve(e,t){return typeof Object.hasOwn=="function"?Object.hasOwn(e,t):Object.prototype.hasOwnProperty.call(e,t)}function _e(...e){return(...t)=>{for(const r of e)typeof r=="function"&&r(...t)}}function nr(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function _o(e,t){const r=oe({},e);for(const n of t)Ve(r,n)&&delete r[n];return r}function Fo(e,t){const r={};for(const n of t)Ve(e,n)&&(r[n]=e[n]);return r}function rr(e){return e}function Oe(e,t){if(!e)throw typeof t!="string"?new Error("Invariant failed"):new Error(t)}function Ro(e){return Object.keys(e)}function or(e,...t){const r=typeof e=="function"?e(...t):e;return r==null?!1:!r}function Ut(e){return e.disabled||e["aria-disabled"]===!0||e["aria-disabled"]==="true"}function Ye(e){const t={};for(const r in e)e[r]!==void 0&&(t[r]=e[r]);return t}function G(...e){for(const t of e)if(t!==void 0)return t}function Lt(e,t){typeof e=="function"?e(t):e&&(e.current=t)}function ko(e){return!e||!c.isValidElement(e)?!1:"ref"in e.props||"ref"in e}function Mo(e){return ko(e)?C({},e.props).ref||e.ref:null}function Vo(e,t){const r=C({},e);for(const n in t){if(!Ve(t,n))continue;if(n==="className"){const u="className";r[u]=e[u]?`${e[u]} ${t[u]}`:t[u];continue}if(n==="style"){const u="style";r[u]=e[u]?C(C({},e[u]),t[u]):t[u];continue}const o=t[n];if(typeof o=="function"&&n.startsWith("on")){const u=e[n];if(typeof u=="function"){r[n]=(...s)=>{o(...s),u(...s)};continue}}r[n]=o}return r}var st=Lo();function Lo(){var e;return typeof window<"u"&&!!((e=window.document)!=null&&e.createElement)}function ue(e){return e?"self"in e?e.document:e.ownerDocument||document:document}function ur(e){return e?"self"in e?e.self:ue(e).defaultView||window:self}function Ke(e,t=!1){const{activeElement:r}=ue(e);if(!(r!=null&&r.nodeName))return null;if(qt(r)&&r.contentDocument)return Ke(r.contentDocument.body,t);if(t){const n=r.getAttribute("aria-activedescendant");if(n){const o=ue(r).getElementById(n);if(o)return o}}return r}function ve(e,t){return e===t||e.contains(t)}function qt(e){return e.tagName==="IFRAME"}function qe(e){const t=e.tagName.toLowerCase();return t==="button"?!0:t==="input"&&e.type?No.indexOf(e.type)!==-1:!1}var No=["button","color","file","image","reset","submit"];function sr(e){if(typeof e.checkVisibility=="function")return e.checkVisibility();const t=e;return t.offsetWidth>0||t.offsetHeight>0||e.getClientRects().length>0}function Be(e){try{const t=e instanceof HTMLInputElement&&e.selectionStart!==null,r=e.tagName==="TEXTAREA";return t||r||!1}catch{return!1}}function Nt(e){return e.isContentEditable||Be(e)}function jo(e){if(Be(e))return e.value;if(e.isContentEditable){const t=ue(e).createRange();return t.selectNodeContents(e),t.toString()}return""}function jt(e){let t=0,r=0;if(Be(e))t=e.selectionStart||0,r=e.selectionEnd||0;else if(e.isContentEditable){const n=ue(e).getSelection();if(n!=null&&n.rangeCount&&n.anchorNode&&ve(e,n.anchorNode)&&n.focusNode&&ve(e,n.focusNode)){const o=n.getRangeAt(0),u=o.cloneRange();u.selectNodeContents(e),u.setEnd(o.startContainer,o.startOffset),t=u.toString().length,u.setEnd(o.endContainer,o.endOffset),r=u.toString().length}}return{start:t,end:r}}function ir(e,t){const r=["dialog","menu","listbox","tree","grid"],n=e==null?void 0:e.getAttribute("role");return n&&r.indexOf(n)!==-1?n:t}function Yt(e){if(!e)return null;const t=r=>r==="auto"||r==="scroll";if(e.clientHeight&&e.scrollHeight>e.clientHeight){const{overflowY:r}=getComputedStyle(e);if(t(r))return e}else if(e.clientWidth&&e.scrollWidth>e.clientWidth){const{overflowX:r}=getComputedStyle(e);if(t(r))return e}return Yt(e.parentElement)||document.scrollingElement||document.body}function _t(e,...t){/text|search|password|tel|url/i.test(e.type)&&e.setSelectionRange(...t)}function $o(e,t){const r=e.map((o,u)=>[u,o]);let n=!1;return r.sort(([o,u],[s,i])=>{const a=t(u),l=t(i);return a===l||!a||!l?0:Ko(a,l)?(o>s&&(n=!0),-1):(o<s&&(n=!0),1)}),n?r.map(([o,u])=>u):e}function Ko(e,t){return!!(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_PRECEDING)}function ar(){return st&&!!navigator.maxTouchPoints}function Gt(){return st?/mac|iphone|ipad|ipod/i.test(navigator.platform):!1}function wt(){return st&&Gt()&&/apple/i.test(navigator.vendor)}function Bo(){return st&&/firefox\//i.test(navigator.userAgent)}function Ho(){return st&&navigator.platform.startsWith("Mac")&&!ar()}function cr(e){return!!(e.currentTarget&&!ve(e.currentTarget,e.target))}function Te(e){return e.target===e.currentTarget}function zo(e){const t=e.currentTarget;if(!t)return!1;const r=Gt();if(r&&!e.metaKey||!r&&!e.ctrlKey)return!1;const n=t.tagName.toLowerCase();return n==="a"||n==="button"&&t.type==="submit"||n==="input"&&t.type==="submit"}function Wo(e){const t=e.currentTarget;if(!t)return!1;const r=t.tagName.toLowerCase();return e.altKey?r==="a"||r==="button"&&t.type==="submit"||r==="input"&&t.type==="submit":!1}function rt(e,t){const r=new FocusEvent("blur",t),n=e.dispatchEvent(r),o=Ee(oe({},t),{bubbles:!0});return e.dispatchEvent(new FocusEvent("focusout",o)),n}function Uo(e,t,r){const n=new KeyboardEvent(t,r);return e.dispatchEvent(n)}function En(e,t){const r=new MouseEvent("click",t);return e.dispatchEvent(r)}function Xe(e,t){const r=t||e.currentTarget,n=e.relatedTarget;return!n||!ve(r,n)}function ot(e,t,r,n){const u=(i=>{const a=requestAnimationFrame(i);return()=>cancelAnimationFrame(a)})(()=>{e.removeEventListener(t,s,!0),r()}),s=()=>{u(),r()};return e.addEventListener(t,s,{once:!0,capture:!0}),u}function Re(e,t,r,n=window){const o=[];try{n.document.addEventListener(e,t,r);for(const s of Array.from(n.frames))o.push(Re(e,t,r,s))}catch{}return()=>{try{n.document.removeEventListener(e,t,r)}catch{}for(const s of o)s()}}var Xt=C({},uo),Cn=Xt.useId;Xt.useDeferredValue;var wn=Xt.useInsertionEffect,X=st?c.useLayoutEffect:c.useEffect;function qo(e){const[t]=c.useState(e);return t}function Yo(e){const t=c.useRef(e);return X(()=>{t.current=e}),t}function W(e){const t=c.useRef(()=>{throw new Error("Cannot call an event handler while rendering.")});return wn?wn(()=>{t.current=e}):t.current=e,c.useCallback((...r)=>{var n;return(n=t.current)==null?void 0:n.call(t,...r)},[])}function Go(e){const[t,r]=c.useState(null);return X(()=>{if(t==null||!e)return;let n=null;return e(o=>(n=o,t)),()=>{e(n)}},[t,e]),[t,r]}function xe(...e){return c.useMemo(()=>{if(e.some(Boolean))return t=>{for(const r of e)Lt(r,t)}},e)}function Qe(e){if(Cn){const n=Cn();return e||n}const[t,r]=c.useState(e);return X(()=>{if(e||t)return;const n=Math.random().toString(36).slice(2,8);r(`id-${n}`)},[e,t]),e||t}function lr(e,t){const r=u=>{if(typeof u=="string")return u},[n,o]=c.useState(()=>r(t));return X(()=>{const u=e&&"current"in e?e.current:e;o((u==null?void 0:u.tagName.toLowerCase())||r(t))},[e,t]),n}function Xo(e,t,r){const n=qo(r),[o,u]=c.useState(n);return c.useEffect(()=>{const s=e&&"current"in e?e.current:e;if(!s)return;const i=()=>{const l=s.getAttribute(t);u(l??n)},a=new MutationObserver(i);return a.observe(s,{attributeFilter:[t]}),i(),()=>a.disconnect()},[e,t,n]),o}function lt(e,t){const r=c.useRef(!1);c.useEffect(()=>{if(r.current)return e();r.current=!0},t),c.useEffect(()=>()=>{r.current=!1},[])}function Jo(e,t){const r=c.useRef(!1);X(()=>{if(r.current)return e();r.current=!0},t),X(()=>()=>{r.current=!1},[])}function fr(){return c.useReducer(()=>[],[])}function ae(e){return W(typeof e=="function"?e:()=>e)}function Pe(e,t,r=[]){const n=c.useCallback(o=>(e.wrapElement&&(o=e.wrapElement(o)),t(o)),[...r,e.wrapElement]);return j(C({},e),{wrapElement:n})}function dr(e=!1,t){const[r,n]=c.useState(null);return{portalRef:xe(n,t),portalNode:r,domReady:!e||r}}function mr(e,t,r){const n=e.onLoadedMetadataCapture,o=c.useMemo(()=>Object.assign(()=>{},j(C({},n),{[t]:r})),[n,t,r]);return[n==null?void 0:n[t],{onLoadedMetadataCapture:o}]}function Zo(){return c.useEffect(()=>{Re("mousemove",eu,!0),Re("mousedown",ht,!0),Re("mouseup",ht,!0),Re("keydown",ht,!0),Re("scroll",ht,!0)},[]),W(()=>Jt)}var Jt=!1,Pn=0,In=0;function Qo(e){const t=e.movementX||e.screenX-Pn,r=e.movementY||e.screenY-In;return Pn=e.screenX,In=e.screenY,t||r||!1}function eu(e){Qo(e)&&(Jt=!0)}function ht(){Jt=!1}function J(e){const t=c.forwardRef((r,n)=>e(j(C({},r),{ref:n})));return t.displayName=e.displayName||e.name,t}function Zt(e,t){return c.memo(e,t)}function Q(e,t){const r=t,{wrapElement:n,render:o}=r,u=Z(r,["wrapElement","render"]),s=xe(t.ref,Mo(o));let i;if(c.isValidElement(o)){const a=j(C({},o.props),{ref:s});i=c.cloneElement(o,Vo(u,a))}else o?i=o(u):i=O.jsx(e,C({},u));return n?n(i):i}function ee(e){const t=(r={})=>e(r);return t.displayName=e.name,t}function et(e=[],t=[]){const r=c.createContext(void 0),n=c.createContext(void 0),o=()=>c.useContext(r),u=(l=!1)=>{const d=c.useContext(n),v=o();return l?d:d||v},s=()=>{const l=c.useContext(n),d=o();if(!(l&&l===d))return d},i=l=>e.reduceRight((d,v)=>O.jsx(v,j(C({},l),{children:d})),O.jsx(r.Provider,C({},l)));return{context:r,scopedContext:n,useContext:o,useScopedContext:u,useProviderContext:s,ContextProvider:i,ScopedContextProvider:l=>O.jsx(i,j(C({},l),{children:t.reduceRight((d,v)=>O.jsx(v,j(C({},l),{children:d})),O.jsx(n.Provider,C({},l)))}))}}var ft=et(),tu=ft.useContext;ft.useScopedContext;ft.useProviderContext;var nu=ft.ContextProvider,ru=ft.ScopedContextProvider,dt=et([nu],[ru]),pr=dt.useContext;dt.useScopedContext;var ou=dt.useProviderContext,Qt=dt.ContextProvider,vr=dt.ScopedContextProvider,uu=c.createContext(void 0),su=c.createContext(void 0);c.createContext(null);c.createContext(null);var iu=et([Qt],[vr]),au=iu.useContext;function tt(e,t){const r=e.__unstableInternals;return Oe(r,"Invalid store"),r[t]}function $e(e,...t){let r=e,n=r,o=Symbol(),u=it;const s=new Set,i=new Set,a=new Set,l=new Set,d=new Set,v=new WeakMap,f=new WeakMap,x=V=>(a.add(V),()=>a.delete(V)),p=()=>{const V=s.size,w=Symbol();s.add(w);const M=()=>{s.delete(w),!s.size&&u()};if(V)return M;const z=Ro(r).map(P=>_e(...t.map(R=>{var S;const k=(S=R==null?void 0:R.getState)==null?void 0:S.call(R);if(k&&Ve(k,P))return we(R,[P],$=>{F(P,$[P],!0)})}))),B=[];for(const P of a)B.push(P());const m=t.map(en);return u=_e(...z,...B,...m),M},g=(V,w,M=l)=>(M.add(w),f.set(w,V),()=>{var z;(z=v.get(w))==null||z(),v.delete(w),f.delete(w),M.delete(w)}),D=(V,w)=>g(V,w),y=(V,w)=>(v.set(w,w(r,r)),g(V,w)),E=(V,w)=>(v.set(w,w(r,n)),g(V,w,d)),I=V=>$e(Fo(r,V),A),h=V=>$e(_o(r,V),A),_=()=>r,F=(V,w,M=!1)=>{var z;if(!Ve(r,V))return;const B=Ao(w,r[V]);if(B===r[V])return;if(!M)for(const S of t)(z=S==null?void 0:S.setState)==null||z.call(S,V,B);const m=r;r=Ee(oe({},r),{[V]:B});const P=Symbol();o=P,i.add(V);const R=(S,k,$)=>{var b;const N=f.get(S),H=de=>$?$.has(de):de===V;(!N||N.some(H))&&((b=v.get(S))==null||b(),v.set(S,S(r,k)))};for(const S of l)R(S,m);queueMicrotask(()=>{if(o!==P)return;const S=r;for(const k of d)R(k,n,i);n=S,i.clear()})},A={getState:_,setState:F,__unstableInternals:{setup:x,init:p,subscribe:D,sync:y,batch:E,pick:I,omit:h}};return A}function Ce(e,...t){if(e)return tt(e,"setup")(...t)}function en(e,...t){if(e)return tt(e,"init")(...t)}function tn(e,...t){if(e)return tt(e,"subscribe")(...t)}function we(e,...t){if(e)return tt(e,"sync")(...t)}function Ct(e,...t){if(e)return tt(e,"batch")(...t)}function br(e,...t){if(e)return tt(e,"omit")(...t)}function cu(e,...t){if(e)return tt(e,"pick")(...t)}function nn(...e){const t=e.reduce((n,o)=>{var u;const s=(u=o==null?void 0:o.getState)==null?void 0:u.call(o);return s?Object.assign(n,s):n},{}),r=$e(t,...e);return Object.assign({},...e,r)}function Pt(e,t){if(!t)return;const r=Object.entries(e).filter(([u,s])=>u.startsWith("default")&&s!==void 0).map(([u])=>{var s;const i=u.replace("default","");return`${((s=i[0])==null?void 0:s.toLowerCase())||""}${i.slice(1)}`});if(!r.length)return;const n=t.getState();if(r.filter(u=>Ve(n,u)).length)throw new Error(`Passing a store prop in conjunction with a default state is not supported.

const store = useSelectStore();
<SelectProvider store={store} defaultValue="Apple" />
                ^             ^

Instead, pass the default state to the topmost store:

const store = useSelectStore({ defaultValue: "Apple" });
<SelectProvider store={store} />

See https://github.com/ariakit/ariakit/pull/2745 for more details.

If there's a particular need for this, please submit a feature request at https://github.com/ariakit/ariakit
`)}var gr={exports:{}},hr={};/**
 * @license React
 * use-sync-external-store-shim.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(){function e(x,p){return x===p&&(x!==0||1/x===1/p)||x!==x&&p!==p}function t(x,p){d||o.startTransition===void 0||(d=!0,console.error("You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."));var g=p();if(!v){var D=p();u(g,D)||(console.error("The result of getSnapshot should be cached to avoid an infinite loop"),v=!0)}D=s({inst:{value:g,getSnapshot:p}});var y=D[0].inst,E=D[1];return a(function(){y.value=g,y.getSnapshot=p,r(y)&&E({inst:y})},[x,g,p]),i(function(){return r(y)&&E({inst:y}),x(function(){r(y)&&E({inst:y})})},[x]),l(g),g}function r(x){var p=x.getSnapshot;x=x.value;try{var g=p();return!u(x,g)}catch{return!0}}function n(x,p){return p()}typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var o=c,u=typeof Object.is=="function"?Object.is:e,s=o.useState,i=o.useEffect,a=o.useLayoutEffect,l=o.useDebugValue,d=!1,v=!1,f=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?n:t;hr.useSyncExternalStore=o.useSyncExternalStore!==void 0?o.useSyncExternalStore:f,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())})();gr.exports=hr;var lu=gr.exports;const fu=so(lu);var{useSyncExternalStore:xr}=fu,Sr=()=>()=>{};function Ie(e,t=rr){const r=c.useCallback(o=>e?tn(e,null,o):Sr(),[e]),n=()=>{const o=typeof t=="string"?t:null,u=typeof t=="function"?t:null,s=e==null?void 0:e.getState();if(u)return u(s);if(s&&o&&Ve(s,o))return s[o]};return xr(r,n,n)}function yr(e,t){const r=c.useRef({}),n=c.useCallback(u=>e?tn(e,null,u):Sr(),[e]),o=()=>{const u=e==null?void 0:e.getState();let s=!1;const i=r.current;for(const a in t){const l=t[a];if(typeof l=="function"){const d=l(u);d!==i[a]&&(i[a]=d,s=!0)}if(typeof l=="string"){if(!u||!Ve(u,l))continue;const d=u[l];d!==i[a]&&(i[a]=d,s=!0)}}return s&&(r.current=C({},i)),r.current};return xr(n,o,o)}function pe(e,t,r,n){const o=Ve(t,r)?t[r]:void 0,u=n?t[n]:void 0,s=Yo({value:o,setValue:u});X(()=>we(e,[r],(i,a)=>{const{value:l,setValue:d}=s.current;d&&i[r]!==a[r]&&i[r]!==l&&d(i[r])}),[e,r]),X(()=>{if(o!==void 0)return e.setState(r,o),Ct(e,[r],()=>{o!==void 0&&e.setState(r,o)})})}function rn(e,t){const[r,n]=c.useState(()=>e(t));X(()=>en(r),[r]);const o=c.useCallback(i=>Ie(r,i),[r]),u=c.useMemo(()=>j(C({},r),{useState:o}),[r,o]),s=W(()=>{n(i=>e(C(C({},t),i.getState())))});return[u,s]}function du(e){var t;const r=e.find(u=>!!u.element),n=[...e].reverse().find(u=>!!u.element);let o=(t=r==null?void 0:r.element)==null?void 0:t.parentElement;for(;o&&(n!=null&&n.element);){if(n&&o.contains(n.element))return o;o=o.parentElement}return ue(o).body}function mu(e){return e==null?void 0:e.__unstablePrivateStore}function pu(e={}){var t;Pt(e,e.store);const r=(t=e.store)==null?void 0:t.getState(),n=G(e.items,r==null?void 0:r.items,e.defaultItems,[]),o=new Map(n.map(f=>[f.id,f])),u={items:n,renderedItems:G(r==null?void 0:r.renderedItems,[])},s=mu(e.store),i=$e({items:n,renderedItems:u.renderedItems},s),a=$e(u,e.store),l=f=>{const x=$o(f,p=>p.element);i.setState("renderedItems",x),a.setState("renderedItems",x)};Ce(a,()=>en(i)),Ce(i,()=>Ct(i,["items"],f=>{a.setState("items",f.items)})),Ce(i,()=>Ct(i,["renderedItems"],f=>{let x=!0,p=requestAnimationFrame(()=>{const{renderedItems:E}=a.getState();f.renderedItems!==E&&l(f.renderedItems)});if(typeof IntersectionObserver!="function")return()=>cancelAnimationFrame(p);const g=()=>{if(x){x=!1;return}cancelAnimationFrame(p),p=requestAnimationFrame(()=>l(f.renderedItems))},D=du(f.renderedItems),y=new IntersectionObserver(g,{root:D});for(const E of f.renderedItems)E.element&&y.observe(E.element);return()=>{cancelAnimationFrame(p),y.disconnect()}}));const d=(f,x,p=!1)=>{let g;return x(y=>{const E=y.findIndex(({id:h})=>h===f.id),I=y.slice();if(E!==-1){g=y[E];const h=oe(oe({},g),f);I[E]=h,o.set(f.id,h)}else I.push(f),o.set(f.id,f);return I}),()=>{x(y=>{if(!g)return p&&o.delete(f.id),y.filter(({id:h})=>h!==f.id);const E=y.findIndex(({id:h})=>h===f.id);if(E===-1)return y;const I=y.slice();return I[E]=g,o.set(f.id,g),I})}},v=f=>d(f,x=>i.setState("items",x),!0);return Ee(oe({},a),{registerItem:v,renderItem:f=>_e(v(f),d(f,x=>i.setState("renderedItems",x))),item:f=>{if(!f)return null;let x=o.get(f);if(!x){const{items:p}=i.getState();x=p.find(g=>g.id===f),x&&o.set(f,x)}return x||null},__unstablePrivateStore:i})}function vu(e,t,r){return lt(t,[r.store]),pe(e,r,"items","setItems"),e}function bu(e){return Array.isArray(e)?e:typeof e<"u"?[e]:[]}function Er(e){const t=[];for(const r of e)t.push(...r);return t}function $t(e){return e.slice().reverse()}var gu={id:null};function je(e,t){return e.find(r=>t?!r.disabled&&r.id!==t:!r.disabled)}function hu(e,t){return e.filter(r=>t?!r.disabled&&r.id!==t:!r.disabled)}function On(e,t){return e.filter(r=>r.rowId===t)}function xu(e,t,r=!1){const n=e.findIndex(o=>o.id===t);return[...e.slice(n+1),...r?[gu]:[],...e.slice(0,n)]}function Cr(e){const t=[];for(const r of e){const n=t.find(o=>{var u;return((u=o[0])==null?void 0:u.rowId)===r.rowId});n?n.push(r):t.push([r])}return t}function wr(e){let t=0;for(const{length:r}of e)r>t&&(t=r);return t}function Su(e){return{id:"__EMPTY_ITEM__",disabled:!0,rowId:e}}function yu(e,t,r){const n=wr(e);for(const o of e)for(let u=0;u<n;u+=1){const s=o[u];if(!s||r&&s.disabled){const a=u===0&&r?je(o):o[u-1];o[u]=a&&t!==a.id&&r?a:Su(a==null?void 0:a.rowId)}}return e}function Eu(e){const t=Cr(e),r=wr(t),n=[];for(let o=0;o<r;o+=1)for(const u of t){const s=u[o];s&&n.push(Ee(oe({},s),{rowId:s.rowId?`${o}`:void 0}))}return n}function Cu(e={}){var t;const r=(t=e.store)==null?void 0:t.getState(),n=pu(e),o=G(e.activeId,r==null?void 0:r.activeId,e.defaultActiveId),u=Ee(oe({},n.getState()),{id:G(e.id,r==null?void 0:r.id,`id-${Math.random().toString(36).slice(2,8)}`),activeId:o,baseElement:G(r==null?void 0:r.baseElement,null),includesBaseElement:G(e.includesBaseElement,r==null?void 0:r.includesBaseElement,o===null),moves:G(r==null?void 0:r.moves,0),orientation:G(e.orientation,r==null?void 0:r.orientation,"both"),rtl:G(e.rtl,r==null?void 0:r.rtl,!1),virtualFocus:G(e.virtualFocus,r==null?void 0:r.virtualFocus,!1),focusLoop:G(e.focusLoop,r==null?void 0:r.focusLoop,!1),focusWrap:G(e.focusWrap,r==null?void 0:r.focusWrap,!1),focusShift:G(e.focusShift,r==null?void 0:r.focusShift,!1)}),s=$e(u,n,e.store);Ce(s,()=>we(s,["renderedItems","activeId"],a=>{s.setState("activeId",l=>{var d;return l!==void 0?l:(d=je(a.renderedItems))==null?void 0:d.id})}));const i=(a="next",l={})=>{var d,v;const f=s.getState(),{skip:x=0,activeId:p=f.activeId,focusShift:g=f.focusShift,focusLoop:D=f.focusLoop,focusWrap:y=f.focusWrap,includesBaseElement:E=f.includesBaseElement,renderedItems:I=f.renderedItems,rtl:h=f.rtl}=l,_=a==="up"||a==="down",F=a==="next"||a==="down",A=F?h&&!_:!h||_,V=g&&!x;let w=_?Er(yu(Cr(I),p,V)):I;if(w=A?$t(w):w,w=_?Eu(w):w,p==null)return(d=je(w))==null?void 0:d.id;const M=w.find(b=>b.id===p);if(!M)return(v=je(w))==null?void 0:v.id;const z=w.some(b=>b.rowId),B=w.indexOf(M),m=w.slice(B+1),P=On(m,M.rowId);if(x){const b=hu(P,p),N=b.slice(x)[0]||b[b.length-1];return N==null?void 0:N.id}const R=D&&(_?D!=="horizontal":D!=="vertical"),S=z&&y&&(_?y!=="horizontal":y!=="vertical"),k=F?(!z||_)&&R&&E:_?E:!1;if(R){const b=S&&!k?w:On(w,M.rowId),N=xu(b,p,k),H=je(N,p);return H==null?void 0:H.id}if(S){const b=je(k?P:m,p);return k?(b==null?void 0:b.id)||null:b==null?void 0:b.id}const $=je(P,p);return!$&&k?null:$==null?void 0:$.id};return Ee(oe(oe({},n),s),{setBaseElement:a=>s.setState("baseElement",a),setActiveId:a=>s.setState("activeId",a),move:a=>{a!==void 0&&(s.setState("activeId",a),s.setState("moves",l=>l+1))},first:()=>{var a;return(a=je(s.getState().renderedItems))==null?void 0:a.id},last:()=>{var a;return(a=je($t(s.getState().renderedItems)))==null?void 0:a.id},next:a=>(a!==void 0&&typeof a=="number"&&(a={skip:a}),i("next",a)),previous:a=>(a!==void 0&&typeof a=="number"&&(a={skip:a}),i("previous",a)),down:a=>(a!==void 0&&typeof a=="number"&&(a={skip:a}),i("down",a)),up:a=>(a!==void 0&&typeof a=="number"&&(a={skip:a}),i("up",a))})}function wu(e){const t=Qe(e.id);return C({id:t},e)}function Pu(e,t,r){return e=vu(e,t,r),pe(e,r,"activeId","setActiveId"),pe(e,r,"includesBaseElement"),pe(e,r,"virtualFocus"),pe(e,r,"orientation"),pe(e,r,"rtl"),pe(e,r,"focusLoop"),pe(e,r,"focusWrap"),pe(e,r,"focusShift"),e}function Pr(e={}){const t=nn(e.store,br(e.disclosure,["contentElement","disclosureElement"]));Pt(e,t);const r=t==null?void 0:t.getState(),n=G(e.open,r==null?void 0:r.open,e.defaultOpen,!1),o=G(e.animated,r==null?void 0:r.animated,!1),u={open:n,animated:o,animating:!!o&&n,mounted:n,contentElement:G(r==null?void 0:r.contentElement,null),disclosureElement:G(r==null?void 0:r.disclosureElement,null)},s=$e(u,t);return Ce(s,()=>we(s,["animated","animating"],i=>{i.animated||s.setState("animating",!1)})),Ce(s,()=>tn(s,["open"],()=>{s.getState().animated&&s.setState("animating",!0)})),Ce(s,()=>we(s,["open","animating"],i=>{s.setState("mounted",i.open||i.animating)})),Ee(oe({},s),{disclosure:e.disclosure,setOpen:i=>s.setState("open",i),show:()=>s.setState("open",!0),hide:()=>s.setState("open",!1),toggle:()=>s.setState("open",i=>!i),stopAnimation:()=>s.setState("animating",!1),setContentElement:i=>s.setState("contentElement",i),setDisclosureElement:i=>s.setState("disclosureElement",i)})}function Ir(e,t,r){return lt(t,[r.store,r.disclosure]),pe(e,r,"open","setOpen"),pe(e,r,"mounted","setMounted"),pe(e,r,"animated"),Object.assign(e,{disclosure:r.disclosure})}function Iu(e={}){const[t,r]=rn(Pr,e);return Ir(t,r,e)}function Or(e={}){return Pr(e)}function Ar(e,t,r){return Ir(e,t,r)}function Ou(e={}){const[t,r]=rn(Or,e);return Ar(t,r,e)}function Au(e={}){var t=e,{popover:r}=t,n=tr(t,["popover"]);const o=nn(n.store,br(r,["arrowElement","anchorElement","contentElement","popoverElement","disclosureElement"]));Pt(n,o);const u=o==null?void 0:o.getState(),s=Or(Ee(oe({},n),{store:o})),i=G(n.placement,u==null?void 0:u.placement,"bottom"),a=Ee(oe({},s.getState()),{placement:i,currentPlacement:i,anchorElement:G(u==null?void 0:u.anchorElement,null),popoverElement:G(u==null?void 0:u.popoverElement,null),arrowElement:G(u==null?void 0:u.arrowElement,null),rendered:Symbol("rendered")}),l=$e(a,s,o);return Ee(oe(oe({},s),l),{setAnchorElement:d=>l.setState("anchorElement",d),setPopoverElement:d=>l.setState("popoverElement",d),setArrowElement:d=>l.setState("arrowElement",d),render:()=>l.setState("rendered",Symbol("rendered"))})}function Du(e,t,r){return lt(t,[r.popover]),pe(e,r,"placement"),Ar(e,t,r)}var Tu=wt()&&ar();function _u(e={}){var t=e,{tag:r}=t,n=tr(t,["tag"]);const o=nn(n.store,cu(r,["value","rtl"]));Pt(n,o);const u=r==null?void 0:r.getState(),s=o==null?void 0:o.getState(),i=G(n.activeId,s==null?void 0:s.activeId,n.defaultActiveId,null),a=Cu(Ee(oe({},n),{activeId:i,includesBaseElement:G(n.includesBaseElement,s==null?void 0:s.includesBaseElement,!0),orientation:G(n.orientation,s==null?void 0:s.orientation,"vertical"),focusLoop:G(n.focusLoop,s==null?void 0:s.focusLoop,!0),focusWrap:G(n.focusWrap,s==null?void 0:s.focusWrap,!0),virtualFocus:G(n.virtualFocus,s==null?void 0:s.virtualFocus,!0)})),l=Au(Ee(oe({},n),{placement:G(n.placement,s==null?void 0:s.placement,"bottom-start")})),d=G(n.value,s==null?void 0:s.value,n.defaultValue,""),v=G(n.selectedValue,s==null?void 0:s.selectedValue,u==null?void 0:u.values,n.defaultSelectedValue,""),f=Array.isArray(v),x=Ee(oe(oe({},a.getState()),l.getState()),{value:d,selectedValue:v,resetValueOnSelect:G(n.resetValueOnSelect,s==null?void 0:s.resetValueOnSelect,f),resetValueOnHide:G(n.resetValueOnHide,s==null?void 0:s.resetValueOnHide,f&&!r),activeValue:s==null?void 0:s.activeValue}),p=$e(x,a,l,o);return Tu&&Ce(p,()=>we(p,["virtualFocus"],()=>{p.setState("virtualFocus",!1)})),Ce(p,()=>{if(r)return _e(we(p,["selectedValue"],g=>{Array.isArray(g.selectedValue)&&r.setValues(g.selectedValue)}),we(r,["values"],g=>{p.setState("selectedValue",g.values)}))}),Ce(p,()=>we(p,["resetValueOnHide","mounted"],g=>{g.resetValueOnHide&&(g.mounted||p.setState("value",d))})),Ce(p,()=>we(p,["open"],g=>{g.open||(p.setState("activeId",i),p.setState("moves",0))})),Ce(p,()=>we(p,["moves","activeId"],(g,D)=>{g.moves===D.moves&&p.setState("activeValue",void 0)})),Ce(p,()=>Ct(p,["moves","renderedItems"],(g,D)=>{if(g.moves===D.moves)return;const{activeId:y}=p.getState(),E=a.item(y);p.setState("activeValue",E==null?void 0:E.value)})),Ee(oe(oe(oe({},l),a),p),{tag:r,setValue:g=>p.setState("value",g),resetValue:()=>p.setState("value",x.value),setSelectedValue:g=>p.setState("selectedValue",g)})}function Fu(e){const t=au();return e=j(C({},e),{tag:e.tag!==void 0?e.tag:t}),wu(e)}function Ru(e,t,r){return lt(t,[r.tag]),pe(e,r,"value","setValue"),pe(e,r,"selectedValue","setSelectedValue"),pe(e,r,"resetValueOnHide"),pe(e,r,"resetValueOnSelect"),Object.assign(Pu(Du(e,t,r),t,r),{tag:r.tag})}function ku(e={}){e=Fu(e);const[t,r]=rn(_u,e);return Ru(t,r,e)}var mt=et();mt.useContext;mt.useScopedContext;var on=mt.useProviderContext,Mu=mt.ContextProvider,Vu=mt.ScopedContextProvider,un=et([Mu],[Vu]),It=un.useProviderContext,Lu=un.ContextProvider,sn=un.ScopedContextProvider,Nu=c.createContext(void 0),ju=c.createContext(void 0),pt=et([Lu],[sn]);pt.useContext;pt.useScopedContext;var an=pt.useProviderContext,$u=pt.ContextProvider,Dr=pt.ScopedContextProvider,Tr=c.createContext(void 0),vt=et([$u,Qt],[Dr,vr]),Ku=vt.useContext,cn=vt.useScopedContext,Ot=vt.useProviderContext,Bu=vt.ContextProvider,Hu=vt.ScopedContextProvider,_r=c.createContext(void 0),Fr=c.createContext(!1),zu="div",Rr=ee(function(t){var r=t,{store:n}=r,o=Z(r,["store"]);const u=an();return n=n||u,o=j(C({},o),{ref:xe(n==null?void 0:n.setAnchorElement,o.ref)}),o});J(function(t){const r=Rr(t);return Q(zu,r)});function Wu(e,t){return e.find(r=>!r.disabled)}function Ue(e,t){return t&&e.item(t)||null}function Uu(e){const t=[];for(const r of e){const n=t.find(o=>{var u;return((u=o[0])==null?void 0:u.rowId)===r.rowId});n?n.push(r):t.push([r])}return t}function qu(e,t=!1){if(Be(e))e.setSelectionRange(t?e.value.length:0,e.value.length);else if(e.isContentEditable){const r=ue(e).getSelection();r==null||r.selectAllChildren(e),t&&(r==null||r.collapseToEnd())}}var Kt=Symbol("FOCUS_SILENTLY");function Yu(e){e[Kt]=!0,e.focus({preventScroll:!0})}function Gu(e){const t=e[Kt];return delete e[Kt],t}function at(e,t,r){return!(!t||t===r||!e.item(t.id))}var kr=c.createContext(!0),At="input:not([type='hidden']):not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], button:not([disabled]), [tabindex], summary, iframe, object, embed, area[href], audio[controls], video[controls], [contenteditable]:not([contenteditable='false'])";function Xu(e){return Number.parseInt(e.getAttribute("tabindex")||"0",10)<0}function ke(e){return!(!e.matches(At)||!sr(e)||e.closest("[inert]"))}function ct(e){if(!ke(e)||Xu(e))return!1;if(!("form"in e)||!e.form||e.checked||e.type!=="radio")return!0;const t=e.form.elements.namedItem(e.name);if(!t||!("length"in t))return!0;const r=Ke(e);return!r||r===e||!("form"in r)||r.form!==e.form||r.name!==e.name}function ln(e,t){const r=Array.from(e.querySelectorAll(At));t&&r.unshift(e);const n=r.filter(ke);return n.forEach((o,u)=>{if(qt(o)&&o.contentDocument){const s=o.contentDocument.body;n.splice(u,1,...ln(s))}}),n}function Dt(e,t,r){const n=Array.from(e.querySelectorAll(At)),o=n.filter(ct);return t&&ct(e)&&o.unshift(e),o.forEach((u,s)=>{if(qt(u)&&u.contentDocument){const i=u.contentDocument.body,a=Dt(i,!1,r);o.splice(s,1,...a)}}),!o.length&&r?n:o}function Ju(e,t,r){const[n]=Dt(e,t,r);return n||null}function Zu(e,t,r,n){const o=Ke(e),u=ln(e,t),s=u.indexOf(o),i=u.slice(s+1);return i.find(ct)||(r?u.find(ct):null)||(n?i[0]:null)||null}function Ft(e,t){return Zu(document.body,!1,e,t)}function Qu(e,t,r,n){const o=Ke(e),u=ln(e,t).reverse(),s=u.indexOf(o);return u.slice(s+1).find(ct)||null||null||null}function An(e,t){return Qu(document.body,!1)}function es(e){for(;e&&!ke(e);)e=e.closest(At);return e||null}function Je(e){const t=Ke(e);if(!t)return!1;if(t===e)return!0;const r=t.getAttribute("aria-activedescendant");return r?r===e.id:!1}function Mr(e){const t=Ke(e);if(!t)return!1;if(ve(e,t))return!0;const r=t.getAttribute("aria-activedescendant");return!r||!("id"in e)?!1:r===e.id?!0:!!e.querySelector(`#${CSS.escape(r)}`)}function Vr(e){!Mr(e)&&ke(e)&&e.focus()}function ts(e){var t;const r=(t=e.getAttribute("tabindex"))!=null?t:"";e.setAttribute("data-tabindex",r),e.setAttribute("tabindex","-1")}function ns(e,t){const r=Dt(e,t);for(const n of r)ts(n)}function rs(e){const t=e.querySelectorAll("[data-tabindex]"),r=n=>{const o=n.getAttribute("data-tabindex");n.removeAttribute("data-tabindex"),o?n.setAttribute("tabindex",o):n.removeAttribute("tabindex")};e.hasAttribute("data-tabindex")&&r(e);for(const n of t)r(n)}function os(e,t){"scrollIntoView"in e?(e.focus({preventScroll:!0}),e.scrollIntoView(oe({block:"nearest",inline:"nearest"},t))):e.focus()}var us="div",Dn=wt(),ss=["text","search","url","tel","email","password","number","date","month","week","time","datetime","datetime-local"],Lr=Symbol("safariFocusAncestor");function is(e){return e?!!e[Lr]:!1}function Tn(e,t){e&&(e[Lr]=t)}function as(e){const{tagName:t,readOnly:r,type:n}=e;return t==="TEXTAREA"&&!r||t==="SELECT"&&!r?!0:t==="INPUT"&&!r?ss.includes(n):!!(e.isContentEditable||e.getAttribute("role")==="combobox"&&e.dataset.name)}function cs(e){return"labels"in e?e.labels:null}function _n(e){return e.tagName.toLowerCase()==="input"&&e.type?e.type==="radio"||e.type==="checkbox":!1}function ls(e){return e?e==="button"||e==="summary"||e==="input"||e==="select"||e==="textarea"||e==="a":!0}function fs(e){return e?e==="button"||e==="input"||e==="select"||e==="textarea":!0}function ds(e,t,r,n,o){return e?t?r&&!n?-1:void 0:r?o:o||0:o}function Rt(e,t){return W(r=>{e==null||e(r),!r.defaultPrevented&&t&&(r.stopPropagation(),r.preventDefault())})}var fn=!0;function ms(e){const t=e.target;t&&"hasAttribute"in t&&(t.hasAttribute("data-focus-visible")||(fn=!1))}function ps(e){e.metaKey||e.ctrlKey||e.altKey||(fn=!0)}var Tt=ee(function(t){var r=t,{focusable:n=!0,accessibleWhenDisabled:o,autoFocus:u,onFocusVisible:s}=r,i=Z(r,["focusable","accessibleWhenDisabled","autoFocus","onFocusVisible"]);const a=c.useRef(null);c.useEffect(()=>{n&&(Re("mousedown",ms,!0),Re("keydown",ps,!0))},[n]),Dn&&c.useEffect(()=>{if(!n)return;const S=a.current;if(!S||!_n(S))return;const k=cs(S);if(!k)return;const $=()=>queueMicrotask(()=>S.focus());for(const b of k)b.addEventListener("mouseup",$);return()=>{for(const b of k)b.removeEventListener("mouseup",$)}},[n]);const l=n&&Ut(i),d=!!l&&!o,[v,f]=c.useState(!1);c.useEffect(()=>{n&&d&&v&&f(!1)},[n,d,v]),c.useEffect(()=>{if(!n||!v)return;const S=a.current;if(!S||typeof IntersectionObserver>"u")return;const k=new IntersectionObserver(()=>{ke(S)||f(!1)});return k.observe(S),()=>k.disconnect()},[n,v]);const x=Rt(i.onKeyPressCapture,l),p=Rt(i.onMouseDownCapture,l),g=Rt(i.onClickCapture,l),D=i.onMouseDown,y=W(S=>{if(D==null||D(S),S.defaultPrevented||!n)return;const k=S.currentTarget;if(!Dn||cr(S)||!qe(k)&&!_n(k))return;let $=!1;const b=()=>{$=!0},N={capture:!0,once:!0};k.addEventListener("focusin",b,N);const H=es(k.parentElement);Tn(H,!0),ot(k,"mouseup",()=>{k.removeEventListener("focusin",b,!0),Tn(H,!1),!$&&Vr(k)})}),E=(S,k)=>{if(k&&(S.currentTarget=k),!n)return;const $=S.currentTarget;$&&Je($)&&(s==null||s(S),!S.defaultPrevented&&($.dataset.focusVisible="true",f(!0)))},I=i.onKeyDownCapture,h=W(S=>{if(I==null||I(S),S.defaultPrevented||!n||v||S.metaKey||S.altKey||S.ctrlKey||!Te(S))return;const k=S.currentTarget;ot(k,"focusout",()=>E(S,k))}),_=i.onFocusCapture,F=W(S=>{if(_==null||_(S),S.defaultPrevented||!n)return;if(!Te(S)){f(!1);return}const k=S.currentTarget,$=()=>E(S,k);fn||as(S.target)?ot(S.target,"focusout",$):f(!1)}),A=i.onBlur,V=W(S=>{A==null||A(S),n&&Xe(S)&&f(!1)}),w=c.useContext(kr),M=W(S=>{n&&u&&S&&w&&queueMicrotask(()=>{Je(S)||ke(S)&&S.focus()})}),z=lr(a),B=n&&ls(z),m=n&&fs(z),P=i.style,R=c.useMemo(()=>d?C({pointerEvents:"none"},P):P,[d,P]);return i=j(C({"data-focus-visible":n&&v||void 0,"data-autofocus":u||void 0,"aria-disabled":l||void 0},i),{ref:xe(a,M,i.ref),style:R,tabIndex:ds(n,d,B,m,i.tabIndex),disabled:m&&d?!0:void 0,contentEditable:l?void 0:i.contentEditable,onKeyPressCapture:x,onClickCapture:g,onMouseDownCapture:p,onMouseDown:y,onKeyDownCapture:h,onFocusCapture:F,onBlur:V}),Ye(i)});J(function(t){const r=Tt(t);return Q(us,r)});var vs="div";function bs(e){return e.some(t=>!!t.rowId)}function gs(e){const t=e.target;return t&&!Be(t)?!1:e.key.length===1&&!e.ctrlKey&&!e.metaKey}function hs(e){return e.key==="Shift"||e.key==="Control"||e.key==="Alt"||e.key==="Meta"}function Fn(e,t,r){return W(n=>{var o;if(t==null||t(n),n.defaultPrevented||n.isPropagationStopped()||!Te(n)||hs(n)||gs(n))return;const u=e.getState(),s=(o=Ue(e,u.activeId))==null?void 0:o.element;if(!s)return;const i=n,a=Z(i,["view"]),l=r==null?void 0:r.current;s!==l&&s.focus(),Uo(s,n.type,a)||n.preventDefault(),n.currentTarget.contains(s)&&n.stopPropagation()})}function xs(e){return Wu(Er($t(Uu(e))))}function Ss(e){const[t,r]=c.useState(!1),n=c.useCallback(()=>r(!0),[]),o=e.useState(u=>Ue(e,u.activeId));return c.useEffect(()=>{const u=o==null?void 0:o.element;t&&u&&(r(!1),u.focus({preventScroll:!0}))},[o,t]),n}var Nr=ee(function(t){var r=t,{store:n,composite:o=!0,focusOnMove:u=o,moveOnKeyPress:s=!0}=r,i=Z(r,["store","composite","focusOnMove","moveOnKeyPress"]);const a=ou();n=n||a,Oe(n,"Composite must receive a `store` prop or be wrapped in a CompositeProvider component.");const l=c.useRef(null),d=c.useRef(null),v=Ss(n),f=n.useState("moves"),[,x]=Go(o?n.setBaseElement:null);c.useEffect(()=>{var m;if(!n||!f||!o||!u)return;const{activeId:P}=n.getState(),R=(m=Ue(n,P))==null?void 0:m.element;R&&os(R)},[n,f,o,u]),X(()=>{if(!n||!f||!o)return;const{baseElement:m,activeId:P}=n.getState();if(!(P===null)||!m)return;const S=d.current;d.current=null,S&&rt(S,{relatedTarget:m}),Je(m)||m.focus()},[n,f,o]);const p=n.useState("activeId"),g=n.useState("virtualFocus");X(()=>{var m;if(!n||!o||!g)return;const P=d.current;if(d.current=null,!P)return;const S=((m=Ue(n,p))==null?void 0:m.element)||Ke(P);S!==P&&rt(P,{relatedTarget:S})},[n,p,g,o]);const D=Fn(n,i.onKeyDownCapture,d),y=Fn(n,i.onKeyUpCapture,d),E=i.onFocusCapture,I=W(m=>{if(E==null||E(m),m.defaultPrevented||!n)return;const{virtualFocus:P}=n.getState();if(!P)return;const R=m.relatedTarget,S=Gu(m.currentTarget);Te(m)&&S&&(m.stopPropagation(),d.current=R)}),h=i.onFocus,_=W(m=>{if(h==null||h(m),m.defaultPrevented||!o||!n)return;const{relatedTarget:P}=m,{virtualFocus:R}=n.getState();R?Te(m)&&!at(n,P)&&queueMicrotask(v):Te(m)&&n.setActiveId(null)}),F=i.onBlurCapture,A=W(m=>{var P;if(F==null||F(m),m.defaultPrevented||!n)return;const{virtualFocus:R,activeId:S}=n.getState();if(!R)return;const k=(P=Ue(n,S))==null?void 0:P.element,$=m.relatedTarget,b=at(n,$),N=d.current;d.current=null,Te(m)&&b?($===k?N&&N!==$&&rt(N,m):k?rt(k,m):N&&rt(N,m),m.stopPropagation()):!at(n,m.target)&&k&&rt(k,m)}),V=i.onKeyDown,w=ae(s),M=W(m=>{var P;if(V==null||V(m),m.defaultPrevented||!n||!Te(m))return;const{orientation:R,renderedItems:S,activeId:k}=n.getState(),$=Ue(n,k);if((P=$==null?void 0:$.element)!=null&&P.isConnected)return;const b=R!=="horizontal",N=R!=="vertical",H=bs(S);if((m.key==="ArrowLeft"||m.key==="ArrowRight"||m.key==="Home"||m.key==="End")&&Be(m.currentTarget))return;const ne={ArrowUp:(H||b)&&(()=>{if(H){const be=xs(S);return be==null?void 0:be.id}return n==null?void 0:n.last()}),ArrowRight:(H||N)&&n.first,ArrowDown:(H||b)&&n.first,ArrowLeft:(H||N)&&n.last,Home:n.first,End:n.last,PageUp:n.first,PageDown:n.last}[m.key];if(ne){const be=ne();if(be!==void 0){if(!w(m))return;m.preventDefault(),n.move(be)}}});i=Pe(i,m=>O.jsx(Qt,{value:n,children:m}),[n]);const z=n.useState(m=>{var P;if(n&&o&&m.virtualFocus)return(P=Ue(n,m.activeId))==null?void 0:P.id});i=j(C({"aria-activedescendant":z},i),{ref:xe(l,x,i.ref),onKeyDownCapture:D,onKeyUpCapture:y,onFocusCapture:I,onFocus:_,onBlurCapture:A,onKeyDown:M});const B=n.useState(m=>o&&(m.virtualFocus||m.activeId===null));return i=Tt(C({focusable:B},i)),i});J(function(t){const r=Nr(t);return Q(vs,r)});var ys="input";function Rn(e,t,r){if(!r)return!1;const n=e.find(o=>!o.disabled&&o.value);return(n==null?void 0:n.value)===t}function kn(e,t){return!t||e==null?!1:(e=nr(e),t.length>e.length&&t.toLowerCase().indexOf(e.toLowerCase())===0)}function Es(e){return e.type==="input"}function Cs(e){return e==="inline"||e==="list"||e==="both"||e==="none"}function ws(e){const t=e.find(r=>{var n;return r.disabled?!1:((n=r.element)==null?void 0:n.getAttribute("role"))!=="tab"});return t==null?void 0:t.id}var Ps=ee(function(t){var r=t,{store:n,focusable:o=!0,autoSelect:u=!1,getAutoSelectId:s,setValueOnChange:i,showMinLength:a=0,showOnChange:l,showOnMouseDown:d,showOnClick:v=d,showOnKeyDown:f,showOnKeyPress:x=f,blurActiveItemOnClick:p,setValueOnClick:g=!0,moveOnKeyPress:D=!0,autoComplete:y="list"}=r,E=Z(r,["store","focusable","autoSelect","getAutoSelectId","setValueOnChange","showMinLength","showOnChange","showOnMouseDown","showOnClick","showOnKeyDown","showOnKeyPress","blurActiveItemOnClick","setValueOnClick","moveOnKeyPress","autoComplete"]);const I=Ot();n=n||I,Oe(n,"Combobox must receive a `store` prop or be wrapped in a ComboboxProvider component.");const h=c.useRef(null),[_,F]=fr(),A=c.useRef(!1),V=c.useRef(!1),w=n.useState(T=>T.virtualFocus&&u),M=y==="inline"||y==="both",[z,B]=c.useState(M);Jo(()=>{M&&B(!0)},[M]);const m=n.useState("value"),P=c.useRef();c.useEffect(()=>we(n,["selectedValue","activeId"],(T,U)=>{P.current=U.selectedValue}),[]);const R=n.useState(T=>{var U;if(M&&z&&!(T.activeValue&&Array.isArray(T.selectedValue)&&(T.selectedValue.includes(T.activeValue)||(U=P.current)!=null&&U.includes(T.activeValue))))return T.activeValue}),S=n.useState("renderedItems"),k=n.useState("open"),$=n.useState("contentElement"),b=c.useMemo(()=>{if(!M||!z)return m;if(Rn(S,R,w)){if(kn(m,R)){const U=(R==null?void 0:R.slice(m.length))||"";return m+U}return m}return R||m},[M,z,S,R,w,m]);c.useEffect(()=>{const T=h.current;if(!T)return;const U=()=>B(!0);return T.addEventListener("combobox-item-move",U),()=>{T.removeEventListener("combobox-item-move",U)}},[]),c.useEffect(()=>{if(!M||!z||!R||!Rn(S,R,w)||!kn(m,R))return;let U=it;return queueMicrotask(()=>{const se=h.current;if(!se)return;const{start:ce,end:De}=jt(se),Ne=m.length,he=R.length;_t(se,Ne,he),U=()=>{if(!Je(se))return;const{start:Ge,end:oo}=jt(se);Ge===Ne&&oo===he&&_t(se,ce,De)}}),()=>U()},[_,M,z,R,S,w,m]);const N=c.useRef(null),H=W(s),de=c.useRef(null);c.useEffect(()=>{if(!k||!$)return;const T=Yt($);if(!T)return;N.current=T;const U=()=>{A.current=!1},se=()=>{if(!n||!A.current)return;const{activeId:De}=n.getState();De!==null&&De!==de.current&&(A.current=!1)},ce={passive:!0,capture:!0};return T.addEventListener("wheel",U,ce),T.addEventListener("touchmove",U,ce),T.addEventListener("scroll",se,ce),()=>{T.removeEventListener("wheel",U,!0),T.removeEventListener("touchmove",U,!0),T.removeEventListener("scroll",se,!0)}},[k,$,n]),X(()=>{m&&(V.current||(A.current=!0))},[m]),X(()=>{w!=="always"&&k||(A.current=k)},[w,k]);const te=n.useState("resetValueOnSelect");lt(()=>{var T,U;const se=A.current;if(!n||!k||!se&&!te)return;const{baseElement:ce,contentElement:De,activeId:Ne}=n.getState();if(!(ce&&!Je(ce))){if(De!=null&&De.hasAttribute("data-placing")){const he=new MutationObserver(F);return he.observe(De,{attributeFilter:["data-placing"]}),()=>he.disconnect()}if(w&&se){const he=H(S),Ge=he!==void 0?he:(T=ws(S))!=null?T:n.first();de.current=Ge,n.move(Ge??null)}else{const he=(U=n.item(Ne||n.first()))==null?void 0:U.element;he&&"scrollIntoView"in he&&he.scrollIntoView({block:"nearest",inline:"nearest"})}}},[n,k,_,m,w,te,H,S]),c.useEffect(()=>{if(!M)return;const T=h.current;if(!T)return;const U=[T,$].filter(ce=>!!ce),se=ce=>{U.every(De=>Xe(ce,De))&&(n==null||n.setValue(b))};for(const ce of U)ce.addEventListener("focusout",se);return()=>{for(const ce of U)ce.removeEventListener("focusout",se)}},[M,$,n,b]);const re=T=>T.currentTarget.value.length>=a,ne=E.onChange,be=ae(l??re),Fe=ae(i??!n.tag),ge=W(T=>{if(ne==null||ne(T),T.defaultPrevented||!n)return;const U=T.currentTarget,{value:se,selectionStart:ce,selectionEnd:De}=U,Ne=T.nativeEvent;if(A.current=!0,Es(Ne)&&(Ne.isComposing&&(A.current=!1,V.current=!0),M)){const he=Ne.inputType==="insertText"||Ne.inputType==="insertCompositionText",Ge=ce===se.length;B(he&&Ge)}if(Fe(T)){const he=se===n.getState().value;n.setValue(se),queueMicrotask(()=>{_t(U,ce,De)}),M&&w&&he&&F()}be(T)&&n.show(),(!w||!A.current)&&n.setActiveId(null)}),me=E.onCompositionEnd,le=W(T=>{A.current=!0,V.current=!1,me==null||me(T),!T.defaultPrevented&&w&&F()}),K=E.onMouseDown,ye=ae(p??(()=>!!(n!=null&&n.getState().includesBaseElement))),ie=ae(g),Me=ae(v??re),He=W(T=>{K==null||K(T),!T.defaultPrevented&&(T.button||T.ctrlKey||n&&(ye(T)&&n.setActiveId(null),ie(T)&&n.setValue(b),Me(T)&&ot(T.currentTarget,"mouseup",n.show)))}),Le=E.onKeyDown,ze=ae(x??re),L=W(T=>{if(Le==null||Le(T),T.repeat||(A.current=!1),T.defaultPrevented||T.ctrlKey||T.altKey||T.shiftKey||T.metaKey||!n)return;const{open:U}=n.getState();U||(T.key==="ArrowUp"||T.key==="ArrowDown")&&ze(T)&&(T.preventDefault(),n.show())}),q=E.onBlur,fe=W(T=>{A.current=!1,q==null||q(T),T.defaultPrevented}),Y=Qe(E.id),Se=Cs(y)?y:void 0,Ae=n.useState(T=>T.activeId===null);return E=j(C({id:Y,role:"combobox","aria-autocomplete":Se,"aria-haspopup":ir($,"listbox"),"aria-expanded":k,"aria-controls":$==null?void 0:$.id,"data-active-item":Ae||void 0,value:b},E),{ref:xe(h,E.ref),onChange:ge,onCompositionEnd:le,onMouseDown:He,onKeyDown:L,onBlur:fe}),E=Nr(j(C({store:n,focusable:o},E),{moveOnKeyPress:T=>or(D,T)?!1:(M&&B(!0),!0)})),E=Rr(C({store:n},E)),C({autoComplete:"off"},E)}),Is=J(function(t){const r=Ps(t);return Q(ys,r)});function Os(e={}){const t=ku(e);return O.jsx(Bu,{value:t,children:e.children})}var As="button";function Mn(e){if(!e.isTrusted)return!1;const t=e.currentTarget;return e.key==="Enter"?qe(t)||t.tagName==="SUMMARY"||t.tagName==="A":e.key===" "?qe(t)||t.tagName==="SUMMARY"||t.tagName==="INPUT"||t.tagName==="SELECT":!1}var Ds=Symbol("command"),dn=ee(function(t){var r=t,{clickOnEnter:n=!0,clickOnSpace:o=!0}=r,u=Z(r,["clickOnEnter","clickOnSpace"]);const s=c.useRef(null),[i,a]=c.useState(!1);c.useEffect(()=>{s.current&&a(qe(s.current))},[]);const[l,d]=c.useState(!1),v=c.useRef(!1),f=Ut(u),[x,p]=mr(u,Ds,!0),g=u.onKeyDown,D=W(I=>{g==null||g(I);const h=I.currentTarget;if(I.defaultPrevented||x||f||!Te(I)||Be(h)||h.isContentEditable)return;const _=n&&I.key==="Enter",F=o&&I.key===" ",A=I.key==="Enter"&&!n,V=I.key===" "&&!o;if(A||V){I.preventDefault();return}if(_||F){const w=Mn(I);if(_){if(!w){I.preventDefault();const M=I,z=Z(M,["view"]),B=()=>En(h,z);Bo()?ot(h,"keyup",B):queueMicrotask(B)}}else F&&(v.current=!0,w||(I.preventDefault(),d(!0)))}}),y=u.onKeyUp,E=W(I=>{if(y==null||y(I),I.defaultPrevented||x||f||I.metaKey)return;const h=o&&I.key===" ";if(v.current&&h&&(v.current=!1,!Mn(I))){I.preventDefault(),d(!1);const _=I.currentTarget,F=I,A=Z(F,["view"]);queueMicrotask(()=>En(_,A))}});return u=j(C(C({"data-active":l||void 0,type:i?"button":void 0},p),u),{ref:xe(s,u.ref),onKeyDown:D,onKeyUp:E}),u=Tt(u),u});J(function(t){const r=dn(t);return Q(As,r)});var jr="button",$r=ee(function(t){const r=c.useRef(null),n=lr(r,jr),[o,u]=c.useState(()=>!!n&&qe({tagName:n,type:t.type}));return c.useEffect(()=>{r.current&&u(qe(r.current))},[]),t=j(C({role:!o&&n!=="a"?"button":void 0},t),{ref:xe(r,t.ref)}),t=dn(t),t});J(function(t){const r=$r(t);return Q(jr,r)});var Ts="button",_s=Symbol("disclosure"),Kr=ee(function(t){var r=t,{store:n,toggleOnClick:o=!0}=r,u=Z(r,["store","toggleOnClick"]);const s=on();n=n||s,Oe(n,"Disclosure must receive a `store` prop or be wrapped in a DisclosureProvider component.");const i=c.useRef(null),[a,l]=c.useState(!1),d=n.useState("disclosureElement"),v=n.useState("open");c.useEffect(()=>{let E=d===i.current;d!=null&&d.isConnected||(n==null||n.setDisclosureElement(i.current),E=!0),l(v&&E)},[d,n,v]);const f=u.onClick,x=ae(o),[p,g]=mr(u,_s,!0),D=W(E=>{f==null||f(E),!E.defaultPrevented&&(p||x(E)&&(n==null||n.setDisclosureElement(E.currentTarget),n==null||n.toggle()))}),y=n.useState("contentElement");return u=j(C(C({"aria-expanded":a,"aria-controls":y==null?void 0:y.id},g),u),{ref:xe(i,u.ref),onClick:D}),u=$r(u),u});J(function(t){const r=Kr(t);return Q(Ts,r)});var Fs="button",Br=ee(function(t){var r=t,{store:n}=r,o=Z(r,["store"]);const u=It();n=n||u,Oe(n,"DialogDisclosure must receive a `store` prop or be wrapped in a DialogProvider component.");const s=n.useState("contentElement");return o=C({"aria-haspopup":ir(s,"dialog")},o),o=Kr(C({store:n},o)),o});J(function(t){const r=Br(t);return Q(Fs,r)});var Rs="button",ks=O.jsx("svg",{"aria-hidden":"true",display:"block",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,viewBox:"0 0 16 16",height:"1em",width:"1em",pointerEvents:"none",children:O.jsx("polyline",{points:"4,6 8,10 12,6"})}),Ms=ee(function(t){var r=t,{store:n}=r,o=Z(r,["store"]);const u=Ot();n=n||u,Oe(n,"ComboboxDisclosure must receive a `store` prop or be wrapped in a ComboboxProvider component.");const s=o.onMouseDown,i=W(v=>{s==null||s(v),v.preventDefault(),n==null||n.move(null)}),a=o.onClick,l=W(v=>{if(a==null||a(v),v.defaultPrevented||!n)return;const{baseElement:f}=n.getState();n.setDisclosureElement(f)}),d=n.useState("open");return o=j(C({children:ks,tabIndex:-1,"aria-label":d?"Hide popup":"Show popup","aria-expanded":d},o),{onMouseDown:i,onClick:l}),o=Br(C({store:n},o)),o}),Vs=J(function(t){const r=Ms(t);return Q(Rs,r)}),Ls=c.createContext(!1),Ns="span",js=O.jsx("svg",{display:"block",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,viewBox:"0 0 16 16",height:"1em",width:"1em",children:O.jsx("polyline",{points:"4,8 7,12 12,4"})});function $s(e){return e.checked?e.children||js:typeof e.children=="function"?e.children:null}var Hr=ee(function(t){var r=t,{store:n,checked:o}=r,u=Z(r,["store","checked"]);const s=c.useContext(Ls);o=o??s;const i=$s({checked:o,children:u.children});return u=j(C({"aria-hidden":!0},u),{children:i,style:C({width:"1em",height:"1em",pointerEvents:"none"},u.style)}),Ye(u)});J(function(t){const r=Hr(t);return Q(Ns,r)});var Ks="span",Bs=ee(function(t){var r=t,{store:n,checked:o}=r,u=Z(r,["store","checked"]);const s=c.useContext(Fr);return o=o??s,u=Hr(j(C({},u),{checked:o})),u}),Hs=J(function(t){const r=Bs(t);return Q(Ks,r)}),zs="span";function Vn(e){return nr(e).toLowerCase()}function Ws(e,t){const r=[];for(const n of t){let o=0;const u=n.length;for(;e.indexOf(n,o)!==-1;){const s=e.indexOf(n,o);s!==-1&&r.push([s,u]),o=s+1}}return r}function Us(e){return e.filter(([t,r],n,o)=>!o.some(([u,s],i)=>i!==n&&u<=t&&u+s>=t+r))}function qs(e){return e.sort(([t],[r])=>t-r)}function Ys(e,t){if(!e||!t)return e;const r=bu(t).filter(Boolean).map(Vn),n=[],o=(a,l=!1)=>O.jsx("span",{"data-autocomplete-value":l?"":void 0,"data-user-value":l?void 0:"",children:a},n.length),u=qs(Us(Ws(Vn(e),new Set(r))));if(!u.length)return n.push(o(e,!0)),n;const[s]=u[0];return[e.slice(0,s),...u.flatMap(([a,l],d)=>{var v;const f=e.slice(a,a+l),x=(v=u[d+1])==null?void 0:v[0],p=e.slice(a+l,x);return[f,p]})].forEach((a,l)=>{a&&n.push(o(a,l%2===0))}),n}var Gs=ee(function(t){var r=t,{store:n,value:o,userValue:u}=r,s=Z(r,["store","value","userValue"]);const i=cn();n=n||i;const a=c.useContext(_r),l=o??a,d=Ie(n,f=>u??(f==null?void 0:f.value)),v=c.useMemo(()=>{if(l)return d?Ys(l,d):l},[l,d]);return s=C({children:v},s),Ye(s)}),Xs=J(function(t){const r=Gs(t);return Q(zs,r)}),Js="div";function zr(e){const t=e.relatedTarget;return(t==null?void 0:t.nodeType)===Node.ELEMENT_NODE?t:null}function Zs(e){const t=zr(e);return t?ve(e.currentTarget,t):!1}var Bt=Symbol("composite-hover");function Qs(e){let t=zr(e);if(!t)return!1;do{if(Ve(t,Bt)&&t[Bt])return!0;t=t.parentElement}while(t);return!1}var Wr=ee(function(t){var r=t,{store:n,focusOnHover:o=!0,blurOnHoverEnd:u=!!o}=r,s=Z(r,["store","focusOnHover","blurOnHoverEnd"]);const i=pr();n=n||i,Oe(n,"CompositeHover must be wrapped in a Composite component.");const a=Zo(),l=s.onMouseMove,d=ae(o),v=W(D=>{if(l==null||l(D),!D.defaultPrevented&&a()&&d(D)){if(!Mr(D.currentTarget)){const y=n==null?void 0:n.getState().baseElement;y&&!Je(y)&&y.focus()}n==null||n.setActiveId(D.currentTarget.id)}}),f=s.onMouseLeave,x=ae(u),p=W(D=>{var y;f==null||f(D),!D.defaultPrevented&&a()&&(Zs(D)||Qs(D)||d(D)&&x(D)&&(n==null||n.setActiveId(null),(y=n==null?void 0:n.getState().baseElement)==null||y.focus()))}),g=c.useCallback(D=>{D&&(D[Bt]=!0)},[]);return s=j(C({},s),{ref:xe(g,s.ref),onMouseMove:v,onMouseLeave:p}),Ye(s)});Zt(J(function(t){const r=Wr(t);return Q(Js,r)}));var ei="div",Ur=ee(function(t){var r=t,{store:n,shouldRegisterItem:o=!0,getItem:u=rr,element:s}=r,i=Z(r,["store","shouldRegisterItem","getItem","element"]);const a=tu();n=n||a;const l=Qe(i.id),d=c.useRef(s);return c.useEffect(()=>{const v=d.current;if(!l||!v||!o)return;const f=u({id:l,element:v});return n==null?void 0:n.renderItem(f)},[l,o,u,n]),i=j(C({},i),{ref:xe(d,i.ref)}),Ye(i)});J(function(t){const r=Ur(t);return Q(ei,r)});var ti="button";function ni(e){return Nt(e)?!0:e.tagName==="INPUT"&&!qe(e)}function ri(e,t=!1){const r=e.clientHeight,{top:n}=e.getBoundingClientRect(),o=Math.max(r*.875,r-40)*1.5,u=t?r-o+n:o+n;return e.tagName==="HTML"?u+e.scrollTop:u}function oi(e,t=!1){const{top:r}=e.getBoundingClientRect();return t?r+e.clientHeight:r}function Ln(e,t,r,n=!1){var o;if(!t||!r)return;const{renderedItems:u}=t.getState(),s=Yt(e);if(!s)return;const i=ri(s,n);let a,l;for(let d=0;d<u.length;d+=1){const v=a;if(a=r(d),!a)break;if(a===v)continue;const f=(o=Ue(t,a))==null?void 0:o.element;if(!f)continue;const p=oi(f,n)-i,g=Math.abs(p);if(n&&p<=0||!n&&p>=0){l!==void 0&&l<g&&(a=v);break}l=g}return a}function ui(e,t){return Te(e)?!1:at(t,e.target)}var qr=ee(function(t){var r=t,{store:n,rowId:o,preventScrollOnKeyDown:u=!1,moveOnKeyPress:s=!0,tabbable:i=!1,getItem:a,"aria-setsize":l,"aria-posinset":d}=r,v=Z(r,["store","rowId","preventScrollOnKeyDown","moveOnKeyPress","tabbable","getItem","aria-setsize","aria-posinset"]);const f=pr();n=n||f;const x=Qe(v.id),p=c.useRef(null),g=c.useContext(su),y=Ut(v)&&!v.accessibleWhenDisabled,{rowId:E,baseElement:I,isActiveItem:h,ariaSetSize:_,ariaPosInSet:F,isTabbable:A}=yr(n,{rowId(b){if(o)return o;if(b&&g!=null&&g.baseElement&&g.baseElement===b.baseElement)return g.id},baseElement(b){return(b==null?void 0:b.baseElement)||void 0},isActiveItem(b){return!!b&&b.activeId===x},ariaSetSize(b){if(l!=null)return l;if(b&&g!=null&&g.ariaSetSize&&g.baseElement===b.baseElement)return g.ariaSetSize},ariaPosInSet(b){if(d!=null)return d;if(!b||!(g!=null&&g.ariaPosInSet)||g.baseElement!==b.baseElement)return;const N=b.renderedItems.filter(H=>H.rowId===E);return g.ariaPosInSet+N.findIndex(H=>H.id===x)},isTabbable(b){if(!(b!=null&&b.renderedItems.length))return!0;if(b.virtualFocus)return!1;if(i)return!0;if(b.activeId===null)return!1;const N=n==null?void 0:n.item(b.activeId);return N!=null&&N.disabled||!(N!=null&&N.element)?!0:b.activeId===x}}),V=c.useCallback(b=>{var N;const H=j(C({},b),{id:x||b.id,rowId:E,disabled:!!y,children:(N=b.element)==null?void 0:N.textContent});return a?a(H):H},[x,E,y,a]),w=v.onFocus,M=c.useRef(!1),z=W(b=>{if(w==null||w(b),b.defaultPrevented||cr(b)||!x||!n||ui(b,n))return;const{virtualFocus:N,baseElement:H}=n.getState();if(n.setActiveId(x),Nt(b.currentTarget)&&qu(b.currentTarget),!N||!Te(b)||ni(b.currentTarget)||!(H!=null&&H.isConnected))return;wt()&&b.currentTarget.hasAttribute("data-autofocus")&&b.currentTarget.scrollIntoView({block:"nearest",inline:"nearest"}),M.current=!0,b.relatedTarget===H||at(n,b.relatedTarget)?Yu(H):H.focus()}),B=v.onBlurCapture,m=W(b=>{if(B==null||B(b),b.defaultPrevented)return;const N=n==null?void 0:n.getState();N!=null&&N.virtualFocus&&M.current&&(M.current=!1,b.preventDefault(),b.stopPropagation())}),P=v.onKeyDown,R=ae(u),S=ae(s),k=W(b=>{if(P==null||P(b),b.defaultPrevented||!Te(b)||!n)return;const{currentTarget:N}=b,H=n.getState(),de=n.item(x),te=!!(de!=null&&de.rowId),re=H.orientation!=="horizontal",ne=H.orientation!=="vertical",be=()=>!!(te||ne||!H.baseElement||!Be(H.baseElement)),ge={ArrowUp:(te||re)&&n.up,ArrowRight:(te||ne)&&n.next,ArrowDown:(te||re)&&n.down,ArrowLeft:(te||ne)&&n.previous,Home:()=>{if(be())return!te||b.ctrlKey?n==null?void 0:n.first():n==null?void 0:n.previous(-1)},End:()=>{if(be())return!te||b.ctrlKey?n==null?void 0:n.last():n==null?void 0:n.next(-1)},PageUp:()=>Ln(N,n,n==null?void 0:n.up,!0),PageDown:()=>Ln(N,n,n==null?void 0:n.down)}[b.key];if(ge){if(Nt(N)){const le=jt(N),K=ne&&b.key==="ArrowLeft",ye=ne&&b.key==="ArrowRight",ie=re&&b.key==="ArrowUp",Me=re&&b.key==="ArrowDown";if(ye||Me){const{length:He}=jo(N);if(le.end!==He)return}else if((K||ie)&&le.start!==0)return}const me=ge();if(R(b)||me!==void 0){if(!S(b))return;b.preventDefault(),n.move(me)}}}),$=c.useMemo(()=>({id:x,baseElement:I}),[x,I]);return v=Pe(v,b=>O.jsx(uu.Provider,{value:$,children:b}),[$]),v=j(C({id:x,"data-active-item":h||void 0},v),{ref:xe(p,v.ref),tabIndex:A?v.tabIndex:-1,onFocus:z,onBlurCapture:m,onKeyDown:k}),v=dn(v),v=Ur(j(C({store:n},v),{getItem:V,shouldRegisterItem:x?v.shouldRegisterItem:!1})),Ye(j(C({},v),{"aria-setsize":_,"aria-posinset":F}))});Zt(J(function(t){const r=qr(t);return Q(ti,r)}));var si="div";function ii(e,t){if(t!=null)return e==null?!1:Array.isArray(e)?e.includes(t):e===t}function ai(e){var t;return(t={menu:"menuitem",listbox:"option",tree:"treeitem"}[e])!=null?t:"option"}var ci=ee(function(t){var r=t,{store:n,value:o,hideOnClick:u,setValueOnClick:s,selectValueOnClick:i=!0,resetValueOnSelect:a,focusOnHover:l=!1,moveOnKeyPress:d=!0,getItem:v}=r,f=Z(r,["store","value","hideOnClick","setValueOnClick","selectValueOnClick","resetValueOnSelect","focusOnHover","moveOnKeyPress","getItem"]),x;const p=cn();n=n||p,Oe(n,"ComboboxItem must be wrapped in a ComboboxList or ComboboxPopover component.");const{resetValueOnSelectState:g,multiSelectable:D,selected:y}=yr(n,{resetValueOnSelectState:"resetValueOnSelect",multiSelectable(m){return Array.isArray(m.selectedValue)},selected(m){return ii(m.selectedValue,o)}}),E=c.useCallback(m=>{const P=j(C({},m),{value:o});return v?v(P):P},[o,v]);s=s??!D,u=u??(o!=null&&!D);const I=f.onClick,h=ae(s),_=ae(i),F=ae((x=a??g)!=null?x:D),A=ae(u),V=W(m=>{I==null||I(m),!m.defaultPrevented&&(Wo(m)||zo(m)||(o!=null&&(_(m)&&(F(m)&&(n==null||n.resetValue()),n==null||n.setSelectedValue(P=>Array.isArray(P)?P.includes(o)?P.filter(R=>R!==o):[...P,o]:o)),h(m)&&(n==null||n.setValue(o))),A(m)&&(n==null||n.hide())))}),w=f.onKeyDown,M=W(m=>{if(w==null||w(m),m.defaultPrevented)return;const P=n==null?void 0:n.getState().baseElement;if(!P||Je(P))return;(m.key.length===1||m.key==="Backspace"||m.key==="Delete")&&(queueMicrotask(()=>P.focus()),Be(P)&&(n==null||n.setValue(P.value)))});D&&y!=null&&(f=C({"aria-selected":y},f)),f=Pe(f,m=>O.jsx(_r.Provider,{value:o,children:O.jsx(Fr.Provider,{value:y??!1,children:m})}),[o,y]);const z=c.useContext(Tr);f=j(C({role:ai(z),children:o},f),{onClick:V,onKeyDown:M});const B=ae(d);return f=qr(j(C({store:n},f),{getItem:E,moveOnKeyPress:m=>{if(!B(m))return!1;const P=new Event("combobox-item-move"),R=n==null?void 0:n.getState().baseElement;return R==null||R.dispatchEvent(P),!0}})),f=Wr(C({store:n,focusOnHover:l},f)),f}),Nn=Zt(J(function(t){const r=ci(t);return Q(si,r)})),li="div";function jn(e,t){const r=setTimeout(t,e);return()=>clearTimeout(r)}function fi(e){let t=requestAnimationFrame(()=>{t=requestAnimationFrame(e)});return()=>cancelAnimationFrame(t)}function $n(...e){return e.join(", ").split(", ").reduce((t,r)=>{const n=r.endsWith("ms")?1:1e3,o=Number.parseFloat(r||"0s")*n;return o>t?o:t},0)}function mn(e,t,r){return!r&&t!==!1&&(!e||!!t)}var pn=ee(function(t){var r=t,{store:n,alwaysVisible:o}=r,u=Z(r,["store","alwaysVisible"]);const s=on();n=n||s,Oe(n,"DisclosureContent must receive a `store` prop or be wrapped in a DisclosureProvider component.");const i=c.useRef(null),a=Qe(u.id),[l,d]=c.useState(null),v=n.useState("open"),f=n.useState("mounted"),x=n.useState("animated"),p=n.useState("contentElement"),g=Ie(n.disclosure,"contentElement");X(()=>{i.current&&(n==null||n.setContentElement(i.current))},[n]),X(()=>{let I;return n==null||n.setState("animated",h=>(I=h,!0)),()=>{I!==void 0&&(n==null||n.setState("animated",I))}},[n]),X(()=>{if(x){if(!(p!=null&&p.isConnected)){d(null);return}return fi(()=>{d(v?"enter":f?"leave":null)})}},[x,p,v,f]),X(()=>{if(!n||!x||!l||!p)return;const I=()=>n==null?void 0:n.setState("animating",!1),h=()=>St.flushSync(I);if(l==="leave"&&v||l==="enter"&&!v)return;if(typeof x=="number")return jn(x,h);const{transitionDuration:_,animationDuration:F,transitionDelay:A,animationDelay:V}=getComputedStyle(p),{transitionDuration:w="0",animationDuration:M="0",transitionDelay:z="0",animationDelay:B="0"}=g?getComputedStyle(g):{},m=$n(A,V,z,B),P=$n(_,F,w,M),R=m+P;if(!R){l==="enter"&&n.setState("animated",!1),I();return}const S=1e3/60,k=Math.max(R-S,0);return jn(k,h)},[n,x,p,g,v,l]),u=Pe(u,I=>O.jsx(sn,{value:n,children:I}),[n]);const D=mn(f,u.hidden,o),y=u.style,E=c.useMemo(()=>D?j(C({},y),{display:"none"}):y,[D,y]);return u=j(C({id:a,"data-open":v||void 0,"data-enter":l==="enter"||void 0,"data-leave":l==="leave"||void 0,hidden:D},u),{ref:xe(a?n.setContentElement:null,i,u.ref),style:E}),Ye(u)}),di=J(function(t){const r=pn(t);return Q(li,r)});J(function(t){var r=t,{unmountOnHide:n}=r,o=Z(r,["unmountOnHide"]);const u=on(),s=o.store||u;return Ie(s,a=>!n||(a==null?void 0:a.mounted))===!1?null:O.jsx(di,C({},o))});var mi="div",Yr=ee(function(t){var r=t,{store:n,alwaysVisible:o}=r,u=Z(r,["store","alwaysVisible"]);const s=cn(!0),i=Ku();n=n||i;const a=!!n&&n===s;Oe(n,"ComboboxList must receive a `store` prop or be wrapped in a ComboboxProvider component.");const l=c.useRef(null),d=Qe(u.id),v=n.useState("mounted"),f=mn(v,u.hidden,o),x=f?j(C({},u.style),{display:"none"}):u.style,p=n.useState(F=>Array.isArray(F.selectedValue)),g=Xo(l,"role",u.role),y=(g==="listbox"||g==="tree"||g==="grid")&&p||void 0,[E,I]=c.useState(!1),h=n.useState("contentElement");X(()=>{if(!v)return;const F=l.current;if(!F||h!==F)return;const A=()=>{I(!!F.querySelector("[role='listbox']"))},V=new MutationObserver(A);return V.observe(F,{subtree:!0,childList:!0,attributeFilter:["role"]}),A(),()=>V.disconnect()},[v,h]),E||(u=C({role:"listbox","aria-multiselectable":y},u)),u=Pe(u,F=>O.jsx(Hu,{value:n,children:O.jsx(Tr.Provider,{value:g,children:F})}),[n,g]);const _=d&&(!s||!a)?n.setContentElement:null;return u=j(C({id:d,hidden:f},u),{ref:xe(_,l,u.ref),style:x}),Ye(u)});J(function(t){const r=Yr(t);return Q(mi,r)});function Gr(e,...t){if(!e)return!1;const r=e.getAttribute("data-backdrop");return r==null?!1:r===""||r==="true"||!t.length?!0:t.some(n=>r===n)}var kt=new WeakMap;function bt(e,t,r){kt.has(e)||kt.set(e,new Map);const n=kt.get(e),o=n.get(t);if(!o)return n.set(t,r()),()=>{var i;(i=n.get(t))==null||i(),n.delete(t)};const u=r(),s=()=>{u(),o(),n.delete(t)};return n.set(t,s),()=>{n.get(t)===s&&(u(),n.set(t,o))}}function vn(e,t,r){return bt(e,t,()=>{const o=e.getAttribute(t);return e.setAttribute(t,r),()=>{o==null?e.removeAttribute(t):e.setAttribute(t,o)}})}function Ze(e,t,r){return bt(e,t,()=>{const o=t in e,u=e[t];return e[t]=r,()=>{o?e[t]=u:delete e[t]}})}function Ht(e,t){return e?bt(e,"style",()=>{const n=e.style.cssText;return Object.assign(e.style,t),()=>{e.style.cssText=n}}):()=>{}}function pi(e,t,r){return e?bt(e,t,()=>{const o=e.style.getPropertyValue(t);return e.style.setProperty(t,r),()=>{o?e.style.setProperty(t,o):e.style.removeProperty(t)}}):()=>{}}var vi=["SCRIPT","STYLE"];function zt(e){return`__ariakit-dialog-snapshot-${e}`}function bi(e,t){const r=ue(t),n=zt(e);if(!r.body[n])return!0;do{if(t===r.body)return!1;if(t[n])return!0;if(!t.parentElement)return!1;t=t.parentElement}while(!0)}function gi(e,t,r){return vi.includes(t.tagName)||!bi(e,t)?!1:!r.some(n=>n&&ve(t,n))}function bn(e,t,r,n){for(let o of t){if(!(o!=null&&o.isConnected))continue;const u=t.some(a=>!a||a===o?!1:a.contains(o)),s=ue(o),i=o;for(;o.parentElement&&o!==s.body;){if(n==null||n(o.parentElement,i),!u)for(const a of o.parentElement.children)gi(e,a,t)&&r(a,i);o=o.parentElement}}}function hi(e,t){const{body:r}=ue(t[0]),n=[];return bn(e,t,u=>{n.push(Ze(u,zt(e),!0))}),_e(Ze(r,zt(e),!0),()=>{for(const u of n)u()})}function ut(e="",t=!1){return`__ariakit-dialog-${t?"ancestor":"outside"}${e?`-${e}`:""}`}function xi(e,t=""){return _e(Ze(e,ut(),!0),Ze(e,ut(t),!0))}function Xr(e,t=""){return _e(Ze(e,ut("",!0),!0),Ze(e,ut(t,!0),!0))}function gn(e,t){const r=ut(t,!0);if(e[r])return!0;const n=ut(t);do{if(e[n])return!0;if(!e.parentElement)return!1;e=e.parentElement}while(!0)}function Kn(e,t){const r=[],n=t.map(u=>u==null?void 0:u.id);return bn(e,t,u=>{Gr(u,...n)||r.unshift(xi(u,e))},(u,s)=>{s.hasAttribute("data-dialog")&&s.id!==e||r.unshift(Xr(u,e))}),()=>{for(const u of r)u()}}var Si="div",yi=["a","button","details","dialog","div","form","h1","h2","h3","h4","h5","h6","header","img","input","label","li","nav","ol","p","section","select","span","summary","textarea","ul","svg"];ee(function(t){return t});var Wt=J(function(t){return Q(Si,t)});Object.assign(Wt,yi.reduce((e,t)=>(e[t]=J(function(n){return Q(t,n)}),e),{}));function Ei({store:e,backdrop:t,alwaysVisible:r,hidden:n}){const o=c.useRef(null),u=Iu({disclosure:e}),s=Ie(e,"contentElement");c.useEffect(()=>{const l=o.current,d=s;l&&d&&(l.style.zIndex=getComputedStyle(d).zIndex)},[s]),X(()=>{const l=s==null?void 0:s.id;if(!l)return;const d=o.current;if(d)return Xr(d,l)},[s]);const i=pn({ref:o,store:u,role:"presentation","data-backdrop":(s==null?void 0:s.id)||"",alwaysVisible:r,hidden:n??void 0,style:{position:"fixed",top:0,right:0,bottom:0,left:0}});if(!t)return null;if(c.isValidElement(t))return O.jsx(Wt,j(C({},i),{render:t}));const a=typeof t!="boolean"?t:"div";return O.jsx(Wt,j(C({},i),{render:O.jsx(a,{})}))}function Ci(e,...t){if(!e)return!1;const r=e.getAttribute("data-focus-trap");return r==null?!1:t.length?r===""?!1:t.some(n=>r===n):!0}function wi(e){return vn(e,"aria-hidden","true")}function Jr(){return"inert"in HTMLElement.prototype}function Zr(e,t){if(!("style"in e))return it;if(Jr())return Ze(e,"inert",!0);const n=Dt(e,!0).map(o=>{if(t!=null&&t.some(s=>s&&ve(s,o)))return it;const u=bt(o,"focus",()=>(o.focus=it,()=>{delete o.focus}));return _e(vn(o,"tabindex","-1"),u)});return _e(...n,wi(e),Ht(e,{pointerEvents:"none",userSelect:"none",cursor:"default"}))}function Pi(e,t){const r=[],n=t.map(u=>u==null?void 0:u.id);return bn(e,t,u=>{Gr(u,...n)||Ci(u,...n)||r.unshift(Zr(u,t))},u=>{u.hasAttribute("role")&&(t.some(s=>s&&ve(s,u))||r.unshift(vn(u,"role","none")))}),()=>{for(const u of r)u()}}function Ii({attribute:e,contentId:t,contentElement:r,enabled:n}){const[o,u]=fr(),s=c.useCallback(()=>{if(!n||!r)return!1;const{body:i}=ue(r),a=i.getAttribute(e);return!a||a===t},[o,n,r,e,t]);return c.useEffect(()=>{if(!n||!t||!r)return;const{body:i}=ue(r);if(s())return i.setAttribute(e,t),()=>i.removeAttribute(e);const a=new MutationObserver(()=>St.flushSync(u));return a.observe(i,{attributeFilter:[e]}),()=>a.disconnect()},[o,n,t,r,s,e]),s}function Oi(e){const t=e.getBoundingClientRect().left;return Math.round(t)+e.scrollLeft?"paddingLeft":"paddingRight"}function Ai(e,t,r){const n=Ii({attribute:"data-dialog-prevent-body-scroll",contentElement:e,contentId:t,enabled:r});c.useEffect(()=>{if(!n()||!e)return;const o=ue(e),u=ur(e),{documentElement:s,body:i}=o,a=s.style.getPropertyValue("--scrollbar-width"),l=a?Number.parseInt(a):u.innerWidth-s.clientWidth,d=()=>pi(s,"--scrollbar-width",`${l}px`),v=Oi(s),f=()=>Ht(i,{overflow:"hidden",[v]:`${l}px`}),x=()=>{var g,D;const{scrollX:y,scrollY:E,visualViewport:I}=u,h=(g=I==null?void 0:I.offsetLeft)!=null?g:0,_=(D=I==null?void 0:I.offsetTop)!=null?D:0,F=Ht(i,{position:"fixed",overflow:"hidden",top:`${-(E-Math.floor(_))}px`,left:`${-(y-Math.floor(h))}px`,right:"0",[v]:`${l}px`});return()=>{F(),u.scrollTo({left:y,top:E,behavior:"instant"})}},p=Gt()&&!Ho();return _e(d(),p?x():f())},[n,e])}var Bn=c.createContext({});function Di(e){const t=c.useContext(Bn),[r,n]=c.useState([]),o=c.useCallback(i=>{var a;return n(l=>[...l,i]),_e((a=t.add)==null?void 0:a.call(t,i),()=>{n(l=>l.filter(d=>d!==i))})},[t]);X(()=>we(e,["open","contentElement"],i=>{var a;if(i.open&&i.contentElement)return(a=t.add)==null?void 0:a.call(t,e)}),[e,t]);const u=c.useMemo(()=>({store:e,add:o}),[e,o]);return{wrapElement:c.useCallback(i=>O.jsx(Bn.Provider,{value:u,children:i}),[u]),nestedDialogs:r}}function Ti(e){const t=c.useRef();return c.useEffect(()=>{if(!e){t.current=null;return}return Re("mousedown",n=>{t.current=n.target},!0)},[e]),t}function _i(e){return e.tagName==="HTML"?!0:ve(ue(e).body,e)}function Fi(e,t){if(!e)return!1;if(ve(e,t))return!0;const r=t.getAttribute("aria-activedescendant");if(r){const n=ue(e).getElementById(r);if(n)return ve(e,n)}return!1}function Ri(e,t){if(!("clientY"in e))return!1;const r=t.getBoundingClientRect();return r.width===0||r.height===0?!1:r.top<=e.clientY&&e.clientY<=r.top+r.height&&r.left<=e.clientX&&e.clientX<=r.left+r.width}function Mt({store:e,type:t,listener:r,capture:n,domReady:o}){const u=W(r),s=Ie(e,"open"),i=c.useRef(!1);X(()=>{if(!s||!o)return;const{contentElement:a}=e.getState();if(!a)return;const l=()=>{i.current=!0};return a.addEventListener("focusin",l,!0),()=>a.removeEventListener("focusin",l,!0)},[e,s,o]),c.useEffect(()=>s?Re(t,l=>{const{contentElement:d,disclosureElement:v}=e.getState(),f=l.target;!d||!f||!_i(f)||ve(d,f)||Fi(v,f)||f.hasAttribute("data-focus-trap")||Ri(l,d)||i.current&&!gn(f,d.id)||is(f)||u(l)},n):void 0,[s,n])}function Vt(e,t){return typeof e=="function"?e(t):!!e}function ki(e,t,r){const n=Ie(e,"open"),o=Ti(n),u={store:e,domReady:r,capture:!0};Mt(j(C({},u),{type:"click",listener:s=>{const{contentElement:i}=e.getState(),a=o.current;a&&sr(a)&&gn(a,i==null?void 0:i.id)&&Vt(t,s)&&e.hide()}})),Mt(j(C({},u),{type:"focusin",listener:s=>{const{contentElement:i}=e.getState();i&&s.target!==ue(i)&&Vt(t,s)&&e.hide()}})),Mt(j(C({},u),{type:"contextmenu",listener:s=>{Vt(t,s)&&e.hide()}}))}function Mi(e,t){const n=ue(e).createElement("button");return n.type="button",n.tabIndex=-1,n.textContent="Dismiss popup",Object.assign(n.style,{border:"0px",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0px",position:"absolute",whiteSpace:"nowrap",width:"1px"}),n.addEventListener("click",t),e.prepend(n),()=>{n.removeEventListener("click",t),n.remove()}}var Vi="div",Qr=ee(function(t){var r=t,{autoFocusOnShow:n=!0}=r,o=Z(r,["autoFocusOnShow"]);return o=Pe(o,u=>O.jsx(kr.Provider,{value:n,children:u}),[n]),o});J(function(t){const r=Qr(t);return Q(Vi,r)});var Hn=c.createContext(0);function Li({level:e,children:t}){const r=c.useContext(Hn),n=Math.max(Math.min(e||r+1,6),1);return O.jsx(Hn.Provider,{value:n,children:t})}var Ni="span",eo=ee(function(t){return t=j(C({},t),{style:C({border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"},t.style)}),t});J(function(t){const r=eo(t);return Q(Ni,r)});var ji="span",$i=ee(function(t){return t=j(C({"data-focus-trap":"",tabIndex:0,"aria-hidden":!0},t),{style:C({position:"fixed",top:0,left:0},t.style)}),t=eo(t),t}),xt=J(function(t){const r=$i(t);return Q(ji,r)}),zn=c.createContext(null),Ki="div";function Bi(e){return ue(e).body}function Hi(e,t){return t?typeof t=="function"?t(e):t:ue(e).createElement("div")}function zi(e="id"){return`${e?`${e}-`:""}${Math.random().toString(36).slice(2,8)}`}function We(e){queueMicrotask(()=>{e==null||e.focus()})}var to=ee(function(t){var r=t,{preserveTabOrder:n,preserveTabOrderAnchor:o,portalElement:u,portalRef:s,portal:i=!0}=r,a=Z(r,["preserveTabOrder","preserveTabOrderAnchor","portalElement","portalRef","portal"]);const l=c.useRef(null),d=xe(l,a.ref),v=c.useContext(zn),[f,x]=c.useState(null),[p,g]=c.useState(null),D=c.useRef(null),y=c.useRef(null),E=c.useRef(null),I=c.useRef(null);return X(()=>{const h=l.current;if(!h||!i){x(null);return}const _=Hi(h,u);if(!_){x(null);return}const F=_.isConnected;if(F||(v||Bi(h)).appendChild(_),_.id||(_.id=h.id?`portal/${h.id}`:zi()),x(_),Lt(s,_),!F)return()=>{_.remove(),Lt(s,null)}},[i,u,v,s]),X(()=>{if(!i||!n||!o)return;const _=ue(o).createElement("span");return _.style.position="fixed",o.insertAdjacentElement("afterend",_),g(_),()=>{_.remove(),g(null)}},[i,n,o]),c.useEffect(()=>{if(!f||!n)return;let h=0;const _=F=>{if(!Xe(F))return;const A=F.type==="focusin";if(cancelAnimationFrame(h),A)return rs(f);h=requestAnimationFrame(()=>{ns(f,!0)})};return f.addEventListener("focusin",_,!0),f.addEventListener("focusout",_,!0),()=>{cancelAnimationFrame(h),f.removeEventListener("focusin",_,!0),f.removeEventListener("focusout",_,!0)}},[f,n]),a=Pe(a,h=>{if(h=O.jsx(zn.Provider,{value:f||v,children:h}),!i)return h;if(!f)return O.jsx("span",{ref:d,id:a.id,style:{position:"fixed"},hidden:!0});h=O.jsxs(O.Fragment,{children:[n&&f&&O.jsx(xt,{ref:y,"data-focus-trap":a.id,className:"__focus-trap-inner-before",onFocus:F=>{Xe(F,f)?We(Ft()):We(D.current)}}),h,n&&f&&O.jsx(xt,{ref:E,"data-focus-trap":a.id,className:"__focus-trap-inner-after",onFocus:F=>{Xe(F,f)?We(An()):We(I.current)}})]}),f&&(h=St.createPortal(h,f));let _=O.jsxs(O.Fragment,{children:[n&&f&&O.jsx(xt,{ref:D,"data-focus-trap":a.id,className:"__focus-trap-outer-before",onFocus:F=>{!(F.relatedTarget===I.current)&&Xe(F,f)?We(y.current):We(An())}}),n&&O.jsx("span",{"aria-owns":f==null?void 0:f.id,style:{position:"fixed"}}),n&&f&&O.jsx(xt,{ref:I,"data-focus-trap":a.id,className:"__focus-trap-outer-after",onFocus:F=>{if(Xe(F,f))We(E.current);else{const A=Ft();if(A===y.current){requestAnimationFrame(()=>{var V;return(V=Ft())==null?void 0:V.focus()});return}We(A)}}})]});return p&&n&&(_=St.createPortal(_,p)),O.jsxs(O.Fragment,{children:[_,h]})},[f,v,i,a.id,n,p]),a=j(C({},a),{ref:d}),a});J(function(t){const r=to(t);return Q(Ki,r)});var Wi="div",Wn=wt();function Ui(e){const t=Ke();return!t||e&&ve(e,t)?!1:!!ke(t)}function Un(e,t=!1){if(!e)return null;const r="current"in e?e.current:e;return r?t?ke(r)?r:null:r:null}var no=ee(function(t){var r=t,{store:n,open:o,onClose:u,focusable:s=!0,modal:i=!0,portal:a=!!i,backdrop:l=!!i,hideOnEscape:d=!0,hideOnInteractOutside:v=!0,getPersistentElements:f,preventBodyScroll:x=!!i,autoFocusOnShow:p=!0,autoFocusOnHide:g=!0,initialFocus:D,finalFocus:y,unmountOnHide:E,unstable_treeSnapshotKey:I}=r,h=Z(r,["store","open","onClose","focusable","modal","portal","backdrop","hideOnEscape","hideOnInteractOutside","getPersistentElements","preventBodyScroll","autoFocusOnShow","autoFocusOnHide","initialFocus","finalFocus","unmountOnHide","unstable_treeSnapshotKey"]);const _=It(),F=c.useRef(null),A=Ou({store:n||_,open:o,setOpen(L){if(L)return;const q=F.current;if(!q)return;const fe=new Event("close",{bubbles:!1,cancelable:!0});u&&q.addEventListener("close",u,{once:!0}),q.dispatchEvent(fe),fe.defaultPrevented&&A.setOpen(!0)}}),{portalRef:V,domReady:w}=dr(a,h.portalRef),M=h.preserveTabOrder,z=Ie(A,L=>M&&!i&&L.mounted),B=Qe(h.id),m=Ie(A,"open"),P=Ie(A,"mounted"),R=Ie(A,"contentElement"),S=mn(P,h.hidden,h.alwaysVisible);Ai(R,B,x&&!S),ki(A,v,w);const{wrapElement:k,nestedDialogs:$}=Di(A);h=Pe(h,k,[k]),X(()=>{if(!m)return;const L=F.current,q=Ke(L,!0);q&&q.tagName!=="BODY"&&(L&&ve(L,q)||A.setDisclosureElement(q))},[A,m]),Wn&&c.useEffect(()=>{if(!P)return;const{disclosureElement:L}=A.getState();if(!L||!qe(L))return;const q=()=>{let fe=!1;const Y=()=>{fe=!0},Se={capture:!0,once:!0};L.addEventListener("focusin",Y,Se),ot(L,"mouseup",()=>{L.removeEventListener("focusin",Y,!0),!fe&&Vr(L)})};return L.addEventListener("mousedown",q),()=>{L.removeEventListener("mousedown",q)}},[A,P]),c.useEffect(()=>{if(!P||!w)return;const L=F.current;if(!L)return;const q=ur(L),fe=q.visualViewport||q,Y=()=>{var Se,Ae;const T=(Ae=(Se=q.visualViewport)==null?void 0:Se.height)!=null?Ae:q.innerHeight;L.style.setProperty("--dialog-viewport-height",`${T}px`)};return Y(),fe.addEventListener("resize",Y),()=>{fe.removeEventListener("resize",Y)}},[P,w]),c.useEffect(()=>{if(!i||!P||!w)return;const L=F.current;if(!(!L||L.querySelector("[data-dialog-dismiss]")))return Mi(L,A.hide)},[A,i,P,w]),X(()=>{if(!Jr()||m||!P||!w)return;const L=F.current;if(L)return Zr(L)},[m,P,w]);const b=m&&w;X(()=>{if(!B||!b)return;const L=F.current;return hi(B,[L])},[B,b,I]);const N=W(f);X(()=>{if(!B||!b)return;const{disclosureElement:L}=A.getState(),q=F.current,fe=N()||[],Y=[q,...fe,...$.map(Se=>Se.getState().contentElement)];return i?_e(Kn(B,Y),Pi(B,Y)):Kn(B,[L,...Y])},[B,A,b,N,$,i,I]);const H=!!p,de=ae(p),[te,re]=c.useState(!1);c.useEffect(()=>{if(!m||!H||!w||!(R!=null&&R.isConnected))return;const L=Un(D,!0)||R.querySelector("[data-autofocus=true],[autofocus]")||Ju(R,!0,a&&z)||R,q=ke(L);de(q?L:null)&&(re(!0),queueMicrotask(()=>{L.focus(),Wn&&L.scrollIntoView({block:"nearest",inline:"nearest"})}))},[m,H,w,R,D,a,z,de]);const ne=!!g,be=ae(g),[Fe,ge]=c.useState(!1);c.useEffect(()=>{if(m)return ge(!0),()=>ge(!1)},[m]);const me=c.useCallback((L,q=!0)=>{const{disclosureElement:fe}=A.getState();if(Ui(L))return;let Y=Un(y)||fe;if(Y!=null&&Y.id){const Ae=ue(Y),T=`[aria-activedescendant="${Y.id}"]`,U=Ae.querySelector(T);U&&(Y=U)}if(Y&&!ke(Y)){const Ae=Y.closest("[data-dialog]");if(Ae!=null&&Ae.id){const T=ue(Ae),U=`[aria-controls~="${Ae.id}"]`,se=T.querySelector(U);se&&(Y=se)}}const Se=Y&&ke(Y);if(!Se&&q){requestAnimationFrame(()=>me(L,!1));return}be(Se?Y:null)&&Se&&(Y==null||Y.focus())},[A,y,be]),le=c.useRef(!1);X(()=>{if(m||!Fe||!ne)return;const L=F.current;le.current=!0,me(L)},[m,Fe,w,ne,me]),c.useEffect(()=>{if(!Fe||!ne)return;const L=F.current;return()=>{if(le.current){le.current=!1;return}me(L)}},[Fe,ne,me]);const K=ae(d);c.useEffect(()=>!w||!P?void 0:Re("keydown",q=>{if(q.key!=="Escape"||q.defaultPrevented)return;const fe=F.current;if(!fe||gn(fe))return;const Y=q.target;if(!Y)return;const{disclosureElement:Se}=A.getState();!!(Y.tagName==="BODY"||ve(fe,Y)||!Se||ve(Se,Y))&&K(q)&&A.hide()},!0),[A,w,P,K]),h=Pe(h,L=>O.jsx(Li,{level:i?1:void 0,children:L}),[i]);const ye=h.hidden,ie=h.alwaysVisible;h=Pe(h,L=>l?O.jsxs(O.Fragment,{children:[O.jsx(Ei,{store:A,backdrop:l,hidden:ye,alwaysVisible:ie}),L]}):L,[A,l,ye,ie]);const[Me,He]=c.useState(),[Le,ze]=c.useState();return h=Pe(h,L=>O.jsx(sn,{value:A,children:O.jsx(Nu.Provider,{value:He,children:O.jsx(ju.Provider,{value:ze,children:L})})}),[A]),h=j(C({id:B,"data-dialog":"",role:"dialog",tabIndex:s?-1:void 0,"aria-labelledby":Me,"aria-describedby":Le},h),{ref:xe(F,h.ref)}),h=Qr(j(C({},h),{autoFocusOnShow:te})),h=pn(C({store:A},h)),h=Tt(j(C({},h),{focusable:s})),h=to(j(C({portal:a},h),{portalRef:V,preserveTabOrder:z})),h});function hn(e,t=It){return J(function(n){const o=t(),u=n.store||o;return Ie(u,i=>!n.unmountOnHide||(i==null?void 0:i.mounted)||!!n.open)?O.jsx(e,C({},n)):null})}hn(J(function(t){const r=no(t);return Q(Wi,r)}),It);var qi="div";function qn(e=0,t=0,r=0,n=0){if(typeof DOMRect=="function")return new DOMRect(e,t,r,n);const o={x:e,y:t,width:r,height:n,top:t,right:e+r,bottom:t+n,left:e};return j(C({},o),{toJSON:()=>o})}function Yi(e){if(!e)return qn();const{x:t,y:r,width:n,height:o}=e;return qn(t,r,n,o)}function Gi(e,t){return{contextElement:e||void 0,getBoundingClientRect:()=>{const n=e,o=t==null?void 0:t(n);return o||!n?Yi(o):n.getBoundingClientRect()}}}function Xi(e){return/^(?:top|bottom|left|right)(?:-(?:start|end))?$/.test(e)}function Yn(e){const t=window.devicePixelRatio||1;return Math.round(e*t)/t}function Ji(e,t){return co(({placement:r})=>{var n;const o=((e==null?void 0:e.clientHeight)||0)/2,u=typeof t.gutter=="number"?t.gutter+o:(n=t.gutter)!=null?n:o;return{crossAxis:!!r.split("-")[1]?void 0:t.shift,mainAxis:u,alignmentAxis:t.shift}})}function Zi(e){if(e.flip===!1)return;const t=typeof e.flip=="string"?e.flip.split(" "):void 0;return Oe(!t||t.every(Xi),"`flip` expects a spaced-delimited list of placements"),lo({padding:e.overflowPadding,fallbackPlacements:t})}function Qi(e){if(!(!e.slide&&!e.overlap))return fo({mainAxis:e.slide,crossAxis:e.overlap,padding:e.overflowPadding,limiter:mo()})}function ea(e){return po({padding:e.overflowPadding,apply({elements:t,availableWidth:r,availableHeight:n,rects:o}){const u=t.floating,s=Math.round(o.reference.width);r=Math.floor(r),n=Math.floor(n),u.style.setProperty("--popover-anchor-width",`${s}px`),u.style.setProperty("--popover-available-width",`${r}px`),u.style.setProperty("--popover-available-height",`${n}px`),e.sameWidth&&(u.style.width=`${s}px`),e.fitViewport&&(u.style.maxWidth=`${r}px`,u.style.maxHeight=`${n}px`)}})}function ta(e,t){if(e)return vo({element:e,padding:t.arrowPadding})}var ro=ee(function(t){var r=t,{store:n,modal:o=!1,portal:u=!!o,preserveTabOrder:s=!0,autoFocusOnShow:i=!0,wrapperProps:a,fixed:l=!1,flip:d=!0,shift:v=0,slide:f=!0,overlap:x=!1,sameWidth:p=!1,fitViewport:g=!1,gutter:D,arrowPadding:y=4,overflowPadding:E=8,getAnchorRect:I,updatePosition:h}=r,_=Z(r,["store","modal","portal","preserveTabOrder","autoFocusOnShow","wrapperProps","fixed","flip","shift","slide","overlap","sameWidth","fitViewport","gutter","arrowPadding","overflowPadding","getAnchorRect","updatePosition"]);const F=an();n=n||F,Oe(n,"Popover must receive a `store` prop or be wrapped in a PopoverProvider component.");const A=n.useState("arrowElement"),V=n.useState("anchorElement"),w=n.useState("disclosureElement"),M=n.useState("popoverElement"),z=n.useState("contentElement"),B=n.useState("placement"),m=n.useState("mounted"),P=n.useState("rendered"),R=c.useRef(null),[S,k]=c.useState(!1),{portalRef:$,domReady:b}=dr(u,_.portalRef),N=W(I),H=W(h),de=!!h;X(()=>{if(!(M!=null&&M.isConnected))return;M.style.setProperty("--popover-overflow-padding",`${E}px`);const re=Gi(V,N),ne=async()=>{if(!m)return;A||(R.current=R.current||document.createElement("div"));const ge=A||R.current,me=[Ji(ge,{gutter:D,shift:v}),Zi({flip:d,overflowPadding:E}),Qi({slide:f,shift:v,overlap:x,overflowPadding:E}),ta(ge,{arrowPadding:y}),ea({sameWidth:p,fitViewport:g,overflowPadding:E})],le=await ao(re,M,{placement:B,strategy:l?"fixed":"absolute",middleware:me});n==null||n.setState("currentPlacement",le.placement),k(!0);const K=Yn(le.x),ye=Yn(le.y);if(Object.assign(M.style,{top:"0",left:"0",transform:`translate3d(${K}px,${ye}px,0)`}),ge&&le.middlewareData.arrow){const{x:ie,y:Me}=le.middlewareData.arrow,He=le.placement.split("-")[0],Le=ge.clientWidth/2,ze=ge.clientHeight/2,L=ie!=null?ie+Le:-Le,q=Me!=null?Me+ze:-ze;M.style.setProperty("--popover-transform-origin",{top:`${L}px calc(100% + ${ze}px)`,bottom:`${L}px ${-ze}px`,left:`calc(100% + ${Le}px) ${q}px`,right:`${-Le}px ${q}px`}[He]),Object.assign(ge.style,{left:ie!=null?`${ie}px`:"",top:Me!=null?`${Me}px`:"",[He]:"100%"})}},Fe=io(re,M,async()=>{de?(await H({updatePosition:ne}),k(!0)):await ne()},{elementResize:typeof ResizeObserver=="function"});return()=>{k(!1),Fe()}},[n,P,M,A,V,M,B,m,b,l,d,v,f,x,p,g,D,y,E,N,de,H]),X(()=>{if(!m||!b||!(M!=null&&M.isConnected)||!(z!=null&&z.isConnected))return;const re=()=>{M.style.zIndex=getComputedStyle(z).zIndex};re();let ne=requestAnimationFrame(()=>{ne=requestAnimationFrame(re)});return()=>cancelAnimationFrame(ne)},[m,b,M,z]);const te=l?"fixed":"absolute";return _=Pe(_,re=>O.jsx("div",j(C({},a),{style:C({position:te,top:0,left:0,width:"max-content"},a==null?void 0:a.style),ref:n==null?void 0:n.setPopoverElement,children:re})),[n,te,a]),_=Pe(_,re=>O.jsx(Dr,{value:n,children:re}),[n]),_=j(C({"data-placing":!S||void 0},_),{style:C({position:"relative"},_.style)}),_=no(j(C({store:n,modal:o,portal:u,preserveTabOrder:s,preserveTabOrderAnchor:w||V,autoFocusOnShow:S&&i},_),{portalRef:$})),_});hn(J(function(t){const r=ro(t);return Q(qi,r)}),an);var na="div";function ra(e,...t){if(!e)return!1;if("id"in e){const r=t.filter(Boolean).map(n=>`[aria-controls~="${n}"]`).join(", ");return r?e.matches(r):!1}return!1}var oa=ee(function(t){var r=t,{store:n,modal:o,tabIndex:u,alwaysVisible:s,autoFocusOnHide:i=!0,hideOnInteractOutside:a=!0}=r,l=Z(r,["store","modal","tabIndex","alwaysVisible","autoFocusOnHide","hideOnInteractOutside"]);const d=Ot();n=n||d,Oe(n,"ComboboxPopover must receive a `store` prop or be wrapped in a ComboboxProvider component.");const v=n.useState("baseElement"),f=c.useRef(!1),x=Ie(n.tag,p=>p==null?void 0:p.renderedItems.length);return l=Yr(C({store:n,alwaysVisible:s},l)),l=ro(j(C({store:n,modal:o,alwaysVisible:s,backdrop:!1,autoFocusOnShow:!1,finalFocus:v,preserveTabOrderAnchor:null,unstable_treeSnapshotKey:x},l),{getPersistentElements(){var p;const g=((p=l.getPersistentElements)==null?void 0:p.call(l))||[];if(!o||!n)return g;const{contentElement:D,baseElement:y}=n.getState();if(!y)return g;const E=ue(y),I=[];if(D!=null&&D.id&&I.push(`[aria-controls~="${D.id}"]`),y!=null&&y.id&&I.push(`[aria-controls~="${y.id}"]`),!I.length)return[...g,y];const h=I.join(","),_=E.querySelectorAll(h);return[...g,..._]},autoFocusOnHide(p){return or(i,p)?!1:f.current?(f.current=!1,!1):!0},hideOnInteractOutside(p){var g,D;const y=n==null?void 0:n.getState(),E=(g=y==null?void 0:y.contentElement)==null?void 0:g.id,I=(D=y==null?void 0:y.baseElement)==null?void 0:D.id;if(ra(p.target,E,I))return!1;const h=typeof a=="function"?a(p):a;return h&&(f.current=p.type==="click"),h}})),l}),ua=hn(J(function(t){const r=oa(t);return Q(na,r)}),Ot),sa="hr",ia=ee(function(t){var r=t,{orientation:n="horizontal"}=r,o=Z(r,["orientation"]);return o=C({role:"separator","aria-orientation":n},o),o}),aa=J(function(t){const r=ia(t);return Q(sa,r)});function ca(e){return c.forwardRef(e)}var Gn=8,Xn=28,la=({value:e,onChange:t,searchValue:r,onSearchValueChange:n,options:o,className:u,placeholder:s,fetchNextPage:i,isFetchingNextPage:a,onCreateOption:l,noResultsPlaceholder:d,allowClear:v,...f},x)=>{var le;const[p,g]=c.useState(!1),[D,y]=c.useTransition(),{t:E}=bo(),I=c.useRef(null),h=c.useRef(null);c.useImperativeHandle(x,()=>I.current);const _=e!==void 0,F=r!==void 0,A=Array.isArray(e),V=A?[]:"",[w,M]=c.useState(r||""),z=c.useDeferredValue(w),[B,m]=c.useState(V),P=F?r:w,R=_?e:B,S=K=>{!o.filter(ie=>!ie.disabled).find(ie=>A?K==null?void 0:K.includes(ie.value):ie.value===K)&&l&&K&&l(K),_||m(K||V),t&&t(K),M("")},k=K=>{M(K),n&&n(K)},$=c.useMemo(()=>F?[]:go(o,z,{keys:["label"]}),[o,z,F]),b=c.useRef(new IntersectionObserver(K=>{K[0].isIntersecting&&(i==null||i())},{threshold:1})),N=c.useCallback(K=>{a||(b.current&&b.current.disconnect(),K&&b.current.observe(K))},[a]),H=K=>{K||M(""),g(K)},te=(R==null?void 0:R.length)>0&&A,re=te&&!P&&!p,ne=!A&&!p,be=(le=o.find(K=>K.value===R))==null?void 0:le.label,Fe=re||p,ge=c.useMemo(()=>{if(!Array.isArray(R))return Xn+Gn;const ye=R.length.toString().length;return Xn+ye*Gn},[R]),me=c.useMemo(()=>F?o:$,[$,o,F]);return O.jsxs(Os,{open:p,setOpen:H,selectedValue:R,setSelectedValue:K=>S(K),value:w,setValue:K=>{y(()=>k(K))},children:[O.jsxs("div",{className:nt("relative flex cursor-pointer items-center gap-x-2 overflow-hidden","h-8 w-full rounded-md","bg-ui-bg-field transition-fg shadow-borders-base","has-[input:focus]:shadow-borders-interactive-with-active","has-[:invalid]:shadow-borders-error has-[[aria-invalid=true]]:shadow-borders-error","has-[:disabled]:bg-ui-bg-disabled has-[:disabled]:text-ui-fg-disabled has-[:disabled]:cursor-not-allowed",u),style:{"--tag-width":`${ge}px`},children:[te&&O.jsxs("button",{type:"button",onClick:K=>{K.preventDefault(),S(void 0)},className:"bg-ui-bg-base hover:bg-ui-bg-base-hover txt-compact-small-plus text-ui-fg-subtle focus-within:border-ui-fg-interactive transition-fg absolute left-0.5 top-0.5 z-[1] flex h-[28px] items-center rounded-[4px] border py-[3px] pl-1.5 pr-1 outline-none",children:[O.jsx("span",{className:"tabular-nums",children:R.length}),O.jsx(xn,{className:"text-ui-fg-muted"})]}),O.jsxs("div",{className:"relative flex size-full items-center",children:[re&&O.jsx("div",{className:nt("pointer-events-none absolute inset-y-0 flex size-full items-center",{"left-[calc(var(--tag-width)+8px)]":te,"left-2":!te}),children:O.jsx(gt,{size:"small",leading:"compact",children:E("general.selected")})}),ne&&O.jsx("div",{className:nt("pointer-events-none absolute inset-y-0 flex size-full items-center overflow-hidden",{"left-[calc(var(--tag-width)+8px)]":te,"left-2":!te}),children:O.jsx(gt,{size:"small",leading:"compact",className:"truncate",children:be})}),O.jsx(Is,{autoSelect:!0,ref:I,onFocus:()=>g(!0),className:nt("txt-compact-small text-ui-fg-base !placeholder:text-ui-fg-muted transition-fg size-full cursor-pointer bg-transparent pl-2 pr-8 outline-none focus:cursor-text","hover:bg-ui-bg-field-hover",{"opacity-0":ne,"pl-2":!te,"pl-[calc(var(--tag-width)+8px)]":te}),placeholder:Fe?void 0:s,...f})]}),v&&e&&O.jsx("button",{type:"button",onClick:K=>{K.preventDefault(),S(void 0)},className:"bg-ui-bg-base hover:bg-ui-bg-base-hover txt-compact-small-plus text-ui-fg-subtle focus-within:border-ui-fg-interactive transition-fg absolute right-[28px] top-0.5 z-[1] flex h-[28px] items-center rounded-[4px] border px-1.5 py-[2px] outline-none",children:O.jsx(xn,{className:"text-ui-fg-muted"})}),O.jsx(Vs,{render:K=>O.jsx("button",{...K,type:"button",className:"text-ui-fg-muted transition-fg hover:bg-ui-bg-field-hover absolute right-0 flex size-8 items-center justify-center rounded-r outline-none",children:O.jsx(So,{})})})]}),O.jsxs(ua,{gutter:4,sameWidth:!0,ref:h,role:"listbox",className:nt("shadow-elevation-flyout bg-ui-bg-base z-50 rounded-[8px] p-1","max-h-[200px] overflow-y-auto","data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95","data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95","data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2"),style:{pointerEvents:p?"auto":"none"},"aria-busy":D,children:[me.map(({value:K,label:ye,disabled:ie})=>O.jsxs(Nn,{value:K,focusOnHover:!0,setValueOnClick:!1,disabled:ie,className:nt("transition-fg bg-ui-bg-base data-[active-item=true]:bg-ui-bg-base-hover group flex cursor-pointer items-center gap-x-2 rounded-[4px] px-2 py-1",{"text-ui-fg-disabled":ie,"bg-ui-bg-component":ie}),children:[O.jsx(Hs,{className:"flex !size-5 items-center justify-center",children:A?O.jsx(ho,{}):O.jsx(xo,{})}),O.jsx(Xs,{className:"txt-compact-small",children:ye})]},K)),!!i&&O.jsx("div",{ref:N,className:"w-px"}),a&&O.jsx("div",{className:"transition-fg bg-ui-bg-base flex items-center rounded-[4px] px-2 py-1.5",children:O.jsx("div",{className:"bg-ui-bg-component size-full h-5 w-full animate-pulse rounded-[4px]"})}),!me.length&&(d&&!(P!=null&&P.length)?d:O.jsx("div",{className:"flex items-center gap-x-2 rounded-[4px] px-2 py-1.5",children:O.jsx(gt,{size:"small",leading:"compact",className:"text-ui-fg-subtle",children:E("general.noResultsTitle")})})),!me.length&&l&&O.jsxs(c.Fragment,{children:[O.jsx(aa,{className:"bg-ui-border-base -mx-1"}),O.jsxs(Nn,{value:w,focusOnHover:!0,setValueOnClick:!1,className:"transition-fg bg-ui-bg-base data-[active-item=true]:bg-ui-bg-base-hover group mt-1 flex cursor-pointer items-center gap-x-2 rounded-[4px] px-2 py-1.5",children:[O.jsx(yo,{className:"text-ui-fg-subtle"}),O.jsxs(gt,{size:"small",leading:"compact",children:[E("actions.create"),' "',P,'"']})]})]})]})]})},va=ca(la);export{va as C};
