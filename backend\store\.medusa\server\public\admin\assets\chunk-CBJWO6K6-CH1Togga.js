import{j as s,w as r,ab as n}from"./index-Bwql5Dzz.js";var p=({label:t,description:l,optional:x=!1,tooltip:a,onCheckedChange:e,...d})=>s.jsx(r.<PERSON>,{...d,render:({field:{value:j,onChange:o,...c}})=>s.jsxs(r.Item,{children:[s.jsxs("div",{className:"bg-ui-bg-component shadow-elevation-card-rest flex items-start gap-x-3 rounded-lg p-3",children:[s.jsx(r.Control,{children:s.jsx(n,{...c,checked:j,onCheckedChange:i=>{e==null||e(i),o(i)}})}),s.jsxs("div",{children:[s.jsx(r.Label,{optional:x,tooltip:a,children:t}),s.jsx(r<PERSON>,{children:l})]})]}),s.jsx(r<PERSON>,{})]})});export{p as S};
