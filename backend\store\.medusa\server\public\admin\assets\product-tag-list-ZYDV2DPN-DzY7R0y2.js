import{u as T}from"./chunk-LNROU6QR-NOnC9z5g.js";import{u as h}from"./chunk-W6676YBU-BEKokOil.js";import{q as m,a as b,j as a,eF as x,s as j,b as p,d as P,bg as v,k as w,H as C,B as D,L,r as S,A as _}from"./index-Bwql5Dzz.js";import{u as q,_ as k}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{a as A}from"./chunk-EMNHBSFU-BcNoY5bk.js";import{a as E}from"./chunk-GW6TVOAA-rYK3_Cle.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as O}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{P as Q}from"./pencil-square-6wRbnn1C.js";import{T as z}from"./trash-BBylvTAG.js";import{C as H}from"./container-Dqi2woPF.js";import{c as N}from"./index-BxZ1678G.js";import"./use-prompt-pbDx0Sfe.js";import"./prompt-BsR9zKsn.js";import"./chunk-WYX5PIA3-DoOUp1ge.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-W7625H47-D4n0RxCV.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";var R=t=>({queryKey:x.list(t),queryFn:async()=>j.admin.productTag.list(t)}),St=async({request:t})=>{const e=new URL(t.url).searchParams,r={};e.forEach((i,s)=>{try{r[s]=JSON.parse(i)}catch{r[s]=i}});const o=R(r);return m.getQueryData(o.queryKey)??await m.fetchQuery(o)},c=20,B=()=>{const{t}=p(),{searchParams:e,raw:r}=A({pageSize:c}),o=P(),{product_tags:i,count:s,isPending:l,isError:d,error:g}=v(e,{initialData:o,placeholderData:w}),u=M(),f=E(),{table:y}=q({data:i,count:s,columns:u,getRowId:n=>n.id,pageSize:c});if(d)throw g;return a.jsxs(H,{className:"divide-y px-0 py-0",children:[a.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[a.jsx(C,{children:t("productTags.domain")}),a.jsx(D,{variant:"secondary",size:"small",asChild:!0,children:a.jsx(L,{to:"create",children:t("actions.create")})})]}),a.jsx(k,{table:y,filters:f,queryObject:r,isLoading:l,columns:u,pageSize:c,count:s,navigateTo:n=>n.original.id,search:!0,pagination:!0,orderBy:[{key:"value",label:t("fields.value")},{key:"created_at",label:t("fields.createdAt")},{key:"updated_at",label:t("fields.updatedAt")}]})]})},F=({productTag:t})=>{const{t:e}=p(),r=T({productTag:t});return a.jsx(_,{groups:[{actions:[{icon:a.jsx(Q,{}),label:e("actions.edit"),to:`${t.id}/edit`}]},{actions:[{icon:a.jsx(z,{}),label:e("actions.delete"),onClick:r}]}]})},K=N(),M=()=>{const t=h();return S.useMemo(()=>[...t,K.display({id:"actions",cell:({row:e})=>a.jsx(F,{productTag:e.original})})],[t])},_t=()=>{const{getWidgets:t}=b();return a.jsx(O,{showMetadata:!1,showJSON:!1,hasOutlet:!0,widgets:{after:t("product_tag.list.after"),before:t("product_tag.list.before")},children:a.jsx(B,{})})};export{_t as Component,St as loader};
