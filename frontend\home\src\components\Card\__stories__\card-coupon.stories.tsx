// card-coupon.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';
import { CardCoupon, CardCouponSkeleton } from '../card-coupon';

// Sample brand logos - replace with actual URLs in production
const brandsConfig = {
  philips: {
    logoUrl: '/images/philips-logo.png',
    name: '<PERSON>'
  },
  lamptan: {
    logoUrl: '/images/LAMPTAN.png',
    name: 'LAMPTAN'
  },
  bcc: {
    logoUrl: '/images/BCC.png',
    name: 'Bangkok Cable'
  },
  songkhla: {
    logoUrl: '/images/Sengaroon.png',
    name: '<PERSON><PERSON><PERSON> Natrin'
  },
  panasonic: {
    logoUrl: '/images/Panasonic.png',
    name: 'Panasonic'
  },
  haco: {
    logoUrl: '/images/HACO.png',
    name: 'HACO'
  }
};

// Define sample coupons based on the images provided
const fixedAmountCoupon = {
  id: 'fixed-amount-1',
  brandLogoUrl: brandsConfig.philips.logoUrl,
  brandName: brandsConfig.philips.name,
  discountType: 'amount' as const,
  discountValue: 2500,
  minPurchase: 30000,
  onSeeDetails: action('onSeeDetails'),
  onClaimCoupon: action('onClaimCoupon'),
};

const fixedAmountWithMaxDiscountCoupon = {
  id: 'fixed-amount-max-2',
  brandLogoUrl: brandsConfig.lamptan.logoUrl,
  brandName: brandsConfig.lamptan.name,
  discountType: 'amount' as const,
  discountValue: 2500,
  minPurchase: 3000,
  maxDiscount: 1000,
  onSeeDetails: action('onSeeDetails'),
  onClaimCoupon: action('onClaimCoupon'),
};

const percentageCoupon = {
  id: 'percentage-1',
  brandLogoUrl: brandsConfig.bcc.logoUrl,
  brandName: brandsConfig.bcc.name,
  discountType: 'percentage' as const,
  discountValue: 75,
  minPurchase: 30000,
  maxDiscount: 1000,
  onSeeDetails: action('onSeeDetails'),
  onClaimCoupon: action('onClaimCoupon'),
};

const maxPercentageCoupon = {
  id: 'max-percentage-1',
  brandLogoUrl: brandsConfig.bcc.logoUrl,
  brandName: brandsConfig.bcc.name,
  discountType: 'max-percentage' as const,
  discountValue: 50,
  minPurchase: 30000,
  maxDiscount: 500,
  onSeeDetails: action('onSeeDetails'),
  onClaimCoupon: action('onClaimCoupon'),
};

const smallAmountCoupon = {
  id: 'small-amount-1',
  brandLogoUrl: brandsConfig.songkhla.logoUrl,
  brandName: brandsConfig.songkhla.name,
  discountType: 'amount' as const,
  discountValue: 500,
  minPurchase: 50000,
  onSeeDetails: action('onSeeDetails'),
  onClaimCoupon: action('onClaimCoupon'),
};

const smallPercentageCoupon = {
  id: 'small-percentage-1',
  brandLogoUrl: brandsConfig.philips.logoUrl,
  brandName: brandsConfig.philips.name,
  discountType: 'percentage' as const,
  discountValue: 10,
  minPurchase: 25000,
  maxDiscount: 500,
  onSeeDetails: action('onSeeDetails'),
  onClaimCoupon: action('onClaimCoupon'),
};

const largeAmountCoupon = {
  id: 'large-amount-1',
  brandLogoUrl: brandsConfig.haco.logoUrl,
  brandName: brandsConfig.haco.name,
  discountType: 'amount' as const,
  discountValue: 5000,
  minPurchase: 50000,
  onSeeDetails: action('onSeeDetails'),
  onClaimCoupon: action('onClaimCoupon'),
};

const meta: Meta<typeof CardCoupon> = {
  title: 'UI/Card/CardCoupon',
  component: CardCoupon,
  parameters: {
    layout: 'centered',
    // Add a11y parameter for accessibility testing
    a11y: {
      config: {
        rules: [
          {
            // Ensure all images have alt text
            id: 'image-alt',
            enabled: true
          }
        ]
      }
    },
  },
  // Define argTypes for Storybook controls
  argTypes: {
    id: { 
      control: 'text',
      description: 'Unique coupon identifier',
    },
    brandLogoUrl: { 
      control: 'text',
      description: 'URL to the brand logo image' 
    },
    brandName: { 
      control: 'text',
      description: 'Brand name for alt text' 
    },
    discountType: { 
      control: { type: 'select', options: ['amount', 'percentage', 'max-percentage'] },
      description: 'Type of discount - fixed amount, percentage, or max percentage' 
    },
    discountValue: { 
      control: { type: 'number', min: 0 },
      description: 'Value of the discount (amount or percentage)' 
    },
    minPurchase: { 
      control: { type: 'number', min: 0 },
      description: 'Minimum purchase required to use the coupon' 
    },
    maxDiscount: { 
      control: { type: 'number', min: 0 },
      description: 'Maximum discount amount for percentage discounts' 
    },
    claimed: { 
      control: 'boolean',
      description: 'Whether the coupon has been claimed already' 
    },
    onSeeDetails: { 
      action: 'seeDetails',
      description: 'Function called when see details button is clicked' 
    },
    onClaimCoupon: { 
      action: 'claimCoupon',
      description: 'Function called when claim button is clicked' 
    },
    isClaimingCoupon: { 
      control: 'boolean',
      description: 'Whether the coupon is currently being claimed' 
    },
    showTermsOnly: {
      control: 'boolean',
      description: 'Whether to show only the terms button (without claim button)'
    }
  },
  // Default values for the stories
  args: {
    onSeeDetails: action('onSeeDetails'),
    onClaimCoupon: action('onClaimCoupon'),
  },
  // Add decorators if needed
  decorators: [
    (Story) => (
      <div style={{ maxWidth: '600px', width: '100%', padding: '20px' }}>
        <Story />
      </div>
    ),
  ],
  // Add tags for filtering in Storybook
  tags: ['autodocs', 'coupon', 'card'],
};

export default meta;
type Story = StoryObj<typeof CardCoupon>;

// Fixed amount discount coupon (฿2,500)
export const FixedAmountCoupon: Story = {
  args: {
    ...fixedAmountCoupon,
  },
};

// Fixed amount with max discount limit
export const FixedAmountWithMaxDiscount: Story = {
  args: {
    ...fixedAmountWithMaxDiscountCoupon,
  },
};

// Percentage discount coupon (75%)
export const PercentageCoupon: Story = {
  args: {
    ...percentageCoupon,
  },
};

// Maximum percentage discount coupon (สูงสุด 50%)
export const MaxPercentageCoupon: Story = {
  args: {
    ...maxPercentageCoupon,
  },
};

// Small fixed amount coupon (฿500)
export const SmallAmountCoupon: Story = {
  args: {
    ...smallAmountCoupon,
  },
};

// Small percentage discount coupon (10%)
export const SmallPercentageCoupon: Story = {
  args: {
    ...smallPercentageCoupon,
  },
};

// Large fixed amount coupon (฿5,000)
export const LargeAmountCoupon: Story = {
  args: {
    ...largeAmountCoupon,
  },
};

// Claimed coupon example
export const ClaimedCoupon: Story = {
  args: {
    ...fixedAmountCoupon,
    claimed: true,
  },
};

// Coupon in claiming state
export const ClaimingCoupon: Story = {
  args: {
    ...percentageCoupon,
    isClaimingCoupon: true,
  },
};

// Show only terms button
export const TermsOnlyCoupon: Story = {
  args: {
    ...maxPercentageCoupon,
    showTermsOnly: true,
  },
};

// Loading state example
export const LoadingState: Story = {
  render: () => <CardCouponSkeleton />,
};