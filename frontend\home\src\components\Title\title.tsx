import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ChevronRight } from 'lucide-react';

export interface TitleProps {
  /**
   * หัวข้อหลักของ Title
   */
  title: string;
  
  /**
   * คำอธิบายหรือ subtitle (optional)
   */
  description?: string;
  
  /**
   * ไอคอนที่จะแสดงด้านหน้าหัวข้อ (optional)
   */
  prefixIcon?: ReactNode;
  
  /**
   * ไอคอนที่จะแสดงด้านหลังหัวข้อ (optional)
   */
  suffixIcon?: ReactNode;
  
  /**
   * ข้อความที่ต้องการไฮไลท์ (ถ้ามี) - จะเป็นส่วนหนึ่งของ title 
   * และแสดงพื้นหลังสีเหลือง
   */
  highlightText?: string;
  
  /**
   * ลิงก์สำหรับปุ่ม "ดูทั้งหมด" (optional)
   * ถ้าไม่มีค่า จะไม่แสดงปุ่ม
   */
  viewAllLink?: string;
  
  /**
   * ข้อความที่แสดงบนปุ่ม (default: "ดูทั้งหมด")
   */
  viewAllText?: string;
  
  /**
   * className เพิ่มเติมสำหรับ root container
   */
  className?: string;
  
  /**
   * className เพิ่มเติมสำหรับ title
   */
  titleClassName?: string;
  
  /**
   * className เพิ่มเติมสำหรับ description
   */
  descriptionClassName?: string;
  
  /**
   * onClick handler สำหรับปุ่ม viewAll
   */
  onViewAllClick?: () => void;
  
  /**
   * กำหนดระดับของ heading (h1-h5)
   * @default 'h2'
   */
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5';
  
  /**
   * กำหนดว่าจะแสดงปุ่ม "ดูทั้งหมด" หรือไม่
   * ถ้าเป็น false จะไม่แสดงปุ่มถึงแม้จะมี viewAllLink
   * @default true
   */
  showViewAllButton?: boolean;
}

export const Title = ({
  title,
  description,
  prefixIcon,
  suffixIcon,
  highlightText,
  viewAllLink,
  viewAllText = 'ดูทั้งหมด',
  className,
  titleClassName,
  descriptionClassName,
  onViewAllClick,
  as = 'h2',
  showViewAllButton = true,
}: TitleProps) => {
  // แยกข้อความเพื่อใส่ไฮไลท์ (ถ้ามี)
  const renderHighlightedTitle = () => {
    if (!highlightText || !title.includes(highlightText)) {
      return (
        <>
          {prefixIcon && <span className="mr-2">{prefixIcon}</span>}
          <span>{title}</span>
          {suffixIcon && <span className="ml-2">{suffixIcon}</span>}
        </>
      );
    }
    
    const parts = title.split(highlightText);
    return (
      <>
        {prefixIcon && <span className="mr-2">{prefixIcon}</span>}
        <span>{parts[0]}</span>
        <span className="bg-yellow-200 px-1 rounded-sm">{highlightText}</span>
        <span>{parts[1]}</span>
        {suffixIcon && <span className="ml-2">{suffixIcon}</span>}
      </>
    );
  };
  
  // สร้าง heading element ตามระดับที่กำหนด
  const Heading = as;
  
  return (
    <div 
      className={cn(
        "flex flex-col sm:flex-row sm:items-stretch sm:justify-between w-full py-4",
        className
      )}
    >
      <div className="flex flex-col justify-center">
        <Heading 
          className={cn(
            "text-xl md:text-2xl font-bold flex items-center",
            titleClassName
          )}
        >
          {renderHighlightedTitle()}
        </Heading>
        
        {description && (
          <p 
            className={cn(
              "text-sm text-gray-600 mt-1",
              descriptionClassName
            )}
          >
            {description}
          </p>
        )}
      </div>
      
      {viewAllLink && showViewAllButton && (
        <div className="mt-2 ml-0 sm:mt-0 sm:ml-2 sm:flex sm:items-center">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1 sm:h-full"
            onClick={onViewAllClick}
            asChild
          >
            <Link href={viewAllLink} className="sm:h-full sm:flex sm:items-center">
              {viewAllText}
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
};

export default Title;