import{r as e}from"./index-Bwql5Dzz.js";var c=Object.defineProperty,n=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,i=(t,a,r)=>a in t?c(t,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[a]=r,s=(t,a)=>{for(var r in a)o.call(a,r)&&i(t,r,a[r]);if(n)for(var r of n(a))f.call(a,r)&&i(t,r,a[r]);return t},m=(t,a)=>{var r={};for(var l in t)o.call(t,l)&&a.indexOf(l)<0&&(r[l]=t[l]);if(t!=null&&n)for(var l of n(t))a.indexOf(l)<0&&f.call(t,l)&&(r[l]=t[l]);return r};const p=e.forwardRef((t,a)=>{var r=t,l=m(r,["color"]);return e.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:20,height:20,fill:"none",ref:a},l),e.createElement("g",{filter:"url(#a)"},e.createElement("circle",{cx:10,cy:8.903,r:7.806,fill:"#3B82F6"}),e.createElement("circle",{cx:10,cy:8.903,r:7.806,fill:"url(#b)",fillOpacity:.2}),e.createElement("circle",{cx:10,cy:8.903,r:7.556,stroke:"#000",strokeOpacity:.2,strokeWidth:.5})),e.createElement("g",{fill:"#fff",clipPath:"url(#c)"},e.createElement("path",{d:"M6.098 11.393a.724.724 0 0 1-.724-.723V7.136a.724.724 0 0 1 .951-.686l.487.163a.434.434 0 1 1-.274.822l-.297-.098v3.133l.297-.099a.433.433 0 1 1 .274.823l-.487.162a.7.7 0 0 1-.227.037M8.41 12.517a.723.723 0 0 1-.722-.723V6.012a.72.72 0 0 1 1-.667l.467.194a.434.434 0 0 1-.333.801l-.267-.111v5.349l.267-.111a.434.434 0 1 1 .333.8l-.467.195a.7.7 0 0 1-.278.055M14.038 5.752l-3.012-1.39A.722.722 0 0 0 10 5.018v7.77a.72.72 0 0 0 .722.724.7.7 0 0 0 .304-.067l3.012-1.39c.357-.165.588-.526.588-.92V6.672c0-.393-.23-.754-.588-.919"})),e.createElement("defs",null,e.createElement("linearGradient",{id:"b",x1:10.09,x2:10.09,y1:1.142,y2:16.754,gradientUnits:"userSpaceOnUse"},e.createElement("stop",{stopColor:"#fff"}),e.createElement("stop",{offset:1,stopColor:"#fff",stopOpacity:0})),e.createElement("clipPath",{id:"c"},e.createElement("path",{fill:"#fff",d:"M4.796 3.699h10.408v10.408H4.796z"})),e.createElement("filter",{id:"a",width:20,height:20,x:0,y:0,colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse"},e.createElement("feFlood",{floodOpacity:0,result:"BackgroundImageFix"}),e.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),e.createElement("feOffset",{dy:1.054}),e.createElement("feGaussianBlur",{stdDeviation:1.054}),e.createElement("feComposite",{in2:"hardAlpha",operator:"out"}),e.createElement("feColorMatrix",{values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"}),e.createElement("feBlend",{in2:"BackgroundImageFix",result:"effect1_dropShadow_6384_214"}),e.createElement("feBlend",{in:"SourceGraphic",in2:"effect1_dropShadow_6384_214",result:"shape"}),e.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),e.createElement("feOffset",{dy:1.054}),e.createElement("feGaussianBlur",{stdDeviation:1.054}),e.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),e.createElement("feColorMatrix",{values:"0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"}),e.createElement("feBlend",{in2:"shape",result:"effect2_innerShadow_6384_214"}),e.createElement("feColorMatrix",{in:"SourceAlpha",result:"hardAlpha",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"}),e.createElement("feOffset",{dy:-1.054}),e.createElement("feGaussianBlur",{stdDeviation:2.635}),e.createElement("feComposite",{in2:"hardAlpha",k2:-1,k3:1,operator:"arithmetic"}),e.createElement("feColorMatrix",{values:"0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"}),e.createElement("feBlend",{in2:"effect2_innerShadow_6384_214",result:"effect3_innerShadow_6384_214"}))))});p.displayName="ThumbnailBadge";export{p as T};
