try{
(()=>{var E=__STORYBOOK_API__,{ActiveTabs:D,Consumer:v,ManagerContext:H,Provider:x,RequestResponseError:M,addons:s,combineParameters:U,controlOrMetaKey:F,controlOrMetaSymbol:G,eventMatchesShortcut:N,eventToShortcut:W,experimental_MockUniversalStore:K,experimental_UniversalStore:Y,experimental_requestResponse:q,experimental_useUniversalStore:V,isMacLike:z,isShortcutTaken:Z,keyToSymbol:j,merge:J,mockChannel:Q,optionOrAltSymbol:X,shortcutMatchesShortcut:$,shortcutToHumanString:oo,types:d,useAddonState:no,useArgTypes:eo,useArgs:co,useChannel:p,useGlobalTypes:to,useGlobals:u,useParameter:ro,useSharedState:Io,useStoryPrepared:ao,useStorybookApi:lo,useStorybookState:io}=__STORYBOOK_API__;var r=__REACT__,{Children:So,Component:Co,Fragment:ho,Profiler:Ao,PureComponent:bo,StrictMode:To,Suspense:_o,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:go,cloneElement:yo,createContext:ko,createElement:Bo,createFactory:Oo,createRef:Lo,forwardRef:Po,isValidElement:fo,lazy:Ro,memo:wo,startTransition:Eo,unstable_act:Do,useCallback:vo,useContext:Ho,useDebugValue:xo,useDeferredValue:Mo,useEffect:Uo,useId:Fo,useImperativeHandle:Go,useInsertionEffect:No,useLayoutEffect:Wo,useMemo:Ko,useReducer:Yo,useRef:qo,useState:Vo,useSyncExternalStore:zo,useTransition:Zo,version:jo}=__REACT__;var on=__STORYBOOK_COMPONENTS__,{A:nn,ActionBar:en,AddonPanel:cn,Badge:tn,Bar:rn,Blockquote:In,Button:an,ClipboardCode:ln,Code:sn,DL:dn,Div:pn,DocumentWrapper:un,EmptyTabContent:mn,ErrorFormatter:Sn,FlexBar:Cn,Form:hn,H1:An,H2:bn,H3:Tn,H4:_n,H5:gn,H6:yn,HR:kn,IconButton:m,IconButtonSkeleton:Bn,Icons:On,Img:Ln,LI:Pn,Link:fn,ListItem:Rn,Loader:wn,Modal:En,OL:Dn,P:vn,Placeholder:Hn,Pre:xn,ProgressSpinner:Mn,ResetWrapper:Un,ScrollArea:Fn,Separator:Gn,Spaced:Nn,Span:Wn,StorybookIcon:Kn,StorybookLogo:Yn,Symbols:qn,SyntaxHighlighter:Vn,TT:zn,TabBar:Zn,TabButton:jn,TabWrapper:Jn,Table:Qn,Tabs:Xn,TabsState:$n,TooltipLinkList:S,TooltipMessage:oe,TooltipNote:ne,UL:ee,WithTooltip:C,WithTooltipPure:ce,Zoom:te,codeCommon:re,components:Ie,createCopyToClipboardFunction:ae,getStoryHref:le,icons:ie,interleaveSeparators:se,nameSpaceClassNames:de,resetComponents:pe,withReset:ue}=__STORYBOOK_COMPONENTS__;var Ae=__STORYBOOK_ICONS__,{AccessibilityAltIcon:be,AccessibilityIcon:Te,AccessibilityIgnoredIcon:_e,AddIcon:ge,AdminIcon:ye,AlertAltIcon:ke,AlertIcon:Be,AlignLeftIcon:Oe,AlignRightIcon:Le,AppleIcon:Pe,ArrowBottomLeftIcon:fe,ArrowBottomRightIcon:Re,ArrowDownIcon:we,ArrowLeftIcon:Ee,ArrowRightIcon:De,ArrowSolidDownIcon:ve,ArrowSolidLeftIcon:He,ArrowSolidRightIcon:xe,ArrowSolidUpIcon:Me,ArrowTopLeftIcon:Ue,ArrowTopRightIcon:Fe,ArrowUpIcon:Ge,AzureDevOpsIcon:Ne,BackIcon:We,BasketIcon:Ke,BatchAcceptIcon:Ye,BatchDenyIcon:qe,BeakerIcon:Ve,BellIcon:ze,BitbucketIcon:Ze,BoldIcon:je,BookIcon:Je,BookmarkHollowIcon:Qe,BookmarkIcon:Xe,BottomBarIcon:$e,BottomBarToggleIcon:oc,BoxIcon:nc,BranchIcon:ec,BrowserIcon:cc,ButtonIcon:tc,CPUIcon:rc,CalendarIcon:Ic,CameraIcon:ac,CameraStabilizeIcon:lc,CategoryIcon:ic,CertificateIcon:sc,ChangedIcon:dc,ChatIcon:pc,CheckIcon:uc,ChevronDownIcon:mc,ChevronLeftIcon:Sc,ChevronRightIcon:Cc,ChevronSmallDownIcon:hc,ChevronSmallLeftIcon:Ac,ChevronSmallRightIcon:bc,ChevronSmallUpIcon:Tc,ChevronUpIcon:_c,ChromaticIcon:gc,ChromeIcon:yc,CircleHollowIcon:kc,CircleIcon:Bc,ClearIcon:Oc,CloseAltIcon:Lc,CloseIcon:Pc,CloudHollowIcon:fc,CloudIcon:Rc,CogIcon:wc,CollapseIcon:Ec,CommandIcon:Dc,CommentAddIcon:vc,CommentIcon:Hc,CommentsIcon:xc,CommitIcon:Mc,CompassIcon:Uc,ComponentDrivenIcon:Fc,ComponentIcon:Gc,ContrastIcon:Nc,ContrastIgnoredIcon:Wc,ControlsIcon:Kc,CopyIcon:Yc,CreditIcon:qc,CrossIcon:Vc,DashboardIcon:zc,DatabaseIcon:Zc,DeleteIcon:jc,DiamondIcon:Jc,DirectionIcon:Qc,DiscordIcon:Xc,DocChartIcon:$c,DocListIcon:ot,DocumentIcon:nt,DownloadIcon:et,DragIcon:ct,EditIcon:tt,EllipsisIcon:rt,EmailIcon:It,ExpandAltIcon:at,ExpandIcon:lt,EyeCloseIcon:it,EyeIcon:st,FaceHappyIcon:dt,FaceNeutralIcon:pt,FaceSadIcon:ut,FacebookIcon:mt,FailedIcon:St,FastForwardIcon:Ct,FigmaIcon:ht,FilterIcon:At,FlagIcon:bt,FolderIcon:Tt,FormIcon:_t,GDriveIcon:gt,GithubIcon:yt,GitlabIcon:kt,GlobeIcon:h,GoogleIcon:Bt,GraphBarIcon:Ot,GraphLineIcon:Lt,GraphqlIcon:Pt,GridAltIcon:ft,GridIcon:Rt,GrowIcon:wt,HeartHollowIcon:Et,HeartIcon:Dt,HomeIcon:vt,HourglassIcon:Ht,InfoIcon:xt,ItalicIcon:Mt,JumpToIcon:Ut,KeyIcon:Ft,LightningIcon:Gt,LightningOffIcon:Nt,LinkBrokenIcon:Wt,LinkIcon:Kt,LinkedinIcon:Yt,LinuxIcon:qt,ListOrderedIcon:Vt,ListUnorderedIcon:zt,LocationIcon:Zt,LockIcon:jt,MarkdownIcon:Jt,MarkupIcon:Qt,MediumIcon:Xt,MemoryIcon:$t,MenuIcon:or,MergeIcon:nr,MirrorIcon:er,MobileIcon:cr,MoonIcon:tr,NutIcon:rr,OutboxIcon:Ir,OutlineIcon:ar,PaintBrushIcon:lr,PaperClipIcon:ir,ParagraphIcon:sr,PassedIcon:dr,PhoneIcon:pr,PhotoDragIcon:ur,PhotoIcon:mr,PhotoStabilizeIcon:Sr,PinAltIcon:Cr,PinIcon:hr,PlayAllHollowIcon:Ar,PlayBackIcon:br,PlayHollowIcon:Tr,PlayIcon:_r,PlayNextIcon:gr,PlusIcon:yr,PointerDefaultIcon:kr,PointerHandIcon:Br,PowerIcon:Or,PrintIcon:Lr,ProceedIcon:Pr,ProfileIcon:fr,PullRequestIcon:Rr,QuestionIcon:wr,RSSIcon:Er,RedirectIcon:Dr,ReduxIcon:vr,RefreshIcon:Hr,ReplyIcon:xr,RepoIcon:Mr,RequestChangeIcon:Ur,RewindIcon:Fr,RulerIcon:Gr,SaveIcon:Nr,SearchIcon:Wr,ShareAltIcon:Kr,ShareIcon:Yr,ShieldIcon:qr,SideBySideIcon:Vr,SidebarAltIcon:zr,SidebarAltToggleIcon:Zr,SidebarIcon:jr,SidebarToggleIcon:Jr,SpeakerIcon:Qr,StackedIcon:Xr,StarHollowIcon:$r,StarIcon:oI,StatusFailIcon:nI,StatusIcon:eI,StatusPassIcon:cI,StatusWarnIcon:tI,StickerIcon:rI,StopAltHollowIcon:II,StopAltIcon:aI,StopIcon:lI,StorybookIcon:iI,StructureIcon:sI,SubtractIcon:dI,SunIcon:pI,SupportIcon:uI,SwitchAltIcon:mI,SyncIcon:SI,TabletIcon:CI,ThumbsUpIcon:hI,TimeIcon:AI,TimerIcon:bI,TransferIcon:TI,TrashIcon:_I,TwitterIcon:gI,TypeIcon:yI,UbuntuIcon:kI,UndoIcon:BI,UnfoldIcon:OI,UnlockIcon:LI,UnpinIcon:PI,UploadIcon:fI,UserAddIcon:RI,UserAltIcon:wI,UserIcon:EI,UsersIcon:DI,VSCodeIcon:vI,VerifiedIcon:HI,VideoIcon:xI,WandIcon:MI,WatchIcon:UI,WindowsIcon:FI,WrenchIcon:GI,XIcon:NI,YoutubeIcon:WI,ZoomIcon:KI,ZoomOutIcon:YI,ZoomResetIcon:qI,iconList:VI}=__STORYBOOK_ICONS__;var b="storybook/i18n-addon",T="storybook/i18n-addon/tool",_="LOCALE_CHANGED",g=o=>typeof o=="string"?{title:o}:{title:o.title||"",icon:o.icon,right:o.right},y=(o,a,I)=>o?Object.entries(o).map(([t,l])=>({...g(l),id:t,active:t===a,onClick:()=>I(t)})):[{id:"none",title:"No locales in parameters",active:!0,onClick:()=>{}}],k=()=>{let[o,a]=u(),{locale:I,locales:t}=o,l=p({});return r.createElement(C,{closeOnOutsideClick:!0,placement:"top",tooltip:({onHide:A})=>r.createElement(S,{links:y(t,I,i=>{i!==I&&(a({locale:i}),l(_,i)),A()})}),trigger:"click"},r.createElement(m,{key:"i18n-locale",title:"Locale Selector"},r.createElement(h,null)))},B=k;s.register(b,()=>{s.add(T,{title:"Storybook i18n",type:d.TOOL,match:({viewMode:o})=>!!o?.match(/^(story|docs)$/),render:B})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
