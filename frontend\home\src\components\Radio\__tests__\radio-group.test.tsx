// radio-group.test.tsx
import * as React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { axe } from "jest-axe";
import { RadioGroup, RadioGroupItem } from "../";

describe("RadioGroup", () => {
  it("renders radio group with items", () => {
    render(
      <RadioGroup title="Test Group" data-testid="radio-group">
        <RadioGroupItem value="red" id="test-red" label="Red" data-testid="radio-red" />
        <RadioGroupItem value="blue" id="test-blue" label="Blue" data-testid="radio-blue" />
        <RadioGroupItem value="green" id="test-green" label="Green" data-testid="radio-green" />
      </RadioGroup>
    );

    expect(screen.getByTestId("radio-group")).toBeInTheDocument();
    expect(screen.getByText("Test Group")).toBeInTheDocument();
    expect(screen.getByTestId("radio-red")).toBeInTheDocument();
    expect(screen.getByText("Red")).toBeInTheDocument();
    expect(screen.getByTestId("radio-blue")).toBeInTheDocument();
    expect(screen.getByTestId("radio-green")).toBeInTheDocument();
  });

  it("handles selection change", () => {
    const handleValueChange = jest.fn();
    render(
      <RadioGroup title="Test Group" onValueChange={handleValueChange}>
        <RadioGroupItem value="red" id="test-red" label="Red" data-testid="radio-red" />
        <RadioGroupItem value="blue" id="test-blue" label="Blue" data-testid="radio-blue" />
      </RadioGroup>
    );

    fireEvent.click(screen.getByTestId("radio-blue"));
    expect(handleValueChange).toHaveBeenCalledWith("blue");
  });

  it("renders with description", () => {
    render(
      <RadioGroup 
        title="Test Group" 
        description="This is a description"
      >
        <RadioGroupItem value="red" id="test-red" label="Red" />
      </RadioGroup>
    );

    expect(screen.getByText("This is a description")).toBeInTheDocument();
  });

  it("renders with error message", () => {
    render(
      <RadioGroup 
        title="Test Group" 
        error="Please select an option"
      >
        <RadioGroupItem value="red" id="test-red" label="Red" />
      </RadioGroup>
    );

    expect(screen.getByText("Please select an option")).toBeInTheDocument();
  });

  it("renders with different variants", () => {
    const { rerender } = render(
      <RadioGroup title="Test Group" variant="card" data-testid="radio-group">
        <RadioGroupItem value="red" id="test-red" label="Red" />
      </RadioGroup>
    );

    let radioGroup = screen.getByTestId("radio-group");
    expect(radioGroup).toHaveClass("border");
    expect(radioGroup).toHaveClass("rounded-md");

    rerender(
      <RadioGroup title="Test Group" variant="outline" data-testid="radio-group">
        <RadioGroupItem value="red" id="test-red" label="Red" />
      </RadioGroup>
    );

    radioGroup = screen.getByTestId("radio-group");
    expect(radioGroup).toHaveClass("border-dashed");
    
    rerender(
      <RadioGroup title="Test Group" variant="buttonCard" data-testid="radio-group">
        <RadioGroupItem value="red" id="test-red" label="Red" variant="buttonCard" />
      </RadioGroup>
    );
    
    radioGroup = screen.getByTestId("radio-group");
    expect(radioGroup).toHaveClass("flex-wrap");
  });

  it("renders with horizontal orientation", () => {
    render(
      <RadioGroup 
        title="Test Group" 
        orientation="horizontal" 
        data-testid="radio-group"
      >
        <RadioGroupItem value="red" id="test-red" label="Red" />
        <RadioGroupItem value="blue" id="test-blue" label="Blue" />
      </RadioGroup>
    );

    const radioGroup = screen.getByTestId("radio-group");
    expect(radioGroup).toHaveClass("flex-row");
  });

  it("disables all radio items when group is disabled", () => {
    render(
      <RadioGroup title="Test Group" disabled>
        <RadioGroupItem value="red" id="test-red" label="Red" data-testid="radio-red" />
        <RadioGroupItem value="blue" id="test-blue" label="Blue" data-testid="radio-blue" />
      </RadioGroup>
    );

    expect(screen.getByTestId("radio-red")).toBeDisabled();
    expect(screen.getByTestId("radio-blue")).toBeDisabled();
  });

  it("renders radio items with different variants", () => {
    render(
      <RadioGroup title="Test Group">
        <RadioGroupItem value="default" id="test-default" label="Default" data-testid="radio-default" />
        <RadioGroupItem value="colorful" id="test-colorful" label="Colorful" variant="colorful" data-testid="radio-colorful" />
        <RadioGroupItem value="branded" id="test-branded" label="Branded" variant="branded" data-testid="radio-branded" />
        <RadioGroupItem value="button" id="test-button" label="Button" variant="buttonCard" data-testid="radio-button" />
      </RadioGroup>
    );

    expect(screen.getByTestId("radio-default")).toBeInTheDocument();
    expect(screen.getByTestId("radio-colorful")).toHaveClass("before:absolute");
    expect(screen.getByTestId("radio-branded")).toHaveClass("border-2");
    expect(screen.getByTestId("radio-button")).toHaveClass("sr-only");
    expect(screen.getByText("Button")).toBeInTheDocument();
  });

  it("renders radio items with different sizes", () => {
    render(
      <RadioGroup title="Test Group">
        <RadioGroupItem value="small" id="test-sm" label="Small" size="sm" data-testid="radio-sm" />
        <RadioGroupItem value="default" id="test-default" label="Default" data-testid="radio-default" />
        <RadioGroupItem value="large" id="test-lg" label="Large" size="lg" data-testid="radio-lg" />
      </RadioGroup>
    );

    expect(screen.getByTestId("radio-sm")).toHaveClass("h-3");
    expect(screen.getByTestId("radio-default")).toHaveClass("h-4");
    expect(screen.getByTestId("radio-lg")).toHaveClass("h-5");
  });

  it("passes a11y checks", async () => {
    const { container } = render(
      <RadioGroup title="Test Group">
        <RadioGroupItem value="red" id="test-red" label="Red" />
        <RadioGroupItem value="blue" id="test-blue" label="Blue" />
        <RadioGroupItem value="green" id="test-green" label="Green" />
      </RadioGroup>
    );

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});