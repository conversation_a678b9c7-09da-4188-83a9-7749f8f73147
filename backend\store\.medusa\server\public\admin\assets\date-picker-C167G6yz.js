import{r as o,aM as Mt,bU as Zl,bV as q,I as Wl,m as ue,aj as Yl,bW as Nr}from"./index-Bwql5Dzz.js";import{c as Hl}from"./clsx-B-dksMZM.js";import{P as zt}from"./popover-B2TSwh5F.js";import{X as Gl}from"./x-mark-mini-DvSTI7zK.js";import{T as Jl}from"./triangle-left-mini-Bu6679Aa.js";var ql=Object.defineProperty,Bt=Object.getOwnPropertySymbols,jr=Object.prototype.hasOwnProperty,Ur=Object.prototype.propertyIsEnumerable,Ga=(e,t,a)=>t in e?ql(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,Ql=(e,t)=>{for(var a in t)jr.call(t,a)&&Ga(e,a,t[a]);if(Bt)for(var a of Bt(t))Ur.call(t,a)&&Ga(e,a,t[a]);return e},Xl=(e,t)=>{var a={};for(var r in e)jr.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&Bt)for(var r of Bt(e))t.indexOf(r)<0&&Ur.call(e,r)&&(a[r]=e[r]);return a};const Kr=o.forwardRef((e,t)=>{var a=e,{color:r="currentColor"}=a,u=Xl(a,["color"]);return o.createElement("svg",Ql({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},u),o.createElement("path",{fill:r,d:"M6.611 8.611c0-.49.4-.889.889-.889a.89.89 0 0 1 0 1.778.89.89 0 0 1-.889-.889M10.611 9.5c.49 0 .889-.4.889-.889a.89.89 0 0 0-.889-.889.89.89 0 0 0-.889.89c0 .489.4.888.89.888M6.611 11.278c0-.49.4-.89.889-.89a.89.89 0 0 1 0 1.778.89.89 0 0 1-.889-.888M4.389 10.389a.89.89 0 0 0-.889.889c0 .49.4.889.889.889a.89.89 0 0 0 0-1.778M9.722 11.278a.89.89 0 0 1 1.778 0c0 .49-.4.889-.889.889a.89.89 0 0 1-.889-.89"}),o.createElement("path",{fill:r,fillRule:"evenodd",d:"M4.611.306a.75.75 0 0 1 .75.75v1.027H9.64V1.056a.75.75 0 1 1 1.5 0v1.027h.583a2.53 2.53 0 0 1 2.528 2.528v7.556a2.53 2.53 0 0 1-2.528 2.527H3.278A2.53 2.53 0 0 1 .75 12.167V4.61a2.53 2.53 0 0 1 2.528-2.528h.583V1.056a.75.75 0 0 1 .75-.75M12.75 4.61v.584H2.25V4.61c0-.568.46-1.028 1.028-1.028h8.444c.568 0 1.028.46 1.028 1.028m0 2.084H2.25v5.472c0 .567.46 1.027 1.028 1.027h8.444c.568 0 1.028-.46 1.028-1.027z",clipRule:"evenodd"}))});Kr.displayName="CalendarMini";var eo=Object.defineProperty,Pt=Object.getOwnPropertySymbols,_r=Object.prototype.hasOwnProperty,zr=Object.prototype.propertyIsEnumerable,Ja=(e,t,a)=>t in e?eo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,to=(e,t)=>{for(var a in t)_r.call(t,a)&&Ja(e,a,t[a]);if(Pt)for(var a of Pt(t))zr.call(t,a)&&Ja(e,a,t[a]);return e},ao=(e,t)=>{var a={};for(var r in e)_r.call(e,r)&&t.indexOf(r)<0&&(a[r]=e[r]);if(e!=null&&Pt)for(var r of Pt(e))t.indexOf(r)<0&&zr.call(e,r)&&(a[r]=e[r]);return a};const Zr=o.forwardRef((e,t)=>{var a=e,{color:r="currentColor"}=a,u=ao(a,["color"]);return o.createElement("svg",to({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},u),o.createElement("g",{stroke:r,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},o.createElement("path",{d:"m12.389.833 1.778 1.778M2.611.833.833 2.611M7.5 13.056a5.556 5.556 0 1 0 0-11.112 5.556 5.556 0 0 0 0 11.112M3.572 11.428l-1.628 1.627M11.428 11.428l1.627 1.627"}),o.createElement("path",{d:"M7.5 4.611V7.5l2.444 1.556"})),o.createElement("defs",null,o.createElement("clipPath",{id:"a"},o.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});Zr.displayName="Clock";function Ve(e,t){return e-t*Math.floor(e/t)}const Wr=1721426;function Ee(e,t,a,r){t=it(e,t);let u=t-1,n=-2;return a<=2?n=0:he(t)&&(n=-1),Wr-1+365*u+Math.floor(u/4)-Math.floor(u/100)+Math.floor(u/400)+Math.floor((367*a-362)/12+n+r)}function he(e){return e%4===0&&(e%100!==0||e%400===0)}function it(e,t){return e==="BC"?1-t:t}function Vt(e){let t="AD";return e<=0&&(t="BC",e=1-e),[t,e]}const ro={standard:[31,28,31,30,31,30,31,31,30,31,30,31],leapyear:[31,29,31,30,31,30,31,31,30,31,30,31]};class G{fromJulianDay(t){let a=t,r=a-Wr,u=Math.floor(r/146097),n=Ve(r,146097),i=Math.floor(n/36524),l=Ve(n,36524),s=Math.floor(l/1461),c=Ve(l,1461),D=Math.floor(c/365),$=u*400+i*100+s*4+D+(i!==4&&D!==4?1:0),[b,E]=Vt($),p=a-Ee(b,E,1,1),x=2;a<Ee(b,E,3,1)?x=0:he(E)&&(x=1);let w=Math.floor(((p+x)*12+373)/367),R=a-Ee(b,E,w,1)+1;return new K(b,E,w,R)}toJulianDay(t){return Ee(t.era,t.year,t.month,t.day)}getDaysInMonth(t){return ro[he(t.year)?"leapyear":"standard"][t.month-1]}getMonthsInYear(t){return 12}getDaysInYear(t){return he(t.year)?366:365}getYearsInEra(t){return 9999}getEras(){return["BC","AD"]}isInverseEra(t){return t.era==="BC"}balanceDate(t){t.year<=0&&(t.era=t.era==="BC"?"AD":"BC",t.year=1-t.year)}constructor(){this.identifier="gregory"}}const uo={"001":1,AD:1,AE:6,AF:6,AI:1,AL:1,AM:1,AN:1,AR:1,AT:1,AU:1,AX:1,AZ:1,BA:1,BE:1,BG:1,BH:6,BM:1,BN:1,BY:1,CH:1,CL:1,CM:1,CN:1,CR:1,CY:1,CZ:1,DE:1,DJ:6,DK:1,DZ:6,EC:1,EE:1,EG:6,ES:1,FI:1,FJ:1,FO:1,FR:1,GB:1,GE:1,GF:1,GP:1,GR:1,HR:1,HU:1,IE:1,IQ:6,IR:6,IS:1,IT:1,JO:6,KG:1,KW:6,KZ:1,LB:1,LI:1,LK:1,LT:1,LU:1,LV:1,LY:6,MC:1,MD:1,ME:1,MK:1,MN:1,MQ:1,MV:5,MY:1,NL:1,NO:1,NZ:1,OM:6,PL:1,QA:6,RE:1,RO:1,RS:1,RU:1,SD:6,SE:1,SI:1,SK:1,SM:1,SY:6,TJ:1,TM:1,TR:1,UA:1,UY:1,UZ:1,VA:1,VN:1,XK:1};function _(e,t){return t=N(t,e.calendar),e.era===t.era&&e.year===t.year&&e.month===t.month&&e.day===t.day}function no(e,t){return e.calendar.identifier===t.calendar.identifier&&e.era===t.era&&e.year===t.year&&e.month===t.month&&e.day===t.day}function io(e,t){return _(e,It(t))}const lo={sun:0,mon:1,tue:2,wed:3,thu:4,fri:5,sat:6};function Aa(e,t,a){let r=e.calendar.toJulianDay(e),u=a?lo[a]:$o(t),n=Math.ceil(r+1-u)%7;return n<0&&(n+=7),n}function Yr(e){return oe(Date.now(),e)}function It(e){return ne(Yr(e))}function Hr(e,t){return e.calendar.toJulianDay(e)-t.calendar.toJulianDay(t)}function Gr(e,t){return qa(e)-qa(t)}function qa(e){return e.hour*36e5+e.minute*6e4+e.second*1e3+e.millisecond}let Zt=null;function Ne(){return Zt==null&&(Zt=new Intl.DateTimeFormat().resolvedOptions().timeZone),Zt}function lt(e){return e.subtract({days:e.day-1})}function sa(e){return e.add({days:e.calendar.getDaysInMonth(e)-e.day})}function oo(e){return lt(e.subtract({months:e.month-1}))}function so(e){return e.calendar.getMinimumMonthInYear?e.calendar.getMinimumMonthInYear(e):1}function co(e){return e.calendar.getMinimumDayInMonth?e.calendar.getMinimumDayInMonth(e):1}function Xe(e,t,a){let r=Aa(e,t,a);return e.subtract({days:r})}function fo(e,t,a){return Xe(e,t,a).add({days:6})}const Qa=new Map;function mo(e){if(Intl.Locale){let a=Qa.get(e);return a||(a=new Intl.Locale(e).maximize().region,a&&Qa.set(e,a)),a}let t=e.split("-")[1];return t==="u"?void 0:t}function $o(e){let t=mo(e);return t&&uo[t]||0}function Do(e,t,a){let r=e.calendar.getDaysInMonth(e);return Math.ceil((Aa(lt(e),t,a)+r)/7)}function Jr(e,t){return e&&t?e.compare(t)<=0?e:t:e||t}function qr(e,t){return e&&t?e.compare(t)>=0?e:t:e||t}function je(e){e=N(e,new G);let t=it(e.era,e.year);return Qr(t,e.month,e.day,e.hour,e.minute,e.second,e.millisecond)}function Qr(e,t,a,r,u,n,i){let l=new Date;return l.setUTCHours(r,u,n,i),l.setUTCFullYear(e,t-1,a),l.getTime()}function da(e,t){if(t==="UTC")return 0;if(e>0&&t===Ne())return new Date(e).getTimezoneOffset()*-6e4;let{year:a,month:r,day:u,hour:n,minute:i,second:l}=Xr(e,t);return Qr(a,r,u,n,i,l,0)-Math.floor(e/1e3)*1e3}const Xa=new Map;function Xr(e,t){let a=Xa.get(t);a||(a=new Intl.DateTimeFormat("en-US",{timeZone:t,hour12:!1,era:"short",year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}),Xa.set(t,a));let r=a.formatToParts(new Date(e)),u={};for(let n of r)n.type!=="literal"&&(u[n.type]=n.value);return{year:u.era==="BC"||u.era==="B"?-u.year+1:+u.year,month:+u.month,day:+u.day,hour:u.hour==="24"?0:+u.hour,minute:+u.minute,second:+u.second}}const er=864e5;function ho(e,t,a,r){return(a===r?[a]:[a,r]).filter(n=>po(e,t,n))}function po(e,t,a){let r=Xr(a,t);return e.year===r.year&&e.month===r.month&&e.day===r.day&&e.hour===r.hour&&e.minute===r.minute&&e.second===r.second}function le(e,t,a="compatible"){let r=ce(e);if(t==="UTC")return je(r);if(t===Ne()&&a==="compatible"){r=N(r,new G);let s=new Date,c=it(r.era,r.year);return s.setFullYear(c,r.month-1,r.day),s.setHours(r.hour,r.minute,r.second,r.millisecond),s.getTime()}let u=je(r),n=da(u-er,t),i=da(u+er,t),l=ho(r,t,u-n,u-i);if(l.length===1)return l[0];if(l.length>1)switch(a){case"compatible":case"earlier":return l[0];case"later":return l[l.length-1];case"reject":throw new RangeError("Multiple possible absolute times found")}switch(a){case"earlier":return Math.min(u-n,u-i);case"compatible":case"later":return Math.max(u-n,u-i);case"reject":throw new RangeError("No such absolute time found")}}function eu(e,t,a="compatible"){return new Date(le(e,t,a))}function oe(e,t){let a=da(e,t),r=new Date(e+a),u=r.getUTCFullYear(),n=r.getUTCMonth()+1,i=r.getUTCDate(),l=r.getUTCHours(),s=r.getUTCMinutes(),c=r.getUTCSeconds(),D=r.getUTCMilliseconds();return new at(u<1?"BC":"AD",u<1?-u+1:u,n,i,t,a,l,s,c,D)}function ne(e){return new K(e.calendar,e.era,e.year,e.month,e.day)}function ce(e,t){let a=0,r=0,u=0,n=0;if("timeZone"in e)({hour:a,minute:r,second:u,millisecond:n}=e);else if("hour"in e&&!t)return e;return t&&({hour:a,minute:r,second:u,millisecond:n}=t),new tt(e.calendar,e.era,e.year,e.month,e.day,a,r,u,n)}function tr(e){return new st(e.hour,e.minute,e.second,e.millisecond)}function N(e,t){if(e.calendar.identifier===t.identifier)return e;let a=t.fromJulianDay(e.calendar.toJulianDay(e)),r=e.copy();return r.calendar=t,r.era=a.era,r.year=a.year,r.month=a.month,r.day=a.day,Be(r),r}function tu(e,t,a){if(e instanceof at)return e.timeZone===t?e:yo(e,t);let r=le(e,t,a);return oe(r,t)}function bo(e){let t=je(e)-e.offset;return new Date(t)}function yo(e,t){let a=je(e)-e.offset;return N(oe(a,t),e.calendar)}const ze=36e5;function Ot(e,t){let a=e.copy(),r="hour"in a?nu(a,t):0;ca(a,t.years||0),a.calendar.balanceYearMonth&&a.calendar.balanceYearMonth(a,e),a.month+=t.months||0,fa(a),au(a),a.day+=(t.weeks||0)*7,a.day+=t.days||0,a.day+=r,go(a),a.calendar.balanceDate&&a.calendar.balanceDate(a),a.year<1&&(a.year=1,a.month=1,a.day=1);let u=a.calendar.getYearsInEra(a);if(a.year>u){var n,i;let s=(n=(i=a.calendar).isInverseEra)===null||n===void 0?void 0:n.call(i,a);a.year=u,a.month=s?1:a.calendar.getMonthsInYear(a),a.day=s?1:a.calendar.getDaysInMonth(a)}a.month<1&&(a.month=1,a.day=1);let l=a.calendar.getMonthsInYear(a);return a.month>l&&(a.month=l,a.day=a.calendar.getDaysInMonth(a)),a.day=Math.max(1,Math.min(a.calendar.getDaysInMonth(a),a.day)),a}function ca(e,t){var a,r;!((a=(r=e.calendar).isInverseEra)===null||a===void 0)&&a.call(r,e)&&(t=-t),e.year+=t}function fa(e){for(;e.month<1;)ca(e,-1),e.month+=e.calendar.getMonthsInYear(e);let t=0;for(;e.month>(t=e.calendar.getMonthsInYear(e));)e.month-=t,ca(e,1)}function go(e){for(;e.day<1;)e.month--,fa(e),e.day+=e.calendar.getDaysInMonth(e);for(;e.day>e.calendar.getDaysInMonth(e);)e.day-=e.calendar.getDaysInMonth(e),e.month++,fa(e)}function au(e){e.month=Math.max(1,Math.min(e.calendar.getMonthsInYear(e),e.month)),e.day=Math.max(1,Math.min(e.calendar.getDaysInMonth(e),e.day))}function Be(e){e.calendar.constrainDate&&e.calendar.constrainDate(e),e.year=Math.max(1,Math.min(e.calendar.getYearsInEra(e),e.year)),au(e)}function Ra(e){let t={};for(let a in e)typeof e[a]=="number"&&(t[a]=-e[a]);return t}function ru(e,t){return Ot(e,Ra(t))}function ka(e,t){let a=e.copy();return t.era!=null&&(a.era=t.era),t.year!=null&&(a.year=t.year),t.month!=null&&(a.month=t.month),t.day!=null&&(a.day=t.day),Be(a),a}function et(e,t){let a=e.copy();return t.hour!=null&&(a.hour=t.hour),t.minute!=null&&(a.minute=t.minute),t.second!=null&&(a.second=t.second),t.millisecond!=null&&(a.millisecond=t.millisecond),uu(a),a}function vo(e){e.second+=Math.floor(e.millisecond/1e3),e.millisecond=ct(e.millisecond,1e3),e.minute+=Math.floor(e.second/60),e.second=ct(e.second,60),e.hour+=Math.floor(e.minute/60),e.minute=ct(e.minute,60);let t=Math.floor(e.hour/24);return e.hour=ct(e.hour,24),t}function uu(e){e.millisecond=Math.max(0,Math.min(e.millisecond,1e3)),e.second=Math.max(0,Math.min(e.second,59)),e.minute=Math.max(0,Math.min(e.minute,59)),e.hour=Math.max(0,Math.min(e.hour,23))}function ct(e,t){let a=e%t;return a<0&&(a+=t),a}function nu(e,t){return e.hour+=t.hours||0,e.minute+=t.minutes||0,e.second+=t.seconds||0,e.millisecond+=t.milliseconds||0,vo(e)}function iu(e,t){let a=e.copy();return nu(a,t),a}function xo(e,t){return iu(e,Ra(t))}function Ta(e,t,a,r){let u=e.copy();switch(t){case"era":{let l=e.calendar.getEras(),s=l.indexOf(e.era);if(s<0)throw new Error("Invalid era: "+e.era);s=se(s,a,0,l.length-1,r==null?void 0:r.round),u.era=l[s],Be(u);break}case"year":var n,i;!((n=(i=u.calendar).isInverseEra)===null||n===void 0)&&n.call(i,u)&&(a=-a),u.year=se(e.year,a,-1/0,9999,r==null?void 0:r.round),u.year===-1/0&&(u.year=1),u.calendar.balanceYearMonth&&u.calendar.balanceYearMonth(u,e);break;case"month":u.month=se(e.month,a,1,e.calendar.getMonthsInYear(e),r==null?void 0:r.round);break;case"day":u.day=se(e.day,a,1,e.calendar.getDaysInMonth(e),r==null?void 0:r.round);break;default:throw new Error("Unsupported field "+t)}return e.calendar.balanceDate&&e.calendar.balanceDate(u),Be(u),u}function Ma(e,t,a,r){let u=e.copy();switch(t){case"hour":{let n=e.hour,i=0,l=23;if((r==null?void 0:r.hourCycle)===12){let s=n>=12;i=s?12:0,l=s?23:11}u.hour=se(n,a,i,l,r==null?void 0:r.round);break}case"minute":u.minute=se(e.minute,a,0,59,r==null?void 0:r.round);break;case"second":u.second=se(e.second,a,0,59,r==null?void 0:r.round);break;case"millisecond":u.millisecond=se(e.millisecond,a,0,999,r==null?void 0:r.round);break;default:throw new Error("Unsupported field "+t)}return u}function se(e,t,a,r,u=!1){if(u){e+=Math.sign(t),e<a&&(e=r);let n=Math.abs(t);t>0?e=Math.ceil(e/n)*n:e=Math.floor(e/n)*n,e>r&&(e=a)}else e+=t,e<a?e=r-(a-e-1):e>r&&(e=a+(e-r-1));return e}function lu(e,t){let a;if(t.years!=null&&t.years!==0||t.months!=null&&t.months!==0||t.weeks!=null&&t.weeks!==0||t.days!=null&&t.days!==0){let u=Ot(ce(e),{years:t.years,months:t.months,weeks:t.weeks,days:t.days});a=le(u,e.timeZone)}else a=je(e)-e.offset;a+=t.milliseconds||0,a+=(t.seconds||0)*1e3,a+=(t.minutes||0)*6e4,a+=(t.hours||0)*36e5;let r=oe(a,e.timeZone);return N(r,e.calendar)}function Eo(e,t){return lu(e,Ra(t))}function Co(e,t,a,r){switch(t){case"hour":{let u=0,n=23;if((r==null?void 0:r.hourCycle)===12){let p=e.hour>=12;u=p?12:0,n=p?23:11}let i=ce(e),l=N(et(i,{hour:u}),new G),s=[le(l,e.timeZone,"earlier"),le(l,e.timeZone,"later")].filter(p=>oe(p,e.timeZone).day===l.day)[0],c=N(et(i,{hour:n}),new G),D=[le(c,e.timeZone,"earlier"),le(c,e.timeZone,"later")].filter(p=>oe(p,e.timeZone).day===c.day).pop(),$=je(e)-e.offset,b=Math.floor($/ze),E=$%ze;return $=se(b,a,Math.floor(s/ze),Math.floor(D/ze),r==null?void 0:r.round)*ze+E,N(oe($,e.timeZone),e.calendar)}case"minute":case"second":case"millisecond":return Ma(e,t,a,r);case"era":case"year":case"month":case"day":{let u=Ta(ce(e),t,a,r),n=le(u,e.timeZone);return N(oe(n,e.timeZone),e.calendar)}default:throw new Error("Unsupported field "+t)}}function Bo(e,t,a){let r=ce(e),u=et(ka(r,t),t);if(u.compare(r)===0)return e;let n=le(u,e.timeZone,a);return N(oe(n,e.timeZone),e.calendar)}function ou(e){return`${String(e.hour).padStart(2,"0")}:${String(e.minute).padStart(2,"0")}:${String(e.second).padStart(2,"0")}${e.millisecond?String(e.millisecond/1e3).slice(1):""}`}function su(e){let t=N(e,new G),a;return t.era==="BC"?a=t.year===1?"0000":"-"+String(Math.abs(1-t.year)).padStart(6,"00"):a=String(t.year).padStart(4,"0"),`${a}-${String(t.month).padStart(2,"0")}-${String(t.day).padStart(2,"0")}`}function du(e){return`${su(e)}T${ou(e)}`}function Po(e){let t=Math.sign(e)<0?"-":"+";e=Math.abs(e);let a=Math.floor(e/36e5),r=e%36e5/6e4;return`${t}${String(a).padStart(2,"0")}:${String(r).padStart(2,"0")}`}function wo(e){return`${du(e)}${Po(e.offset)}[${e.timeZone}]`}function Fo(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function ot(e,t,a){Fo(e,t),t.set(e,a)}function Va(e){let t=typeof e[0]=="object"?e.shift():new G,a;if(typeof e[0]=="string")a=e.shift();else{let i=t.getEras();a=i[i.length-1]}let r=e.shift(),u=e.shift(),n=e.shift();return[t,a,r,u,n]}var So=new WeakMap;class K{copy(){return this.era?new K(this.calendar,this.era,this.year,this.month,this.day):new K(this.calendar,this.year,this.month,this.day)}add(t){return Ot(this,t)}subtract(t){return ru(this,t)}set(t){return ka(this,t)}cycle(t,a,r){return Ta(this,t,a,r)}toDate(t){return eu(this,t)}toString(){return su(this)}compare(t){return Hr(this,t)}constructor(...t){ot(this,So,{writable:!0,value:void 0});let[a,r,u,n,i]=Va(t);this.calendar=a,this.era=r,this.year=u,this.month=n,this.day=i,Be(this)}}var Ao=new WeakMap;class st{copy(){return new st(this.hour,this.minute,this.second,this.millisecond)}add(t){return iu(this,t)}subtract(t){return xo(this,t)}set(t){return et(this,t)}cycle(t,a,r){return Ma(this,t,a,r)}toString(){return ou(this)}compare(t){return Gr(this,t)}constructor(t=0,a=0,r=0,u=0){ot(this,Ao,{writable:!0,value:void 0}),this.hour=t,this.minute=a,this.second=r,this.millisecond=u,uu(this)}}var Ro=new WeakMap;class tt{copy(){return this.era?new tt(this.calendar,this.era,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond):new tt(this.calendar,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond)}add(t){return Ot(this,t)}subtract(t){return ru(this,t)}set(t){return ka(et(this,t),t)}cycle(t,a,r){switch(t){case"era":case"year":case"month":case"day":return Ta(this,t,a,r);default:return Ma(this,t,a,r)}}toDate(t,a){return eu(this,t,a)}toString(){return du(this)}compare(t){let a=Hr(this,t);return a===0?Gr(this,ce(t)):a}constructor(...t){ot(this,Ro,{writable:!0,value:void 0});let[a,r,u,n,i]=Va(t);this.calendar=a,this.era=r,this.year=u,this.month=n,this.day=i,this.hour=t.shift()||0,this.minute=t.shift()||0,this.second=t.shift()||0,this.millisecond=t.shift()||0,Be(this)}}var ko=new WeakMap;class at{copy(){return this.era?new at(this.calendar,this.era,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond):new at(this.calendar,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond)}add(t){return lu(this,t)}subtract(t){return Eo(this,t)}set(t,a){return Bo(this,t,a)}cycle(t,a,r){return Co(this,t,a,r)}toDate(){return bo(this)}toString(){return wo(this)}toAbsoluteString(){return this.toDate().toISOString()}compare(t){return this.toDate().getTime()-tu(t,this.timeZone).toDate().getTime()}constructor(...t){ot(this,ko,{writable:!0,value:void 0});let[a,r,u,n,i]=Va(t),l=t.shift(),s=t.shift();this.calendar=a,this.era=r,this.year=u,this.month=n,this.day=i,this.timeZone=l,this.offset=s,this.hour=t.shift()||0,this.minute=t.shift()||0,this.second=t.shift()||0,this.millisecond=t.shift()||0,Be(this)}}const Ie=[[1868,9,8],[1912,7,30],[1926,12,25],[1989,1,8],[2019,5,1]],To=[[1912,7,29],[1926,12,24],[1989,1,7],[2019,4,30]],gt=[1867,1911,1925,1988,2018],$e=["meiji","taisho","showa","heisei","reiwa"];function ar(e){const t=Ie.findIndex(([a,r,u])=>e.year<a||e.year===a&&e.month<r||e.year===a&&e.month===r&&e.day<u);return t===-1?Ie.length-1:t===0?0:t-1}function Wt(e){let t=gt[$e.indexOf(e.era)];if(!t)throw new Error("Unknown era: "+e.era);return new K(e.year+t,e.month,e.day)}class Mo extends G{fromJulianDay(t){let a=super.fromJulianDay(t),r=ar(a);return new K(this,$e[r],a.year-gt[r],a.month,a.day)}toJulianDay(t){return super.toJulianDay(Wt(t))}balanceDate(t){let a=Wt(t),r=ar(a);$e[r]!==t.era&&(t.era=$e[r],t.year=a.year-gt[r]),this.constrainDate(t)}constrainDate(t){let a=$e.indexOf(t.era),r=To[a];if(r!=null){let[u,n,i]=r,l=u-gt[a];t.year=Math.max(1,Math.min(l,t.year)),t.year===l&&(t.month=Math.min(n,t.month),t.month===n&&(t.day=Math.min(i,t.day)))}if(t.year===1&&a>=0){let[,u,n]=Ie[a];t.month=Math.max(u,t.month),t.month===u&&(t.day=Math.max(n,t.day))}}getEras(){return $e}getYearsInEra(t){let a=$e.indexOf(t.era),r=Ie[a],u=Ie[a+1];if(u==null)return 9999-r[0]+1;let n=u[0]-r[0];return(t.month<u[1]||t.month===u[1]&&t.day<u[2])&&n++,n}getDaysInMonth(t){return super.getDaysInMonth(Wt(t))}getMinimumMonthInYear(t){let a=rr(t);return a?a[1]:1}getMinimumDayInMonth(t){let a=rr(t);return a&&t.month===a[1]?a[2]:1}constructor(...t){super(...t),this.identifier="japanese"}}function rr(e){if(e.year===1){let t=$e.indexOf(e.era);return Ie[t]}}const cu=-543;class Vo extends G{fromJulianDay(t){let a=super.fromJulianDay(t),r=it(a.era,a.year);return new K(this,r-cu,a.month,a.day)}toJulianDay(t){return super.toJulianDay(ur(t))}getEras(){return["BE"]}getDaysInMonth(t){return super.getDaysInMonth(ur(t))}balanceDate(){}constructor(...t){super(...t),this.identifier="buddhist"}}function ur(e){let[t,a]=Vt(e.year+cu);return new K(t,a,e.month,e.day)}const wt=1911;function fu(e){return e.era==="minguo"?e.year+wt:1-e.year+wt}function nr(e){let t=e-wt;return t>0?["minguo",t]:["before_minguo",1-t]}class Io extends G{fromJulianDay(t){let a=super.fromJulianDay(t),r=it(a.era,a.year),[u,n]=nr(r);return new K(this,u,n,a.month,a.day)}toJulianDay(t){return super.toJulianDay(ir(t))}getEras(){return["before_minguo","minguo"]}balanceDate(t){let[a,r]=nr(fu(t));t.era=a,t.year=r}isInverseEra(t){return t.era==="before_minguo"}getDaysInMonth(t){return super.getDaysInMonth(ir(t))}getYearsInEra(t){return t.era==="before_minguo"?9999:9999-wt}constructor(...t){super(...t),this.identifier="roc"}}function ir(e){let[t,a]=Vt(fu(e));return new K(t,a,e.month,e.day)}const lr=1948320,or=[0,31,62,93,124,155,186,216,246,276,306,336];class Oo{fromJulianDay(t){let a=t-lr,r=1+Math.floor((33*a+3)/12053),u=365*(r-1)+Math.floor((8*r+21)/33),n=a-u,i=n<216?Math.floor(n/31):Math.floor((n-6)/30),l=n-or[i]+1;return new K(this,r,i+1,l)}toJulianDay(t){let a=lr-1+365*(t.year-1)+Math.floor((8*t.year+21)/33);return a+=or[t.month-1],a+=t.day,a}getMonthsInYear(){return 12}getDaysInMonth(t){return t.month<=6?31:t.month<=11||Ve(25*t.year+11,33)<8?30:29}getEras(){return["AP"]}getYearsInEra(){return 9377}constructor(){this.identifier="persian"}}const Yt=78,sr=80;class Lo extends G{fromJulianDay(t){let a=super.fromJulianDay(t),r=a.year-Yt,u=t-Ee(a.era,a.year,1,1),n;u<sr?(r--,n=he(a.year-1)?31:30,u+=n+155+90+10):(n=he(a.year)?31:30,u-=sr);let i,l;if(u<n)i=1,l=u+1;else{let s=u-n;s<155?(i=Math.floor(s/31)+2,l=s%31+1):(s-=155,i=Math.floor(s/30)+7,l=s%30+1)}return new K(this,r,i,l)}toJulianDay(t){let a=t.year+Yt,[r,u]=Vt(a),n,i;return he(u)?(n=31,i=Ee(r,u,3,21)):(n=30,i=Ee(r,u,3,22)),t.month===1?i+t.day-1:(i+=n+Math.min(t.month-2,5)*31,t.month>=8&&(i+=(t.month-7)*30),i+=t.day-1,i)}getDaysInMonth(t){return t.month===1&&he(t.year+Yt)||t.month>=2&&t.month<=6?31:30}getYearsInEra(){return 9919}getEras(){return["saka"]}balanceDate(){}constructor(...t){super(...t),this.identifier="indian"}}const Ft=1948440,dr=1948439,X=1300,Re=1600,No=460322;function St(e,t,a,r){return r+Math.ceil(29.5*(a-1))+(t-1)*354+Math.floor((3+11*t)/30)+e-1}function mu(e,t,a){let r=Math.floor((30*(a-t)+10646)/10631),u=Math.min(12,Math.ceil((a-(29+St(t,r,1,1)))/29.5)+1),n=a-St(t,r,u,1)+1;return new K(e,r,u,n)}function cr(e){return(14+11*e)%30<11}class Ia{fromJulianDay(t){return mu(this,Ft,t)}toJulianDay(t){return St(Ft,t.year,t.month,t.day)}getDaysInMonth(t){let a=29+t.month%2;return t.month===12&&cr(t.year)&&a++,a}getMonthsInYear(){return 12}getDaysInYear(t){return cr(t.year)?355:354}getYearsInEra(){return 9665}getEras(){return["AH"]}constructor(){this.identifier="islamic-civil"}}class jo extends Ia{fromJulianDay(t){return mu(this,dr,t)}toJulianDay(t){return St(dr,t.year,t.month,t.day)}constructor(...t){super(...t),this.identifier="islamic-tbla"}}const Uo="qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=";let ma,Oe;function vt(e){return No+Oe[e-X]}function Ge(e,t){let a=e-X,r=1<<11-(t-1);return ma[a]&r?30:29}function fr(e,t){let a=vt(e);for(let r=1;r<t;r++)a+=Ge(e,r);return a}function mr(e){return Oe[e+1-X]-Oe[e-X]}class Ko extends Ia{fromJulianDay(t){let a=t-Ft,r=vt(X),u=vt(Re);if(a<r||a>u)return super.fromJulianDay(t);{let n=X-1,i=1,l=1;for(;l>0;){n++,l=a-vt(n)+1;let s=mr(n);if(l===s){i=12;break}else if(l<s){let c=Ge(n,i);for(i=1;l>c;)l-=c,i++,c=Ge(n,i);break}}return new K(this,n,i,a-fr(n,i)+1)}}toJulianDay(t){return t.year<X||t.year>Re?super.toJulianDay(t):Ft+fr(t.year,t.month)+(t.day-1)}getDaysInMonth(t){return t.year<X||t.year>Re?super.getDaysInMonth(t):Ge(t.year,t.month)}getDaysInYear(t){return t.year<X||t.year>Re?super.getDaysInYear(t):mr(t.year)}constructor(){if(super(),this.identifier="islamic-umalqura",ma||(ma=new Uint16Array(Uint8Array.from(atob(Uo),t=>t.charCodeAt(0)).buffer)),!Oe){Oe=new Uint32Array(Re-X+1);let t=0;for(let a=X;a<=Re;a++){Oe[a-X]=t;for(let r=1;r<=12;r++)t+=Ge(a,r)}}}}const $r=347997,$u=1080,Du=24*$u,_o=29,zo=12*$u+793,Zo=_o*Du+zo;function xe(e){return Ve(e*7+1,19)<7}function xt(e){let t=Math.floor((235*e-234)/19),a=12084+13753*t,r=t*29+Math.floor(a/25920);return Ve(3*(r+1),7)<3&&(r+=1),r}function Wo(e){let t=xt(e-1),a=xt(e);return xt(e+1)-a===356?2:a-t===382?1:0}function qe(e){return xt(e)+Wo(e)}function hu(e){return qe(e+1)-qe(e)}function Yo(e){let t=hu(e);switch(t>380&&(t-=30),t){case 353:return 0;case 354:return 1;case 355:return 2}}function ft(e,t){if(t>=6&&!xe(e)&&t++,t===4||t===7||t===9||t===11||t===13)return 29;let a=Yo(e);return t===2?a===2?30:29:t===3?a===0?29:30:t===6?xe(e)?30:0:30}class Ho{fromJulianDay(t){let a=t-$r,r=a*Du/Zo,u=Math.floor((19*r+234)/235)+1,n=qe(u),i=Math.floor(a-n);for(;i<1;)u--,n=qe(u),i=Math.floor(a-n);let l=1,s=0;for(;s<i;)s+=ft(u,l),l++;l--,s-=ft(u,l);let c=i-s;return new K(this,u,l,c)}toJulianDay(t){let a=qe(t.year);for(let r=1;r<t.month;r++)a+=ft(t.year,r);return a+t.day+$r}getDaysInMonth(t){return ft(t.year,t.month)}getMonthsInYear(t){return xe(t.year)?13:12}getDaysInYear(t){return hu(t.year)}getYearsInEra(){return 9999}getEras(){return["AM"]}balanceYearMonth(t,a){a.year!==t.year&&(xe(a.year)&&!xe(t.year)&&a.month>6?t.month--:!xe(a.year)&&xe(t.year)&&a.month>6&&t.month++)}constructor(){this.identifier="hebrew"}}const $a=1723856,Dr=1824665,Da=5500;function At(e,t,a,r){return e+365*t+Math.floor(t/4)+30*(a-1)+r-1}function Oa(e,t){let a=Math.floor(4*(t-e)/1461),r=1+Math.floor((t-At(e,a,1,1))/30),u=t+1-At(e,a,r,1);return[a,r,u]}function pu(e){return Math.floor(e%4/3)}function bu(e,t){return t%13!==0?30:pu(e)+5}class La{fromJulianDay(t){let[a,r,u]=Oa($a,t),n="AM";return a<=0&&(n="AA",a+=Da),new K(this,n,a,r,u)}toJulianDay(t){let a=t.year;return t.era==="AA"&&(a-=Da),At($a,a,t.month,t.day)}getDaysInMonth(t){return bu(t.year,t.month)}getMonthsInYear(){return 13}getDaysInYear(t){return 365+pu(t.year)}getYearsInEra(t){return t.era==="AA"?9999:9991}getEras(){return["AA","AM"]}constructor(){this.identifier="ethiopic"}}class Go extends La{fromJulianDay(t){let[a,r,u]=Oa($a,t);return a+=Da,new K(this,"AA",a,r,u)}getEras(){return["AA"]}getYearsInEra(){return 9999}constructor(...t){super(...t),this.identifier="ethioaa"}}class Jo extends La{fromJulianDay(t){let[a,r,u]=Oa(Dr,t),n="CE";return a<=0&&(n="BCE",a=1-a),new K(this,n,a,r,u)}toJulianDay(t){let a=t.year;return t.era==="BCE"&&(a=1-a),At(Dr,a,t.month,t.day)}getDaysInMonth(t){let a=t.year;return t.era==="BCE"&&(a=1-a),bu(a,t.month)}isInverseEra(t){return t.era==="BCE"}balanceDate(t){t.year<=0&&(t.era=t.era==="BCE"?"CE":"BCE",t.year=1-t.year)}getEras(){return["BCE","CE"]}getYearsInEra(t){return t.era==="BCE"?9999:9715}constructor(...t){super(...t),this.identifier="coptic"}}function yu(e){switch(e){case"buddhist":return new Vo;case"ethiopic":return new La;case"ethioaa":return new Go;case"coptic":return new Jo;case"hebrew":return new Ho;case"indian":return new Lo;case"islamic-civil":return new Ia;case"islamic-tbla":return new jo;case"islamic-umalqura":return new Ko;case"japanese":return new Mo;case"persian":return new Oo;case"roc":return new Io;case"gregory":default:return new G}}let Ht=new Map;class de{format(t){return this.formatter.format(t)}formatToParts(t){return this.formatter.formatToParts(t)}formatRange(t,a){if(typeof this.formatter.formatRange=="function")return this.formatter.formatRange(t,a);if(a<t)throw new RangeError("End date must be >= start date");return`${this.formatter.format(t)} – ${this.formatter.format(a)}`}formatRangeToParts(t,a){if(typeof this.formatter.formatRangeToParts=="function")return this.formatter.formatRangeToParts(t,a);if(a<t)throw new RangeError("End date must be >= start date");let r=this.formatter.formatToParts(t),u=this.formatter.formatToParts(a);return[...r.map(n=>({...n,source:"startRange"})),{type:"literal",value:" – ",source:"shared"},...u.map(n=>({...n,source:"endRange"}))]}resolvedOptions(){let t=this.formatter.resolvedOptions();return Xo()&&(this.resolvedHourCycle||(this.resolvedHourCycle=es(t.locale,this.options)),t.hourCycle=this.resolvedHourCycle,t.hour12=this.resolvedHourCycle==="h11"||this.resolvedHourCycle==="h12"),t.calendar==="ethiopic-amete-alem"&&(t.calendar="ethioaa"),t}constructor(t,a={}){this.formatter=gu(t,a),this.options=a}}const qo={true:{ja:"h11"},false:{}};function gu(e,t={}){if(typeof t.hour12=="boolean"&&Qo()){t={...t};let u=qo[String(t.hour12)][e.split("-")[0]],n=t.hour12?"h12":"h23";t.hourCycle=u??n,delete t.hour12}let a=e+(t?Object.entries(t).sort((u,n)=>u[0]<n[0]?-1:1).join():"");if(Ht.has(a))return Ht.get(a);let r=new Intl.DateTimeFormat(e,t);return Ht.set(a,r),r}let Gt=null;function Qo(){return Gt==null&&(Gt=new Intl.DateTimeFormat("en-US",{hour:"numeric",hour12:!1}).format(new Date(2020,2,3,0))==="24"),Gt}let Jt=null;function Xo(){return Jt==null&&(Jt=new Intl.DateTimeFormat("fr",{hour:"numeric",hour12:!1}).resolvedOptions().hourCycle==="h12"),Jt}function es(e,t){if(!t.timeStyle&&!t.hour)return;e=e.replace(/(-u-)?-nu-[a-zA-Z0-9]+/,""),e+=(e.includes("-u-")?"":"-u")+"-nu-latn";let a=gu(e,{...t,timeZone:void 0}),r=parseInt(a.formatToParts(new Date(2020,2,3,0)).find(n=>n.type==="hour").value,10),u=parseInt(a.formatToParts(new Date(2020,2,3,23)).find(n=>n.type==="hour").value,10);if(r===0&&u===23)return"h23";if(r===24&&u===23)return"h24";if(r===0&&u===11)return"h11";if(r===12&&u===11)return"h12";throw new Error("Unexpected hour cycle result")}const fe=typeof document<"u"?Mt.useLayoutEffect:()=>{};function Z(e){const t=o.useRef(null);return fe(()=>{t.current=e},[e]),o.useCallback((...a)=>{const r=t.current;return r==null?void 0:r(...a)},[])}function ts(e){let[t,a]=o.useState(e),r=o.useRef(null),u=Z(()=>{if(!r.current)return;let i=r.current.next();if(i.done){r.current=null;return}t===i.value?u():a(i.value)});fe(()=>{r.current&&u()});let n=Z(i=>{r.current=i(t),u()});return[t,n]}let as=!!(typeof window<"u"&&window.document&&window.document.createElement),De=new Map;function be(e){let[t,a]=o.useState(e),r=o.useRef(null),u=Zl(t),n=o.useCallback(i=>{r.current=i},[]);return as&&(De.has(u)&&!De.get(u).includes(n)?De.set(u,[...De.get(u),n]):De.set(u,[n])),fe(()=>{let i=u;return()=>{De.delete(i)}},[u]),o.useEffect(()=>{let i=r.current;i&&(r.current=null,a(i))}),u}function rs(e,t){if(e===t)return e;let a=De.get(e);if(a)return a.forEach(u=>u(t)),t;let r=De.get(t);return r?(r.forEach(u=>u(e)),e):t}function ha(e=[]){let t=be(),[a,r]=ts(t),u=o.useCallback(()=>{r(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,r]);return fe(u,[t,u,...e]),a}function vu(...e){return(...t)=>{for(let a of e)typeof a=="function"&&a(...t)}}const Y=e=>{var t;return(t=e==null?void 0:e.ownerDocument)!==null&&t!==void 0?t:document},Pe=e=>e&&"window"in e&&e.window===e?e:Y(e).defaultView||window;function H(...e){let t={...e[0]};for(let a=1;a<e.length;a++){let r=e[a];for(let u in r){let n=t[u],i=r[u];typeof n=="function"&&typeof i=="function"&&u[0]==="o"&&u[1]==="n"&&u.charCodeAt(2)>=65&&u.charCodeAt(2)<=90?t[u]=vu(n,i):(u==="className"||u==="UNSAFE_className")&&typeof n=="string"&&typeof i=="string"?t[u]=Hl(n,i):u==="id"&&n&&i?t.id=rs(n,i):t[u]=i!==void 0?i:n}}return t}const us=new Set(["id"]),ns=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),is=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),ls=/^(data-.*)$/;function Lt(e,t={}){let{labelable:a,isLink:r,propNames:u}=t,n={};for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&(us.has(i)||a&&ns.has(i)||r&&is.has(i)||u!=null&&u.has(i)||ls.test(i))&&(n[i]=e[i]);return n}function pe(e){if(os())e.focus({preventScroll:!0});else{let t=ss(e);e.focus(),ds(t)}}let mt=null;function os(){if(mt==null){mt=!1;try{document.createElement("div").focus({get preventScroll(){return mt=!0,!0}})}catch{}}return mt}function ss(e){let t=e.parentNode,a=[],r=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==r;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&a.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return r instanceof HTMLElement&&a.push({element:r,scrollTop:r.scrollTop,scrollLeft:r.scrollLeft}),a}function ds(e){for(let{element:t,scrollTop:a,scrollLeft:r}of e)t.scrollTop=a,t.scrollLeft=r}function Nt(e){var t;return typeof window>"u"||window.navigator==null?!1:((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.brands.some(a=>e.test(a.brand)))||e.test(window.navigator.userAgent)}function Na(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function ye(e){let t=null;return()=>(t==null&&(t=e()),t)}const Ue=ye(function(){return Na(/^Mac/i)}),cs=ye(function(){return Na(/^iPhone/i)}),xu=ye(function(){return Na(/^iPad/i)||Ue()&&navigator.maxTouchPoints>1}),ja=ye(function(){return cs()||xu()}),fs=ye(function(){return Nt(/AppleWebKit/i)&&!ms()}),ms=ye(function(){return Nt(/Chrome/i)}),Eu=ye(function(){return Nt(/Android/i)}),$s=ye(function(){return Nt(/Firefox/i)});function rt(e,t,a=!0){var r,u;let{metaKey:n,ctrlKey:i,altKey:l,shiftKey:s}=t;$s()&&(!((u=window.event)===null||u===void 0||(r=u.type)===null||r===void 0)&&r.startsWith("key"))&&e.target==="_blank"&&(Ue()?n=!0:i=!0);let c=fs()&&Ue()&&!xu()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:n,ctrlKey:i,altKey:l,shiftKey:s}):new MouseEvent("click",{metaKey:n,ctrlKey:i,altKey:l,shiftKey:s,bubbles:!0,cancelable:!0});rt.isOpening=a,pe(e),e.dispatchEvent(c),rt.isOpening=!1}rt.isOpening=!1;let Te=new Map,pa=new Set;function hr(){if(typeof window>"u")return;function e(r){return"propertyName"in r}let t=r=>{if(!e(r)||!r.target)return;let u=Te.get(r.target);u||(u=new Set,Te.set(r.target,u),r.target.addEventListener("transitioncancel",a,{once:!0})),u.add(r.propertyName)},a=r=>{if(!e(r)||!r.target)return;let u=Te.get(r.target);if(u&&(u.delete(r.propertyName),u.size===0&&(r.target.removeEventListener("transitioncancel",a),Te.delete(r.target)),Te.size===0)){for(let n of pa)n();pa.clear()}};document.body.addEventListener("transitionrun",t),document.body.addEventListener("transitionend",a)}typeof document<"u"&&(document.readyState!=="loading"?hr():document.addEventListener("DOMContentLoaded",hr));function Cu(e){requestAnimationFrame(()=>{Te.size===0?e():pa.add(e)})}function Bu(){let e=o.useRef(new Map),t=o.useCallback((u,n,i,l)=>{let s=l!=null&&l.once?(...c)=>{e.current.delete(i),i(...c)}:i;e.current.set(i,{type:n,eventTarget:u,fn:s,options:l}),u.addEventListener(n,s,l)},[]),a=o.useCallback((u,n,i,l)=>{var s;let c=((s=e.current.get(i))===null||s===void 0?void 0:s.fn)||i;u.removeEventListener(n,c,l),e.current.delete(i)},[]),r=o.useCallback(()=>{e.current.forEach((u,n)=>{a(u.eventTarget,u.type,n,u.options)})},[a]);return o.useEffect(()=>r,[r]),{addGlobalListener:t,removeGlobalListener:a,removeAllGlobalListeners:r}}function jt(e,t){let{id:a,"aria-label":r,"aria-labelledby":u}=e;return a=be(a),u&&r?u=[...new Set([a,...u.trim().split(/\s+/)])].join(" "):u&&(u=u.trim().split(/\s+/).join(" ")),!r&&!u&&t&&(r=t),{id:a,"aria-label":r,"aria-labelledby":u}}function pr(e,t){const a=o.useRef(!0),r=o.useRef(null);o.useEffect(()=>(a.current=!0,()=>{a.current=!1}),[]),o.useEffect(()=>{a.current?a.current=!1:(!r.current||t.some((u,n)=>!Object.is(u,r[n])))&&e(),r.current=t},t)}function Pu(e,t){fe(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}function ba(e,t){if(!e)return!1;let a=window.getComputedStyle(e),r=/(auto|scroll)/.test(a.overflow+a.overflowX+a.overflowY);return r&&t&&(r=e.scrollHeight!==e.clientHeight||e.scrollWidth!==e.clientWidth),r}function wu(e,t){let a=e;for(ba(a,t)&&(a=a.parentElement);a&&!ba(a,t);)a=a.parentElement;return a||document.scrollingElement||document.documentElement}function Ds(e,t){const a=[];for(;e&&e!==document.documentElement;)ba(e,t)&&a.push(e),e=e.parentElement;return a}let hs=0;const qt=new Map;function Ua(e){let[t,a]=o.useState();return fe(()=>{if(!e)return;let r=qt.get(e);if(r)a(r.element.id);else{let u=`react-aria-description-${hs++}`;a(u);let n=document.createElement("div");n.id=u,n.style.display="none",n.textContent=e,document.body.appendChild(n),r={refCount:0,element:n},qt.set(e,r)}return r.refCount++,()=>{r&&--r.refCount===0&&(r.element.remove(),qt.delete(e))}},[e]),{"aria-describedby":e?t:void 0}}function Qt(e,t,a,r){let u=Z(a),n=a==null;o.useEffect(()=>{if(n||!e.current)return;let i=e.current;return i.addEventListener(t,u,r),()=>{i.removeEventListener(t,u,r)}},[e,t,r,n,u])}function ps(e,t){let a=br(e,t,"left"),r=br(e,t,"top"),u=t.offsetWidth,n=t.offsetHeight,i=e.scrollLeft,l=e.scrollTop,{borderTopWidth:s,borderLeftWidth:c,scrollPaddingTop:D,scrollPaddingRight:$,scrollPaddingBottom:b,scrollPaddingLeft:E}=getComputedStyle(e),p=i+parseInt(c,10),x=l+parseInt(s,10),w=p+e.clientWidth,R=x+e.clientHeight,k=parseInt(D,10)||0,C=parseInt(b,10)||0,y=parseInt($,10)||0,A=parseInt(E,10)||0;a<=i+A?i=a-parseInt(c,10)-A:a+u>w-y&&(i+=a+u-w+y),r<=x+k?l=r-parseInt(s,10)-k:r+n>R-C&&(l+=r+n-R+C),e.scrollLeft=i,e.scrollTop=l}function br(e,t,a){const r=a==="left"?"offsetLeft":"offsetTop";let u=0;for(;t.offsetParent&&(u+=t[r],t.offsetParent!==e);){if(t.offsetParent.contains(e)){u-=e[r];break}t=t.offsetParent}return u}function Fu(e,t){if(e&&document.contains(e)){let i=document.scrollingElement||document.documentElement;if(window.getComputedStyle(i).overflow==="hidden"){let s=Ds(e);for(let c of s)ps(c,e)}else{var a;let{left:s,top:c}=e.getBoundingClientRect();e==null||(a=e.scrollIntoView)===null||a===void 0||a.call(e,{block:"nearest"});let{left:D,top:$}=e.getBoundingClientRect();if(Math.abs(s-D)>1||Math.abs(c-$)>1){var r,u,n;t==null||(u=t.containingElement)===null||u===void 0||(r=u.scrollIntoView)===null||r===void 0||r.call(u,{block:"center",inline:"center"}),(n=e.scrollIntoView)===null||n===void 0||n.call(e,{block:"nearest"})}}}}function ya(e){return e.mozInputSource===0&&e.isTrusted?!0:Eu()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function bs(e){return!Eu()&&e.width===0&&e.height===0||e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType==="mouse"}function Su(e,t){let a=o.useRef(null);return e&&a.current&&t(e,a.current)&&(e=a.current),a.current=e,e}function ys(e,t,a){let r=o.useRef(t),u=Z(()=>{a&&a(r.current)});o.useEffect(()=>{var n;let i=e==null||(n=e.current)===null||n===void 0?void 0:n.form;return i==null||i.addEventListener("reset",u),()=>{i==null||i.removeEventListener("reset",u)}},[e,u])}function Ke(e,t,a){let[r,u]=o.useState(e||t),n=o.useRef(e!==void 0),i=e!==void 0;o.useEffect(()=>{let c=n.current;c!==i&&console.warn(`WARN: A component changed from ${c?"controlled":"uncontrolled"} to ${i?"controlled":"uncontrolled"}.`),n.current=i},[i]);let l=i?e:r,s=o.useCallback((c,...D)=>{let $=(b,...E)=>{a&&(Object.is(l,b)||a(b,...E)),i||(l=b)};typeof c=="function"?(console.warn("We can not support a function callback. See Github Issues for details https://github.com/adobe/react-spectrum/issues/2320"),u((E,...p)=>{let x=c(i?l:E,...p);return $(x,...D),i?E:x})):(i||u(c),$(c,...D))},[i,l,a]);return[l,s]}let Me="default",ga="",Et=new WeakMap;function yr(e){if(ja()){if(Me==="default"){const t=Y(e);ga=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}Me="disabled"}else(e instanceof HTMLElement||e instanceof SVGElement)&&(Et.set(e,e.style.userSelect),e.style.userSelect="none")}function $t(e){if(ja()){if(Me!=="disabled")return;Me="restoring",setTimeout(()=>{Cu(()=>{if(Me==="restoring"){const t=Y(e);t.documentElement.style.webkitUserSelect==="none"&&(t.documentElement.style.webkitUserSelect=ga||""),ga="",Me="default"}})},300)}else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&Et.has(e)){let t=Et.get(e);e.style.userSelect==="none"&&(e.style.userSelect=t),e.getAttribute("style")===""&&e.removeAttribute("style"),Et.delete(e)}}const Au=Mt.createContext({register:()=>{}});Au.displayName="PressResponderContext";function gs(e,t){return t.get?t.get.call(e):t.value}function Ru(e,t,a){if(!t.has(e))throw new TypeError("attempted to "+a+" private field on non-instance");return t.get(e)}function vs(e,t){var a=Ru(e,t,"get");return gs(e,a)}function xs(e,t,a){if(t.set)t.set.call(e,a);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=a}}function gr(e,t,a){var r=Ru(e,t,"set");return xs(e,r,a),a}function Es(e){let t=o.useContext(Au);if(t){let{register:a,...r}=t;e=H(r,e),a()}return Pu(t,e.ref),e}var Dt=new WeakMap;class ht{continuePropagation(){gr(this,Dt,!1)}get shouldStopPropagation(){return vs(this,Dt)}constructor(t,a,r,u){ot(this,Dt,{writable:!0,value:void 0}),gr(this,Dt,!0);var n;let i=(n=u==null?void 0:u.target)!==null&&n!==void 0?n:r.currentTarget;const l=i==null?void 0:i.getBoundingClientRect();let s,c=0,D,$=null;r.clientX!=null&&r.clientY!=null&&(D=r.clientX,$=r.clientY),l&&(D!=null&&$!=null?(s=D-l.left,c=$-l.top):(s=l.width/2,c=l.height/2)),this.type=t,this.pointerType=a,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=s,this.y=c}}const vr=Symbol("linkClicked");function Ka(e){let{onPress:t,onPressChange:a,onPressStart:r,onPressEnd:u,onPressUp:n,isDisabled:i,isPressed:l,preventFocusOnPress:s,shouldCancelOnPointerExit:c,allowTextSelectionOnPress:D,ref:$,...b}=Es(e),[E,p]=o.useState(!1),x=o.useRef({isPressed:!1,ignoreEmulatedMouseEvents:!1,ignoreClickAfterPress:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null}),{addGlobalListener:w,removeAllGlobalListeners:R}=Bu(),k=Z((d,S)=>{let g=x.current;if(i||g.didFirePressStart)return!1;let f=!0;if(g.isTriggeringEvent=!0,r){let F=new ht("pressstart",S,d);r(F),f=F.shouldStopPropagation}return a&&a(!0),g.isTriggeringEvent=!1,g.didFirePressStart=!0,p(!0),f}),C=Z((d,S,g=!0)=>{let f=x.current;if(!f.didFirePressStart)return!1;f.ignoreClickAfterPress=!0,f.didFirePressStart=!1,f.isTriggeringEvent=!0;let F=!0;if(u){let m=new ht("pressend",S,d);u(m),F=m.shouldStopPropagation}if(a&&a(!1),p(!1),t&&g&&!i){let m=new ht("press",S,d);t(m),F&&(F=m.shouldStopPropagation)}return f.isTriggeringEvent=!1,F}),y=Z((d,S)=>{let g=x.current;if(i)return!1;if(n){g.isTriggeringEvent=!0;let f=new ht("pressup",S,d);return n(f),g.isTriggeringEvent=!1,f.shouldStopPropagation}return!0}),A=Z(d=>{let S=x.current;S.isPressed&&S.target&&(S.isOverTarget&&S.pointerType!=null&&C(ie(S.target,d),S.pointerType,!1),S.isPressed=!1,S.isOverTarget=!1,S.activePointerId=null,S.pointerType=null,R(),D||$t(S.target))}),T=Z(d=>{c&&A(d)}),I=o.useMemo(()=>{let d=x.current,S={onKeyDown(f){if(Xt(f.nativeEvent,f.currentTarget)&&f.currentTarget.contains(f.target)){var F;Er(f.target,f.key)&&f.preventDefault();let m=!0;if(!d.isPressed&&!f.repeat){d.target=f.currentTarget,d.isPressed=!0,m=k(f,"keyboard");let h=f.currentTarget,V=z=>{Xt(z,h)&&!z.repeat&&h.contains(z.target)&&d.target&&y(ie(d.target,z),"keyboard")};w(Y(f.currentTarget),"keyup",vu(V,g),!0)}m&&f.stopPropagation(),f.metaKey&&Ue()&&((F=d.metaKeyEvents)===null||F===void 0||F.set(f.key,f.nativeEvent))}else f.key==="Meta"&&(d.metaKeyEvents=new Map)},onClick(f){if(!(f&&!f.currentTarget.contains(f.target))&&f&&f.button===0&&!d.isTriggeringEvent&&!rt.isOpening){let F=!0;if(i&&f.preventDefault(),!d.ignoreClickAfterPress&&!d.ignoreEmulatedMouseEvents&&!d.isPressed&&(d.pointerType==="virtual"||ya(f.nativeEvent))){!i&&!s&&pe(f.currentTarget);let m=k(f,"virtual"),h=y(f,"virtual"),V=C(f,"virtual");F=m&&h&&V}d.ignoreEmulatedMouseEvents=!1,d.ignoreClickAfterPress=!1,F&&f.stopPropagation()}}},g=f=>{var F;if(d.isPressed&&d.target&&Xt(f,d.target)){var m;Er(f.target,f.key)&&f.preventDefault();let V=f.target;C(ie(d.target,f),"keyboard",d.target.contains(V)),R(),f.key!=="Enter"&&_a(d.target)&&d.target.contains(V)&&!f[vr]&&(f[vr]=!0,rt(d.target,f,!1)),d.isPressed=!1,(m=d.metaKeyEvents)===null||m===void 0||m.delete(f.key)}else if(f.key==="Meta"&&(!((F=d.metaKeyEvents)===null||F===void 0)&&F.size)){var h;let V=d.metaKeyEvents;d.metaKeyEvents=void 0;for(let z of V.values())(h=d.target)===null||h===void 0||h.dispatchEvent(new KeyboardEvent("keyup",z))}};if(typeof PointerEvent<"u"){S.onPointerDown=h=>{if(h.button!==0||!h.currentTarget.contains(h.target))return;if(bs(h.nativeEvent)){d.pointerType="virtual";return}ta(h.currentTarget)&&h.preventDefault(),d.pointerType=h.pointerType;let V=!0;if(!d.isPressed){d.isPressed=!0,d.isOverTarget=!0,d.activePointerId=h.pointerId,d.target=h.currentTarget,!i&&!s&&pe(h.currentTarget),D||yr(d.target),V=k(h,d.pointerType);let z=h.target;"releasePointerCapture"in z&&z.releasePointerCapture(h.pointerId),w(Y(h.currentTarget),"pointerup",f,!1),w(Y(h.currentTarget),"pointercancel",m,!1)}V&&h.stopPropagation()},S.onMouseDown=h=>{h.currentTarget.contains(h.target)&&h.button===0&&(ta(h.currentTarget)&&h.preventDefault(),h.stopPropagation())},S.onPointerUp=h=>{!h.currentTarget.contains(h.target)||d.pointerType==="virtual"||h.button===0&&y(h,d.pointerType||h.pointerType)},S.onPointerEnter=h=>{h.pointerId===d.activePointerId&&d.target&&!d.isOverTarget&&d.pointerType!=null&&(d.isOverTarget=!0,k(ie(d.target,h),d.pointerType))},S.onPointerLeave=h=>{h.pointerId===d.activePointerId&&d.target&&d.isOverTarget&&d.pointerType!=null&&(d.isOverTarget=!1,C(ie(d.target,h),d.pointerType,!1),T(h))};let f=h=>{h.pointerId===d.activePointerId&&d.isPressed&&h.button===0&&d.target&&(d.target.contains(h.target)&&d.pointerType!=null?C(ie(d.target,h),d.pointerType):d.isOverTarget&&d.pointerType!=null&&C(ie(d.target,h),d.pointerType,!1),d.isPressed=!1,d.isOverTarget=!1,d.activePointerId=null,d.pointerType=null,R(),D||$t(d.target),"ontouchend"in d.target&&h.pointerType!=="mouse"&&w(d.target,"touchend",F,{once:!0}))},F=h=>{ku(h.currentTarget)&&h.preventDefault()},m=h=>{A(h)};S.onDragStart=h=>{h.currentTarget.contains(h.target)&&A(h)}}else{S.onMouseDown=m=>{if(m.button!==0||!m.currentTarget.contains(m.target))return;if(ta(m.currentTarget)&&m.preventDefault(),d.ignoreEmulatedMouseEvents){m.stopPropagation();return}d.isPressed=!0,d.isOverTarget=!0,d.target=m.currentTarget,d.pointerType=ya(m.nativeEvent)?"virtual":"mouse",!i&&!s&&pe(m.currentTarget),k(m,d.pointerType)&&m.stopPropagation(),w(Y(m.currentTarget),"mouseup",f,!1)},S.onMouseEnter=m=>{if(!m.currentTarget.contains(m.target))return;let h=!0;d.isPressed&&!d.ignoreEmulatedMouseEvents&&d.pointerType!=null&&(d.isOverTarget=!0,h=k(m,d.pointerType)),h&&m.stopPropagation()},S.onMouseLeave=m=>{if(!m.currentTarget.contains(m.target))return;let h=!0;d.isPressed&&!d.ignoreEmulatedMouseEvents&&d.pointerType!=null&&(d.isOverTarget=!1,h=C(m,d.pointerType,!1),T(m)),h&&m.stopPropagation()},S.onMouseUp=m=>{m.currentTarget.contains(m.target)&&!d.ignoreEmulatedMouseEvents&&m.button===0&&y(m,d.pointerType||"mouse")};let f=m=>{if(m.button===0){if(d.isPressed=!1,R(),d.ignoreEmulatedMouseEvents){d.ignoreEmulatedMouseEvents=!1;return}d.target&&ea(m,d.target)&&d.pointerType!=null?C(ie(d.target,m),d.pointerType):d.target&&d.isOverTarget&&d.pointerType!=null&&C(ie(d.target,m),d.pointerType,!1),d.isOverTarget=!1}};S.onTouchStart=m=>{if(!m.currentTarget.contains(m.target))return;let h=Cs(m.nativeEvent);if(!h)return;d.activePointerId=h.identifier,d.ignoreEmulatedMouseEvents=!0,d.isOverTarget=!0,d.isPressed=!0,d.target=m.currentTarget,d.pointerType="touch",!i&&!s&&pe(m.currentTarget),D||yr(d.target),k(me(d.target,m),d.pointerType)&&m.stopPropagation(),w(Pe(m.currentTarget),"scroll",F,!0)},S.onTouchMove=m=>{if(!m.currentTarget.contains(m.target))return;if(!d.isPressed){m.stopPropagation();return}let h=xr(m.nativeEvent,d.activePointerId),V=!0;h&&ea(h,m.currentTarget)?!d.isOverTarget&&d.pointerType!=null&&(d.isOverTarget=!0,V=k(me(d.target,m),d.pointerType)):d.isOverTarget&&d.pointerType!=null&&(d.isOverTarget=!1,V=C(me(d.target,m),d.pointerType,!1),T(me(d.target,m))),V&&m.stopPropagation()},S.onTouchEnd=m=>{if(!m.currentTarget.contains(m.target))return;if(!d.isPressed){m.stopPropagation();return}let h=xr(m.nativeEvent,d.activePointerId),V=!0;h&&ea(h,m.currentTarget)&&d.pointerType!=null?(y(me(d.target,m),d.pointerType),V=C(me(d.target,m),d.pointerType)):d.isOverTarget&&d.pointerType!=null&&(V=C(me(d.target,m),d.pointerType,!1)),V&&m.stopPropagation(),d.isPressed=!1,d.activePointerId=null,d.isOverTarget=!1,d.ignoreEmulatedMouseEvents=!0,d.target&&!D&&$t(d.target),R()},S.onTouchCancel=m=>{m.currentTarget.contains(m.target)&&(m.stopPropagation(),d.isPressed&&A(me(d.target,m)))};let F=m=>{d.isPressed&&m.target.contains(d.target)&&A({currentTarget:d.target,shiftKey:!1,ctrlKey:!1,metaKey:!1,altKey:!1})};S.onDragStart=m=>{m.currentTarget.contains(m.target)&&A(m)}}return S},[w,i,s,R,D,A,T,C,k,y]);return o.useEffect(()=>()=>{var d;D||$t((d=x.current.target)!==null&&d!==void 0?d:void 0)},[D]),{isPressed:l||E,pressProps:H(b,I)}}function _a(e){return e.tagName==="A"&&e.hasAttribute("href")}function Xt(e,t){const{key:a,code:r}=e,u=t,n=u.getAttribute("role");return(a==="Enter"||a===" "||a==="Spacebar"||r==="Space")&&!(u instanceof Pe(u).HTMLInputElement&&!Tu(u,a)||u instanceof Pe(u).HTMLTextAreaElement||u.isContentEditable)&&!((n==="link"||!n&&_a(u))&&a!=="Enter")}function Cs(e){const{targetTouches:t}=e;return t.length>0?t[0]:null}function xr(e,t){const a=e.changedTouches;for(let r=0;r<a.length;r++){const u=a[r];if(u.identifier===t)return u}return null}function me(e,t){let a=0,r=0;return t.targetTouches&&t.targetTouches.length===1&&(a=t.targetTouches[0].clientX,r=t.targetTouches[0].clientY),{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:a,clientY:r}}function ie(e,t){let a=t.clientX,r=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:a,clientY:r}}function Bs(e){let t=0,a=0;return e.width!==void 0?t=e.width/2:e.radiusX!==void 0&&(t=e.radiusX),e.height!==void 0?a=e.height/2:e.radiusY!==void 0&&(a=e.radiusY),{top:e.clientY-a,right:e.clientX+t,bottom:e.clientY+a,left:e.clientX-t}}function Ps(e,t){return!(e.left>t.right||t.left>e.right||e.top>t.bottom||t.top>e.bottom)}function ea(e,t){let a=t.getBoundingClientRect(),r=Bs(e);return Ps(a,r)}function ta(e){return!(e instanceof HTMLElement)||!e.hasAttribute("draggable")}function ku(e){return e instanceof HTMLInputElement?!1:e instanceof HTMLButtonElement?e.type!=="submit"&&e.type!=="reset":!_a(e)}function Er(e,t){return e instanceof HTMLInputElement?!Tu(e,t):ku(e)}const ws=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Tu(e,t){return e.type==="checkbox"||e.type==="radio"?t===" ":ws.has(e.type)}class Fs{isDefaultPrevented(){return this.nativeEvent.defaultPrevented}preventDefault(){this.defaultPrevented=!0,this.nativeEvent.preventDefault()}stopPropagation(){this.nativeEvent.stopPropagation(),this.isPropagationStopped=()=>!0}isPropagationStopped(){return!1}persist(){}constructor(t,a){this.nativeEvent=a,this.target=a.target,this.currentTarget=a.currentTarget,this.relatedTarget=a.relatedTarget,this.bubbles=a.bubbles,this.cancelable=a.cancelable,this.defaultPrevented=a.defaultPrevented,this.eventPhase=a.eventPhase,this.isTrusted=a.isTrusted,this.timeStamp=a.timeStamp,this.type=t}}function Mu(e){let t=o.useRef({isFocused:!1,observer:null});fe(()=>{const r=t.current;return()=>{r.observer&&(r.observer.disconnect(),r.observer=null)}},[]);let a=Z(r=>{e==null||e(r)});return o.useCallback(r=>{if(r.target instanceof HTMLButtonElement||r.target instanceof HTMLInputElement||r.target instanceof HTMLTextAreaElement||r.target instanceof HTMLSelectElement){t.current.isFocused=!0;let u=r.target,n=i=>{t.current.isFocused=!1,u.disabled&&a(new Fs("blur",i)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};u.addEventListener("focusout",n,{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&u.disabled){var i;(i=t.current.observer)===null||i===void 0||i.disconnect();let l=u===document.activeElement?null:document.activeElement;u.dispatchEvent(new FocusEvent("blur",{relatedTarget:l})),u.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:l}))}}),t.current.observer.observe(u,{attributes:!0,attributeFilter:["disabled"]})}},[a])}function Ss(e){let{isDisabled:t,onFocus:a,onBlur:r,onFocusChange:u}=e;const n=o.useCallback(s=>{if(s.target===s.currentTarget)return r&&r(s),u&&u(!1),!0},[r,u]),i=Mu(n),l=o.useCallback(s=>{const c=Y(s.target);s.target===s.currentTarget&&c.activeElement===s.target&&(a&&a(s),u&&u(!0),i(s))},[u,a,i]);return{focusProps:{onFocus:!t&&(a||u||r)?l:void 0,onBlur:!t&&(r||u)?n:void 0}}}let _e=null,As=new Set,Qe=new Map,we=!1,va=!1;function Ut(e,t){for(let a of As)a(e,t)}function Rs(e){return!(e.metaKey||!Ue()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function Rt(e){we=!0,Rs(e)&&(_e="keyboard",Ut("keyboard",e))}function ee(e){_e="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(we=!0,Ut("pointer",e))}function Vu(e){ya(e)&&(we=!0,_e="virtual")}function Iu(e){e.target===window||e.target===document||(!we&&!va&&(_e="virtual",Ut("virtual",e)),we=!1,va=!1)}function Ou(){we=!1,va=!0}function Cr(e){if(typeof window>"u"||Qe.get(Pe(e)))return;const t=Pe(e),a=Y(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){we=!0,r.apply(this,arguments)},a.addEventListener("keydown",Rt,!0),a.addEventListener("keyup",Rt,!0),a.addEventListener("click",Vu,!0),t.addEventListener("focus",Iu,!0),t.addEventListener("blur",Ou,!1),typeof PointerEvent<"u"?(a.addEventListener("pointerdown",ee,!0),a.addEventListener("pointermove",ee,!0),a.addEventListener("pointerup",ee,!0)):(a.addEventListener("mousedown",ee,!0),a.addEventListener("mousemove",ee,!0),a.addEventListener("mouseup",ee,!0)),t.addEventListener("beforeunload",()=>{Lu(e)},{once:!0}),Qe.set(t,{focus:r})}const Lu=(e,t)=>{const a=Pe(e),r=Y(e);t&&r.removeEventListener("DOMContentLoaded",t),Qe.has(a)&&(a.HTMLElement.prototype.focus=Qe.get(a).focus,r.removeEventListener("keydown",Rt,!0),r.removeEventListener("keyup",Rt,!0),r.removeEventListener("click",Vu,!0),a.removeEventListener("focus",Iu,!0),a.removeEventListener("blur",Ou,!1),typeof PointerEvent<"u"?(r.removeEventListener("pointerdown",ee,!0),r.removeEventListener("pointermove",ee,!0),r.removeEventListener("pointerup",ee,!0)):(r.removeEventListener("mousedown",ee,!0),r.removeEventListener("mousemove",ee,!0),r.removeEventListener("mouseup",ee,!0)),Qe.delete(a))};function ks(e){const t=Y(e);let a;return t.readyState!=="loading"?Cr(e):(a=()=>{Cr(e)},t.addEventListener("DOMContentLoaded",a)),()=>Lu(e,a)}typeof document<"u"&&ks();function Nu(){return _e}function Ts(e){_e=e,Ut(e,null)}function ju(e){let{isDisabled:t,onBlurWithin:a,onFocusWithin:r,onFocusWithinChange:u}=e,n=o.useRef({isFocusWithin:!1}),i=o.useCallback(c=>{n.current.isFocusWithin&&!c.currentTarget.contains(c.relatedTarget)&&(n.current.isFocusWithin=!1,a&&a(c),u&&u(!1))},[a,u,n]),l=Mu(i),s=o.useCallback(c=>{!n.current.isFocusWithin&&document.activeElement===c.target&&(r&&r(c),u&&u(!0),n.current.isFocusWithin=!0,l(c))},[r,u,l]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:s,onBlur:i}}}function Ms(e){let{ref:t,onInteractOutside:a,isDisabled:r,onInteractOutsideStart:u}=e,n=o.useRef({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),i=Z(s=>{a&&pt(s,t)&&(u&&u(s),n.current.isPointerDown=!0)}),l=Z(s=>{a&&a(s)});o.useEffect(()=>{let s=n.current;if(r)return;const c=t.current,D=Y(c);if(typeof PointerEvent<"u"){let $=b=>{s.isPointerDown&&pt(b,t)&&l(b),s.isPointerDown=!1};return D.addEventListener("pointerdown",i,!0),D.addEventListener("pointerup",$,!0),()=>{D.removeEventListener("pointerdown",i,!0),D.removeEventListener("pointerup",$,!0)}}else{let $=E=>{s.ignoreEmulatedMouseEvents?s.ignoreEmulatedMouseEvents=!1:s.isPointerDown&&pt(E,t)&&l(E),s.isPointerDown=!1},b=E=>{s.ignoreEmulatedMouseEvents=!0,s.isPointerDown&&pt(E,t)&&l(E),s.isPointerDown=!1};return D.addEventListener("mousedown",i,!0),D.addEventListener("mouseup",$,!0),D.addEventListener("touchstart",i,!0),D.addEventListener("touchend",b,!0),()=>{D.removeEventListener("mousedown",i,!0),D.removeEventListener("mouseup",$,!0),D.removeEventListener("touchstart",i,!0),D.removeEventListener("touchend",b,!0)}}},[t,r,i,l])}function pt(e,t){if(e.button>0)return!1;if(e.target){const a=e.target.ownerDocument;if(!a||!a.documentElement.contains(e.target)||e.target.closest("[data-react-aria-top-layer]"))return!1}return t.current&&!t.current.contains(e.target)}function Br(e){if(!e)return;let t=!0;return a=>{let r={...a,preventDefault(){a.preventDefault()},isDefaultPrevented(){return a.isDefaultPrevented()},stopPropagation(){t?console.error("stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior."):t=!0},continuePropagation(){t=!1},isPropagationStopped(){return t}};e(r),t&&a.stopPropagation()}}function Vs(e){return{keyboardProps:e.isDisabled?{}:{onKeyDown:Br(e.onKeyDown),onKeyUp:Br(e.onKeyUp)}}}function Uu(e){const t=Y(e);if(Nu()==="virtual"){let a=t.activeElement;Cu(()=>{t.activeElement===a&&e.isConnected&&pe(e)})}else pe(e)}function Is(e){const t=Pe(e);if(!(e instanceof t.HTMLElement)&&!(e instanceof t.SVGElement))return!1;let{display:a,visibility:r}=e.style,u=a!=="none"&&r!=="hidden"&&r!=="collapse";if(u){const{getComputedStyle:n}=e.ownerDocument.defaultView;let{display:i,visibility:l}=n(e);u=i!=="none"&&l!=="hidden"&&l!=="collapse"}return u}function Os(e,t){return!e.hasAttribute("hidden")&&!e.hasAttribute("data-react-aria-prevent-focus")&&(e.nodeName==="DETAILS"&&t&&t.nodeName!=="SUMMARY"?e.hasAttribute("open"):!0)}function Ku(e,t){return e.nodeName!=="#comment"&&Is(e)&&Os(e,t)&&(!e.parentElement||Ku(e.parentElement,e))}const za=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])'],Ls=za.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";za.push('[tabindex]:not([tabindex="-1"]):not([disabled])');const Ns=za.join(':not([hidden]):not([tabindex="-1"]),');function _u(e,t){return!e||!t?!1:t.some(a=>a.contains(e))}function Ze(e,t=!1){if(e!=null&&!t)try{Uu(e)}catch{}else if(e!=null)try{e.focus()}catch{}}function Je(e,t,a){let r=t!=null&&t.tabbable?Ns:Ls,u=Y(e).createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode(n){var i;return!(t==null||(i=t.from)===null||i===void 0)&&i.contains(n)?NodeFilter.FILTER_REJECT:n.matches(r)&&Ku(n)&&(!a||_u(n,a))&&(!(t!=null&&t.accept)||t.accept(n))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return t!=null&&t.from&&(u.currentNode=t.from),u}function Za(e,t={}){return{focusNext(a={}){let r=e.current;if(!r)return null;let{from:u,tabbable:n=t.tabbable,wrap:i=t.wrap,accept:l=t.accept}=a,s=u||Y(r).activeElement,c=Je(r,{tabbable:n,accept:l});r.contains(s)&&(c.currentNode=s);let D=c.nextNode();return!D&&i&&(c.currentNode=r,D=c.nextNode()),D&&Ze(D,!0),D},focusPrevious(a=t){let r=e.current;if(!r)return null;let{from:u,tabbable:n=t.tabbable,wrap:i=t.wrap,accept:l=t.accept}=a,s=u||Y(r).activeElement,c=Je(r,{tabbable:n,accept:l});if(r.contains(s))c.currentNode=s;else{let $=aa(c);return $&&Ze($,!0),$??null}let D=c.previousNode();if(!D&&i){c.currentNode=r;let $=aa(c);if(!$)return null;D=$}return D&&Ze(D,!0),D??null},focusFirst(a=t){let r=e.current;if(!r)return null;let{tabbable:u=t.tabbable,accept:n=t.accept}=a,l=Je(r,{tabbable:u,accept:n}).nextNode();return l&&Ze(l,!0),l},focusLast(a=t){let r=e.current;if(!r)return null;let{tabbable:u=t.tabbable,accept:n=t.accept}=a,i=Je(r,{tabbable:u,accept:n}),l=aa(i);return l&&Ze(l,!0),l??null}}}function aa(e){let t,a;do a=e.lastChild(),a&&(t=a);while(a);return t}class Wa{get size(){return this.fastMap.size}getTreeNode(t){return this.fastMap.get(t)}addTreeNode(t,a,r){let u=this.fastMap.get(a??null);if(!u)return;let n=new Pr({scopeRef:t});u.addChild(n),n.parent=u,this.fastMap.set(t,n),r&&(n.nodeToRestore=r)}addNode(t){this.fastMap.set(t.scopeRef,t)}removeTreeNode(t){if(t===null)return;let a=this.fastMap.get(t);if(!a)return;let r=a.parent;for(let n of this.traverse())n!==a&&a.nodeToRestore&&n.nodeToRestore&&a.scopeRef&&a.scopeRef.current&&_u(n.nodeToRestore,a.scopeRef.current)&&(n.nodeToRestore=a.nodeToRestore);let u=a.children;r&&(r.removeChild(a),u.size>0&&u.forEach(n=>r&&r.addChild(n))),this.fastMap.delete(a.scopeRef)}*traverse(t=this.root){if(t.scopeRef!=null&&(yield t),t.children.size>0)for(let a of t.children)yield*this.traverse(a)}clone(){var t;let a=new Wa;var r;for(let u of this.traverse())a.addTreeNode(u.scopeRef,(r=(t=u.parent)===null||t===void 0?void 0:t.scopeRef)!==null&&r!==void 0?r:null,u.nodeToRestore);return a}constructor(){this.fastMap=new Map,this.root=new Pr({scopeRef:null}),this.fastMap.set(null,this.root)}}class Pr{addChild(t){this.children.add(t),t.parent=this}removeChild(t){this.children.delete(t),t.parent=void 0}constructor(t){this.children=new Set,this.contain=!1,this.scopeRef=t.scopeRef}}new Wa;let js=Mt.createContext(null);function Us(e){let t=o.useContext(js)||{};Pu(t,e);let{ref:a,...r}=t;return r}function Ks(e,t){let{focusProps:a}=Ss(e),{keyboardProps:r}=Vs(e),u=H(a,r),n=Us(t),i=e.isDisabled?{}:n,l=o.useRef(e.autoFocus);return o.useEffect(()=>{l.current&&t.current&&Uu(t.current),l.current=!1},[t]),{focusableProps:H({...u,tabIndex:e.excludeFromTabOrder&&!e.isDisabled?-1:void 0},i)}}const _s=Symbol.for("react-aria.i18n.locale"),zs=Symbol.for("react-aria.i18n.strings");let ke;class Fe{getStringForLocale(t,a){let u=this.getStringsForLocale(a)[t];if(!u)throw new Error(`Could not find intl message ${t} in ${a} locale`);return u}getStringsForLocale(t){let a=this.strings[t];return a||(a=Zs(t,this.strings,this.defaultLocale),this.strings[t]=a),a}static getGlobalDictionaryForPackage(t){if(typeof window>"u")return null;let a=window[_s];if(ke===void 0){let u=window[zs];if(!u)return null;ke={};for(let n in u)ke[n]=new Fe({[a]:u[n]},a)}let r=ke==null?void 0:ke[t];if(!r)throw new Error(`Strings for package "${t}" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);return r}constructor(t,a="en-US"){this.strings=Object.fromEntries(Object.entries(t).filter(([,r])=>r)),this.defaultLocale=a}}function Zs(e,t,a="en-US"){if(t[e])return t[e];let r=Ws(e);if(t[r])return t[r];for(let u in t)if(u.startsWith(r+"-"))return t[u];return t[a]}function Ws(e){return Intl.Locale?new Intl.Locale(e).language:e.split("-")[0]}const wr=new Map,Fr=new Map;class zu{format(t,a){let r=this.strings.getStringForLocale(t,this.locale);return typeof r=="function"?r(a,this):r}plural(t,a,r="cardinal"){let u=a["="+t];if(u)return typeof u=="function"?u():u;let n=this.locale+":"+r,i=wr.get(n);i||(i=new Intl.PluralRules(this.locale,{type:r}),wr.set(n,i));let l=i.select(t);return u=a[l]||a.other,typeof u=="function"?u():u}number(t){let a=Fr.get(this.locale);return a||(a=new Intl.NumberFormat(this.locale),Fr.set(this.locale,a)),a.format(t)}select(t,a){let r=t[a]||t.other;return typeof r=="function"?r():r}constructor(t,a){this.locale=t,this.strings=a}}const Sr=new WeakMap;function Ys(e){let t=Sr.get(e);return t||(t=new Fe(e),Sr.set(e,t)),t}function Zu(e,t){return t&&Fe.getGlobalDictionaryForPackage(t)||Ys(e)}function Se(e,t){let{locale:a}=q(),r=Zu(e,t);return o.useMemo(()=>new zu(a,r),[a,r])}function re(e){e=Su(e??{},Hs);let{locale:t}=q();return o.useMemo(()=>new de(t,e),[t,e])}function Hs(e,t){if(e===t)return!0;let a=Object.keys(e),r=Object.keys(t);if(a.length!==r.length)return!1;for(let u of a)if(t[u]!==e[u])return!1;return!0}let ra=new Map,xa=!1;try{xa=new Intl.NumberFormat("de-DE",{signDisplay:"exceptZero"}).resolvedOptions().signDisplay==="exceptZero"}catch{}let kt=!1;try{kt=new Intl.NumberFormat("de-DE",{style:"unit",unit:"degree"}).resolvedOptions().style==="unit"}catch{}const Wu={degree:{narrow:{default:"°","ja-JP":" 度","zh-TW":"度","sl-SI":" °"}}};class Gs{format(t){let a="";if(!xa&&this.options.signDisplay!=null?a=qs(this.numberFormatter,this.options.signDisplay,t):a=this.numberFormatter.format(t),this.options.style==="unit"&&!kt){var r;let{unit:u,unitDisplay:n="short",locale:i}=this.resolvedOptions();if(!u)return a;let l=(r=Wu[u])===null||r===void 0?void 0:r[n];a+=l[i]||l.default}return a}formatToParts(t){return this.numberFormatter.formatToParts(t)}formatRange(t,a){if(typeof this.numberFormatter.formatRange=="function")return this.numberFormatter.formatRange(t,a);if(a<t)throw new RangeError("End date must be >= start date");return`${this.format(t)} – ${this.format(a)}`}formatRangeToParts(t,a){if(typeof this.numberFormatter.formatRangeToParts=="function")return this.numberFormatter.formatRangeToParts(t,a);if(a<t)throw new RangeError("End date must be >= start date");let r=this.numberFormatter.formatToParts(t),u=this.numberFormatter.formatToParts(a);return[...r.map(n=>({...n,source:"startRange"})),{type:"literal",value:" – ",source:"shared"},...u.map(n=>({...n,source:"endRange"}))]}resolvedOptions(){let t=this.numberFormatter.resolvedOptions();return!xa&&this.options.signDisplay!=null&&(t={...t,signDisplay:this.options.signDisplay}),!kt&&this.options.style==="unit"&&(t={...t,style:"unit",unit:this.options.unit,unitDisplay:this.options.unitDisplay}),t}constructor(t,a={}){this.numberFormatter=Js(t,a),this.options=a}}function Js(e,t={}){let{numberingSystem:a}=t;if(a&&e.includes("-nu-")&&(e.includes("-u-")||(e+="-u-"),e+=`-nu-${a}`),t.style==="unit"&&!kt){var r;let{unit:i,unitDisplay:l="short"}=t;if(!i)throw new Error('unit option must be provided with style: "unit"');if(!(!((r=Wu[i])===null||r===void 0)&&r[l]))throw new Error(`Unsupported unit ${i} with unitDisplay = ${l}`);t={...t,style:"decimal"}}let u=e+(t?Object.entries(t).sort((i,l)=>i[0]<l[0]?-1:1).join():"");if(ra.has(u))return ra.get(u);let n=new Intl.NumberFormat(e,t);return ra.set(u,n),n}function qs(e,t,a){if(t==="auto")return e.format(a);if(t==="never")return e.format(Math.abs(a));{let r=!1;if(t==="always"?r=a>0||Object.is(a,0):t==="exceptZero"&&(Object.is(a,-0)||Object.is(a,0)?a=Math.abs(a):r=a>0),r){let u=e.format(-a),n=e.format(a),i=u.replace(n,"").replace(/\u200e|\u061C/,"");return[...i].length!==1&&console.warn("@react-aria/i18n polyfill for NumberFormat signDisplay: Unsupported case"),u.replace(n,"!!!").replace(i,"+").replace("!!!",n)}else return e.format(a)}}const Qs=new RegExp("^.*\\(.*\\).*$"),Xs=["latn","arab","hanidec","deva","beng"];class Yu{parse(t){return ua(this.locale,this.options,t).parse(t)}isValidPartialNumber(t,a,r){return ua(this.locale,this.options,t).isValidPartialNumber(t,a,r)}getNumberingSystem(t){return ua(this.locale,this.options,t).options.numberingSystem}constructor(t,a={}){this.locale=t,this.options=a}}const Ar=new Map;function ua(e,t,a){let r=Rr(e,t);if(!e.includes("-nu-")&&!r.isValidPartialNumber(a)){for(let u of Xs)if(u!==r.options.numberingSystem){let n=Rr(e+(e.includes("-u-")?"-nu-":"-u-nu-")+u,t);if(n.isValidPartialNumber(a))return n}}return r}function Rr(e,t){let a=e+(t?Object.entries(t).sort((u,n)=>u[0]<n[0]?-1:1).join():""),r=Ar.get(a);return r||(r=new ed(e,t),Ar.set(a,r)),r}class ed{parse(t){let a=this.sanitize(t);if(this.symbols.group&&(a=bt(a,this.symbols.group,"")),this.symbols.decimal&&(a=a.replace(this.symbols.decimal,".")),this.symbols.minusSign&&(a=a.replace(this.symbols.minusSign,"-")),a=a.replace(this.symbols.numeral,this.symbols.index),this.options.style==="percent"){let i=a.indexOf("-");a=a.replace("-","");let l=a.indexOf(".");l===-1&&(l=a.length),a=a.replace(".",""),l-2===0?a=`0.${a}`:l-2===-1?a=`0.0${a}`:l-2===-2?a="0.00":a=`${a.slice(0,l-2)}.${a.slice(l-2)}`,i>-1&&(a=`-${a}`)}let r=a?+a:NaN;if(isNaN(r))return NaN;if(this.options.style==="percent"){var u,n;let i={...this.options,style:"decimal",minimumFractionDigits:Math.min(((u=this.options.minimumFractionDigits)!==null&&u!==void 0?u:0)+2,20),maximumFractionDigits:Math.min(((n=this.options.maximumFractionDigits)!==null&&n!==void 0?n:0)+2,20)};return new Yu(this.locale,i).parse(new Gs(this.locale,i).format(r))}return this.options.currencySign==="accounting"&&Qs.test(t)&&(r=-1*r),r}sanitize(t){return t=t.replace(this.symbols.literals,""),this.symbols.minusSign&&(t=t.replace("-",this.symbols.minusSign)),this.options.numberingSystem==="arab"&&(this.symbols.decimal&&(t=t.replace(",",this.symbols.decimal),t=t.replace("،",this.symbols.decimal)),this.symbols.group&&(t=bt(t,".",this.symbols.group))),this.options.locale==="fr-FR"&&(t=bt(t,"."," ")),t}isValidPartialNumber(t,a=-1/0,r=1/0){return t=this.sanitize(t),this.symbols.minusSign&&t.startsWith(this.symbols.minusSign)&&a<0?t=t.slice(this.symbols.minusSign.length):this.symbols.plusSign&&t.startsWith(this.symbols.plusSign)&&r>0&&(t=t.slice(this.symbols.plusSign.length)),this.symbols.group&&t.startsWith(this.symbols.group)||this.symbols.decimal&&t.indexOf(this.symbols.decimal)>-1&&this.options.maximumFractionDigits===0?!1:(this.symbols.group&&(t=bt(t,this.symbols.group,"")),t=t.replace(this.symbols.numeral,""),this.symbols.decimal&&(t=t.replace(this.symbols.decimal,"")),t.length===0)}constructor(t,a={}){this.locale=t,this.formatter=new Intl.NumberFormat(t,a),this.options=this.formatter.resolvedOptions(),this.symbols=ad(t,this.formatter,this.options,a);var r,u;this.options.style==="percent"&&(((r=this.options.minimumFractionDigits)!==null&&r!==void 0?r:0)>18||((u=this.options.maximumFractionDigits)!==null&&u!==void 0?u:0)>18)&&console.warn("NumberParser cannot handle percentages with greater than 18 decimal places, please reduce the number in your options.")}}const kr=new Set(["decimal","fraction","integer","minusSign","plusSign","group"]),td=[0,4,2,1,11,20,3,7,100,21,.1,1.1];function ad(e,t,a,r){var u,n,i,l;let s=new Intl.NumberFormat(e,{...a,minimumSignificantDigits:1,maximumSignificantDigits:21,roundingIncrement:1,roundingPriority:"auto",roundingMode:"halfExpand"}),c=s.formatToParts(-10000.111),D=s.formatToParts(10000.111),$=td.map(g=>s.formatToParts(g));var b;let E=(b=(u=c.find(g=>g.type==="minusSign"))===null||u===void 0?void 0:u.value)!==null&&b!==void 0?b:"-",p=(n=D.find(g=>g.type==="plusSign"))===null||n===void 0?void 0:n.value;!p&&((r==null?void 0:r.signDisplay)==="exceptZero"||(r==null?void 0:r.signDisplay)==="always")&&(p="+");let w=(i=new Intl.NumberFormat(e,{...a,minimumFractionDigits:2,maximumFractionDigits:2}).formatToParts(.001).find(g=>g.type==="decimal"))===null||i===void 0?void 0:i.value,R=(l=c.find(g=>g.type==="group"))===null||l===void 0?void 0:l.value,k=c.filter(g=>!kr.has(g.type)).map(g=>Tr(g.value)),C=$.flatMap(g=>g.filter(f=>!kr.has(f.type)).map(f=>Tr(f.value))),y=[...new Set([...k,...C])].sort((g,f)=>f.length-g.length),A=y.length===0?new RegExp("[\\p{White_Space}]","gu"):new RegExp(`${y.join("|")}|[\\p{White_Space}]`,"gu"),T=[...new Intl.NumberFormat(a.locale,{useGrouping:!1}).format(9876543210)].reverse(),I=new Map(T.map((g,f)=>[g,f])),d=new RegExp(`[${T.join("")}]`,"g");return{minusSign:E,plusSign:p,decimal:w,group:R,literals:A,numeral:d,index:g=>String(I.get(g))}}function bt(e,t,a){return e.replaceAll?e.replaceAll(t,a):e.split(t).join(a)}function Tr(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}let na=new Map;function rd(e){let{locale:t}=q(),a=t+(e?Object.entries(e).sort((u,n)=>u[0]<n[0]?-1:1).join():"");if(na.has(a))return na.get(a);let r=new Intl.Collator(t,e);return na.set(a,r),r}function ud(e){let t=rd({usage:"search",...e}),a=o.useCallback((n,i)=>i.length===0?!0:(n=n.normalize("NFC"),i=i.normalize("NFC"),t.compare(n.slice(0,i.length),i)===0),[t]),r=o.useCallback((n,i)=>i.length===0?!0:(n=n.normalize("NFC"),i=i.normalize("NFC"),t.compare(n.slice(-i.length),i)===0),[t]),u=o.useCallback((n,i)=>{if(i.length===0)return!0;n=n.normalize("NFC"),i=i.normalize("NFC");let l=0,s=i.length;for(;l+s<=n.length;l++){let c=n.slice(l,l+s);if(t.compare(i,c)===0)return!0}return!1},[t]);return o.useMemo(()=>({startsWith:a,endsWith:r,contains:u}),[a,r,u])}function Hu(e,t){let{elementType:a="button",isDisabled:r,onPress:u,onPressStart:n,onPressEnd:i,onPressUp:l,onPressChange:s,preventFocusOnPress:c,allowFocusWhenDisabled:D,onClick:$,href:b,target:E,rel:p,type:x="button"}=e,w;a==="button"?w={type:x,disabled:r}:w={role:"button",tabIndex:r?void 0:0,href:a==="a"&&!r?b:void 0,target:a==="a"?E:void 0,type:a==="input"?x:void 0,disabled:a==="input"?r:void 0,"aria-disabled":!r||a==="input"?void 0:r,rel:a==="a"?p:void 0};let{pressProps:R,isPressed:k}=Ka({onPressStart:n,onPressEnd:i,onPressChange:s,onPress:u,onPressUp:l,isDisabled:r,preventFocusOnPress:c,ref:t}),{focusableProps:C}=Ks(e,t);D&&(C.tabIndex=r?-1:C.tabIndex);let y=H(C,R,Lt(e,{labelable:!0}));return{isPressed:k,buttonProps:H(w,y,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],onClick:A=>{$&&($(A),console.warn("onClick is deprecated, please use onPress"))}})}}var Gu={};Gu={dateRange:e=>`${e.startDate} إلى ${e.endDate}`,dateSelected:e=>`${e.date} المحدد`,finishRangeSelectionPrompt:"انقر لإنهاء عملية تحديد نطاق التاريخ",maximumDate:"آخر تاريخ متاح",minimumDate:"أول تاريخ متاح",next:"التالي",previous:"السابق",selectedDateDescription:e=>`تاريخ محدد: ${e.date}`,selectedRangeDescription:e=>`المدى الزمني المحدد: ${e.dateRange}`,startRangeSelectionPrompt:"انقر لبدء عملية تحديد نطاق التاريخ",todayDate:e=>`اليوم، ${e.date}`,todayDateSelected:e=>`اليوم، ${e.date} محدد`};var Ju={};Ju={dateRange:e=>`${e.startDate} до ${e.endDate}`,dateSelected:e=>`Избрано е ${e.date}`,finishRangeSelectionPrompt:"Натиснете, за да довършите избора на времеви интервал",maximumDate:"Последна налична дата",minimumDate:"Първа налична дата",next:"Напред",previous:"Назад",selectedDateDescription:e=>`Избрана дата: ${e.date}`,selectedRangeDescription:e=>`Избран диапазон: ${e.dateRange}`,startRangeSelectionPrompt:"Натиснете, за да пристъпите към избора на времеви интервал",todayDate:e=>`Днес, ${e.date}`,todayDateSelected:e=>`Днес, ${e.date} са избрани`};var qu={};qu={dateRange:e=>`${e.startDate} až ${e.endDate}`,dateSelected:e=>`Vybráno ${e.date}`,finishRangeSelectionPrompt:"Kliknutím dokončíte výběr rozsahu dat",maximumDate:"Poslední dostupné datum",minimumDate:"První dostupné datum",next:"Další",previous:"Předchozí",selectedDateDescription:e=>`Vybrané datum: ${e.date}`,selectedRangeDescription:e=>`Vybrané období: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknutím zahájíte výběr rozsahu dat",todayDate:e=>`Dnes, ${e.date}`,todayDateSelected:e=>`Dnes, vybráno ${e.date}`};var Qu={};Qu={dateRange:e=>`${e.startDate} til ${e.endDate}`,dateSelected:e=>`${e.date} valgt`,finishRangeSelectionPrompt:"Klik for at fuldføre valg af datoområde",maximumDate:"Sidste ledige dato",minimumDate:"Første ledige dato",next:"Næste",previous:"Forrige",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt interval: ${e.dateRange}`,startRangeSelectionPrompt:"Klik for at starte valg af datoområde",todayDate:e=>`I dag, ${e.date}`,todayDateSelected:e=>`I dag, ${e.date} valgt`};var Xu={};Xu={dateRange:e=>`${e.startDate} bis ${e.endDate}`,dateSelected:e=>`${e.date} ausgewählt`,finishRangeSelectionPrompt:"Klicken, um die Auswahl des Datumsbereichs zu beenden",maximumDate:"Letztes verfügbares Datum",minimumDate:"Erstes verfügbares Datum",next:"Weiter",previous:"Zurück",selectedDateDescription:e=>`Ausgewähltes Datum: ${e.date}`,selectedRangeDescription:e=>`Ausgewählter Bereich: ${e.dateRange}`,startRangeSelectionPrompt:"Klicken, um die Auswahl des Datumsbereichs zu beginnen",todayDate:e=>`Heute, ${e.date}`,todayDateSelected:e=>`Heute, ${e.date} ausgewählt`};var en={};en={dateRange:e=>`${e.startDate} έως ${e.endDate}`,dateSelected:e=>`Επιλέχθηκε ${e.date}`,finishRangeSelectionPrompt:"Κάντε κλικ για να ολοκληρώσετε την επιλογή εύρους ημερομηνιών",maximumDate:"Τελευταία διαθέσιμη ημερομηνία",minimumDate:"Πρώτη διαθέσιμη ημερομηνία",next:"Επόμενο",previous:"Προηγούμενο",selectedDateDescription:e=>`Επιλεγμένη ημερομηνία: ${e.date}`,selectedRangeDescription:e=>`Επιλεγμένο εύρος: ${e.dateRange}`,startRangeSelectionPrompt:"Κάντε κλικ για να ξεκινήσετε την επιλογή εύρους ημερομηνιών",todayDate:e=>`Σήμερα, ${e.date}`,todayDateSelected:e=>`Σήμερα, επιλέχτηκε ${e.date}`};var tn={};tn={previous:"Previous",next:"Next",selectedDateDescription:e=>`Selected Date: ${e.date}`,selectedRangeDescription:e=>`Selected Range: ${e.dateRange}`,todayDate:e=>`Today, ${e.date}`,todayDateSelected:e=>`Today, ${e.date} selected`,dateSelected:e=>`${e.date} selected`,startRangeSelectionPrompt:"Click to start selecting date range",finishRangeSelectionPrompt:"Click to finish selecting date range",minimumDate:"First available date",maximumDate:"Last available date",dateRange:e=>`${e.startDate} to ${e.endDate}`};var an={};an={dateRange:e=>`${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} seleccionado`,finishRangeSelectionPrompt:"Haga clic para terminar de seleccionar rango de fechas",maximumDate:"Última fecha disponible",minimumDate:"Primera fecha disponible",next:"Siguiente",previous:"Anterior",selectedDateDescription:e=>`Fecha seleccionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo seleccionado: ${e.dateRange}`,startRangeSelectionPrompt:"Haga clic para comenzar a seleccionar un rango de fechas",todayDate:e=>`Hoy, ${e.date}`,todayDateSelected:e=>`Hoy, ${e.date} seleccionado`};var rn={};rn={dateRange:e=>`${e.startDate} kuni ${e.endDate}`,dateSelected:e=>`${e.date} valitud`,finishRangeSelectionPrompt:"Klõpsake kuupäevavahemiku valimise lõpetamiseks",maximumDate:"Viimane saadaolev kuupäev",minimumDate:"Esimene saadaolev kuupäev",next:"Järgmine",previous:"Eelmine",selectedDateDescription:e=>`Valitud kuupäev: ${e.date}`,selectedRangeDescription:e=>`Valitud vahemik: ${e.dateRange}`,startRangeSelectionPrompt:"Klõpsake kuupäevavahemiku valimiseks",todayDate:e=>`Täna, ${e.date}`,todayDateSelected:e=>`Täna, ${e.date} valitud`};var un={};un={dateRange:e=>`${e.startDate} – ${e.endDate}`,dateSelected:e=>`${e.date} valittu`,finishRangeSelectionPrompt:"Lopeta päivämääräalueen valinta napsauttamalla tätä.",maximumDate:"Viimeinen varattavissa oleva päivämäärä",minimumDate:"Ensimmäinen varattavissa oleva päivämäärä",next:"Seuraava",previous:"Edellinen",selectedDateDescription:e=>`Valittu päivämäärä: ${e.date}`,selectedRangeDescription:e=>`Valittu aikaväli: ${e.dateRange}`,startRangeSelectionPrompt:"Aloita päivämääräalueen valinta napsauttamalla tätä.",todayDate:e=>`Tänään, ${e.date}`,todayDateSelected:e=>`Tänään, ${e.date} valittu`};var nn={};nn={dateRange:e=>`${e.startDate} à ${e.endDate}`,dateSelected:e=>`${e.date} sélectionné`,finishRangeSelectionPrompt:"Cliquer pour finir de sélectionner la plage de dates",maximumDate:"Dernière date disponible",minimumDate:"Première date disponible",next:"Suivant",previous:"Précédent",selectedDateDescription:e=>`Date sélectionnée : ${e.date}`,selectedRangeDescription:e=>`Plage sélectionnée : ${e.dateRange}`,startRangeSelectionPrompt:"Cliquer pour commencer à sélectionner la plage de dates",todayDate:e=>`Aujourd'hui, ${e.date}`,todayDateSelected:e=>`Aujourd’hui, ${e.date} sélectionné`};var ln={};ln={dateRange:e=>`${e.startDate} עד ${e.endDate}`,dateSelected:e=>`${e.date} נבחר`,finishRangeSelectionPrompt:"חץ כדי לסיים את בחירת טווח התאריכים",maximumDate:"תאריך פנוי אחרון",minimumDate:"תאריך פנוי ראשון",next:"הבא",previous:"הקודם",selectedDateDescription:e=>`תאריך נבחר: ${e.date}`,selectedRangeDescription:e=>`טווח נבחר: ${e.dateRange}`,startRangeSelectionPrompt:"לחץ כדי להתחיל בבחירת טווח התאריכים",todayDate:e=>`היום, ${e.date}`,todayDateSelected:e=>`היום, ${e.date} נבחר`};var on={};on={dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`${e.date} odabran`,finishRangeSelectionPrompt:"Kliknite da dovršite raspon odabranih datuma",maximumDate:"Posljednji raspoloživi datum",minimumDate:"Prvi raspoloživi datum",next:"Sljedeći",previous:"Prethodni",selectedDateDescription:e=>`Odabrani datum: ${e.date}`,selectedRangeDescription:e=>`Odabrani raspon: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknite da započnete raspon odabranih datuma",todayDate:e=>`Danas, ${e.date}`,todayDateSelected:e=>`Danas, odabran ${e.date}`};var sn={};sn={dateRange:e=>`${e.startDate}–${e.endDate}`,dateSelected:e=>`${e.date} kiválasztva`,finishRangeSelectionPrompt:"Kattintson a dátumtartomány kijelölésének befejezéséhez",maximumDate:"Utolsó elérhető dátum",minimumDate:"Az első elérhető dátum",next:"Következő",previous:"Előző",selectedDateDescription:e=>`Kijelölt dátum: ${e.date}`,selectedRangeDescription:e=>`Kijelölt tartomány: ${e.dateRange}`,startRangeSelectionPrompt:"Kattintson a dátumtartomány kijelölésének indításához",todayDate:e=>`Ma, ${e.date}`,todayDateSelected:e=>`Ma, ${e.date} kijelölve`};var dn={};dn={dateRange:e=>`Da ${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} selezionata`,finishRangeSelectionPrompt:"Fai clic per completare la selezione dell’intervallo di date",maximumDate:"Ultima data disponibile",minimumDate:"Prima data disponibile",next:"Successivo",previous:"Precedente",selectedDateDescription:e=>`Data selezionata: ${e.date}`,selectedRangeDescription:e=>`Intervallo selezionato: ${e.dateRange}`,startRangeSelectionPrompt:"Fai clic per selezionare l’intervallo di date",todayDate:e=>`Oggi, ${e.date}`,todayDateSelected:e=>`Oggi, ${e.date} selezionata`};var cn={};cn={dateRange:e=>`${e.startDate} から ${e.endDate}`,dateSelected:e=>`${e.date} を選択`,finishRangeSelectionPrompt:"クリックして日付範囲の選択を終了",maximumDate:"最終利用可能日",minimumDate:"最初の利用可能日",next:"次へ",previous:"前へ",selectedDateDescription:e=>`選択した日付 : ${e.date}`,selectedRangeDescription:e=>`選択範囲 : ${e.dateRange}`,startRangeSelectionPrompt:"クリックして日付範囲の選択を開始",todayDate:e=>`本日、${e.date}`,todayDateSelected:e=>`本日、${e.date} を選択`};var fn={};fn={dateRange:e=>`${e.startDate} ~ ${e.endDate}`,dateSelected:e=>`${e.date} 선택됨`,finishRangeSelectionPrompt:"날짜 범위 선택을 완료하려면 클릭하십시오.",maximumDate:"마지막으로 사용 가능한 일자",minimumDate:"처음으로 사용 가능한 일자",next:"다음",previous:"이전",selectedDateDescription:e=>`선택 일자: ${e.date}`,selectedRangeDescription:e=>`선택 범위: ${e.dateRange}`,startRangeSelectionPrompt:"날짜 범위 선택을 시작하려면 클릭하십시오.",todayDate:e=>`오늘, ${e.date}`,todayDateSelected:e=>`오늘, ${e.date} 선택됨`};var mn={};mn={dateRange:e=>`Nuo ${e.startDate} iki ${e.endDate}`,dateSelected:e=>`Pasirinkta ${e.date}`,finishRangeSelectionPrompt:"Spustelėkite, kad baigtumėte pasirinkti datų intervalą",maximumDate:"Paskutinė galima data",minimumDate:"Pirmoji galima data",next:"Paskesnis",previous:"Ankstesnis",selectedDateDescription:e=>`Pasirinkta data: ${e.date}`,selectedRangeDescription:e=>`Pasirinktas intervalas: ${e.dateRange}`,startRangeSelectionPrompt:"Spustelėkite, kad pradėtumėte pasirinkti datų intervalą",todayDate:e=>`Šiandien, ${e.date}`,todayDateSelected:e=>`Šiandien, pasirinkta ${e.date}`};var $n={};$n={dateRange:e=>`No ${e.startDate} līdz ${e.endDate}`,dateSelected:e=>`Atlasīts: ${e.date}`,finishRangeSelectionPrompt:"Noklikšķiniet, lai pabeigtu datumu diapazona atlasi",maximumDate:"Pēdējais pieejamais datums",minimumDate:"Pirmais pieejamais datums",next:"Tālāk",previous:"Atpakaļ",selectedDateDescription:e=>`Atlasītais datums: ${e.date}`,selectedRangeDescription:e=>`Atlasītais diapazons: ${e.dateRange}`,startRangeSelectionPrompt:"Noklikšķiniet, lai sāktu datumu diapazona atlasi",todayDate:e=>`Šodien, ${e.date}`,todayDateSelected:e=>`Atlasīta šodiena, ${e.date}`};var Dn={};Dn={dateRange:e=>`${e.startDate} til ${e.endDate}`,dateSelected:e=>`${e.date} valgt`,finishRangeSelectionPrompt:"Klikk for å fullføre valg av datoområde",maximumDate:"Siste tilgjengelige dato",minimumDate:"Første tilgjengelige dato",next:"Neste",previous:"Forrige",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt område: ${e.dateRange}`,startRangeSelectionPrompt:"Klikk for å starte valg av datoområde",todayDate:e=>`I dag, ${e.date}`,todayDateSelected:e=>`I dag, ${e.date} valgt`};var hn={};hn={dateRange:e=>`${e.startDate} tot ${e.endDate}`,dateSelected:e=>`${e.date} geselecteerd`,finishRangeSelectionPrompt:"Klik om de selectie van het datumbereik te voltooien",maximumDate:"Laatste beschikbare datum",minimumDate:"Eerste beschikbare datum",next:"Volgende",previous:"Vorige",selectedDateDescription:e=>`Geselecteerde datum: ${e.date}`,selectedRangeDescription:e=>`Geselecteerd bereik: ${e.dateRange}`,startRangeSelectionPrompt:"Klik om het datumbereik te selecteren",todayDate:e=>`Vandaag, ${e.date}`,todayDateSelected:e=>`Vandaag, ${e.date} geselecteerd`};var pn={};pn={dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`Wybrano ${e.date}`,finishRangeSelectionPrompt:"Kliknij, aby zakończyć wybór zakresu dat",maximumDate:"Ostatnia dostępna data",minimumDate:"Pierwsza dostępna data",next:"Dalej",previous:"Wstecz",selectedDateDescription:e=>`Wybrana data: ${e.date}`,selectedRangeDescription:e=>`Wybrany zakres: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknij, aby rozpocząć wybór zakresu dat",todayDate:e=>`Dzisiaj, ${e.date}`,todayDateSelected:e=>`Dzisiaj wybrano ${e.date}`};var bn={};bn={dateRange:e=>`${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} selecionado`,finishRangeSelectionPrompt:"Clique para concluir a seleção do intervalo de datas",maximumDate:"Última data disponível",minimumDate:"Primeira data disponível",next:"Próximo",previous:"Anterior",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.dateRange}`,startRangeSelectionPrompt:"Clique para iniciar a seleção do intervalo de datas",todayDate:e=>`Hoje, ${e.date}`,todayDateSelected:e=>`Hoje, ${e.date} selecionado`};var yn={};yn={dateRange:e=>`${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} selecionado`,finishRangeSelectionPrompt:"Clique para terminar de selecionar o intervalo de datas",maximumDate:"Última data disponível",minimumDate:"Primeira data disponível",next:"Próximo",previous:"Anterior",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.dateRange}`,startRangeSelectionPrompt:"Clique para começar a selecionar o intervalo de datas",todayDate:e=>`Hoje, ${e.date}`,todayDateSelected:e=>`Hoje, ${e.date} selecionado`};var gn={};gn={dateRange:e=>`De la ${e.startDate} până la ${e.endDate}`,dateSelected:e=>`${e.date} selectată`,finishRangeSelectionPrompt:"Apăsaţi pentru a finaliza selecţia razei pentru dată",maximumDate:"Ultima dată disponibilă",minimumDate:"Prima dată disponibilă",next:"Următorul",previous:"Înainte",selectedDateDescription:e=>`Dată selectată: ${e.date}`,selectedRangeDescription:e=>`Interval selectat: ${e.dateRange}`,startRangeSelectionPrompt:"Apăsaţi pentru a începe selecţia razei pentru dată",todayDate:e=>`Astăzi, ${e.date}`,todayDateSelected:e=>`Azi, ${e.date} selectată`};var vn={};vn={dateRange:e=>`С ${e.startDate} по ${e.endDate}`,dateSelected:e=>`Выбрано ${e.date}`,finishRangeSelectionPrompt:"Щелкните, чтобы завершить выбор диапазона дат",maximumDate:"Последняя доступная дата",minimumDate:"Первая доступная дата",next:"Далее",previous:"Назад",selectedDateDescription:e=>`Выбранная дата: ${e.date}`,selectedRangeDescription:e=>`Выбранный диапазон: ${e.dateRange}`,startRangeSelectionPrompt:"Щелкните, чтобы начать выбор диапазона дат",todayDate:e=>`Сегодня, ${e.date}`,todayDateSelected:e=>`Сегодня, выбрано ${e.date}`};var xn={};xn={dateRange:e=>`Od ${e.startDate} do ${e.endDate}`,dateSelected:e=>`Vybratý dátum ${e.date}`,finishRangeSelectionPrompt:"Kliknutím dokončíte výber rozsahu dátumov",maximumDate:"Posledný dostupný dátum",minimumDate:"Prvý dostupný dátum",next:"Nasledujúce",previous:"Predchádzajúce",selectedDateDescription:e=>`Vybratý dátum: ${e.date}`,selectedRangeDescription:e=>`Vybratý rozsah: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknutím spustíte výber rozsahu dátumov",todayDate:e=>`Dnes ${e.date}`,todayDateSelected:e=>`Vybratý dnešný dátum ${e.date}`};var En={};En={dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`${e.date} izbrano`,finishRangeSelectionPrompt:"Kliknite za dokončanje izbire datumskega obsega",maximumDate:"Zadnji razpoložljivi datum",minimumDate:"Prvi razpoložljivi datum",next:"Naprej",previous:"Nazaj",selectedDateDescription:e=>`Izbrani datum: ${e.date}`,selectedRangeDescription:e=>`Izbrano območje: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknite za začetek izbire datumskega obsega",todayDate:e=>`Danes, ${e.date}`,todayDateSelected:e=>`Danes, ${e.date} izbrano`};var Cn={};Cn={dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`${e.date} izabran`,finishRangeSelectionPrompt:"Kliknite da dovršite opseg izabranih datuma",maximumDate:"Zadnji raspoloživi datum",minimumDate:"Prvi raspoloživi datum",next:"Sledeći",previous:"Prethodni",selectedDateDescription:e=>`Izabrani datum: ${e.date}`,selectedRangeDescription:e=>`Izabrani period: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknite da započnete opseg izabranih datuma",todayDate:e=>`Danas, ${e.date}`,todayDateSelected:e=>`Danas, izabran ${e.date}`};var Bn={};Bn={dateRange:e=>`${e.startDate} till ${e.endDate}`,dateSelected:e=>`${e.date} har valts`,finishRangeSelectionPrompt:"Klicka för att avsluta val av datumintervall",maximumDate:"Sista tillgängliga datum",minimumDate:"Första tillgängliga datum",next:"Nästa",previous:"Föregående",selectedDateDescription:e=>`Valt datum: ${e.date}`,selectedRangeDescription:e=>`Valt intervall: ${e.dateRange}`,startRangeSelectionPrompt:"Klicka för att välja datumintervall",todayDate:e=>`Idag, ${e.date}`,todayDateSelected:e=>`Idag, ${e.date} har valts`};var Pn={};Pn={dateRange:e=>`${e.startDate} - ${e.endDate}`,dateSelected:e=>`${e.date} seçildi`,finishRangeSelectionPrompt:"Tarih aralığı seçimini tamamlamak için tıklayın",maximumDate:"Son müsait tarih",minimumDate:"İlk müsait tarih",next:"Sonraki",previous:"Önceki",selectedDateDescription:e=>`Seçilen Tarih: ${e.date}`,selectedRangeDescription:e=>`Seçilen Aralık: ${e.dateRange}`,startRangeSelectionPrompt:"Tarih aralığı seçimini başlatmak için tıklayın",todayDate:e=>`Bugün, ${e.date}`,todayDateSelected:e=>`Bugün, ${e.date} seçildi`};var wn={};wn={dateRange:e=>`${e.startDate} — ${e.endDate}`,dateSelected:e=>`Вибрано ${e.date}`,finishRangeSelectionPrompt:"Натисніть, щоб завершити вибір діапазону дат",maximumDate:"Остання доступна дата",minimumDate:"Перша доступна дата",next:"Наступний",previous:"Попередній",selectedDateDescription:e=>`Вибрана дата: ${e.date}`,selectedRangeDescription:e=>`Вибраний діапазон: ${e.dateRange}`,startRangeSelectionPrompt:"Натисніть, щоб почати вибір діапазону дат",todayDate:e=>`Сьогодні, ${e.date}`,todayDateSelected:e=>`Сьогодні, вибрано ${e.date}`};var Fn={};Fn={dateRange:e=>`${e.startDate} 至 ${e.endDate}`,dateSelected:e=>`已选择 ${e.date}`,finishRangeSelectionPrompt:"单击以完成选择日期范围",maximumDate:"最后一个可用日期",minimumDate:"第一个可用日期",next:"下一页",previous:"上一页",selectedDateDescription:e=>`选定的日期：${e.date}`,selectedRangeDescription:e=>`选定的范围：${e.dateRange}`,startRangeSelectionPrompt:"单击以开始选择日期范围",todayDate:e=>`今天，即 ${e.date}`,todayDateSelected:e=>`已选择今天，即 ${e.date}`};var Sn={};Sn={dateRange:e=>`${e.startDate} 至 ${e.endDate}`,dateSelected:e=>`已選取 ${e.date}`,finishRangeSelectionPrompt:"按一下以完成選取日期範圍",maximumDate:"最後一個可用日期",minimumDate:"第一個可用日期",next:"下一頁",previous:"上一頁",selectedDateDescription:e=>`選定的日期：${e.date}`,selectedRangeDescription:e=>`選定的範圍：${e.dateRange}`,startRangeSelectionPrompt:"按一下以開始選取日期範圍",todayDate:e=>`今天，${e.date}`,todayDateSelected:e=>`已選取今天，${e.date}`};var dt={};dt={"ar-AE":Gu,"bg-BG":Ju,"cs-CZ":qu,"da-DK":Qu,"de-DE":Xu,"el-GR":en,"en-US":tn,"es-ES":an,"et-EE":rn,"fi-FI":un,"fr-FR":nn,"he-IL":ln,"hr-HR":on,"hu-HU":sn,"it-IT":dn,"ja-JP":cn,"ko-KR":fn,"lt-LT":mn,"lv-LV":$n,"nb-NO":Dn,"nl-NL":hn,"pl-PL":pn,"pt-BR":bn,"pt-PT":yn,"ro-RO":gn,"ru-RU":vn,"sk-SK":xn,"sl-SI":En,"sr-SP":Cn,"sv-SE":Bn,"tr-TR":Pn,"uk-UA":wn,"zh-CN":Fn,"zh-TW":Sn};function An(e){return e&&e.__esModule?e.default:e}const Ya=new WeakMap;function ut(e){return(e==null?void 0:e.calendar.identifier)==="gregory"&&e.era==="BC"?"short":void 0}function nd(e){let t=Se(An(dt),"@react-aria/calendar"),a,r;var u;"highlightedRange"in e?{start:a,end:r}=e.highlightedRange||{}:a=r=(u=e.value)!==null&&u!==void 0?u:void 0;let n=re({weekday:"long",month:"long",year:"numeric",day:"numeric",era:ut(a)||ut(r),timeZone:e.timeZone}),i="anchorDate"in e?e.anchorDate:null;return o.useMemo(()=>{if(!i&&a&&r)if(_(a,r)){let l=n.format(a.toDate(e.timeZone));return t.format("selectedDateDescription",{date:l})}else{let l=Ca(n,t,a,r,e.timeZone);return t.format("selectedRangeDescription",{dateRange:l})}return""},[a,r,i,e.timeZone,t,n])}function Ea(e,t,a,r){let u=Se(An(dt),"@react-aria/calendar"),n=ut(e)||ut(t),i=re({month:"long",year:"numeric",era:n,calendar:e.calendar.identifier,timeZone:a}),l=re({month:"long",year:"numeric",day:"numeric",era:n,calendar:e.calendar.identifier,timeZone:a});return o.useMemo(()=>{if(_(e,lt(e))){if(_(t,sa(e)))return i.format(e.toDate(a));if(_(t,sa(t)))return r?Ca(i,u,e,t,a):i.formatRange(e.toDate(a),t.toDate(a))}return r?Ca(l,u,e,t,a):l.formatRange(e.toDate(a),t.toDate(a))},[e,t,i,l,u,a,r])}function Ca(e,t,a,r,u){let n=e.formatRangeToParts(a.toDate(u),r.toDate(u)),i=-1;for(let c=0;c<n.length;c++){let D=n[c];if(D.source==="shared"&&D.type==="literal")i=c;else if(D.source==="endRange")break}let l="",s="";for(let c=0;c<n.length;c++)c<i?l+=n[c].value:c>i&&(s+=n[c].value);return t.format("dateRange",{startDate:l,endDate:s})}const Rn=7e3;let Q=null;function Ba(e,t="assertive",a=Rn){Q?Q.announce(e,t,a):(Q=new ld,(typeof IS_REACT_ACT_ENVIRONMENT=="boolean"?IS_REACT_ACT_ENVIRONMENT:typeof jest<"u")?Q.announce(e,t,a):setTimeout(()=>{Q!=null&&Q.isAttached()&&(Q==null||Q.announce(e,t,a))},100))}function id(e){Q&&Q.clear(e)}class ld{isAttached(){var t;return(t=this.node)===null||t===void 0?void 0:t.isConnected}createLog(t){let a=document.createElement("div");return a.setAttribute("role","log"),a.setAttribute("aria-live",t),a.setAttribute("aria-relevant","additions"),a}destroy(){this.node&&(document.body.removeChild(this.node),this.node=null)}announce(t,a="assertive",r=Rn){var u,n;if(!this.node)return;let i=document.createElement("div");typeof t=="object"?(i.setAttribute("role","img"),i.setAttribute("aria-labelledby",t["aria-labelledby"])):i.textContent=t,a==="assertive"?(u=this.assertiveLog)===null||u===void 0||u.appendChild(i):(n=this.politeLog)===null||n===void 0||n.appendChild(i),t!==""&&setTimeout(()=>{i.remove()},r)}clear(t){this.node&&((!t||t==="assertive")&&this.assertiveLog&&(this.assertiveLog.innerHTML=""),(!t||t==="polite")&&this.politeLog&&(this.politeLog.innerHTML=""))}constructor(){this.node=null,this.assertiveLog=null,this.politeLog=null,typeof document<"u"&&(this.node=document.createElement("div"),this.node.dataset.liveAnnouncer="true",Object.assign(this.node.style,{border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"}),this.assertiveLog=this.createLog("assertive"),this.node.appendChild(this.assertiveLog),this.politeLog=this.createLog("polite"),this.node.appendChild(this.politeLog),document.body.prepend(this.node))}}function od(e){return e&&e.__esModule?e.default:e}function sd(e,t){let a=Se(od(dt),"@react-aria/calendar"),r=Lt(e),u=Ea(t.visibleRange.start,t.visibleRange.end,t.timeZone,!1),n=Ea(t.visibleRange.start,t.visibleRange.end,t.timeZone,!0);pr(()=>{t.isFocused||Ba(n)},[n]);let i=nd(t);pr(()=>{i&&Ba(i,"polite",4e3)},[i]);let l=ha([!!e.errorMessage,e.isInvalid,e.validationState]);Ya.set(t,{ariaLabel:e["aria-label"],ariaLabelledBy:e["aria-labelledby"],errorMessageId:l,selectedDateDescription:i});let[s,c]=o.useState(!1),D=e.isDisabled||t.isNextVisibleRangeInvalid();D&&s&&(c(!1),t.setFocused(!0));let[$,b]=o.useState(!1),E=e.isDisabled||t.isPreviousVisibleRangeInvalid();E&&$&&(b(!1),t.setFocused(!0));let p=jt({id:e.id,"aria-label":[e["aria-label"],n].filter(Boolean).join(", "),"aria-labelledby":e["aria-labelledby"]});return{calendarProps:H(r,p,{role:"application","aria-describedby":e["aria-describedby"]||void 0}),nextButtonProps:{onPress:()=>t.focusNextPage(),"aria-label":a.format("next"),isDisabled:D,onFocusChange:c},prevButtonProps:{onPress:()=>t.focusPreviousPage(),"aria-label":a.format("previous"),isDisabled:E,onFocusChange:b},errorMessageProps:{id:l},title:u}}function dd(e,t){return sd(e,t)}function cd(e,t){let{startDate:a=t.visibleRange.start,endDate:r=t.visibleRange.end,firstDayOfWeek:u}=e,{direction:n}=q(),i=p=>{switch(p.key){case"Enter":case" ":p.preventDefault(),t.selectFocusedDate();break;case"PageUp":p.preventDefault(),p.stopPropagation(),t.focusPreviousSection(p.shiftKey);break;case"PageDown":p.preventDefault(),p.stopPropagation(),t.focusNextSection(p.shiftKey);break;case"End":p.preventDefault(),p.stopPropagation(),t.focusSectionEnd();break;case"Home":p.preventDefault(),p.stopPropagation(),t.focusSectionStart();break;case"ArrowLeft":p.preventDefault(),p.stopPropagation(),n==="rtl"?t.focusNextDay():t.focusPreviousDay();break;case"ArrowUp":p.preventDefault(),p.stopPropagation(),t.focusPreviousRow();break;case"ArrowRight":p.preventDefault(),p.stopPropagation(),n==="rtl"?t.focusPreviousDay():t.focusNextDay();break;case"ArrowDown":p.preventDefault(),p.stopPropagation(),t.focusNextRow();break;case"Escape":"setAnchorDate"in t&&(p.preventDefault(),t.setAnchorDate(null));break}},l=Ea(a,r,t.timeZone,!0),{ariaLabel:s,ariaLabelledBy:c}=Ya.get(t),D=jt({"aria-label":[s,l].filter(Boolean).join(", "),"aria-labelledby":c}),$=re({weekday:e.weekdayStyle||"narrow",timeZone:t.timeZone}),{locale:b}=q(),E=o.useMemo(()=>{let p=Xe(It(t.timeZone),b,u);return[...new Array(7).keys()].map(x=>{let R=p.add({days:x}).toDate(t.timeZone);return $.format(R)})},[b,t.timeZone,$,u]);return{gridProps:H(D,{role:"grid","aria-readonly":t.isReadOnly||void 0,"aria-disabled":t.isDisabled||void 0,"aria-multiselectable":"highlightedRange"in t||void 0,onKeyDown:i,onFocus:()=>t.setFocused(!0),onBlur:()=>t.setFocused(!1)}),headerProps:{"aria-hidden":!0},weekDays:E}}function fd(e){return e&&e.__esModule?e.default:e}function md(e,t,a){let{date:r,isDisabled:u}=e,{errorMessageId:n,selectedDateDescription:i}=Ya.get(t),l=Se(fd(dt),"@react-aria/calendar"),s=re({weekday:"long",day:"numeric",month:"long",year:"numeric",era:ut(r),timeZone:t.timeZone}),c=t.isSelected(r),D=t.isCellFocused(r);u=u||t.isCellDisabled(r);let $=t.isCellUnavailable(r),b=!u&&!$,E=t.isValueInvalid&&!!("highlightedRange"in t?!t.anchorDate&&t.highlightedRange&&r.compare(t.highlightedRange.start)>=0&&r.compare(t.highlightedRange.end)<=0:t.value&&_(t.value,r));E&&(c=!0),r=Su(r,no);let p=o.useMemo(()=>r.toDate(t.timeZone),[r,t.timeZone]),x=io(r,t.timeZone),w=o.useMemo(()=>{let f="";return"highlightedRange"in t&&t.value&&!t.anchorDate&&(_(r,t.value.start)||_(r,t.value.end))&&(f=i+", "),f+=s.format(p),x?f=l.format(c?"todayDateSelected":"todayDate",{date:f}):c&&(f=l.format("dateSelected",{date:f})),t.minValue&&_(r,t.minValue)?f+=", "+l.format("minimumDate"):t.maxValue&&_(r,t.maxValue)&&(f+=", "+l.format("maximumDate")),f},[s,p,l,c,x,r,t,i]),R="";"anchorDate"in t&&D&&!t.isReadOnly&&b&&(t.anchorDate?R=l.format("finishRangeSelectionPrompt"):R=l.format("startRangeSelectionPrompt"));let k=Ua(R),C=o.useRef(!1),y=o.useRef(!1),A=o.useRef(void 0),{pressProps:T,isPressed:I}=Ka({shouldCancelOnPointerExit:"anchorDate"in t&&!!t.anchorDate,preventFocusOnPress:!0,isDisabled:!b||t.isReadOnly,onPressStart(f){if(t.isReadOnly){t.setFocusedDate(r);return}if("highlightedRange"in t&&!t.anchorDate&&(f.pointerType==="mouse"||f.pointerType==="touch")){if(t.highlightedRange&&!E){if(_(r,t.highlightedRange.start)){t.setAnchorDate(t.highlightedRange.end),t.setFocusedDate(r),t.setDragging(!0),y.current=!0;return}else if(_(r,t.highlightedRange.end)){t.setAnchorDate(t.highlightedRange.start),t.setFocusedDate(r),t.setDragging(!0),y.current=!0;return}}let F=()=>{t.setDragging(!0),A.current=void 0,t.selectDate(r),t.setFocusedDate(r),C.current=!0};f.pointerType==="touch"?A.current=setTimeout(F,200):F()}},onPressEnd(){y.current=!1,C.current=!1,clearTimeout(A.current),A.current=void 0},onPress(){!("anchorDate"in t)&&!t.isReadOnly&&(t.selectDate(r),t.setFocusedDate(r))},onPressUp(f){if(!t.isReadOnly&&("anchorDate"in t&&A.current&&(t.selectDate(r),t.setFocusedDate(r)),"anchorDate"in t))if(y.current)t.setAnchorDate(r);else if(t.anchorDate&&!C.current)t.selectDate(r),t.setFocusedDate(r);else if(f.pointerType==="keyboard"&&!t.anchorDate){t.selectDate(r);let F=r.add({days:1});t.isInvalid(F)&&(F=r.subtract({days:1})),t.isInvalid(F)||t.setFocusedDate(F)}else f.pointerType==="virtual"&&(t.selectDate(r),t.setFocusedDate(r))}}),d;u||(d=_(r,t.focusedDate)?0:-1),o.useEffect(()=>{D&&a.current&&(pe(a.current),Nu()!=="pointer"&&document.activeElement===a.current&&Fu(a.current,{containingElement:wu(a.current)}))},[D,a]);let S=re({day:"numeric",timeZone:t.timeZone,calendar:r.calendar.identifier}),g=o.useMemo(()=>S.formatToParts(p).find(f=>f.type==="day").value,[S,p]);return{cellProps:{role:"gridcell","aria-disabled":!b||void 0,"aria-selected":c||void 0,"aria-invalid":E||void 0},buttonProps:H(T,{onFocus(){u||t.setFocusedDate(r)},tabIndex:d,role:"button","aria-disabled":!b||void 0,"aria-label":w,"aria-invalid":E||void 0,"aria-describedby":[E?n:void 0,k["aria-describedby"]].filter(Boolean).join(" ")||void 0,onPointerEnter(f){"highlightDate"in t&&(f.pointerType!=="touch"||t.isDragging)&&b&&t.highlightDate(r)},onPointerDown(f){"releasePointerCapture"in f.target&&f.target.releasePointerCapture(f.pointerId)},onContextMenu(f){f.preventDefault()}}),isPressed:I,isFocused:D,isSelected:c,isDisabled:u,isUnavailable:$,isOutsideVisibleRange:r.compare(t.visibleRange.start)<0||r.compare(t.visibleRange.end)>0,isInvalid:E,formattedDate:g}}const kn={badInput:!1,customError:!1,patternMismatch:!1,rangeOverflow:!1,rangeUnderflow:!1,stepMismatch:!1,tooLong:!1,tooShort:!1,typeMismatch:!1,valueMissing:!1,valid:!0},Tn={...kn,customError:!0,valid:!1},We={isInvalid:!1,validationDetails:kn,validationErrors:[]},$d=o.createContext({}),Pa="__formValidationState"+Date.now();function Mn(e){if(e[Pa]){let{realtimeValidation:t,displayValidation:a,updateValidation:r,resetValidation:u,commitValidation:n}=e[Pa];return{realtimeValidation:t,displayValidation:a,updateValidation:r,resetValidation:u,commitValidation:n}}return Dd(e)}function Dd(e){let{isInvalid:t,validationState:a,name:r,value:u,builtinValidation:n,validate:i,validationBehavior:l="aria"}=e;a&&(t||(t=a==="invalid"));let s=t!==void 0?{isInvalid:t,validationErrors:[],validationDetails:Tn}:null,c=o.useMemo(()=>{if(!i||u==null)return null;let g=hd(i,u);return Mr(g)},[i,u]);n!=null&&n.validationDetails.valid&&(n=void 0);let D=o.useContext($d),$=o.useMemo(()=>r?Array.isArray(r)?r.flatMap(g=>wa(D[g])):wa(D[r]):[],[D,r]),[b,E]=o.useState(D),[p,x]=o.useState(!1);D!==b&&(E(D),x(!1));let w=o.useMemo(()=>Mr(p?[]:$),[p,$]),R=o.useRef(We),[k,C]=o.useState(We),y=o.useRef(We),A=()=>{if(!T)return;I(!1);let g=c||n||R.current;ia(g,y.current)||(y.current=g,C(g))},[T,I]=o.useState(!1);return o.useEffect(A),{realtimeValidation:s||w||c||n||We,displayValidation:l==="native"?s||w||k:s||w||c||n||k,updateValidation(g){l==="aria"&&!ia(k,g)?C(g):R.current=g},resetValidation(){let g=We;ia(g,y.current)||(y.current=g,C(g)),l==="native"&&I(!1),x(!0)},commitValidation(){l==="native"&&I(!0),x(!0)}}}function wa(e){return e?Array.isArray(e)?e:[e]:[]}function hd(e,t){if(typeof e=="function"){let a=e(t);if(a&&typeof a!="boolean")return wa(a)}return[]}function Mr(e){return e.length?{isInvalid:!0,validationErrors:e,validationDetails:Tn}:null}function ia(e,t){return e===t?!0:!!e&&!!t&&e.isInvalid===t.isInvalid&&e.validationErrors.length===t.validationErrors.length&&e.validationErrors.every((a,r)=>a===t.validationErrors[r])&&Object.entries(e.validationDetails).every(([a,r])=>t.validationDetails[a]===r)}function pd(e,t,a){let{validationBehavior:r,focus:u}=e;fe(()=>{if(r==="native"&&(a!=null&&a.current)&&!a.current.disabled){let s=t.realtimeValidation.isInvalid?t.realtimeValidation.validationErrors.join(" ")||"Invalid value.":"";a.current.setCustomValidity(s),a.current.hasAttribute("title")||(a.current.title=""),t.realtimeValidation.isInvalid||t.updateValidation(yd(a.current))}});let n=Z(()=>{t.resetValidation()}),i=Z(s=>{var c;t.displayValidation.isInvalid||t.commitValidation();let D=a==null||(c=a.current)===null||c===void 0?void 0:c.form;if(!s.defaultPrevented&&a&&D&&gd(D)===a.current){var $;u?u():($=a.current)===null||$===void 0||$.focus(),Ts("keyboard")}s.preventDefault()}),l=Z(()=>{t.commitValidation()});o.useEffect(()=>{let s=a==null?void 0:a.current;if(!s)return;let c=s.form;return s.addEventListener("invalid",i),s.addEventListener("change",l),c==null||c.addEventListener("reset",n),()=>{s.removeEventListener("invalid",i),s.removeEventListener("change",l),c==null||c.removeEventListener("reset",n)}},[a,i,l,n,r])}function bd(e){let t=e.validity;return{badInput:t.badInput,customError:t.customError,patternMismatch:t.patternMismatch,rangeOverflow:t.rangeOverflow,rangeUnderflow:t.rangeUnderflow,stepMismatch:t.stepMismatch,tooLong:t.tooLong,tooShort:t.tooShort,typeMismatch:t.typeMismatch,valueMissing:t.valueMissing,valid:t.valid}}function yd(e){return{isInvalid:!e.validity.valid,validationDetails:bd(e),validationErrors:e.validationMessage?[e.validationMessage]:[]}}function gd(e){for(let t=0;t<e.elements.length;t++){let a=e.elements[t];if(!a.validity.valid)return a}return null}function vd(e){let{id:t,label:a,"aria-labelledby":r,"aria-label":u,labelElementType:n="label"}=e;t=be(t);let i=be(),l={};a?(r=r?`${i} ${r}`:i,l={id:i,htmlFor:n==="label"?t:void 0}):!r&&!u&&console.warn("If you do not provide a visible label, you must specify an aria-label or aria-labelledby attribute for accessibility");let s=jt({id:t,"aria-label":u,"aria-labelledby":r});return{labelProps:l,fieldProps:s}}function Vn(e){let{description:t,errorMessage:a,isInvalid:r,validationState:u}=e,{labelProps:n,fieldProps:i}=vd(e),l=ha([!!t,!!a,r,u]),s=ha([!!t,!!a,r,u]);return i=H(i,{"aria-describedby":[l,s,e["aria-describedby"]].filter(Boolean).join(" ")||void 0}),{labelProps:n,fieldProps:i,descriptionProps:{id:l},errorMessageProps:{id:s}}}var In={};In={Empty:"فارغ"};var On={};On={Empty:"Изпразни"};var Ln={};Ln={Empty:"Prázdné"};var Nn={};Nn={Empty:"Tom"};var jn={};jn={Empty:"Leer"};var Un={};Un={Empty:"Άδειο"};var Kn={};Kn={Empty:"Empty"};var _n={};_n={Empty:"Vacío"};var zn={};zn={Empty:"Tühjenda"};var Zn={};Zn={Empty:"Tyhjä"};var Wn={};Wn={Empty:"Vide"};var Yn={};Yn={Empty:"ריק"};var Hn={};Hn={Empty:"Prazno"};var Gn={};Gn={Empty:"Üres"};var Jn={};Jn={Empty:"Vuoto"};var qn={};qn={Empty:"空"};var Qn={};Qn={Empty:"비어 있음"};var Xn={};Xn={Empty:"Tuščias"};var ei={};ei={Empty:"Tukšs"};var ti={};ti={Empty:"Tom"};var ai={};ai={Empty:"Leeg"};var ri={};ri={Empty:"Pusty"};var ui={};ui={Empty:"Vazio"};var ni={};ni={Empty:"Vazio"};var ii={};ii={Empty:"Gol"};var li={};li={Empty:"Не заполнено"};var oi={};oi={Empty:"Prázdne"};var si={};si={Empty:"Prazen"};var di={};di={Empty:"Prazno"};var ci={};ci={Empty:"Tomt"};var fi={};fi={Empty:"Boş"};var mi={};mi={Empty:"Пусто"};var $i={};$i={Empty:"空"};var Di={};Di={Empty:"空白"};var hi={};hi={"ar-AE":In,"bg-BG":On,"cs-CZ":Ln,"da-DK":Nn,"de-DE":jn,"el-GR":Un,"en-US":Kn,"es-ES":_n,"et-EE":zn,"fi-FI":Zn,"fr-FR":Wn,"he-IL":Yn,"hr-HR":Hn,"hu-HU":Gn,"it-IT":Jn,"ja-JP":qn,"ko-KR":Qn,"lt-LT":Xn,"lv-LV":ei,"nb-NO":ti,"nl-NL":ai,"pl-PL":ri,"pt-BR":ui,"pt-PT":ni,"ro-RO":ii,"ru-RU":li,"sk-SK":oi,"sl-SI":si,"sr-SP":di,"sv-SE":ci,"tr-TR":fi,"uk-UA":mi,"zh-CN":$i,"zh-TW":Di};function xd(e){return e&&e.__esModule?e.default:e}function Ed(e){const t=o.useRef(void 0);let{value:a,textValue:r,minValue:u,maxValue:n,isDisabled:i,isReadOnly:l,isRequired:s,onIncrement:c,onIncrementPage:D,onDecrement:$,onDecrementPage:b,onDecrementToMin:E,onIncrementToMax:p}=e;const x=Se(xd(hi),"@react-aria/spinbutton"),w=()=>clearTimeout(t.current);o.useEffect(()=>()=>w(),[]);let R=f=>{if(!(f.ctrlKey||f.metaKey||f.shiftKey||f.altKey||l))switch(f.key){case"PageUp":if(D){f.preventDefault(),D==null||D();break}case"ArrowUp":case"Up":c&&(f.preventDefault(),c==null||c());break;case"PageDown":if(b){f.preventDefault(),b==null||b();break}case"ArrowDown":case"Down":$&&(f.preventDefault(),$==null||$());break;case"Home":E&&(f.preventDefault(),E==null||E());break;case"End":p&&(f.preventDefault(),p==null||p());break}},k=o.useRef(!1),C=()=>{k.current=!0},y=()=>{k.current=!1},A=r===""?x.format("Empty"):(r||`${a}`).replace("-","−");o.useEffect(()=>{k.current&&(id("assertive"),Ba(A,"assertive"))},[A]);const T=Z(f=>{w(),c==null||c(),t.current=window.setTimeout(()=>{(n===void 0||isNaN(n)||a===void 0||isNaN(a)||a<n)&&T(60)},f)}),I=Z(f=>{w(),$==null||$(),t.current=window.setTimeout(()=>{(u===void 0||isNaN(u)||a===void 0||isNaN(a)||a>u)&&I(60)},f)});let d=f=>{f.preventDefault()},{addGlobalListener:S,removeAllGlobalListeners:g}=Bu();return{spinButtonProps:{role:"spinbutton","aria-valuenow":a!==void 0&&!isNaN(a)?a:void 0,"aria-valuetext":A,"aria-valuemin":u,"aria-valuemax":n,"aria-disabled":i||void 0,"aria-readonly":l||void 0,"aria-required":s||void 0,onKeyDown:R,onFocus:C,onBlur:y},incrementButtonProps:{onPressStart:()=>{T(400),S(window,"contextmenu",d)},onPressEnd:()=>{w(),g()},onFocus:C,onBlur:y},decrementButtonProps:{onPressStart:()=>{I(400),S(window,"contextmenu",d)},onPressEnd:()=>{w(),g()},onFocus:C,onBlur:y}}}var pi={};pi={calendar:"التقويم",day:"يوم",dayPeriod:"ص/م",endDate:"تاريخ الانتهاء",era:"العصر",hour:"الساعات",minute:"الدقائق",month:"الشهر",second:"الثواني",selectedDateDescription:e=>`تاريخ محدد: ${e.date}`,selectedRangeDescription:e=>`المدى الزمني المحدد: ${e.startDate} إلى ${e.endDate}`,selectedTimeDescription:e=>`الوقت المحدد: ${e.time}`,startDate:"تاريخ البدء",timeZoneName:"التوقيت",weekday:"اليوم",year:"السنة"};var bi={};bi={calendar:"Календар",day:"ден",dayPeriod:"пр.об./сл.об.",endDate:"Крайна дата",era:"ера",hour:"час",minute:"минута",month:"месец",second:"секунда",selectedDateDescription:e=>`Избрана дата: ${e.date}`,selectedRangeDescription:e=>`Избран диапазон: ${e.startDate} до ${e.endDate}`,selectedTimeDescription:e=>`Избрано време: ${e.time}`,startDate:"Начална дата",timeZoneName:"часова зона",weekday:"ден от седмицата",year:"година"};var yi={};yi={calendar:"Kalendář",day:"den",dayPeriod:"část dne",endDate:"Konečné datum",era:"letopočet",hour:"hodina",minute:"minuta",month:"měsíc",second:"sekunda",selectedDateDescription:e=>`Vybrané datum: ${e.date}`,selectedRangeDescription:e=>`Vybrané období: ${e.startDate} až ${e.endDate}`,selectedTimeDescription:e=>`Vybraný čas: ${e.time}`,startDate:"Počáteční datum",timeZoneName:"časové pásmo",weekday:"den v týdnu",year:"rok"};var gi={};gi={calendar:"Kalender",day:"dag",dayPeriod:"AM/PM",endDate:"Slutdato",era:"æra",hour:"time",minute:"minut",month:"måned",second:"sekund",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt interval: ${e.startDate} til ${e.endDate}`,selectedTimeDescription:e=>`Valgt tidspunkt: ${e.time}`,startDate:"Startdato",timeZoneName:"tidszone",weekday:"ugedag",year:"år"};var vi={};vi={calendar:"Kalender",day:"Tag",dayPeriod:"Tageshälfte",endDate:"Enddatum",era:"Epoche",hour:"Stunde",minute:"Minute",month:"Monat",second:"Sekunde",selectedDateDescription:e=>`Ausgewähltes Datum: ${e.date}`,selectedRangeDescription:e=>`Ausgewählter Bereich: ${e.startDate} bis ${e.endDate}`,selectedTimeDescription:e=>`Ausgewählte Zeit: ${e.time}`,startDate:"Anfangsdatum",timeZoneName:"Zeitzone",weekday:"Wochentag",year:"Jahr"};var xi={};xi={calendar:"Ημερολόγιο",day:"ημέρα",dayPeriod:"π.μ./μ.μ.",endDate:"Ημερομηνία λήξης",era:"περίοδος",hour:"ώρα",minute:"λεπτό",month:"μήνας",second:"δευτερόλεπτο",selectedDateDescription:e=>`Επιλεγμένη ημερομηνία: ${e.date}`,selectedRangeDescription:e=>`Επιλεγμένο εύρος: ${e.startDate} έως ${e.endDate}`,selectedTimeDescription:e=>`Επιλεγμένη ώρα: ${e.time}`,startDate:"Ημερομηνία έναρξης",timeZoneName:"ζώνη ώρας",weekday:"καθημερινή",year:"έτος"};var Ei={};Ei={era:"era",year:"year",month:"month",day:"day",hour:"hour",minute:"minute",second:"second",dayPeriod:"AM/PM",calendar:"Calendar",startDate:"Start Date",endDate:"End Date",weekday:"day of the week",timeZoneName:"time zone",selectedDateDescription:e=>`Selected Date: ${e.date}`,selectedRangeDescription:e=>`Selected Range: ${e.startDate} to ${e.endDate}`,selectedTimeDescription:e=>`Selected Time: ${e.time}`};var Ci={};Ci={calendar:"Calendario",day:"día",dayPeriod:"a. m./p. m.",endDate:"Fecha final",era:"era",hour:"hora",minute:"minuto",month:"mes",second:"segundo",selectedDateDescription:e=>`Fecha seleccionada: ${e.date}`,selectedRangeDescription:e=>`Rango seleccionado: ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Hora seleccionada: ${e.time}`,startDate:"Fecha de inicio",timeZoneName:"zona horaria",weekday:"día de la semana",year:"año"};var Bi={};Bi={calendar:"Kalender",day:"päev",dayPeriod:"enne/pärast lõunat",endDate:"Lõppkuupäev",era:"ajastu",hour:"tund",minute:"minut",month:"kuu",second:"sekund",selectedDateDescription:e=>`Valitud kuupäev: ${e.date}`,selectedRangeDescription:e=>`Valitud vahemik: ${e.startDate} kuni ${e.endDate}`,selectedTimeDescription:e=>`Valitud aeg: ${e.time}`,startDate:"Alguskuupäev",timeZoneName:"ajavöönd",weekday:"nädalapäev",year:"aasta"};var Pi={};Pi={calendar:"Kalenteri",day:"päivä",dayPeriod:"vuorokaudenaika",endDate:"Päättymispäivä",era:"aikakausi",hour:"tunti",minute:"minuutti",month:"kuukausi",second:"sekunti",selectedDateDescription:e=>`Valittu päivämäärä: ${e.date}`,selectedRangeDescription:e=>`Valittu aikaväli: ${e.startDate} – ${e.endDate}`,selectedTimeDescription:e=>`Valittu aika: ${e.time}`,startDate:"Alkamispäivä",timeZoneName:"aikavyöhyke",weekday:"viikonpäivä",year:"vuosi"};var wi={};wi={calendar:"Calendrier",day:"jour",dayPeriod:"cadran",endDate:"Date de fin",era:"ère",hour:"heure",minute:"minute",month:"mois",second:"seconde",selectedDateDescription:e=>`Date sélectionnée : ${e.date}`,selectedRangeDescription:e=>`Plage sélectionnée : ${e.startDate} au ${e.endDate}`,selectedTimeDescription:e=>`Heure choisie : ${e.time}`,startDate:"Date de début",timeZoneName:"fuseau horaire",weekday:"jour de la semaine",year:"année"};var Fi={};Fi={calendar:"לוח שנה",day:"יום",dayPeriod:"לפנה״צ/אחה״צ",endDate:"תאריך סיום",era:"תקופה",hour:"שעה",minute:"דקה",month:"חודש",second:"שנייה",selectedDateDescription:e=>`תאריך נבחר: ${e.date}`,selectedRangeDescription:e=>`טווח נבחר: ${e.startDate} עד ${e.endDate}`,selectedTimeDescription:e=>`זמן נבחר: ${e.time}`,startDate:"תאריך התחלה",timeZoneName:"אזור זמן",weekday:"יום בשבוע",year:"שנה"};var Si={};Si={calendar:"Kalendar",day:"dan",dayPeriod:"AM/PM",endDate:"Datum završetka",era:"era",hour:"sat",minute:"minuta",month:"mjesec",second:"sekunda",selectedDateDescription:e=>`Odabrani datum: ${e.date}`,selectedRangeDescription:e=>`Odabrani raspon: ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Odabrano vrijeme: ${e.time}`,startDate:"Datum početka",timeZoneName:"vremenska zona",weekday:"dan u tjednu",year:"godina"};var Ai={};Ai={calendar:"Naptár",day:"nap",dayPeriod:"napszak",endDate:"Befejező dátum",era:"éra",hour:"óra",minute:"perc",month:"hónap",second:"másodperc",selectedDateDescription:e=>`Kijelölt dátum: ${e.date}`,selectedRangeDescription:e=>`Kijelölt tartomány: ${e.startDate}–${e.endDate}`,selectedTimeDescription:e=>`Kijelölt idő: ${e.time}`,startDate:"Kezdő dátum",timeZoneName:"időzóna",weekday:"hét napja",year:"év"};var Ri={};Ri={calendar:"Calendario",day:"giorno",dayPeriod:"AM/PM",endDate:"Data finale",era:"era",hour:"ora",minute:"minuto",month:"mese",second:"secondo",selectedDateDescription:e=>`Data selezionata: ${e.date}`,selectedRangeDescription:e=>`Intervallo selezionato: da ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Ora selezionata: ${e.time}`,startDate:"Data iniziale",timeZoneName:"fuso orario",weekday:"giorno della settimana",year:"anno"};var ki={};ki={calendar:"カレンダー",day:"日",dayPeriod:"午前/午後",endDate:"終了日",era:"時代",hour:"時",minute:"分",month:"月",second:"秒",selectedDateDescription:e=>`選択した日付 : ${e.date}`,selectedRangeDescription:e=>`選択範囲 : ${e.startDate} から ${e.endDate}`,selectedTimeDescription:e=>`選択した時間 : ${e.time}`,startDate:"開始日",timeZoneName:"タイムゾーン",weekday:"曜日",year:"年"};var Ti={};Ti={calendar:"달력",day:"일",dayPeriod:"오전/오후",endDate:"종료일",era:"연호",hour:"시",minute:"분",month:"월",second:"초",selectedDateDescription:e=>`선택 일자: ${e.date}`,selectedRangeDescription:e=>`선택 범위: ${e.startDate} ~ ${e.endDate}`,selectedTimeDescription:e=>`선택 시간: ${e.time}`,startDate:"시작일",timeZoneName:"시간대",weekday:"요일",year:"년"};var Mi={};Mi={calendar:"Kalendorius",day:"diena",dayPeriod:"iki pietų / po pietų",endDate:"Pabaigos data",era:"era",hour:"valanda",minute:"minutė",month:"mėnuo",second:"sekundė",selectedDateDescription:e=>`Pasirinkta data: ${e.date}`,selectedRangeDescription:e=>`Pasirinktas intervalas: nuo ${e.startDate} iki ${e.endDate}`,selectedTimeDescription:e=>`Pasirinktas laikas: ${e.time}`,startDate:"Pradžios data",timeZoneName:"laiko juosta",weekday:"savaitės diena",year:"metai"};var Vi={};Vi={calendar:"Kalendārs",day:"diena",dayPeriod:"priekšpusdienā/pēcpusdienā",endDate:"Beigu datums",era:"ēra",hour:"stundas",minute:"minūtes",month:"mēnesis",second:"sekundes",selectedDateDescription:e=>`Atlasītais datums: ${e.date}`,selectedRangeDescription:e=>`Atlasītais diapazons: no ${e.startDate} līdz ${e.endDate}`,selectedTimeDescription:e=>`Atlasītais laiks: ${e.time}`,startDate:"Sākuma datums",timeZoneName:"laika josla",weekday:"nedēļas diena",year:"gads"};var Ii={};Ii={calendar:"Kalender",day:"dag",dayPeriod:"a.m./p.m.",endDate:"Sluttdato",era:"tidsalder",hour:"time",minute:"minutt",month:"måned",second:"sekund",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt område: ${e.startDate} til ${e.endDate}`,selectedTimeDescription:e=>`Valgt tid: ${e.time}`,startDate:"Startdato",timeZoneName:"tidssone",weekday:"ukedag",year:"år"};var Oi={};Oi={calendar:"Kalender",day:"dag",dayPeriod:"a.m./p.m.",endDate:"Einddatum",era:"tijdperk",hour:"uur",minute:"minuut",month:"maand",second:"seconde",selectedDateDescription:e=>`Geselecteerde datum: ${e.date}`,selectedRangeDescription:e=>`Geselecteerd bereik: ${e.startDate} tot ${e.endDate}`,selectedTimeDescription:e=>`Geselecteerde tijd: ${e.time}`,startDate:"Startdatum",timeZoneName:"tijdzone",weekday:"dag van de week",year:"jaar"};var Li={};Li={calendar:"Kalendarz",day:"dzień",dayPeriod:"rano / po południu / wieczorem",endDate:"Data końcowa",era:"era",hour:"godzina",minute:"minuta",month:"miesiąc",second:"sekunda",selectedDateDescription:e=>`Wybrana data: ${e.date}`,selectedRangeDescription:e=>`Wybrany zakres: ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Wybrany czas: ${e.time}`,startDate:"Data początkowa",timeZoneName:"strefa czasowa",weekday:"dzień tygodnia",year:"rok"};var Ni={};Ni={calendar:"Calendário",day:"dia",dayPeriod:"AM/PM",endDate:"Data final",era:"era",hour:"hora",minute:"minuto",month:"mês",second:"segundo",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Hora selecionada: ${e.time}`,startDate:"Data inicial",timeZoneName:"fuso horário",weekday:"dia da semana",year:"ano"};var ji={};ji={calendar:"Calendário",day:"dia",dayPeriod:"am/pm",endDate:"Data de Término",era:"era",hour:"hora",minute:"minuto",month:"mês",second:"segundo",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Hora selecionada: ${e.time}`,startDate:"Data de Início",timeZoneName:"fuso horário",weekday:"dia da semana",year:"ano"};var Ui={};Ui={calendar:"Calendar",day:"zi",dayPeriod:"a.m/p.m.",endDate:"Dată final",era:"eră",hour:"oră",minute:"minut",month:"lună",second:"secundă",selectedDateDescription:e=>`Dată selectată: ${e.date}`,selectedRangeDescription:e=>`Interval selectat: de la ${e.startDate} până la ${e.endDate}`,selectedTimeDescription:e=>`Ora selectată: ${e.time}`,startDate:"Dată început",timeZoneName:"fus orar",weekday:"ziua din săptămână",year:"an"};var Ki={};Ki={calendar:"Календарь",day:"день",dayPeriod:"AM/PM",endDate:"Дата окончания",era:"эра",hour:"час",minute:"минута",month:"месяц",second:"секунда",selectedDateDescription:e=>`Выбранная дата: ${e.date}`,selectedRangeDescription:e=>`Выбранный диапазон: с ${e.startDate} по ${e.endDate}`,selectedTimeDescription:e=>`Выбранное время: ${e.time}`,startDate:"Дата начала",timeZoneName:"часовой пояс",weekday:"день недели",year:"год"};var _i={};_i={calendar:"Kalendár",day:"deň",dayPeriod:"AM/PM",endDate:"Dátum ukončenia",era:"letopočet",hour:"hodina",minute:"minúta",month:"mesiac",second:"sekunda",selectedDateDescription:e=>`Vybratý dátum: ${e.date}`,selectedRangeDescription:e=>`Vybratý rozsah: od ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Vybratý čas: ${e.time}`,startDate:"Dátum začatia",timeZoneName:"časové pásmo",weekday:"deň týždňa",year:"rok"};var zi={};zi={calendar:"Koledar",day:"dan",dayPeriod:"dop/pop",endDate:"Datum konca",era:"doba",hour:"ura",minute:"minuta",month:"mesec",second:"sekunda",selectedDateDescription:e=>`Izbrani datum: ${e.date}`,selectedRangeDescription:e=>`Izbrano območje: ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Izbrani čas: ${e.time}`,startDate:"Datum začetka",timeZoneName:"časovni pas",weekday:"dan v tednu",year:"leto"};var Zi={};Zi={calendar:"Kalendar",day:"дан",dayPeriod:"пре подне/по подне",endDate:"Datum završetka",era:"ера",hour:"сат",minute:"минут",month:"месец",second:"секунд",selectedDateDescription:e=>`Izabrani datum: ${e.date}`,selectedRangeDescription:e=>`Izabrani opseg: od ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Izabrano vreme: ${e.time}`,startDate:"Datum početka",timeZoneName:"временска зона",weekday:"дан у недељи",year:"година"};var Wi={};Wi={calendar:"Kalender",day:"dag",dayPeriod:"fm/em",endDate:"Slutdatum",era:"era",hour:"timme",minute:"minut",month:"månad",second:"sekund",selectedDateDescription:e=>`Valt datum: ${e.date}`,selectedRangeDescription:e=>`Valt intervall: ${e.startDate} till ${e.endDate}`,selectedTimeDescription:e=>`Vald tid: ${e.time}`,startDate:"Startdatum",timeZoneName:"tidszon",weekday:"veckodag",year:"år"};var Yi={};Yi={calendar:"Takvim",day:"gün",dayPeriod:"ÖÖ/ÖS",endDate:"Bitiş Tarihi",era:"çağ",hour:"saat",minute:"dakika",month:"ay",second:"saniye",selectedDateDescription:e=>`Seçilen Tarih: ${e.date}`,selectedRangeDescription:e=>`Seçilen Aralık: ${e.startDate} - ${e.endDate}`,selectedTimeDescription:e=>`Seçilen Zaman: ${e.time}`,startDate:"Başlangıç Tarihi",timeZoneName:"saat dilimi",weekday:"haftanın günü",year:"yıl"};var Hi={};Hi={calendar:"Календар",day:"день",dayPeriod:"дп/пп",endDate:"Дата завершення",era:"ера",hour:"година",minute:"хвилина",month:"місяць",second:"секунда",selectedDateDescription:e=>`Вибрана дата: ${e.date}`,selectedRangeDescription:e=>`Вибраний діапазон: ${e.startDate} — ${e.endDate}`,selectedTimeDescription:e=>`Вибраний час: ${e.time}`,startDate:"Дата початку",timeZoneName:"часовий пояс",weekday:"день тижня",year:"рік"};var Gi={};Gi={calendar:"日历",day:"日",dayPeriod:"上午/下午",endDate:"结束日期",era:"纪元",hour:"小时",minute:"分钟",month:"月",second:"秒",selectedDateDescription:e=>`选定的日期：${e.date}`,selectedRangeDescription:e=>`选定的范围：${e.startDate} 至 ${e.endDate}`,selectedTimeDescription:e=>`选定的时间：${e.time}`,startDate:"开始日期",timeZoneName:"时区",weekday:"工作日",year:"年"};var Ji={};Ji={calendar:"日曆",day:"日",dayPeriod:"上午/下午",endDate:"結束日期",era:"纪元",hour:"小时",minute:"分钟",month:"月",second:"秒",selectedDateDescription:e=>`選定的日期：${e.date}`,selectedRangeDescription:e=>`選定的範圍：${e.startDate} 至 ${e.endDate}`,selectedTimeDescription:e=>`選定的時間：${e.time}`,startDate:"開始日期",timeZoneName:"时区",weekday:"工作日",year:"年"};var Kt={};Kt={"ar-AE":pi,"bg-BG":bi,"cs-CZ":yi,"da-DK":gi,"de-DE":vi,"el-GR":xi,"en-US":Ei,"es-ES":Ci,"et-EE":Bi,"fi-FI":Pi,"fr-FR":wi,"he-IL":Fi,"hr-HR":Si,"hu-HU":Ai,"it-IT":Ri,"ja-JP":ki,"ko-KR":Ti,"lt-LT":Mi,"lv-LV":Vi,"nb-NO":Ii,"nl-NL":Oi,"pl-PL":Li,"pt-BR":Ni,"pt-PT":ji,"ro-RO":Ui,"ru-RU":Ki,"sk-SK":_i,"sl-SI":zi,"sr-SP":Zi,"sv-SE":Wi,"tr-TR":Yi,"uk-UA":Hi,"zh-CN":Gi,"zh-TW":Ji};function qi(e,t,a){let{direction:r}=q(),u=o.useMemo(()=>Za(t),[t]),n=s=>{if(s.currentTarget.contains(s.target)&&(s.altKey&&(s.key==="ArrowDown"||s.key==="ArrowUp")&&"setOpen"in e&&(s.preventDefault(),s.stopPropagation(),e.setOpen(!0)),!a))switch(s.key){case"ArrowLeft":s.preventDefault(),s.stopPropagation(),r==="rtl"?u.focusNext():u.focusPrevious();break;case"ArrowRight":s.preventDefault(),s.stopPropagation(),r==="rtl"?u.focusPrevious():u.focusNext();break}},i=()=>{var s;if(!t.current)return;let c=(s=window.event)===null||s===void 0?void 0:s.target,D=Je(t.current,{tabbable:!0});if(c&&(D.currentNode=c,c=D.previousNode()),!c){let $;do $=D.lastChild(),$&&(c=$);while($)}for(;c!=null&&c.hasAttribute("data-placeholder");){let $=D.previousNode();if($&&$.hasAttribute("data-placeholder"))c=$;else break}c&&c.focus()},{pressProps:l}=Ka({preventFocusOnPress:!0,allowTextSelectionOnPress:!0,onPressStart(s){s.pointerType==="mouse"&&i()},onPress(s){s.pointerType!=="mouse"&&i()}});return H(l,{onKeyDown:n})}function Cd(e){return e&&e.__esModule?e.default:e}const Qi=new WeakMap,Ct="__role_"+Date.now(),Bd="__focusManager_"+Date.now();function Xi(e,t,a){var r;let{isInvalid:u,validationErrors:n,validationDetails:i}=t.displayValidation,{labelProps:l,fieldProps:s,descriptionProps:c,errorMessageProps:D}=Vn({...e,labelElementType:"span",isInvalid:u,errorMessage:e.errorMessage||n}),$=o.useRef(null),{focusWithinProps:b}=ju({...e,onFocusWithin(g){var f;$.current=t.value,(f=e.onFocus)===null||f===void 0||f.call(e,g)},onBlurWithin:g=>{var f;t.confirmPlaceholder(),t.value!==$.current&&t.commitValidation(),(f=e.onBlur)===null||f===void 0||f.call(e,g)},onFocusWithinChange:e.onFocusChange}),E=Se(Cd(Kt),"@react-aria/datepicker"),p=t.maxGranularity==="hour"?"selectedTimeDescription":"selectedDateDescription",x=t.maxGranularity==="hour"?"time":"date",w=t.value?E.format(p,{[x]:t.formatValue({month:"long"})}):"",R=Ua(w),k=e[Ct]==="presentation"?s["aria-describedby"]:[R["aria-describedby"],s["aria-describedby"]].filter(Boolean).join(" ")||void 0,C=e[Bd],y=o.useMemo(()=>C||Za(a),[C,a]),A=qi(t,a,e[Ct]==="presentation");Qi.set(t,{ariaLabel:e["aria-label"],ariaLabelledBy:[l.id,e["aria-labelledby"]].filter(Boolean).join(" ")||void 0,ariaDescribedBy:k,focusManager:y});let T=o.useRef(e.autoFocus),I;e[Ct]==="presentation"?I={role:"presentation"}:I=H(s,{role:"group","aria-disabled":e.isDisabled||void 0,"aria-describedby":k}),o.useEffect(()=>{T.current&&y.focusFirst(),T.current=!1},[y]),ys(e.inputRef,t.value,t.setValue),pd({...e,focus(){y.focusFirst()}},t,e.inputRef);let d={type:"hidden",name:e.name,value:((r=t.value)===null||r===void 0?void 0:r.toString())||"",disabled:e.isDisabled};e.validationBehavior==="native"&&(d.type="text",d.hidden=!0,d.required=e.isRequired,d.onChange=()=>{});let S=Lt(e);return{labelProps:{...l,onClick:()=>{y.focusFirst()}},fieldProps:H(S,I,A,b,{onKeyDown(g){e.onKeyDown&&e.onKeyDown(g)},onKeyUp(g){e.onKeyUp&&e.onKeyUp(g)}}),inputProps:d,descriptionProps:c,errorMessageProps:D,isInvalid:u,validationErrors:n,validationDetails:i}}function Pd(e,t,a){var r;let u=Xi(e,t,a);return u.inputProps.value=((r=t.timeValue)===null||r===void 0?void 0:r.toString())||"",u}function wd(e){return e&&e.__esModule?e.default:e}function Fd(e,t,a){let r=be(),u=be(),n=be(),i=Se(wd(Kt),"@react-aria/datepicker"),{isInvalid:l,validationErrors:s,validationDetails:c}=t.displayValidation,{labelProps:D,fieldProps:$,descriptionProps:b,errorMessageProps:E}=Vn({...e,labelElementType:"span",isInvalid:l,errorMessage:e.errorMessage||s}),p=qi(t,a),x=$["aria-labelledby"]||$.id,{locale:w}=q(),R=t.formatValue(w,{month:"long"}),k=R?i.format("selectedDateDescription",{date:R}):"",C=Ua(k),y=[C["aria-describedby"],$["aria-describedby"]].filter(Boolean).join(" ")||void 0,A=Lt(e),T=o.useMemo(()=>Za(a),[a]),{focusWithinProps:I}=ju({...e,isDisabled:t.isOpen,onBlurWithin:e.onBlur,onFocusWithin:e.onFocus,onFocusWithinChange:e.onFocusChange});return{groupProps:H(A,p,$,C,I,{role:"group","aria-disabled":e.isDisabled||null,"aria-labelledby":x,"aria-describedby":y,onKeyDown(d){t.isOpen||e.onKeyDown&&e.onKeyDown(d)},onKeyUp(d){t.isOpen||e.onKeyUp&&e.onKeyUp(d)}}),labelProps:{...D,onClick:()=>{T.focusFirst()}},fieldProps:{...$,id:n,[Ct]:"presentation","aria-describedby":y,value:t.value,onChange:t.setValue,placeholderValue:e.placeholderValue,hideTimeZone:e.hideTimeZone,hourCycle:e.hourCycle,shouldForceLeadingZeros:e.shouldForceLeadingZeros,granularity:e.granularity,isDisabled:e.isDisabled,isReadOnly:e.isReadOnly,isRequired:e.isRequired,validationBehavior:e.validationBehavior,[Pa]:t,autoFocus:e.autoFocus,name:e.name},descriptionProps:b,errorMessageProps:E,buttonProps:{...C,id:r,"aria-haspopup":"dialog","aria-label":i.format("calendar"),"aria-labelledby":`${r} ${x}`,"aria-describedby":y,"aria-expanded":t.isOpen,isDisabled:e.isDisabled||e.isReadOnly,onPress:()=>t.setOpen(!0)},dialogProps:{id:u,"aria-labelledby":`${r} ${x}`},calendarProps:{autoFocus:!0,value:t.dateValue,onChange:t.setDateValue,minValue:e.minValue,maxValue:e.maxValue,isDisabled:e.isDisabled,isReadOnly:e.isReadOnly,isDateUnavailable:e.isDateUnavailable,defaultFocusedValue:t.dateValue?void 0:e.placeholderValue,isInvalid:t.isInvalid,errorMessage:typeof e.errorMessage=="function"?e.errorMessage(t.displayValidation):e.errorMessage||t.displayValidation.validationErrors.join(" ")},isInvalid:l,validationErrors:s,validationDetails:c}}function Sd(e){return e&&e.__esModule?e.default:e}function Ad(){let{locale:e}=q(),t=Zu(Sd(Kt),"@react-aria/datepicker");return o.useMemo(()=>{try{return new Intl.DisplayNames(e,{type:"dateTimeField"})}catch{return new Rd(e,t)}},[e,t])}class Rd{of(t){return this.dictionary.getStringForLocale(t,this.locale)}constructor(t,a){this.locale=t,this.dictionary=a}}function kd(e,t,a){let r=o.useRef(""),{locale:u}=q(),n=Ad(),{ariaLabel:i,ariaLabelledBy:l,ariaDescribedBy:s,focusManager:c}=Qi.get(t),D=e.isPlaceholder?"":e.text,$=o.useMemo(()=>t.dateFormatter.resolvedOptions(),[t.dateFormatter]),b=re({month:"long",timeZone:$.timeZone}),E=re({hour:"numeric",hour12:$.hour12,timeZone:$.timeZone});if(e.type==="month"&&!e.isPlaceholder){let v=b.format(t.dateValue);D=v!==D?`${D} – ${v}`:v}else e.type==="hour"&&!e.isPlaceholder&&(D=E.format(t.dateValue));let{spinButtonProps:p}=Ed({value:e.value,textValue:D,minValue:e.minValue,maxValue:e.maxValue,isDisabled:t.isDisabled,isReadOnly:t.isReadOnly||!e.isEditable,isRequired:t.isRequired,onIncrement:()=>{r.current="",t.increment(e.type)},onDecrement:()=>{r.current="",t.decrement(e.type)},onIncrementPage:()=>{r.current="",t.incrementPage(e.type)},onDecrementPage:()=>{r.current="",t.decrementPage(e.type)},onIncrementToMax:()=>{r.current="",e.maxValue!==void 0&&t.setSegment(e.type,e.maxValue)},onDecrementToMin:()=>{r.current="",e.minValue!==void 0&&t.setSegment(e.type,e.minValue)}}),x=o.useMemo(()=>new Yu(u,{maximumFractionDigits:0}),[u]),w=()=>{if(e.text===e.placeholder&&c.focusPrevious(),x.isValidPartialNumber(e.text)&&!t.isReadOnly&&!e.isPlaceholder){let v=e.text.slice(0,-1),M=x.parse(v);v=M===0?"":v,v.length===0||M===0?t.clearSegment(e.type):t.setSegment(e.type,M),r.current=v}else e.type==="dayPeriod"&&t.clearSegment(e.type)},R=v=>{if(v.key==="a"&&(Ue()?v.metaKey:v.ctrlKey)&&v.preventDefault(),!(v.ctrlKey||v.metaKey||v.shiftKey||v.altKey))switch(v.key){case"Backspace":case"Delete":v.preventDefault(),v.stopPropagation(),w();break}},{startsWith:k}=ud({sensitivity:"base"}),C=re({hour:"numeric",hour12:!0}),y=o.useMemo(()=>{let v=new Date;return v.setHours(0),C.formatToParts(v).find(M=>M.type==="dayPeriod").value},[C]),A=o.useMemo(()=>{let v=new Date;return v.setHours(12),C.formatToParts(v).find(M=>M.type==="dayPeriod").value},[C]),T=re({year:"numeric",era:"narrow",timeZone:"UTC"}),I=o.useMemo(()=>{if(e.type!=="era")return[];let v=N(new K(1,1,1),t.calendar),M=t.calendar.getEras().map(B=>{let L=v.set({year:1,month:1,day:1,era:B}).toDate("UTC"),te=T.formatToParts(L).find(Ae=>Ae.type==="era").value;return{era:B,formatted:te}}),O=Td(M.map(B=>B.formatted));if(O)for(let B of M)B.formatted=B.formatted.slice(O);return M},[T,t.calendar,e.type]),d=v=>{if(t.isDisabled||t.isReadOnly)return;let M=r.current+v;switch(e.type){case"dayPeriod":if(k(y,v))t.setSegment("dayPeriod",0);else if(k(A,v))t.setSegment("dayPeriod",12);else break;c.focusNext();break;case"era":{let O=I.find(B=>k(B.formatted,v));O&&(t.setSegment("era",O.era),c.focusNext());break}case"day":case"hour":case"minute":case"second":case"month":case"year":{if(!x.isValidPartialNumber(M))return;let O=x.parse(M),B=O,L=e.minValue===0;if(e.type==="hour"&&t.dateFormatter.resolvedOptions().hour12){switch(t.dateFormatter.resolvedOptions().hourCycle){case"h11":O>11&&(B=x.parse(v));break;case"h12":L=!1,O>12&&(B=x.parse(v));break}e.value!==void 0&&e.value>=12&&O>1&&(O+=12)}else e.maxValue!==void 0&&O>e.maxValue&&(B=x.parse(v));if(isNaN(O))return;let J=B!==0||L;J&&t.setSegment(e.type,B),e.maxValue!==void 0&&(+(O+"0")>e.maxValue||M.length>=String(e.maxValue).length)?(r.current="",J&&c.focusNext()):r.current=M;break}}},S=()=>{r.current="",a.current&&Fu(a.current,{containingElement:wu(a.current)});let v=window.getSelection();v==null||v.collapse(a.current)},g=o.useRef(typeof document<"u"?document:null);Qt(g,"selectionchange",()=>{var v;let M=window.getSelection();M!=null&&M.anchorNode&&(!((v=a.current)===null||v===void 0)&&v.contains(M==null?void 0:M.anchorNode))&&M.collapse(a.current)});let f=o.useRef("");Qt(a,"beforeinput",v=>{if(a.current)switch(v.preventDefault(),v.inputType){case"deleteContentBackward":case"deleteContentForward":x.isValidPartialNumber(e.text)&&!t.isReadOnly&&w();break;case"insertCompositionText":f.current=a.current.textContent,a.current.textContent=a.current.textContent;break;default:v.data!=null&&d(v.data);break}}),Qt(a,"input",v=>{let{inputType:M,data:O}=v;switch(M){case"insertCompositionText":a.current&&(a.current.textContent=f.current),O!=null&&(k(y,O)||k(A,O))&&d(O);break}}),fe(()=>{let v=a.current;return()=>{document.activeElement===v&&(c.focusPrevious()||c.focusNext())}},[a,c]);let F=ja()||e.type==="timeZoneName"?{role:"textbox","aria-valuemax":null,"aria-valuemin":null,"aria-valuetext":null,"aria-valuenow":null}:{},m=o.useMemo(()=>t.segments.find(v=>v.isEditable),[t.segments]);e!==m&&!t.isInvalid&&(s=void 0);let h=be(),V=!t.isDisabled&&!t.isReadOnly&&e.isEditable,z=e.type==="literal"?"":n.of(e.type),j=jt({"aria-label":`${z}${i?`, ${i}`:""}${l?", ":""}`,"aria-labelledby":l});return e.type==="literal"?{segmentProps:{"aria-hidden":!0}}:{segmentProps:H(p,j,{id:h,...F,"aria-invalid":t.isInvalid?"true":void 0,"aria-describedby":s,"aria-readonly":t.isReadOnly||!e.isEditable?"true":void 0,"data-placeholder":e.isPlaceholder||void 0,contentEditable:V,suppressContentEditableWarning:V,spellCheck:V?"false":void 0,autoCorrect:V?"off":void 0,[parseInt(Mt.version,10)>=17?"enterKeyHint":"enterkeyhint"]:V?"next":void 0,inputMode:t.isDisabled||e.type==="dayPeriod"||e.type==="era"||!V?void 0:"numeric",tabIndex:t.isDisabled?void 0:0,onKeyDown:R,onFocus:S,style:{caretColor:"transparent"},onPointerDown(v){v.stopPropagation()},onMouseDown(v){v.stopPropagation()}})}}function Td(e){e.sort();let t=e[0],a=e[e.length-1];for(let r=0;r<t.length;r++)if(t[r]!==a[r])return r;return 0}function la(e,t,a){return t!=null&&e.compare(t)<0||a!=null&&e.compare(a)>0}function Vr(e,t,a,r,u){let n={};for(let l in t)n[l]=Math.floor(t[l]/2),n[l]>0&&t[l]%2===0&&n[l]--;let i=Ce(e,t,a).subtract(n);return nt(e,i,t,a,r,u)}function Ce(e,t,a,r,u){let n=e;return t.years?n=oo(e):t.months?n=lt(e):t.weeks&&(n=Xe(e,a)),nt(e,n,t,a,r,u)}function Fa(e,t,a,r,u){let n={...t};n.days?n.days--:n.weeks?n.weeks--:n.months?n.months--:n.years&&n.years--;let i=Ce(e,t,a).subtract(n);return nt(e,i,t,a,r,u)}function nt(e,t,a,r,u,n){if(u&&e.compare(u)>=0){let i=qr(t,Ce(ne(u),a,r));i&&(t=i)}if(n&&e.compare(n)<=0){let i=Jr(t,Fa(ne(n),a,r));i&&(t=i)}return t}function ve(e,t,a){if(t){let r=qr(e,ne(t));r&&(e=r)}if(a){let r=Jr(e,ne(a));r&&(e=r)}return e}function Md(e,t,a){if(!a)return e;for(;e.compare(t)>=0&&a(e);)e=e.subtract({days:1});return e.compare(t)>=0?e:null}function Vd(e){let t=o.useMemo(()=>new de(e.locale),[e.locale]),a=o.useMemo(()=>t.resolvedOptions(),[t]),{locale:r,createCalendar:u,visibleDuration:n={months:1},minValue:i,maxValue:l,selectionAlignment:s,isDateUnavailable:c,pageBehavior:D="visible",firstDayOfWeek:$}=e,b=o.useMemo(()=>u(a.calendar),[u,a.calendar]);var E;let[p,x]=Ke(e.value,(E=e.defaultValue)!==null&&E!==void 0?E:null,e.onChange),w=o.useMemo(()=>p?N(ne(p),b):null,[p,b]),R=o.useMemo(()=>p&&"timeZone"in p?p.timeZone:a.timeZone,[p,a.timeZone]),k=o.useMemo(()=>e.focusedValue?ve(N(ne(e.focusedValue),b),i,l):void 0,[e.focusedValue,b,i,l]),C=o.useMemo(()=>ve(e.defaultFocusedValue?N(ne(e.defaultFocusedValue),b):w||N(It(R),b),i,l),[e.defaultFocusedValue,w,R,b,i,l]),[y,A]=Ke(k,C,e.onFocusChange),[T,I]=o.useState(()=>{switch(s){case"start":return Ce(y,n,r,i,l);case"end":return Fa(y,n,r,i,l);case"center":default:return Vr(y,n,r,i,l)}}),[d,S]=o.useState(e.autoFocus||!1),g=o.useMemo(()=>{let B={...n};return B.days?B.days--:B.days=-1,T.add(B)},[T,n]),[f,F]=o.useState(b.identifier);if(b.identifier!==f){let B=N(y,b);I(Vr(B,n,r,i,l)),A(B),F(b.identifier)}la(y,i,l)?A(ve(y,i,l)):y.compare(T)<0?I(Fa(y,n,r,i,l)):y.compare(g)>0&&I(Ce(y,n,r,i,l));function m(B){B=ve(B,i,l),A(B)}function h(B){if(!e.isDisabled&&!e.isReadOnly){let L=B;if(L===null){x(null);return}if(L=ve(L,i,l),L=Md(L,T,c),!L)return;L=N(L,(p==null?void 0:p.calendar)||new G),p&&"hour"in p?x(p.set(L)):x(L)}}let V=o.useMemo(()=>w?c&&c(w)?!0:la(w,i,l):!1,[w,c,i,l]),z=e.isInvalid||e.validationState==="invalid"||V,j=z?"invalid":null,v=o.useMemo(()=>D==="visible"?n:oa(n),[D,n]);var M,O;return{isDisabled:(M=e.isDisabled)!==null&&M!==void 0?M:!1,isReadOnly:(O=e.isReadOnly)!==null&&O!==void 0?O:!1,value:w,setValue:h,visibleRange:{start:T,end:g},minValue:i,maxValue:l,focusedDate:y,timeZone:R,validationState:j,isValueInvalid:z,setFocusedDate(B){m(B),S(!0)},focusNextDay(){m(y.add({days:1}))},focusPreviousDay(){m(y.subtract({days:1}))},focusNextRow(){n.days?this.focusNextPage():(n.weeks||n.months||n.years)&&m(y.add({weeks:1}))},focusPreviousRow(){n.days?this.focusPreviousPage():(n.weeks||n.months||n.years)&&m(y.subtract({weeks:1}))},focusNextPage(){let B=T.add(v);A(ve(y.add(v),i,l)),I(Ce(nt(y,B,v,r,i,l),v,r))},focusPreviousPage(){let B=T.subtract(v);A(ve(y.subtract(v),i,l)),I(Ce(nt(y,B,v,r,i,l),v,r))},focusSectionStart(){n.days?m(T):n.weeks?m(Xe(y,r)):(n.months||n.years)&&m(lt(y))},focusSectionEnd(){n.days?m(g):n.weeks?m(fo(y,r)):(n.months||n.years)&&m(sa(y))},focusNextSection(B){if(!B&&!n.days){m(y.add(oa(n)));return}n.days?this.focusNextPage():n.weeks?m(y.add({months:1})):(n.months||n.years)&&m(y.add({years:1}))},focusPreviousSection(B){if(!B&&!n.days){m(y.subtract(oa(n)));return}n.days?this.focusPreviousPage():n.weeks?m(y.subtract({months:1})):(n.months||n.years)&&m(y.subtract({years:1}))},selectFocusedDate(){c&&c(y)||h(y)},selectDate(B){h(B)},isFocused:d,setFocused:S,isInvalid(B){return la(B,i,l)},isSelected(B){return w!=null&&_(B,w)&&!this.isCellDisabled(B)&&!this.isCellUnavailable(B)},isCellFocused(B){return d&&y&&_(B,y)},isCellDisabled(B){return e.isDisabled||B.compare(T)<0||B.compare(g)>0||this.isInvalid(B)},isCellUnavailable(B){return e.isDateUnavailable?e.isDateUnavailable(B):!1},isPreviousVisibleRangeInvalid(){let B=T.subtract({days:1});return _(B,T)||this.isInvalid(B)},isNextVisibleRangeInvalid(){let B=g.add({days:1});return _(B,g)||this.isInvalid(B)},getDatesInWeek(B,L=T){let J=L.add({weeks:B}),te=[];J=Xe(J,r,$);let Ae=Aa(J,r,$);for(let ge=0;ge<Ae;ge++)te.push(null);for(;te.length<7;){te.push(J);let ge=J.add({days:1});if(_(J,ge))break;J=ge}for(;te.length<7;)te.push(null);return te}}}function oa(e){let t={...e};for(let a in e)t[a]=1;return t}function Id(e){let[t,a]=Ke(e.isOpen,e.defaultOpen||!1,e.onOpenChange);const r=o.useCallback(()=>{a(!0)},[a]),u=o.useCallback(()=>{a(!1)},[a]),n=o.useCallback(()=>{a(!t)},[a,t]);return{isOpen:t,setOpen:a,open:r,close:u,toggle:n}}var el={};el={rangeOverflow:e=>`يجب أن تكون القيمة ${e.maxValue} أو قبل ذلك.`,rangeReversed:"تاريخ البدء يجب أن يكون قبل تاريخ الانتهاء.",rangeUnderflow:e=>`يجب أن تكون القيمة ${e.minValue} أو بعد ذلك.`,unavailableDate:"البيانات المحددة غير متاحة."};var tl={};tl={rangeOverflow:e=>`Стойността трябва да е ${e.maxValue} или по-ранна.`,rangeReversed:"Началната дата трябва да е преди крайната.",rangeUnderflow:e=>`Стойността трябва да е ${e.minValue} или по-късно.`,unavailableDate:"Избраната дата не е налична."};var al={};al={rangeOverflow:e=>`Hodnota musí být ${e.maxValue} nebo dřívější.`,rangeReversed:"Datum zahájení musí předcházet datu ukončení.",rangeUnderflow:e=>`Hodnota musí být ${e.minValue} nebo pozdější.`,unavailableDate:"Vybrané datum není k dispozici."};var rl={};rl={rangeOverflow:e=>`Værdien skal være ${e.maxValue} eller tidligere.`,rangeReversed:"Startdatoen skal være før slutdatoen.",rangeUnderflow:e=>`Værdien skal være ${e.minValue} eller nyere.`,unavailableDate:"Den valgte dato er ikke tilgængelig."};var ul={};ul={rangeOverflow:e=>`Der Wert muss ${e.maxValue} oder früher sein.`,rangeReversed:"Das Anfangsdatum muss vor dem Enddatum liegen.",rangeUnderflow:e=>`Der Wert muss ${e.minValue} oder später sein.`,unavailableDate:"Das ausgewählte Datum ist nicht verfügbar."};var nl={};nl={rangeOverflow:e=>`Η τιμή πρέπει να είναι ${e.maxValue} ή παλαιότερη.`,rangeReversed:"Η ημερομηνία έναρξης πρέπει να είναι πριν από την ημερομηνία λήξης.",rangeUnderflow:e=>`Η τιμή πρέπει να είναι ${e.minValue} ή μεταγενέστερη.`,unavailableDate:"Η επιλεγμένη ημερομηνία δεν είναι διαθέσιμη."};var il={};il={rangeUnderflow:e=>`Value must be ${e.minValue} or later.`,rangeOverflow:e=>`Value must be ${e.maxValue} or earlier.`,rangeReversed:"Start date must be before end date.",unavailableDate:"Selected date unavailable."};var ll={};ll={rangeOverflow:e=>`El valor debe ser ${e.maxValue} o anterior.`,rangeReversed:"La fecha de inicio debe ser anterior a la fecha de finalización.",rangeUnderflow:e=>`El valor debe ser ${e.minValue} o posterior.`,unavailableDate:"Fecha seleccionada no disponible."};var ol={};ol={rangeOverflow:e=>`Väärtus peab olema ${e.maxValue} või varasem.`,rangeReversed:"Alguskuupäev peab olema enne lõppkuupäeva.",rangeUnderflow:e=>`Väärtus peab olema ${e.minValue} või hilisem.`,unavailableDate:"Valitud kuupäev pole saadaval."};var sl={};sl={rangeOverflow:e=>`Arvon on oltava ${e.maxValue} tai sitä aikaisempi.`,rangeReversed:"Aloituspäivän on oltava ennen lopetuspäivää.",rangeUnderflow:e=>`Arvon on oltava ${e.minValue} tai sitä myöhäisempi.`,unavailableDate:"Valittu päivämäärä ei ole käytettävissä."};var dl={};dl={rangeOverflow:e=>`La valeur doit être ${e.maxValue} ou antérieure.`,rangeReversed:"La date de début doit être antérieure à la date de fin.",rangeUnderflow:e=>`La valeur doit être ${e.minValue} ou ultérieure.`,unavailableDate:"La date sélectionnée n’est pas disponible."};var cl={};cl={rangeOverflow:e=>`הערך חייב להיות ${e.maxValue} או מוקדם יותר.`,rangeReversed:"תאריך ההתחלה חייב להיות לפני תאריך הסיום.",rangeUnderflow:e=>`הערך חייב להיות ${e.minValue} או מאוחר יותר.`,unavailableDate:"התאריך הנבחר אינו זמין."};var fl={};fl={rangeOverflow:e=>`Vrijednost mora biti ${e.maxValue} ili ranije.`,rangeReversed:"Datum početka mora biti prije datuma završetka.",rangeUnderflow:e=>`Vrijednost mora biti ${e.minValue} ili kasnije.`,unavailableDate:"Odabrani datum nije dostupan."};var ml={};ml={rangeOverflow:e=>`Az értéknek ${e.maxValue} vagy korábbinak kell lennie.`,rangeReversed:"A kezdő dátumnak a befejező dátumnál korábbinak kell lennie.",rangeUnderflow:e=>`Az értéknek ${e.minValue} vagy későbbinek kell lennie.`,unavailableDate:"A kiválasztott dátum nem érhető el."};var $l={};$l={rangeOverflow:e=>`Il valore deve essere ${e.maxValue} o precedente.`,rangeReversed:"La data di inizio deve essere antecedente alla data di fine.",rangeUnderflow:e=>`Il valore deve essere ${e.minValue} o successivo.`,unavailableDate:"Data selezionata non disponibile."};var Dl={};Dl={rangeOverflow:e=>`値は ${e.maxValue} 以下にする必要があります。`,rangeReversed:"開始日は終了日より前にする必要があります。",rangeUnderflow:e=>`値は ${e.minValue} 以上にする必要があります。`,unavailableDate:"選択した日付は使用できません。"};var hl={};hl={rangeOverflow:e=>`값은 ${e.maxValue} 이전이어야 합니다.`,rangeReversed:"시작일은 종료일 이전이어야 합니다.",rangeUnderflow:e=>`값은 ${e.minValue} 이상이어야 합니다.`,unavailableDate:"선택한 날짜를 사용할 수 없습니다."};var pl={};pl={rangeOverflow:e=>`Reikšmė turi būti ${e.maxValue} arba ankstesnė.`,rangeReversed:"Pradžios data turi būti ankstesnė nei pabaigos data.",rangeUnderflow:e=>`Reikšmė turi būti ${e.minValue} arba naujesnė.`,unavailableDate:"Pasirinkta data nepasiekiama."};var bl={};bl={rangeOverflow:e=>`Vērtībai ir jābūt ${e.maxValue} vai agrākai.`,rangeReversed:"Sākuma datumam ir jābūt pirms beigu datuma.",rangeUnderflow:e=>`Vērtībai ir jābūt ${e.minValue} vai vēlākai.`,unavailableDate:"Atlasītais datums nav pieejams."};var yl={};yl={rangeOverflow:e=>`Verdien må være ${e.maxValue} eller tidligere.`,rangeReversed:"Startdatoen må være før sluttdatoen.",rangeUnderflow:e=>`Verdien må være ${e.minValue} eller senere.`,unavailableDate:"Valgt dato utilgjengelig."};var gl={};gl={rangeOverflow:e=>`Waarde moet ${e.maxValue} of eerder zijn.`,rangeReversed:"De startdatum moet voor de einddatum liggen.",rangeUnderflow:e=>`Waarde moet ${e.minValue} of later zijn.`,unavailableDate:"Geselecteerde datum niet beschikbaar."};var vl={};vl={rangeOverflow:e=>`Wartość musi mieć wartość ${e.maxValue} lub wcześniejszą.`,rangeReversed:"Data rozpoczęcia musi być wcześniejsza niż data zakończenia.",rangeUnderflow:e=>`Wartość musi mieć wartość ${e.minValue} lub późniejszą.`,unavailableDate:"Wybrana data jest niedostępna."};var xl={};xl={rangeOverflow:e=>`O valor deve ser ${e.maxValue} ou anterior.`,rangeReversed:"A data inicial deve ser anterior à data final.",rangeUnderflow:e=>`O valor deve ser ${e.minValue} ou posterior.`,unavailableDate:"Data selecionada indisponível."};var El={};El={rangeOverflow:e=>`O valor tem de ser ${e.maxValue} ou anterior.`,rangeReversed:"A data de início deve ser anterior à data de fim.",rangeUnderflow:e=>`O valor tem de ser ${e.minValue} ou posterior.`,unavailableDate:"Data selecionada indisponível."};var Cl={};Cl={rangeOverflow:e=>`Valoarea trebuie să fie ${e.maxValue} sau anterioară.`,rangeReversed:"Data de început trebuie să fie anterioară datei de sfârșit.",rangeUnderflow:e=>`Valoarea trebuie să fie ${e.minValue} sau ulterioară.`,unavailableDate:"Data selectată nu este disponibilă."};var Bl={};Bl={rangeOverflow:e=>`Значение должно быть не позже ${e.maxValue}.`,rangeReversed:"Дата начала должна предшествовать дате окончания.",rangeUnderflow:e=>`Значение должно быть не раньше ${e.minValue}.`,unavailableDate:"Выбранная дата недоступна."};var Pl={};Pl={rangeOverflow:e=>`Hodnota musí byť ${e.maxValue} alebo skoršia.`,rangeReversed:"Dátum začiatku musí byť skorší ako dátum konca.",rangeUnderflow:e=>`Hodnota musí byť ${e.minValue} alebo neskoršia.`,unavailableDate:"Vybratý dátum je nedostupný."};var wl={};wl={rangeOverflow:e=>`Vrednost mora biti ${e.maxValue} ali starejša.`,rangeReversed:"Začetni datum mora biti pred končnim datumom.",rangeUnderflow:e=>`Vrednost mora biti ${e.minValue} ali novejša.`,unavailableDate:"Izbrani datum ni na voljo."};var Fl={};Fl={rangeOverflow:e=>`Vrednost mora da bude ${e.maxValue} ili starija.`,rangeReversed:"Datum početka mora biti pre datuma završetka.",rangeUnderflow:e=>`Vrednost mora da bude ${e.minValue} ili novija.`,unavailableDate:"Izabrani datum nije dostupan."};var Sl={};Sl={rangeOverflow:e=>`Värdet måste vara ${e.maxValue} eller tidigare.`,rangeReversed:"Startdatumet måste vara före slutdatumet.",rangeUnderflow:e=>`Värdet måste vara ${e.minValue} eller senare.`,unavailableDate:"Det valda datumet är inte tillgängligt."};var Al={};Al={rangeOverflow:e=>`Değer, ${e.maxValue} veya öncesi olmalıdır.`,rangeReversed:"Başlangıç tarihi bitiş tarihinden önce olmalıdır.",rangeUnderflow:e=>`Değer, ${e.minValue} veya sonrası olmalıdır.`,unavailableDate:"Seçilen tarih kullanılamıyor."};var Rl={};Rl={rangeOverflow:e=>`Значення має бути не пізніше ${e.maxValue}.`,rangeReversed:"Дата початку має передувати даті завершення.",rangeUnderflow:e=>`Значення має бути не раніше ${e.minValue}.`,unavailableDate:"Вибрана дата недоступна."};var kl={};kl={rangeOverflow:e=>`值必须是 ${e.maxValue} 或更早日期。`,rangeReversed:"开始日期必须早于结束日期。",rangeUnderflow:e=>`值必须是 ${e.minValue} 或更晚日期。`,unavailableDate:"所选日期不可用。"};var Tl={};Tl={rangeOverflow:e=>`值必須是 ${e.maxValue} 或更早。`,rangeReversed:"開始日期必須在結束日期之前。",rangeUnderflow:e=>`值必須是 ${e.minValue} 或更晚。`,unavailableDate:"所選日期無法使用。"};var Ml={};Ml={"ar-AE":el,"bg-BG":tl,"cs-CZ":al,"da-DK":rl,"de-DE":ul,"el-GR":nl,"en-US":il,"es-ES":ll,"et-EE":ol,"fi-FI":sl,"fr-FR":dl,"he-IL":cl,"hr-HR":fl,"hu-HU":ml,"it-IT":$l,"ja-JP":Dl,"ko-KR":hl,"lt-LT":pl,"lv-LV":bl,"nb-NO":yl,"nl-NL":gl,"pl-PL":vl,"pt-BR":xl,"pt-PT":El,"ro-RO":Cl,"ru-RU":Bl,"sk-SK":Pl,"sl-SI":wl,"sr-SP":Fl,"sv-SE":Sl,"tr-TR":Al,"uk-UA":Rl,"zh-CN":kl,"zh-TW":Tl};function Od(e){return e&&e.__esModule?e.default:e}const Ld=new Fe(Od(Ml));function Nd(){return typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US"}function Vl(e,t,a,r,u){let n=e!=null&&a!=null&&e.compare(a)>0,i=e!=null&&t!=null&&e.compare(t)<0,l=e!=null&&(r==null?void 0:r(e))||!1,s=n||i||l,c=[];if(s){let D=Nd(),$=Fe.getGlobalDictionaryForPackage("@react-stately/datepicker")||Ld,b=new zu(D,$),E=new de(D,Le({},u)),p=E.resolvedOptions().timeZone;i&&t!=null&&c.push(b.format("rangeUnderflow",{minValue:E.format(t.toDate(p))})),n&&a!=null&&c.push(b.format("rangeOverflow",{maxValue:E.format(a.toDate(p))})),l&&c.push(b.format("unavailableDate"))}return{isInvalid:s,validationErrors:c,validationDetails:{badInput:l,customError:!1,patternMismatch:!1,rangeOverflow:n,rangeUnderflow:i,stepMismatch:!1,tooLong:!1,tooShort:!1,typeMismatch:!1,valueMissing:!1,valid:!s}}}const jd={year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"2-digit",second:"2-digit"},Ud={year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"};function Le(e,t){e={...t.shouldForceLeadingZeros?Ud:jd,...e};let r=t.granularity||"minute",u=Object.keys(e);var n;let i=u.indexOf((n=t.maxGranularity)!==null&&n!==void 0?n:"year");i<0&&(i=0);let l=u.indexOf(r);if(l<0&&(l=2),i>l)throw new Error("maxGranularity must be greater than granularity");let s=u.slice(i,l+1).reduce((D,$)=>(D[$]=e[$],D),{});return t.hourCycle!=null&&(s.hour12=t.hourCycle===12),s.timeZone=t.timeZone||"UTC",(r==="hour"||r==="minute"||r==="second")&&t.timeZone&&!t.hideTimeZone&&(s.timeZoneName="short"),t.showEra&&i===0&&(s.era="short"),s}function Ir(e){return e&&"hour"in e?e:new st}function Il(e,t){if(e===null)return null;if(e)return N(e,t)}function Ye(e,t,a,r){if(e)return Il(e,a);let u=N(Yr(r??Ne()).set({hour:0,minute:0,second:0,millisecond:0}),a);return t==="year"||t==="month"||t==="day"?ne(u):r?u:ce(u)}function Ol(e,t){let a=e&&"timeZone"in e?e.timeZone:void 0,r=e&&"minute"in e?"minute":"day";if(e&&t&&!(t in e))throw new Error("Invalid granularity "+t+" for value "+e.toString());let[u,n]=o.useState([r,a]);e&&(u[0]!==r||u[1]!==a)&&n([r,a]),t||(t=e?r:u[0]);let i=e?a:u[1];return[t,i]}function Kd(e){let t=Id(e),[a,r]=Ke(e.value,e.defaultValue||null,e.onChange),u=a||e.placeholderValue||null,[n,i]=Ol(u,e.granularity),l=a!=null?a.toDate(i??"UTC"):null,s=n==="hour"||n==="minute"||n==="second";var c;let D=(c=e.shouldCloseOnSelect)!==null&&c!==void 0?c:!0,[$,b]=o.useState(null),[E,p]=o.useState(null);if(a&&($=a,"hour"in a&&(E=a)),u&&!(n in u))throw new Error("Invalid granularity "+n+" for value "+u.toString());let x=(a==null?void 0:a.calendar.identifier)==="gregory"&&a.era==="BC",w=o.useMemo(()=>({granularity:n,timeZone:i,hideTimeZone:e.hideTimeZone,hourCycle:e.hourCycle,shouldForceLeadingZeros:e.shouldForceLeadingZeros,showEra:x}),[n,e.hourCycle,e.shouldForceLeadingZeros,i,e.hideTimeZone,x]),{minValue:R,maxValue:k,isDateUnavailable:C}=e,y=o.useMemo(()=>Vl(a,R,k,C,w),[a,R,k,C,w]),A=Mn({...e,value:a,builtinValidation:y}),T=A.displayValidation.isInvalid,I=e.validationState||(T?"invalid":null),d=(f,F)=>{r("timeZone"in F?F.set(ne(f)):ce(f,F)),b(null),p(null),A.commitValidation()};return{...A,value:a,setValue:r,dateValue:$,timeValue:E,setDateValue:f=>{let F=typeof D=="function"?D():D;s?E||F?d(f,E||Ir(e.defaultValue||e.placeholderValue)):b(f):(r(f),A.commitValidation()),F&&t.setOpen(!1)},setTimeValue:f=>{$&&f?d($,f):p(f)},granularity:n,hasTime:s,...t,setOpen(f){!f&&!a&&$&&s&&d($,E||Ir(e.defaultValue||e.placeholderValue)),t.setOpen(f)},validationState:I,isInvalid:T,formatValue(f,F){if(!l)return"";let m=Le(F,w);return new de(f,m).format(l)},getDateFormatter(f,F){let m={...w,...F},h=Le({},m);return new de(f,h)}}}const _d=new Fe({ach:{year:"mwaka",month:"dwe",day:"nino"},af:{year:"jjjj",month:"mm",day:"dd"},am:{year:"ዓዓዓዓ",month:"ሚሜ",day:"ቀቀ"},an:{year:"aaaa",month:"mm",day:"dd"},ar:{year:"سنة",month:"شهر",day:"يوم"},ast:{year:"aaaa",month:"mm",day:"dd"},az:{year:"iiii",month:"aa",day:"gg"},be:{year:"гггг",month:"мм",day:"дд"},bg:{year:"гггг",month:"мм",day:"дд"},bn:{year:"yyyy",month:"মিমি",day:"dd"},br:{year:"bbbb",month:"mm",day:"dd"},bs:{year:"gggg",month:"mm",day:"dd"},ca:{year:"aaaa",month:"mm",day:"dd"},cak:{year:"jjjj",month:"ii",day:"q'q'"},ckb:{year:"ساڵ",month:"مانگ",day:"ڕۆژ"},cs:{year:"rrrr",month:"mm",day:"dd"},cy:{year:"bbbb",month:"mm",day:"dd"},da:{year:"åååå",month:"mm",day:"dd"},de:{year:"jjjj",month:"mm",day:"tt"},dsb:{year:"llll",month:"mm",day:"źź"},el:{year:"εεεε",month:"μμ",day:"ηη"},en:{year:"yyyy",month:"mm",day:"dd"},eo:{year:"jjjj",month:"mm",day:"tt"},es:{year:"aaaa",month:"mm",day:"dd"},et:{year:"aaaa",month:"kk",day:"pp"},eu:{year:"uuuu",month:"hh",day:"ee"},fa:{year:"سال",month:"ماه",day:"روز"},ff:{year:"hhhh",month:"ll",day:"ññ"},fi:{year:"vvvv",month:"kk",day:"pp"},fr:{year:"aaaa",month:"mm",day:"jj"},fy:{year:"jjjj",month:"mm",day:"dd"},ga:{year:"bbbb",month:"mm",day:"ll"},gd:{year:"bbbb",month:"mm",day:"ll"},gl:{year:"aaaa",month:"mm",day:"dd"},he:{year:"שנה",month:"חודש",day:"יום"},hr:{year:"gggg",month:"mm",day:"dd"},hsb:{year:"llll",month:"mm",day:"dd"},hu:{year:"éééé",month:"hh",day:"nn"},ia:{year:"aaaa",month:"mm",day:"dd"},id:{year:"tttt",month:"bb",day:"hh"},it:{year:"aaaa",month:"mm",day:"gg"},ja:{year:" 年 ",month:"月",day:"日"},ka:{year:"წწწწ",month:"თთ",day:"რრ"},kk:{year:"жжжж",month:"аа",day:"кк"},kn:{year:"ವವವವ",month:"ಮಿಮೀ",day:"ದಿದಿ"},ko:{year:"연도",month:"월",day:"일"},lb:{year:"jjjj",month:"mm",day:"dd"},lo:{year:"ປປປປ",month:"ດດ",day:"ວວ"},lt:{year:"mmmm",month:"mm",day:"dd"},lv:{year:"gggg",month:"mm",day:"dd"},meh:{year:"aaaa",month:"mm",day:"dd"},ml:{year:"വർഷം",month:"മാസം",day:"തീയതി"},ms:{year:"tttt",month:"mm",day:"hh"},nl:{year:"jjjj",month:"mm",day:"dd"},nn:{year:"åååå",month:"mm",day:"dd"},no:{year:"åååå",month:"mm",day:"dd"},oc:{year:"aaaa",month:"mm",day:"jj"},pl:{year:"rrrr",month:"mm",day:"dd"},pt:{year:"aaaa",month:"mm",day:"dd"},rm:{year:"oooo",month:"mm",day:"dd"},ro:{year:"aaaa",month:"ll",day:"zz"},ru:{year:"гггг",month:"мм",day:"дд"},sc:{year:"aaaa",month:"mm",day:"dd"},scn:{year:"aaaa",month:"mm",day:"jj"},sk:{year:"rrrr",month:"mm",day:"dd"},sl:{year:"llll",month:"mm",day:"dd"},sr:{year:"гггг",month:"мм",day:"дд"},sv:{year:"åååå",month:"mm",day:"dd"},szl:{year:"rrrr",month:"mm",day:"dd"},tg:{year:"сссс",month:"мм",day:"рр"},th:{year:"ปปปป",month:"ดด",day:"วว"},tr:{year:"yyyy",month:"aa",day:"gg"},uk:{year:"рррр",month:"мм",day:"дд"},"zh-CN":{year:"年",month:"月",day:"日"},"zh-TW":{year:"年",month:"月",day:"日"}},"en");function zd(e,t,a){return e==="era"||e==="dayPeriod"?t:e==="year"||e==="month"||e==="day"?_d.getStringForLocale(e,a):"––"}const yt={year:!0,month:!0,day:!0,hour:!0,minute:!0,second:!0,dayPeriod:!0,era:!0},Or={year:5,month:2,day:7,hour:2,minute:15,second:15},Zd={dayperiod:"dayPeriod"};function Ll(e){let{locale:t,createCalendar:a,hideTimeZone:r,isDisabled:u=!1,isReadOnly:n=!1,isRequired:i=!1,minValue:l,maxValue:s,isDateUnavailable:c}=e,D=e.value||e.defaultValue||e.placeholderValue||null,[$,b]=Ol(D,e.granularity),E=b||"UTC";if(D&&!($ in D))throw new Error("Invalid granularity "+$+" for value "+D.toString());let p=o.useMemo(()=>new de(t),[t]),x=o.useMemo(()=>a(p.resolvedOptions().calendar),[a,p]);var w;let[R,k]=Ke(e.value,(w=e.defaultValue)!==null&&w!==void 0?w:null,e.onChange),C=o.useMemo(()=>{var P;return(P=Il(R,x))!==null&&P!==void 0?P:null},[R,x]),[y,A]=o.useState(()=>Ye(e.placeholderValue,$,x,b)),T=C||y,I=x.identifier==="gregory"&&T.era==="BC",d=o.useMemo(()=>{var P;return{granularity:$,maxGranularity:(P=e.maxGranularity)!==null&&P!==void 0?P:"year",timeZone:b,hideTimeZone:r,hourCycle:e.hourCycle,showEra:I,shouldForceLeadingZeros:e.shouldForceLeadingZeros}},[e.maxGranularity,$,e.hourCycle,e.shouldForceLeadingZeros,b,r,I]),S=o.useMemo(()=>Le({},d),[d]),g=o.useMemo(()=>new de(t,S),[t,S]),f=o.useMemo(()=>g.resolvedOptions(),[g]),F=o.useMemo(()=>g.formatToParts(new Date).filter(P=>yt[P.type]).reduce((P,U)=>(P[U.type]=!0,P),{}),[g]),[m,h]=o.useState(()=>e.value||e.defaultValue?{...F}:{}),V=o.useRef(null),z=o.useRef(x.identifier);o.useEffect(()=>{x.identifier!==z.current&&(z.current=x.identifier,A(P=>Object.keys(m).length>0?N(P,x):Ye(e.placeholderValue,$,x,b)))},[x,$,m,b,e.placeholderValue]),R&&Object.keys(m).length<Object.keys(F).length&&(m={...F},h(m)),R==null&&Object.keys(m).length===Object.keys(F).length&&(m={},h(m),A(Ye(e.placeholderValue,$,x,b)));let j=C&&Object.keys(m).length>=Object.keys(F).length?C:y,v=P=>{if(e.isDisabled||e.isReadOnly)return;let U=Object.keys(m),W=Object.keys(F);P==null?(k(null),A(Ye(e.placeholderValue,$,x,b)),h({})):U.length>=W.length||U.length===W.length-1&&F.dayPeriod&&!m.dayPeriod&&V.current!=="dayPeriod"?(P=N(P,(D==null?void 0:D.calendar)||new G),k(P)):A(P),V.current=null},M=o.useMemo(()=>j.toDate(E),[j,E]),O=o.useMemo(()=>g.formatToParts(M).map(P=>{let U=yt[P.type];P.type==="era"&&x.getEras().length===1&&(U=!1);let W=yt[P.type]&&!m[P.type],ae=yt[P.type]?zd(P.type,P.value,t):null;return{type:Zd[P.type]||P.type,text:W?ae:P.value,...Wd(j,P.type,f),isPlaceholder:W,placeholder:ae,isEditable:U}}),[M,m,g,f,j,x,t]);F.era&&m.year&&!m.era?(m.era=!0,h({...m})):!F.era&&m.era&&(delete m.era,h({...m}));let B=P=>{m[P]=!0,P==="year"&&F.era&&(m.era=!0),h({...m})},L=(P,U)=>{if(m[P])v(Yd(j,P,U,f));else{B(P);let W=Object.keys(m),ae=Object.keys(F);(W.length>=ae.length||W.length===ae.length-1&&F.dayPeriod&&!m.dayPeriod)&&v(j)}},J=o.useMemo(()=>Vl(R,l,s,c,d),[R,l,s,c,d]),te=Mn({...e,value:R,builtinValidation:J}),Ae=te.displayValidation.isInvalid,ge=e.validationState||(Ae?"invalid":null);var _t;return{...te,value:C,dateValue:M,calendar:x,setValue:v,segments:O,dateFormatter:g,validationState:ge,isInvalid:Ae,granularity:$,maxGranularity:(_t=e.maxGranularity)!==null&&_t!==void 0?_t:"year",isDisabled:u,isReadOnly:n,isRequired:i,increment(P){L(P,1)},decrement(P){L(P,-1)},incrementPage(P){L(P,Or[P]||1)},decrementPage(P){L(P,-(Or[P]||1))},setSegment(P,U){B(P),v(Hd(j,P,U,f))},confirmPlaceholder(){if(e.isDisabled||e.isReadOnly)return;let P=Object.keys(m),U=Object.keys(F);P.length===U.length-1&&F.dayPeriod&&!m.dayPeriod&&(m={...F},h(m),v(j.copy()))},clearSegment(P){delete m[P],V.current=P,h({...m});let U=Ye(e.placeholderValue,$,x,b),W=j;if(P==="dayPeriod"&&"hour"in j&&"hour"in U){let ae=j.hour>=12,Ha=U.hour>=12;ae&&!Ha?W=j.set({hour:j.hour-12}):!ae&&Ha&&(W=j.set({hour:j.hour+12}))}else P in j&&(W=j.set({[P]:U[P]}));k(null),v(W)},formatValue(P){if(!C)return"";let U=Le(P,d);return new de(t,U).format(M)},getDateFormatter(P,U){let W={...d,...U},ae=Le({},W);return new de(P,ae)}}}function Wd(e,t,a){switch(t){case"era":{let r=e.calendar.getEras();return{value:r.indexOf(e.era),minValue:0,maxValue:r.length-1}}case"year":return{value:e.year,minValue:1,maxValue:e.calendar.getYearsInEra(e)};case"month":return{value:e.month,minValue:so(e),maxValue:e.calendar.getMonthsInYear(e)};case"day":return{value:e.day,minValue:co(e),maxValue:e.calendar.getDaysInMonth(e)}}if("hour"in e)switch(t){case"dayPeriod":return{value:e.hour>=12?12:0,minValue:0,maxValue:12};case"hour":if(a.hour12){let r=e.hour>=12;return{value:e.hour,minValue:r?12:0,maxValue:r?23:11}}return{value:e.hour,minValue:0,maxValue:23};case"minute":return{value:e.minute,minValue:0,maxValue:59};case"second":return{value:e.second,minValue:0,maxValue:59}}return{}}function Yd(e,t,a,r){switch(t){case"era":case"year":case"month":case"day":return e.cycle(t,a,{round:t==="year"})}if("hour"in e)switch(t){case"dayPeriod":{let u=e.hour,n=u>=12;return e.set({hour:n?u-12:u+12})}case"hour":case"minute":case"second":return e.cycle(t,a,{round:t!=="hour",hourCycle:r.hour12?12:24})}throw new Error("Unknown segment: "+t)}function Hd(e,t,a,r){switch(t){case"day":case"month":case"year":case"era":return e.set({[t]:a})}if("hour"in e&&typeof a=="number")switch(t){case"dayPeriod":{let u=e.hour,n=u>=12;return a>=12===n?e:e.set({hour:n?u-12:u+12})}case"hour":if(r.hour12){let n=e.hour>=12;!n&&a===12&&(a=0),n&&a<12&&(a+=12)}case"minute":case"second":return e.set({[t]:a})}throw new Error("Unknown segment: "+t)}function Gd(e){let{placeholderValue:t=new st,minValue:a,maxValue:r,granularity:u,validate:n}=e;var i;let[l,s]=Ke(e.value,(i=e.defaultValue)!==null&&i!==void 0?i:null,e.onChange),c=l||t,D=c&&"day"in c?c:void 0,$=e.defaultValue&&"timeZone"in e.defaultValue?e.defaultValue.timeZone:void 0,b=o.useMemo(()=>{let C=c&&"timeZone"in c?c.timeZone:void 0;return(C||$)&&t?tu(He(t),C||$):He(t)},[t,c,$]),E=o.useMemo(()=>He(a,D),[a,D]),p=o.useMemo(()=>He(r,D),[r,D]),x=o.useMemo(()=>l&&"day"in l?tr(l):l,[l]),w=o.useMemo(()=>l==null?null:He(l),[l]);return{...Ll({...e,value:w,defaultValue:void 0,minValue:E,maxValue:p,onChange:C=>{s(D||$?C:C&&tr(C))},granularity:u||"minute",maxGranularity:"hour",placeholderValue:b??void 0,createCalendar:()=>new G,validate:o.useCallback(()=>n==null?void 0:n(l),[n,l])}),timeValue:x}}function He(e,t=It(Ne())){return e?"day"in e?e:ce(t,e):null}const Sa=o.forwardRef(({children:e,...t},a)=>{const r=o.useRef(null);o.useImperativeHandle(a,()=>r.current);const{buttonProps:u}=Hu(t,r);return o.createElement(Wl,{size:"small",variant:"transparent",className:"rounded-[4px]",...u},e)});Sa.displayName="CalendarButton";const Jd=({state:e,date:t})=>{const a=o.useRef(null),{cellProps:r,buttonProps:u,isSelected:n,isOutsideVisibleRange:i,isDisabled:l,isUnavailable:s,formattedDate:c}=md({date:t},e,a),D=qd(t);return o.createElement("td",{...r,className:"p-1"},o.createElement("div",{...u,ref:a,hidden:i,className:ue("bg-ui-bg-base txt-compact-small relative flex size-8 items-center justify-center rounded-md outline-none transition-fg border border-transparent","hover:bg-ui-bg-base-hover","focus-visible:shadow-borders-focus focus-visible:border-ui-border-interactive",{"!bg-ui-bg-interactive !text-ui-fg-on-color":n,hidden:i,"text-ui-fg-muted hover:!bg-ui-bg-base cursor-default":l||s})},c,D&&o.createElement("div",{role:"none",className:ue("bg-ui-bg-interactive absolute bottom-[3px] left-1/2 size-[3px] -translate-x-1/2 rounded-full transition-fg",{"bg-ui-fg-on-color":n})})))};function qd(e){const t=new Date;return[e.year,e.month,e.day].join("-")===[t.getFullYear(),t.getMonth()+1,t.getDate()].join("-")}const Qd=({state:e,...t})=>{const{locale:a}=q(),{gridProps:r,headerProps:u,weekDays:n}=cd(t,e),i=Do(e.visibleRange.start,a);return o.createElement("table",{...r},o.createElement("thead",{...u},o.createElement("tr",null,n.map((l,s)=>o.createElement("th",{key:s,className:"txt-compact-small-plus text-ui-fg-muted size-8 p-1 rounded-md"},l)))),o.createElement("tbody",null,[...new Array(i).keys()].map(l=>o.createElement("tr",{key:l},e.getDatesInWeek(l).map((s,c)=>s?o.createElement(Jd,{key:c,state:e,date:s}):o.createElement("td",{key:c}))))))},Xd=e=>{const{locale:t}=q(),a=Vd({...e,locale:t,createCalendar:yu}),{calendarProps:r,prevButtonProps:u,nextButtonProps:n,title:i}=dd(e,a);return o.createElement("div",{...r,className:"flex flex-col gap-y-2"},o.createElement("div",{className:"bg-ui-bg-field border-base grid grid-cols-[28px_1fr_28px] items-center gap-1 rounded-md border p-0.5"},o.createElement(Sa,{...u},o.createElement(Jl,null)),o.createElement("div",{className:"flex items-center justify-center"},o.createElement("h2",{className:"txt-compact-small-plus"},i)),o.createElement(Sa,{...n},o.createElement(Yl,null))),o.createElement(Qd,{state:a}))};function Nl(e){return new tt(e.getFullYear(),e.getMonth()+1,e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds())}function ec(e,t){return e?(e.set({day:t.getDate(),month:t.getMonth()+1,year:t.getFullYear(),hour:t.getHours(),minute:t.getMinutes(),second:t.getSeconds(),millisecond:t.getMilliseconds()}),e):Nl(t)}function jl(e){return new K(e.getFullYear(),e.getMonth()+1,e.getDate())}function tc(e,t){return e?(e.set({day:t.getDate(),month:t.getMonth()+1,year:t.getFullYear()}),e):jl(t)}const Ul=new Set(["hour","minute","second"]);function Tt(e,t){return t&&Ul.has(t)?Nl(e):jl(e)}function Lr(e,t,a){return a&&Ul.has(a)?ec(e,t):tc(e,t)}function ac(e,t,a){return e?Tt(e,a):t?Tt(t,a):null}const Kl=({segment:e,state:t})=>{const a=o.useRef(null),{segmentProps:r}=kd(e,t,a);return e.type==="literal"&&e.text===", "?o.createElement("span",{className:"mx-1"}):o.createElement("span",null,o.createElement("div",{ref:a,className:ue("transition-fg outline-none","focus-visible:bg-ui-bg-interactive focus-visible:text-ui-fg-on-color",{"text-ui-fg-muted uppercase":e.isPlaceholder,"text-ui-fg-muted":!e.isEditable&&!t.value}),...r},e.text))},rc=e=>{const t=o.useRef(null),{locale:a}=q(),r=Gd({...e,locale:a}),{fieldProps:u}=Pd(e,r,t);return o.createElement("div",{ref:t,...u,"aria-label":"Time input",className:ue("bg-ui-bg-field shadow-borders-base txt-compact-small flex items-center rounded-md px-2 py-1",{"":e.isDisabled})},r.segments.map((n,i)=>o.createElement(Kl,{key:i,segment:n,state:r})))},_l=o.forwardRef(({children:e,size:t="base",...a},r)=>{const u=o.useRef(null);o.useImperativeHandle(r,()=>u.current);const{buttonProps:n}=Hu(a,u);return o.createElement("button",{type:"button",className:ue("text-ui-fg-muted transition-fg flex items-center justify-center border-r outline-none","disabled:text-ui-fg-disabled","hover:bg-ui-button-transparent-hover","focus-visible:bg-ui-bg-interactive focus-visible:text-ui-fg-on-color",{"size-7":t==="small","size-8":t==="base"}),"aria-label":"Open calendar",...n},e)});_l.displayName="DatePickerButton";const uc=["ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],zl=o.forwardRef(({type:e="button",className:t,children:a,...r},u)=>{const n=i=>{uc.includes(i.key)||i.stopPropagation()};return o.createElement("button",{ref:u,type:e,className:ue("text-ui-fg-muted transition-fg flex size-full items-center justify-center outline-none","hover:bg-ui-button-transparent-hover","focus-visible:bg-ui-bg-interactive focus-visible:text-ui-fg-on-color",t),"aria-label":"Clear date",onKeyDown:n,...r},a)});zl.displayName="DatePickerClearButton";const nc=Nr({base:"flex items-center tabular-nums",variants:{size:{small:"py-1",base:"py-1.5"}},defaultVariants:{size:"base"}}),ic=({size:e="base",...t})=>{const{locale:a}=q(),r=Ll({...t,locale:a,createCalendar:yu}),u=o.useRef(null),{fieldProps:n}=Xi(t,r,u);return o.createElement("div",{ref:u,"aria-label":"Date input",className:nc({size:e}),...n},r.segments.map((i,l)=>o.createElement(Kl,{key:l,segment:i,state:r})))},lc=(e,t,a)=>Nr({base:ue("bg-ui-bg-field shadow-borders-base txt-compact-small text-ui-fg-base transition-fg grid items-center gap-2 overflow-hidden rounded-md h-fit","focus-within:shadow-borders-interactive-with-active focus-visible:shadow-borders-interactive-with-active","aria-[invalid=true]:shadow-borders-error invalid:shadow-borders-error",{"shadow-borders-interactive-with-active":e,"shadow-borders-error":t,"pr-2":!a}),variants:{size:{small:ue("grid-cols-[28px_1fr]",{"grid-cols-[28px_1fr_28px]":!!a}),base:ue("grid-cols-[32px_1fr]",{"grid-cols-[32px_1fr_32px]":!!a})}}}),oc=new Set(["hour","minute","second"]),sc=o.forwardRef(({size:e="base",shouldCloseOnSelect:t=!0,className:a,modal:r=!1,...u},n)=>{const[i,l]=o.useState(ac(u.value,u.defaultValue,u.granularity)),s=o.useRef(null);o.useImperativeHandle(n,()=>s.current);const c=o.useRef(null),D=dc(u,l),$=Kd({...D,shouldCloseOnSelect:t}),{groupProps:b,fieldProps:E,buttonProps:p,dialogProps:x,calendarProps:w}=Fd(D,$,s);o.useEffect(()=>{l(u.value?Lr(i,u.value,u.granularity):null),$.setValue(u.value?Lr(i,u.value,u.granularity):null)},[u.value]);function R(y){var A;y.preventDefault(),y.stopPropagation(),(A=u.onChange)===null||A===void 0||A.call(u,null),$.setValue(null)}Ms({ref:c,onInteractOutside:()=>{$.setOpen(!1)}});const C=u.granularity&&oc.has(u.granularity)?Zr:Kr;return o.createElement(zt,{modal:r,open:$.isOpen,onOpenChange:$.setOpen},o.createElement(zt.Anchor,{asChild:!0},o.createElement("div",{ref:n,className:ue(lc($.isOpen,$.isInvalid,$.value)({size:e}),a),...b},o.createElement(_l,{...p,size:e},o.createElement(C,null)),o.createElement(ic,{...E,size:e}),!!$.value&&o.createElement(zl,{onClick:R},o.createElement(Gl,null)))),o.createElement(zt.Content,{ref:c,...x,className:"flex flex-col divide-y p-0"},o.createElement("div",{className:"p-3"},o.createElement(Xd,{autoFocus:!0,...w})),$.hasTime&&o.createElement("div",{className:"p-3"},o.createElement(rc,{value:$.timeValue,onChange:$.setTimeValue,hourCycle:u.hourCycle}))))});sc.displayName="DatePicker";function dc(e,t){const{minValue:a,maxValue:r,isDateUnavailable:u,onChange:n,value:i,defaultValue:l,...s}=e;return{...s,onChange:$=>{t($),n==null||n($?$.toDate(Ne()):null)},isDateUnavailable:$=>{const b=$.toDate(Ne());return u?u(b):!1},minValue:a&&Tt(a,e.granularity),maxValue:r&&Tt(r,e.granularity)}}export{sc as D};
