import type { Meta, StoryObj } from '@storybook/react';
import { expect, fn, userEvent, within } from '@storybook/test';
import { Create, Edit } from '..';
import { sampleProjects } from '../__fixtures__/ApiKey.fixtures';
import {
  longName<PERSON>ey,
  specialCharsNameKey,
  allWritePermissions<PERSON>ey,
  noPermissions<PERSON>ey,
  justCreated<PERSON>ey,
  veryOld<PERSON>ey,
} from '../__fixtures__/ApiKey.testCases';

const meta = {
  title: 'Components/ApiKey/EdgeCases',
  parameters: {
    docs: {
      description: {
        component:
          'Edge cases and special scenarios for the API key components to ensure they handle all possible inputs correctly.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="flex h-screen w-full items-center justify-center">
        <Story />
      </div>
    ),
  ],
  tags: ['tests', 'edge-cases'],
} as Meta;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Tests editing an API key with an extremely long name
 */
export const LongNameKey: Story = {
  render: () => <Edit apiKey={longNameKey} onSubmit={fn()} onCancel={fn()} isSubmitting={false} />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Name input should contain the long name
    const nameInput = canvas.getByLabelText('Name');
    await expect(nameInput).toHaveValue(longNameKey.name);

    // Name should be properly truncated or handled in the UI
    await expect(nameInput).toBeVisible();
  },
};

/**
 * Tests editing an API key with special characters in the name
 */
export const SpecialCharsInName: Story = {
  render: () => (
    <Edit apiKey={specialCharsNameKey} onSubmit={fn()} onCancel={fn()} isSubmitting={false} />
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Name input should contain the special characters
    const nameInput = canvas.getByLabelText('Name');
    await expect(nameInput).toHaveValue(specialCharsNameKey.name);
  },
};

/**
 * Shows an API key with all "Write" resource permissions.
 */
export const AllWritePermissions: Story = {
  render: () => <Edit apiKey={allWritePermissionsKey} onSubmit={fn()} onCancel={fn()} />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check that permission type is set to Restricted
    await expect(canvas.getByText('Restricted')).toBeInTheDocument();

    // Check that Resources section is visible
    await expect(canvas.getByText('Resources')).toBeInTheDocument();

    // Check for Write permissions
    // Instead of checking for the bg-primary class, check that the buttons exist
    const writeButtons = canvas.getAllByText('Write');
    await expect(writeButtons.length).toBeGreaterThan(5); // Should have multiple Write buttons

    // Verify specific resource permissions
    await expect(canvas.getByText('Models')).toBeInTheDocument();
    await expect(canvas.getByText('Files')).toBeInTheDocument();
  },
};

/**
 * Shows an API key with no resource permissions (all None).
 */
export const NoResourcePermissions: Story = {
  render: () => <Edit apiKey={noPermissionsKey} onSubmit={fn()} onCancel={fn()} />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check that permission type is set to Restricted
    await expect(canvas.getByText('Restricted')).toBeInTheDocument();

    // Check that Resources section is visible
    await expect(canvas.getByText('Resources')).toBeInTheDocument();

    // Check for None permissions
    // Instead of checking for the bg-primary class, check that the buttons exist
    const noneButtons = canvas.getAllByText('None');
    await expect(noneButtons.length).toBeGreaterThan(5); // Should have multiple None buttons

    // Verify that setting a permission works
    const readButtons = canvas.getAllByText('Read');
    await userEvent.click(readButtons[0]); // Click the first Read button
  },
};

/**
 * Tests a just-created API key that has never been used
 */
export const JustCreatedKey: Story = {
  render: () => (
    <Edit apiKey={justCreatedKey} onSubmit={fn()} onCancel={fn()} isSubmitting={false} />
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Name input should contain the correct name
    const nameInput = canvas.getByLabelText('Name');
    await expect(nameInput).toHaveValue(justCreatedKey.name);
  },
};

/**
 * Tests a very old API key to ensure date handling works correctly
 */
export const VeryOldKey: Story = {
  render: () => <Edit apiKey={veryOldKey} onSubmit={fn()} onCancel={fn()} isSubmitting={false} />,
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Name input should contain the correct name
    const nameInput = canvas.getByLabelText('Name');
    await expect(nameInput).toHaveValue(veryOldKey.name);
  },
};

/**
 * Tests creating a key with extremely long inputs
 */
export const CreateWithLongInput: Story = {
  render: () => (
    <Create
      onSubmit={fn()}
      onCancel={fn()}
      isSubmitting={false}
      availableProjects={sampleProjects}
    />
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Enter extremely long name
    const nameInput = canvas.getByLabelText('Name');
    await userEvent.type(nameInput, longNameKey.name || '');

    // Input should handle the long text
    await expect(nameInput).toHaveValue(longNameKey.name || '');
  },
};

/**
 * Tests a key with invalid permissions
 */
export const InvalidPermissionHandler: Story = {
  render: () => (
    <Edit
      apiKey={{
        ...allWritePermissionsKey,
        // @ts-ignore - Intentionally providing an invalid value to test error handling
        permissionType: 'InvalidType',
      }}
      onSubmit={fn()}
      onCancel={fn()}
      isSubmitting={false}
    />
  ),
};
