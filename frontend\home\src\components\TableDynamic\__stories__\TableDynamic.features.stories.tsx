'use client';

import * as React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import TableDynamic from '../table-dynamic';
import {
  defaultData,
  defaultColumns,
  featureConfigs,
  largeDataset,
} from '../__fixtures__/TableDynamic.fixtures';
import { ActionCell } from './TableDynamic.stories';
import { TooltipProvider } from '@/components/ui/tooltip';
import type { ColumnConfig } from '../types';

// Helper to enhance columns with renderers
const getEnhancedColumns = (baseColumns: Partial<ColumnConfig>[]): ColumnConfig[] => {
  return baseColumns.map((col) => {
    const enhancedCol = { ...col } as ColumnConfig;

    if (col.id === 'price') {
      enhancedCol.cell = ({ row }) => <div className="text-right">${row.getValue('price')}</div>;
      enhancedCol.aggregationFn = (values: unknown[]) =>
        values.reduce((acc: number, val) => acc + (typeof val === 'number' ? val : 0), 0);
      enhancedCol.aggregationFormatter = (value: unknown) =>
        `$${typeof value === 'number' ? value.toFixed(2) : '0.00'}`;
      enhancedCol.headerTooltip = 'Total sum on hover';
    }

    if (col.id === 'name') {
      enhancedCol.cell = ({ row }) => <div className="font-medium">{row.getValue('name')}</div>;
    }

    if (col.id === 'rating') {
      enhancedCol.cell = ({ row }) => (
        <div className="flex items-center">
          <svg
            className="mr-1 h-4 w-4 text-yellow-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M12 2l2.2 6.6h7.1l-5.7 4.2 2.2 6.6-5.7-4.2-5.7 4.2 2.2-6.6-5.7-4.2h7.1z" />
          </svg>
          <span>{row.getValue('rating')}</span>
        </div>
      );
    }

    if (col.id === 'status') {
      enhancedCol.cell = ({ row }) => {
        const status = row.getValue('status') as string;
        const variant =
          status === 'In Stock' ? 'default' : status === 'Low Stock' ? 'secondary' : 'destructive';

        return (
          <div
            className={`inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold ${
              variant === 'default'
                ? 'bg-primary text-primary-foreground'
                : variant === 'secondary'
                  ? 'bg-secondary text-secondary-foreground'
                  : 'bg-destructive text-destructive-foreground'
            }`}
          >
            {status}
          </div>
        );
      };
    }

    if (col.id === 'actions') {
      enhancedCol.cell = ActionCell;
    }

    if (col.id === 'createdAt') {
      enhancedCol.cell = ({ row }) => {
        const date = row.getValue('createdAt') as Date;
        return <div>{date.toLocaleDateString()}</div>;
      };
    }

    return enhancedCol;
  });
};

// Enhanced full columns for stories
const getEnhancedFullColumns = (): ColumnConfig[] => {
  return getEnhancedColumns(defaultColumns);
};

const meta = {
  title: 'Components/TableDynamic/Features',
  component: TableDynamic,
  parameters: {
    docs: {
      description: {
        story: 'Demonstrations of the advanced features of the TableDynamic component.',
      },
    },
  },
  decorators: [
    (Story) => (
      <TooltipProvider>
        <div className="max-w-full p-4">
          <Story />
        </div>
      </TooltipProvider>
    ),
  ],
} satisfies Meta<typeof TableDynamic>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shows a table with sorting functionality.
 */
export const Sorting: Story = {
  args: {
    data: defaultData,
    columns: getEnhancedFullColumns(),
    variant: 'primary',
    size: 'md',
    sorting: featureConfigs.sorting,
  },
};

/**
 * Shows a table with filtering functionality.
 */
export const Filtering: Story = {
  args: {
    data: defaultData,
    columns: getEnhancedFullColumns(),
    variant: 'primary',
    size: 'md',
    filtering: featureConfigs.filtering,
  },
};

/**
 * Shows a table with pagination controls.
 */
export const Pagination: Story = {
  args: {
    data: defaultData,
    columns: getEnhancedFullColumns(),
    variant: 'primary',
    size: 'md',
    pagination: featureConfigs.pagination,
    footer: {
      show: true,
      sticky: false,
      showSummary: false,
    },
  },
};

/**
 * Shows a table with column visibility controls.
 */
export const ColumnVisibility: Story = {
  args: {
    data: defaultData,
    columns: getEnhancedFullColumns(),
    variant: 'primary',
    size: 'md',
    columnVisibility: featureConfigs.columnVisibility,
  },
};

/**
 * Shows a table with row selection capabilities.
 */
export const RowSelection: Story = {
  args: {
    data: defaultData,
    columns: getEnhancedFullColumns(),
    variant: 'primary',
    size: 'md',
    rowSelection: featureConfigs.rowSelection,
  },
};

/**
 * Shows a table with virtualization for handling large datasets efficiently.
 */
export const Virtualization: Story = {
  args: {
    data: largeDataset,
    columns: getEnhancedFullColumns(),
    variant: 'primary',
    size: 'md',
    virtualized: true,
    height: 400,
    width: '100%',
    overscan: 10,
  },
};

/**
 * Shows a table with footer summary calculations.
 */
export const FooterSummary: Story = {
  args: {
    data: defaultData,
    columns: getEnhancedFullColumns(),
    variant: 'primary',
    size: 'md',
    footer: {
      show: true,
      sticky: false,
      showSummary: true,
    },
  },
};

/**
 * Shows a table with all features enabled for a complete experience.
 */
export const AllFeatures: Story = {
  args: {
    data: defaultData,
    columns: getEnhancedFullColumns(),
    variant: 'primary',
    size: 'md',
    pagination: featureConfigs.pagination,
    sorting: featureConfigs.sorting,
    filtering: featureConfigs.filtering,
    columnVisibility: featureConfigs.columnVisibility,
    rowSelection: featureConfigs.rowSelection,
    highlighting: featureConfigs.highlighting,
    header: {
      show: true,
      sticky: true,
      showColumnSeparators: true,
    },
    footer: {
      show: true,
      sticky: false,
      showSummary: true,
    },
    animate: true,
  },
};
