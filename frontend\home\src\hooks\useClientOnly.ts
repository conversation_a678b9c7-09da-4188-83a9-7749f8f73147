'use client';

import { useState, useEffect } from 'react';

/**
 * Hook that returns true only when code is running on the client side
 * Useful for preventing hydration mismatches from Radix UI or other libraries
 * that inject dynamic attributes during client-side rendering
 * 
 * @returns {boolean} Whether the code is running in a browser environment after hydration
 */
export function useClientOnly() {
  const [isMounted, setIsMounted] = useState(false);
  
  useEffect(() => {
    // This useEffect will only run on the client, marking the component as mounted
    setIsMounted(true);
  }, []);
  
  return isMounted;
} 