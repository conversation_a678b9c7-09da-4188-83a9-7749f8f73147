import React from 'react';
import { X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export type FilterTag = {
  id: string;
  type: string;
  value: string;
  label: string;
};

export interface FilterTagGroupProps {
  tags: FilterTag[];
  onRemoveTag: (tag: FilterTag) => void;
  className?: string;
  tagClassName?: string;
  emptyStateComponent?: React.ReactNode;
  ariaLabel?: string;
}

/**
 * FilterTagGroup - A component to display active filters as removable tags
 */
export const FilterTagGroup = ({
  tags,
  onRemoveTag,
  className,
  tagClassName,
  emptyStateComponent = null,
  ariaLabel = 'กลุ่มของตัวกรองที่เลือก',
}: FilterTagGroupProps) => {
  const hasTags = tags.length > 0;

  if (!hasTags && !emptyStateComponent) {
    return null;
  }

  return (
    <div
      className={cn('flex flex-wrap items-center gap-2', className)}
      role="region"
      aria-label={ariaLabel}
    >
      {!hasTags && emptyStateComponent}

      {hasTags && (
        <>
          <p className="mr-1 text-sm font-medium text-gray-600">ตัวกรองที่เลือก :</p>

          {tags.map((tag) => (
            <Badge
              key={`${tag.type}-${tag.id}`}
              variant="outline"
              className={cn(
                'flex h-[40px] items-center gap-2 overflow-hidden rounded-lg border border-[#121E72] bg-gray-50 px-2',
                tagClassName,
              )}
            >
              <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                {tag.type && `${tag.type} : `}
                {tag.label}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 flex-shrink-0 rounded-full p-0 hover:bg-gray-200"
                onClick={() => onRemoveTag(tag)}
                aria-label={`ลบตัวกรอง ${tag.label}`}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </>
      )}
    </div>
  );
};
