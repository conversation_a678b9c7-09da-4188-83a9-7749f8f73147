import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState, useEffect } from 'react';
import Create from '../I18nCreate';
import Edit from '../I18nEdit';
import { sampleProjects, sampleApiKeyRestricted } from '../__fixtures__/ApiKey.fixtures';

// Animation wrapper that fades-in children
const FadeIn = ({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => setVisible(true), delay);
    return () => clearTimeout(timeout);
  }, [delay]);

  return (
    <div
      className="transition-opacity duration-500 ease-in-out"
      style={{ opacity: visible ? 1 : 0 }}
    >
      {children}
    </div>
  );
};

// Animation wrapper for slide-in from the right
const SlideInRight = ({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => setVisible(true), delay);
    return () => clearTimeout(timeout);
  }, [delay]);

  return (
    <div
      className="transition-all duration-500 ease-in-out"
      style={{
        transform: visible ? 'translateX(0)' : 'translateX(50px)',
        opacity: visible ? 1 : 0,
      }}
    >
      {children}
    </div>
  );
};

// Animation wrapper for slide-in from the bottom
const SlideInBottom = ({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => setVisible(true), delay);
    return () => clearTimeout(timeout);
  }, [delay]);

  return (
    <div
      className="transition-all duration-500 ease-in-out"
      style={{
        transform: visible ? 'translateY(0)' : 'translateY(50px)',
        opacity: visible ? 1 : 0,
      }}
    >
      {children}
    </div>
  );
};

// Animation wrapper for scale-in effect
const ScaleIn = ({ children, delay = 0 }: { children: React.ReactNode; delay?: number }) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => setVisible(true), delay);
    return () => clearTimeout(timeout);
  }, [delay]);

  return (
    <div
      className="transition-all duration-500 ease-in-out"
      style={{
        transform: visible ? 'scale(1)' : 'scale(0.9)',
        opacity: visible ? 1 : 0,
      }}
    >
      {children}
    </div>
  );
};

// Component for modal transitions
const AnimatedModal = ({
  children,
  isOpen = true,
  onClose = () => {},
}: {
  children: React.ReactNode;
  isOpen?: boolean;
  onClose?: () => void;
}) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      const timeout = setTimeout(() => setVisible(true), 50);
      return () => clearTimeout(timeout);
    } else {
      setVisible(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 transition-opacity duration-300 ease-in-out"
      style={{ opacity: visible ? 1 : 0 }}
      onClick={() => onClose()}
    >
      <div className="absolute inset-0 bg-black/50" />
      <div className="flex min-h-screen items-center justify-center p-4">
        <div
          className="bg-background rounded-lg shadow-lg transition-all duration-300 ease-in-out"
          style={{
            transform: visible ? 'translateY(0) scale(1)' : 'translateY(20px) scale(0.95)',
            opacity: visible ? 1 : 0,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

const meta = {
  title: 'Components/ApiKey/Animation',
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        story: 'Demonstrates various animation states and transitions for API Key components.',
      },
    },
    chromatic: {
      delay: 1000, // Ensure animations have time to complete for visual tests
    },
  },
  decorators: [
    (Story) => (
      <div className="min-h-screen bg-gray-100 p-4">
        <Story />
      </div>
    ),
  ],
  tags: ['animation', 'transitions'],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Demonstrates a simple fade-in animation for the Create component.
 */
export const FadeInCreateForm: Story = {
  render: () => (
    <div className="mx-auto max-w-md">
      <FadeIn>
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />
      </FadeIn>
    </div>
  ),
};

/**
 * Demonstrates a slide-in animation from the right for the Edit component.
 */
export const SlideInEditForm: Story = {
  render: () => (
    <div className="mx-auto max-w-md">
      <SlideInRight>
        <Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />
      </SlideInRight>
    </div>
  ),
};

/**
 * Demonstrates a sequential fade-in animation for different sections of the Create component.
 */
export const SequentialAnimationCreate: Story = {
  render: () => {
    const FormWithSequentialAnimation = () => {
      const [showComponent, setShowComponent] = useState(false);

      // Trigger the animation sequence after initial render
      useEffect(() => {
        const timeout = setTimeout(() => setShowComponent(true), 300);
        return () => clearTimeout(timeout);
      }, []);

      if (!showComponent) {
        return (
          <div className="bg-background flex h-64 items-center justify-center rounded-lg shadow-sm">
            <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
          </div>
        );
      }

      return (
        <div className="bg-background rounded-lg p-6 shadow-lg">
          <FadeIn>
            <h2 className="mb-4 text-xl font-semibold">Create new secret key</h2>
          </FadeIn>

          <FadeIn delay={300}>
            <div className="mb-6">
              <h3 className="mb-2 text-sm font-medium">Owner Type</h3>
              <div className="border-border rounded-md border bg-gray-50 p-4">
                {/* Placeholder for owner type field */}
                <div className="flex gap-2">
                  <div className="bg-primary text-primary-foreground rounded-md px-3 py-1.5">
                    You
                  </div>
                  <div className="bg-background text-muted-foreground border-border rounded-md border px-3 py-1.5">
                    Service account
                  </div>
                </div>
              </div>
            </div>
          </FadeIn>

          <FadeIn delay={600}>
            <div className="mb-6">
              <h3 className="mb-2 text-sm font-medium">Name</h3>
              <input
                type="text"
                className="w-full rounded-md border border-gray-300 px-3 py-2"
                placeholder="My Test Key"
              />
            </div>
          </FadeIn>

          <FadeIn delay={900}>
            <div className="mb-6">
              <h3 className="mb-2 text-sm font-medium">Permissions</h3>
              <div className="border-border rounded-md border bg-gray-50 p-4">
                {/* Placeholder for permissions field */}
                <div className="flex gap-2">
                  <div className="bg-primary text-primary-foreground rounded-md px-3 py-1.5">
                    All
                  </div>
                  <div className="bg-background text-muted-foreground border-border rounded-md border px-3 py-1.5">
                    Restricted
                  </div>
                  <div className="bg-background text-muted-foreground border-border rounded-md border px-3 py-1.5">
                    Read only
                  </div>
                </div>
              </div>
            </div>
          </FadeIn>

          <FadeIn delay={1200}>
            <div className="flex justify-end gap-2">
              <button className="rounded-md border border-gray-300 px-4 py-2">Cancel</button>
              <button className="bg-primary text-primary-foreground rounded-md px-4 py-2">
                Create secret key
              </button>
            </div>
          </FadeIn>
        </div>
      );
    };

    return (
      <div className="mx-auto max-w-md">
        <FormWithSequentialAnimation />
      </div>
    );
  },
};

/**
 * Demonstrates a modal animation for the Create component.
 */
export const ModalAnimationCreate: Story = {
  render: () => {
    const ModalCreateExample = () => {
      const [isOpen, setIsOpen] = useState(false);

      // Automatically open the modal after a delay for demonstration
      useEffect(() => {
        const timeout = setTimeout(() => setIsOpen(true), 500);
        return () => clearTimeout(timeout);
      }, []);

      return (
        <div className="flex h-96 items-center justify-center">
          <button
            className="bg-primary text-primary-foreground rounded-md px-4 py-2"
            onClick={() => setIsOpen(true)}
          >
            {isOpen ? 'Modal Opened' : 'Open Create API Key Modal'}
          </button>

          <AnimatedModal isOpen={isOpen} onClose={() => setIsOpen(false)}>
            <div className="w-full max-w-md p-6">
              <Create
                onSubmit={() => {}}
                onCancel={() => setIsOpen(false)}
                availableProjects={sampleProjects}
              />
            </div>
          </AnimatedModal>
        </div>
      );
    };

    return <ModalCreateExample />;
  },
};

/**
 * Demonstrates staggered animation for sections of a form.
 */
export const StaggeredAnimationEdit: Story = {
  render: () => (
    <div className="mx-auto max-w-md">
      <ScaleIn>
        <div className="bg-background rounded-lg p-6 shadow-lg">
          <h2 className="mb-4 text-xl font-semibold">Edit API Key</h2>

          <SlideInBottom delay={200}>
            <div className="mb-4">
              <h3 className="mb-2 text-sm font-medium">Name</h3>
              <input
                type="text"
                className="w-full rounded-md border border-gray-300 px-3 py-2"
                defaultValue="songpra-parser"
              />
            </div>
          </SlideInBottom>

          <SlideInBottom delay={400}>
            <div className="mb-4">
              <h3 className="mb-2 text-sm font-medium">Permissions</h3>
              <div className="border-border rounded-md border bg-gray-50 p-4">
                <div className="flex gap-2">
                  <div className="bg-background text-muted-foreground border-border rounded-md border px-3 py-1.5">
                    All
                  </div>
                  <div className="bg-primary text-primary-foreground rounded-md px-3 py-1.5">
                    Restricted
                  </div>
                  <div className="bg-background text-muted-foreground border-border rounded-md border px-3 py-1.5">
                    Read only
                  </div>
                </div>
              </div>
            </div>
          </SlideInBottom>

          <SlideInBottom delay={600}>
            <div className="text-muted-foreground mb-4 text-sm italic">
              Permission changes may take a few minutes to take effect.
            </div>
          </SlideInBottom>

          <SlideInBottom delay={800}>
            <div className="mb-4">
              <h3 className="mb-2 text-sm font-medium">Resources</h3>
              <div className="border-border rounded-md border bg-gray-50 p-4">
                <div className="mb-2 grid grid-cols-4 gap-2">
                  <div className="col-span-2 text-sm font-medium">Models</div>
                  <div className="col-span-2 flex gap-1">
                    <div className="bg-background text-muted-foreground border-border rounded border px-2 py-1 text-xs">
                      None
                    </div>
                    <div className="bg-primary text-primary-foreground rounded px-2 py-1 text-xs">
                      Read
                    </div>
                    <div className="bg-background text-muted-foreground border-border rounded border px-2 py-1 text-xs">
                      Write
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-4 gap-2">
                  <div className="col-span-2 text-sm font-medium">Files</div>
                  <div className="col-span-2 flex gap-1">
                    <div className="bg-background text-muted-foreground border-border rounded border px-2 py-1 text-xs">
                      None
                    </div>
                    <div className="bg-primary text-primary-foreground rounded px-2 py-1 text-xs">
                      Read
                    </div>
                    <div className="bg-background text-muted-foreground border-border rounded border px-2 py-1 text-xs">
                      Write
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SlideInBottom>

          <FadeIn delay={1000}>
            <div className="flex justify-end gap-2">
              <button className="rounded-md border border-gray-300 px-4 py-2">Cancel</button>
              <button className="bg-primary text-primary-foreground rounded-md px-4 py-2">
                Save
              </button>
            </div>
          </FadeIn>
        </div>
      </ScaleIn>
    </div>
  ),
};

/**
 * Demonstrates loading and success state animations.
 */
export const LoadingAndSuccessAnimation: Story = {
  render: () => {
    const LoadingSuccessExample = () => {
      const [state, setState] = useState<'loading' | 'success' | 'idle'>('idle');

      // Cycle through animation states
      useEffect(() => {
        if (state === 'idle') {
          const timeout = setTimeout(() => setState('loading'), 1000);
          return () => clearTimeout(timeout);
        } else if (state === 'loading') {
          const timeout = setTimeout(() => setState('success'), 2000);
          return () => clearTimeout(timeout);
        } else if (state === 'success') {
          const timeout = setTimeout(() => setState('idle'), 3000);
          return () => clearTimeout(timeout);
        }
      }, [state]);

      return (
        <div className="bg-background relative rounded-lg p-6 shadow-lg">
          {state === 'loading' && (
            <div className="absolute inset-0 z-10 flex items-center justify-center rounded-lg bg-black/10">
              <div className="flex flex-col items-center">
                <div className="border-primary h-10 w-10 animate-spin rounded-full border-4 border-t-transparent"></div>
                <p className="mt-2 text-sm font-medium">Creating API Key...</p>
              </div>
            </div>
          )}

          {state === 'success' && (
            <div className="bg-primary/10 animate-fadeIn absolute inset-0 z-10 flex items-center justify-center rounded-lg">
              <div className="bg-background animate-scaleIn flex transform flex-col items-center rounded-lg p-6 shadow-lg">
                <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-white"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </div>
                <h3 className="mb-1 text-lg font-medium">API Key Created!</h3>
                <p className="text-muted-foreground mb-4 text-sm">
                  Your new API key has been generated successfully.
                </p>
                <div className="mb-4 w-full rounded-md bg-gray-100 p-3 font-mono text-sm">
                  sk_test_abcdef123456789
                </div>
                <p className="mb-4 text-sm text-red-500">
                  Make sure to copy your key now. You won&apos;t be able to see it again!
                </p>
                <button className="bg-primary text-primary-foreground rounded-md px-4 py-2">
                  Copy Key
                </button>
              </div>
            </div>
          )}

          <h2 className="mb-4 text-xl font-semibold">Create new secret key</h2>

          <div className="space-y-6">
            <div>
              <label className="mb-1 block text-sm font-medium">Name</label>
              <input
                type="text"
                className="w-full rounded-md border border-gray-300 px-3 py-2"
                placeholder="My Test Key"
                defaultValue="New API Key"
              />
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium">Permissions</label>
              <div className="border-border rounded-md border bg-gray-50 p-3">
                <div className="flex gap-2">
                  <div className="bg-primary text-primary-foreground rounded-md px-3 py-1.5">
                    All
                  </div>
                  <div className="bg-background text-muted-foreground border-border rounded-md border px-3 py-1.5">
                    Restricted
                  </div>
                  <div className="bg-background text-muted-foreground border-border rounded-md border px-3 py-1.5">
                    Read only
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <button className="rounded-md border border-gray-300 px-4 py-2">Cancel</button>
              <button
                className={`rounded-md px-4 py-2 ${
                  state === 'loading'
                    ? 'bg-primary/70 text-primary-foreground'
                    : 'bg-primary text-primary-foreground'
                }`}
                disabled={state === 'loading'}
              >
                Create secret key
              </button>
            </div>
          </div>
        </div>
      );
    };

    return (
      <div className="mx-auto max-w-md">
        <LoadingSuccessExample />
      </div>
    );
  },
};
