// _tests_/input.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Input } from '../input';

describe('Input Component', () => {
  // Test basic rendering
  test('renders input element correctly', () => {
    render(<Input placeholder="Test placeholder" />);
    
    const inputElement = screen.getByPlaceholderText('Test placeholder');
    expect(inputElement).toBeInTheDocument();
  });
  
  // Test label rendering
  test('renders label when provided', () => {
    render(<Input id="test-input" label="Test Label" />);
    
    const labelElement = screen.getByText('Test Label');
    expect(labelElement).toBeInTheDocument();
  });
  
  // Test helper text
  test('renders helper text when provided', () => {
    render(<Input id="test-input" helperText="This is a helper text" />);
    
    const helperTextElement = screen.getByText('This is a helper text');
    expect(helperTextElement).toBeInTheDocument();
  });
  
  // Test error state
  test('renders error message when error is true', () => {
    render(
      <Input 
        id="test-input" 
        error={true} 
        errorMessage="This is an error message" 
      />
    );
    
    const errorMessageElement = screen.getByText('This is an error message');
    expect(errorMessageElement).toBeInTheDocument();
  });
  
  // Test value change
  test('updates value on change', () => {
    render(<Input id="test-input" />);
    
    const inputElement = screen.getByRole('textbox');
    fireEvent.change(inputElement, { target: { value: 'test value' } });
    
    expect(inputElement).toHaveValue('test value');
  });
  
  // Test onValueChange callback
  test('calls onValueChange when value changes', () => {
    const handleValueChange = jest.fn();
    
    render(<Input id="test-input" onValueChange={handleValueChange} />);
    
    const inputElement = screen.getByRole('textbox');
    fireEvent.change(inputElement, { target: { value: 'test value' } });
    
    expect(handleValueChange).toHaveBeenCalledWith('test value');
  });
  
  // Test onChange callback
  test('calls onChange when value changes', () => {
    const handleChange = jest.fn();
    
    render(<Input id="test-input" onChange={handleChange} />);
    
    const inputElement = screen.getByRole('textbox');
    fireEvent.change(inputElement, { target: { value: 'test value' } });
    
    expect(handleChange).toHaveBeenCalled();
  });
  
  // Test disabled state
  test('disables input when disabled prop is true', () => {
    render(<Input id="test-input" disabled />);
    
    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toBeDisabled();
  });
  
  // Test required attribute
  test('adds required attribute when required prop is true', () => {
    render(<Input id="test-input" required />);
    
    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toBeRequired();
  });
  
  // Test aria attributes for accessibility
  test('sets correct aria attributes for accessibility', () => {
    render(
      <Input 
        id="test-input" 
        error={true} 
        errorMessage="This is an error message" 
      />
    );
    
    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toHaveAttribute('aria-invalid', 'true');
    expect(inputElement).toHaveAttribute('aria-describedby', 'test-input-error');
  });
  
  // Test defaultValue prop
  test('displays defaultValue when provided', () => {
    render(<Input id="test-input" defaultValue="default text" />);
    
    const inputElement = screen.getByRole('textbox');
    expect(inputElement).toHaveValue('default text');
  });

  // ----- Custom Variant Tests -----

  // Test search variant
  test('renders search variant correctly with search button', () => {
    render(<Input variant="search" placeholder="Search..." />);
    
    const inputElement = screen.getByPlaceholderText('Search...');
    expect(inputElement).toBeInTheDocument();
    
    const searchButton = screen.getByTestId('search-button');
    expect(searchButton).toBeInTheDocument();
  });
  
  // Test button click in search variant
  test('calls onButtonClick when search button is clicked', () => {
    const handleButtonClick = jest.fn();
    
    render(<Input variant="search" placeholder="Search..." onButtonClick={handleButtonClick} />);
    
    const searchButton = screen.getByTestId('search-button');
    fireEvent.click(searchButton);
    
    expect(handleButtonClick).toHaveBeenCalled();
  });
  
  // Test withButton variant
  test('renders withButton variant correctly with custom button text', () => {
    render(<Input variant="withButton" placeholder="Enter code" buttonText="Apply" />);
    
    const inputElement = screen.getByPlaceholderText('Enter code');
    expect(inputElement).toBeInTheDocument();
    
    const buttonElement = screen.getByRole('button', { name: 'Apply' });
    expect(buttonElement).toBeInTheDocument();
  });
  
  // Test different sizes
  test('applies correct size classes', () => {
    const { rerender } = render(<Input size="sm" placeholder="Small input" />);
    
    let inputElement = screen.getByPlaceholderText('Small input');
    expect(inputElement).toHaveClass('h-8');
    
    rerender(<Input size="default" placeholder="Small input" />);
    inputElement = screen.getByPlaceholderText('Small input');
    expect(inputElement).toHaveClass('h-10');
    
    rerender(<Input size="lg" placeholder="Small input" />);
    inputElement = screen.getByPlaceholderText('Small input');
    expect(inputElement).toHaveClass('h-12');
  });
  
  // Test custom search button className
  test('applies custom className to search button', () => {
    render(
      <Input 
        variant="search" 
        placeholder="Search..." 
        searchButtonClassName="bg-red-500"
      />
    );
    
    const searchButton = screen.getByTestId('search-button');
    expect(searchButton).toHaveClass('bg-red-500');
  });
  
  // Test custom search variant className
  test('applies custom className to search variant wrapper', () => {
    render(
      <Input 
        variant="search" 
        placeholder="Search..." 
        searchVariantClassName="border-red-500"
      />
    );
    
    const inputWrapper = screen.getByPlaceholderText('Search...').closest('div');
    expect(inputWrapper).toHaveClass('border-red-500');
  });
});