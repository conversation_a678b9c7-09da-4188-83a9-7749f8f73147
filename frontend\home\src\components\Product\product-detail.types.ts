import { ReactNode } from 'react';

export type ProductDetailVariant = 'default' | 'bordered' | 'compact' | 'expanded';
export type ProductDetailLayout = 'standard' | 'horizontal' | 'mobile' | 'catalog';

export interface ProductRating {
  value: number;
  badge?: string;
}

export interface ProductSpecification {
  icon?: string;
  label?: string;
  text: string;
}

export interface ProductData {
  brand: string;
  title: string;
  productCode: string;
  rating?: ProductRating;
  reviewCount?: number;
  salesCount?: number;
  specifications?: ProductSpecification[];
  currentPrice: number;
  originalPrice?: number;
  currency?: string;
  priceUnit?: string;
}

export interface ProductDetailProps {
  product: ProductData;
  variant?: ProductDetailVariant;
  layout?: ProductDetailLayout;
  showRating?: boolean;
  showSalesCount?: boolean;
  showSpecifications?: boolean;
  className?: string;
}