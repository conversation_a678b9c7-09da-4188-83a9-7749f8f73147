// Mock data for the MSW Table component
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'pending';
  lastLogin: string;
}

// Sample users data
export const mockUsers: Record<string, User> = {
  'user-1': {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'active',
    lastLogin: '2023-09-15T10:30:00Z',
  },
  'user-2': {
    id: 'user-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Editor',
    status: 'active',
    lastLogin: '2023-09-14T08:45:00Z',
  },
  'user-3': {
    id: 'user-3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Viewer',
    status: 'inactive',
    lastLogin: '2023-08-28T16:20:00Z',
  },
  'user-4': {
    id: 'user-4',
    name: '<PERSON>',
    email: 'emily.w<PERSON><PERSON>@example.com',
    role: 'Editor',
    status: 'active',
    lastLogin: '2023-09-13T11:10:00Z',
  },
  'user-5': {
    id: 'user-5',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Viewer',
    status: 'pending',
    lastLogin: '2023-09-10T14:25:00Z',
  },
};

// List of all users
export const usersList = Object.values(mockUsers);

// Validation errors
export const mockValidationErrors = {
  name: ['Name is required'],
  email: ['Invalid email format'],
  role: ['Role must be one of: Admin, Editor, Viewer'],
};

// Network simulation delays
export const NETWORK_SPEEDS = {
  fast: 300,
  normal: 800,
  slow: 2000,
  verySlow: 5000,
}; 