// This file is for server components only
import { getServerTranslation, type SupportedLanguage } from '@/lib';

/**
 * Server-side translation utility for server components
 * Use this in server components to access translations
 * 
 * @example
 * // In a server component:
 * const { t } = await createTranslation('common', 'en');
 * return <div>{t('welcome')}</div>;
 */
export async function createTranslation(namespace: string = 'common', language: SupportedLanguage = 'en') {
  return getServerTranslation(namespace, language);
}

// Alias for backward compatibility
export const getTranslation = createTranslation;

// This helper can be used to get the user's language preference from cookies or headers
export function getLanguageFromRequest(request: Request): SupportedLanguage {
  // Implementation to extract language from cookies or accept-language header
  // For now we'll return a default
  return 'en';
} 