import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

export interface LinkListItem {
  /**
   * ข้อความที่จะแสดงในลิงก์
   */
  label: string;
  
  /**
   * URL ที่จะลิงก์ไป
   */
  href: string;
  
  /**
   * เปิดลิงก์ในแท็บใหม่หรือไม่
   */
  external?: boolean;
}

export interface LinkListProps {
  /**
   * หัวข้อของเมนู
   */
  title: string;
  
  /**
   * รายการเมนูทั้งหมด
   */
  items: LinkListItem[];
  
  /**
   * CSS class เพิ่มเติมสำหรับ container
   */
  className?: string;
  
  /**
   * CSS class เพิ่มเติมสำหรับหัวข้อ
   */
  titleClassName?: string;
  
  /**
   * CSS class เพิ่มเติมสำหรับรายการ
   */
  itemClassName?: string;
  
  /**
   * CSS class เพิ่มเติมสำหรับ list container
   */
  listClassName?: string;
}

/**
 * LinkList Component สำหรับแสดงลิงก์ ที่เป็น OrderList ในส่วนต่างๆ ของเว็บไซต์
 */
export const LinkList: React.FC<LinkListProps> = ({
  title,
  items,
  className,
  titleClassName,
  itemClassName,
  listClassName,
}) => {
  return (
    <div className={cn("flex flex-col", className)} data-testid="order-list-menu">
      <h3 
        className={cn(
          "text-xl font-semibold mb-3", 
          titleClassName
        )}
      >
        {title}
      </h3>
      
      <ul className={cn("list-disc pl-5 ml-1.5 space-y-1", listClassName)}>
        {items.map((item, index) => (
          <li key={index} data-testid={`order-list-item-${index}`} className="text-[#5D6A85] hover:text-primary transition-all">
            <Link
              href={item.href}
              target={item.external ? "_blank" : undefined}
              rel={item.external ? "noopener noreferrer" : undefined}
              className={cn(
                "hover:underline",
                itemClassName
              )}
            >
              {item.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default LinkList;