import{u as n}from"./chunk-DV5RB7II-B2VrP-dr.js";import{b as i,r as o,j as r,V as s}from"./index-Bwql5Dzz.js";import{c}from"./chunk-MGPZHEOT-CqdSNFtj.js";var l=c(),m=()=>{const{t:e}=i(),{getFullDate:t}=n();return o.useMemo(()=>[l.accessor("created_at",{header:e("fields.createdAt"),cell:({row:a})=>r.jsx(s,{content:t({date:a.original.created_at,includeTime:!0}),children:r.jsx("span",{children:t({date:a.original.created_at})})}),enableSorting:!0,sortAscLabel:e("filters.sorting.dateAsc"),sortDescLabel:e("filters.sorting.dateDesc")}),l.accessor("updated_at",{header:e("fields.updatedAt"),cell:({row:a})=>r.jsx(s,{content:t({date:a.original.updated_at,includeTime:!0}),children:r.jsx("span",{children:t({date:a.original.updated_at})})}),enableSorting:!0,sortAscLabel:e("filters.sorting.dateAsc"),sortDescLabel:e("filters.sorting.dateDesc")})],[e,t])};export{m as u};
