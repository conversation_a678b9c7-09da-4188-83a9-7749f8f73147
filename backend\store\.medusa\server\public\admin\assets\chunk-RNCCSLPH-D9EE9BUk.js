import{r as s,j as i,L as f,T as o,aj as m}from"./index-Bwql5Dzz.js";import{I as g}from"./chunk-EQTBJSBZ-C4fKII8C.js";var h=Object.defineProperty,n=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,l=(e,a,r)=>a in e?h(e,a,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[a]=r,v=(e,a)=>{for(var r in a)c.call(a,r)&&l(e,r,a[r]);if(n)for(var r of n(a))d.call(a,r)&&l(e,r,a[r]);return e},x=(e,a)=>{var r={};for(var t in e)c.call(e,t)&&a.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&n)for(var t of n(e))a.indexOf(t)<0&&d.call(e,t)&&(r[t]=e[t]);return r};const u=s.forwardRef((e,a)=>{var r=e,{color:t="currentColor"}=r,p=x(r,["color"]);return s.createElement("svg",v({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:a},p),s.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M5.625 4.16V2.7a1.875 1.875 0 1 1 3.75 0V4.16"}),s.createElement("path",{stroke:t,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4.445 4.16h6.11c.865 0 1.587.66 1.66 1.522l.544 6.25a1.666 1.666 0 0 1-1.66 1.811H3.901a1.666 1.666 0 0 1-1.66-1.81l.543-6.25A1.667 1.667 0 0 1 4.445 4.16"}))});u.displayName="ShoppingBag";var w=({to:e,labelKey:a,descriptionKey:r,icon:t})=>i.jsx(f,{to:e,className:"group outline-none",children:i.jsx("div",{className:"flex flex-col gap-2 px-2 pb-2",children:i.jsx("div",{className:"shadow-elevation-card-rest bg-ui-bg-component transition-fg hover:bg-ui-bg-component-hover active:bg-ui-bg-component-pressed group-focus-visible:shadow-borders-interactive-with-active rounded-md px-4 py-2",children:i.jsxs("div",{className:"flex items-center gap-4",children:[i.jsx(g,{children:t}),i.jsxs("div",{className:"flex flex-1 flex-col",children:[i.jsx(o,{size:"small",leading:"compact",weight:"plus",children:a}),i.jsx(o,{size:"small",leading:"compact",className:"text-ui-fg-subtle",children:r})]}),i.jsx("div",{className:"flex size-7 items-center justify-center",children:i.jsx(m,{className:"text-ui-fg-muted"})})]})})})});export{w as S,u as a};
