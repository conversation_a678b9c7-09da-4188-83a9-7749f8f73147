// section-popular-products.tsx
import React from 'react';
import Link from 'next/link';
import { cn } from "@/lib/utils";
import CardProduct, { CardProductProps } from '../Card/card-product';
import { ChevronRight } from 'lucide-react';

export interface SectionPopularProductsProps {
  /** Section title (default: "สินค้ายอดนิยม") */
  title?: React.ReactNode;
  /** List of products to display */
  products: CardProductProps[];
  /** Number of columns to display at different breakpoints */
  columns?: {
    mobile?: number; // default: 2
    tablet?: number; // default: 3
    desktop?: number; // default: 6
  };
  /** Whether to show the "View All" button */
  showViewAll?: boolean;
  /** URL for the "View All" button */
  viewAllUrl?: string;
  /** Event handler for adding product to cart */
  onAddToCart?: (productId: string) => void;
  /** IDs of products currently being added to cart */
  addingToCartIds?: string[];
  /** Extra CSS class names */
  className?: string;
  /** Accessibility label for section */
  ariaLabel?: string;
  /** Data test id for testing */
  dataTestId?: string;
}

/**
 * Section component for displaying popular products in a responsive grid layout
 */
export const SectionPopularProducts = ({
  title = "สินค้ายอดนิยม",
  products = [],
  columns = {
    mobile: 2,
    tablet: 3,
    desktop: 6
  },
  showViewAll = true,
  viewAllUrl = "/products",
  onAddToCart,
  addingToCartIds = [],
  className,
  ariaLabel = "Popular products section",
  dataTestId = "section-popular-products",
}: SectionPopularProductsProps) => {
  const handleAddToCart = (productId: string) => {
    if (onAddToCart) {
      onAddToCart(productId);
    }
  };

  // Generate grid classes based on column configuration
  const gridClasses = cn(
    "grid gap-4",
    `grid-cols-${columns.mobile || 2}`,
    `md:grid-cols-${columns.tablet || 3}`,
    `lg:grid-cols-${columns.desktop || 6}`
  );
  
  // Schema.org structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "itemListElement": products.map((product, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Product",
        "name": product.name,
        "image": product.imageUrl,
        "offers": {
          "@type": "Offer",
          "price": product.price,
          "priceCurrency": "THB"
        }
      }
    }))
  };

  return (
    <section 
      className={cn("py-8", className)}
      aria-label={ariaLabel}
      data-testid={dataTestId}
    >
      {/* Add structured data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold flex items-center">
            <span className="relative">
              <span className="flex items-center">
                <span className="mr-2">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15 4.5L18.5 8L15 11.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8.5 19.5L5 16L8.5 12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M18.5 8H13.5C10.1863 8 7.5 10.6863 7.5 14V16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M5 16H10C13.3137 16 16 13.3137 16 10V8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </span>
                {typeof title === 'string' ? title : title}
              </span>
            </span>
          </h2>
          
          {showViewAll && (
            <Link 
              href={viewAllUrl}
              className="flex items-center text-blue-600 hover:text-blue-800 transition-colors"
              aria-label="ดูสินค้าทั้งหมด"
            >
              <span className="mr-1">ดูทั้งหมด</span>
              <ChevronRight className="h-4 w-4" />
            </Link>
          )}
        </div>

        {products.length > 0 ? (
          <div className={gridClasses} data-testid="products-grid">
            {products.map((product) => (
              <CardProduct
                key={product.id}
                {...product}
                onAddToCart={handleAddToCart}
                isAddingToCart={addingToCartIds.includes(product.id)}
              />
            ))}
          </div>
        ) : (
          <div 
            className="py-8 text-center text-gray-500" 
            data-testid="no-products-message"
          >
            ไม่พบสินค้ายอดนิยมในขณะนี้
          </div>
        )}
      </div>
    </section>
  );
};

export default SectionPopularProducts;