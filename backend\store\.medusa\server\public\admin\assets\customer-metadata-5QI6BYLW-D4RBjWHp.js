import{M as i}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as n,ba as p,dN as d,j as u}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var P=()=>{const{id:a}=n(),{customer:t,isPending:r,isError:o,error:s}=p(a),{mutateAsync:e,isPending:m}=d(a);if(o)throw s;return u.jsx(i,{metadata:t==null?void 0:t.metadata,hook:e,isPending:r,isMutating:m})};export{P as Component};
