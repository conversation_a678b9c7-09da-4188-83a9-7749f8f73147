'use client';

import * as React from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useLanguageChange } from '@/hooks/useLanguageChange';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { ROW_HEIGHTS, HEADER_HEIGHTS, FOOTER_HEIGHTS } from './constants';

/**
 * TableDynamicSkeleton props
 */
export interface TableDynamicSkeletonProps {
  /** Number of columns to show in the skeleton */
  columnCount?: number;
  /** Number of rows to show in the skeleton */
  rowCount?: number;
  /** Whether to use a simplified version for better performance */
  simplified?: boolean;
  /** Whether to show the header */
  showHeader?: boolean;
  /** Whether to show the footer */
  showFooter?: boolean;
  /** Whether to show filters */
  showFilters?: boolean;
  /** Table size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Custom classes for the skeleton */
  className?: string;
  /** Custom height for the skeleton */
  height?: number | string;
  /** Translation namespace */
  i18nNamespace?: string;
  /** Translation prefix for keys */
  i18nPrefix?: string;
}

/**
 * TableDynamicSkeleton component
 *
 * Displays a skeleton loader for the table while data is loading
 */
export const TableDynamicSkeleton: React.FC<TableDynamicSkeletonProps> = ({
  columnCount = 5,
  rowCount = 10,
  simplified = false,
  showHeader = true,
  showFooter = true,
  showFilters = true,
  size = 'md',
  className,
  height,
  i18nNamespace,
  i18nPrefix = 'tableDynamic.skeleton',
}) => {
  const { t } = useTranslation(i18nNamespace);
  const currentLang = useLanguuseLanguageChangeageChangeStorybook();

  // Generate column sizes with some variation for realism
  const columnWidths = React.useMemo(() => {
    return Array(columnCount)
      .fill(0)
      .map((_, i) => {
        // First column is usually wider, last column might be actions (narrower)
        if (i === 0) return 180 + Math.floor(Math.random() * 40);
        if (i === columnCount - 1) return 80 + Math.floor(Math.random() * 20);
        // Random widths for middle columns
        return 120 + Math.floor(Math.random() * 60);
      });
  }, [columnCount]);

  // For very simplified skeletons (extreme performance needs)
  if (simplified) {
    return (
      <div
        className={cn('w-full rounded-md border', className)}
        aria-label={t(`${i18nPrefix}.ariaLabel`)}
        role="status"
      >
        <Skeleton className="h-10 w-full rounded-none" />
        {Array(rowCount)
          .fill(0)
          .map((_, i) => (
            <Skeleton
              key={i}
              className="h-10 w-full rounded-none"
              style={{ opacity: 1 - i * 0.07 }}
            />
          ))}
        <Skeleton className="h-10 w-full rounded-none" />
      </div>
    );
  }

  return (
    <div
      className={cn('w-full space-y-4', className)}
      aria-label={t(`${i18nPrefix}.ariaLabel`)}
      role="status"
    >
      {/* Filter skeleton */}
      {showFilters && (
        <div className="flex items-center justify-between gap-2">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
      )}

      <div className="overflow-hidden rounded-md border">
        {/* Table skeleton */}
        <div className="w-full">
          {/* Header skeleton */}
          {showHeader && (
            <div className="bg-muted/50 border-b" style={{ height: HEADER_HEIGHTS[size] }}>
              <div className="flex h-full items-center">
                {columnWidths.map((width, i) => (
                  <div key={`header-${i}`} className="px-3" style={{ width }}>
                    <Skeleton className="h-5 w-full" />
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Body skeleton */}
          <div
            className="divide-y"
            style={{ height: height || `calc(${rowCount} * ${ROW_HEIGHTS[size]}px)` }}
          >
            {Array(rowCount)
              .fill(0)
              .map((_, rowIndex) => (
                <div
                  key={`row-${rowIndex}`}
                  className="flex items-center"
                  style={{ height: ROW_HEIGHTS[size] }}
                >
                  {columnWidths.map((width, colIndex) => (
                    <div key={`cell-${rowIndex}-${colIndex}`} className="px-3" style={{ width }}>
                      <Skeleton
                        className={cn(
                          'h-4 w-full',
                          // Vary the widths for more realism
                          colIndex === 0 && 'w-[90%]',
                          colIndex === columnCount - 1 && 'w-[60%]',
                        )}
                      />
                      {/* Add an optional second line for the first column */}
                      {colIndex === 0 && <Skeleton className="mt-1 h-3 w-[70%]" />}
                    </div>
                  ))}
                </div>
              ))}
          </div>

          {/* Footer skeleton */}
          {showFooter && (
            <div className="bg-muted/10 border-t" style={{ height: FOOTER_HEIGHTS[size] }}>
              <div className="flex h-full items-center justify-between px-4">
                <Skeleton className="h-6 w-32" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-6 w-40" />
                  <Skeleton className="h-8 w-24" />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="text-muted-foreground hidden text-xs">
        {t('language.current')}: {currentLang}
      </div>
    </div>
  );
};

TableDynamicSkeleton.displayName = 'TableDynamicSkeleton';
