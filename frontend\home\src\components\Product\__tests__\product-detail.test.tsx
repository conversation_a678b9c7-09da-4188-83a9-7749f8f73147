// _tests_/product-detail.test.tsx
import React from 'react';
import { render, screen } from '@testing-library/react';
import { ProductDetail } from '../product-detail';
import { 
  productWithBadgeFixture, 
  productWithoutBadgeFixture,
  productPromotionFixture 
} from '../__fixtures__/product-detail.fixtures';

// Mock Lucide React components
jest.mock('lucide-react', () => ({
  Star: () => <div data-testid="star-icon" />,
  Power: () => <div data-testid="power-icon" />,
  Sun: () => <div data-testid="sun-icon" />,
}));

describe('ProductDetail', () => {
  it('แสดงข้อมูลพื้นฐานของสินค้าอย่างถูกต้อง', () => {
    render(<ProductDetail product={productWithBadgeFixture} />);
    
    // ตรวจสอบชื่อแบรนด์และชื่อสินค้า
    expect(screen.getByText('Philips')).toBeInTheDocument();
    expect(screen.getByText('หลอด LED PHILIPS T8 ECOFIT 765 18 วัตต์ DAYLIGHT G13')).toBeInTheDocument();
    
    // ตรวจสอบรหัสสินค้า
    expect(screen.getByText('รหัสสินค้า: 11194009203')).toBeInTheDocument();
    
    // ตรวจสอบยอดขาย
    expect(screen.getByText('ขายแล้ว: 8,525 ชิ้น')).toBeInTheDocument();
    
    // ตรวจสอบราคา
    expect(screen.getByText(/129/)).toBeInTheDocument();
  });
  
  it('แสดง badge "3 แถม 1" เมื่อกำหนดในข้อมูลสินค้า', () => {
    render(<ProductDetail product={productWithBadgeFixture} />);
    expect(screen.getByText('3 แถม 1')).toBeInTheDocument();
  });
  
  it('แสดง badge "ซื้อ 1 แถม 1" เมื่อกำหนดในข้อมูลสินค้า', () => {
    render(<ProductDetail product={productPromotionFixture} />);
    expect(screen.getByText('ซื้อ 1 แถม 1')).toBeInTheDocument();
  });
  
  it('ไม่แสดง badge เมื่อไม่ได้กำหนดในข้อมูลสินค้า', () => {
    render(<ProductDetail product={productWithoutBadgeFixture} />);
    expect(screen.queryByText('3 แถม 1')).not.toBeInTheDocument();
    expect(screen.queryByText('ซื้อ 1 แถม 1')).not.toBeInTheDocument();
  });
  
  it('ไม่แสดงคะแนนเมื่อตั้งค่า showRating เป็น false', () => {
    render(<ProductDetail product={productWithBadgeFixture} showRating={false} />);
    
    // ไม่ควรมี badge
    expect(screen.queryByText('3 แถม 1')).not.toBeInTheDocument();
    
    // ไม่ควรมีไอคอนดาว
    expect(screen.queryAllByTestId('star-icon')).toHaveLength(0);
  });
  
  it('ไม่แสดงยอดขายเมื่อตั้งค่า showSalesCount เป็น false', () => {
    render(<ProductDetail product={productWithBadgeFixture} showSalesCount={false} />);
    expect(screen.queryByText(/ขายแล้ว:/)).not.toBeInTheDocument();
  });
  
  it('ไม่แสดงรายละเอียดเมื่อตั้งค่า showSpecifications เป็น false', () => {
    render(<ProductDetail product={productWithBadgeFixture} showSpecifications={false} />);
    expect(screen.queryByText('รายละเอียด')).not.toBeInTheDocument();
  });
  
  it('แสดงผลในแบบ variant - bordered', () => {
    const { container } = render(
      <ProductDetail product={productWithBadgeFixture} variant="bordered" />
    );
    
    // ตรวจสอบว่ามีคลาส border
    expect(container.firstChild).toHaveClass('border');
    expect(container.firstChild).toHaveClass('border-gray-200');
  });
  
  it('แสดงผลในแบบ variant - compact', () => {
    const { container } = render(
      <ProductDetail product={productWithBadgeFixture} variant="compact" />
    );
    
    // ตรวจสอบว่ามีคลาส p-3
    expect(container.firstChild).toHaveClass('p-3');
  });
  
  it('แสดงผลในแบบ variant - default', () => {
    const { container } = render(
      <ProductDetail product={productWithBadgeFixture} variant="default" />
    );
    
    // ตรวจสอบว่ามีคลาส shadow-md
    expect(container.firstChild).toHaveClass('shadow-md');
  });
  
  it('ข้อมูลสินค้า originalPrice แสดงผลถูกต้อง', () => {
    render(<ProductDetail product={productWithBadgeFixture} />);
    
    // ตรวจสอบราคาปกติ (ที่ถูกขีดฆ่า)
    expect(screen.getByText(/294/)).toBeInTheDocument();
  });
  
  it('รองรับการกำหนด className เพิ่มเติม', () => {
    const customClass = 'custom-class-test';
    const { container } = render(
      <ProductDetail 
        product={productWithBadgeFixture} 
        className={customClass} 
      />
    );
    
    // ตรวจสอบว่ามีคลาสที่กำหนดเพิ่มเติม
    expect(container.firstChild).toHaveClass(customClass);
  });
});