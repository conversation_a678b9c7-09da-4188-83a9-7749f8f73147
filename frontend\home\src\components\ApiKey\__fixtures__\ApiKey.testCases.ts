import type { <PERSON><PERSON><PERSON><PERSON> } from '../ApiKey-Types';

/**
 * Edge case: API Key with extremely long name
 */
export const longNameKey: ApiKey = {
  id: 'key-long-name',
  name: 'this-is-an-extremely-long-api-key-name-that-tests-the-limits-of-our-ui-components-to-ensure-they-can-handle-such-cases-without-breaking-the-layout-or-truncating-inappropriately',
  permissionType: 'All',
  createdAt: new Date().toISOString(),
  lastUsed: undefined,
};

/**
 * Edge case: API Key with special characters in name
 */
export const specialCharsNameKey: ApiKey = {
  id: 'key-special-chars',
  name: 'api-key_with-special_chars!@#$%^&*()_+',
  permissionType: 'All',
  createdAt: new Date().toISOString(),
  lastUsed: new Date().toISOString(),
};

/**
 * Edge case: API Key with all resource permissions set to Write
 */
export const allWritePermissionsKey: ApiKey = {
  id: 'key-all-write',
  name: 'all-write-permissions',
  permissionType: 'Restricted',
  resourcePermissions: {
    models: 'Write',
    modelCapabilities: 'Write',
    assistants: 'Write',
    threads: 'Write',
    evals: 'Write',
    fineTuning: 'Write',
    files: 'Write',
  },
  createdAt: new Date().toISOString(),
  lastUsed: new Date().toISOString(),
};

/**
 * Edge case: API Key with all resource permissions set to None
 */
export const noPermissionsKey: ApiKey = {
  id: 'key-no-permissions',
  name: 'no-resource-permissions',
  permissionType: 'Restricted',
  resourcePermissions: {
    models: 'None',
    modelCapabilities: 'None',
    assistants: 'None',
    threads: 'None',
    evals: 'None',
    fineTuning: 'None',
    files: 'None',
  },
  createdAt: new Date().toISOString(),
  lastUsed: new Date().toISOString(),
};

/**
 * Edge case: API Key that was just created (never used)
 */
export const justCreatedKey: ApiKey = {
  id: 'key-just-created',
  name: 'brand-new-key',
  permissionType: 'All',
  createdAt: new Date().toISOString(),
  lastUsed: undefined,
};

/**
 * Edge case: API Key with very old creation and usage dates
 */
export const veryOldKey: ApiKey = {
  id: 'key-very-old',
  name: 'legacy-key',
  permissionType: 'All',
  createdAt: new Date('2020-01-01T00:00:00.000Z').toISOString(),
  lastUsed: new Date('2020-01-15T00:00:00.000Z').toISOString(),
};

/**
 * Collection of all edge case test scenarios
 */
export const edgeCaseTestScenarios = [
  longNameKey,
  specialCharsNameKey,
  allWritePermissionsKey,
  noPermissionsKey,
  justCreatedKey,
  veryOldKey,
];
