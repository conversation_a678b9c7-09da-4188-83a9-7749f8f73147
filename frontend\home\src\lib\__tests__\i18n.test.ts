import '@testing-library/jest-dom';
import { act, renderHook } from '@testing-library/react';
import {
  i18n,
  SupportedLanguage,
  supportedLngs,
  languageCodes,
  changeLanguage,
  getLanguage,
  isRTL
} from '../index';

// Mock dynamic imports for server-side implementation
jest.mock('fs/promises', () => ({
  readFile: jest.fn().mockImplementation((path) => {
    // Mock translation files
    if (path.includes('/en/common.json')) {
      return Promise.resolve(JSON.stringify({
        greeting: 'Hello',
        welcome: 'Welcome to our site'
      }));
    } else if (path.includes('/fr/common.json')) {
      return Promise.resolve(JSON.stringify({
        greeting: 'Bonjour',
        welcome: 'Bienvenue sur notre site'
      }));
    } else if (path.includes('/ar/common.json')) {
      return Promise.resolve(JSON.stringify({
        greeting: 'مرحبا',
        welcome: 'مرحبا بكم في موقعنا'
      }));
    }
    return Promise.resolve('{}');
  })
}));

// Mock window for server-side tests
const originalWindow = global.window;

describe('i18n implementation', () => {
  afterEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Reset window for each test
    global.window = originalWindow;
  });

  describe('client-side functionality', () => {
    beforeEach(() => {
      // Ensure window exists for client tests
      if (!global.window) {
        // @ts-ignore - we're mocking window for testing purposes
        global.window = Object.create(global);
        
        // Mock localStorage
        Object.defineProperty(global.window, 'localStorage', {
          value: {
            getItem: jest.fn(),
            setItem: jest.fn(),
            removeItem: jest.fn()
          },
          writable: true
        });
      }
    });
    
    test('exports a client i18n instance', () => {
      expect(i18n).toBeDefined();
      expect(i18n.client).toBeDefined();
      expect(i18n.client.i18n).toBeDefined();
    });
    
    test('supports language list is correctly defined', () => {
      expect(languageCodes).toContain('en');
      expect(languageCodes).toContain('fr');
      expect(languageCodes).toContain('ja');
      expect(languageCodes).toContain('ar');
      
      expect(supportedLngs).toHaveProperty('en');
      expect(supportedLngs).toHaveProperty('fr');
      expect(supportedLngs).toHaveProperty('ja');
      expect(supportedLngs).toHaveProperty('ar');
    });
    
    test('changeLanguage function changes the language', async () => {
      const spy = jest.spyOn(i18n.client.i18n, 'changeLanguage');
      
      await changeLanguage('fr');
      
      expect(spy).toHaveBeenCalledWith('fr');
      spy.mockRestore();
    });
    
    test('getLanguage returns the current language', () => {
      // Mock the i18n.language property
      const originalLanguage = i18n.client.i18n.language;
      Object.defineProperty(i18n.client.i18n, 'language', {
        get: jest.fn().mockReturnValue('fr'),
        configurable: true
      });
      
      const result = getLanguage();
      
      expect(result).toBe('fr');
      
      // Restore the original value
      Object.defineProperty(i18n.client.i18n, 'language', {
        value: originalLanguage,
        configurable: true
      });
    });
    
    test('getLanguage returns default language for unsupported language', () => {
      // Mock the i18n.language property with an unsupported language
      const originalLanguage = i18n.client.i18n.language;
      Object.defineProperty(i18n.client.i18n, 'language', {
        get: jest.fn().mockReturnValue('de'), // Not in our supported list
        configurable: true
      });
      
      const result = getLanguage();
      
      expect(result).toBe('en'); // Should return default 'en'
      
      // Restore the original value
      Object.defineProperty(i18n.client.i18n, 'language', {
        value: originalLanguage,
        configurable: true
      });
    });
    
    test('isRTL correctly identifies RTL languages', () => {
      expect(isRTL('en')).toBe(false);
      expect(isRTL('fr')).toBe(false);
      expect(isRTL('ja')).toBe(false);
      expect(isRTL('ar')).toBe(true);
    });
    
    test('client-side translation function works', async () => {
      // Mock the t function
      const tMock = jest.fn().mockImplementation((key) => `translated:${key}`);
      // @ts-ignore - mocking for test purposes
      jest.spyOn(i18n.client.i18n, 't').mockImplementation(tMock);
      
      const translationFunc = await i18n.getTranslationFunction('common');
      translationFunc.t('greeting');
      
      expect(tMock).toHaveBeenCalledWith('greeting', { ns: 'common' });
      
      // Restore original
      jest.spyOn(i18n.client.i18n, 't').mockRestore();
    });
  });
  
  describe('server-side functionality', () => {
    beforeEach(() => {
      // Remove window to simulate server environment
      // @ts-ignore - we're removing window for server-side testing
      global.window = undefined;
    });
    
    test('detects server environment correctly', () => {
      expect(i18n.isServer).toBe(true);
      expect(i18n.isClient).toBe(false);
    });
    
    test('getServerTranslation loads translations from file system', async () => {
      const { t } = await i18n.server.getTranslation('common', 'en');
      
      expect(t('greeting')).toBe('Hello');
      expect(t('welcome')).toBe('Welcome to our site');
    });
    
    test('getServerTranslation handles different languages', async () => {
      const frResult = await i18n.server.getTranslation('common', 'fr');
      
      expect(frResult.t('greeting')).toBe('Bonjour');
      expect(frResult.t('welcome')).toBe('Bienvenue sur notre site');
    });
    
    test('getServerTranslation handles missing translations', async () => {
      const { t } = await i18n.server.getTranslation('common', 'en');
      
      // Missing key should return the key itself as fallback
      expect(t('missing.key')).toBe('missing.key');
    });
    
    test('getServerTranslation with fallback text', async () => {
      const { t } = await i18n.server.getTranslation('common', 'en');
      
      // Should use the provided fallback
      expect(t('missing.key', 'Fallback Text')).toBe('Fallback Text');
    });
    
    test('getTranslationFunction uses server implementation on server', async () => {
      const translationFunc = await i18n.getTranslationFunction('common', 'en');
      
      expect(translationFunc.t('greeting')).toBe('Hello');
      expect(translationFunc.t('welcome')).toBe('Welcome to our site');
    });
  });
  
  // Reset window after all tests
  afterAll(() => {
    global.window = originalWindow;
  });
}); 