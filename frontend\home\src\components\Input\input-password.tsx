import React, { forwardRef, useState, ChangeEvent } from 'react';
import { Input as ShadcnInput } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface InputPasswordProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'type'> {
  label?: string;
  helperText?: string;
  error?: boolean;
  errorMessage?: string;
  onValueChange?: (value: string) => void;
  showPasswordToggle?: boolean;
  inputWrapperClassName?: string;
  size?: 'default' | 'sm' | 'lg';
}

const InputPassword = forwardRef<HTMLInputElement, InputPasswordProps>(
  ({ 
    className, 
    label, 
    helperText, 
    error = false, 
    errorMessage, 
    id, 
    onValueChange, 
    onChange,
    showPasswordToggle = true,
    inputWrapperClassName,
    size = 'default',
    ...props 
  }, ref) => {
    const [value, setValue] = useState<string>(props.defaultValue?.toString() || props.value?.toString() || '');
    const [showPassword, setShowPassword] = useState<boolean>(false);
    
    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setValue(newValue);
      
      // Call both handlers if provided
      if (onValueChange) {
        onValueChange(newValue);
      }
      
      if (onChange) {
        onChange(e);
      }
    };

    // Determine size class
    const sizeClass = {
      sm: "h-8 text-sm",
      default: "h-10",
      lg: "h-12 text-lg"
    }[size];
    
    return (
      <div className="w-full space-y-2" data-testid="password-input-container">
        {label && (
          <Label 
            htmlFor={id} 
            className={cn(
              error && "text-destructive"
            )}
          >
            {label}
          </Label>
        )}
        
        <div className={cn("relative", inputWrapperClassName)}>
          <ShadcnInput
            id={id}
            ref={ref}
            value={props.value !== undefined ? props.value : value}
            onChange={handleChange}
            className={cn(
              "pr-10",
              error && "border-destructive focus-visible:ring-destructive",
              sizeClass,
              className
            )}
            type={showPassword ? "text" : "password"}
            aria-invalid={error}
            aria-describedby={error ? `${id}-error` : helperText ? `${id}-helper` : undefined}
            {...props}
          />
          
          {showPasswordToggle && (
            <Button
              type="button"
              variant="link"
              size="icon"
              className="absolute right-0 top-0 h-full px-3 text-muted-foreground hover:text-foreground"
              onClick={() => setShowPassword(!showPassword)}
              tabIndex={-1}
              data-testid="password-toggle"
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
              <span className="sr-only">
                {showPassword ? 'Hide password' : 'Show password'}
              </span>
            </Button>
          )}
        </div>
        
        {helperText && !error && (
          <p 
            id={`${id}-helper`}
            className="text-sm text-muted-foreground"
          >
            {helperText}
          </p>
        )}
        
        {error && errorMessage && (
          <p 
            id={`${id}-error`}
            className="text-sm font-medium text-destructive"
          >
            {errorMessage}
          </p>
        )}
      </div>
    );
  }
);

InputPassword.displayName = 'InputPassword';

export { InputPassword };