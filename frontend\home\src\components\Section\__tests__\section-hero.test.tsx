import React from 'react';
import { render, screen } from '@testing-library/react';
import SectionHero from '../section-hero';
import SectionHeroCarousel from '../section-hero-carousel';
import { heroFixtures, carouselFixtures } from '../__fixtures__/section-hero.fixtures';

// Mock next/image and necessary dependencies
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />;
  },
}));

// Mock Carousel component
jest.mock('../Carousel/carousel', () => {
  return jest.fn(({ items, renderItem }) => (
    <div data-testid="mock-carousel">
      {items.map((item: any, index: number) => (
        <div key={index} data-testid={`carousel-slide-${index}`}>
          {renderItem(item, index)}
        </div>
      ))}
    </div>
  ));
});

describe('SectionHero Component', () => {
  test('renders default hero with correct elements', () => {
    render(<SectionHero {...heroFixtures.default} />);
    
    // Check for title
    expect(screen.getByText('ศูนย์รวมอุปกรณ์ไฟฟ้าครบวงจร')).toBeInTheDocument();
    
    // Check for subtitle
    expect(screen.getByText('แสงธรณเนครินทร์')).toBeInTheDocument();
    
    // Check for description (partial text)
    expect(screen.getByText(/เราจำหน่ายอุปกรณ์ไฟฟ้าคุณภาพสูง/)).toBeInTheDocument();
    
    // Check for buttons
    expect(screen.getByText('เลือกซื้อสินค้าของเรา')).toBeInTheDocument();
    expect(screen.getByText('ติดต่อเรา')).toBeInTheDocument();
  });

  test('renders banner variant correctly', () => {
    render(<SectionHero {...heroFixtures.banner} />);
    
    // Check for title
    expect(screen.getByText('หมวดหมู่ทั้งหมด')).toBeInTheDocument();
    
    // Check for breadcrumb
    expect(screen.getByText('หน้าแรก')).toBeInTheDocument();
  });

  test('renders centered variant correctly', () => {
    render(<SectionHero {...heroFixtures.centered} />);
    
    // Check for title
    expect(screen.getByText('โคมไฟและหลอดไฟ')).toBeInTheDocument();
    
    // Check for description (partial text)
    expect(screen.getByText(/เปลี่ยนบรรยากาศที่บ้านและที่ทำงานของคุณ/)).toBeInTheDocument();
    
    // Check for button
    expect(screen.getByText('ขอใบเสนอราคา')).toBeInTheDocument();
  });

  test('renders card variant with stats correctly', () => {
    render(<SectionHero {...heroFixtures.card} />);
    
    // Check for title
    expect(screen.getByText('BANGKOK CABLE')).toBeInTheDocument();
    
    // Check for subtitle
    expect(screen.getByText('ระบบไฟฟ้าและความปลอดภัย')).toBeInTheDocument();
    
    // Check for stats
    expect(screen.getByText('100%')).toBeInTheDocument();
    expect(screen.getByText('382')).toBeInTheDocument();
    expect(screen.getByText('38k')).toBeInTheDocument();
    
    // Check for stat labels
    expect(screen.getByText('การดูแลลูกค้า')).toBeInTheDocument();
    expect(screen.getByText('รายการสินค้า')).toBeInTheDocument();
    expect(screen.getByText('ขายแล้วทั้งหมด')).toBeInTheDocument();
  });

  test('renders features variant with stats correctly', () => {
    render(<SectionHero {...heroFixtures.features} />);
    
    // Check for title
    expect(screen.getByText('ครอบคลุมเรื่องไฟฟ้าให้เราดูแลคุณ')).toBeInTheDocument();
    
    // Check for stats
    expect(screen.getByText('40,000+')).toBeInTheDocument();
    expect(screen.getByText('100%')).toBeInTheDocument();
    expect(screen.getByText('20+')).toBeInTheDocument();
    
    // Check for stat labels
    expect(screen.getByText('สินค้าในคลัง')).toBeInTheDocument();
    expect(screen.getByText('ความพึงพอใจลูกค้า')).toBeInTheDocument();
    expect(screen.getByText('ประสบการณ์')).toBeInTheDocument();
  });

  test('custom children are rendered correctly', () => {
    render(
      <SectionHero
        variant="custom"
        title="Test Title"
        backgroundColor="#000"
      >
        <div data-testid="custom-content">Custom Content</div>
      </SectionHero>
    );
    
    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('Custom Content')).toBeInTheDocument();
  });

  test('renders with correct background color', () => {
    const { container } = render(
      <SectionHero
        variant="default"
        title="Test Title"
        backgroundColor="#ff0000"
      />
    );
    
    const bgElement = container.querySelector('.absolute.inset-0.z-0');
    expect(bgElement).toHaveStyle('background-color: #ff0000');
  });

  test('renders buttons with correct href attributes', () => {
    render(<SectionHero {...heroFixtures.default} />);
    
    const primaryButton = screen.getByText('เลือกซื้อสินค้าของเรา').closest('a');
    const secondaryButton = screen.getByText('ติดต่อเรา').closest('a');
    
    expect(primaryButton).toHaveAttribute('href', '/products');
    expect(secondaryButton).toHaveAttribute('href', '/contact');
  });
});

describe('SectionHeroCarousel Component', () => {
  test('renders carousel with correct slides', () => {
    render(
      <SectionHeroCarousel
        slides={carouselFixtures}
        height="lg"
        backgroundStyle="wave"
        contentPosition="left"
      />
    );
    
    // Check if carousel component is rendered
    expect(screen.getByTestId('mock-carousel')).toBeInTheDocument();
    
    // Check if all slides are present
    expect(screen.getByTestId('carousel-slide-0')).toBeInTheDocument();
    expect(screen.getByTestId('carousel-slide-1')).toBeInTheDocument();
    expect(screen.getByTestId('carousel-slide-2')).toBeInTheDocument();
    
    // Check content of first slide
    expect(screen.getByText('ศูนย์รวมอุปกรณ์ไฟฟ้าครบวงจร')).toBeInTheDocument();
    expect(screen.getByText('แสงธรณเนครินทร์')).toBeInTheDocument();
    
    // Check content of second slide
    expect(screen.getByText('โปรโมชั่นพิเศษประจำเดือน')).toBeInTheDocument();
    expect(screen.getByText('ลดสูงสุด 50%')).toBeInTheDocument();
    
    // Check content of third slide
    expect(screen.getByText('อุปกรณ์คุณภาพจากแบรนด์ชั้นนำ')).toBeInTheDocument();
    expect(screen.getByText('พันธมิตรของเรา')).toBeInTheDocument();
  });

  test('renders Schema.org structured data', () => {
    const { container } = render(
      <SectionHeroCarousel
        slides={carouselFixtures}
        height="lg"
        backgroundStyle="wave"
        contentPosition="left"
      />
    );
    
    const scriptElement = container.querySelector('script[type="application/ld+json"]');
    expect(scriptElement).toBeInTheDocument();
    
    const scriptContent = JSON.parse(scriptElement?.textContent || '');
    expect(scriptContent['@context']).toBe('https://schema.org');
    expect(scriptContent['@type']).toBe('ItemList');
    expect(scriptContent.itemListElement.length).toBe(carouselFixtures.length);
  });

  test('renders buttons with correct text and href', () => {
    render(
      <SectionHeroCarousel
        slides={carouselFixtures}
        height="lg"
        backgroundStyle="wave"
        contentPosition="left"
      />
    );
    
    // Check buttons from first slide
    const primaryButton = screen.getByText('เลือกซื้อสินค้าของเรา').closest('a');
    const secondaryButton = screen.getByText('ติดต่อเรา').closest('a');
    
    expect(primaryButton).toHaveAttribute('href', '/products');
    expect(secondaryButton).toHaveAttribute('href', '/contact');
    
    // Check button from second slide
    const shopButton = screen.getByText('ช้อปเลย').closest('a');
    expect(shopButton).toHaveAttribute('href', '/promotion');
  });

  test('applies correct height class', () => {
    const { container } = render(
      <SectionHeroCarousel
        slides={carouselFixtures}
        height="xl"
        backgroundStyle="wave"
        contentPosition="left"
      />
    );
    
    const slideElements = screen.getAllByText(/ศูนย์รวมอุปกรณ์ไฟฟ้าครบวงจร|โปรโมชั่นพิเศษประจำเดือน|อุปกรณ์คุณภาพจากแบรนด์ชั้นนำ/);
    
    // Get the closest ancestor with hero-slide class
    const slideContainer = slideElements[0].closest('.hero-slide');
    expect(slideContainer).toHaveClass('min-h-[600px]');
  });
});