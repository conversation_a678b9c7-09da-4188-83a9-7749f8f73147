import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import { ContentWrapper } from '@/components/rendering-examples/ContentWrapper';
import Link from 'next/link';

// This page will be generated at build time
export default async function StaticSiteGeneratedPage() {
  // This function will be executed at build time
  const generateStaticData = async () => {
    // Used for static generation, no delays needed for build process
    return {
      buildTime: new Date().toISOString(),
      randomValue: Math.floor(Math.random() * 1000),
    };
  };

  // Function to fetch example data for the "with-data" route that would be added later
  const generateDetailedUserData = async () => {
    return {
      users: [
        { id: 1, name: '<PERSON>', role: 'Developer', lastActive: new Date().toISOString() },
        { id: 2, name: '<PERSON>', role: 'Designer', lastActive: new Date().toISOString() },
        {
          id: 3,
          name: '<PERSON>',
          role: 'Product Manager',
          lastActive: new Date().toISOString(),
        },
      ],
      systemStatus: {
        cpu: Math.floor(Math.random() * 100) + '%',
        memory: Math.floor(Math.random() * 100) + '%',
        uptime: Math.floor(Math.random() * 10000) + ' minutes',
      },
      timestamp: new Date().toISOString(),
    };
  };

  // Data is fetched once at build time
  const staticData = await generateStaticData();
  const _detailedData = await generateDetailedUserData();

  // Create the basic content for the SSG page
  const basicContent = (
    <>
      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">How SSG Works</h2>
        <p className="mb-4">
          With Static Site Generation, the HTML for this page is generated at{' '}
          <strong>build time</strong>. This means all users see the same content, which was
          generated when the site was built.
        </p>
        <p>
          This is ideal for pages with content that doesn&apos;t change frequently, such as blog
          posts, documentation, or marketing pages. SSG provides the fastest page loads and
          excellent SEO.
        </p>
      </div>

      <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
        <h2 className="mb-4 text-xl font-semibold">Build-Time Generated Data</h2>
        <p className="mb-2">This data was generated at build time, not at request time:</p>

        <div className="bg-card mb-4 rounded border p-4">
          <p>
            <strong>Build Time:</strong> {staticData.buildTime}
          </p>
          <p>
            <strong>Random Value:</strong> {staticData.randomValue}
          </p>
        </div>

        <p className="text-sm">
          Refreshing the page will not change these values, as they were generated when the site was
          built, not when a user visits the page.
        </p>
      </div>

      <div className="bg-muted rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">Implementation</h2>
        <p className="mb-4">
          In Next.js 15 with App Router, you can implement SSG by using static data fetching in a
          page component:
        </p>

        <pre className="bg-card overflow-x-auto rounded-lg p-4 text-sm">
          {`// This is a server component with static data fetching
export default async function Page() {
  // Data fetching happens once at build time
  const data = await fetchData();
  
  return (
    <div>
      <h1>Static Generated Page</h1>
      <p>Data: {data}</p>
    </div>
  );
}

// Enable SSG build mode with Next.js
// This works with NEXT_OUTPUT_MODE=export`}
        </pre>
      </div>

      <div className="mt-8 flex flex-col gap-4 sm:flex-row">
        <Link
          href="/rendering-examples/ssg/with-data"
          className="bg-primary/5 hover:bg-primary/10 border-primary/20 flex-1 rounded-lg border px-4 py-3 text-center font-medium"
        >
          SSG with Detailed Data →
        </Link>
        <Link
          href="/rendering-examples/ssg/advanced"
          className="bg-primary/5 hover:bg-primary/10 border-primary/20 flex-1 rounded-lg border px-4 py-3 text-center font-medium"
        >
          Advanced SSG Example →
        </Link>
        <Link
          href="/rendering-examples/ssg/router-example"
          className="bg-primary hover:bg-primary/90 flex-1 rounded-lg px-4 py-3 text-center font-medium text-white"
        >
          SSG Router Example →
        </Link>
      </div>
    </>
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="ssg" />
      <main className="container mx-auto px-4 pb-12">
        <ContentWrapper defaultContent={basicContent} />
      </main>
    </div>
  );
}
