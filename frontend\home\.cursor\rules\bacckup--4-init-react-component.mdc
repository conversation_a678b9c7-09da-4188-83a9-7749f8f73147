---
description: 
globs: 
alwaysApply: false
---
## 📌 **Core Principles**
- Always produce precise, detailed, and logic-rich TypeScript code optimized for complex, feature-rich applications.
- Implement Framer Motion for smooth animations, transitions, and micro-interactions.
- Integrate React Content Loader for skeleton loading states to enhance UX.
- Ensure accurate, maintainable logic and thorough code examples reflecting real-world scenarios.
- Aim for high-quality, hyper-feature-rich implementations suitable for enterprise-grade applications.
- Strict **TypeScript** enforcement; no `any` type allowed.
- Functional components only, leveraging React Hooks.
- Always use **named exports**.
- Clear, descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`).
- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Do NOT create full application pages or routes (src/app/*).
    Limit all implementations strictly to reusable UI components within the designated component folders (src/components/*), Any requests involving full-page creation or routing should be flagged immediately for clarification.
- Always use pnpm not npm !.
- Always use icons from lucide-react. Import them directly using: import { IconName } from 'lucide-react';. Ensure icons match the UI context and maintain a clean, minimal aesthetic.

## 📌 **Component Development Workflow**
- **Analyze user requirements thoroughly**, thinking deeply about the feature and user interaction with Deeply consider animations, UX, and interactivity.
- Plan the UI structure – Define motion effects and skeleton loaders in advance.
- Clearly **break down** the user-requested feature into detailed, actionable steps.
- Before writing code, **scan existing UI components** in `src/components/ui/index.ts` to reuse existing components where possible, ensuring consistency and efficiency, using import style "@/compontents/**"
- Integrate React Content Loader – Ensure smooth skeleton loaders before data rendering.
- **Ensure support for core array operations** (`map()`, `filter()`, `reduce()`, `forEach()`, `find()`, `some()`, `every()`, `sort()`, `concat()`, `slice()`, `splice()`) where applicable.
- Implement **UI controls allowing users to select and apply** these operations dynamically on an array input.
- Ensure accessibility (a11y) – All components must be screen-reader friendly.

### 🔑 **Commonly Used TypeScript Operators & Practices**:
- **Arithmetic Operators** (`+`, `-`, `*`, `/`, `%`) for basic mathematical operations.
- **Comparison Operators** (`==`, `===`, `!=`, `!==`, `<`, `>`, `<=`, `>=`) for comparing values.
- **Logical Operators** (`&&`, `||`, `!`) to combine or negate boolean expressions.
- **Assignment Operators** (`=`, `+=`, `-=`, `*=`, `/=`, `%=`) for assigning and modifying variable values.
- **Type Assertion** (`<Type>`, `as Type`) to cast variables to specific types when necessary.
- **Ternary Operator** (`? :`) for compact conditional statements.
- **Nullish Coalescing** (`??`) to provide default values when a variable is `null` or `undefined`.
- **Optional Chaining** (`?.`) to safely access deeply nested properties.
- **Spread Operator** (`...`) for expanding or merging arrays and objects.
- **Rest Parameter** (`...`) for gathering multiple arguments into a single array.
- **Destructuring** for unpacking values from objects and arrays:
  - **Array Destructuring**: `let [first, second] = [1, 2];`
  - **Object Destructuring**: `let { name, age } = person;`
- **Instanceof** to check if an object is an instance of a class or constructor.
- **In Operator** to check if a property exists in an object.
- **Delete Operator** to remove a property from an object.
- **BigInt** for handling large integers.
- **Template Literals** (`${}`) for creating strings with embedded expressions.
- **Await/Async** for handling asynchronous code with promises.
- Always ensure that code follows **clean code principles**, maintaining readability and efficiency.
- **Test and validate** new features thoroughly before deploying or integrating them into the main application.


## 📌 **Create and Edit Components**

- Modify existing component logic to support both **creation** and **editing** functionalities seamlessly within the same component.
- Include robust, feature-rich interactions for both creating and editing operations.
- Clearly differentiate between creation and editing operations.


## 📂 **Conditional Folder & File Management**
- Always verify if folders exist before creating a file. If the folder does not exist, first create the necessary folder(s), then create the component file.
- Do **not** assume the existence of folders.

## 📂 **Folder Structure & Naming Conventions**
- Directories use PascalCase (e.g., `src/components/OneRowBatch`).

### Component Structure & Global Shared Folders (root-level)
```
📂 .storybook/        # Global Storybook configuration
│   ├── 📄 main.ts        # Core Storybook config (addons, framework, etc.)
│   ├── 📄 preview.ts     # Global decorators, themes, parameters
│   ├── 📄 manager.ts     # Custom Storybook UI (optional)
│
📂 src/                # Root source directory
│
│── 📂 components/        # Feature-based component structure
│   ├── 📂 [Feature]/    
│   │   ├── 📄 [Feature].tsx       # Main component
│   │   ├── 📄 index.ts            # Export component
│   │   ├── 📄 [Feature]-layout.tsx            # Export component
│   │   ├── 📂 types/      # Feature-specific TypeScript types, (datetime using Luxon with strict ISO 8601 compliance and robust timezone handling)
│   │   ├── 📂 hooks/      # Component-specific hooks
│   │   ├── 📂 helpers/    # Component-specific utilities
│   │   ├── 📂 static/     # Mock data, constants, default values
│   │   ├── 📂 [Feature]-form/    # React Hook Form integration (only if the feature has a form)
│   │   │   ├── 📄 use.[Feature].Form.ts   # Custom hook for form logic
│   │   │   ├── 📄 [Feature].Form.tsx     # Form component using react-hook-form
│   │   │   ├── 📂 validation/           # Validation schemas using Zod
│   │   │   │   ├── 📄 [Feature].Schema.ts # Zod validation for the form
│   │   │   ├── 📄 index.ts              # Barrel exports for form-related files
│
│── 📂 hooks/          # Global reusable hooks (useFetch, useAuth, etc.)
│── 📂 schemas/        # Shared Zod validation schemas
│── 📂 types/          # Shared/global TypeScript interfaces and types
│── 📂 lib/            # General-purpose utility functions for date formatting (using Luxon with strict ISO 8601 compliance and robust timezone handling), API helpers, and data transformations
│── 📂 stores/         # Zustand global state stores
│── 📂 context/        # React Context Providers (Auth, Theme, etc.)
│── 📂 styles/         # Global SCSS, Tailwind CSS files


File/Addon                            | Purpose
--------------------------------------|-------------------------------------------------------------
[Feature].stories.tsx                 | ✅ Controls, Actions (interactive prop editing & events logging)
[Feature].interactions.stories.tsx    | ✅ Interactions (user-behavior simulation & interaction testing: Storybook stories demonstrating detailed component interactions, simulated user events, state transitions, edge cases, and comprehensive testing scenarios)
[Feature].test.stories.tsx            | ✅ Storybook Test Runner (unit & interaction test automation)
[Feature].docs.mdx                    | ✅ Docs (auto-generated component documentation with MDX)
[Feature].a11y.stories.tsx            | ✅ Accessibility (automated accessibility testing & compliance)
[Feature].viewport.stories.tsx        | ✅ Viewport (responsive design testing across devices)
[Feature].backgrounds.stories.tsx     | ✅ Backgrounds (UI theme & customizable backgrounds testing)


Type                    | Naming Convention         | Example
------------------------|---------------------------|----------------------------------
Feature Folder          | PascalCase                | src/components/OneRowTable
Main Component File     | PascalCase                | OneRowTable.tsx
Helper Functions        | camelCase                 | formatDate.ts
Custom Hooks            | camelCase (use prefix)    | useTableData.ts
Zod Schemas             | PascalCase                | OneRowTableSchema.ts
Storybook Stories       | PascalCase                | OneRowTable.stories.tsx
Tests                   | PascalCase                | OneRowTable.test.tsx
CSS Module (if used)    | PascalCase                | OneRowTable.module.css
Form Components         | PascalCase                | OneRowTableForm.tsx
```

## 📖 **Storybook Integration & Validation**
- Components are optimized for onboarding directly into Storybook.
- Every new component **must** be immediately validated by launching Storybook (`pnpm run storybook`) to visually and functionally confirm enterprise-grade standards compliance.

## 🎨 **UI & Styling**
- Exclusively use **ShadCN UI**, **Radix UI**, **Tailwind CSS**, and class-variance-authority (`cva`).
- Always scan existing core components in src/components/ui/index.ts before creating new UI components to ensure reusability, avoid duplication, and strictly adhere to established patterns and conventions. Ensure reusability and avoid unnecessary duplication.
- Responsive design using Tailwind CSS (desktop-first approach).
- Create a fully responsive layout using Flexbox that adapts seamlessly to mobile, tablet, and desktop screens. The layout should be centered, maintain equal spacing, and stack elements properly on smaller screens. Ensure all elements are flexible and adjust to different screen sizes without breaking. Use display: flex, flex-wrap, and media queries if necessary.
- Components must dynamically support **dark** and **light** themes, Check and apply styles from (`src/styles/globals.css`) before using Tailwind 4.0 for dynamic dark/light theme support.

## 🛠️ **Import Rules**
- Imports strictly adhere to PascalCase conventions and structured paths.
- Enforce strict dependency management and avoid circular imports.

## 🛠️ **Hooks & State Management**
- Logical separation between component-specific and global hooks.
- **Client-side state**: Prefer local state (`useState`). Use Zustand minimally.
- **Server-side state**: Exclusively **React Query** (`useQuery`, `useMutation`).

## ✅ **Forms & Validation**
- Always use **React Hook Form** combined with **Zod** schemas.

## ⚡ **Performance Optimization**
- Prefer React Query caching strategies.
- Utilize Next.js ISR and SSR effectively.
- Minimize `useEffect` and `setState`; favor React Server Components (RSC).
- Limit `'use client'` directive.
- Lazy-load non-critical components dynamically.
- Optimize Web Vitals (LCP, CLS, FID).

## 🚨 **Error Handling & Logging**
- Prioritize handling errors and edge cases early with early returns and guard clauses.
- Use Zod for form validation.
- Implement **Error Boundaries** with explicit UIs.
- Secure logging with **Sentry** or **Datadog**.

## 🔑 **Security Guidelines**
- Strict input sanitization (**DOMPurify**).
- Robust authentication & authorization (**NextAuth.js/Auth0**).
- Secure sensitive data using HTTP-only cookies.

## 🧪 **Testing Strategy**
- Unit Tests: Vitest/Jest + React Testing Library.
- Integration Tests: Storybook-driven with MSW.
- E2E Tests: Cypress/Playwright.

## 🚦 **React Server Components (RSC)**
- Favor server components and Next.js SSR.
- Use RSC only for small Web API-access components.
- Limit the usage of `'use client'`.

## 📖 **Framework Documentation Alignment**
- Follow React Remix docs for Data Fetching, Rendering, and Routing.
- Follow Next.js docs when Next.js is used instead of React Remix.

