import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { composeStories } from '@storybook/react';
import * as stories from '../__stories__/modal.stories';
import { Modal } from '../modal';

// Compose stories for testing
const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } = composeStories(stories);

describe('Modal', () => {
  it('renders correctly with default props', () => {
    const handleClose = jest.fn();
    render(
      <Modal 
        isOpen={true} 
        onClose={handleClose} 
        title="Test Modal" 
        description="Test Description"
      >
        Test Content
      </Modal>
    );
    
    expect(screen.getByTestId('modal-content')).toBeInTheDocument();
    expect(screen.getByTestId('modal-title')).toHaveTextContent('Test Modal');
    expect(screen.getByTestId('modal-description')).toHaveTextContent('Test Description');
    expect(screen.getByTestId('modal-body')).toHaveTextContent('Test Content');
    expect(screen.getByTestId('modal-close-button')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', async () => {
    const handleClose = jest.fn();
    render(
      <Modal isOpen={true} onClose={handleClose} title="Test Modal">
        Test Content
      </Modal>
    );
    
    fireEvent.click(screen.getByTestId('modal-close-button'));
    await waitFor(() => {
      expect(handleClose).toHaveBeenCalledTimes(1);
    });
  });

  it('renders with different sizes', () => {
    const { rerender } = render(
      <Modal isOpen={true} onClose={() => {}} size="sm">
        Small Modal
      </Modal>
    );
    expect(screen.getByTestId('modal-content')).toHaveClass('sm:max-w-sm');
    
    rerender(
      <Modal isOpen={true} onClose={() => {}} size="lg">
        Large Modal
      </Modal>
    );
    expect(screen.getByTestId('modal-content')).toHaveClass('sm:max-w-xl');
    
    rerender(
      <Modal isOpen={true} onClose={() => {}} size="xl">
        Extra Large Modal
      </Modal>
    );
    expect(screen.getByTestId('modal-content')).toHaveClass('sm:max-w-2xl');
    
    rerender(
      <Modal isOpen={true} onClose={() => {}} size="full">
        Full Width Modal
      </Modal>
    );
    expect(screen.getByTestId('modal-content')).toHaveClass('sm:max-w-full');
  });

  it('renders with different positions', () => {
    const { rerender } = render(
      <Modal isOpen={true} onClose={() => {}} position="top">
        Top Modal
      </Modal>
    );
    expect(screen.getByTestId('modal-content')).toHaveClass('data-[state=open]:slide-in-from-top-[2%]');
    
    rerender(
      <Modal isOpen={true} onClose={() => {}} position="center">
        Center Modal
      </Modal>
    );
    expect(screen.getByTestId('modal-content')).toHaveClass('data-[state=open]:zoom-in-90');
  });

  it('does not render close button when showCloseButton is false', () => {
    render(
      <Modal isOpen={true} onClose={() => {}} showCloseButton={false}>
        No Close Button
      </Modal>
    );
    
    expect(screen.queryByTestId('modal-close-button')).not.toBeInTheDocument();
  });

  it('does not render header when title and description are not provided', () => {
    render(
      <Modal isOpen={true} onClose={() => {}}>
        No Header
      </Modal>
    );
    
    expect(screen.queryByTestId('modal-title')).not.toBeInTheDocument();
    expect(screen.queryByTestId('modal-description')).not.toBeInTheDocument();
  });

  it('renders footer when provided', () => {
    const footer = <div data-testid="custom-footer">Footer Content</div>;
    render(
      <Modal isOpen={true} onClose={() => {}} footer={footer}>
        With Footer
      </Modal>
    );
    
    expect(screen.getByTestId('modal-footer')).toBeInTheDocument();
    expect(screen.getByTestId('custom-footer')).toBeInTheDocument();
    expect(screen.getByTestId('custom-footer')).toHaveTextContent('Footer Content');
  });

  // Test composed stories
  it('renders Default story correctly', () => {
    render(<Default />);
    
    // Check if trigger button is rendered
    const button = screen.getByText('Open Modal');
    expect(button).toBeInTheDocument();
    
    // Open modal
    fireEvent.click(button);
    
    // Check if modal content is rendered
    expect(screen.getByText('Example Modal')).toBeInTheDocument();
    expect(screen.getByText('This is a description of the modal content')).toBeInTheDocument();
    expect(screen.getByText('This is the content of the modal. You can put any React components here.')).toBeInTheDocument();
  });

  it('renders NoCloseButton story correctly', () => {
    render(<NoCloseButton />);
    
    // Open modal
    fireEvent.click(screen.getByText('Open Modal'));
    
    // Check that close button is not present
    const closeButtons = screen.queryAllByRole('button', { name: /close/i });
    const closeIcons = document.querySelectorAll('svg');
    expect(closeButtons.length).toBe(0);
    expect(closeIcons.length).toBe(0);
  });

  it('renders NoHeader story correctly', () => {
    render(<NoHeader />);
    
    // Open modal
    fireEvent.click(screen.getByText('Open Modal'));
    
    // Check that title and description are not present
    expect(screen.queryByText(/Example Modal/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/This is a description/i)).not.toBeInTheDocument();
    
    // But content is present
    expect(screen.getByText('This modal has no title or description.')).toBeInTheDocument();
  });

  it('renders NoFooter story correctly', () => {
    render(<NoFooter />);
    
    // Open modal
    fireEvent.click(screen.getByText('Open Modal'));
    
    // Check that footer buttons are not present
    expect(screen.queryByRole('button', { name: /cancel/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /confirm/i })).not.toBeInTheDocument();
  });

  // Accessibility tests
  it('meets accessibility requirements', async () => {
    render(
      <Modal 
        isOpen={true} 
        onClose={() => {}} 
        title="Accessible Modal" 
        description="This modal is accessible"
      >
        Content for screen readers
      </Modal>
    );
    
    // Basic a11y checks
    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();
    
    // Check for proper labelling
    expect(dialog).toHaveAttribute('aria-labelledby');
    expect(dialog).toHaveAttribute('aria-describedby');
    
    // Check close button has accessible name
    const closeButton = screen.getByTestId('modal-close-button');
    expect(closeButton).toHaveTextContent('Close');
  });
});