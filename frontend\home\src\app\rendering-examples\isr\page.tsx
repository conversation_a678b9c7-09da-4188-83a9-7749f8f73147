import Link from 'next/link';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';

// This function will be executed at build time and periodically afterward
async function getRevalidatedData() {
  // Artificial delay to simulate data fetching
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    currentTime: new Date().toISOString(),
    randomValue: Math.floor(Math.random() * 1000),
    lastRevalidated: new Date().toISOString(),
  };
}

// Set the revalidation period to 30 seconds
export const revalidate = 30;

export default async function IncrementalStaticRegenerationPage() {
  // Data is refreshed at most every 30 seconds
  const data = await getRevalidatedData();

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="isr" />

      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">Incremental Static Regeneration (ISR)</h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">How ISR Works</h2>
          <p className="mb-4">
            ISR combines the benefits of static generation (SSG) and server-side rendering (SSR).
            This page is statically generated at build time,{' '}
            <strong>but revalidated periodically</strong>.
          </p>
          <p>
            This allows you to update static content without rebuilding your entire site. It&apos;s
            perfect for pages that need fresh data, but don&apos;t change with every request.
          </p>
        </div>

        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">Revalidated Data</h2>
          <p className="mb-2">This data was generated at build time or during revalidation:</p>

          <div className="bg-card mb-4 rounded border p-4">
            <p>
              <strong>Current Time:</strong> {data.currentTime}
            </p>
            <p>
              <strong>Random Value:</strong> {data.randomValue}
            </p>
            <p>
              <strong>Last Revalidated:</strong> {data.lastRevalidated}
            </p>
          </div>

          <p className="text-sm">
            This page automatically revalidates every 30 seconds. Refresh the page after that period
            to see updated values.
          </p>
        </div>

        <div className="bg-muted rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">Implementation</h2>
          <p className="mb-4">
            In Next.js 15, you can enable ISR by setting the <code>revalidate</code> property.
          </p>

          <pre className="bg-card overflow-x-auto rounded-lg p-4 text-sm">
            {`// Set revalidation time in seconds
export const revalidate = 30;

// You can also control revalidation per-fetch
async function fetchData() {
  const res = await fetch('https://api.example.com/data', {
    next: { revalidate: 60 } // Revalidate every 60 seconds
  });
  return res.json();
}

export default async function Page() {
  const data = await fetchData();
  
  return (
    <div>
      <h1>Incremental Static Regeneration</h1>
      <p>Data: {JSON.stringify(data)}</p>
      <p>Last Updated: {new Date().toISOString()}</p>
    </div>
  );
}`}
          </pre>
        </div>

        <div className="mt-8 flex flex-col gap-4 sm:flex-row">
          <Link
            href="/rendering-examples/isr/with-data"
            className="bg-primary/5 hover:bg-primary/10 border-primary/20 flex-1 rounded-lg border px-4 py-3 text-center font-medium"
          >
            ISR with Detailed Data →
          </Link>
          <Link
            href="/rendering-examples/isr/advanced"
            className="bg-primary hover:bg-primary/90 flex-1 rounded-lg px-4 py-3 text-center font-medium text-white"
          >
            Advanced ISR Example →
          </Link>
        </div>
      </main>
    </div>
  );
}
