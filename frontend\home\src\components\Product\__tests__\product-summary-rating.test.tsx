import { render, screen, fireEvent } from '@testing-library/react';
import { ProductSummaryRating } from '../product-summary-rating';

describe('ProductSummaryRating', () => {
  const defaultProps = {
    averageRating: 4.5,
    totalReviews: 100,
    ratingDistribution: {
      5: 60,
      4: 30,
      3: 5,
      2: 3,
      1: 2,
    },
  };

  test('renders the component correctly', () => {
    render(<ProductSummaryRating {...defaultProps} />);
    
    // Test for average rating display
    expect(screen.getByText('4.5')).toBeInTheDocument();
    
    // Test for total reviews display
    expect(screen.getByText('100 reviews')).toBeInTheDocument();
    
    // Test for rating distribution
    expect(screen.getByText('60')).toBeInTheDocument(); // 5-star count
    expect(screen.getByText('30')).toBeInTheDocument(); // 4-star count
  });

  test('handles loading state correctly', () => {
    render(<ProductSummaryRating {...defaultProps} isLoading={true} />);
    
    // Check for loading state
    expect(screen.getByTestId('loading-placeholder')).toBeInTheDocument();
  });

  test('handles no reviews state correctly', () => {
    render(
      <ProductSummaryRating
        averageRating={0}
        totalReviews={0}
        ratingDistribution={{ 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }}
      />
    );
    
    // Check for no reviews message
    expect(screen.getByText('No reviews yet')).toBeInTheDocument();
  });

  test('handles rating click', () => {
    const mockOnRatingClick = jest.fn();
    render(<ProductSummaryRating {...defaultProps} onRatingClick={mockOnRatingClick} />);
    
    // Click on 5-star rating bar
    fireEvent.click(screen.getByText('5').closest('div')!);
    
    // Check if click handler was called with correct rating
    expect(mockOnRatingClick).toHaveBeenCalledWith(5);
  });

  test('renders different variants correctly', () => {
    const { rerender } = render(<ProductSummaryRating {...defaultProps} variant="compact" />);
    
    // Check compact variant has appropriate classes
    expect(screen.getByText('4.5').closest('div')).toHaveClass('flex items-center gap-4');
    
    // Rerender with detailed variant
    rerender(<ProductSummaryRating {...defaultProps} variant="detailed" />);
    
    // Check detailed variant shows percentages
    expect(screen.getByText('60%')).toBeInTheDocument();
  });
});