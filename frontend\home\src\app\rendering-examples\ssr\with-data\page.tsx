import Link from 'next/link';
import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import { createTranslation } from '@/components/Providers/server-i18n';

// Function to fetch some data for the "with-data" route
async function getDetailedUserData() {
  await new Promise((resolve) => setTimeout(resolve, 800));

  return {
    users: [
      { id: 1, name: '<PERSON>', role: '<PERSON>elo<PERSON>', lastActive: new Date().toISOString() },
      { id: 2, name: '<PERSON>', role: 'Designer', lastActive: new Date().toISOString() },
      {
        id: 3,
        name: '<PERSON>',
        role: 'Product Manager',
        lastActive: new Date().toISOString(),
      },
    ],
    systemStatus: {
      cpu: Math.floor(Math.random() * 100) + '%',
      memory: Math.floor(Math.random() * 100) + '%',
      uptime: Math.floor(Math.random() * 10000) + ' minutes',
    },
    timestamp: new Date().toISOString(),
  };
}

export default async function SSRWithDataPage() {
  // Use the centralized translation utility
  const { t } = await createTranslation('common');

  // Fetch data on the server for this specific route
  const detailedData = await getDetailedUserData();

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="ssr" />

      <main className="container mx-auto px-4 pb-12">
        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">{t('rendering.ssrDataFetch')}</h2>
          <p className="mb-4">{t('rendering.ssrDataFetchDescription')}</p>

          <div className="overflow-x-auto">
            <table className="bg-card w-full overflow-hidden rounded-md border">
              <thead className="bg-muted">
                <tr>
                  <th className="px-4 py-2 text-left">{t('rendering.userId')}</th>
                  <th className="px-4 py-2 text-left">{t('rendering.userName')}</th>
                  <th className="px-4 py-2 text-left">{t('rendering.userRole')}</th>
                  <th className="px-4 py-2 text-left">{t('rendering.userLastActive')}</th>
                </tr>
              </thead>
              <tbody>
                {detailedData.users.map((user) => (
                  <tr key={user.id} className="border-t">
                    <td className="px-4 py-2">{user.id}</td>
                    <td className="px-4 py-2">{user.name}</td>
                    <td className="px-4 py-2">{user.role}</td>
                    <td className="px-4 py-2">{new Date(user.lastActive).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">{t('rendering.systemStatus')}</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">{t('rendering.cpuUsage')}</h3>
              <p className="text-2xl">{detailedData.systemStatus.cpu}</p>
            </div>
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">{t('rendering.memoryUsage')}</h3>
              <p className="text-2xl">{detailedData.systemStatus.memory}</p>
            </div>
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">{t('rendering.uptime')}</h3>
              <p className="text-2xl">{detailedData.systemStatus.uptime}</p>
            </div>
          </div>
          <p className="mt-4 text-xs">
            {t('rendering.generatedAt')}: {detailedData.timestamp}
          </p>
        </div>

        <div className="mt-6 flex flex-wrap gap-3">
          <Link
            href="/rendering-examples/ssr"
            className="bg-secondary text-secondary-foreground hover:bg-secondary/90 rounded-md px-4 py-2 transition-colors"
          >
            {t('rendering.basicExample')}
          </Link>
          <Link
            href="/rendering-examples/ssr/advanced"
            className="bg-secondary text-secondary-foreground hover:bg-secondary/90 rounded-md px-4 py-2 transition-colors"
          >
            {t('rendering.advancedExample')}
          </Link>
          <Link
            href="/rendering-examples/ssr/router-example"
            className="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 transition-colors"
          >
            {t('rendering.viewRouterExample')}
          </Link>
        </div>
      </main>
    </div>
  );
}
