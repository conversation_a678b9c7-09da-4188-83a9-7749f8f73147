import{r as l,b as o,j as a,L as p}from"./index-Bwql5Dzz.js";import{T as c}from"./Trans-VWqfqpAH.js";var d=Object.defineProperty,s=Object.getOwnPropertySymbols,m=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,i=(e,r,t)=>r in e?d(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,h=(e,r)=>{for(var t in r)m.call(r,t)&&i(e,t,r[t]);if(s)for(var t of s(r))f.call(r,t)&&i(e,t,r[t]);return e},x=(e,r)=>{var t={};for(var n in e)m.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&s)for(var n of s(e))r.indexOf(n)<0&&f.call(e,n)&&(t[n]=e[n]);return t};const g=l.forwardRef((e,r)=>{var t=e,{color:n="currentColor"}=t,u=x(t,["color"]);return l.createElement("svg",h({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:r},u),l.createElement("g",{clipPath:"url(#a)"},l.createElement("path",{fill:n,fillRule:"evenodd",d:"M2.833.75A2.53 2.53 0 0 0 .306 3.278V9.5a2.53 2.53 0 0 0 2.527 2.528h1.028v1.916a.75.75 0 0 0 1.219.586l3.128-2.502h3.959A2.53 2.53 0 0 0 14.695 9.5V3.278A2.53 2.53 0 0 0 12.167.75zM1.806 3.278c0-.568.46-1.028 1.027-1.028h9.334c.568 0 1.028.46 1.028 1.028V9.5c0 .567-.46 1.028-1.028 1.028H7.945a.75.75 0 0 0-.469.164l-2.115 1.692v-1.106a.75.75 0 0 0-.75-.75H2.833c-.567 0-1.027-.46-1.027-1.028zm4.805 3.11a.89.89 0 0 0 1.778 0 .89.89 0 0 0-1.778 0m-2.222.89a.89.89 0 0 1 0-1.778.89.89 0 0 1 0 1.778m5.333-.89a.89.89 0 0 0 1.778 0 .89.89 0 0 0-1.778 0",clipRule:"evenodd"})),l.createElement("defs",null,l.createElement("clipPath",{id:"a"},l.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});g.displayName="ChatBubble";var j=()=>{const{t:e}=o();return a.jsxs("div",{className:"flex h-[120px] flex-col items-center justify-center gap-2 p-2 text-center",children:[a.jsx("span",{className:"txt-small text-ui-fg-subtle font-medium",children:e("orders.returns.placeholders.noReturnShippingOptions.title")}),a.jsx("span",{className:"txt-small text-ui-fg-muted",children:a.jsx(c,{i18nKey:"orders.returns.placeholders.noReturnShippingOptions.hint",components:{LinkComponent:a.jsx(p,{to:"/settings/locations",className:"text-blue-500"})}})})]})},O=()=>{const{t:e}=o();return a.jsxs("div",{className:"flex h-[120px] flex-col items-center justify-center gap-2 p-2 text-center",children:[a.jsx("span",{className:"txt-small text-ui-fg-subtle font-medium",children:e("orders.returns.placeholders.outboundShippingOptions.title")}),a.jsx("span",{className:"txt-small text-ui-fg-muted",children:a.jsx(c,{i18nKey:"orders.returns.placeholders.outboundShippingOptions.hint",components:{LinkComponent:a.jsx(p,{to:"/settings/locations",className:"text-blue-500"})}})})]})};export{g as C,O,j as R};
