import React from "react";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";

export interface RatingProps {
  /**
   * คะแนนที่ต้องการแสดง (0-5)
   */
  value: number;
  /**
   * คะแนนสูงสุดที่เป็นไปได้
   */
  maxValue?: number;
  /**
   * ขนาดของดาว
   */
  size?: "sm" | "md" | "lg";
  /**
   * สีของดาวที่เติมแล้ว
   */
  filledColor?: string;
  /**
   * สีของดาวที่ยังไม่เติม
   */
  emptyColor?: string;
  /**
   * อนุญาตให้มีคะแนนเป็นเศษส่วนหรือไม่
   */
  allowFraction?: boolean;
  /**
   * การจัดวางแนวของดาว
   */
  direction?: "horizontal" | "vertical";
  /**
   * อนุญาตให้มีการคลิกเพื่อให้คะแนนหรือไม่
   */
  interactive?: boolean;
  /**
   * Event handler สำหรับเมื่อมีการเปลี่ยนคะแนน
   */
  onChange?: (value: number) => void;
  /**
   * CSS class เพิ่มเติม
   */
  className?: string;
  /**
   * อธิบายคะแนนสำหรับ screen readers
   */
  label?: string;
}

export const Rating = ({
  value = 0,
  maxValue = 5,
  size = "md",
  filledColor = "text-yellow-400",
  emptyColor = "text-gray-300",
  allowFraction = false,
  direction = "horizontal",
  interactive = false,
  onChange,
  className,
  label = `Rating: ${value} out of ${maxValue}`,
}: RatingProps) => {
  // คำนวณขนาดของดาวตาม size
  const starSize = {
    sm: 16,
    md: 24,
    lg: 32,
  }[size];

  // สร้างอาร์เรย์ของดาวตาม maxValue
  const stars = Array.from({ length: maxValue }, (_, i) => i + 1);

  // ฟังก์ชันสำหรับจัดการการคลิก
  const handleClick = (clickedValue: number) => {
    if (interactive && onChange) {
      onChange(clickedValue);
    }
  };

  // สร้าง CSS classes ตาม props
  const containerClasses = cn(
    "flex gap-1",
    direction === "vertical" ? "flex-col" : "flex-row",
    interactive && "cursor-pointer",
    className
  );

  return (
    <div 
      className={containerClasses}
      role="img" 
      aria-label={label}
    >
      {stars.map((star) => {
        // คำนวณว่าดาวนี้ควรเติมเท่าใด (0 = ไม่เติม, 0.5 = ครึ่งเดียว, 1 = เต็มดวง)
        let fillAmount = 0;
        
        if (value >= star) {
          fillAmount = 1;
        } else if (allowFraction && value > star - 1) {
          fillAmount = value - (star - 1);
        }

        return (
          <div 
            key={star} 
            onClick={() => handleClick(star)}
            className={interactive ? "cursor-pointer" : ""}
          >
            <Star
              size={starSize}
              className={fillAmount > 0 ? filledColor : emptyColor}
              fill={fillAmount > 0 ? "currentColor" : "none"}
            />
          </div>
        );
      })}
    </div>
  );
};