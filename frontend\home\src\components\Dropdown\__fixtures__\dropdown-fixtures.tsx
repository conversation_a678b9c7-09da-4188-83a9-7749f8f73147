// _fixtures_/dropdown-items.tsx
import { Dropdown } from '../dropdown';
import { User, Package, LayoutGrid, Search } from 'lucide-react';

// Common options for testing
const fruitOptions = [
  { value: 'apple', label: 'Apple' },
  { value: 'banana', label: '<PERSON><PERSON>' },
  { value: 'cherry', label: 'Cherry' },
  { value: 'durian', label: 'Durian' },
  { value: 'elderberry', label: 'Elderberry' },
];

const colorOptions = [
  { value: 'red', label: 'Red' },
  { value: 'green', label: 'Green' },
  { value: 'blue', label: 'Blue' },
  { value: 'yellow', label: 'Yellow' },
  { value: 'purple', label: 'Purple' },
];

const userOptions = [
  { value: 'user1', label: 'ธยาดา แสงสว่าง', icon: <User className="h-5 w-5 text-blue-900" /> },
  { value: 'user2', label: 'สมชาย ใจดี', icon: <User className="h-5 w-5 text-blue-900" /> },
  { value: 'user3', label: 'วราภรณ์ สมใจ', icon: <User className="h-5 w-5 text-blue-900" /> },
  { value: 'user4', label: 'ชัยวัฒน์ รุ่งโรจน์', icon: <User className="h-5 w-5 text-blue-900" /> },
];

const categoryOptions = [
  { value: 'cat1', label: 'หมวดหมู่สินค้า', icon: <LayoutGrid className="h-5 w-5 text-blue-900" /> },
  { value: 'cat2', label: 'เสื้อผ้า', icon: <Package className="h-5 w-5 text-blue-900" /> },
  { value: 'cat3', label: 'อุปกรณ์อิเล็กทรอนิกส์', icon: <Package className="h-5 w-5 text-blue-900" /> },
  { value: 'cat4', label: 'อาหารและเครื่องดื่ม', icon: <Package className="h-5 w-5 text-blue-900" /> },
];

// Basic dropdown
export const DefaultDropdown = (
  <Dropdown 
    id="default-dropdown"
    options={fruitOptions}
    placeholder="Select a fruit"
  />
);

// Dropdown with label
export const DropdownWithLabel = (
  <Dropdown 
    id="labeled-dropdown"
    label="Favorite Fruit"
    options={fruitOptions}
    placeholder="Select a fruit"
  />
);

// Dropdown with helper text
export const DropdownWithHelper = (
  <Dropdown 
    id="helper-dropdown"
    label="Preferred Color"
    options={colorOptions}
    placeholder="Select a color"
    helperText="This will be used for your profile theme"
  />
);

// Dropdown with error
export const DropdownWithError = (
  <Dropdown 
    id="error-dropdown"
    label="Category"
    options={categoryOptions}
    placeholder="Select a category"
    error={true}
    errorMessage="Please select a category to continue"
  />
);

// Dropdown with default value
export const DropdownWithDefaultValue = (
  <Dropdown 
    id="default-value-dropdown"
    label="Favorite Color"
    options={colorOptions}
    defaultValue="blue"
  />
);

// Disabled dropdown
export const DisabledDropdown = (
  <Dropdown 
    id="disabled-dropdown"
    label="Unavailable Selection"
    options={fruitOptions}
    defaultValue="apple"
    disabled={true}
  />
);

// Required dropdown
export const RequiredDropdown = (
  <Dropdown 
    id="required-dropdown"
    label="Category"
    options={categoryOptions}
    placeholder="Select a category"
    required={true}
  />
);

// Searchable dropdown
export const SearchableDropdown = (
  <Dropdown 
    id="searchable-dropdown"
    label="Category"
    options={categoryOptions}
    placeholder="Search and select category"
    searchable={true}
  />
);

// Dropdown with prefix icon (เดิมชื่อ UserDropdown)
export const DropdownWithPrefix = (
  <Dropdown 
    id="prefix-dropdown"
    options={userOptions}
    placeholder="ธยาดา แสงสว่าง"
    variant="blue"
    prefixIcon={<User className="h-5 w-5 text-blue-900" />}
  />
);

// Different sizes
export const SmallDropdown = (
  <Dropdown 
    id="small-dropdown"
    options={fruitOptions}
    placeholder="Small dropdown"
    size="sm"
  />
);

export const DefaultSizeDropdown = (
  <Dropdown 
    id="default-size-dropdown"
    options={fruitOptions}
    placeholder="Default size dropdown"
    size="default"
  />
);

export const LargeDropdown = (
  <Dropdown 
    id="large-dropdown"
    options={fruitOptions}
    placeholder="Large dropdown"
    size="lg"
  />
);