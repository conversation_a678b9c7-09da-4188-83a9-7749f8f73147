import{S as Z,a as X}from"./chunk-RNCCSLPH-D9EE9BUk.js";import{Q as U,j as e,q as ee,d as te,R as se,a as B,S as ae,p as ie,s as ne,b as g,H as _,A as C,u as R,f as re,r as w,U as le,m as Y,V as A,L as E,T as k,B as oe,W as ce,Y as H,J as de,Z as ue,_ as Q,$ as me,a0 as pe,a1 as xe,k as he}from"./index-Bwql5Dzz.js";import{g as fe}from"./chunk-OIAPXGI2-Bd-HAC-t.js";import{P as ge}from"./chunk-AM2BU2RH-Jz_rw4zs.js";import{S as p}from"./chunk-LFLGEXIG-DHqE42LF.js";import{T as q}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as ve}from"./chunk-FFVOUYTF-DR1d4TPs.js";import{D as je,u as ye,c as be,a as Se}from"./chunk-MGPZHEOT-CqdSNFtj.js";import{u as we}from"./chunk-C76H5USB-ByRPKhW7.js";import{P as L}from"./chunk-B3XEMIUY-BRCRLSgz.js";import{P as y}from"./pencil-square-6wRbnn1C.js";import{u as M}from"./use-prompt-pbDx0Sfe.js";import{S as Ce}from"./status-badge-B-sIb9s0.js";import{T as W}from"./trash-BBylvTAG.js";import{T as Ne}from"./thumbnail-badge-i0OhbLw4.js";import{C as _e}from"./channels-CoQb8GLB.js";import{T as ke}from"./Trans-VWqfqpAH.js";import{C as F}from"./component-Cob2CL0R.js";import{c as Pe}from"./create-data-table-command-helper-C3wntDfO.js";import{C as N}from"./container-Dqi2woPF.js";import{C as Te}from"./checkbox-B4pL6X49.js";import{C as I}from"./command-bar-Cyd2ymXA.js";import"./chunk-EQTBJSBZ-C4fKII8C.js";import"./x-mark-mini-DvSTI7zK.js";import"./check-BGSYwiWc.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./index-BxZ1678G.js";import"./table-BDZtqXjX.js";import"./arrow-up-mini-D5bOKjDW.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./index-DP5bcQyU.js";import"./triangle-left-mini-Bu6679Aa.js";import"./prompt-BsR9zKsn.js";var Pt=s=>{const{id:t}=s.params||{},{product:a}=U(t,{fields:L},{initialData:s.data,enabled:!!t});return a?e.jsx("span",{children:a.title}):null},De=s=>({queryKey:ie.detail(s,{fields:L}),queryFn:async()=>ne.admin.product.retrieve(s,{fields:L})}),Tt=async({params:s})=>{const t=s.id,a=De(t);return await ee.ensureQueryData({...a,staleTime:9e4})},Ie=({product:s})=>{const{t}=g(),{getDisplays:a}=B();return e.jsxs(N,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(_,{level:"h2",children:t("products.attributes")}),e.jsx(C,{groups:[{actions:[{label:t("actions.edit"),to:"attributes",icon:e.jsx(y,{})}]}]})]}),e.jsx(p,{title:t("fields.height"),value:s.height}),e.jsx(p,{title:t("fields.width"),value:s.width}),e.jsx(p,{title:t("fields.length"),value:s.length}),e.jsx(p,{title:t("fields.weight"),value:s.weight}),e.jsx(p,{title:t("fields.midCode"),value:s.mid_code}),e.jsx(p,{title:t("fields.hsCode"),value:s.hs_code}),e.jsx(p,{title:t("fields.countryOfOrigin"),value:fe(s.origin_country)}),a("product","attributes").map((i,r)=>e.jsx(i,{data:s},r))]})},ze=s=>{switch(s){case"draft":return"grey";case"proposed":return"orange";case"published":return"green";case"rejected":return"red";default:return"grey"}},Ae=({product:s})=>{const{t}=g(),a=M(),i=R(),{getDisplays:r}=B(),n=r("product","general"),{mutateAsync:u}=re(s.id),m=async()=>{await a({title:t("general.areYouSure"),description:t("products.deleteWarning",{title:s.title}),confirmText:t("actions.delete"),cancelText:t("actions.cancel")})&&await u(void 0,{onSuccess:()=>{i("..")}})};return e.jsxs(N,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(_,{children:s.title}),e.jsxs("div",{className:"flex items-center gap-x-4",children:[e.jsx(Ce,{color:ze(s.status),children:t(`products.productStatus.${s.status}`)}),e.jsx(C,{groups:[{actions:[{label:t("actions.edit"),to:"edit",icon:e.jsx(y,{})}]},{actions:[{label:t("actions.delete"),onClick:m,icon:e.jsx(W,{})}]}]})]})]}),e.jsx(p,{title:t("fields.description"),value:s.description}),e.jsx(p,{title:t("fields.subtitle"),value:s.subtitle}),e.jsx(p,{title:t("fields.handle"),value:`/${s.handle}`}),e.jsx(p,{title:t("fields.discountable"),value:s.discountable?t("fields.true"):t("fields.false")}),n.map((f,o)=>e.jsx(f,{data:s},o))]})},Oe=({product:s})=>{const{t}=g(),a=M(),[i,r]=w.useState({}),n=$e(s),u=o=>{r(x=>{if(x[o]){const{[o]:b,...c}=x;return c}else return{...x,[o]:!0}})},{mutateAsync:m}=le(s.id),f=async()=>{const o=Object.keys(i),x=o.some(l=>{var d;return(d=n.find(h=>h.id===l))==null?void 0:d.isThumbnail});if(!await a({title:t("general.areYouSure"),description:x?t("products.media.deleteWarningWithThumbnail",{count:o.length}):t("products.media.deleteWarning",{count:o.length}),confirmText:t("actions.delete"),cancelText:t("actions.cancel")}))return;const c=s.images.filter(l=>!o.includes(l.id)).map(l=>({url:l.url}));await m({images:c,thumbnail:x?"":void 0},{onSuccess:()=>{r({})}})};return e.jsxs(N,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(_,{level:"h2",children:t("products.media.label")}),e.jsx(C,{groups:[{actions:[{label:t("actions.edit"),to:"media?view=edit",icon:e.jsx(y,{})}]}]})]}),n.length>0?e.jsx("div",{className:"grid grid-cols-[repeat(auto-fill,minmax(96px,1fr))] gap-4 px-6 py-4",children:n.map((o,x)=>{const b=i[o.id];return e.jsxs("div",{className:"shadow-elevation-card-rest hover:shadow-elevation-card-hover transition-fg group relative aspect-square size-full cursor-pointer overflow-hidden rounded-[8px]",children:[e.jsx("div",{className:Y("transition-fg invisible absolute right-2 top-2 opacity-0 group-hover:visible group-hover:opacity-100",{"visible opacity-100":b}),children:e.jsx(Te,{checked:i[o.id]||!1,onCheckedChange:()=>u(o.id)})}),o.isThumbnail&&e.jsx("div",{className:"absolute left-2 top-2",children:e.jsx(A,{content:t("fields.thumbnail"),children:e.jsx(Ne,{})})}),e.jsx(E,{to:"media",state:{curr:x},children:e.jsx("img",{src:o.url,alt:`${s.title} image`,className:"size-full object-cover"})})]},o.id)})}):e.jsxs("div",{className:"flex flex-col items-center gap-y-4 pb-8 pt-6",children:[e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx(k,{size:"small",leading:"compact",weight:"plus",className:"text-ui-fg-subtle",children:t("products.media.emptyState.header")}),e.jsx(k,{size:"small",className:"text-ui-fg-muted",children:t("products.media.emptyState.description")})]}),e.jsx(oe,{size:"small",variant:"secondary",asChild:!0,children:e.jsx(E,{to:"media?view=edit",children:t("products.media.emptyState.action")})})]}),e.jsx(I,{open:!!Object.keys(i).length,children:e.jsxs(I.Bar,{children:[e.jsx(I.Value,{children:t("general.countSelected",{count:Object.keys(i).length})}),e.jsx(I.Seperator,{}),e.jsx(I.Command,{action:f,label:t("actions.delete"),shortcut:"d"})]})})]})},$e=s=>{const{images:t=[],thumbnail:a}=s,i=t.map(r=>({id:r.id,url:r.url,isThumbnail:r.url===a}));return a&&!i.some(r=>r.url===a)&&i.unshift({id:"img_thumbnail",url:a,isThumbnail:!0}),i},Le=({product:s,option:t})=>{const{t:a}=g(),{mutateAsync:i}=xe(s.id,t.id),r=M(),n=async()=>{await r({title:a("general.areYouSure"),description:a("products.options.deleteWarning",{title:t.title}),confirmText:a("actions.delete"),cancelText:a("actions.cancel")})&&await i()};return e.jsx(C,{groups:[{actions:[{label:a("actions.edit"),to:`options/${t.id}/edit`,icon:e.jsx(y,{})}]},{actions:[{label:a("actions.delete"),onClick:n,icon:e.jsx(W,{})}]}]})},Be=({product:s})=>{var a;const{t}=g();return e.jsxs(N,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(_,{level:"h2",children:t("products.options.header")}),e.jsx(C,{groups:[{actions:[{label:t("actions.create"),to:"options/create",icon:e.jsx(ce,{})}]}]})]}),(a=s.options)==null?void 0:a.map(i=>{var r;return e.jsx(p,{title:i.title,value:(r=i.values)==null?void 0:r.map(n=>e.jsx(H,{size:"2xsmall",className:"flex min-w-[20px] items-center justify-center",children:n.value},n.value)),actions:e.jsx(Le,{product:s,option:i})},i.id)})]})},Me=({product:s})=>{var i,r;const{t}=g(),{getDisplays:a}=B();return e.jsxs(N,{className:"divide-y p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(_,{level:"h2",children:t("products.organization.header")}),e.jsx(C,{groups:[{actions:[{label:t("actions.edit"),to:"organization",icon:e.jsx(y,{})}]}]})]}),e.jsx(p,{title:t("fields.tags"),value:(i=s.tags)!=null&&i.length?s.tags.map(n=>e.jsx($,{label:n.value,to:`/settings/product-tags/${n.id}`},n.id)):void 0}),e.jsx(p,{title:t("fields.type"),value:s.type?e.jsx($,{label:s.type.value,to:`/settings/product-types/${s.type_id}`}):void 0}),e.jsx(p,{title:t("fields.collection"),value:s.collection?e.jsx($,{label:s.collection.title,to:`/collections/${s.collection.id}`}):void 0}),e.jsx(p,{title:t("fields.categories"),value:(r=s.categories)!=null&&r.length?s.categories.map(n=>e.jsx($,{label:n.name,to:`/categories/${n.id}`},n.id)):void 0}),a("product","organize").map((n,u)=>e.jsx(n,{data:s},u))]})},$=({label:s,to:t})=>e.jsx(A,{content:s,children:e.jsx(H,{size:"2xsmall",className:"block w-fit truncate",asChild:!0,children:e.jsx(E,{to:t,children:s})})}),qe=({product:s})=>{var u;const{count:t}=de(),{t:a}=g(),i=((u=s.sales_channels)==null?void 0:u.map(m=>({id:m.id,name:m.name})))??[],r=i.slice(0,3),n=i.slice(3);return e.jsxs(N,{className:"flex flex-col gap-y-4 px-6 py-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(_,{level:"h2",children:a("fields.sales_channels")}),e.jsx(C,{groups:[{actions:[{label:a("actions.edit"),to:"sales-channels",icon:e.jsx(y,{})}]}]})]}),e.jsxs("div",{className:"grid grid-cols-[28px_1fr] items-center gap-x-3",children:[e.jsx("div",{className:"bg-ui-bg-base shadow-borders-base flex size-7 items-center justify-center rounded-md",children:e.jsx("div",{className:"bg-ui-bg-component flex size-6 items-center justify-center rounded-[4px]",children:e.jsx(_e,{className:"text-ui-fg-subtle"})})}),i.length>0?e.jsxs("div",{className:"flex items-center gap-x-1",children:[e.jsx(k,{size:"small",leading:"compact",children:r.map(m=>m.name).join(", ")}),n.length>0&&e.jsx(A,{content:e.jsx("ul",{children:n.map(m=>e.jsx("li",{children:m.name},m.id))}),children:e.jsx(k,{size:"small",leading:"compact",className:"text-ui-fg-subtle",children:`+${n.length}`})})]}):e.jsx(k,{size:"small",leading:"compact",className:"text-ui-fg-subtle",children:a("products.noSalesChannels")})]}),e.jsx("div",{children:e.jsx(k,{className:"text-ui-fg-subtle",size:"small",leading:"compact",children:e.jsx(ke,{i18nKey:"sales_channels.availableIn",values:{x:i.length,y:t??0},components:[e.jsx("span",{className:"text-ui-fg-base txt-compact-medium-plus"},"x"),e.jsx("span",{className:"text-ui-fg-base txt-compact-medium-plus"},"y")]})})})]})},V=10,K="pv",Ee=({product:s})=>{const{t}=g(),{q:a,order:i,offset:r,allow_backorder:n,manage_inventory:u,created_at:m,updated_at:f}=we(["q","order","offset","manage_inventory","allow_backorder","created_at","updated_at"],K),o=Ke(s),x=Re(),b=We(),{variants:c,count:l,isPending:d,isError:h,error:v}=ue(s.id,{q:a,order:i||"variant_rank",offset:r?parseInt(r):void 0,limit:V,allow_backorder:n?JSON.parse(n):void 0,manage_inventory:u?JSON.parse(u):void 0,created_at:m?JSON.parse(m):void 0,updated_at:f?JSON.parse(f):void 0,fields:"title,sku,*options,created_at,updated_at,*inventory_items.inventory.location_levels,inventory_quantity,manage_inventory"},{placeholderData:he});if(h)throw v;return e.jsx(N,{className:"divide-y p-0",children:e.jsx(je,{data:c,columns:o,filters:x,rowCount:l,getRowId:j=>j.id,rowHref:j=>`/products/${s.id}/variants/${j.id}`,pageSize:V,isLoading:d,heading:t("products.variants.header"),emptyState:{empty:{heading:t("products.variants.empty.heading"),description:t("products.variants.empty.description")},filtered:{heading:t("products.variants.filtered.heading"),description:t("products.variants.filtered.description")}},action:{label:t("actions.create"),to:"variants/create"},actionMenu:{groups:[{actions:[{label:t("products.editPrices"),to:"prices",icon:e.jsx(y,{})},{label:t("inventory.stock.action"),to:"stock",icon:e.jsx(Q,{})}]}]},commands:b,prefix:K})})},z=be(),Ke=s=>{const{t}=g(),a=R(),{mutateAsync:i}=me(s.id),r=M(),[n]=pe(),u=w.useMemo(()=>{const c=new URLSearchParams;for(const[l,d]of n.entries())l.startsWith(`${K}_`)&&c.append(l,d);return c},[n]),m=ve(),f=w.useCallback(async(c,l)=>{await r({title:t("general.areYouSure"),description:t("products.deleteVariantWarning",{title:l}),confirmText:t("actions.delete"),cancelText:t("actions.cancel")})&&await i({variantId:c})},[i,r,t]),o=w.useMemo(()=>s!=null&&s.options?s.options.map(c=>z.display({id:c.id,header:c.title,cell:({row:l})=>{var h;const d=(h=l.original.options)==null?void 0:h.find(v=>v.option_id===c.id);return d?e.jsx("div",{className:"flex items-center",children:e.jsx(A,{content:d.value,children:e.jsx(H,{size:"2xsmall",title:d.value,className:"inline-flex min-w-[20px] max-w-[140px] items-center justify-center overflow-hidden truncate",children:d.value})})}):e.jsx("span",{className:"text-ui-fg-muted",children:"-"})}})):[],[s]),x=w.useCallback(c=>{var j,P;const l=c.row.original,d=[{icon:e.jsx(y,{}),label:t("actions.edit"),onClick:S=>{a(`edit-variant?variant_id=${S.row.original.id}&${u.toString()}`,{state:{restore_params:u.toString()}})}}],h=[{icon:e.jsx(W,{}),label:t("actions.delete"),onClick:()=>f(l.id,l.title)}];switch(((j=l.inventory_items)==null?void 0:j.length)||0){case 0:break;case 1:{const S=`/inventory/${l.inventory_items[0].inventory.id}`;d.push({label:t("products.variant.inventory.actions.inventoryItems"),onClick:()=>{a(S)},icon:e.jsx(Q,{})});break}default:{const S=(P=l.inventory_items)==null?void 0:P.map(T=>{var D;return(D=T.inventory)==null?void 0:D.id});if(!S||S.length===0)break;const O=`/inventory?${new URLSearchParams({id:S.join(",")}).toString()}`;d.push({label:t("products.variant.inventory.actions.inventoryKit"),onClick:()=>{a(O)},icon:e.jsx(F,{})})}}return[d,h]},[f,a,t,u]),b=w.useCallback(c=>{var O;const l=c;if(!c.manage_inventory)return{text:t("products.variant.inventory.notManaged"),hasInventoryKit:!1,notManaged:!0};const d=c.inventory_quantity,h=(O=l.inventory_items)==null?void 0:O.map(T=>T.inventory).filter(Boolean),v=h.length>1,j={};h.forEach(T=>{var D;(D=T.location_levels)==null||D.forEach(G=>{j[G.id]=!0})});const P=Object.keys(j).length;return{text:v?t("products.variant.tableItemAvailable",{availableCount:d}):t("products.variant.tableItem",{availableCount:d,locationCount:P,count:P}),hasInventoryKit:v,quantity:d,notManaged:!1}},[t]);return w.useMemo(()=>[z.accessor("title",{header:t("fields.title"),enableSorting:!0,sortAscLabel:t("filters.sorting.alphabeticallyAsc"),sortDescLabel:t("filters.sorting.alphabeticallyDesc")}),z.accessor("sku",{header:t("fields.sku"),enableSorting:!0,sortAscLabel:t("filters.sorting.alphabeticallyAsc"),sortDescLabel:t("filters.sorting.alphabeticallyDesc")}),...o,z.display({id:"inventory",header:t("fields.inventory"),cell:({row:c})=>{const{text:l,hasInventoryKit:d,quantity:h,notManaged:v}=b(c.original);return e.jsx(A,{content:l,children:e.jsxs("div",{className:"flex h-full w-full items-center gap-2 overflow-hidden",children:[d&&e.jsx(F,{}),e.jsx("span",{className:Y("truncate",{"text-ui-fg-error":!h&&!v}),children:l})]})})},maxSize:250}),...m,z.action({actions:x})],[t,o,m,x,b])},J=Se(),Re=()=>{const{t:s}=g(),t=ye();return w.useMemo(()=>[J.accessor("allow_backorder",{type:"radio",label:s("fields.allowBackorder"),options:[{label:s("filters.radio.yes"),value:"true"},{label:s("filters.radio.no"),value:"false"}]}),J.accessor("manage_inventory",{type:"radio",label:s("fields.manageInventory"),options:[{label:s("filters.radio.yes"),value:"true"},{label:s("filters.radio.no"),value:"false"}]}),...t],[s,t])},He=Pe(),We=()=>{const{t:s}=g(),t=R();return[He.command({label:s("inventory.stock.action"),shortcut:"i",action:async a=>{t(`stock?${ge}=${Object.keys(a).join(",")}`)}})]},Fe=({product:s})=>{const{t}=g(),a=s.shipping_profile;return e.jsxs(N,{className:"p-0",children:[e.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[e.jsx(_,{level:"h2",children:t("products.shippingProfile.header")}),e.jsx(C,{groups:[{actions:[{label:t("actions.edit"),to:"shipping-profile",icon:e.jsx(y,{})}]}]})]}),a&&e.jsx(Z,{to:`/settings/locations/shipping-profiles/${a.id}`,labelKey:a.name,descriptionKey:a.type,icon:e.jsx(X,{})})]})},Dt=()=>{const s=te(),{id:t}=se(),{product:a,isLoading:i,isError:r,error:n}=U(t,{fields:L},{initialData:s}),{getWidgets:u}=B(),m=u("product.details.after"),f=u("product.details.before"),o=u("product.details.side.after"),x=u("product.details.side.before");if(i||!a)return e.jsx(ae,{mainSections:4,sidebarSections:3,showJSON:!0,showMetadata:!0});if(r)throw n;return e.jsxs(q,{widgets:{after:m,before:f,sideAfter:o,sideBefore:x},showJSON:!0,showMetadata:!0,data:a,children:[e.jsxs(q.Main,{children:[e.jsx(Ae,{product:a}),e.jsx(Oe,{product:a}),e.jsx(Be,{product:a}),e.jsx(Ee,{product:a})]}),e.jsxs(q.Sidebar,{children:[e.jsx(qe,{product:a}),e.jsx(Fe,{product:a}),e.jsx(Me,{product:a}),e.jsx(Ie,{product:a})]})]})};export{Pt as Breadcrumb,Dt as Component,Tt as loader};
