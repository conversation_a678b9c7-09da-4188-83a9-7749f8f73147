import{u as b}from"./chunk-XWO5BP42-Cctq7kYe.js";import{a as g,j as t,b as m,dr as j,k as C,H as h,T,L as y,B as v,r as k,ds as w,A as P}from"./index-Bwql5Dzz.js";import{u as S,_ as A}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{u as D}from"./chunk-EMNHBSFU-BcNoY5bk.js";import{u as _}from"./chunk-GW6TVOAA-rYK3_Cle.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as E}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as z}from"./use-prompt-pbDx0Sfe.js";import{P as L}from"./pencil-square-6wRbnn1C.js";import{T as H}from"./trash-BBylvTAG.js";import{C as B}from"./container-Dqi2woPF.js";import{c as I}from"./index-BxZ1678G.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-W7625H47-D4n0RxCV.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var N=({collection:e})=>{const{t:o}=m(),r=z(),{mutateAsync:s}=w(e.id),i=async()=>{await r({title:o("general.areYouSure"),description:o("collections.deleteWarning",{title:e.title}),verificationText:e.title,verificationInstruction:o("general.typeToConfirm"),confirmText:o("actions.delete"),cancelText:o("actions.cancel")})&&await s()};return t.jsx(P,{groups:[{actions:[{label:o("actions.edit"),to:`/collections/${e.id}/edit`,icon:t.jsx(L,{})}]},{actions:[{label:o("actions.delete"),onClick:i,icon:t.jsx(H,{}),disabled:!e.id}]}]})},l=20,R=()=>{const{t:e}=m(),{searchParams:o,raw:r}=D({pageSize:l}),{collections:s,count:i,isError:n,error:p,isLoading:d}=j({...o,fields:"+products.id"},{placeholderData:C}),u=_(),c=q(),{table:f}=S({data:s??[],columns:c,count:i,enablePagination:!0,getRowId:(a,x)=>a.id??`${x}`,pageSize:l});if(n)throw p;return t.jsxs(B,{className:"divide-y p-0",children:[t.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[t.jsxs("div",{children:[t.jsx(h,{children:e("collections.domain")}),t.jsx(T,{className:"text-ui-fg-subtle",size:"small",children:e("collections.subtitle")})]}),t.jsx(y,{to:"/collections/create",children:t.jsx(v,{size:"small",variant:"secondary",children:e("actions.create")})})]}),t.jsx(A,{table:f,columns:c,pageSize:l,count:i,filters:u,orderBy:[{key:"title",label:e("fields.title")},{key:"handle",label:e("fields.handle")},{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}],search:!0,navigateTo:a=>`/collections/${a.original.id}`,queryObject:r,isLoading:d})]})},$=I(),q=()=>{const e=b();return k.useMemo(()=>[...e,$.display({id:"actions",cell:({row:o})=>t.jsx(N,{collection:o.original})})],[e])},ve=()=>{const{getWidgets:e}=g();return t.jsx(E,{widgets:{after:e("product_collection.list.after"),before:e("product_collection.list.before")},children:t.jsx(R,{})})};export{ve as Component};
