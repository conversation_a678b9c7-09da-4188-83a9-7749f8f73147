import React from 'react';
import { render, screen } from '@testing-library/react';
import CoverflowCarousel from '../carousel-coverflow';
import { promotionalSlides } from '../__fixtures__/carousel.fixtures';

// Similar mock setup as in carousel.test.tsx
jest.mock('swiper/react', () => ({
  Swiper: ({ children, onSwiper }: any) => {
    const swiperRef = { slideTo: jest.fn() };
    React.useEffect(() => {
      if (onSwiper) onSwiper(swiperRef);
    }, [onSwiper]);
    return (
      <div data-testid="swiper-container">
        {children}
      </div>
    );
  },
  SwiperSlide: ({ children }: any) => <div data-testid="swiper-slide">{children}</div>,
}));

jest.mock('swiper/modules', () => ({
  Navigation: jest.fn(),
  Pagination: jest.fn(),
  Autoplay: jest.fn(),
  EffectCoverflow: jest.fn(),
}));

jest.mock('swiper/css', () => ({}));
jest.mock('swiper/css/navigation', () => ({}));
jest.mock('swiper/css/pagination', () => ({}));
jest.mock('swiper/css/effect-coverflow', () => ({}));

describe('CoverflowCarousel Component', () => {
  it('renders the coverflow carousel with brand slides', () => {
    render(
      <CoverflowCarousel
        items={promotionalSlides}
      />
    );
    
    const slides = screen.getAllByTestId('swiper-slide');
    expect(slides).toHaveLength(promotionalSlides.length);
  });
  
  it('applies the brand-coverflow-carousel class', () => {
    const { container } = render(
      <CoverflowCarousel
        items={promotionalSlides}
      />
    );
    
    expect(container.querySelector('.brand-coverflow-carousel')).toBeInTheDocument();
  });
  
  it('renders with autoplay disabled', () => {
    render(
      <CoverflowCarousel
        items={promotionalSlides}
        autoplay={false}
      />
    );
    
    // This is a superficial test since we mocked Swiper
    const container = screen.getByTestId('swiper-container');
    expect(container).toBeInTheDocument();
  });
  
  it('passes custom class name', () => {
    const { container } = render(
      <CoverflowCarousel
        items={promotionalSlides}
        className="custom-coverflow"
      />
    );
    
    expect(container.querySelector('.custom-coverflow')).toBeInTheDocument();
  });
  
  it('renders slides with linked images when links are provided', () => {
    render(
      <CoverflowCarousel
        items={[
          {
            id: 'test',
            title: 'Test Slide',
            image: '/test.jpg',
            link: 'https://example.com'
          }
        ]}
      />
    );
    
    const slide = screen.getByTestId('swiper-slide');
    expect(slide).toBeInTheDocument();
    
    // Check if link is rendered
    const link = slide.querySelector('a');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', 'https://example.com');
  });
});