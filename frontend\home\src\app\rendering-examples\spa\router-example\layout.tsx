'use client';

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { ThemeToggle } from '@/components/theme-toggle';
import { I18nButton } from '@/components/I18nButton';
import Link from 'next/link';

// Define routes for SPA navigation - no server-side rendering needed
const routes = [
  { path: 'home', labelKey: 'rendering.routes.home', defaultLabel: 'Home' },
  { path: 'about', labelKey: 'rendering.routes.about', defaultLabel: 'About' },
  { path: 'products', labelKey: 'rendering.routes.products', defaultLabel: 'Products' },
  { path: 'contact', labelKey: 'rendering.routes.contact', defaultLabel: 'Contact' },
];

export default function SPARouterLayout({ children: _children }: { children: React.ReactNode }) {
  const { t } = useTranslation();
  const { theme } = useTheme();
  const [currentRoute, setCurrentRoute] = useState<string>('home');
  const [url, setUrl] = useState<string>('');

  // Initialize SPA by checking for a hash in the URL
  useEffect(() => {
    const hash = window.location.hash.replace('#', '');
    if (hash) {
      setCurrentRoute(hash);
    }
    setUrl(window.location.href);

    // Also handle browser back/forward navigation
    const handlePopState = () => {
      const hash = window.location.hash.replace('#', '');
      if (hash) {
        setCurrentRoute(hash);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Handle navigation to different routes
  const navigateTo = (route: string) => {
    setCurrentRoute(route);
    // Update the browser URL without a page reload (this is what SPAs do)
    const newUrl = `/rendering-examples/spa/router-example#${route}`;
    window.history.pushState(null, '', newUrl);
    setUrl(newUrl);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <Link href="/rendering-examples/spa" className="text-primary font-semibold hover:underline">
          ← {t('rendering.backToSPA', 'Back to SPA Example')}
        </Link>

        <div className="flex items-center gap-2">
          <ThemeToggle />
          <I18nButton />
          <div className="text-muted-foreground text-sm">
            {t('theme.current', 'Current theme')}: {theme}
          </div>
        </div>
      </div>

      <h1 className="mb-6 text-3xl font-bold">
        {t('rendering.spaTitle', 'Single Page Application (SPA) Router Example')}
      </h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.spaRouterTitle', 'How SPA Routing Works')}
        </h2>
        <p className="mb-4">
          {t(
            'rendering.spaRouterDescription',
            'In a Single Page Application, all routing happens client-side without refreshing the page. The app intercepts URL changes and renders the appropriate content without requesting new HTML from the server.',
          )}
        </p>
        <p className="mb-4">
          {t(
            'rendering.spaRouterNote',
            'This provides the smoothest user experience with persistent state and no page flashes. Current URL: ',
          )}{' '}
          <code className="bg-muted-foreground/20 rounded px-1">{url}</code>
        </p>
      </div>

      {/* Pure client-side navigation */}
      <nav className="bg-muted/40 mb-4 flex overflow-x-auto rounded-lg p-2">
        <ul className="flex min-w-full flex-none gap-2">
          {routes.map((route) => {
            const isActive = currentRoute === route.path;

            return (
              <li key={route.path}>
                <button
                  onClick={() => navigateTo(route.path)}
                  className={`ring-offset-background focus-visible:ring-ring inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium whitespace-nowrap transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 ${
                    isActive
                      ? 'bg-primary text-primary-foreground'
                      : 'hover:bg-muted hover:text-muted-foreground'
                  }`}
                >
                  {t(`rendering.routes.${route.path}`, route.defaultLabel)}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* We don't use children for SPA because we control the rendering entirely on the client */}
      {currentRoute === 'home' && (
        <div className="bg-card space-y-6 rounded-lg p-4 shadow-sm">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Home</h1>
            <p className="text-muted-foreground mt-2">This is the home page rendered using SPA</p>
            <div className="bg-muted mt-4 rounded p-2 text-sm">
              <p>Timestamp: {new Date().toISOString()}</p>
              <p>Render type: SPA</p>
            </div>
          </div>
        </div>
      )}

      {currentRoute === 'about' && (
        <div className="bg-card space-y-6 rounded-lg p-4 shadow-sm">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">About</h1>
            <p className="text-muted-foreground mt-2">This is the about page rendered using SPA</p>
            <div className="bg-muted mt-4 rounded p-2 text-sm">
              <p>Timestamp: {new Date().toISOString()}</p>
              <p>Render type: SPA</p>
            </div>
          </div>
        </div>
      )}

      {currentRoute === 'products' && (
        <div className="bg-card space-y-6 rounded-lg p-4 shadow-sm">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Products</h1>
            <p className="text-muted-foreground mt-2">
              This is the products page rendered using SPA
            </p>
            <div className="bg-muted mt-4 rounded p-2 text-sm">
              <p>Timestamp: {new Date().toISOString()}</p>
              <p>Render type: SPA</p>
            </div>
          </div>
        </div>
      )}

      {currentRoute === 'contact' && (
        <div className="bg-card space-y-6 rounded-lg p-4 shadow-sm">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Contact</h1>
            <p className="text-muted-foreground mt-2">
              This is the contact page rendered using SPA
            </p>
            <div className="bg-muted mt-4 rounded p-2 text-sm">
              <p>Timestamp: {new Date().toISOString()}</p>
              <p>Render type: SPA</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
