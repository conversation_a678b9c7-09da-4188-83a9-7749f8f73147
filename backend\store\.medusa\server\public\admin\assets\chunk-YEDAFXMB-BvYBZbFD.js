import{u as p,l as m}from"./lodash-CPCX-RQp.js";import{b as f,r as l,j as h,x as i}from"./index-Bwql5Dzz.js";var g=({placeholder:o,prefix:r,autofocus:n})=>{const{t:c}=f(),u=o||c("general.search"),e=p({param:"q",prefix:r,multiple:!1}),a=e.get(),s=l.useCallback(m.debounce(d=>{const t=d.target.value;t?e.add(t):e.delete()},500),[e]);return l.useEffect(()=>()=>{s.cancel()},[s]),h.jsx(i,{autoComplete:"off",name:"q",type:"search",size:"small",autoFocus:n,defaultValue:(a==null?void 0:a[0])||void 0,onChange:s,placeholder:u})};export{g as D};
