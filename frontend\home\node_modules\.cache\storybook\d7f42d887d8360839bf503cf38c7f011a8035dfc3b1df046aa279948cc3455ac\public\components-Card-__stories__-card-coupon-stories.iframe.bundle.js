"use strict";
(self["webpackChunkshadcn_timeline"] = self["webpackChunkshadcn_timeline"] || []).push([["components-Card-__stories__-card-coupon-stories"],{

/***/ "./src/components/Card/__stories__/card-coupon.stories.tsx":
/*!*****************************************************************!*\
  !*** ./src/components/Card/__stories__/card-coupon.stories.tsx ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache;
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClaimedCoupon: () => (/* binding */ ClaimedCoupon),
/* harmony export */   ClaimingCoupon: () => (/* binding */ ClaimingCoupon),
/* harmony export */   FixedAmountCoupon: () => (/* binding */ FixedAmountCoupon),
/* harmony export */   FixedAmountWithMaxDiscount: () => (/* binding */ FixedAmountWithMaxDiscount),
/* harmony export */   LargeAmountCoupon: () => (/* binding */ LargeAmountCoupon),
/* harmony export */   LoadingState: () => (/* binding */ LoadingState),
/* harmony export */   MaxPercentageCoupon: () => (/* binding */ MaxPercentageCoupon),
/* harmony export */   PercentageCoupon: () => (/* binding */ PercentageCoupon),
/* harmony export */   SmallAmountCoupon: () => (/* binding */ SmallAmountCoupon),
/* harmony export */   SmallPercentageCoupon: () => (/* binding */ SmallPercentageCoupon),
/* harmony export */   TermsOnlyCoupon: () => (/* binding */ TermsOnlyCoupon),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @storybook/addon-actions */ "./node_modules/@storybook/addon-actions/dist/index.mjs");
/* harmony import */ var _card_coupon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../card-coupon */ "./src/components/Card/card-coupon.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");

__webpack_require__.$Refresh$.runtime = /*#__PURE__*/ (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__, 2)));

/* eslint-disable */
// @ts-nocheck
// @ts-expect-error (Converted from ts-ignore)
var __STORY__ = "// card-coupon.stories.tsx\r\nimport type { Meta, StoryObj } from '@storybook/react';\r\nimport { action } from '@storybook/addon-actions';\r\nimport { CardCoupon, CardCouponSkeleton } from '../card-coupon';\r\n\r\n// Sample brand logos - replace with actual URLs in production\r\nconst brandsConfig = {\r\n  philips: {\r\n    logoUrl: '/images/philips-logo.png',\r\n    name: 'Philips'\r\n  },\r\n  lamptan: {\r\n    logoUrl: '/images/LAMPTAN.png',\r\n    name: 'LAMPTAN'\r\n  },\r\n  bcc: {\r\n    logoUrl: '/images/BCC.png',\r\n    name: 'Bangkok Cable'\r\n  },\r\n  songkhla: {\r\n    logoUrl: '/images/Sengaroon.png',\r\n    name: 'Songkhla Natrin'\r\n  },\r\n  panasonic: {\r\n    logoUrl: '/images/Panasonic.png',\r\n    name: 'Panasonic'\r\n  },\r\n  haco: {\r\n    logoUrl: '/images/HACO.png',\r\n    name: 'HACO'\r\n  }\r\n};\r\n\r\n// Define sample coupons based on the images provided\r\nconst fixedAmountCoupon = {\r\n  id: 'fixed-amount-1',\r\n  brandLogoUrl: brandsConfig.philips.logoUrl,\r\n  brandName: brandsConfig.philips.name,\r\n  discountType: 'amount' as const,\r\n  discountValue: 2500,\r\n  minPurchase: 30000,\r\n  onSeeDetails: action('onSeeDetails'),\r\n  onClaimCoupon: action('onClaimCoupon'),\r\n};\r\n\r\nconst fixedAmountWithMaxDiscountCoupon = {\r\n  id: 'fixed-amount-max-2',\r\n  brandLogoUrl: brandsConfig.lamptan.logoUrl,\r\n  brandName: brandsConfig.lamptan.name,\r\n  discountType: 'amount' as const,\r\n  discountValue: 2500,\r\n  minPurchase: 3000,\r\n  maxDiscount: 1000,\r\n  onSeeDetails: action('onSeeDetails'),\r\n  onClaimCoupon: action('onClaimCoupon'),\r\n};\r\n\r\nconst percentageCoupon = {\r\n  id: 'percentage-1',\r\n  brandLogoUrl: brandsConfig.bcc.logoUrl,\r\n  brandName: brandsConfig.bcc.name,\r\n  discountType: 'percentage' as const,\r\n  discountValue: 75,\r\n  minPurchase: 30000,\r\n  maxDiscount: 1000,\r\n  onSeeDetails: action('onSeeDetails'),\r\n  onClaimCoupon: action('onClaimCoupon'),\r\n};\r\n\r\nconst maxPercentageCoupon = {\r\n  id: 'max-percentage-1',\r\n  brandLogoUrl: brandsConfig.bcc.logoUrl,\r\n  brandName: brandsConfig.bcc.name,\r\n  discountType: 'max-percentage' as const,\r\n  discountValue: 50,\r\n  minPurchase: 30000,\r\n  maxDiscount: 500,\r\n  onSeeDetails: action('onSeeDetails'),\r\n  onClaimCoupon: action('onClaimCoupon'),\r\n};\r\n\r\nconst smallAmountCoupon = {\r\n  id: 'small-amount-1',\r\n  brandLogoUrl: brandsConfig.songkhla.logoUrl,\r\n  brandName: brandsConfig.songkhla.name,\r\n  discountType: 'amount' as const,\r\n  discountValue: 500,\r\n  minPurchase: 50000,\r\n  onSeeDetails: action('onSeeDetails'),\r\n  onClaimCoupon: action('onClaimCoupon'),\r\n};\r\n\r\nconst smallPercentageCoupon = {\r\n  id: 'small-percentage-1',\r\n  brandLogoUrl: brandsConfig.philips.logoUrl,\r\n  brandName: brandsConfig.philips.name,\r\n  discountType: 'percentage' as const,\r\n  discountValue: 10,\r\n  minPurchase: 25000,\r\n  maxDiscount: 500,\r\n  onSeeDetails: action('onSeeDetails'),\r\n  onClaimCoupon: action('onClaimCoupon'),\r\n};\r\n\r\nconst largeAmountCoupon = {\r\n  id: 'large-amount-1',\r\n  brandLogoUrl: brandsConfig.haco.logoUrl,\r\n  brandName: brandsConfig.haco.name,\r\n  discountType: 'amount' as const,\r\n  discountValue: 5000,\r\n  minPurchase: 50000,\r\n  onSeeDetails: action('onSeeDetails'),\r\n  onClaimCoupon: action('onClaimCoupon'),\r\n};\r\n\r\nconst meta: Meta<typeof CardCoupon> = {\r\n  title: 'UI/Card/CardCoupon',\r\n  component: CardCoupon,\r\n  parameters: {\r\n    layout: 'centered',\r\n    // Add a11y parameter for accessibility testing\r\n    a11y: {\r\n      config: {\r\n        rules: [\r\n          {\r\n            // Ensure all images have alt text\r\n            id: 'image-alt',\r\n            enabled: true\r\n          }\r\n        ]\r\n      }\r\n    },\r\n  },\r\n  // Define argTypes for Storybook controls\r\n  argTypes: {\r\n    id: { \r\n      control: 'text',\r\n      description: 'Unique coupon identifier',\r\n    },\r\n    brandLogoUrl: { \r\n      control: 'text',\r\n      description: 'URL to the brand logo image' \r\n    },\r\n    brandName: { \r\n      control: 'text',\r\n      description: 'Brand name for alt text' \r\n    },\r\n    discountType: { \r\n      control: { type: 'select', options: ['amount', 'percentage', 'max-percentage'] },\r\n      description: 'Type of discount - fixed amount, percentage, or max percentage' \r\n    },\r\n    discountValue: { \r\n      control: { type: 'number', min: 0 },\r\n      description: 'Value of the discount (amount or percentage)' \r\n    },\r\n    minPurchase: { \r\n      control: { type: 'number', min: 0 },\r\n      description: 'Minimum purchase required to use the coupon' \r\n    },\r\n    maxDiscount: { \r\n      control: { type: 'number', min: 0 },\r\n      description: 'Maximum discount amount for percentage discounts' \r\n    },\r\n    claimed: { \r\n      control: 'boolean',\r\n      description: 'Whether the coupon has been claimed already' \r\n    },\r\n    onSeeDetails: { \r\n      action: 'seeDetails',\r\n      description: 'Function called when see details button is clicked' \r\n    },\r\n    onClaimCoupon: { \r\n      action: 'claimCoupon',\r\n      description: 'Function called when claim button is clicked' \r\n    },\r\n    isClaimingCoupon: { \r\n      control: 'boolean',\r\n      description: 'Whether the coupon is currently being claimed' \r\n    },\r\n    showTermsOnly: {\r\n      control: 'boolean',\r\n      description: 'Whether to show only the terms button (without claim button)'\r\n    }\r\n  },\r\n  // Default values for the stories\r\n  args: {\r\n    onSeeDetails: action('onSeeDetails'),\r\n    onClaimCoupon: action('onClaimCoupon'),\r\n  },\r\n  // Add decorators if needed\r\n  decorators: [\r\n    (Story) => (\r\n      <div style={{ maxWidth: '600px', width: '100%', padding: '20px' }}>\r\n        <Story />\r\n      </div>\r\n    ),\r\n  ],\r\n  // Add tags for filtering in Storybook\r\n  tags: ['autodocs', 'coupon', 'card'],\r\n};\r\n\r\nexport default meta;\r\ntype Story = StoryObj<typeof CardCoupon>;\r\n\r\n// Fixed amount discount coupon (฿2,500)\r\nexport const FixedAmountCoupon: Story = {\r\n  args: {\r\n    ...fixedAmountCoupon,\r\n  },\r\n};\r\n\r\n// Fixed amount with max discount limit\r\nexport const FixedAmountWithMaxDiscount: Story = {\r\n  args: {\r\n    ...fixedAmountWithMaxDiscountCoupon,\r\n  },\r\n};\r\n\r\n// Percentage discount coupon (75%)\r\nexport const PercentageCoupon: Story = {\r\n  args: {\r\n    ...percentageCoupon,\r\n  },\r\n};\r\n\r\n// Maximum percentage discount coupon (สูงสุด 50%)\r\nexport const MaxPercentageCoupon: Story = {\r\n  args: {\r\n    ...maxPercentageCoupon,\r\n  },\r\n};\r\n\r\n// Small fixed amount coupon (฿500)\r\nexport const SmallAmountCoupon: Story = {\r\n  args: {\r\n    ...smallAmountCoupon,\r\n  },\r\n};\r\n\r\n// Small percentage discount coupon (10%)\r\nexport const SmallPercentageCoupon: Story = {\r\n  args: {\r\n    ...smallPercentageCoupon,\r\n  },\r\n};\r\n\r\n// Large fixed amount coupon (฿5,000)\r\nexport const LargeAmountCoupon: Story = {\r\n  args: {\r\n    ...largeAmountCoupon,\r\n  },\r\n};\r\n\r\n// Claimed coupon example\r\nexport const ClaimedCoupon: Story = {\r\n  args: {\r\n    ...fixedAmountCoupon,\r\n    claimed: true,\r\n  },\r\n};\r\n\r\n// Coupon in claiming state\r\nexport const ClaimingCoupon: Story = {\r\n  args: {\r\n    ...percentageCoupon,\r\n    isClaimingCoupon: true,\r\n  },\r\n};\r\n\r\n// Show only terms button\r\nexport const TermsOnlyCoupon: Story = {\r\n  args: {\r\n    ...maxPercentageCoupon,\r\n    showTermsOnly: true,\r\n  },\r\n};\r\n\r\n// Loading state example\r\nexport const LoadingState: Story = {\r\n  render: () => <CardCouponSkeleton />,\r\n};";
// @ts-expect-error (Converted from ts-ignore)
var __LOCATIONS_MAP__ = {
  "FixedAmountCoupon": {
    "startLoc": {
      "col": 33,
      "line": 225
    },
    "endLoc": {
      "col": 1,
      "line": 229
    },
    "startBody": {
      "col": 33,
      "line": 225
    },
    "endBody": {
      "col": 1,
      "line": 229
    }
  },
  "FixedAmountWithMaxDiscount": {
    "startLoc": {
      "col": 42,
      "line": 231
    },
    "endLoc": {
      "col": 1,
      "line": 235
    },
    "startBody": {
      "col": 42,
      "line": 231
    },
    "endBody": {
      "col": 1,
      "line": 235
    }
  },
  "PercentageCoupon": {
    "startLoc": {
      "col": 32,
      "line": 237
    },
    "endLoc": {
      "col": 1,
      "line": 241
    },
    "startBody": {
      "col": 32,
      "line": 237
    },
    "endBody": {
      "col": 1,
      "line": 241
    }
  },
  "MaxPercentageCoupon": {
    "startLoc": {
      "col": 35,
      "line": 243
    },
    "endLoc": {
      "col": 1,
      "line": 247
    },
    "startBody": {
      "col": 35,
      "line": 243
    },
    "endBody": {
      "col": 1,
      "line": 247
    }
  },
  "SmallAmountCoupon": {
    "startLoc": {
      "col": 33,
      "line": 249
    },
    "endLoc": {
      "col": 1,
      "line": 253
    },
    "startBody": {
      "col": 33,
      "line": 249
    },
    "endBody": {
      "col": 1,
      "line": 253
    }
  },
  "SmallPercentageCoupon": {
    "startLoc": {
      "col": 37,
      "line": 255
    },
    "endLoc": {
      "col": 1,
      "line": 259
    },
    "startBody": {
      "col": 37,
      "line": 255
    },
    "endBody": {
      "col": 1,
      "line": 259
    }
  },
  "LargeAmountCoupon": {
    "startLoc": {
      "col": 33,
      "line": 261
    },
    "endLoc": {
      "col": 1,
      "line": 265
    },
    "startBody": {
      "col": 33,
      "line": 261
    },
    "endBody": {
      "col": 1,
      "line": 265
    }
  },
  "ClaimedCoupon": {
    "startLoc": {
      "col": 29,
      "line": 267
    },
    "endLoc": {
      "col": 1,
      "line": 272
    },
    "startBody": {
      "col": 29,
      "line": 267
    },
    "endBody": {
      "col": 1,
      "line": 272
    }
  },
  "ClaimingCoupon": {
    "startLoc": {
      "col": 30,
      "line": 274
    },
    "endLoc": {
      "col": 1,
      "line": 279
    },
    "startBody": {
      "col": 30,
      "line": 274
    },
    "endBody": {
      "col": 1,
      "line": 279
    }
  },
  "TermsOnlyCoupon": {
    "startLoc": {
      "col": 31,
      "line": 281
    },
    "endLoc": {
      "col": 1,
      "line": 286
    },
    "startBody": {
      "col": 31,
      "line": 281
    },
    "endBody": {
      "col": 1,
      "line": 286
    }
  },
  "LoadingState": {
    "startLoc": {
      "col": 28,
      "line": 288
    },
    "endLoc": {
      "col": 1,
      "line": 294
    },
    "startBody": {
      "col": 28,
      "line": 288
    },
    "endBody": {
      "col": 1,
      "line": 294
    }
  }
};

// card-coupon.stories.tsx



// Sample brand logos - replace with actual URLs in production
const brandsConfig = {
  philips: {
    logoUrl: '/images/philips-logo.png',
    name: 'Philips'
  },
  lamptan: {
    logoUrl: '/images/LAMPTAN.png',
    name: 'LAMPTAN'
  },
  bcc: {
    logoUrl: '/images/BCC.png',
    name: 'Bangkok Cable'
  },
  songkhla: {
    logoUrl: '/images/Sengaroon.png',
    name: 'Songkhla Natrin'
  },
  panasonic: {
    logoUrl: '/images/Panasonic.png',
    name: 'Panasonic'
  },
  haco: {
    logoUrl: '/images/HACO.png',
    name: 'HACO'
  }
};
// Define sample coupons based on the images provided
const fixedAmountCoupon = {
  id: 'fixed-amount-1',
  brandLogoUrl: brandsConfig.philips.logoUrl,
  brandName: brandsConfig.philips.name,
  discountType: 'amount',
  discountValue: 2500,
  minPurchase: 30000,
  onSeeDetails: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onSeeDetails'),
  onClaimCoupon: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onClaimCoupon')
};
const fixedAmountWithMaxDiscountCoupon = {
  id: 'fixed-amount-max-2',
  brandLogoUrl: brandsConfig.lamptan.logoUrl,
  brandName: brandsConfig.lamptan.name,
  discountType: 'amount',
  discountValue: 2500,
  minPurchase: 3000,
  maxDiscount: 1000,
  onSeeDetails: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onSeeDetails'),
  onClaimCoupon: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onClaimCoupon')
};
const percentageCoupon = {
  id: 'percentage-1',
  brandLogoUrl: brandsConfig.bcc.logoUrl,
  brandName: brandsConfig.bcc.name,
  discountType: 'percentage',
  discountValue: 75,
  minPurchase: 30000,
  maxDiscount: 1000,
  onSeeDetails: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onSeeDetails'),
  onClaimCoupon: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onClaimCoupon')
};
const maxPercentageCoupon = {
  id: 'max-percentage-1',
  brandLogoUrl: brandsConfig.bcc.logoUrl,
  brandName: brandsConfig.bcc.name,
  discountType: 'max-percentage',
  discountValue: 50,
  minPurchase: 30000,
  maxDiscount: 500,
  onSeeDetails: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onSeeDetails'),
  onClaimCoupon: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onClaimCoupon')
};
const smallAmountCoupon = {
  id: 'small-amount-1',
  brandLogoUrl: brandsConfig.songkhla.logoUrl,
  brandName: brandsConfig.songkhla.name,
  discountType: 'amount',
  discountValue: 500,
  minPurchase: 50000,
  onSeeDetails: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onSeeDetails'),
  onClaimCoupon: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onClaimCoupon')
};
const smallPercentageCoupon = {
  id: 'small-percentage-1',
  brandLogoUrl: brandsConfig.philips.logoUrl,
  brandName: brandsConfig.philips.name,
  discountType: 'percentage',
  discountValue: 10,
  minPurchase: 25000,
  maxDiscount: 500,
  onSeeDetails: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onSeeDetails'),
  onClaimCoupon: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onClaimCoupon')
};
const largeAmountCoupon = {
  id: 'large-amount-1',
  brandLogoUrl: brandsConfig.haco.logoUrl,
  brandName: brandsConfig.haco.name,
  discountType: 'amount',
  discountValue: 5000,
  minPurchase: 50000,
  onSeeDetails: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onSeeDetails'),
  onClaimCoupon: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onClaimCoupon')
};
const meta = {
  title: 'UI/Card/CardCoupon',
  component: _card_coupon__WEBPACK_IMPORTED_MODULE_3__.CardCoupon,
  parameters: {
    "storySource": {
      "source": "// card-coupon.stories.tsx\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { action } from '@storybook/addon-actions';\nimport { CardCoupon, CardCouponSkeleton } from '../card-coupon';\n// Sample brand logos - replace with actual URLs in production\nconst brandsConfig = {\n    philips: {\n        logoUrl: '/images/philips-logo.png',\n        name: 'Philips'\n    },\n    lamptan: {\n        logoUrl: '/images/LAMPTAN.png',\n        name: 'LAMPTAN'\n    },\n    bcc: {\n        logoUrl: '/images/BCC.png',\n        name: 'Bangkok Cable'\n    },\n    songkhla: {\n        logoUrl: '/images/Sengaroon.png',\n        name: 'Songkhla Natrin'\n    },\n    panasonic: {\n        logoUrl: '/images/Panasonic.png',\n        name: 'Panasonic'\n    },\n    haco: {\n        logoUrl: '/images/HACO.png',\n        name: 'HACO'\n    }\n};\n// Define sample coupons based on the images provided\nconst fixedAmountCoupon = {\n    id: 'fixed-amount-1',\n    brandLogoUrl: brandsConfig.philips.logoUrl,\n    brandName: brandsConfig.philips.name,\n    discountType: 'amount',\n    discountValue: 2500,\n    minPurchase: 30000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst fixedAmountWithMaxDiscountCoupon = {\n    id: 'fixed-amount-max-2',\n    brandLogoUrl: brandsConfig.lamptan.logoUrl,\n    brandName: brandsConfig.lamptan.name,\n    discountType: 'amount',\n    discountValue: 2500,\n    minPurchase: 3000,\n    maxDiscount: 1000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst percentageCoupon = {\n    id: 'percentage-1',\n    brandLogoUrl: brandsConfig.bcc.logoUrl,\n    brandName: brandsConfig.bcc.name,\n    discountType: 'percentage',\n    discountValue: 75,\n    minPurchase: 30000,\n    maxDiscount: 1000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst maxPercentageCoupon = {\n    id: 'max-percentage-1',\n    brandLogoUrl: brandsConfig.bcc.logoUrl,\n    brandName: brandsConfig.bcc.name,\n    discountType: 'max-percentage',\n    discountValue: 50,\n    minPurchase: 30000,\n    maxDiscount: 500,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst smallAmountCoupon = {\n    id: 'small-amount-1',\n    brandLogoUrl: brandsConfig.songkhla.logoUrl,\n    brandName: brandsConfig.songkhla.name,\n    discountType: 'amount',\n    discountValue: 500,\n    minPurchase: 50000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst smallPercentageCoupon = {\n    id: 'small-percentage-1',\n    brandLogoUrl: brandsConfig.philips.logoUrl,\n    brandName: brandsConfig.philips.name,\n    discountType: 'percentage',\n    discountValue: 10,\n    minPurchase: 25000,\n    maxDiscount: 500,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst largeAmountCoupon = {\n    id: 'large-amount-1',\n    brandLogoUrl: brandsConfig.haco.logoUrl,\n    brandName: brandsConfig.haco.name,\n    discountType: 'amount',\n    discountValue: 5000,\n    minPurchase: 50000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst meta = {\n    title: 'UI/Card/CardCoupon',\n    component: CardCoupon,\n    parameters: {\n        layout: 'centered',\n        // Add a11y parameter for accessibility testing\n        a11y: {\n            config: {\n                rules: [\n                    {\n                        // Ensure all images have alt text\n                        id: 'image-alt',\n                        enabled: true\n                    }\n                ]\n            }\n        }\n    },\n    // Define argTypes for Storybook controls\n    argTypes: {\n        id: {\n            control: 'text',\n            description: 'Unique coupon identifier'\n        },\n        brandLogoUrl: {\n            control: 'text',\n            description: 'URL to the brand logo image'\n        },\n        brandName: {\n            control: 'text',\n            description: 'Brand name for alt text'\n        },\n        discountType: {\n            control: {\n                type: 'select',\n                options: [\n                    'amount',\n                    'percentage',\n                    'max-percentage'\n                ]\n            },\n            description: 'Type of discount - fixed amount, percentage, or max percentage'\n        },\n        discountValue: {\n            control: {\n                type: 'number',\n                min: 0\n            },\n            description: 'Value of the discount (amount or percentage)'\n        },\n        minPurchase: {\n            control: {\n                type: 'number',\n                min: 0\n            },\n            description: 'Minimum purchase required to use the coupon'\n        },\n        maxDiscount: {\n            control: {\n                type: 'number',\n                min: 0\n            },\n            description: 'Maximum discount amount for percentage discounts'\n        },\n        claimed: {\n            control: 'boolean',\n            description: 'Whether the coupon has been claimed already'\n        },\n        onSeeDetails: {\n            action: 'seeDetails',\n            description: 'Function called when see details button is clicked'\n        },\n        onClaimCoupon: {\n            action: 'claimCoupon',\n            description: 'Function called when claim button is clicked'\n        },\n        isClaimingCoupon: {\n            control: 'boolean',\n            description: 'Whether the coupon is currently being claimed'\n        },\n        showTermsOnly: {\n            control: 'boolean',\n            description: 'Whether to show only the terms button (without claim button)'\n        }\n    },\n    // Default values for the stories\n    args: {\n        onSeeDetails: action('onSeeDetails'),\n        onClaimCoupon: action('onClaimCoupon')\n    },\n    // Add decorators if needed\n    decorators: [\n        (Story)=>/*#__PURE__*/ _jsxDEV(\"div\", {\n                style: {\n                    maxWidth: '600px',\n                    width: '100%',\n                    padding: '20px'\n                },\n                children: /*#__PURE__*/ _jsxDEV(Story, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Card\\\\__stories__\\\\card-coupon.stories.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Card\\\\__stories__\\\\card-coupon.stories.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n    ],\n    // Add tags for filtering in Storybook\n    tags: [\n        'autodocs',\n        'coupon',\n        'card'\n    ]\n};\nexport default meta;\n// Fixed amount discount coupon (฿2,500)\nexport const FixedAmountCoupon = {\n    args: {\n        ...fixedAmountCoupon\n    }\n};\n// Fixed amount with max discount limit\nexport const FixedAmountWithMaxDiscount = {\n    args: {\n        ...fixedAmountWithMaxDiscountCoupon\n    }\n};\n// Percentage discount coupon (75%)\nexport const PercentageCoupon = {\n    args: {\n        ...percentageCoupon\n    }\n};\n// Maximum percentage discount coupon (สูงสุด 50%)\nexport const MaxPercentageCoupon = {\n    args: {\n        ...maxPercentageCoupon\n    }\n};\n// Small fixed amount coupon (฿500)\nexport const SmallAmountCoupon = {\n    args: {\n        ...smallAmountCoupon\n    }\n};\n// Small percentage discount coupon (10%)\nexport const SmallPercentageCoupon = {\n    args: {\n        ...smallPercentageCoupon\n    }\n};\n// Large fixed amount coupon (฿5,000)\nexport const LargeAmountCoupon = {\n    args: {\n        ...largeAmountCoupon\n    }\n};\n// Claimed coupon example\nexport const ClaimedCoupon = {\n    args: {\n        ...fixedAmountCoupon,\n        claimed: true\n    }\n};\n// Coupon in claiming state\nexport const ClaimingCoupon = {\n    args: {\n        ...percentageCoupon,\n        isClaimingCoupon: true\n    }\n};\n// Show only terms button\nexport const TermsOnlyCoupon = {\n    args: {\n        ...maxPercentageCoupon,\n        showTermsOnly: true\n    }\n};\n// Loading state example\nexport const LoadingState = {\n    render: ()=>/*#__PURE__*/ _jsxDEV(CardCouponSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Card\\\\__stories__\\\\card-coupon.stories.tsx\",\n            lineNumber: 280,\n            columnNumber: 17\n        }, this)\n};\n",
      "locationsMap": {
        "fixed-amount-coupon": {
          "startLoc": {
            "col": 33,
            "line": 225
          },
          "endLoc": {
            "col": 1,
            "line": 229
          },
          "startBody": {
            "col": 33,
            "line": 225
          },
          "endBody": {
            "col": 1,
            "line": 229
          }
        },
        "fixed-amount-with-max-discount": {
          "startLoc": {
            "col": 42,
            "line": 231
          },
          "endLoc": {
            "col": 1,
            "line": 235
          },
          "startBody": {
            "col": 42,
            "line": 231
          },
          "endBody": {
            "col": 1,
            "line": 235
          }
        },
        "percentage-coupon": {
          "startLoc": {
            "col": 32,
            "line": 237
          },
          "endLoc": {
            "col": 1,
            "line": 241
          },
          "startBody": {
            "col": 32,
            "line": 237
          },
          "endBody": {
            "col": 1,
            "line": 241
          }
        },
        "max-percentage-coupon": {
          "startLoc": {
            "col": 35,
            "line": 243
          },
          "endLoc": {
            "col": 1,
            "line": 247
          },
          "startBody": {
            "col": 35,
            "line": 243
          },
          "endBody": {
            "col": 1,
            "line": 247
          }
        },
        "small-amount-coupon": {
          "startLoc": {
            "col": 33,
            "line": 249
          },
          "endLoc": {
            "col": 1,
            "line": 253
          },
          "startBody": {
            "col": 33,
            "line": 249
          },
          "endBody": {
            "col": 1,
            "line": 253
          }
        },
        "small-percentage-coupon": {
          "startLoc": {
            "col": 37,
            "line": 255
          },
          "endLoc": {
            "col": 1,
            "line": 259
          },
          "startBody": {
            "col": 37,
            "line": 255
          },
          "endBody": {
            "col": 1,
            "line": 259
          }
        },
        "large-amount-coupon": {
          "startLoc": {
            "col": 33,
            "line": 261
          },
          "endLoc": {
            "col": 1,
            "line": 265
          },
          "startBody": {
            "col": 33,
            "line": 261
          },
          "endBody": {
            "col": 1,
            "line": 265
          }
        },
        "claimed-coupon": {
          "startLoc": {
            "col": 29,
            "line": 267
          },
          "endLoc": {
            "col": 1,
            "line": 272
          },
          "startBody": {
            "col": 29,
            "line": 267
          },
          "endBody": {
            "col": 1,
            "line": 272
          }
        },
        "claiming-coupon": {
          "startLoc": {
            "col": 30,
            "line": 274
          },
          "endLoc": {
            "col": 1,
            "line": 279
          },
          "startBody": {
            "col": 30,
            "line": 274
          },
          "endBody": {
            "col": 1,
            "line": 279
          }
        },
        "terms-only-coupon": {
          "startLoc": {
            "col": 31,
            "line": 281
          },
          "endLoc": {
            "col": 1,
            "line": 286
          },
          "startBody": {
            "col": 31,
            "line": 281
          },
          "endBody": {
            "col": 1,
            "line": 286
          }
        },
        "loading-state": {
          "startLoc": {
            "col": 28,
            "line": 288
          },
          "endLoc": {
            "col": 1,
            "line": 294
          },
          "startBody": {
            "col": 28,
            "line": 288
          },
          "endBody": {
            "col": 1,
            "line": 294
          }
        }
      }
    },
    layout: 'centered',
    // Add a11y parameter for accessibility testing
    a11y: {
      config: {
        rules: [{
          // Ensure all images have alt text
          id: 'image-alt',
          enabled: true
        }]
      }
    }
  },
  // Define argTypes for Storybook controls
  argTypes: {
    id: {
      control: 'text',
      description: 'Unique coupon identifier'
    },
    brandLogoUrl: {
      control: 'text',
      description: 'URL to the brand logo image'
    },
    brandName: {
      control: 'text',
      description: 'Brand name for alt text'
    },
    discountType: {
      control: {
        type: 'select',
        options: ['amount', 'percentage', 'max-percentage']
      },
      description: 'Type of discount - fixed amount, percentage, or max percentage'
    },
    discountValue: {
      control: {
        type: 'number',
        min: 0
      },
      description: 'Value of the discount (amount or percentage)'
    },
    minPurchase: {
      control: {
        type: 'number',
        min: 0
      },
      description: 'Minimum purchase required to use the coupon'
    },
    maxDiscount: {
      control: {
        type: 'number',
        min: 0
      },
      description: 'Maximum discount amount for percentage discounts'
    },
    claimed: {
      control: 'boolean',
      description: 'Whether the coupon has been claimed already'
    },
    onSeeDetails: {
      action: 'seeDetails',
      description: 'Function called when see details button is clicked'
    },
    onClaimCoupon: {
      action: 'claimCoupon',
      description: 'Function called when claim button is clicked'
    },
    isClaimingCoupon: {
      control: 'boolean',
      description: 'Whether the coupon is currently being claimed'
    },
    showTermsOnly: {
      control: 'boolean',
      description: 'Whether to show only the terms button (without claim button)'
    }
  },
  // Default values for the stories
  args: {
    onSeeDetails: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onSeeDetails'),
    onClaimCoupon: (0,_storybook_addon_actions__WEBPACK_IMPORTED_MODULE_2__.action)('onClaimCoupon')
  },
  // Add decorators if needed
  decorators: [Story => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
    style: {
      maxWidth: '600px',
      width: '100%',
      padding: '20px'
    },
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Story, {}, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\__stories__\\card-coupon.stories.tsx",
      lineNumber: 194,
      columnNumber: 9
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\__stories__\\card-coupon.stories.tsx",
    lineNumber: 193,
    columnNumber: 7
  }, undefined)],
  // Add tags for filtering in Storybook
  tags: ['autodocs', 'coupon', 'card']
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
// Fixed amount discount coupon (฿2,500)
const FixedAmountCoupon = {
  args: {
    ...fixedAmountCoupon
  }
};
;
// Fixed amount with max discount limit
const FixedAmountWithMaxDiscount = {
  args: {
    ...fixedAmountWithMaxDiscountCoupon
  }
};
;
// Percentage discount coupon (75%)
const PercentageCoupon = {
  args: {
    ...percentageCoupon
  }
};
;
// Maximum percentage discount coupon (สูงสุด 50%)
const MaxPercentageCoupon = {
  args: {
    ...maxPercentageCoupon
  }
};
;
// Small fixed amount coupon (฿500)
const SmallAmountCoupon = {
  args: {
    ...smallAmountCoupon
  }
};
;
// Small percentage discount coupon (10%)
const SmallPercentageCoupon = {
  args: {
    ...smallPercentageCoupon
  }
};
;
// Large fixed amount coupon (฿5,000)
const LargeAmountCoupon = {
  args: {
    ...largeAmountCoupon
  }
};
;
// Claimed coupon example
const ClaimedCoupon = {
  args: {
    ...fixedAmountCoupon,
    claimed: true
  }
};
;
// Coupon in claiming state
const ClaimingCoupon = {
  args: {
    ...percentageCoupon,
    isClaimingCoupon: true
  }
};
;
// Show only terms button
const TermsOnlyCoupon = {
  args: {
    ...maxPercentageCoupon,
    showTermsOnly: true
  }
};
;
// Loading state example
const LoadingState = {
  render: () => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_card_coupon__WEBPACK_IMPORTED_MODULE_3__.CardCouponSkeleton, {}, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\__stories__\\card-coupon.stories.tsx",
    lineNumber: 280,
    columnNumber: 17
  }, undefined)
};
;
const __namedExportsOrder = ["FixedAmountCoupon", "FixedAmountWithMaxDiscount", "PercentageCoupon", "MaxPercentageCoupon", "SmallAmountCoupon", "SmallPercentageCoupon", "LargeAmountCoupon", "ClaimedCoupon", "ClaimingCoupon", "TermsOnlyCoupon", "LoadingState"];
FixedAmountCoupon.parameters = {
  ...FixedAmountCoupon.parameters,
  docs: {
    ...FixedAmountCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...fixedAmountCoupon\n  }\n}",
      ...FixedAmountCoupon.parameters?.docs?.source
    }
  }
};
FixedAmountWithMaxDiscount.parameters = {
  ...FixedAmountWithMaxDiscount.parameters,
  docs: {
    ...FixedAmountWithMaxDiscount.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...fixedAmountWithMaxDiscountCoupon\n  }\n}",
      ...FixedAmountWithMaxDiscount.parameters?.docs?.source
    }
  }
};
PercentageCoupon.parameters = {
  ...PercentageCoupon.parameters,
  docs: {
    ...PercentageCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...percentageCoupon\n  }\n}",
      ...PercentageCoupon.parameters?.docs?.source
    }
  }
};
MaxPercentageCoupon.parameters = {
  ...MaxPercentageCoupon.parameters,
  docs: {
    ...MaxPercentageCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...maxPercentageCoupon\n  }\n}",
      ...MaxPercentageCoupon.parameters?.docs?.source
    }
  }
};
SmallAmountCoupon.parameters = {
  ...SmallAmountCoupon.parameters,
  docs: {
    ...SmallAmountCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...smallAmountCoupon\n  }\n}",
      ...SmallAmountCoupon.parameters?.docs?.source
    }
  }
};
SmallPercentageCoupon.parameters = {
  ...SmallPercentageCoupon.parameters,
  docs: {
    ...SmallPercentageCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...smallPercentageCoupon\n  }\n}",
      ...SmallPercentageCoupon.parameters?.docs?.source
    }
  }
};
LargeAmountCoupon.parameters = {
  ...LargeAmountCoupon.parameters,
  docs: {
    ...LargeAmountCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...largeAmountCoupon\n  }\n}",
      ...LargeAmountCoupon.parameters?.docs?.source
    }
  }
};
ClaimedCoupon.parameters = {
  ...ClaimedCoupon.parameters,
  docs: {
    ...ClaimedCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...fixedAmountCoupon,\n    claimed: true\n  }\n}",
      ...ClaimedCoupon.parameters?.docs?.source
    }
  }
};
ClaimingCoupon.parameters = {
  ...ClaimingCoupon.parameters,
  docs: {
    ...ClaimingCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...percentageCoupon,\n    isClaimingCoupon: true\n  }\n}",
      ...ClaimingCoupon.parameters?.docs?.source
    }
  }
};
TermsOnlyCoupon.parameters = {
  ...TermsOnlyCoupon.parameters,
  docs: {
    ...TermsOnlyCoupon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...maxPercentageCoupon,\n    showTermsOnly: true\n  }\n}",
      ...TermsOnlyCoupon.parameters?.docs?.source
    }
  }
};
LoadingState.parameters = {
  ...LoadingState.parameters,
  docs: {
    ...LoadingState.parameters?.docs,
    source: {
      originalSource: "{\n  render: () => <CardCouponSkeleton />\n}",
      ...LoadingState.parameters?.docs?.source
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./src/components/Card/card-coupon.tsx":
/*!*********************************************!*\
  !*** ./src/components/Card/card-coupon.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache;
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CardCoupon: () => (/* binding */ CardCoupon),
/* harmony export */   CardCouponSkeleton: () => (/* binding */ CardCouponSkeleton),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ "./node_modules/@storybook/nextjs/dist/images/next-image.mjs");
/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ "./src/lib/utils.ts");
/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ "./src/components/ui/card.tsx");
/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ "./src/components/ui/button.tsx");
/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ "./src/components/ui/skeleton.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");

__webpack_require__.$Refresh$.runtime = /*#__PURE__*/ (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__, 2)));

function cov_24j29p8p12() {
  var path = "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx";
  var hash = "3845ab059ccfe0afb237f5254762034a2ecdffd1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
    statementMap: {
      "0": {
        start: {
          line: 8,
          column: 26
        },
        end: {
          line: 261,
          column: 1
        }
      },
      "1": {
        start: {
          line: 9,
          column: 29
        },
        end: {
          line: 13,
          column: 5
        }
      },
      "2": {
        start: {
          line: 10,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "3": {
        start: {
          line: 11,
          column: 12
        },
        end: {
          line: 11,
          column: 29
        }
      },
      "4": {
        start: {
          line: 14,
          column: 30
        },
        end: {
          line: 18,
          column: 5
        }
      },
      "5": {
        start: {
          line: 15,
          column: 8
        },
        end: {
          line: 17,
          column: 9
        }
      },
      "6": {
        start: {
          line: 16,
          column: 12
        },
        end: {
          line: 16,
          column: 30
        }
      },
      "7": {
        start: {
          line: 19,
          column: 32
        },
        end: {
          line: 26,
          column: 16
        }
      },
      "8": {
        start: {
          line: 19,
          column: 50
        },
        end: {
          line: 26,
          column: 16
        }
      },
      "9": {
        start: {
          line: 27,
          column: 32
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "10": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 65,
          column: 9
        }
      },
      "11": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 39,
          column: 21
        }
      },
      "12": {
        start: {
          line: 40,
          column: 15
        },
        end: {
          line: 65,
          column: 9
        }
      },
      "13": {
        start: {
          line: 41,
          column: 12
        },
        end: {
          line: 51,
          column: 21
        }
      },
      "14": {
        start: {
          line: 52,
          column: 15
        },
        end: {
          line: 65,
          column: 9
        }
      },
      "15": {
        start: {
          line: 53,
          column: 12
        },
        end: {
          line: 64,
          column: 21
        }
      },
      "16": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 66,
          column: 20
        }
      },
      "17": {
        start: {
          line: 68,
          column: 38
        },
        end: {
          line: 96,
          column: 5
        }
      },
      "18": {
        start: {
          line: 69,
          column: 8
        },
        end: {
          line: 94,
          column: 9
        }
      },
      "19": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 80,
          column: 21
        }
      },
      "20": {
        start: {
          line: 81,
          column: 15
        },
        end: {
          line: 94,
          column: 9
        }
      },
      "21": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 93,
          column: 21
        }
      },
      "22": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 95,
          column: 20
        }
      },
      "23": {
        start: {
          line: 97,
          column: 28
        },
        end: {
          line: 97,
          column: 71
        }
      },
      "24": {
        start: {
          line: 98,
          column: 29
        },
        end: {
          line: 98,
          column: 148
        }
      },
      "25": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 260,
          column: 13
        }
      },
      "26": {
        start: {
          line: 155,
          column: 68
        },
        end: {
          line: 161,
          column: 52
        }
      },
      "27": {
        start: {
          line: 262,
          column: 0
        },
        end: {
          line: 262,
          column: 16
        }
      },
      "28": {
        start: {
          line: 263,
          column: 34
        },
        end: {
          line: 370,
          column: 12
        }
      },
      "29": {
        start: {
          line: 263,
          column: 52
        },
        end: {
          line: 370,
          column: 12
        }
      },
      "30": {
        start: {
          line: 371,
          column: 0
        },
        end: {
          line: 371,
          column: 25
        }
      },
      "31": {
        start: {
          line: 373,
          column: 0
        },
        end: {
          line: 527,
          column: 2
        }
      },
      "32": {
        start: {
          line: 528,
          column: 0
        },
        end: {
          line: 532,
          column: 2
        }
      },
      "33": {
        start: {
          line: 534,
          column: 0
        },
        end: {
          line: 534,
          column: 31
        }
      },
      "34": {
        start: {
          line: 535,
          column: 0
        },
        end: {
          line: 535,
          column: 40
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 8,
            column: 26
          },
          end: {
            line: 8,
            column: 27
          }
        },
        loc: {
          start: {
            line: 8,
            column: 222
          },
          end: {
            line: 261,
            column: 1
          }
        },
        line: 8
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 9,
            column: 29
          },
          end: {
            line: 9,
            column: 30
          }
        },
        loc: {
          start: {
            line: 9,
            column: 33
          },
          end: {
            line: 13,
            column: 5
          }
        },
        line: 9
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 34
          },
          end: {
            line: 18,
            column: 5
          }
        },
        line: 14
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 19,
            column: 32
          },
          end: {
            line: 19,
            column: 33
          }
        },
        loc: {
          start: {
            line: 19,
            column: 50
          },
          end: {
            line: 26,
            column: 16
          }
        },
        line: 19
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 27,
            column: 32
          },
          end: {
            line: 27,
            column: 33
          }
        },
        loc: {
          start: {
            line: 27,
            column: 36
          },
          end: {
            line: 67,
            column: 5
          }
        },
        line: 27
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 68,
            column: 38
          },
          end: {
            line: 68,
            column: 39
          }
        },
        loc: {
          start: {
            line: 68,
            column: 42
          },
          end: {
            line: 96,
            column: 5
          }
        },
        line: 68
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 155,
            column: 46
          },
          end: {
            line: 155,
            column: 47
          }
        },
        loc: {
          start: {
            line: 155,
            column: 68
          },
          end: {
            line: 161,
            column: 52
          }
        },
        line: 155
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 263,
            column: 34
          },
          end: {
            line: 263,
            column: 35
          }
        },
        loc: {
          start: {
            line: 263,
            column: 52
          },
          end: {
            line: 370,
            column: 12
          }
        },
        line: 263
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 8,
            column: 113
          },
          end: {
            line: 8,
            column: 128
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 8,
            column: 123
          },
          end: {
            line: 8,
            column: 128
          }
        }],
        line: 8
      },
      "1": {
        loc: {
          start: {
            line: 8,
            column: 170
          },
          end: {
            line: 8,
            column: 194
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 8,
            column: 189
          },
          end: {
            line: 8,
            column: 194
          }
        }],
        line: 8
      },
      "2": {
        loc: {
          start: {
            line: 8,
            column: 196
          },
          end: {
            line: 8,
            column: 217
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 8,
            column: 212
          },
          end: {
            line: 8,
            column: 217
          }
        }],
        line: 8
      },
      "3": {
        loc: {
          start: {
            line: 10,
            column: 8
          },
          end: {
            line: 12,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 8
          },
          end: {
            line: 12,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "4": {
        loc: {
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 17,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 8
          },
          end: {
            line: 17,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 12
          },
          end: {
            line: 15,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 12
          },
          end: {
            line: 15,
            column: 25
          }
        }, {
          start: {
            line: 15,
            column: 29
          },
          end: {
            line: 15,
            column: 46
          }
        }, {
          start: {
            line: 15,
            column: 50
          },
          end: {
            line: 15,
            column: 58
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 65,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 8
          },
          end: {
            line: 65,
            column: 9
          }
        }, {
          start: {
            line: 40,
            column: 15
          },
          end: {
            line: 65,
            column: 9
          }
        }],
        line: 28
      },
      "7": {
        loc: {
          start: {
            line: 40,
            column: 15
          },
          end: {
            line: 65,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 15
          },
          end: {
            line: 65,
            column: 9
          }
        }, {
          start: {
            line: 52,
            column: 15
          },
          end: {
            line: 65,
            column: 9
          }
        }],
        line: 40
      },
      "8": {
        loc: {
          start: {
            line: 52,
            column: 15
          },
          end: {
            line: 65,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 52,
            column: 15
          },
          end: {
            line: 65,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 52
      },
      "9": {
        loc: {
          start: {
            line: 69,
            column: 8
          },
          end: {
            line: 94,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 8
          },
          end: {
            line: 94,
            column: 9
          }
        }, {
          start: {
            line: 81,
            column: 15
          },
          end: {
            line: 94,
            column: 9
          }
        }],
        line: 69
      },
      "10": {
        loc: {
          start: {
            line: 69,
            column: 12
          },
          end: {
            line: 69,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 12
          },
          end: {
            line: 69,
            column: 37
          }
        }, {
          start: {
            line: 69,
            column: 41
          },
          end: {
            line: 69,
            column: 52
          }
        }],
        line: 69
      },
      "11": {
        loc: {
          start: {
            line: 81,
            column: 15
          },
          end: {
            line: 94,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 81,
            column: 15
          },
          end: {
            line: 94,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 81
      },
      "12": {
        loc: {
          start: {
            line: 81,
            column: 19
          },
          end: {
            line: 81,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 20
          },
          end: {
            line: 81,
            column: 49
          }
        }, {
          start: {
            line: 81,
            column: 53
          },
          end: {
            line: 81,
            column: 86
          }
        }, {
          start: {
            line: 81,
            column: 91
          },
          end: {
            line: 81,
            column: 102
          }
        }],
        line: 81
      },
      "13": {
        loc: {
          start: {
            line: 87,
            column: 20
          },
          end: {
            line: 87,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 20
          },
          end: {
            line: 87,
            column: 31
          }
        }, {
          start: {
            line: 87,
            column: 35
          },
          end: {
            line: 87,
            column: 101
          }
        }],
        line: 87
      },
      "14": {
        loc: {
          start: {
            line: 97,
            column: 28
          },
          end: {
            line: 97,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 97,
            column: 38
          },
          end: {
            line: 97,
            column: 53
          }
        }, {
          start: {
            line: 97,
            column: 56
          },
          end: {
            line: 97,
            column: 71
          }
        }],
        line: 97
      },
      "15": {
        loc: {
          start: {
            line: 98,
            column: 29
          },
          end: {
            line: 98,
            column: 148
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 98,
            column: 39
          },
          end: {
            line: 98,
            column: 103
          }
        }, {
          start: {
            line: 98,
            column: 106
          },
          end: {
            line: 98,
            column: 148
          }
        }],
        line: 98
      },
      "16": {
        loc: {
          start: {
            line: 225,
            column: 36
          },
          end: {
            line: 236,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 225,
            column: 36
          },
          end: {
            line: 225,
            column: 50
          }
        }, {
          start: {
            line: 225,
            column: 68
          },
          end: {
            line: 236,
            column: 44
          }
        }],
        line: 225
      },
      "17": {
        loc: {
          start: {
            line: 228,
            column: 50
          },
          end: {
            line: 228,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 50
          },
          end: {
            line: 228,
            column: 66
          }
        }, {
          start: {
            line: 228,
            column: 70
          },
          end: {
            line: 228,
            column: 77
          }
        }],
        line: 228
      },
      "18": {
        loc: {
          start: {
            line: 229,
            column: 111
          },
          end: {
            line: 229,
            column: 155
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 111
          },
          end: {
            line: 229,
            column: 127
          }
        }, {
          start: {
            line: 229,
            column: 131
          },
          end: {
            line: 229,
            column: 155
          }
        }],
        line: 229
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      sources: ["C:/Users/<USER>/Desktop/fristJob/sangaroon-nakharin-ecommerce/frontend/home/<USER>/components/Card/card-coupon.tsx"],
      sourcesContent: ["import React from 'react';\r\nimport Image from 'next/image';\r\nimport { cn } from \"@/lib/utils\";\r\nimport { \r\n  Card, \r\n  CardContent,\r\n} from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nexport interface CardCouponProps {\r\n  id: string;\r\n  brandLogoUrl: string;\r\n  brandName: string;\r\n  discountType: 'amount' | 'percentage' | 'max-percentage';\r\n  discountValue: number;\r\n  minPurchase?: number;\r\n  maxDiscount?: number;\r\n  claimed?: boolean;\r\n  className?: string;\r\n  onSeeDetails?: (id: string) => void;\r\n  onClaimCoupon?: (id: string) => void;\r\n  isClaimingCoupon?: boolean;\r\n  showTermsOnly?: boolean;\r\n}\r\n\r\nexport const CardCoupon = ({\r\n  id,\r\n  brandLogoUrl,\r\n  brandName,\r\n  discountType,\r\n  discountValue,\r\n  minPurchase,\r\n  maxDiscount,\r\n  claimed = false,\r\n  className,\r\n  onSeeDetails,\r\n  onClaimCoupon,\r\n  isClaimingCoupon = false,\r\n  showTermsOnly = false,\r\n}: CardCouponProps) => {\r\n  const handleSeeDetails = () => {\r\n    if (onSeeDetails) {\r\n      onSeeDetails(id);\r\n    }\r\n  };\r\n\r\n  const handleClaimCoupon = () => {\r\n    if (onClaimCoupon && !isClaimingCoupon && !claimed) {\r\n      onClaimCoupon(id);\r\n    }\r\n  };\r\n\r\n  const renderDiscountTitle = () => (\r\n    <div className=\"text-gray-600 text-sm\">\u0E2A\u0E48\u0E27\u0E19\u0E25\u0E14</div>\r\n  );\r\n\r\n  const renderDiscountValue = () => {\r\n    if (discountType === 'amount') {\r\n      return (\r\n        <div className=\"text-3xl font-bold text-blue-900\">\r\n          {new Intl.NumberFormat('th-TH').format(discountValue)} \u0E1A\u0E32\u0E17\r\n        </div>\r\n      );\r\n    } else if (discountType === 'percentage') {\r\n      return (\r\n        <div className=\"text-3xl font-bold text-blue-900\">\r\n          {discountValue}%\r\n        </div>\r\n      );\r\n    } else if (discountType === 'max-percentage') {\r\n      return (\r\n        <div className=\"text-3xl font-bold text-blue-900\">\r\n          \u0E2A\u0E39\u0E07\u0E2A\u0E38\u0E14 {discountValue}%\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const renderPurchaseRequirement = () => {\r\n    if (discountType === 'amount' && minPurchase) {\r\n      return (\r\n        <div className=\"text-xs text-gray-500\">\r\n          \u0E40\u0E21\u0E37\u0E48\u0E2D\u0E0B\u0E37\u0E49\u0E2D\u0E2A\u0E34\u0E19\u0E04\u0E49\u0E32\u0E02\u0E31\u0E49\u0E19\u0E15\u0E48\u0E33 \u0E3F{new Intl.NumberFormat('th-TH').format(minPurchase)}\r\n        </div>\r\n      );\r\n    } else if ((discountType === 'percentage' || discountType === 'max-percentage') && minPurchase) {\r\n      return (\r\n        <div className=\"text-xs text-gray-500\">\r\n          \u0E0B\u0E37\u0E49\u0E2D\u0E02\u0E31\u0E49\u0E19\u0E15\u0E48\u0E33 \u0E3F{new Intl.NumberFormat('th-TH').format(minPurchase)}\r\n          {maxDiscount && ` \u0E25\u0E14\u0E2A\u0E39\u0E07\u0E2A\u0E38\u0E14 \u0E3F${new Intl.NumberFormat('th-TH').format(maxDiscount)}`}\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const claimButtonText = claimed ? '\u0E40\u0E01\u0E47\u0E1A\u0E04\u0E39\u0E1B\u0E2D\u0E07\u0E41\u0E25\u0E49\u0E27' : '\u0E40\u0E01\u0E47\u0E1A\u0E04\u0E39\u0E1B\u0E2D\u0E07\u0E40\u0E25\u0E22!';\r\n  const claimButtonClass = claimed \r\n    ? 'bg-white text-gray-400 border border-gray-300 hover:bg-gray-50' \r\n    : 'bg-blue-900 text-white hover:bg-blue-800';\r\n\r\n  return (\r\n    <Card\r\n  className={cn(\r\n    \"relative overflow-hidden border border-gray-200 \",\r\n    \"rounded-xl w-full md:w-[444px] min-h-[168px] bg-white z-0\", // \u0E1B\u0E23\u0E31\u0E1A\u0E02\u0E19\u0E32\u0E14\u0E43\u0E2B\u0E49 responsive\r\n    // ,\r\n    className\r\n  )}\r\n  data-testid=\"card-coupon\"\r\n>\r\n  {/* Notch left */}\r\n  <div className=\"absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-12 h-12 bg-[#f5f5f5] rounded-full z-10 \" />\r\n\r\n  {/* Notch right */}\r\n  <div className=\"absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-12 h-12 bg-[#f5f5f5] rounded-full z-10 \" />\r\n\r\n      <CardContent className=\"p-4 md:p-6 flex flex-row h-full\">\r\n        {/* Brand logo section */}\r\n        <div className=\"w-1/3 flex items-center justify-start relative pr-6\"> {/* \u0E40\u0E1E\u0E34\u0E48\u0E21 padding-right */}\r\n          <div className=\"relative h-[70px] w-[70px] md:h-[100px] md:w-[100px]\"> {/* \u0E1B\u0E23\u0E31\u0E1A\u0E02\u0E19\u0E32\u0E14\u0E23\u0E39\u0E1B\u0E43\u0E2B\u0E49\u0E40\u0E25\u0E47\u0E01\u0E25\u0E07\u0E1A\u0E19\u0E21\u0E37\u0E2D\u0E16\u0E37\u0E2D */}\r\n            <Image\r\n              src={brandLogoUrl}\r\n              alt={brandName}\r\n              fill\r\n              className=\"object-contain\"\r\n              sizes=\"(max-width: 768px) 70px, 100px\"\r\n              data-testid=\"coupon-brand-logo\"\r\n            />\r\n          </div>\r\n          {/* Vertical dotted line */}\r\n          <div className=\"absolute right-3 md:right-4 top-0 h-full\"> {/* \u0E1B\u0E23\u0E31\u0E1A\u0E15\u0E33\u0E41\u0E2B\u0E19\u0E48\u0E07\u0E40\u0E2A\u0E49\u0E19\u0E1B\u0E23\u0E30 */}\r\n            <div className=\"h-full flex flex-col justify-around items-center\">\r\n              {[...Array(6)].map((_, i) => (\r\n                <div \r\n                  key={i} \r\n                  className=\"w-[1px] h-[10px] md:h-[12px] border-r border-[#F0F0F0]\" \r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {/* Discount information and actions section */}\r\n        <div className=\"w-2/3 flex flex-col justify-between pl-2 md:pl-4\">\r\n          <div className=\"space-y-2\" data-testid=\"coupon-discount-info\">\r\n            {renderDiscountTitle()}\r\n            <div className=\"text-2xl md:text-3xl font-bold text-blue-900\">\r\n              {renderDiscountValue()}\r\n            </div>\r\n            <div className=\"text-[11px] md:text-xs text-gray-500\">\r\n              {renderPurchaseRequirement()}\r\n            </div>\r\n          </div>\r\n          \r\n          {/* \u0E1B\u0E23\u0E31\u0E1A\u0E2A\u0E48\u0E27\u0E19\u0E1B\u0E38\u0E48\u0E21 */}\r\n          <div className=\"flex flex-wrap gap-2 mt-4 w-full\" data-testid=\"coupon-actions\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={handleSeeDetails}\r\n              className=\"hidden md:inline-flex rounded-lg h-10 text-gray-600 border-gray-200 bg-[#F6F7F9] hover:bg-gray-50 hover:text-gray-700 text-sm\"\r\n              data-testid=\"see-details-button\"\r\n            >\r\n              \u0E40\u0E07\u0E37\u0E48\u0E2D\u0E19\u0E44\u0E02\r\n            </Button>\r\n            \r\n            {!showTermsOnly && (\r\n              <Button\r\n                size=\"sm\"\r\n                onClick={handleClaimCoupon}\r\n                disabled={isClaimingCoupon || claimed}\r\n                className={cn(\r\n                  \"rounded-lg h-10 font-normal text-sm\",\r\n                  claimButtonClass,\r\n                  isClaimingCoupon && \"opacity-70 cursor-wait\"\r\n                )}\r\n                data-testid=\"claim-coupon-button\"\r\n              >\r\n                {claimButtonText}\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport const CardCouponSkeleton = () => (\r\n  <Card className=\"relative overflow-hidden border border-gray-200  rounded-xl w-[444px] h-[168px] bg-white z-0\">\r\n    <div className=\"absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-6 h-6 bg-white border border-gray-200 rounded-full z-10\" />\r\n    <div className=\"absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-6 h-6 bg-white border border-gray-200 rounded-full z-10\" />\r\n    \r\n    <CardContent className=\"p-4 flex flex-col md:flex-row\">\r\n      <div className=\"w-full md:w-1/3 flex items-center justify-center md:justify-start mb-4 md:mb-0\">\r\n        <Skeleton className=\"h-12 w-32\" />\r\n      </div>\r\n      \r\n      <div className=\"w-full md:w-2/3 flex flex-col\">\r\n        <div className=\"mb-2\">\r\n          <Skeleton className=\"h-4 w-16 mb-1\" />\r\n          <Skeleton className=\"h-8 w-32 mb-1\" />\r\n          <Skeleton className=\"h-3 w-48\" />\r\n        </div>\r\n        \r\n        <div className=\"flex flex-wrap gap-2 mt-1\">\r\n          <Skeleton className=\"h-10 w-24 rounded-lg\" />\r\n          <Skeleton className=\"h-10 w-32 rounded-lg\" />\r\n        </div>\r\n      </div>\r\n    </CardContent>\r\n  </Card>\r\n);\r\n\r\nexport default CardCoupon;"],
      names: [],
      mappings: ";AAAA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAkBpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1B,CAAC,CAAC,CAAC,CACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;IACH,CAAC,CAAC;IAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,CAAC;IAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WACjC,QAAC,CAAC,CAAC,CAAC;YAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;IAGrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aACN,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;QAGhE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aACN,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAAC,CAAC;;;;;;;QAGtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aACN,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAAC;oBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAAC,CAAC;;;;;;;QAG7B,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC;IAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aACN,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAAC;oBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;QAGlF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aACN,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oBAAC;oBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;QAGzF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC;IAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aACN,QAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAC7D,CAD8D,AAC7D,CAD8D,AAC7D,CAD8D,AAC7D,CAD8D,AAC7D,CAD8D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;0BAGzB,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;0BAGvH,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;0BAEnH,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kCAEvD,QAAC,CAAC,CAAC,CAAC;wBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;4BAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0CAC/F,QAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kDACzG,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCACf,CAAC,CAAC,CAAC,CAAC;wCACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;0CAInC,QAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kDACnF,QAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kDAC/D,CAAC,CAAC,CAAC;+CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;yCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAC5B,QAAC,CAAC,CAAC,CAAC,CAAC;gDAEH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;+CAD9D,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;kCAQlB,QAAC,CAAC,CAAC,CAAC;wBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;0CAChE,QAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;oCAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kDACvB,QAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kDAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;kDAEzB,QAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kDACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;0CAKjC,QAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kDAC7E,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCACjB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;wCACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCACzI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kDACjC;;;;;;oCAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAClB,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCACL,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC;wCACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAE9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kDAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC,CAAC,CAAC;;AAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WACvC,QAAC,CAAC,CAAC,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;0BAC7G,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;0BACvI,QAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;0BAEvI,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kCACrD,QAAC,CAAC,CAAC,CAAC;wBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gDAC9F,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;kCAGpC,QAAC,CAAC,CAAC,CAAC;wBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;0CAC7C,QAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kDACpB,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;kDACtC,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;kDACtC,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;0CAGnC,QAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kDACzC,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;kDAC7C,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;aAKrD;MAxBW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AA0BjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAAA,WAAA,YAAA,GAAA;IAAA,eAAA;IAAA,WAAA,EAAA;IAAA,eAAA;IAAA,SAAA;QAAA,MAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;QAAA;QAAA,gBAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;QAAA;QAAA,aAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;QAAA;QAAA,gBAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;gBAAA,OAAA;gBAAA,YAAA;oBAAA;wBAAA,QAAA;wBAAA,SAAA;oBAAA;oBAAA;wBAAA,QAAA;wBAAA,SAAA;oBAAA;oBAAA;wBAAA,QAAA;wBAAA,SAAA;oBAAA;iBAAA;YAAA;YAAA,eAAA;QAAA;QAAA,iBAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;QAAA;QAAA,eAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;QAAA;QAAA,eAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;QAAA;QAAA,WAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;YAAA,gBAAA;gBAAA,SAAA;gBAAA,YAAA;YAAA;QAAA;QAAA,aAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;QAAA;QAAA,gBAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;gBAAA,QAAA;gBAAA,OAAA;gBAAA,aAAA;oBAAA,aAAA;wBAAA;4BAAA,QAAA;gCAAA,QAAA;4BAAA;4BAAA,QAAA;wBAAA;qBAAA;oBAAA,UAAA;wBAAA,QAAA;oBAAA;gBAAA;YAAA;YAAA,eAAA;QAAA;QAAA,iBAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;gBAAA,QAAA;gBAAA,OAAA;gBAAA,aAAA;oBAAA,aAAA;wBAAA;4BAAA,QAAA;gCAAA,QAAA;4BAAA;4BAAA,QAAA;wBAAA;qBAAA;oBAAA,UAAA;wBAAA,QAAA;oBAAA;gBAAA;YAAA;YAAA,eAAA;QAAA;QAAA,oBAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;YAAA,gBAAA;gBAAA,SAAA;gBAAA,YAAA;YAAA;QAAA;QAAA,iBAAA;YAAA,YAAA;YAAA,UAAA;gBAAA,QAAA;YAAA;YAAA,eAAA;YAAA,gBAAA;gBAAA,SAAA;gBAAA,YAAA;YAAA;QAAA;IAAA;AAAA;AAAA,mBAAA,YAAA,GAAA;IAAA,eAAA;IAAA,WAAA,EAAA;IAAA,eAAA;AAAA"
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3845ab059ccfe0afb237f5254762034a2ecdffd1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_24j29p8p12 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_24j29p8p12();







cov_24j29p8p12().s[0]++;
const CardCoupon = ({
  id,
  brandLogoUrl,
  brandName,
  discountType,
  discountValue,
  minPurchase,
  maxDiscount,
  claimed = (cov_24j29p8p12().b[0][0]++, false),
  className,
  onSeeDetails,
  onClaimCoupon,
  isClaimingCoupon = (cov_24j29p8p12().b[1][0]++, false),
  showTermsOnly = (cov_24j29p8p12().b[2][0]++, false)
}) => {
  cov_24j29p8p12().f[0]++;
  cov_24j29p8p12().s[1]++;
  const handleSeeDetails = () => {
    cov_24j29p8p12().f[1]++;
    cov_24j29p8p12().s[2]++;
    if (onSeeDetails) {
      cov_24j29p8p12().b[3][0]++;
      cov_24j29p8p12().s[3]++;
      onSeeDetails(id);
    } else {
      cov_24j29p8p12().b[3][1]++;
    }
  };
  cov_24j29p8p12().s[4]++;
  const handleClaimCoupon = () => {
    cov_24j29p8p12().f[2]++;
    cov_24j29p8p12().s[5]++;
    if ((cov_24j29p8p12().b[5][0]++, onClaimCoupon) && (cov_24j29p8p12().b[5][1]++, !isClaimingCoupon) && (cov_24j29p8p12().b[5][2]++, !claimed)) {
      cov_24j29p8p12().b[4][0]++;
      cov_24j29p8p12().s[6]++;
      onClaimCoupon(id);
    } else {
      cov_24j29p8p12().b[4][1]++;
    }
  };
  cov_24j29p8p12().s[7]++;
  const renderDiscountTitle = () => {
    cov_24j29p8p12().f[3]++;
    cov_24j29p8p12().s[8]++;
    return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
      className: "text-gray-600 text-sm",
      children: "ส่วนลด"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
      lineNumber: 55,
      columnNumber: 5
    }, undefined);
  };
  cov_24j29p8p12().s[9]++;
  const renderDiscountValue = () => {
    cov_24j29p8p12().f[4]++;
    cov_24j29p8p12().s[10]++;
    if (discountType === 'amount') {
      cov_24j29p8p12().b[6][0]++;
      cov_24j29p8p12().s[11]++;
      return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
        className: "text-3xl font-bold text-blue-900",
        children: [new Intl.NumberFormat('th-TH').format(discountValue), " บาท"]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
        lineNumber: 61,
        columnNumber: 9
      }, undefined);
    } else {
      cov_24j29p8p12().b[6][1]++;
      cov_24j29p8p12().s[12]++;
      if (discountType === 'percentage') {
        cov_24j29p8p12().b[7][0]++;
        cov_24j29p8p12().s[13]++;
        return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "text-3xl font-bold text-blue-900",
          children: [discountValue, "%"]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 67,
          columnNumber: 9
        }, undefined);
      } else {
        cov_24j29p8p12().b[7][1]++;
        cov_24j29p8p12().s[14]++;
        if (discountType === 'max-percentage') {
          cov_24j29p8p12().b[8][0]++;
          cov_24j29p8p12().s[15]++;
          return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: "text-3xl font-bold text-blue-900",
            children: ["สูงสุด ", discountValue, "%"]
          }, void 0, true, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 73,
            columnNumber: 9
          }, undefined);
        } else {
          cov_24j29p8p12().b[8][1]++;
        }
      }
    }
    cov_24j29p8p12().s[16]++;
    return null;
  };
  cov_24j29p8p12().s[17]++;
  const renderPurchaseRequirement = () => {
    cov_24j29p8p12().f[5]++;
    cov_24j29p8p12().s[18]++;
    if ((cov_24j29p8p12().b[10][0]++, discountType === 'amount') && (cov_24j29p8p12().b[10][1]++, minPurchase)) {
      cov_24j29p8p12().b[9][0]++;
      cov_24j29p8p12().s[19]++;
      return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
        className: "text-xs text-gray-500",
        children: ["เมื่อซื้อสินค้าขั้นต่ำ ฿", new Intl.NumberFormat('th-TH').format(minPurchase)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
        lineNumber: 84,
        columnNumber: 9
      }, undefined);
    } else {
      cov_24j29p8p12().b[9][1]++;
      cov_24j29p8p12().s[20]++;
      if (((cov_24j29p8p12().b[12][0]++, discountType === 'percentage') || (cov_24j29p8p12().b[12][1]++, discountType === 'max-percentage')) && (cov_24j29p8p12().b[12][2]++, minPurchase)) {
        cov_24j29p8p12().b[11][0]++;
        cov_24j29p8p12().s[21]++;
        return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "text-xs text-gray-500",
          children: ["ซื้อขั้นต่ำ ฿", new Intl.NumberFormat('th-TH').format(minPurchase), (cov_24j29p8p12().b[13][0]++, maxDiscount) && (cov_24j29p8p12().b[13][1]++, ` ลดสูงสุด ฿${new Intl.NumberFormat('th-TH').format(maxDiscount)}`)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 90,
          columnNumber: 9
        }, undefined);
      } else {
        cov_24j29p8p12().b[11][1]++;
      }
    }
    cov_24j29p8p12().s[22]++;
    return null;
  };
  const claimButtonText = (cov_24j29p8p12().s[23]++, claimed ? (cov_24j29p8p12().b[14][0]++, 'เก็บคูปองแล้ว') : (cov_24j29p8p12().b[14][1]++, 'เก็บคูปองเลย!'));
  const claimButtonClass = (cov_24j29p8p12().s[24]++, claimed ? (cov_24j29p8p12().b[15][0]++, 'bg-white text-gray-400 border border-gray-300 hover:bg-gray-50') : (cov_24j29p8p12().b[15][1]++, 'bg-blue-900 text-white hover:bg-blue-800'));
  cov_24j29p8p12().s[25]++;
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {
    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)("relative overflow-hidden border border-gray-200 ", "rounded-xl w-full md:w-[444px] min-h-[168px] bg-white z-0",
    // ,
    className),
    "data-testid": "card-coupon",
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
      className: "absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-12 h-12 bg-[#f5f5f5] rounded-full z-10 "
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
      lineNumber: 115,
      columnNumber: 3
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
      className: "absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-12 h-12 bg-[#f5f5f5] rounded-full z-10 "
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
      lineNumber: 118,
      columnNumber: 3
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {
      className: "p-4 md:p-6 flex flex-row h-full",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
        className: "w-1/3 flex items-center justify-start relative pr-6",
        children: [" ", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "relative h-[70px] w-[70px] md:h-[100px] md:w-[100px]",
          children: [" ", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__["default"], {
            src: brandLogoUrl,
            alt: brandName,
            fill: true,
            className: "object-contain",
            sizes: "(max-width: 768px) 70px, 100px",
            "data-testid": "coupon-brand-logo"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 124,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 123,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "absolute right-3 md:right-4 top-0 h-full",
          children: [" ", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: "h-full flex flex-col justify-around items-center",
            children: [...Array(6)].map((_, i) => {
              cov_24j29p8p12().f[6]++;
              cov_24j29p8p12().s[26]++;
              return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
                className: "w-[1px] h-[10px] md:h-[12px] border-r border-[#F0F0F0]"
              }, i, false, {
                fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
                lineNumber: 137,
                columnNumber: 17
              }, undefined);
            })
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 135,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 134,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
        lineNumber: 122,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
        className: "w-2/3 flex flex-col justify-between pl-2 md:pl-4",
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "space-y-2",
          "data-testid": "coupon-discount-info",
          children: [renderDiscountTitle(), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: "text-2xl md:text-3xl font-bold text-blue-900",
            children: renderDiscountValue()
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 149,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: "text-[11px] md:text-xs text-gray-500",
            children: renderPurchaseRequirement()
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 152,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 147,
          columnNumber: 11
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "flex flex-wrap gap-2 mt-4 w-full",
          "data-testid": "coupon-actions",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {
            variant: "outline",
            size: "sm",
            onClick: handleSeeDetails,
            className: "hidden md:inline-flex rounded-lg h-10 text-gray-600 border-gray-200 bg-[#F6F7F9] hover:bg-gray-50 hover:text-gray-700 text-sm",
            "data-testid": "see-details-button",
            children: "เงื่อนไข"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 159,
            columnNumber: 13
          }, undefined), (cov_24j29p8p12().b[16][0]++, !showTermsOnly) && (cov_24j29p8p12().b[16][1]++, /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {
            size: "sm",
            onClick: handleClaimCoupon,
            disabled: (cov_24j29p8p12().b[17][0]++, isClaimingCoupon) || (cov_24j29p8p12().b[17][1]++, claimed),
            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)("rounded-lg h-10 font-normal text-sm", claimButtonClass, (cov_24j29p8p12().b[18][0]++, isClaimingCoupon) && (cov_24j29p8p12().b[18][1]++, "opacity-70 cursor-wait")),
            "data-testid": "claim-coupon-button",
            children: claimButtonText
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 170,
            columnNumber: 15
          }, undefined))]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 158,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
        lineNumber: 146,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
      lineNumber: 120,
      columnNumber: 7
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
    lineNumber: 105,
    columnNumber: 5
  }, undefined);
};
cov_24j29p8p12().s[27]++;
_c = CardCoupon;
cov_24j29p8p12().s[28]++;
const CardCouponSkeleton = () => {
  cov_24j29p8p12().f[7]++;
  cov_24j29p8p12().s[29]++;
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {
    className: "relative overflow-hidden border border-gray-200  rounded-xl w-[444px] h-[168px] bg-white z-0",
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
      className: "absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-6 h-6 bg-white border border-gray-200 rounded-full z-10"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
      lineNumber: 193,
      columnNumber: 5
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
      className: "absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-6 h-6 bg-white border border-gray-200 rounded-full z-10"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
      lineNumber: 194,
      columnNumber: 5
    }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {
      className: "p-4 flex flex-col md:flex-row",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
        className: "w-full md:w-1/3 flex items-center justify-center md:justify-start mb-4 md:mb-0",
        children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {
          className: "h-12 w-32"
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 198,
          columnNumber: 9
        }, undefined)
      }, void 0, false, {
        fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
        lineNumber: 197,
        columnNumber: 7
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
        className: "w-full md:w-2/3 flex flex-col",
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "mb-2",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {
            className: "h-4 w-16 mb-1"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 203,
            columnNumber: 11
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {
            className: "h-8 w-32 mb-1"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 204,
            columnNumber: 11
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {
            className: "h-3 w-48"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 205,
            columnNumber: 11
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 202,
          columnNumber: 9
        }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "flex flex-wrap gap-2 mt-1",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {
            className: "h-10 w-24 rounded-lg"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 209,
            columnNumber: 11
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {
            className: "h-10 w-32 rounded-lg"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
            lineNumber: 210,
            columnNumber: 11
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
          lineNumber: 208,
          columnNumber: 9
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
        lineNumber: 201,
        columnNumber: 7
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
      lineNumber: 196,
      columnNumber: 5
    }, undefined)]
  }, void 0, true, {
    fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Card\\card-coupon.tsx",
    lineNumber: 192,
    columnNumber: 3
  }, undefined);
};
cov_24j29p8p12().s[30]++;
_c1 = CardCouponSkeleton;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CardCoupon);
cov_24j29p8p12().s[31]++;
CardCoupon.__docgenInfo = {
  "description": "",
  "methods": [],
  "displayName": "CardCoupon",
  "props": {
    "id": {
      "required": true,
      "tsType": {
        "name": "string"
      },
      "description": ""
    },
    "brandLogoUrl": {
      "required": true,
      "tsType": {
        "name": "string"
      },
      "description": ""
    },
    "brandName": {
      "required": true,
      "tsType": {
        "name": "string"
      },
      "description": ""
    },
    "discountType": {
      "required": true,
      "tsType": {
        "name": "union",
        "raw": "'amount' | 'percentage' | 'max-percentage'",
        "elements": [{
          "name": "literal",
          "value": "'amount'"
        }, {
          "name": "literal",
          "value": "'percentage'"
        }, {
          "name": "literal",
          "value": "'max-percentage'"
        }]
      },
      "description": ""
    },
    "discountValue": {
      "required": true,
      "tsType": {
        "name": "number"
      },
      "description": ""
    },
    "minPurchase": {
      "required": false,
      "tsType": {
        "name": "number"
      },
      "description": ""
    },
    "maxDiscount": {
      "required": false,
      "tsType": {
        "name": "number"
      },
      "description": ""
    },
    "claimed": {
      "required": false,
      "tsType": {
        "name": "boolean"
      },
      "description": "",
      "defaultValue": {
        "value": "false",
        "computed": false
      }
    },
    "className": {
      "required": false,
      "tsType": {
        "name": "string"
      },
      "description": ""
    },
    "onSeeDetails": {
      "required": false,
      "tsType": {
        "name": "signature",
        "type": "function",
        "raw": "(id: string) => void",
        "signature": {
          "arguments": [{
            "type": {
              "name": "string"
            },
            "name": "id"
          }],
          "return": {
            "name": "void"
          }
        }
      },
      "description": ""
    },
    "onClaimCoupon": {
      "required": false,
      "tsType": {
        "name": "signature",
        "type": "function",
        "raw": "(id: string) => void",
        "signature": {
          "arguments": [{
            "type": {
              "name": "string"
            },
            "name": "id"
          }],
          "return": {
            "name": "void"
          }
        }
      },
      "description": ""
    },
    "isClaimingCoupon": {
      "required": false,
      "tsType": {
        "name": "boolean"
      },
      "description": "",
      "defaultValue": {
        "value": "false",
        "computed": false
      }
    },
    "showTermsOnly": {
      "required": false,
      "tsType": {
        "name": "boolean"
      },
      "description": "",
      "defaultValue": {
        "value": "false",
        "computed": false
      }
    }
  }
};
cov_24j29p8p12().s[32]++;
CardCouponSkeleton.__docgenInfo = {
  "description": "",
  "methods": [],
  "displayName": "CardCouponSkeleton"
};
var _c, _c1;
cov_24j29p8p12().s[33]++;
__webpack_require__.$Refresh$.register(_c, "CardCoupon");
cov_24j29p8p12().s[34]++;
__webpack_require__.$Refresh$.register(_c1, "CardCouponSkeleton");

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache;
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Skeleton: () => (/* binding */ Skeleton)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ "./src/lib/utils.ts");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");

__webpack_require__.$Refresh$.runtime = /*#__PURE__*/ (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__, 2)));

function cov_1qmyd4zawk() {
  var path = "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\ui\\skeleton.tsx";
  var hash = "c4cad32bf667cc9ca055292e9810b3f4ab18186a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\ui\\skeleton.tsx",
    statementMap: {
      "0": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 11,
          column: 13
        }
      },
      "1": {
        start: {
          line: 13,
          column: 0
        },
        end: {
          line: 13,
          column: 14
        }
      },
      "2": {
        start: {
          line: 15,
          column: 0
        },
        end: {
          line: 19,
          column: 2
        }
      },
      "3": {
        start: {
          line: 21,
          column: 0
        },
        end: {
          line: 21,
          column: 29
        }
      }
    },
    fnMap: {
      "0": {
        name: "Skeleton",
        decl: {
          start: {
            line: 3,
            column: 9
          },
          end: {
            line: 3,
            column: 17
          }
        },
        loc: {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 3
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    f: {
      "0": 0
    },
    b: {},
    inputSourceMap: {
      version: 3,
      sources: ["C:/Users/<USER>/Desktop/fristJob/sangaroon-nakharin-ecommerce/frontend/home/<USER>/components/ui/skeleton.tsx"],
      sourcesContent: ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {\r\n  return <div className={cn('bg-primary/10 animate-pulse rounded-md', className)} {...props} />;\r\n}\r\n\r\nexport { Skeleton };\r\n"],
      names: [],
      mappings: ";AAAA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAuC,AAAtC,CAAC,AAAsC,CAAC,AAAtC,CAAC,AAAsC,CAArC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAC,QAAC,CAAC,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;AAChG,CAAC;KAFQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAIjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC"
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c4cad32bf667cc9ca055292e9810b3f4ab18186a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1qmyd4zawk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1qmyd4zawk();


function Skeleton({
  className,
  ...props
}) {
  cov_1qmyd4zawk().f[0]++;
  cov_1qmyd4zawk().s[0]++;
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-primary/10 animate-pulse rounded-md', className),
    ...props
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\ui\\skeleton.tsx",
    lineNumber: 4,
    columnNumber: 10
  }, this);
}
cov_1qmyd4zawk().s[1]++;
_c = Skeleton;

cov_1qmyd4zawk().s[2]++;
Skeleton.__docgenInfo = {
  "description": "",
  "methods": [],
  "displayName": "Skeleton"
};
var _c;
cov_1qmyd4zawk().s[3]++;
__webpack_require__.$Refresh$.register(_c, "Skeleton");

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=components-Card-__stories__-card-coupon-stories.iframe.bundle.js.map