import type { Meta, StoryObj } from '@storybook/react';
import { TimeConversionTooltip } from '../TimeConversionTooltip';
import {
  defaultTooltipData,
  tooltipDataVariants,
  emptyTooltipData,
  longTextTooltipData,
} from '../__fixtures__/TimeConversionTooltip.fixtures';

/**
 * TimeConversionTooltip displays localized date/time conversions in a hover tooltip
 */
const meta = {
  title: 'Components/TimeConversionTooltip',
  component: TimeConversionTooltip,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A component that shows time conversion information when hovering over a date or time. ' +
          'It displays both local and UTC time with a subtle dotted underline to indicate interactivity.',
      },
    },
  },
  argTypes: {
    data: {
      control: 'object',
      description: 'Date conversion data to display in tooltip',
    },
    enabled: {
      control: 'boolean',
      description: 'Whether the tooltip functionality is enabled',
    },
    openDelay: {
      control: { type: 'number', min: 0, max: 1000, step: 50 },
      description: 'Delay before showing tooltip (ms)',
    },
    closeDelay: {
      control: { type: 'number', min: 0, max: 1000, step: 50 },
      description: 'Delay before hiding tooltip (ms)',
    },
    zIndex: {
      control: { type: 'number', min: 0, max: 1000, step: 10 },
      description: 'Z-index for the tooltip',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
    children: {
      control: 'text',
      description: 'Content to display with the tooltip',
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof TimeConversionTooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default time conversion tooltip example
 */
export const Default: Story = {
  args: {
    data: defaultTooltipData,
    children: 'March 15, 2023',
  },
};

/**
 * Example with tooltip functionality disabled (just shows underline on hover)
 */
export const Disabled: Story = {
  args: {
    data: defaultTooltipData,
    children: 'March 15, 2023',
    enabled: false,
  },
};

/**
 * Example with custom delay settings for showing/hiding the tooltip
 */
export const CustomDelays: Story = {
  args: {
    data: defaultTooltipData,
    children: 'March 15, 2023',
    openDelay: 500,
    closeDelay: 300,
  },
};

/**
 * Example with custom z-index for proper layering
 */
export const CustomZIndex: Story = {
  args: {
    data: defaultTooltipData,
    children: 'March 15, 2023',
    zIndex: 100,
  },
};

/**
 * Example with empty data (fallback handling)
 */
export const EmptyData: Story = {
  args: {
    data: emptyTooltipData,
    children: 'No date available',
  },
};

/**
 * Example with very long text that tests overflow handling
 */
export const LongText: Story = {
  args: {
    data: longTextTooltipData,
    children:
      'This is a very long date and time string that might cause layout issues if not handled properly',
  },
};

/**
 * Multiple examples showing different timezone conversions
 */
export const MultipleExamples: Story = {
  args: {
    data: defaultTooltipData,
    children: 'This will be overridden by the render function',
  },
  render: () => (
    <div className="flex flex-col space-y-4">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">New York</h3>
        <TimeConversionTooltip data={tooltipDataVariants[0]}>
          January 10, 2023 - 11:45 AM
        </TimeConversionTooltip>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Tokyo</h3>
        <TimeConversionTooltip data={tooltipDataVariants[1]}>
          March 22, 2023 - 9:30 PM
        </TimeConversionTooltip>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">London</h3>
        <TimeConversionTooltip data={tooltipDataVariants[2]}>
          September 5, 2023 - 5:15 PM
        </TimeConversionTooltip>
      </div>

      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Sydney</h3>
        <TimeConversionTooltip data={tooltipDataVariants[3]}>
          December 31, 2023 - 7:00 AM
        </TimeConversionTooltip>
      </div>
    </div>
  ),
};
