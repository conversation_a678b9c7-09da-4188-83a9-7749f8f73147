// _tests_/inputpassword.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { InputPassword } from '../input-password';

describe('InputPassword Component', () => {
  // Test basic rendering
  test('renders password input element correctly', () => {
    render(<InputPassword placeholder="Test placeholder" />);
    
    const inputElement = screen.getByPlaceholderText('Test placeholder');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveAttribute('type', 'password');
  });
  
  // Test label rendering
  test('renders label when provided', () => {
    render(<InputPassword id="test-password" label="Test Label" />);
    
    const labelElement = screen.getByText('Test Label');
    expect(labelElement).toBeInTheDocument();
  });
  
  // Test helper text
  test('renders helper text when provided', () => {
    render(<InputPassword id="test-password" helperText="This is a helper text" />);
    
    const helperTextElement = screen.getByText('This is a helper text');
    expect(helperTextElement).toBeInTheDocument();
  });
  
  // Test error state
  test('renders error message when error is true', () => {
    render(
      <InputPassword 
        id="test-password" 
        error={true} 
        errorMessage="This is an error message" 
      />
    );
    
    const errorMessageElement = screen.getByText('This is an error message');
    expect(errorMessageElement).toBeInTheDocument();
  });
  
  // Test value change
  test('updates value on change', () => {
    render(<InputPassword id="test-password" />);
    
    const inputElement = screen.getByRole('textbox', { hidden: true });
    fireEvent.change(inputElement, { target: { value: 'test password' } });
    
    expect(inputElement).toHaveValue('test password');
  });
  
  // Test onValueChange callback
  test('calls onValueChange when value changes', () => {
    const handleValueChange = jest.fn();
    
    render(<InputPassword id="test-password" onValueChange={handleValueChange} />);
    
    const inputElement = screen.getByRole('textbox', { hidden: true });
    fireEvent.change(inputElement, { target: { value: 'test password' } });
    
    expect(handleValueChange).toHaveBeenCalledWith('test password');
  });
  
  // Test onChange callback
  test('calls onChange when value changes', () => {
    const handleChange = jest.fn();
    
    render(<InputPassword id="test-password" onChange={handleChange} />);
    
    const inputElement = screen.getByRole('textbox', { hidden: true });
    fireEvent.change(inputElement, { target: { value: 'test password' } });
    
    expect(handleChange).toHaveBeenCalled();
  });
  
  // Test disabled state
  test('disables input when disabled prop is true', () => {
    render(<InputPassword id="test-password" disabled />);
    
    const inputElement = screen.getByRole('textbox', { hidden: true });
    expect(inputElement).toBeDisabled();
  });
  
  // Test required attribute
  test('adds required attribute when required prop is true', () => {
    render(<InputPassword id="test-password" required />);
    
    const inputElement = screen.getByRole('textbox', { hidden: true });
    expect(inputElement).toBeRequired();
  });
  
  // Test aria attributes for accessibility
  test('sets correct aria attributes for accessibility', () => {
    render(
      <InputPassword 
        id="test-password" 
        error={true} 
        errorMessage="This is an error message" 
      />
    );
    
    const inputElement = screen.getByRole('textbox', { hidden: true });
    expect(inputElement).toHaveAttribute('aria-invalid', 'true');
    expect(inputElement).toHaveAttribute('aria-describedby', 'test-password-error');
  });
  
  // Test defaultValue prop
  test('displays defaultValue when provided', () => {
    render(<InputPassword id="test-password" defaultValue="default password" />);
    
    const inputElement = screen.getByRole('textbox', { hidden: true });
    expect(inputElement).toHaveValue('default password');
  });

  // Test password visibility toggle
  test('toggles password visibility when toggle button is clicked', () => {
    render(<InputPassword id="test-password" />);
    
    const inputElement = screen.getByRole('textbox', { hidden: true });
    expect(inputElement).toHaveAttribute('type', 'password');
    
    const toggleButton = screen.getByTestId('password-toggle');
    fireEvent.click(toggleButton);
    
    expect(inputElement).toHaveAttribute('type', 'text');
    
    fireEvent.click(toggleButton);
    
    expect(inputElement).toHaveAttribute('type', 'password');
  });
  
  // Test if toggle is hidden when showPasswordToggle is false
  test('does not render toggle button when showPasswordToggle is false', () => {
    render(<InputPassword id="test-password" showPasswordToggle={false} />);
    
    const toggleButton = screen.queryByTestId('password-toggle');
    expect(toggleButton).not.toBeInTheDocument();
  });
  
  // Test different sizes
  test('applies correct size classes', () => {
    const { rerender } = render(<InputPassword size="sm" placeholder="Small password" />);
    
    let inputElement = screen.getByPlaceholderText('Small password');
    expect(inputElement).toHaveClass('h-8');
    
    rerender(<InputPassword size="default" placeholder="Small password" />);
    inputElement = screen.getByPlaceholderText('Small password');
    expect(inputElement).toHaveClass('h-10');
    
    rerender(<InputPassword size="lg" placeholder="Small password" />);
    inputElement = screen.getByPlaceholderText('Small password');
    expect(inputElement).toHaveClass('h-12');
  });
});