import{u as y,_ as b}from"./chunk-X3LH6P65-BtKDvzuz.js";import{a as x,j as i,b as o,er as P,H as j,T as _,B as v,L as S,r as T,k,es as w,A,t as u}from"./index-Bwql5Dzz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as C}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as D}from"./chunk-C76H5USB-ByRPKhW7.js";import{u as E}from"./use-prompt-pbDx0Sfe.js";import{T as z}from"./trash-BBylvTAG.js";import{C as L}from"./container-Dqi2woPF.js";import{c as N}from"./index-BxZ1678G.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var q=({profile:e})=>{const{t}=o(),a=E(),{mutateAsync:r}=w(e.id),s=async()=>{await a({title:t("shippingProfile.delete.title"),description:t("shippingProfile.delete.description",{name:e.name}),verificationText:e.name,verificationInstruction:t("general.typeToConfirm"),confirmText:t("actions.delete"),cancelText:t("actions.cancel")})&&await r(void 0,{onSuccess:()=>{u.success(t("shippingProfile.delete.successToast",{name:e.name}))},onError:n=>{u.error(n.message)}})};return i.jsx(A,{groups:[{actions:[{icon:i.jsx(z,{}),label:t("actions.delete"),onClick:s}]}]})},p=N(),H=()=>{const{t:e}=o();return T.useMemo(()=>[p.accessor("name",{header:e("fields.name"),cell:t=>t.getValue()}),p.accessor("type",{header:e("fields.type"),cell:t=>t.getValue()}),p.display({id:"actions",cell:({row:t})=>i.jsx(q,{profile:t.original})})],[e])},I=()=>{const{t:e}=o();let t=[];t.push({key:"name",label:e("fields.name"),type:"string"}),t.push({key:"type",label:e("fields.type"),type:"string"});const a=[{label:e("fields.createdAt"),key:"created_at"},{label:e("fields.updatedAt"),key:"updated_at"}].map(r=>({key:r.key,label:r.label,type:"date"}));return t=[...t,...a],t},O=({pageSize:e=20,prefix:t})=>{const a=D(["offset","q","order","created_at","updated_at","name","type"],t);return{searchParams:{limit:e,offset:a.offset?parseInt(a.offset):0,q:a.q,order:a.order,created_at:a.created_at?JSON.parse(a.created_at):void 0,updated_at:a.updated_at?JSON.parse(a.updated_at):void 0,name:a.name,type:a.type},raw:a}},c=20,B=()=>{const{t:e}=o(),{raw:t,searchParams:a}=O({pageSize:c}),{shipping_profiles:r,count:s,isLoading:d,isError:n,error:f}=P(a,{placeholderData:k}),m=H(),g=I(),{table:h}=y({data:r,columns:m,count:s,enablePagination:!0,getRowId:l=>l.id,pageSize:c});if(n)throw f;return i.jsxs(L,{className:"divide-y p-0",children:[i.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[i.jsxs("div",{children:[i.jsx(j,{children:e("shippingProfile.domain")}),i.jsx(_,{className:"text-ui-fg-subtle",size:"small",children:e("shippingProfile.subtitle")})]}),i.jsx("div",{children:i.jsx(v,{size:"small",variant:"secondary",asChild:!0,children:i.jsx(S,{to:"create",children:e("actions.create")})})})]}),i.jsx(b,{table:h,pageSize:c,count:s,columns:m,filters:g,orderBy:[{key:"name",label:e("fields.name")},{key:"type",label:e("fields.type")},{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}],isLoading:d,navigateTo:l=>l.id,queryObject:t,search:!0,pagination:!0})]})},ge=()=>{const{getWidgets:e}=x();return i.jsx(C,{widgets:{before:e("shipping_profile.list.before"),after:e("shipping_profile.list.after")},children:i.jsx(B,{})})};export{ge as Component};
