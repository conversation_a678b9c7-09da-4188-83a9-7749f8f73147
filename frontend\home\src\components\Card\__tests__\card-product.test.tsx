// card-product.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { CardProduct } from '../card-product';
import { 
  wireProduct, 
  lightFixtureProduct, 
  deskLampProduct
} from '../__fixtures__/card-product.fixtures';

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    return <img {...props} />;
  },
}));

describe('CardProduct', () => {
  it('renders product information correctly', () => {
    render(<CardProduct {...wireProduct} />);
    
    // Check all main elements are rendered
    expect(screen.getByTestId('card-product')).toBeInTheDocument();
    expect(screen.getByTestId('product-brand')).toHaveTextContent('BCC');
    expect(screen.getByTestId('product-name')).toHaveTextContent('สายไฟ IEC(IV) 1x1 ตร.มม. ยาว 100เมตร สีเขียว');
    expect(screen.getByTestId('product-price')).toHaveTextContent('597');
    expect(screen.getByTestId('product-original-price')).toHaveTextContent('670');
    expect(screen.getByTestId('product-sold-count')).toHaveTextContent('ขายแล้ว 1,338 ชิ้น');
    
    // Check discount badge
    expect(screen.getByText('-10%')).toBeInTheDocument();
  });

  it('renders product without discount correctly', () => {
    render(<CardProduct {...deskLampProduct} />);
    
    // Should not have discount badge or original price
    expect(screen.queryByText('-10%')).not.toBeInTheDocument();
    expect(screen.queryByTestId('product-original-price')).not.toBeInTheDocument();
  });

  it('calls onAddToCart when add button is clicked', () => {
    const mockOnAddToCart = jest.fn();
    
    render(
      <CardProduct 
        {...wireProduct} 
        onAddToCart={mockOnAddToCart} 
      />
    );
    
    // Click the add to cart button
    const addButton = screen.getByTestId('add-to-cart-button');
    fireEvent.click(addButton);
    
    // Check if the mock function was called with correct ID
    expect(mockOnAddToCart).toHaveBeenCalledTimes(1);
    expect(mockOnAddToCart).toHaveBeenCalledWith(wireProduct.id);
  });

  it('disables add button when isAddingToCart is true', () => {
    const mockOnAddToCart = jest.fn();
    
    render(
      <CardProduct 
        {...wireProduct} 
        onAddToCart={mockOnAddToCart} 
        isAddingToCart={true} 
      />
    );
    
    // Button should be disabled
    const addButton = screen.getByTestId('add-to-cart-button');
    expect(addButton).toBeDisabled();
    
    // Click should not trigger onAddToCart
    fireEvent.click(addButton);
    expect(mockOnAddToCart).not.toHaveBeenCalled();
  });

  it('renders image with alt text for accessibility', () => {
    render(<CardProduct {...wireProduct} />);
    
    // Find image and check its alt text
    const image = screen.getByAltText(wireProduct.imageAlt as string);
    expect(image).toBeInTheDocument();
  });

  it('formats numbers correctly with locale string', () => {
    const props = {
      ...wireProduct,
      price: 1234567.89,
      originalPrice: 9876543.21,
      soldCount: 1000000,
    };
    
    render(<CardProduct {...props} />);
    
    // Check number formatting
    expect(screen.getByTestId('product-price')).toHaveTextContent('1,234,567.89');
    expect(screen.getByTestId('product-original-price')).toHaveTextContent('9,876,543.21');
    expect(screen.getByTestId('product-sold-count')).toHaveTextContent('ขายแล้ว 1,000,000 ชิ้น');
  });
});