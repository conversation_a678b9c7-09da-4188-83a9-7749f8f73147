import{u as P}from"./chunk-6FJ2PJHE-DxZ9pYOZ.js";import{u as T}from"./chunk-G3QXMPRB-BXa5-FQN.js";import{eU as c,j as r,q as g,R as j,d as v,a as S,dv as D,eV as _,s as w,b as l,H as m,A as C,e as q}from"./index-Bwql5Dzz.js";import{u as k,_ as A}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{u as E,a as N}from"./chunk-U6CSGYH6-BpcURsBT.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as B}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{P as Q}from"./pencil-square-6wRbnn1C.js";import{T as z}from"./trash-BBylvTAG.js";import{C as y}from"./container-Dqi2woPF.js";import"./use-prompt-pbDx0Sfe.js";import"./prompt-BsR9zKsn.js";import"./chunk-IQBAUTU5-D_4dFOf0.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./index-BxZ1678G.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";var je=e=>{const{id:t}=e.params||{},{product_type:a}=c(t,void 0,{initialData:e.data,enabled:!!t});return a?r.jsx("span",{children:a.value}):null},L=e=>({queryKey:_.detail(e),queryFn:async()=>w.admin.productType.retrieve(e)}),ve=async({params:e})=>{const t=e.id,a=L(t);return g.ensureQueryData(a)},M=({productType:e})=>{const{t}=l(),a=P(e.id);return r.jsxs(y,{className:"flex items-center justify-between",children:[r.jsx(m,{children:e.value}),r.jsx(C,{groups:[{actions:[{label:t("actions.edit"),icon:r.jsx(Q,{}),to:"edit"}]},{actions:[{label:t("actions.delete"),icon:r.jsx(z,{}),onClick:a}]}]})]})},u=10,O=({productType:e})=>{const{t}=l(),{searchParams:a,raw:i}=E({pageSize:u}),{products:o,count:n,isPending:s,isError:f,error:b}=q({...a,type_id:[e.id]}),h=N(["product_types"]),p=T(),{table:x}=k({columns:p,data:o,count:(o==null?void 0:o.length)||0,getRowId:d=>d.id,pageSize:u});if(f)throw b;return r.jsxs(y,{className:"divide-y p-0",children:[r.jsx("div",{className:"px-6 py-4",children:r.jsx(m,{level:"h2",children:t("products.domain")})}),r.jsx(A,{table:x,filters:h,isLoading:s,columns:p,count:n,pageSize:u,navigateTo:({original:d})=>`/products/${d.id}`,orderBy:[{key:"title",label:t("fields.title")},{key:"created_at",label:t("fields.createdAt")},{key:"updated_at",label:t("fields.updatedAt")}],queryObject:i,search:!0,pagination:!0})]})},Se=()=>{const{id:e}=j(),t=v(),{product_type:a,isPending:i,isError:o,error:n}=c(e,void 0,{initialData:t}),{getWidgets:s}=S();if(i||!a)return r.jsx(D,{sections:2,showJSON:!0,showMetadata:!0});if(o)throw n;return r.jsxs(B,{widgets:{after:s("product_type.details.after"),before:s("product_type.details.before")},showJSON:!0,showMetadata:!0,data:a,children:[r.jsx(M,{productType:a}),r.jsx(O,{productType:a})]})};export{je as Breadcrumb,Se as Component,ve as loader};
