import{P as a}from"./chunk-P3UUX2T6-CnJzifYv.js";import{j as r,cD as c,m as s}from"./index-Bwql5Dzz.js";var o=({text:e,align:t="left",maxWidth:l=220})=>{if(!e)return r.jsx(a,{});const n=e.toString().length;return r.jsx(c,{content:e,showTooltip:n>20,children:r.jsx("div",{className:s("flex h-full w-full items-center gap-x-3 overflow-hidden",{"justify-start text-start":t==="left","justify-center text-center":t==="center","justify-end text-end":t==="right"}),style:{maxWidth:l},children:r.jsx("span",{className:"truncate",children:e})})})},x=({text:e,align:t="left"})=>r.jsx("div",{className:s("flex h-full w-full items-center",{"justify-start text-start":t==="left","justify-center text-center":t==="center","justify-end text-end":t==="right"}),children:r.jsx("span",{className:"truncate",children:e})});export{x as T,o as a};
