import{r as n}from"./index-Bwql5Dzz.js";var c=Object.defineProperty,l=Object.getOwnPropertySymbols,f=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,o=(e,t,r)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,s=(e,t)=>{for(var r in t)f.call(t,r)&&o(e,r,t[r]);if(l)for(var r of l(t))i.call(t,r)&&o(e,r,t[r]);return e},h=(e,t)=>{var r={};for(var a in e)f.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&l)for(var a of l(e))t.indexOf(a)<0&&i.call(e,a)&&(r[a]=e[a]);return r};const m=n.forwardRef((e,t)=>{var r=e,{color:a="currentColor"}=r,p=h(r,["color"]);return n.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),n.createElement("g",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},n.createElement("path",{d:"M7.722 7.5h-3.11M10.389 3.722H9.5c-.982 0-1.778.796-1.778 1.778v4c0 .982.796 1.778 1.778 1.778h.889"}),n.createElement("path",{d:"M10.389 3.722a1.778 1.778 0 1 0 3.555 0 1.778 1.778 0 0 0-3.555 0M10.389 11.278a1.778 1.778 0 1 0 3.556 0 1.778 1.778 0 0 0-3.556 0M1.056 7.5a1.778 1.778 0 1 0 3.555 0 1.778 1.778 0 0 0-3.555 0"})),n.createElement("defs",null,n.createElement("clipPath",{id:"a"},n.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});m.displayName="Channels";export{m as C};
