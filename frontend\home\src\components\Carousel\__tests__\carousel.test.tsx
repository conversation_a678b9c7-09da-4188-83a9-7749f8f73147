import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Carousel from '../carousel';
import { sampleSlides, renderFunctions } from '../__fixtures__/carousel.fixtures';

/**
 * Mock Swiper and related dependencies
 */
jest.mock('swiper/react', () => ({
  Swiper: ({ children, onSwiper, onSlideChange }: any) => {
    const swiperRef = { slideTo: jest.fn() };
    React.useEffect(() => {
      if (onSwiper) onSwiper(swiperRef);
    }, [onSwiper]);
    return (
      <div data-testid="swiper-container">
        {children}
        <button 
          onClick={() => onSlideChange?.({ activeIndex: 1 })}
          data-testid="mock-slide-change"
        >
          Change Slide
        </button>
      </div>
    );
  },
  SwiperSlide: ({ children }: any) => <div data-testid="swiper-slide">{children}</div>,
}));

jest.mock('swiper/modules', () => ({
  Navigation: jest.fn(),
  Pagination: jest.fn(),
  Autoplay: jest.fn(),
  EffectCoverflow: jest.fn(),
}));

jest.mock('swiper/css', () => ({}));
jest.mock('swiper/css/navigation', () => ({}));
jest.mock('swiper/css/pagination', () => ({}));
jest.mock('swiper/css/effect-coverflow', () => ({}));

describe('Carousel Component', () => {
  it('renders the carousel with slides', () => {
    render(
      <Carousel
        items={sampleSlides}
        renderItem={renderFunctions.basic}
      />
    );
    
    const slides = screen.getAllByTestId('swiper-slide');
    expect(slides).toHaveLength(sampleSlides.length);
  });
  
  it('applies the correct variant class', () => {
    const { container, rerender } = render(
      <Carousel
        variant="default"
        items={sampleSlides}
        renderItem={renderFunctions.basic}
      />
    );
    
    expect(container.querySelector('.carousel-default')).toBeInTheDocument();
    
    rerender(
      <Carousel
        variant="multiple"
        items={sampleSlides}
        renderItem={renderFunctions.basic}
      />
    );
    
    expect(container.querySelector('.carousel-multiple')).toBeInTheDocument();
    
    rerender(
      <Carousel
        variant="coverflow"
        items={sampleSlides}
        renderItem={renderFunctions.basic}
      />
    );
    
    expect(container.querySelector('.carousel-coverflow')).toBeInTheDocument();
  });
  
  it('calls onSlideChange when slide changes', async () => {
    const handleSlideChange = jest.fn();
    
    render(
      <Carousel
        items={sampleSlides}
        renderItem={renderFunctions.basic}
        onSlideChange={handleSlideChange}
      />
    );
    
    fireEvent.click(screen.getByTestId('mock-slide-change'));
    
    await waitFor(() => {
      expect(handleSlideChange).toHaveBeenCalledWith(expect.objectContaining({
        activeIndex: 1
      }));
    });
  });
  
  it('applies custom class name', () => {
    const { container } = render(
      <Carousel
        items={sampleSlides}
        renderItem={renderFunctions.basic}
        className="custom-carousel-class"
      />
    );
    
    expect(container.querySelector('.custom-carousel-class')).toBeInTheDocument();
  });
  
  it('renders with autoplay enabled', () => {
    render(
      <Carousel
        items={sampleSlides}
        renderItem={renderFunctions.basic}
        autoplay={true}
        autoplayDelay={2000}
      />
    );
    
    // This is a superficial test since we mocked Swiper
    // We could enhance it by checking if the autoplay config was passed correctly
    const container = screen.getByTestId('swiper-container');
    expect(container).toBeInTheDocument();
  });
});