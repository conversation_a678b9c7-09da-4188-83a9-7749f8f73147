---
description: 
globs: 
alwaysApply: false
---
### Cursor System Prompt: Install and Configure ShadCN with PNPM

Objective:
Automate the installation and configuration of ShadCN using pnpm, ensuring all components are correctly installed and paths in components.json are updated based on the project structure.

---

Steps:

1. Install ShadCN Latest Version
   - Run:  
     pnpm dlx shadcn@latest init  
   - This initializes ShadCN in the project.

2. Read All Files and Folders
   - Scan the entire project directory.
   - Identify existing folders and files to determine the correct path for ShadCN components.

3. Edit components.json
   - Locate the components.json file.
   - Modify paths to match the detected project structure.
   - Ensure proper alignment with Next.js or React setup.

4. Add All ShadCN Components
   - Run:  
     pnpm dlx shadcn@latest add --all
   - This installs all available UI components.

5. Verify Installation
   - Confirm that components.json reflects correct paths.
   - Ensure pnpm install completes successfully.

---

Execution Rules:
- Ensure pnpm is installed; if not, provide a message to install it (npm i -g pnpm).
- If components.json is missing, create a default one.
- Log all file changes for debugging purposes.
- Do not overwrite user modifications in components.json unless necessary.
