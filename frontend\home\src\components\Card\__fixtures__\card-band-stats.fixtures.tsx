// components/Card/CardBandStats/_fixtures/card-band-stats.fixtures.tsx
import React from 'react';
import { Package, BarChart2, Award } from 'lucide-react';
import type { CardBandStatsProps } from '../card-band-stats';

/**
 * Default fixtures for the CardBandStats component
 * These can be used in both tests and stories
 */

// Base fixture props
export const baseFixture: CardBandStatsProps = {
  value: '40,000+',
  description: 'สินค้าในคลัง',
  colorScheme: 'white',
  icon: <Package className="text-orange-400 w-10 h-10" />,
};

// Hero variant fixtures
export const heroFixture: CardBandStatsProps = {
  value: '40,000+',
  description: 'สินค้าในคลัง',
  colorScheme: 'white',
  variant: 'hero',
  icon: <Package className="text-orange-400 w-16 h-16" />,
};

export const heroOrangeFixture: CardBandStatsProps = {
  value: '100%',
  description: 'ความพึงพอใจลูกค้า',
  colorScheme: 'orange',
  variant: 'hero',
  icon: <BarChart2 className="text-blue-900 w-16 h-16" />,
};

export const heroBlueFixture: CardBandStatsProps = {
  value: '20+',
  description: 'ประสบการณ์',
  colorScheme: 'blue',
  variant: 'hero',
  icon: <Award className="text-orange-400 w-16 h-16" />,
};

// Different color scheme fixtures
export const orangeFixture: CardBandStatsProps = {
  value: '100%',
  description: 'ความพึงพอใจลูกค้า',
  colorScheme: 'orange',
  icon: <BarChart2 className="text-blue-900 w-10 h-10" />,
};

export const blueFixture: CardBandStatsProps = {
  value: '20+',
  description: 'ประสบการณ์',
  colorScheme: 'blue',
  icon: <Award className="text-orange-400 w-10 h-10" />,
};

// Compact variant fixtures
export const compactFixture: CardBandStatsProps = {
  ...baseFixture,
  variant: 'compact',
  icon: <Package className="text-orange-400 w-8 h-8" />,
};

export const compactOrangeFixture: CardBandStatsProps = {
  ...orangeFixture,
  variant: 'compact',
  icon: <BarChart2 className="text-blue-900 w-8 h-8" />,
};

export const compactBlueFixture: CardBandStatsProps = {
  ...blueFixture,
  variant: 'compact',
  icon: <Award className="text-orange-400 w-8 h-8" />,
};

// Interactive fixture
export const interactiveFixture: CardBandStatsProps = {
  ...baseFixture,
  onClick: () => alert('Card clicked'),
};

// Complete fixture set for grid display
export const defaultGridFixtures: CardBandStatsProps[] = [
  baseFixture,
  orangeFixture,
  blueFixture
];

export const compactGridFixtures: CardBandStatsProps[] = [
  compactFixture,
  compactOrangeFixture,
  compactBlueFixture
];

export const heroGridFixtures: CardBandStatsProps[] = [
  heroFixture,
  heroOrangeFixture,
  heroBlueFixture
];