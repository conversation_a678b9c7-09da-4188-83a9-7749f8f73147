# Modal Components

A collection of reusable and customizable modal components for Next.js applications using shadcn/ui as base components.

## Table of Contents

- [Installation](#installation)
- [Components](#components)
  - [Modal](#modal)
  - [ModalContentCoupon](#modalcontentcoupon)
- [Usage Examples](#usage-examples)
  - [Basic Modal](#basic-modal)
  - [Coupon Modal](#coupon-modal)
  - [Advanced Usage](#advanced-usage)
- [Props](#props)
  - [Modal Props](#modal-props)
  - [ModalContentCoupon Props](#modalcontentcoupon-props)
- [Testing](#testing)
- [Storybook](#storybook)

## Installation

These components are designed to work with a Next.js project that uses shadcn/ui components. Ensure you have the following dependencies installed:

```bash
# Install shadcn/ui components
npx shadcn-ui@latest add dialog
npx shadcn-ui@latest add button

# Install additional dependencies
npm install class-variance-authority lucide-react
```

## Components

### Modal

A reusable and accessible modal component that serves as a foundation for various modal content types. It provides different sizes, positions, and animation options.

### ModalContentCoupon

A specialized component for displaying coupon information within a modal. It shows coupon details such as title, discount amount, expiry date, and usage restrictions.

## Usage Examples

### Basic Modal

```tsx
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/Modal';

export default function BasicModalExample() {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>Open Modal</Button>
      
      <Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title="Example Modal"
        description="This is a description of the modal content"
        size="default"
        position="default"
        footer={
          <>
            <Button variant="outline" onClick={() => setIsOpen(false)}>Cancel</Button>
            <Button onClick={() => setIsOpen(false)}>Confirm</Button>
          </>
        }
      >
        <p>This is the content of the modal. You can put any React components here.</p>
      </Modal>
    </div>
  );
}
```

### Coupon Modal

```tsx
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/Modal';
import { ModalContentCoupon, type CouponDetails } from '@/components/Modal/modal-content-coupon';

// Define your coupon data
const myCoupon: CouponDetails = {
  title: 'คูปองส่วนลด',
  amount: 2500,
  currency: 'บาท',
  minimumPurchase: 30000,
  expiryDate: '31/03/2025',
  applicableProducts: 'สินค้าที่ร่วมรายการ',
  restrictions: [
    'สามารถใช้ได้กับสินค้าที่ร่วมรายการ',
    'ไม่สามารถใช้ได้คู่ส่วนลดนี้ซ้ำได้',
    'บริษัทสามารถเปลี่ยนแปลงเงื่อนไขโดยที่ไม่ต้องแจ้งให้ทราบล่วงหน้า'
  ],
  canBeUsedTogether: false,
  companyLogo: '/philips-logo.png',
  additionalInfo: 'ท่านสามารถใช้คูปองส่วนลดนี้ได้ผ่านหน้าข้อมูลการชำระเงิน'
};

export default function CouponModalExample() {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>ดูคูปองส่วนลด</Button>
      
      <Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        size="default"
        position="center"
        showCloseButton={false}
      >
        <ModalContentCoupon
          coupon={myCoupon}
          onClose={() => setIsOpen(false)}
        />
      </Modal>
    </div>
  );
}
```

### Advanced Usage

```tsx
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/Modal';
import { ModalContentCoupon, type CouponDetails } from '@/components/Modal/modal-content-coupon';

// Multiple coupons for a carousel
const coupons: CouponDetails[] = [
  {
    title: 'คูปองส่วนลด',
    amount: 2500,
    currency: 'บาท',
    minimumPurchase: 30000,
    expiryDate: '31/03/2025',
    restrictions: ['สามารถใช้ได้กับสินค้าที่ร่วมรายการ'],
    companyLogo: '/philips-logo.png',
  },
  {
    title: 'ส่วนลด',
    amount: 15,
    currency: '%',
    minimumPurchase: 2000,
    expiryDate: '15/06/2025',
    restrictions: ['ยกเว้นสินค้าลดราคาพิเศษ'],
    additionalInfo: 'ส่วนลดสูงสุด 500 บาท',
  }
];

export default function AdvancedCouponExample() {
  const [isOpen, setIsOpen] = useState(false);
  const [currentCouponIndex, setCurrentCouponIndex] = useState(0);
  
  const nextCoupon = () => {
    setCurrentCouponIndex((prev) => (prev + 1) % coupons.length);
  };
  
  const prevCoupon = () => {
    setCurrentCouponIndex((prev) => (prev - 1 + coupons.length) % coupons.length);
  };
  
  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>View Coupons ({coupons.length})</Button>
      
      <Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        size="default"
        position="center"
        showCloseButton={false}
        footer={
          <div className="flex justify-between w-full">
            <Button variant="outline" onClick={prevCoupon} disabled={coupons.length <= 1}>
              Previous
            </Button>
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Close
            </Button>
            <Button variant="outline" onClick={nextCoupon} disabled={coupons.length <= 1}>
              Next
            </Button>
          </div>
        }
      >
        <ModalContentCoupon
          coupon={coupons[currentCouponIndex]}
          onClose={() => setIsOpen(false)}
        />
      </Modal>
    </div>
  );
}
```

## Props

### Modal Props

| Prop             | Type                  | Default     | Description                                      |
|------------------|------------------------|-------------|--------------------------------------------------|
| `children`       | ReactNode             | -           | Content to display inside the modal              |
| `title`          | string                | -           | Title text for the modal header                  |
| `description`    | string                | -           | Description text below the title                 |
| `isOpen`         | boolean               | -           | Controls whether the modal is open               |
| `onClose`        | () => void            | -           | Function called when the modal is closed         |
| `size`           | 'default' \| 'sm' \| 'lg' \| 'xl' \| 'full' | 'default' | Controls the width of the modal |
| `position`       | 'default' \| 'top' \| 'center' | 'default' | Controls the position and animation of the modal |
| `className`      | string                | -           | Additional CSS classes to apply                  |
| `showCloseButton`| boolean               | true        | Whether to show the close button in the corner   |
| `footer`         | ReactNode             | -           | Content to display in the modal footer           |

### ModalContentCoupon Props

| Prop        | Type            | Default | Description                                   |
|-------------|-----------------|---------|-----------------------------------------------|
| `coupon`    | CouponDetails   | -       | Object containing all coupon details          |
| `onClose`   | () => void      | -       | Function called when the close button is clicked |
| `className` | string          | -       | Additional CSS classes to apply               |

#### CouponDetails Interface

```typescript
interface CouponDetails {
  title: string;
  amount: string | number;
  currency?: string;
  minimumPurchase?: string | number;
  expiryDate?: string;
  applicableProducts?: string;
  restrictions?: string[];
  canBeUsedTogether?: boolean;
  companyLogo?: string;
  additionalInfo?: string;
}
```

## Testing

Both components include comprehensive test suites using React Testing Library. Run the tests with:

```bash
npm test
```

The tests cover:
- Rendering with different props
- User interactions
- Accessibility features
- Various component states

## Storybook

Both components have Storybook stories that showcase their capabilities and variations. Run Storybook with:

```bash
npm run storybook
```

The stories include:
- Different sizes and positions for Modal
- Various coupon configurations for ModalContentCoupon
- Interactive examples that demonstrate state management

---

## Folder Structure

```
/components
  /Modal
    /_fixtures_
      modal.fixtures.ts
    /_tests_
      modal.test.tsx
      modal-content-coupon.test.tsx
    index.ts
    modal.tsx
    modal.stories.tsx
    modal-content-coupon.tsx
    modal-content-coupon.stories.tsx
```