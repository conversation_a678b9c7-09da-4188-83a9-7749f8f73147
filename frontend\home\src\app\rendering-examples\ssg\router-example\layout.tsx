import { ExampleNavigation } from '@/components/rendering-examples/ExampleNavigation';
import { ThemeToggle } from '@/components/theme-toggle';
import { I18nButton } from '@/components/I18nButton';
import Link from 'next/link';
import { createTranslation } from '@/components/Providers/server-i18n';
import { Fragment } from 'react';
import { Suspense } from 'react';

// Define the routes for this example
const routes = [
  { path: 'home', labelKey: 'rendering.routes.home', defaultLabel: 'Home' },
  { path: 'about', labelKey: 'rendering.routes.about', defaultLabel: 'About' },
  { path: 'products', labelKey: 'rendering.routes.products', defaultLabel: 'Products' },
  { path: 'contact', labelKey: 'rendering.routes.contact', defaultLabel: 'Contact' },
];

export default async function SSGRouterLayout({ children }: { children: React.ReactNode }) {
  // For server components, we use server-side translation utility
  const { t } = await createTranslation('common');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <Link href="/rendering-examples/ssg" className="text-primary font-semibold hover:underline">
          ← {t('rendering.backToSSG', 'Back to SSG')}
        </Link>

        <div className="flex items-center gap-2">
          <ThemeToggle />
          <I18nButton />
        </div>
      </div>

      <h1 className="mb-6 text-3xl font-bold">
        {t('rendering.ssgRouterTitle', 'SSG with Multiple Routes')}
      </h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">
          {t('rendering.ssgRouterHowWorks', 'How SSG Routing Works')}
        </h2>
        <p className="mb-4">
          {t(
            'rendering.ssgRouterDescription',
            'With SSG, all routes are pre-rendered at build time. This navigation demonstrates how multiple static pages can be generated and linked together.',
          )}
        </p>
        <p>
          {t(
            'rendering.ssgRouterNote',
            'Notice how navigation between routes is instant, as all pages were already generated during the build process.',
          )}
        </p>
      </div>

      <ExampleNavigation basePath="/rendering-examples/ssg/router-example" routes={routes} />

      {children}
    </div>
  );
}
