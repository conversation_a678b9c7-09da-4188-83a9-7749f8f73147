/**
 * Internationalization settings and translations for the UserTable component
 */

// Import the supported language types from our i18n system
import { SupportedLanguage } from '@/lib';

// Define the translations object structure
export interface UserTableTranslations {
  userTable: {
    headers: {
      name: string;
      email: string;
      role: string;
      status: string;
      lastLogin: string;
    };
    status: {
      active: string;
      inactive: string;
      pending: string;
    };
    empty: {
      title: string;
      description: string;
    };
    error: {
      title: string;
      retry: string;
    };
    footer: {
      total: string;
    };
  }
}

// Define translations for all supported languages
export const translations: Record<SupportedLanguage, UserTableTranslations> = {
  en: {
    userTable: {
      headers: {
        name: 'Name',
        email: 'Email',
        role: 'Role',
        status: 'Status',
        lastLogin: 'Last Login'
      },
      status: {
        active: 'Active',
        inactive: 'Inactive',
        pending: 'Pending'
      },
      empty: {
        title: 'No Users Found',
        description: 'There are no users to display.'
      },
      error: {
        title: 'Error',
        retry: 'Retry'
      },
      footer: {
        total: 'Total: {{count}} users'
      }
    }
  },
  fr: {
    userTable: {
      headers: {
        name: 'Nom',
        email: 'E-mail',
        role: 'Rô<PERSON>',
        status: 'Statut',
        lastLogin: 'Der<PERSON><PERSON> Connexion'
      },
      status: {
        active: 'Actif',
        inactive: 'Inactif',
        pending: 'En attente'
      },
      empty: {
        title: 'Aucun Utilisateur Trouvé',
        description: 'Il n\'y a aucun utilisateur à afficher.'
      },
      error: {
        title: 'Erreur',
        retry: 'Réessayer'
      },
      footer: {
        total: 'Total: {{count}} utilisateurs'
      }
    }
  },
  ja: {
    userTable: {
      headers: {
        name: '名前',
        email: 'メール',
        role: '役割',
        status: '状態',
        lastLogin: '最終ログイン'
      },
      status: {
        active: 'アクティブ',
        inactive: '非アクティブ',
        pending: '保留中'
      },
      empty: {
        title: 'ユーザーが見つかりません',
        description: '表示するユーザーはありません。'
      },
      error: {
        title: 'エラー',
        retry: '再試行'
      },
      footer: {
        total: '合計：{{count}}人のユーザー'
      }
    }
  },
  ar: {
    userTable: {
      headers: {
        name: 'الاسم',
        email: 'البريد الإلكتروني',
        role: 'الدور',
        status: 'الحالة',
        lastLogin: 'آخر تسجيل دخول'
      },
      status: {
        active: 'نشط',
        inactive: 'غير نشط',
        pending: 'قيد الانتظار'
      },
      empty: {
        title: 'لم يتم العثور على مستخدمين',
        description: 'لا يوجد مستخدمين للعرض.'
      },
      error: {
        title: 'خطأ',
        retry: 'إعادة المحاولة'
      },
      footer: {
        total: 'الإجمالي: {{count}} مستخدم'
      }
    }
  }
};

// Internationalization configurations similar to other components
export const i18nConfigs = {
  english: {
    i18nNamespace: undefined, // Default namespace
    i18nPrefix: 'userTable',
  },
  french: {
    i18nNamespace: undefined,
    i18nPrefix: 'userTable',
  },
  japanese: {
    i18nNamespace: undefined,
    i18nPrefix: 'userTable',
  },
  arabic: {
    i18nNamespace: undefined,
    i18nPrefix: 'userTable',
  }
};

/**
 * Get a translation for the UserTable component
 * @param key Translation key to retrieve
 * @param language Target language (defaults to current language)
 * @returns The translated string or the key itself if translation is not found
 */
export const getTranslation = (
  key: string, 
  language: SupportedLanguage = 'en'
): string => {
  const parts = key.split('.');
  let result: any = translations[language];
  
  for (const part of parts) {
    if (!result || typeof result !== 'object') {
      return key; // Key not found, return the key itself
    }
    result = result[part];
  }
  
  return typeof result === 'string' ? result : key;
}; 