'use client';

import { useEffect, useState } from 'react';
import { RouteContent } from '@/components/rendering-examples/RouteContent';

export default function ContactPage() {
  const [timestamp, setTimestamp] = useState<string>('');

  useEffect(() => {
    // In CSR, this code only runs in the browser
    setTimestamp(new Date().toISOString());
  }, []);

  return <RouteContent route="contact" timestamp={timestamp} renderType="csr" />;
}
