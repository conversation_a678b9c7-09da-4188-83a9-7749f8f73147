import{a as x}from"./chunk-W6676YBU-BEKokOil.js";import{q as p,a as y,j as t,fe as h,s as g,b as u,d1 as j,H as b,T as v,B as T,L as w,r as D,k as C,A as S,ff as L,t as d}from"./index-Bwql5Dzz.js";import{u as P,_ as q}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{b as E}from"./chunk-EMNHBSFU-BcNoY5bk.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as A}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{P as _}from"./pencil-square-6wRbnn1C.js";import{T as k}from"./trash-BBylvTAG.js";import{u as z}from"./use-prompt-pbDx0Sfe.js";import{C as H}from"./container-Dqi2woPF.js";import{c as Q}from"./index-BxZ1678G.js";import"./chunk-WYX5PIA3-DoOUp1ge.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var N=e=>({queryKey:h.list(e),queryFn:async()=>g.admin.returnReason.list(e)}),De=async()=>{const e=N();return p.getQueryData(e.queryKey)??await p.fetchQuery(e)},K=({id:e,label:r})=>{const{t:s}=u(),o=z(),{mutateAsync:a}=L(e);return async()=>{await o({title:s("general.areYouSure"),description:s("returnReasons.delete.confirmation",{label:r}),confirmText:s("actions.delete"),cancelText:s("actions.cancel")})&&await a(void 0,{onSuccess:()=>{d.success(s("returnReasons.delete.successToast",{label:r}))},onError:n=>{d.error(n.message)}})}},i=20,M=()=>{const{t:e}=u(),{searchParams:r,raw:s}=E({pageSize:i}),{return_reasons:o,count:a,isPending:c,isError:l,error:n}=j(r,{placeholderData:C}),m=I(),{table:f}=P({data:o,columns:m,count:a,getRowId:R=>R.id,pageSize:i});if(l)throw n;return t.jsxs(H,{className:"divide-y px-0 py-0",children:[t.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[t.jsxs("div",{children:[t.jsx(b,{children:e("returnReasons.domain")}),t.jsx(v,{className:"text-ui-fg-subtle",size:"small",children:e("returnReasons.subtitle")})]}),t.jsx(T,{variant:"secondary",size:"small",asChild:!0,children:t.jsx(w,{to:"create",children:e("actions.create")})})]}),t.jsx(q,{table:f,queryObject:s,count:a,isLoading:c,columns:m,pageSize:i,noHeader:!0,pagination:!0,search:!0})]})},O=({returnReason:e})=>{const{t:r}=u(),s=K(e);return t.jsx(S,{groups:[{actions:[{icon:t.jsx(_,{}),label:r("actions.edit"),to:`${e.id}/edit`}]},{actions:[{icon:t.jsx(k,{}),label:r("actions.delete"),onClick:s}]}]})},B=Q(),I=()=>{const e=x();return D.useMemo(()=>[...e,B.display({id:"actions",cell:({row:r})=>t.jsx(O,{returnReason:r.original})})],[e])},Ce=()=>{const{getWidgets:e}=y();return t.jsx(A,{showMetadata:!1,showJSON:!1,hasOutlet:!0,widgets:{after:e("return_reason.list.after"),before:e("return_reason.list.before")},children:t.jsx(M,{})})};export{Ce as Component,De as loader};
