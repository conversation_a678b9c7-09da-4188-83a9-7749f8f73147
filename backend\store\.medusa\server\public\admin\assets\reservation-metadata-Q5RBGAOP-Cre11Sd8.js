import{M as n}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as d,dW as p,dY as u,j as r}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import{b as R}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var v=()=>{const{id:a}=d(),{reservation:t,isPending:o,isError:e,error:s}=p(a),{mutateAsync:i,isPending:m}=u(a);if(e)throw s;return r.jsx(R,{children:r.jsx(n,{isPending:o,isMutating:m,hook:i,metadata:t==null?void 0:t.metadata})})};export{v as Component};
