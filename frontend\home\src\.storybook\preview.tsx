import React from 'react';
import { initialize, mswLoader } from 'msw-storybook-addon';
import type { Preview } from '@storybook/react';
import '../src/styles/globals.css';
import { withThemeByClassName } from '@storybook/addon-themes';
import i18n from './i18next';
import { languageCodes, isRTL } from '../src/lib';
import { handlers } from './mswHandlers';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
    },
  },
});

// Language display names
const languageNames = {
  en: 'English',
  fr: 'Français',
  ja: '日本語',
  ar: 'العربية',
};

// Create toolbar items from the language codes
const localeItems = languageCodes.map(code => ({
  value: code,
  title: `${languageNames[code] || code} ${isRTL(code) ? '(RTL)' : ''}`,
}));


// Initialize MSW
initialize({
  onUnhandledRequest: 'bypass',
  serviceWorker: {
    url: './mockServiceWorker.js',
  },
});

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    i18n, // Provide i18n instance to the addon
    backgrounds: {
      disable: true,
    },
    layout: 'fullscreen',
    docs: {
      story: {
        inline: true,
      },
    },
    msw: {
      handlers: handlers,
    },
  },
  loaders: [mswLoader],
  // Define toolbar items
  globalTypes: {
    locale: {
      name: 'Locale',
      description: 'Internationalization locale',
      defaultValue: 'en',
      toolbar: {
        icon: 'globe',
        items: localeItems,
        showName: true,
      },
    },
  },
  
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <Story />
      </QueryClientProvider>
    ),
    withThemeByClassName({
      themes: {
        light: 'light',
        dark: 'dark',
      },
      defaultTheme: 'dark',
    }),
  ],
};

export default preview; 