{"version": 3, "file": "components-Card-__stories__-card-coupon-stories.iframe.bundle.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChpBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAjDA;AACA;AACA;AACA;AAIA;AACA;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAGA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;;;;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAAA;;;;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;;;;;;AAKA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAGA;AAAA;;;;;AAGA;AAAA;;;;;AAEA;AAAA;AAEA;AAAA;AAAA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAIA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AADA;;;;;;;;;;;;;;;;;;;;AAQA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;AAEA;AAAA;AACA;;;;;;;;;;AAKA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAIA;AAEA;AACA;AACA;AACA;AAKA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;AAAA;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;AAEA;AAAA;AACA;AAAA;AACA;AAAA;;;;;;;;;;AAGA;AAAA;AACA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;AAGA;AAAA;AACA;AAAA;;;;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AAxBA;AA0BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxNA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;;;;AACA;AAAA;AAFA;AAIA;AAAA", "sources": ["webpack://shadcn-timeline/./src/components/Card/__stories__/card-coupon.stories.tsx", "webpack://shadcn-timeline/./src/components/Card/card-coupon.tsx", "webpack://shadcn-timeline/./src/components/ui/skeleton.tsx"], "sourcesContent": ["\n    /* eslint-disable */\n    // @ts-nocheck\n    // @ts-expect-error (Converted from ts-ignore)\n    var __STORY__ = \"// card-coupon.stories.tsx\\r\\nimport type { Meta, StoryObj } from '@storybook/react';\\r\\nimport { action } from '@storybook/addon-actions';\\r\\nimport { CardCoupon, CardCouponSkeleton } from '../card-coupon';\\r\\n\\r\\n// Sample brand logos - replace with actual URLs in production\\r\\nconst brandsConfig = {\\r\\n  philips: {\\r\\n    logoUrl: '/images/philips-logo.png',\\r\\n    name: '<PERSON>'\\r\\n  },\\r\\n  lamptan: {\\r\\n    logoUrl: '/images/LAMPTAN.png',\\r\\n    name: 'LAMPTAN'\\r\\n  },\\r\\n  bcc: {\\r\\n    logoUrl: '/images/BCC.png',\\r\\n    name: 'Bangkok Cable'\\r\\n  },\\r\\n  songkhla: {\\r\\n    logoUrl: '/images/Sengaroon.png',\\r\\n    name: '<PERSON><PERSON><PERSON> Natrin'\\r\\n  },\\r\\n  panasonic: {\\r\\n    logoUrl: '/images/Panasonic.png',\\r\\n    name: 'Panasonic'\\r\\n  },\\r\\n  haco: {\\r\\n    logoUrl: '/images/HACO.png',\\r\\n    name: 'HACO'\\r\\n  }\\r\\n};\\r\\n\\r\\n// Define sample coupons based on the images provided\\r\\nconst fixedAmountCoupon = {\\r\\n  id: 'fixed-amount-1',\\r\\n  brandLogoUrl: brandsConfig.philips.logoUrl,\\r\\n  brandName: brandsConfig.philips.name,\\r\\n  discountType: 'amount' as const,\\r\\n  discountValue: 2500,\\r\\n  minPurchase: 30000,\\r\\n  onSeeDetails: action('onSeeDetails'),\\r\\n  onClaimCoupon: action('onClaimCoupon'),\\r\\n};\\r\\n\\r\\nconst fixedAmountWithMaxDiscountCoupon = {\\r\\n  id: 'fixed-amount-max-2',\\r\\n  brandLogoUrl: brandsConfig.lamptan.logoUrl,\\r\\n  brandName: brandsConfig.lamptan.name,\\r\\n  discountType: 'amount' as const,\\r\\n  discountValue: 2500,\\r\\n  minPurchase: 3000,\\r\\n  maxDiscount: 1000,\\r\\n  onSeeDetails: action('onSeeDetails'),\\r\\n  onClaimCoupon: action('onClaimCoupon'),\\r\\n};\\r\\n\\r\\nconst percentageCoupon = {\\r\\n  id: 'percentage-1',\\r\\n  brandLogoUrl: brandsConfig.bcc.logoUrl,\\r\\n  brandName: brandsConfig.bcc.name,\\r\\n  discountType: 'percentage' as const,\\r\\n  discountValue: 75,\\r\\n  minPurchase: 30000,\\r\\n  maxDiscount: 1000,\\r\\n  onSeeDetails: action('onSeeDetails'),\\r\\n  onClaimCoupon: action('onClaimCoupon'),\\r\\n};\\r\\n\\r\\nconst maxPercentageCoupon = {\\r\\n  id: 'max-percentage-1',\\r\\n  brandLogoUrl: brandsConfig.bcc.logoUrl,\\r\\n  brandName: brandsConfig.bcc.name,\\r\\n  discountType: 'max-percentage' as const,\\r\\n  discountValue: 50,\\r\\n  minPurchase: 30000,\\r\\n  maxDiscount: 500,\\r\\n  onSeeDetails: action('onSeeDetails'),\\r\\n  onClaimCoupon: action('onClaimCoupon'),\\r\\n};\\r\\n\\r\\nconst smallAmountCoupon = {\\r\\n  id: 'small-amount-1',\\r\\n  brandLogoUrl: brandsConfig.songkhla.logoUrl,\\r\\n  brandName: brandsConfig.songkhla.name,\\r\\n  discountType: 'amount' as const,\\r\\n  discountValue: 500,\\r\\n  minPurchase: 50000,\\r\\n  onSeeDetails: action('onSeeDetails'),\\r\\n  onClaimCoupon: action('onClaimCoupon'),\\r\\n};\\r\\n\\r\\nconst smallPercentageCoupon = {\\r\\n  id: 'small-percentage-1',\\r\\n  brandLogoUrl: brandsConfig.philips.logoUrl,\\r\\n  brandName: brandsConfig.philips.name,\\r\\n  discountType: 'percentage' as const,\\r\\n  discountValue: 10,\\r\\n  minPurchase: 25000,\\r\\n  maxDiscount: 500,\\r\\n  onSeeDetails: action('onSeeDetails'),\\r\\n  onClaimCoupon: action('onClaimCoupon'),\\r\\n};\\r\\n\\r\\nconst largeAmountCoupon = {\\r\\n  id: 'large-amount-1',\\r\\n  brandLogoUrl: brandsConfig.haco.logoUrl,\\r\\n  brandName: brandsConfig.haco.name,\\r\\n  discountType: 'amount' as const,\\r\\n  discountValue: 5000,\\r\\n  minPurchase: 50000,\\r\\n  onSeeDetails: action('onSeeDetails'),\\r\\n  onClaimCoupon: action('onClaimCoupon'),\\r\\n};\\r\\n\\r\\nconst meta: Meta<typeof CardCoupon> = {\\r\\n  title: 'UI/Card/CardCoupon',\\r\\n  component: CardCoupon,\\r\\n  parameters: {\\r\\n    layout: 'centered',\\r\\n    // Add a11y parameter for accessibility testing\\r\\n    a11y: {\\r\\n      config: {\\r\\n        rules: [\\r\\n          {\\r\\n            // Ensure all images have alt text\\r\\n            id: 'image-alt',\\r\\n            enabled: true\\r\\n          }\\r\\n        ]\\r\\n      }\\r\\n    },\\r\\n  },\\r\\n  // Define argTypes for Storybook controls\\r\\n  argTypes: {\\r\\n    id: { \\r\\n      control: 'text',\\r\\n      description: 'Unique coupon identifier',\\r\\n    },\\r\\n    brandLogoUrl: { \\r\\n      control: 'text',\\r\\n      description: 'URL to the brand logo image' \\r\\n    },\\r\\n    brandName: { \\r\\n      control: 'text',\\r\\n      description: 'Brand name for alt text' \\r\\n    },\\r\\n    discountType: { \\r\\n      control: { type: 'select', options: ['amount', 'percentage', 'max-percentage'] },\\r\\n      description: 'Type of discount - fixed amount, percentage, or max percentage' \\r\\n    },\\r\\n    discountValue: { \\r\\n      control: { type: 'number', min: 0 },\\r\\n      description: 'Value of the discount (amount or percentage)' \\r\\n    },\\r\\n    minPurchase: { \\r\\n      control: { type: 'number', min: 0 },\\r\\n      description: 'Minimum purchase required to use the coupon' \\r\\n    },\\r\\n    maxDiscount: { \\r\\n      control: { type: 'number', min: 0 },\\r\\n      description: 'Maximum discount amount for percentage discounts' \\r\\n    },\\r\\n    claimed: { \\r\\n      control: 'boolean',\\r\\n      description: 'Whether the coupon has been claimed already' \\r\\n    },\\r\\n    onSeeDetails: { \\r\\n      action: 'seeDetails',\\r\\n      description: 'Function called when see details button is clicked' \\r\\n    },\\r\\n    onClaimCoupon: { \\r\\n      action: 'claimCoupon',\\r\\n      description: 'Function called when claim button is clicked' \\r\\n    },\\r\\n    isClaimingCoupon: { \\r\\n      control: 'boolean',\\r\\n      description: 'Whether the coupon is currently being claimed' \\r\\n    },\\r\\n    showTermsOnly: {\\r\\n      control: 'boolean',\\r\\n      description: 'Whether to show only the terms button (without claim button)'\\r\\n    }\\r\\n  },\\r\\n  // Default values for the stories\\r\\n  args: {\\r\\n    onSeeDetails: action('onSeeDetails'),\\r\\n    onClaimCoupon: action('onClaimCoupon'),\\r\\n  },\\r\\n  // Add decorators if needed\\r\\n  decorators: [\\r\\n    (Story) => (\\r\\n      <div style={{ maxWidth: '600px', width: '100%', padding: '20px' }}>\\r\\n        <Story />\\r\\n      </div>\\r\\n    ),\\r\\n  ],\\r\\n  // Add tags for filtering in Storybook\\r\\n  tags: ['autodocs', 'coupon', 'card'],\\r\\n};\\r\\n\\r\\nexport default meta;\\r\\ntype Story = StoryObj<typeof CardCoupon>;\\r\\n\\r\\n// Fixed amount discount coupon (฿2,500)\\r\\nexport const FixedAmountCoupon: Story = {\\r\\n  args: {\\r\\n    ...fixedAmountCoupon,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Fixed amount with max discount limit\\r\\nexport const FixedAmountWithMaxDiscount: Story = {\\r\\n  args: {\\r\\n    ...fixedAmountWithMaxDiscountCoupon,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Percentage discount coupon (75%)\\r\\nexport const PercentageCoupon: Story = {\\r\\n  args: {\\r\\n    ...percentageCoupon,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Maximum percentage discount coupon (สูงสุด 50%)\\r\\nexport const MaxPercentageCoupon: Story = {\\r\\n  args: {\\r\\n    ...maxPercentageCoupon,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Small fixed amount coupon (฿500)\\r\\nexport const SmallAmountCoupon: Story = {\\r\\n  args: {\\r\\n    ...smallAmountCoupon,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Small percentage discount coupon (10%)\\r\\nexport const SmallPercentageCoupon: Story = {\\r\\n  args: {\\r\\n    ...smallPercentageCoupon,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Large fixed amount coupon (฿5,000)\\r\\nexport const LargeAmountCoupon: Story = {\\r\\n  args: {\\r\\n    ...largeAmountCoupon,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Claimed coupon example\\r\\nexport const ClaimedCoupon: Story = {\\r\\n  args: {\\r\\n    ...fixedAmountCoupon,\\r\\n    claimed: true,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Coupon in claiming state\\r\\nexport const ClaimingCoupon: Story = {\\r\\n  args: {\\r\\n    ...percentageCoupon,\\r\\n    isClaimingCoupon: true,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Show only terms button\\r\\nexport const TermsOnlyCoupon: Story = {\\r\\n  args: {\\r\\n    ...maxPercentageCoupon,\\r\\n    showTermsOnly: true,\\r\\n  },\\r\\n};\\r\\n\\r\\n// Loading state example\\r\\nexport const LoadingState: Story = {\\r\\n  render: () => <CardCouponSkeleton />,\\r\\n};\";\n    // @ts-expect-error (Converted from ts-ignore)\n    var __LOCATIONS_MAP__ = {\n  \"FixedAmountCoupon\": {\n    \"startLoc\": {\n      \"col\": 33,\n      \"line\": 225\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 229\n    },\n    \"startBody\": {\n      \"col\": 33,\n      \"line\": 225\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 229\n    }\n  },\n  \"FixedAmountWithMaxDiscount\": {\n    \"startLoc\": {\n      \"col\": 42,\n      \"line\": 231\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 235\n    },\n    \"startBody\": {\n      \"col\": 42,\n      \"line\": 231\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 235\n    }\n  },\n  \"PercentageCoupon\": {\n    \"startLoc\": {\n      \"col\": 32,\n      \"line\": 237\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 241\n    },\n    \"startBody\": {\n      \"col\": 32,\n      \"line\": 237\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 241\n    }\n  },\n  \"MaxPercentageCoupon\": {\n    \"startLoc\": {\n      \"col\": 35,\n      \"line\": 243\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 247\n    },\n    \"startBody\": {\n      \"col\": 35,\n      \"line\": 243\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 247\n    }\n  },\n  \"SmallAmountCoupon\": {\n    \"startLoc\": {\n      \"col\": 33,\n      \"line\": 249\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 253\n    },\n    \"startBody\": {\n      \"col\": 33,\n      \"line\": 249\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 253\n    }\n  },\n  \"SmallPercentageCoupon\": {\n    \"startLoc\": {\n      \"col\": 37,\n      \"line\": 255\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 259\n    },\n    \"startBody\": {\n      \"col\": 37,\n      \"line\": 255\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 259\n    }\n  },\n  \"LargeAmountCoupon\": {\n    \"startLoc\": {\n      \"col\": 33,\n      \"line\": 261\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 265\n    },\n    \"startBody\": {\n      \"col\": 33,\n      \"line\": 261\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 265\n    }\n  },\n  \"ClaimedCoupon\": {\n    \"startLoc\": {\n      \"col\": 29,\n      \"line\": 267\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 272\n    },\n    \"startBody\": {\n      \"col\": 29,\n      \"line\": 267\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 272\n    }\n  },\n  \"ClaimingCoupon\": {\n    \"startLoc\": {\n      \"col\": 30,\n      \"line\": 274\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 279\n    },\n    \"startBody\": {\n      \"col\": 30,\n      \"line\": 274\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 279\n    }\n  },\n  \"TermsOnlyCoupon\": {\n    \"startLoc\": {\n      \"col\": 31,\n      \"line\": 281\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 286\n    },\n    \"startBody\": {\n      \"col\": 31,\n      \"line\": 281\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 286\n    }\n  },\n  \"LoadingState\": {\n    \"startLoc\": {\n      \"col\": 28,\n      \"line\": 288\n    },\n    \"endLoc\": {\n      \"col\": 1,\n      \"line\": 294\n    },\n    \"startBody\": {\n      \"col\": 28,\n      \"line\": 288\n    },\n    \"endBody\": {\n      \"col\": 1,\n      \"line\": 294\n    }\n  }\n};\n    \n// card-coupon.stories.tsx\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { action } from '@storybook/addon-actions';\nimport { CardCoupon, CardCouponSkeleton } from '../card-coupon';\n// Sample brand logos - replace with actual URLs in production\nconst brandsConfig = {\n    philips: {\n        logoUrl: '/images/philips-logo.png',\n        name: 'Philips'\n    },\n    lamptan: {\n        logoUrl: '/images/LAMPTAN.png',\n        name: 'LAMPTAN'\n    },\n    bcc: {\n        logoUrl: '/images/BCC.png',\n        name: 'Bangkok Cable'\n    },\n    songkhla: {\n        logoUrl: '/images/Sengaroon.png',\n        name: 'Songkhla Natrin'\n    },\n    panasonic: {\n        logoUrl: '/images/Panasonic.png',\n        name: 'Panasonic'\n    },\n    haco: {\n        logoUrl: '/images/HACO.png',\n        name: 'HACO'\n    }\n};\n// Define sample coupons based on the images provided\nconst fixedAmountCoupon = {\n    id: 'fixed-amount-1',\n    brandLogoUrl: brandsConfig.philips.logoUrl,\n    brandName: brandsConfig.philips.name,\n    discountType: 'amount',\n    discountValue: 2500,\n    minPurchase: 30000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst fixedAmountWithMaxDiscountCoupon = {\n    id: 'fixed-amount-max-2',\n    brandLogoUrl: brandsConfig.lamptan.logoUrl,\n    brandName: brandsConfig.lamptan.name,\n    discountType: 'amount',\n    discountValue: 2500,\n    minPurchase: 3000,\n    maxDiscount: 1000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst percentageCoupon = {\n    id: 'percentage-1',\n    brandLogoUrl: brandsConfig.bcc.logoUrl,\n    brandName: brandsConfig.bcc.name,\n    discountType: 'percentage',\n    discountValue: 75,\n    minPurchase: 30000,\n    maxDiscount: 1000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst maxPercentageCoupon = {\n    id: 'max-percentage-1',\n    brandLogoUrl: brandsConfig.bcc.logoUrl,\n    brandName: brandsConfig.bcc.name,\n    discountType: 'max-percentage',\n    discountValue: 50,\n    minPurchase: 30000,\n    maxDiscount: 500,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst smallAmountCoupon = {\n    id: 'small-amount-1',\n    brandLogoUrl: brandsConfig.songkhla.logoUrl,\n    brandName: brandsConfig.songkhla.name,\n    discountType: 'amount',\n    discountValue: 500,\n    minPurchase: 50000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst smallPercentageCoupon = {\n    id: 'small-percentage-1',\n    brandLogoUrl: brandsConfig.philips.logoUrl,\n    brandName: brandsConfig.philips.name,\n    discountType: 'percentage',\n    discountValue: 10,\n    minPurchase: 25000,\n    maxDiscount: 500,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst largeAmountCoupon = {\n    id: 'large-amount-1',\n    brandLogoUrl: brandsConfig.haco.logoUrl,\n    brandName: brandsConfig.haco.name,\n    discountType: 'amount',\n    discountValue: 5000,\n    minPurchase: 50000,\n    onSeeDetails: action('onSeeDetails'),\n    onClaimCoupon: action('onClaimCoupon')\n};\nconst meta = {\n    title: 'UI/Card/CardCoupon',\n    component: CardCoupon,\n    parameters: {\n  \"storySource\": {\n    \"source\": \"// card-coupon.stories.tsx\\nimport { jsxDEV as _jsxDEV } from \\\"react/jsx-dev-runtime\\\";\\nimport { action } from '@storybook/addon-actions';\\nimport { CardCoupon, CardCouponSkeleton } from '../card-coupon';\\n// Sample brand logos - replace with actual URLs in production\\nconst brandsConfig = {\\n    philips: {\\n        logoUrl: '/images/philips-logo.png',\\n        name: 'Philips'\\n    },\\n    lamptan: {\\n        logoUrl: '/images/LAMPTAN.png',\\n        name: 'LAMPTAN'\\n    },\\n    bcc: {\\n        logoUrl: '/images/BCC.png',\\n        name: 'Bangkok Cable'\\n    },\\n    songkhla: {\\n        logoUrl: '/images/Sengaroon.png',\\n        name: 'Songkhla Natrin'\\n    },\\n    panasonic: {\\n        logoUrl: '/images/Panasonic.png',\\n        name: 'Panasonic'\\n    },\\n    haco: {\\n        logoUrl: '/images/HACO.png',\\n        name: 'HACO'\\n    }\\n};\\n// Define sample coupons based on the images provided\\nconst fixedAmountCoupon = {\\n    id: 'fixed-amount-1',\\n    brandLogoUrl: brandsConfig.philips.logoUrl,\\n    brandName: brandsConfig.philips.name,\\n    discountType: 'amount',\\n    discountValue: 2500,\\n    minPurchase: 30000,\\n    onSeeDetails: action('onSeeDetails'),\\n    onClaimCoupon: action('onClaimCoupon')\\n};\\nconst fixedAmountWithMaxDiscountCoupon = {\\n    id: 'fixed-amount-max-2',\\n    brandLogoUrl: brandsConfig.lamptan.logoUrl,\\n    brandName: brandsConfig.lamptan.name,\\n    discountType: 'amount',\\n    discountValue: 2500,\\n    minPurchase: 3000,\\n    maxDiscount: 1000,\\n    onSeeDetails: action('onSeeDetails'),\\n    onClaimCoupon: action('onClaimCoupon')\\n};\\nconst percentageCoupon = {\\n    id: 'percentage-1',\\n    brandLogoUrl: brandsConfig.bcc.logoUrl,\\n    brandName: brandsConfig.bcc.name,\\n    discountType: 'percentage',\\n    discountValue: 75,\\n    minPurchase: 30000,\\n    maxDiscount: 1000,\\n    onSeeDetails: action('onSeeDetails'),\\n    onClaimCoupon: action('onClaimCoupon')\\n};\\nconst maxPercentageCoupon = {\\n    id: 'max-percentage-1',\\n    brandLogoUrl: brandsConfig.bcc.logoUrl,\\n    brandName: brandsConfig.bcc.name,\\n    discountType: 'max-percentage',\\n    discountValue: 50,\\n    minPurchase: 30000,\\n    maxDiscount: 500,\\n    onSeeDetails: action('onSeeDetails'),\\n    onClaimCoupon: action('onClaimCoupon')\\n};\\nconst smallAmountCoupon = {\\n    id: 'small-amount-1',\\n    brandLogoUrl: brandsConfig.songkhla.logoUrl,\\n    brandName: brandsConfig.songkhla.name,\\n    discountType: 'amount',\\n    discountValue: 500,\\n    minPurchase: 50000,\\n    onSeeDetails: action('onSeeDetails'),\\n    onClaimCoupon: action('onClaimCoupon')\\n};\\nconst smallPercentageCoupon = {\\n    id: 'small-percentage-1',\\n    brandLogoUrl: brandsConfig.philips.logoUrl,\\n    brandName: brandsConfig.philips.name,\\n    discountType: 'percentage',\\n    discountValue: 10,\\n    minPurchase: 25000,\\n    maxDiscount: 500,\\n    onSeeDetails: action('onSeeDetails'),\\n    onClaimCoupon: action('onClaimCoupon')\\n};\\nconst largeAmountCoupon = {\\n    id: 'large-amount-1',\\n    brandLogoUrl: brandsConfig.haco.logoUrl,\\n    brandName: brandsConfig.haco.name,\\n    discountType: 'amount',\\n    discountValue: 5000,\\n    minPurchase: 50000,\\n    onSeeDetails: action('onSeeDetails'),\\n    onClaimCoupon: action('onClaimCoupon')\\n};\\nconst meta = {\\n    title: 'UI/Card/CardCoupon',\\n    component: CardCoupon,\\n    parameters: {\\n        layout: 'centered',\\n        // Add a11y parameter for accessibility testing\\n        a11y: {\\n            config: {\\n                rules: [\\n                    {\\n                        // Ensure all images have alt text\\n                        id: 'image-alt',\\n                        enabled: true\\n                    }\\n                ]\\n            }\\n        }\\n    },\\n    // Define argTypes for Storybook controls\\n    argTypes: {\\n        id: {\\n            control: 'text',\\n            description: 'Unique coupon identifier'\\n        },\\n        brandLogoUrl: {\\n            control: 'text',\\n            description: 'URL to the brand logo image'\\n        },\\n        brandName: {\\n            control: 'text',\\n            description: 'Brand name for alt text'\\n        },\\n        discountType: {\\n            control: {\\n                type: 'select',\\n                options: [\\n                    'amount',\\n                    'percentage',\\n                    'max-percentage'\\n                ]\\n            },\\n            description: 'Type of discount - fixed amount, percentage, or max percentage'\\n        },\\n        discountValue: {\\n            control: {\\n                type: 'number',\\n                min: 0\\n            },\\n            description: 'Value of the discount (amount or percentage)'\\n        },\\n        minPurchase: {\\n            control: {\\n                type: 'number',\\n                min: 0\\n            },\\n            description: 'Minimum purchase required to use the coupon'\\n        },\\n        maxDiscount: {\\n            control: {\\n                type: 'number',\\n                min: 0\\n            },\\n            description: 'Maximum discount amount for percentage discounts'\\n        },\\n        claimed: {\\n            control: 'boolean',\\n            description: 'Whether the coupon has been claimed already'\\n        },\\n        onSeeDetails: {\\n            action: 'seeDetails',\\n            description: 'Function called when see details button is clicked'\\n        },\\n        onClaimCoupon: {\\n            action: 'claimCoupon',\\n            description: 'Function called when claim button is clicked'\\n        },\\n        isClaimingCoupon: {\\n            control: 'boolean',\\n            description: 'Whether the coupon is currently being claimed'\\n        },\\n        showTermsOnly: {\\n            control: 'boolean',\\n            description: 'Whether to show only the terms button (without claim button)'\\n        }\\n    },\\n    // Default values for the stories\\n    args: {\\n        onSeeDetails: action('onSeeDetails'),\\n        onClaimCoupon: action('onClaimCoupon')\\n    },\\n    // Add decorators if needed\\n    decorators: [\\n        (Story)=>/*#__PURE__*/ _jsxDEV(\\\"div\\\", {\\n                style: {\\n                    maxWidth: '600px',\\n                    width: '100%',\\n                    padding: '20px'\\n                },\\n                children: /*#__PURE__*/ _jsxDEV(Story, {}, void 0, false, {\\n                    fileName: \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Desktop\\\\\\\\fristJob\\\\\\\\sangaroon-nakharin-ecommerce\\\\\\\\frontend\\\\\\\\home\\\\\\\\src\\\\\\\\components\\\\\\\\Card\\\\\\\\__stories__\\\\\\\\card-coupon.stories.tsx\\\",\\n                    lineNumber: 194,\\n                    columnNumber: 9\\n                }, this)\\n            }, void 0, false, {\\n                fileName: \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Desktop\\\\\\\\fristJob\\\\\\\\sangaroon-nakharin-ecommerce\\\\\\\\frontend\\\\\\\\home\\\\\\\\src\\\\\\\\components\\\\\\\\Card\\\\\\\\__stories__\\\\\\\\card-coupon.stories.tsx\\\",\\n                lineNumber: 193,\\n                columnNumber: 7\\n            }, this)\\n    ],\\n    // Add tags for filtering in Storybook\\n    tags: [\\n        'autodocs',\\n        'coupon',\\n        'card'\\n    ]\\n};\\nexport default meta;\\n// Fixed amount discount coupon (฿2,500)\\nexport const FixedAmountCoupon = {\\n    args: {\\n        ...fixedAmountCoupon\\n    }\\n};\\n// Fixed amount with max discount limit\\nexport const FixedAmountWithMaxDiscount = {\\n    args: {\\n        ...fixedAmountWithMaxDiscountCoupon\\n    }\\n};\\n// Percentage discount coupon (75%)\\nexport const PercentageCoupon = {\\n    args: {\\n        ...percentageCoupon\\n    }\\n};\\n// Maximum percentage discount coupon (สูงสุด 50%)\\nexport const MaxPercentageCoupon = {\\n    args: {\\n        ...maxPercentageCoupon\\n    }\\n};\\n// Small fixed amount coupon (฿500)\\nexport const SmallAmountCoupon = {\\n    args: {\\n        ...smallAmountCoupon\\n    }\\n};\\n// Small percentage discount coupon (10%)\\nexport const SmallPercentageCoupon = {\\n    args: {\\n        ...smallPercentageCoupon\\n    }\\n};\\n// Large fixed amount coupon (฿5,000)\\nexport const LargeAmountCoupon = {\\n    args: {\\n        ...largeAmountCoupon\\n    }\\n};\\n// Claimed coupon example\\nexport const ClaimedCoupon = {\\n    args: {\\n        ...fixedAmountCoupon,\\n        claimed: true\\n    }\\n};\\n// Coupon in claiming state\\nexport const ClaimingCoupon = {\\n    args: {\\n        ...percentageCoupon,\\n        isClaimingCoupon: true\\n    }\\n};\\n// Show only terms button\\nexport const TermsOnlyCoupon = {\\n    args: {\\n        ...maxPercentageCoupon,\\n        showTermsOnly: true\\n    }\\n};\\n// Loading state example\\nexport const LoadingState = {\\n    render: ()=>/*#__PURE__*/ _jsxDEV(CardCouponSkeleton, {}, void 0, false, {\\n            fileName: \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\Desktop\\\\\\\\fristJob\\\\\\\\sangaroon-nakharin-ecommerce\\\\\\\\frontend\\\\\\\\home\\\\\\\\src\\\\\\\\components\\\\\\\\Card\\\\\\\\__stories__\\\\\\\\card-coupon.stories.tsx\\\",\\n            lineNumber: 280,\\n            columnNumber: 17\\n        }, this)\\n};\\n\",\n    \"locationsMap\": {\n      \"fixed-amount-coupon\": {\n        \"startLoc\": {\n          \"col\": 33,\n          \"line\": 225\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 229\n        },\n        \"startBody\": {\n          \"col\": 33,\n          \"line\": 225\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 229\n        }\n      },\n      \"fixed-amount-with-max-discount\": {\n        \"startLoc\": {\n          \"col\": 42,\n          \"line\": 231\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 235\n        },\n        \"startBody\": {\n          \"col\": 42,\n          \"line\": 231\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 235\n        }\n      },\n      \"percentage-coupon\": {\n        \"startLoc\": {\n          \"col\": 32,\n          \"line\": 237\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 241\n        },\n        \"startBody\": {\n          \"col\": 32,\n          \"line\": 237\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 241\n        }\n      },\n      \"max-percentage-coupon\": {\n        \"startLoc\": {\n          \"col\": 35,\n          \"line\": 243\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 247\n        },\n        \"startBody\": {\n          \"col\": 35,\n          \"line\": 243\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 247\n        }\n      },\n      \"small-amount-coupon\": {\n        \"startLoc\": {\n          \"col\": 33,\n          \"line\": 249\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 253\n        },\n        \"startBody\": {\n          \"col\": 33,\n          \"line\": 249\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 253\n        }\n      },\n      \"small-percentage-coupon\": {\n        \"startLoc\": {\n          \"col\": 37,\n          \"line\": 255\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 259\n        },\n        \"startBody\": {\n          \"col\": 37,\n          \"line\": 255\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 259\n        }\n      },\n      \"large-amount-coupon\": {\n        \"startLoc\": {\n          \"col\": 33,\n          \"line\": 261\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 265\n        },\n        \"startBody\": {\n          \"col\": 33,\n          \"line\": 261\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 265\n        }\n      },\n      \"claimed-coupon\": {\n        \"startLoc\": {\n          \"col\": 29,\n          \"line\": 267\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 272\n        },\n        \"startBody\": {\n          \"col\": 29,\n          \"line\": 267\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 272\n        }\n      },\n      \"claiming-coupon\": {\n        \"startLoc\": {\n          \"col\": 30,\n          \"line\": 274\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 279\n        },\n        \"startBody\": {\n          \"col\": 30,\n          \"line\": 274\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 279\n        }\n      },\n      \"terms-only-coupon\": {\n        \"startLoc\": {\n          \"col\": 31,\n          \"line\": 281\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 286\n        },\n        \"startBody\": {\n          \"col\": 31,\n          \"line\": 281\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 286\n        }\n      },\n      \"loading-state\": {\n        \"startLoc\": {\n          \"col\": 28,\n          \"line\": 288\n        },\n        \"endLoc\": {\n          \"col\": 1,\n          \"line\": 294\n        },\n        \"startBody\": {\n          \"col\": 28,\n          \"line\": 288\n        },\n        \"endBody\": {\n          \"col\": 1,\n          \"line\": 294\n        }\n      }\n    }\n  }\n,\n        layout: 'centered',\n        // Add a11y parameter for accessibility testing\n        a11y: {\n            config: {\n                rules: [\n                    {\n                        // Ensure all images have alt text\n                        id: 'image-alt',\n                        enabled: true\n                    }\n                ]\n            }\n        }\n    },\n    // Define argTypes for Storybook controls\n    argTypes: {\n        id: {\n            control: 'text',\n            description: 'Unique coupon identifier'\n        },\n        brandLogoUrl: {\n            control: 'text',\n            description: 'URL to the brand logo image'\n        },\n        brandName: {\n            control: 'text',\n            description: 'Brand name for alt text'\n        },\n        discountType: {\n            control: {\n                type: 'select',\n                options: [\n                    'amount',\n                    'percentage',\n                    'max-percentage'\n                ]\n            },\n            description: 'Type of discount - fixed amount, percentage, or max percentage'\n        },\n        discountValue: {\n            control: {\n                type: 'number',\n                min: 0\n            },\n            description: 'Value of the discount (amount or percentage)'\n        },\n        minPurchase: {\n            control: {\n                type: 'number',\n                min: 0\n            },\n            description: 'Minimum purchase required to use the coupon'\n        },\n        maxDiscount: {\n            control: {\n                type: 'number',\n                min: 0\n            },\n            description: 'Maximum discount amount for percentage discounts'\n        },\n        claimed: {\n            control: 'boolean',\n            description: 'Whether the coupon has been claimed already'\n        },\n        onSeeDetails: {\n            action: 'seeDetails',\n            description: 'Function called when see details button is clicked'\n        },\n        onClaimCoupon: {\n            action: 'claimCoupon',\n            description: 'Function called when claim button is clicked'\n        },\n        isClaimingCoupon: {\n            control: 'boolean',\n            description: 'Whether the coupon is currently being claimed'\n        },\n        showTermsOnly: {\n            control: 'boolean',\n            description: 'Whether to show only the terms button (without claim button)'\n        }\n    },\n    // Default values for the stories\n    args: {\n        onSeeDetails: action('onSeeDetails'),\n        onClaimCoupon: action('onClaimCoupon')\n    },\n    // Add decorators if needed\n    decorators: [\n        (Story)=>/*#__PURE__*/ _jsxDEV(\"div\", {\n                style: {\n                    maxWidth: '600px',\n                    width: '100%',\n                    padding: '20px'\n                },\n                children: /*#__PURE__*/ _jsxDEV(Story, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Card\\\\__stories__\\\\card-coupon.stories.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Card\\\\__stories__\\\\card-coupon.stories.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n    ],\n    // Add tags for filtering in Storybook\n    tags: [\n        'autodocs',\n        'coupon',\n        'card'\n    ]\n};\nexport default meta;\n// Fixed amount discount coupon (฿2,500)\nexport const FixedAmountCoupon = {\n    args: {\n        ...fixedAmountCoupon\n    }\n};;\n// Fixed amount with max discount limit\nexport const FixedAmountWithMaxDiscount = {\n    args: {\n        ...fixedAmountWithMaxDiscountCoupon\n    }\n};;\n// Percentage discount coupon (75%)\nexport const PercentageCoupon = {\n    args: {\n        ...percentageCoupon\n    }\n};;\n// Maximum percentage discount coupon (สูงสุด 50%)\nexport const MaxPercentageCoupon = {\n    args: {\n        ...maxPercentageCoupon\n    }\n};;\n// Small fixed amount coupon (฿500)\nexport const SmallAmountCoupon = {\n    args: {\n        ...smallAmountCoupon\n    }\n};;\n// Small percentage discount coupon (10%)\nexport const SmallPercentageCoupon = {\n    args: {\n        ...smallPercentageCoupon\n    }\n};;\n// Large fixed amount coupon (฿5,000)\nexport const LargeAmountCoupon = {\n    args: {\n        ...largeAmountCoupon\n    }\n};;\n// Claimed coupon example\nexport const ClaimedCoupon = {\n    args: {\n        ...fixedAmountCoupon,\n        claimed: true\n    }\n};;\n// Coupon in claiming state\nexport const ClaimingCoupon = {\n    args: {\n        ...percentageCoupon,\n        isClaimingCoupon: true\n    }\n};;\n// Show only terms button\nexport const TermsOnlyCoupon = {\n    args: {\n        ...maxPercentageCoupon,\n        showTermsOnly: true\n    }\n};;\n// Loading state example\nexport const LoadingState = {\n    render: ()=>/*#__PURE__*/ _jsxDEV(CardCouponSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Card\\\\__stories__\\\\card-coupon.stories.tsx\",\n            lineNumber: 280,\n            columnNumber: 17\n        }, this)\n};\n;export const __namedExportsOrder = [\"FixedAmountCoupon\",\"FixedAmountWithMaxDiscount\",\"PercentageCoupon\",\"MaxPercentageCoupon\",\"SmallAmountCoupon\",\"SmallPercentageCoupon\",\"LargeAmountCoupon\",\"ClaimedCoupon\",\"ClaimingCoupon\",\"TermsOnlyCoupon\",\"LoadingState\"];", "import React from 'react';\r\nimport Image from 'next/image';\r\nimport { cn } from \"@/lib/utils\";\r\nimport { \r\n  Card, \r\n  CardContent,\r\n} from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nexport interface CardCouponProps {\r\n  id: string;\r\n  brandLogoUrl: string;\r\n  brandName: string;\r\n  discountType: 'amount' | 'percentage' | 'max-percentage';\r\n  discountValue: number;\r\n  minPurchase?: number;\r\n  maxDiscount?: number;\r\n  claimed?: boolean;\r\n  className?: string;\r\n  onSeeDetails?: (id: string) => void;\r\n  onClaimCoupon?: (id: string) => void;\r\n  isClaimingCoupon?: boolean;\r\n  showTermsOnly?: boolean;\r\n}\r\n\r\nexport const CardCoupon = ({\r\n  id,\r\n  brandLogoUrl,\r\n  brandName,\r\n  discountType,\r\n  discountValue,\r\n  minPurchase,\r\n  maxDiscount,\r\n  claimed = false,\r\n  className,\r\n  onSeeDetails,\r\n  onClaimCoupon,\r\n  isClaimingCoupon = false,\r\n  showTermsOnly = false,\r\n}: CardCouponProps) => {\r\n  const handleSeeDetails = () => {\r\n    if (onSeeDetails) {\r\n      onSeeDetails(id);\r\n    }\r\n  };\r\n\r\n  const handleClaimCoupon = () => {\r\n    if (onClaimCoupon && !isClaimingCoupon && !claimed) {\r\n      onClaimCoupon(id);\r\n    }\r\n  };\r\n\r\n  const renderDiscountTitle = () => (\r\n    <div className=\"text-gray-600 text-sm\">ส่วนลด</div>\r\n  );\r\n\r\n  const renderDiscountValue = () => {\r\n    if (discountType === 'amount') {\r\n      return (\r\n        <div className=\"text-3xl font-bold text-blue-900\">\r\n          {new Intl.NumberFormat('th-TH').format(discountValue)} บาท\r\n        </div>\r\n      );\r\n    } else if (discountType === 'percentage') {\r\n      return (\r\n        <div className=\"text-3xl font-bold text-blue-900\">\r\n          {discountValue}%\r\n        </div>\r\n      );\r\n    } else if (discountType === 'max-percentage') {\r\n      return (\r\n        <div className=\"text-3xl font-bold text-blue-900\">\r\n          สูงสุด {discountValue}%\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const renderPurchaseRequirement = () => {\r\n    if (discountType === 'amount' && minPurchase) {\r\n      return (\r\n        <div className=\"text-xs text-gray-500\">\r\n          เมื่อซื้อสินค้าขั้นต่ำ ฿{new Intl.NumberFormat('th-TH').format(minPurchase)}\r\n        </div>\r\n      );\r\n    } else if ((discountType === 'percentage' || discountType === 'max-percentage') && minPurchase) {\r\n      return (\r\n        <div className=\"text-xs text-gray-500\">\r\n          ซื้อขั้นต่ำ ฿{new Intl.NumberFormat('th-TH').format(minPurchase)}\r\n          {maxDiscount && ` ลดสูงสุด ฿${new Intl.NumberFormat('th-TH').format(maxDiscount)}`}\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const claimButtonText = claimed ? 'เก็บคูปองแล้ว' : 'เก็บคูปองเลย!';\r\n  const claimButtonClass = claimed \r\n    ? 'bg-white text-gray-400 border border-gray-300 hover:bg-gray-50' \r\n    : 'bg-blue-900 text-white hover:bg-blue-800';\r\n\r\n  return (\r\n    <Card\r\n  className={cn(\r\n    \"relative overflow-hidden border border-gray-200 \",\r\n    \"rounded-xl w-full md:w-[444px] min-h-[168px] bg-white z-0\", // ปรับขนาดให้ responsive\r\n    // ,\r\n    className\r\n  )}\r\n  data-testid=\"card-coupon\"\r\n>\r\n  {/* Notch left */}\r\n  <div className=\"absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-12 h-12 bg-[#f5f5f5] rounded-full z-10 \" />\r\n\r\n  {/* Notch right */}\r\n  <div className=\"absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-12 h-12 bg-[#f5f5f5] rounded-full z-10 \" />\r\n\r\n      <CardContent className=\"p-4 md:p-6 flex flex-row h-full\">\r\n        {/* Brand logo section */}\r\n        <div className=\"w-1/3 flex items-center justify-start relative pr-6\"> {/* เพิ่ม padding-right */}\r\n          <div className=\"relative h-[70px] w-[70px] md:h-[100px] md:w-[100px]\"> {/* ปรับขนาดรูปให้เล็กลงบนมือถือ */}\r\n            <Image\r\n              src={brandLogoUrl}\r\n              alt={brandName}\r\n              fill\r\n              className=\"object-contain\"\r\n              sizes=\"(max-width: 768px) 70px, 100px\"\r\n              data-testid=\"coupon-brand-logo\"\r\n            />\r\n          </div>\r\n          {/* Vertical dotted line */}\r\n          <div className=\"absolute right-3 md:right-4 top-0 h-full\"> {/* ปรับตำแหน่งเส้นประ */}\r\n            <div className=\"h-full flex flex-col justify-around items-center\">\r\n              {[...Array(6)].map((_, i) => (\r\n                <div \r\n                  key={i} \r\n                  className=\"w-[1px] h-[10px] md:h-[12px] border-r border-[#F0F0F0]\" \r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {/* Discount information and actions section */}\r\n        <div className=\"w-2/3 flex flex-col justify-between pl-2 md:pl-4\">\r\n          <div className=\"space-y-2\" data-testid=\"coupon-discount-info\">\r\n            {renderDiscountTitle()}\r\n            <div className=\"text-2xl md:text-3xl font-bold text-blue-900\">\r\n              {renderDiscountValue()}\r\n            </div>\r\n            <div className=\"text-[11px] md:text-xs text-gray-500\">\r\n              {renderPurchaseRequirement()}\r\n            </div>\r\n          </div>\r\n          \r\n          {/* ปรับส่วนปุ่ม */}\r\n          <div className=\"flex flex-wrap gap-2 mt-4 w-full\" data-testid=\"coupon-actions\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={handleSeeDetails}\r\n              className=\"hidden md:inline-flex rounded-lg h-10 text-gray-600 border-gray-200 bg-[#F6F7F9] hover:bg-gray-50 hover:text-gray-700 text-sm\"\r\n              data-testid=\"see-details-button\"\r\n            >\r\n              เงื่อนไข\r\n            </Button>\r\n            \r\n            {!showTermsOnly && (\r\n              <Button\r\n                size=\"sm\"\r\n                onClick={handleClaimCoupon}\r\n                disabled={isClaimingCoupon || claimed}\r\n                className={cn(\r\n                  \"rounded-lg h-10 font-normal text-sm\",\r\n                  claimButtonClass,\r\n                  isClaimingCoupon && \"opacity-70 cursor-wait\"\r\n                )}\r\n                data-testid=\"claim-coupon-button\"\r\n              >\r\n                {claimButtonText}\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport const CardCouponSkeleton = () => (\r\n  <Card className=\"relative overflow-hidden border border-gray-200  rounded-xl w-[444px] h-[168px] bg-white z-0\">\r\n    <div className=\"absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 w-6 h-6 bg-white border border-gray-200 rounded-full z-10\" />\r\n    <div className=\"absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 w-6 h-6 bg-white border border-gray-200 rounded-full z-10\" />\r\n    \r\n    <CardContent className=\"p-4 flex flex-col md:flex-row\">\r\n      <div className=\"w-full md:w-1/3 flex items-center justify-center md:justify-start mb-4 md:mb-0\">\r\n        <Skeleton className=\"h-12 w-32\" />\r\n      </div>\r\n      \r\n      <div className=\"w-full md:w-2/3 flex flex-col\">\r\n        <div className=\"mb-2\">\r\n          <Skeleton className=\"h-4 w-16 mb-1\" />\r\n          <Skeleton className=\"h-8 w-32 mb-1\" />\r\n          <Skeleton className=\"h-3 w-48\" />\r\n        </div>\r\n        \r\n        <div className=\"flex flex-wrap gap-2 mt-1\">\r\n          <Skeleton className=\"h-10 w-24 rounded-lg\" />\r\n          <Skeleton className=\"h-10 w-32 rounded-lg\" />\r\n        </div>\r\n      </div>\r\n    </CardContent>\r\n  </Card>\r\n);\r\n\r\nexport default CardCoupon;", "import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {\r\n  return <div className={cn('bg-primary/10 animate-pulse rounded-md', className)} {...props} />;\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "sourceRoot": ""}