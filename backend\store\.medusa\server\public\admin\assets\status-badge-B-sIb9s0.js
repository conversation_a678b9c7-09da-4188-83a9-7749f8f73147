import{bW as o,r as e,m as s}from"./index-Bwql5Dzz.js";const d=o({base:"flex items-center justify-center w-5 h-[18px] [&_div]:w-2 [&_div]:h-2 [&_div]:rounded-sm",variants:{color:{green:"[&_div]:bg-ui-tag-green-icon",red:"[&_div]:bg-ui-tag-red-icon",orange:"[&_div]:bg-ui-tag-orange-icon",blue:"[&_div]:bg-ui-tag-blue-icon",purple:"[&_div]:bg-ui-tag-purple-icon",grey:"[&_div]:bg-ui-tag-neutral-icon"}},defaultVariants:{color:"grey"}}),l=e.forwardRef(({children:r,className:t,color:a="grey",...i},n)=>e.createElement("span",{ref:n,className:s("txt-compact-xsmall-plus bg-ui-bg-subtle text-ui-fg-subtle border-ui-border-base box-border flex w-fit select-none items-center overflow-hidden rounded-md border pl-0 pr-1 leading-none",t),...i},e.createElement("div",{role:"presentation",className:d({color:a})},e.createElement("div",null)),r));l.displayName="StatusBadge";export{l as S};
