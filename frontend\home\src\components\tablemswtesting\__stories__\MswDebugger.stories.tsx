import type { Meta, StoryObj } from '@storybook/react';
import { MswDebugger } from './MswDebugger';
import { TableMswHandlers } from '../../../../.storybook/mswHandlers/TableMsw.handlers';

const meta = {
  title: 'Components/TableMswTesting/Debug',
  component: MswDebugger,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A component for debugging MSW handlers in Storybook'
      }
    },
    msw: {
      handlers: [...TableMswHandlers],
    },
  },
  tags: ['autodocs'],
  argTypes: {
    method: {
      control: 'select',
      options: ['GET', 'POST', 'PUT', 'DELETE'],
    },
    simulationOptions: {
      control: 'object',
    },
    payload: {
      control: 'object',
    },
  },
} satisfies Meta<typeof MswDebugger>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default GET story
export const GetUsers: Story = {
  args: {
    endpoint: '/api/users',
    method: 'GET',
    buttonLabel: 'Fetch Users',
    autoFetch: false,
  },
};

// GET a specific user
export const GetSingleUser: Story = {
  args: {
    endpoint: '/api/users/user-1',
    method: 'GET',
    buttonLabel: 'Fetch User',
    autoFetch: false,
  },
};

// POST create a new user
export const CreateUser: Story = {
  args: {
    endpoint: '/api/users',
    method: 'POST',
    buttonLabel: 'Create User',
    payload: {
      name: 'New User',
      email: '<EMAIL>',
      role: 'Viewer',
      status: 'pending'
    },
    autoFetch: false,
  },
};

// PUT update a user
export const UpdateUser: Story = {
  args: {
    endpoint: '/api/users/user-1',
    method: 'PUT',
    buttonLabel: 'Update User',
    payload: {
      name: 'Updated Name',
      role: 'Editor'
    },
    autoFetch: false,
  },
};

// DELETE a user
export const DeleteUser: Story = {
  args: {
    endpoint: '/api/users/user-1',
    method: 'DELETE',
    buttonLabel: 'Delete User',
    autoFetch: false,
  },
}; 