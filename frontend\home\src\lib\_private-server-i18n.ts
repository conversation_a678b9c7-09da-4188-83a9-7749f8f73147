/**
 * @private
 * PRIVATE - SERVER-SIDE i18n IMPLEMENTATION
 * This file is intended for internal use only.
 * Use the unified i18n API from unified-i18n.ts instead.
 */

import path from 'path';
import { type SupportedLanguage, languageCodes, type TranslationObject } from './_private-shared-i18n';

/**
 * Simple cache for server-side translations to avoid repeated file system access
 */
const translationsCache: Record<string, TranslationObject> = {};

/**
 * Load translations for server-side rendering
 */
async function loadTranslations(language: SupportedLanguage, namespace: string): Promise<TranslationObject> {
  const cacheKey = `${language}:${namespace}`;
  
  if (translationsCache[cacheKey]) {
    return translationsCache[cacheKey];
  }

  try {
    // Check if running on server-side
    if (typeof window === 'undefined') {
      // Dynamically import fs/promises only on the server
      const fs = await import('fs/promises');
      
      // Load the translation file for the namespace
      const filePath = path.join(process.cwd(), 'public/locales', language, `${namespace}.json`);
      const fileContent = await fs.readFile(filePath, 'utf8');
      const translations = JSON.parse(fileContent) as TranslationObject;
      
      // Cache the translations
      translationsCache[cacheKey] = translations;
      
      return translations;
    } else {
      // For client-side, return empty object (should never happen as this is a server-only module)
      console.warn('Attempted to use server-side translation on the client');
      return {};
    }
  } catch (error) {
    console.error(`Error loading translation file for ${language}:${namespace}:`, error);
    return {};
  }
}

/**
 * Get a translation value from a key (with nested key support)
 */
function getTranslationValue(obj: TranslationObject, keyPath: string[], fallback?: string): string {
  let current: string | TranslationObject = obj;

  for (const key of keyPath) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return fallback || keyPath.join('.');
    }
  }

  return typeof current === 'string' ? current : fallback || keyPath.join('.');
}

/**
 * Create a translation function for a given namespace and language
 * This function is used internally and exported through getServerTranslation
 */
export async function getServerTranslation(namespace: string = 'common', language: SupportedLanguage = 'en') {
  const translations = await loadTranslations(language, namespace);
  
  // Simple translation function that gets nested keys
  const t = (key: string, fallback?: string) => {
    const keys = key.split('.');
    return getTranslationValue(translations, keys, fallback);
  };

  return { t };
} 