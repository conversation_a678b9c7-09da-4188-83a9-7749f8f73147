{"app": {"title": "الجدول الزمني", "description": "مكون جدول زمني قابل للتخصيص لعرض الأحداث الزمنية."}, "nav": {"home": "الرئيسية", "about": "حو<PERSON>", "settings": "الإعدادات", "installation": "التثبيت", "usage": "الاستخدام", "examples": "أمثلة", "rendering_examples": "أمثلة العرض"}, "buttons": {"submit": "إرسال", "cancel": "إلغاء", "save": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "clickMe": "اضغط هنا"}, "language": {"en": "الإنجليزية", "fr": "الفرنسية", "ja": "اليابانية", "ar": "العربية", "switchLanguage": "تغيير اللغة", "current": "اللغة الحالية"}, "theme": {"current": "السمة الحالية"}, "rendering": {"backToExamples": "العودة إلى الأمثلة", "backToCSR": "العودة إلى مثال CSR", "backToSPA": "العودة إلى مثال SPA", "backToSSR": "العودة إلى مثال SSR", "viewRouterExample": "عرض مثال الموجه مع دعم السمة واللغة", "viewFullSPAExample": "عرض مثال SPA كامل مع دعم السمة واللغة", "basicExample": "مثال أساسي", "withDataExample": "مثال مع بيانات", "advancedExample": "مثال متقدم", "ssr": "SSR", "ssg": "SSG", "csr": "CSR", "isr": "ISR", "spa": "SPA", "routes": {"basic": "أساسي", "withData": "مع بيانات", "advanced": "متقدم", "home": "الرئيسية", "about": "حو<PERSON>", "products": "المنتجات", "contact": "اتصل بنا"}, "ssrTitle": "عرض من جانب الخادم (SSR)", "ssrHowWorks": "كيف <PERSON>", "ssrDescription": "في العرض من جانب الخادم، يتم إنشاء HTML لهذه الصفحة على الخادم لكل طلب. وهذا يعني أن المحتوى دائمًا محدث، لأنه يتم إنشاؤه عندما يطلب المستخدم الصفحة.", "ssrNote": "هذا مفيد للصفحات التي يتغير محتواها بشكل متكرر أو التي تحتاج إلى تخصيص لكل مستخدم. SSR مثالي أيضًا لتحسين محركات البحث (SEO) حيث يمكن لمحركات البحث رؤية المحتوى المعروض بالكامل.", "ssrServerData": "البيانات المنشأة من الخادم", "ssrServerDataDescription": "تم إنشاء هذه البيانات على الخادم وقت الطلب:", "ssrRefreshNote": "حدّث الصفحة لرؤية هذه القيم تتغير، مما يؤكد أن الصفحة تُعرض لكل طلب.", "ssrImplementation": "التنفيذ", "ssrImplementationDescription": "في Next.js 15، الإعداد الافتراضي للصفحات في AppRouter هو SSR. ما عليك سوى تصدير مكون خادم يجلب البيانات.", "ssrWithData": "SSR مع بيانات مفصلة", "ssrDataFetch": "بيانات المستخدم الديناميكية", "ssrDataFetchDescription": "يوضح هذا المثال كيفية جلب بيانات أكثر تعقيدًا على الخادم لكل طلب. فيما يلي بيانات المستخدم التي يتم إنشاؤها حديثًا في كل مرة يتم فيها تحميل الصفحة.", "userData": "بيانات المستخدم", "userId": "المعرف", "userName": "الاسم", "userRole": "الدور", "userLastActive": "آخر نشاط", "systemStatus": "حالة النظام", "cpuUsage": "استخدام وحدة المعالجة المركزية", "memoryUsage": "استخدام الذاكرة", "uptime": "وقت التشغيل", "generatedAt": "تم الإنشاء في", "ssrAdvanced": "تحليلات SSR متقدمة", "realTimeAnalytics": "لوحة تحليلات في الوقت الحقيقي", "ssrAdvancedDescription": "يوضح هذا المثال المتقدم كيفية استخدام SSR لإنشاء لوحة معلومات غنية بالبيانات ومعقدة تحتوي على أحدث المعلومات لكل طلب.", "pageViews": "مشاهدات الصفحة", "today": "اليوم", "weekly": "أسبوعي", "monthly": "شهري", "userSessions": "جلسات المستخدم", "avgDuration": "متوسط المدة", "bounceRate": "معدل الارتداد", "newUsers": "مستخدمون جدد", "serverLoad": "<PERSON><PERSON><PERSON> الخ<PERSON>م", "current": "الحالي", "average": "المتوسط", "peak": "الذروة", "ssrAdvantages": "مزايا SSR", "ssrAdvantage1": "بيانات محدثة دائمًا لكل طلب", "ssrAdvantage2": "تحسين محركات البحث (SEO) ممتاز حيث يمكن لمحركات البحث رؤية HTML الكامل", "ssrAdvantage3": "وقت أقل للمحتوى الظاهر للمستخدمين", "ssrAdvantage4": "يعمل حتى عندما يكون JavaScript معطلاً", "ssrRouterTitle": "مثال موجه العرض من جانب الخادم (SSR)", "ssrRouterHowWorks": "كيف يعمل توجيه SSR", "ssrRouterDescription": "في العرض من جانب الخادم، كل تغيير للمسار يؤدي إلى طلب جديد للخادم، حيث يتم إنشاء HTML لهذا المسار. هذا يعني أن كل صفحة يتم عرضها حديثًا على الخادم بأحدث البيانات.", "ssrRouterNote": "هذا يعطيك دائمًا أحدث المحتوى، لكنك ستلاحظ إعادة تحميل كاملة للصفحة أثناء التنقل. يتم الحفاظ على إعدادات السمة واللغة بشكل طبيعي أثناء التنقل نظرًا لعدم إعادة تحميل الصفحة بالكامل.", "csrTitle": "عرض من جانب العميل (CSR)", "csrHowWorks": "كيف يعمل CSR", "csrDescription": "في العرض من جانب العميل، يرسل الخادم صفحة HTML الحد الأدنى، ثم يقوم JavaScript ببناء الصفحة في المتصفح. يمكنك رؤية هذا في العمل حيث يبدأ بمحتوى الحد الأدنى ثم 'يترطب' بواجهة مستخدم كاملة بمجرد تحميل JavaScript.", "csrNote": "CSR مناسب للصفحات التفاعلية للغاية حيث يكون معظم المحتوى خاصًا بالمستخدم وحيث لا يكون تحسين محركات البحث (SEO) مهمًا جدًا.", "clientState": "حالة جانب العميل", "clientStateDescription": "تتوفر هذه القيم فقط بعد تنفيذ JavaScript في المتصفح:", "clientStateLoading": "جاري تحميل بيانات جانب العميل...", "clientTime": "وقت المتصفح", "windowSize": "حجم النافذة", "refreshNote": "حدّث الصفحة لرؤية كيف يتسبب CSR في وميض للمحتوى بدون نمط/الحد الأدنى.", "csrWithData": "CSR مع جلب البيانات", "csrDataFetch": "جلب البيانات من جانب العميل", "csrDataFetchDescription": "مع CSR، يتم جلب البيانات بواسطة المتصفح بعد تحميل JavaScript. هذا يعني أن هناك دائمًا تأخيرًا قبل أن تظهر البيانات.", "fetchedData": "البيانات التي تم جلبها", "loadingData": "جاري تحميل البيانات...", "dataMessage": "الرسالة", "dataTimestamp": "الطابع الزمني", "csrAdvanced": "تقنيات CSR متقدمة", "csrAdvancedTips": "تحسين تطبيقات CSR", "csrAdvancedDescription": "تستخدم تطبيقات CSR المتقدمة تقنيات مثل تقسيم الكود والتحميل المؤجل والتخزين المؤقت من جانب العميل لتحسين الأداء.", "csrBestPractices": "أفضل الممارسات", "csrBestPractice1": "استخدم حالات التحميل للإشارة إلى أن المحتوى قيد الجلب", "csrBestPractice2": "نفذ حدود الخطأ للتعامل مع الأخطاء بشكل مناسب", "csrBestPractice3": "فكر في استخدام عمال الخدمة للوظائف غير المتصلة", "csrBestPractice4": "التحميل المسبق للبيانات المهمة عندما يكون ذلك ممكنًا", "csrRouterTitle": "كيف يعمل توجيه CSR", "csrRouterDescription": "في العرض من جانب العميل، تكون HTML الأولية الحد الأدنى، ويقوم JavaScript ببناء الصفحة في المتصفح. التنقل بين المسارات يحدث بالكامل في المتصفح، بدون إعادة تحميل كاملة للصفحة.", "csrRouterNote": "يوفر هذا النهج تجربة سلسة تشبه التطبيق حيث يتم الحفاظ على الحالة بين تغييرات المسار. يتم الحفاظ على إعدادات السمة واللغة بشكل طبيعي أثناء التنقل نظرًا لعدم إعادة تحميل الصفحة بالكامل.", "spaTitle": "تطبيق الصفحة الواحدة (SPA)", "spaHowWorks": "كيف يعمل SPA", "spaDescription": "تطبيق الصفحة الواحدة يحمل صفحة HTML واحدة ثم يحدث المحتوى ديناميكيًا مع تفاعل المستخدم مع التطبيق. التنقل بين 'الصفحات' يحدث بالكامل من جانب العميل، بدون تحديث المتصفح.", "spaNote": "توفر تطبيقات SPA تجربة مستخدم أكثر سلاسة لأنها لا تتطلب إعادة تحميل الصفحة، مما يسمح بالحفاظ على حالة التطبيق والتفاعلات الأقرب إلى التطبيق. تتم إدارة عنوان URL الحالي باستخدام واجهة برمجة التطبيقات للسجل في المتصفح.", "currentUrl": "عنوان URL الحالي", "loadedAt": "تم التحميل في", "spaFeatures": "الميزات الرئيسية لـ SPA", "spaFeature1": "توجيه من جانب العميل بدون إعادة تحميل الصفحة", "spaFeature2": "استمرارية حالة التطبيق بين انتقالات 'الصفحة'", "spaFeature3": "القدرة على العمل دون اتصال مع عمال الخدمة", "spaFeature4": "انتقالات ورسوم متحركة سلسة بين العروض", "spaRouterTitle": "كيف يعمل توجيه SPA", "spaRouterDescription": "في تطبيق الصفحة الواحدة، يحدث كل التوجيه من جانب العميل بدون تحديث الصفحة. يعترض التطبيق تغييرات عنوان URL ويعرض المحتوى المناسب بدون طلب HTML جديد من الخادم.", "spaRouterNote": "هذا يوفر أكثر تجارب المستخدم سلاسة مع حالة مستمرة وبدون وميض للصفحة. عنوان URL الحالي: "}, "timeConversion": {"title": "تحويل الوقت", "utc": "التوقيت العالمي", "local": "التوقيت المحلي", "timezone": "المنطقة الزمنية", "date": "التاريخ", "time": "الوقت"}, "apiKey": {"name": {"label": "الاسم", "placeholder": "اختياري", "serviceLabel": "اسم مفتاح الخدمة", "servicePlaceholder": "مف<PERSON><PERSON><PERSON> حسا<PERSON> الخدمة الخاص بي", "userKeyPlaceholder": "مف<PERSON><PERSON><PERSON>ي التجريبي"}, "ownerType": {"label": "المالك", "you": "أنت", "serviceAccount": "حساب الخدمة"}, "permissionType": {"label": "الصلاحيات", "all": "الكل", "restricted": "مقيّد", "readOnly": "للقراءة فقط"}, "project": {"label": "المشروع", "placeholder": "اختر مشروعًا"}, "resources": {"title": "الموارد", "permissions": "الصلاحيات", "models": "النماذج", "modelsPath": "/v1/models", "modelCapabilities": "قدرات النموذج", "modelCapabilitiesPath": "/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations", "assistants": "المساعدون", "assistantsPath": "/v1/assistants", "assistantsModelsPath": "/v1/models (مطلوب للمساعدين)", "threads": "المحادثات", "threadsPath": "/v1/threads", "threadsModelsPath": "/v1/models (مطلوب للمحادثات)", "evals": "التقييمات", "evalsPath": "/v1/evals", "fineTuning": "الضبط الدقيق", "fineTuningPath": "/v1/fine_tuning", "files": "الملفات", "filesPath": "/v1/files"}, "resourcePermissions": {"none": "لا شيء", "read": "قراءة", "write": "كتابة"}, "descriptions": {"serviceAccount": "سيتم إضافة عضو بوت جديد (حسا<PERSON> خدمة) إلى مشروعك، وسيتم إنشاء مفتاح API.", "userAccount": "هذا المفتاح مرتبط بحسابك ويمكنه إجراء طلبات على المشروع المحدد. إذا تمت إزالتك من المؤسسة أو المشروع، سيتم تعطيل هذا المفتاح.", "personal": "هذا المفتاح مرتبط بحسابك ويمكنه إجراء طلبات على المشروع المحدد. إذا تمت إزالتك من المؤسسة أو المشروع، سيتم تعطيل هذا المفتاح.", "permissionChange": "قد تستغرق تغييرات الصلاحيات بضع دقائق لتصبح سارية المفعول."}, "actions": {"cancel": "إلغاء", "save": "<PERSON><PERSON><PERSON>", "createKey": "إنشاء مفتاح سري", "create": "إنشاء مفتاح سري", "submitting": "جاري الإرسال..."}, "titles": {"create": "إنشاء مفتاح سري جديد", "edit": "تعديل المفتاح السري"}, "validation": {"required": "هذا الحقل مطلوب", "name": "يج<PERSON> أن يحتوي الاسم على أحرف وأرقام وشرطات وشرطات سفلية فقط"}}, "tableDynamic": {"empty": {"title": "لا توجد بيانات متاحة", "description": "لا توجد عناصر للعرض."}, "error": {"generic": "حد<PERSON> خطأ أثناء تحميل البيانات."}, "filter": {"searchPlaceholder": "بحث...", "clearSearch": "<PERSON><PERSON><PERSON> البحث", "filters": "الفلاتر", "filterColumns": "تصفية الأعمدة", "clearAllFilters": "م<PERSON><PERSON> ج<PERSON>يع الفلاتر"}, "footer": {"rowsPerPage": "الصفوف في كل صفحة:", "pageInfo": "الصفحة {{current}} من {{total}} ({{from}}-{{to}} من إجمالي {{totalRows}} عنصر)", "firstPage": "الصفحة الأولى", "previousPage": "الصفحة السابقة", "nextPage": "الصفحة التالية", "lastPage": "الصفحة الأخيرة"}}, "batchDetailsCard": {"title": "الدُفعة", "status": {"label": "الحالة", "completed": "مكتمل", "failed": "فشل", "pending": "قيد الانتظار"}}, "rtlDemo": {"title": "عرض توضيحي للكتابة من اليمين إلى اليسار", "currentLanguage": "اللغة الحالية", "direction": "اتجاه النص", "paragraph1": "يوضح هذا المكون كيفية تغيير اتجاه النص بناءً على اللغة المحددة. اللغة العربية هي لغة من اليمين إلى اليسار (RTL).", "paragraph2": "لاحظ كيف تحافظ الأزرار في الأسفل على مواقعها بالنسبة لاتجاه النص. في وضع LTR، يكون 'السابق' على اليسار، بينما في وضع RTL، يظهر على اليمين.", "previousButton": "السابق", "nextButton": "التالي", "formTitle": "عرض عناصر النموذج", "nameLabel": "الاسم", "namePlaceholder": "أ<PERSON><PERSON>ل اسمك", "emailLabel": "الب<PERSON>يد الإلكتروني", "emailPlaceholder": "<EMAIL>", "subscribeLabel": "اشترك في النشرة الإخبارية", "submitButton": "إرسال", "textAlignmentTitle": "عرض محاذاة النص", "textAlignStart": "هذا النص محاذى إلى البداية (اليمين في RTL، اليسار في LTR).", "textAlignEnd": "هذا النص محاذى إلى النهاية (اليسار في RTL، اليمين في LTR).", "textAlignCenter": "هذا النص مركز بغض النظر عن الاتجاه."}, "userTable": {"headers": {"name": "الاسم", "email": "الب<PERSON>يد الإلكتروني", "role": "الدور", "status": "الحالة", "lastLogin": "آخر تسجيل دخول"}, "status": {"active": "نشط", "inactive": "غير نشط", "pending": "قيد الانتظار"}, "empty": {"title": "لم يتم العثور على مستخدمين", "description": "لا يوجد مستخدمين للعرض."}, "error": {"title": "خطأ", "retry": "إعادة المحاولة"}, "footer": {"total": "الإجمالي: {{count}} مستخدم"}}}