import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FormProvider, useForm } from 'react-hook-form';
import { Create, Edit, ApiKeyPermissionSection, PermissionNotification } from '../../index';
import { sampleProjects, sampleApiKeyRestricted } from '../../__fixtures__/ApiKey.fixtures';

// Wrapper component to provide form context for ApiKeyPermissionSection
const PermissionSectionWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      permissionType: 'Restricted' as const,
      resourcePermissions: {
        models: 'Read' as const,
        modelCapabilities: 'Write' as const,
        assistants: 'None' as const,
        threads: 'None' as const,
        evals: 'None' as const,
        fineTuning: 'None' as const,
        files: 'Read' as const,
      },
    },
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
};

// Mock for announcer monitoring
const mockScreenReaderAnnouncements = () => {
  const announcements: string[] = [];
  const originalGetAttribute = Element.prototype.getAttribute;

  // Mock getAttribute to capture aria-live region updates
  Element.prototype.getAttribute = function (name: string) {
    const value = originalGetAttribute.call(this, name);
    if ((name === 'role' && value === 'alert') || name === 'aria-live') {
      const content = this.textContent;
      if (content && !announcements.includes(content)) {
        announcements.push(content);
      }
    }
    return value;
  };

  return {
    getAnnouncements: () => [...announcements],
    reset: () => {
      announcements.length = 0;
    },
    restore: () => {
      Element.prototype.getAttribute = originalGetAttribute;
    },
  };
};

describe('ApiKey Components - Screen Reader Accessibility', () => {
  beforeEach(() => {
    // Create a div to hold live regions for screen reader announcements
    const ariaLive = document.createElement('div');
    ariaLive.id = 'aria-live-announcer';
    ariaLive.setAttribute('aria-live', 'polite');
    ariaLive.style.position = 'absolute';
    ariaLive.style.width = '1px';
    ariaLive.style.height = '1px';
    ariaLive.style.overflow = 'hidden';
    document.body.appendChild(ariaLive);
  });

  afterEach(() => {
    // Remove the live region after each test
    const announcer = document.getElementById('aria-live-announcer');
    if (announcer) {
      document.body.removeChild(announcer);
    }
  });

  it('should announce form validation errors to screen readers', async () => {
    const user = userEvent.setup();

    // Create the form with pre-set validation error
    render(
      <Create
        onSubmit={() => {}}
        onCancel={() => {}}
        availableProjects={sampleProjects}
        validateForm={() => ({ name: 'This field is required' })}
      />,
    );

    // Find and click the submit button to trigger validation
    const submitButton = screen.getByRole('button', { name: /Create/i });
    await user.click(submitButton);

    // Error should be displayed with proper role="alert"
    const errorMessage = await screen.findByText('This field is required');
    expect(errorMessage).toBeInTheDocument();
    expect(errorMessage).toHaveAttribute('role', 'alert');

    // Check that the input has aria-invalid and aria-describedby
    const nameInput = screen.getByLabelText(/Name/i);
    expect(nameInput).toHaveAttribute('aria-invalid', 'true');
    expect(nameInput).toHaveAttribute('aria-describedby');

    // The input's aria-describedby should match the error message id
    const errorId = nameInput.getAttribute('aria-describedby');
    expect(errorMessage.id).toBe(errorId);
  });

  it('should have accessible text alternatives for SVG icons', () => {
    render(<PermissionNotification variant="info" />);

    // All SVG icons should have aria-hidden="true"
    const svgIcons = document.querySelectorAll('svg');
    expect(svgIcons.length).toBeGreaterThan(0);

    svgIcons.forEach((icon) => {
      expect(icon).toHaveAttribute('aria-hidden', 'true');
    });

    // The notification should have a role for screen readers
    const notification = screen.getByText(
      'Permission changes may take a few minutes to take effect.',
    );
    const notificationContainer = notification.closest('[role]');
    expect(notificationContainer).toHaveAttribute('role', 'status');
  });

  it('should provide accessible names for all resource permission buttons', () => {
    render(
      <PermissionSectionWrapper>
        <ApiKeyPermissionSection permissionType="Restricted" />
      </PermissionSectionWrapper>,
    );

    // Find all permission button groups
    const radiogroups = document.querySelectorAll('[role="radiogroup"]');
    expect(radiogroups.length).toBeGreaterThan(0);

    // For each radio group, check that there are accessible radio buttons
    radiogroups.forEach((group) => {
      const radioButtons = group.querySelectorAll('[role="radio"]');
      expect(radioButtons.length).toBe(3); // None, Read, Write

      radioButtons.forEach((button) => {
        // Each button should have aria-checked and aria-label
        expect(button).toHaveAttribute('aria-checked');
        expect(button).toHaveAttribute('aria-label');

        // The aria-label should describe the action
        const ariaLabel = button.getAttribute('aria-label');
        expect(ariaLabel).toMatch(/Set .* permission to (None|Read|Write)/);
      });

      // Group should be labeled by a resource name
      const labelId = group.getAttribute('aria-labelledby');
      if (labelId) {
        const label = document.getElementById(labelId);
        expect(label).not.toBeNull();
        expect(label?.textContent).not.toBe('');
      }
    });
  });

  it('should handle focus management with keyboard navigation', async () => {
    const user = userEvent.setup();

    render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

    // Find and click on the "Restricted" permission type
    const restrictedButton = screen.getByText('Restricted');
    await user.click(restrictedButton);

    // After clicking, permission options should be displayed
    const permissionButtonEls = screen.getAllByRole('radio');
    expect(permissionButtonEls.length).toBeGreaterThan(0);

    // Try to tab to one of the permission buttons
    await user.tab(); // Tab from the Restricted button

    // Check that focus is managed correctly (on a focusable element)
    const activeElement = document.activeElement;
    expect(activeElement).not.toBe(document.body);
    expect(activeElement?.getAttribute('tabindex')).not.toBe('-1');
  });
});

describe('ApiKey Components - Screen Reader Compatibility', () => {
  describe('Create Component Screen Reader Access', () => {
    it('should have properly labeled form controls for screen readers', () => {
      render(<Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />);

      // Check input elements have accessible names
      const nameInput = screen.getByLabelText(/Name/i);
      expect(nameInput).toBeInTheDocument();

      // Check select elements have accessible names
      const projectSelect = screen.getByLabelText(/Project/i);
      expect(projectSelect).toBeInTheDocument();

      // Check that buttons have accessible names
      const buttons = screen.getAllByRole('button');
      buttons.forEach((button) => {
        expect(button).toHaveAccessibleName();
      });
    });

    it('should have grouped content with appropriate ARIA landmarks', () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Form should have proper role
      const form = container.querySelector('form');
      expect(form).toBeInTheDocument();

      // Verify no duplicate landmarks
      const regions = container.querySelectorAll('[role="region"]');
      const mainRegions = Array.from(regions).filter((region) =>
        region.getAttribute('aria-labelledby'),
      );

      // Check for unique IDs on landmarks
      const regionIds = mainRegions.map((region) => region.getAttribute('aria-labelledby'));
      const uniqueIds = new Set(regionIds.filter(Boolean));

      expect(regionIds.length).toBe(uniqueIds.size);
    });

    it('should announce form validation errors to screen readers', () => {
      // Mock error messages for validation
      const errors = {
        name: { message: 'Name is required' },
      };

      // Override useFormContext to provide error state
      jest.mock('react-hook-form', () => ({
        ...jest.requireActual('react-hook-form'),
        useFormContext: () => ({
          control: {},
          formState: { errors },
        }),
      }));

      const { container } = render(
        <div>
          <span id="name-error" role="alert" className="text-destructive text-sm font-medium">
            Name is required
          </span>
        </div>,
      );

      // Error should have proper role for screen readers
      const errorElement = container.querySelector('[role="alert"]');
      expect(errorElement).toBeInTheDocument();
      expect(errorElement).toHaveTextContent('Name is required');
    });
  });

  describe('ApiKeyPermissionSection Screen Reader Access', () => {
    it('should have descriptive labels for permission options', () => {
      render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // Find section heading
      const sectionHeading = screen.getByText('Resources');
      expect(sectionHeading).toBeInTheDocument();

      // Find permission options
      const permissionOptions = screen.getAllByText(/^None$|^Read$|^Write$/);
      expect(permissionOptions.length).toBeGreaterThan(0);

      // Check that they have accessible names (either through text or aria-label)
      permissionOptions.forEach((option) => {
        expect(option).toHaveAccessibleName();
      });
    });

    it('should group related permissions for screen reader navigation', () => {
      render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" groupResources={true} />
        </PermissionSectionWrapper>,
      );

      // Find group headings if supported
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);

      // Groups should be distinguishable in the DOM structure
      const { container } = render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" groupResources={true} />
        </PermissionSectionWrapper>,
      );

      // Look for structural elements that would help screen readers
      const sections = container.querySelectorAll('.space-y-3, .border-b, .mb-3');
      expect(sections.length).toBeGreaterThan(0);
    });
  });

  describe('Notification Screen Reader Announcements', () => {
    it('should provide notifications that are accessible to screen readers', () => {
      render(<PermissionNotification />);

      // Check notification text is present
      const notification = screen.getByText(
        'Permission changes may take a few minutes to take effect.',
      );
      expect(notification).toBeInTheDocument();

      // In a real component, verify it has appropriate ARIA role
      // If it's a notification that should be announced, check for role="alert" or aria-live
      const notificationContainer = notification.closest('div');

      // The notification should have an accessible mechanism (assumed implementation)
      // This is a loose check since we're not seeing the actual component implementation
      expect(
        notificationContainer?.getAttribute('role') === 'alert' ||
          notificationContainer?.getAttribute('aria-live') !== null,
      ).toBeTruthy();
    });
  });

  describe('Edit Component Screen Reader Access', () => {
    it('should communicate the current state of form fields to screen readers', () => {
      render(<Edit apiKey={sampleApiKeyRestricted} onSubmit={() => {}} onCancel={() => {}} />);

      // Current values should be set in form fields
      const nameInput = screen.getByLabelText(/Name/i);
      expect(nameInput).toHaveValue(sampleApiKeyRestricted.name);

      // Selected option should be the current one
      const permissionTypeValue = sampleApiKeyRestricted.permissionType;
      const selectedPermissionButton = screen.getByText(permissionTypeValue);
      expect(selectedPermissionButton).toHaveClass('bg-primary');
    });

    it('should indicate the revoked or expired status to screen readers', () => {
      // Mock a revoked key
      const revokedKey = {
        ...sampleApiKeyRestricted,
        status: 'revoked' as const,
      };

      render(<Edit apiKey={revokedKey} onSubmit={() => {}} onCancel={() => {}} />);

      // Status should be clearly indicated
      // This assumes there's some visual indicator. Let's look for a common pattern:
      const statusElement = screen.queryByText(/revoked/i);

      // If the component shows status, ensure it's accessible
      if (statusElement) {
        // Should be visible or have proper ARIA attributes
        expect(
          statusElement.isConnected || statusElement.closest('[aria-label*="revoked" i]') !== null,
        ).toBeTruthy();
      }
    });
  });

  describe('Live Region Announcements', () => {
    let announcer: ReturnType<typeof mockScreenReaderAnnouncements>;

    beforeEach(() => {
      announcer = mockScreenReaderAnnouncements();
    });

    afterEach(() => {
      announcer.restore();
    });

    it('should announce dynamic content changes', () => {
      const { rerender } = render(<div role="alert">Initial content</div>);

      // After rendering, the initial content should be captured
      expect(announcer.getAnnouncements()).toContain('Initial content');

      // Reset announcements
      announcer.reset();

      // Rerender with updated content
      rerender(<div role="alert">Updated content</div>);

      // Updated content should be announced
      expect(announcer.getAnnouncements()).toContain('Updated content');
    });

    it('should announce form submission status', () => {
      render(
        <div>
          <div role="alert" id="form-status">
            Form submitted successfully
          </div>
        </div>,
      );

      // Success message should be captured
      expect(announcer.getAnnouncements()).toContain('Form submitted successfully');
    });
  });

  describe('ARIA Attributes and States', () => {
    it('should use appropriate ARIA roles', () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Form should have implicit role="form"
      const form = container.querySelector('form');
      expect(form).toBeInTheDocument();

      // Buttons should have role="button"
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
    });

    it('should use correct ARIA states for interactive elements', () => {
      const { container } = render(
        <Create onSubmit={() => {}} onCancel={() => {}} availableProjects={sampleProjects} />,
      );

      // Find disabled elements
      const disabledElements = container.querySelectorAll('[disabled], [aria-disabled="true"]');

      // Each disabled element should have proper ARIA state
      disabledElements.forEach((element) => {
        const isDisabled =
          element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true';
        expect(isDisabled).toBe(true);
      });

      // Required fields should be marked as such
      const requiredFields = container.querySelectorAll('[required], [aria-required="true"]');
      requiredFields.forEach((field) => {
        const isRequired =
          field.hasAttribute('required') || field.getAttribute('aria-required') === 'true';
        expect(isRequired).toBe(true);
      });
    });

    it('should use appropriate ARIA properties for custom controls', () => {
      render(
        <PermissionSectionWrapper>
          <ApiKeyPermissionSection permissionType="Restricted" />
        </PermissionSectionWrapper>,
      );

      // The permission buttons should have appropriate roles
      const permissionButtons = screen.getAllByText(/None|Read|Write/);

      permissionButtons.forEach((button) => {
        // Custom buttons might use ARIA roles and states
        const element = button.closest('[role="button"], button');
        expect(element).not.toBeNull();

        // Pressed state for selected buttons
        if (element?.classList.contains('bg-primary')) {
          expect(
            element.getAttribute('aria-pressed') === 'true' ||
              element.getAttribute('aria-selected') === 'true',
          ).toBeTruthy();
        }
      });
    });
  });
});
