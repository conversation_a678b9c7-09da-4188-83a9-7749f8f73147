// components/product-specifications.tsx
import React from 'react';
import { Power, Sun } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';

export interface ProductSpecificationItem {
  icon?: string;
  label?: string;
  text: string;
}

export interface ProductSpecificationsProps {
  specifications: ProductSpecificationItem[];
  layout?: 'standard' | 'horizontal' | 'mobile' | 'catalog';
  className?: string;
}

export const ProductSpecifications: React.FC<ProductSpecificationsProps> = ({
  specifications,
  layout = 'standard',
  className = '',
}) => {
  if (!specifications || specifications.length === 0) {
    return null;
  }

  // แยกรายการที่มีไอคอนและไม่มีไอคอน
  const iconSpecs = specifications.filter((spec) => !!spec.icon);
  const textSpecs = specifications.filter((spec) => !spec.icon);

  // ฟังก์ชั่นสำหรับแสดงไอคอนเฉพาะตามดีไซน์ในภาพ
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case 'socket':
        return (
          <div className="flex h-8 w-8 items-center justify-center">
            {' '}
            {/* Changed to w-8 h-8 for 32x32 */}
            <Image
              src="/images/G13.png"
              alt="G13 socket"
              width={32}
              height={32}
              className="object-contain"
            />
          </div>
        );
      case 'sun':
      case 'lightbulb':
        return (
          <div className="flex h-8 w-8 items-center justify-center">
            {' '}
            {/* Changed to h-8 w-8 for 32x32px */}
            <Image
              src="/images/SUN.png"
              alt="Sun icon"
              width={32}
              height={32}
              className="h-8 w-8 object-contain" /* Changed to h-8 w-8 for 32x32px */
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`product-specifications ${className}`}>
      {/* แสดงรายการที่มีไอคอนในแนวนอน (flex row) */}
      <div className="mb-4 flex  gap-x-2 gap-y-2">
        {iconSpecs.map((spec, index) => (
          <Badge
            key={`icon-${index}`}
            variant="outline"
            className="flex h-12 items-center gap-2 border-gray-100"
          >
            {spec.icon && <div className="flex-shrink-0">{renderIcon(spec.icon)}</div>}

            <div className="flex items-center">
              {spec.label && (
                <span className="text-body-18-reg mr-1 text-[#787E90]">{spec.label}</span>
              )}
              <span className="text-body-18-med text-title">{spec.text}</span>
            </div>
          </Badge>
        ))}
      </div>

      {/* แสดงรายการที่ไม่มีไอคอนเป็นจุด (bullets) */}
      <ul className="list-none p-0">
        {textSpecs.map((spec, index) => (
          <li key={`text-${index}`} className="flex items-start gap-2">
            <div className="bg-discription-secondary mt-1.5 ml-1 h-1 w-1 flex-shrink-0 rounded-full " />{' '}
            {/* Changed h-1.5 w-1.5 to h-1 w-1 */}
            <span className="text-body-16-reg text-discription-secondary ">{spec.text}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ProductSpecifications;
