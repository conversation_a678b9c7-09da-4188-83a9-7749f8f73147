import{r as e,m as s,z as p,E as f}from"./index-Bwql5Dzz.js";import{R as h,a as w,I as d}from"./index-DX0YxfHa.js";const c=e.forwardRef(({className:a,...t},r)=>e.createElement(h,{className:s("grid gap-2",a),...t,ref:r}));c.displayName="RadioGroup";const o=e.forwardRef(({className:a,...t},r)=>e.createElement(w,{ref:r,className:s("flex items-center justify-center",a),...t},e.createElement("div",{className:s("bg-ui-bg-base shadow-details-contrast-on-bg-interactive h-1.5 w-1.5 rounded-full")})));o.displayName="RadioGroup.Indicator";const l=e.forwardRef(({className:a,...t},r)=>e.createElement(d,{ref:r,className:s("group relative flex h-5 w-5 items-center justify-center outline-none",a),...t},e.createElement("div",{className:s("shadow-borders-base bg-ui-bg-base transition-fg flex h-[14px] w-[14px] items-center justify-center rounded-full","group-hover:group-enabled:group-data-[state=unchecked]:bg-ui-bg-base-hover","group-data-[state=checked]:bg-ui-bg-interactive group-data-[state=checked]:shadow-borders-interactive-with-shadow","group-focus-visible:!shadow-borders-interactive-with-focus","group-disabled:cursor-not-allowed group-disabled:opacity-50")},e.createElement(o,null))));l.displayName="RadioGroup.Item";const n=e.forwardRef(({className:a,id:t,label:r,description:u,...b},g)=>{const m=e.useId();t||(t=m);const i=`${t}-description`;return e.createElement(d,{ref:g,className:s("shadow-borders-base bg-ui-bg-base focus-visible:shadow-borders-interactive-with-focus transition-fg group flex items-start gap-x-2 rounded-lg p-3 outline-none","hover:enabled:bg-ui-bg-base-hover","data-[state=checked]:shadow-borders-interactive-with-shadow","group-disabled:cursor-not-allowed group-disabled:opacity-50",a),...b,id:t,"aria-describedby":i},e.createElement("div",{className:"flex h-5 w-5 items-center justify-center"},e.createElement("div",{className:s("shadow-borders-base bg-ui-bg-base group-data-[state=checked]:bg-ui-bg-interactive group-data-[state=checked]:shadow-borders-interactive-with-shadow transition-fg flex h-3.5 w-3.5 items-center justify-center rounded-full","group-hover:group-enabled:group-data-[state=unchecked]:bg-ui-bg-base-hover")},e.createElement(o,null))),e.createElement("div",{className:"flex flex-col items-start"},e.createElement(p,{htmlFor:t,size:"small",weight:"plus",className:"group-disabled:text-ui-fg-disabled cursor-pointer group-disabled:cursor-not-allowed"},r),e.createElement(f,{className:"txt-small text-ui-fg-subtle group-disabled:text-ui-fg-disabled text-left",id:i},u)))});n.displayName="RadioGroup.ChoiceBox";const E=Object.assign(c,{Item:l,ChoiceBox:n});export{E as R};
