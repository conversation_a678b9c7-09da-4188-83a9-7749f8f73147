import React, { ReactNode } from 'react';
import Carousel from '../Carousel/carousel';
import type {CarouselVariant} from '../Carousel/carousel';
import { cn } from '@/lib/utils';
import Link from 'next/link';

export interface HeroCarouselSlide {
  /**
   * หัวข้อย่อยด้านบน
   */
  subtitle?: string;
  
  /**
   * หัวข้อหลัก
   */
  title: string;
  
  /**
   * คำอธิบายหรือเนื้อหา
   */
  description?: string;
  
  /**
   * รูปภาพพื้นหลัง
   */
  backgroundImage?: string;
  
  /**
   * สีพื้นหลัง (ถ้าไม่มีรูปภาพ)
   */
  backgroundColor?: string;
  
  /**
   * รูปภาพหรือ component แสดงด้านขวา
   */
  image?: string | ReactNode;
  
  /**
   * ปุ่มหลัก
   */
  primaryButton?: {
    text: string;
    href: string;
    icon?: ReactNode;
    onClick?: () => void;
  };
  
  /**
   * ปุ่มรอง
   */
  secondaryButton?: {
    text: string;
    href: string;
    icon?: ReactNode;
    onClick?: () => void;
  };
  
  /**
   * Schema.org structured data สำหรับ SEO
   */
  structuredData?: Record<string, any>;
}

export interface SectionHeroCarouselProps {
  /**
   * ข้อมูล slides ทั้งหมด
   */
  slides: HeroCarouselSlide[];
  
  /**
   * ความสูงของ hero section
   * @default 'md'
   */
  height?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  
  /**
   * การแสดงผลพื้นหลัง
   * @default 'none'
   */
  backgroundStyle?: 'wave' | 'flat' | 'gradient' | 'none';
  
  /**
   * การแสดงผลรูปภาพ
   * @default 'right'
   */
  imagePosition?: 'right' | 'left' | 'background' | 'none';
  
  /**
   * เปิดใช้ autoplay
   * @default true
   */
  autoplay?: boolean;
  
  /**
   * แสดงปุ่มนำทาง
   * @default true
   */
  showNavigation?: boolean;
  
  /**
   * แสดงจุดบอกตำแหน่ง
   * @default true
   */
  showPagination?: boolean;
  
  /**
   * Custom class name
   */
  className?: string;
  
  /**
   * ตำแหน่งของเนื้อหา
   * @default 'center'
   */
  contentPosition?: 'left' | 'center' | 'right';
  
  /**
   * Custom options สำหรับ carousel
   */
  carouselOptions?: Record<string, any>;
  
  /**
   * การแสดงผลบนอุปกรณ์พกพา
   * @default 'stack'
   */
  mobileLayout?: 'stack' | 'compact';
}

/**
 * Hero Section พร้อม Carousel สำหรับหน้าแรกหรือหน้า landing page
 * 
 * ใช้สำหรับการแสดงเนื้อหาสำคัญที่มีหลาย slides
 */
const SectionHeroCarousel: React.FC<SectionHeroCarouselProps> = ({
  slides = [],
  height = 'md',
  backgroundStyle = 'none',
  imagePosition = 'right',
  autoplay = true,
  showNavigation = true,
  showPagination = true,
  className = '',
  contentPosition = 'center',
  carouselOptions = {},
  mobileLayout = 'stack',
}) => {
  // Height mapping
  const heightClasses = {
    sm: 'min-h-[300px] md:min-h-[400px]',
    md: 'min-h-[400px] md:min-h-[500px]',
    lg: 'min-h-[500px] md:min-h-[600px]',
    xl: 'min-h-[600px] md:min-h-[700px]',
    full: 'min-h-screen',
  };
  
  // Content position mapping
  const contentPositionClasses = {
    left: 'justify-start text-left',
    center: 'justify-center text-center',
    right: 'justify-end text-right',
  };
  
  // Background style mapping
  const backgroundStyleClasses = {
    wave: 'bg-accent rounded-b-[50%] md:rounded-b-[30%]',
    flat: 'bg-background',
    gradient: 'bg-gradient-to-r from-primary to-secondary',
    none: 'bg-background dark:bg-background',
  };
  
  // Render individual slide
  const renderSlide = (slide: HeroCarouselSlide, index: number) => {
    return (
        <section
        key={index}
        className={cn(
          'hero-slide w-full relative overflow-hidden',
          heightClasses[height],
          (!slide.backgroundColor && !slide.backgroundImage) ? 'bg-background dark:bg-background' : ''
        )}
        style={{
          backgroundImage: slide.backgroundImage ? `url(${slide.backgroundImage})` : undefined,
          backgroundColor: slide.backgroundColor || undefined,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        {/* Background style if not using custom background */}
        {backgroundStyle !== 'none' && !slide.backgroundImage && !slide.backgroundColor && (
          <div className={cn(
            'absolute inset-0 w-full h-full',
            backgroundStyleClasses[backgroundStyle]
          )} />
        )}
        
        {/* Overlay for background image */}
        {slide.backgroundImage && (
          <div className="absolute inset-0 bg-black/30 dark:bg-black/50" aria-hidden="true"></div>
        )}
        
        <div className={cn(
          'container mx-auto px-4 h-full relative z-10',
          mobileLayout === 'stack' ? 'flex flex-col md:flex-row items-center' : 'flex flex-row items-center'
        )}>
          {/* Content Section */}
          <div className={cn(
            'flex flex-col w-full md:w-1/2 py-8 md:py-0',
            contentPositionClasses[contentPosition],
            imagePosition === 'left' ? 'md:order-2' : 'md:order-1'
          )}>
            {slide.subtitle && (
              <div className="text-sm md:text-base font-medium mb-2 text-foreground/80 dark:text-foreground/80">
                {slide.subtitle}
              </div>
            )}
            
            <h1 className="text-3xl md:text-5xl font-bold mb-4 text-foreground dark:text-foreground">
              {slide.title}
            </h1>
            
            {slide.description && (
              <div className="text-sm md:text-base mb-6 text-foreground/90 dark:text-foreground/90">
                {slide.description}
              </div>
            )}
            
            <div className="flex flex-wrap gap-4 mt-2">
              {slide.primaryButton && (
                <Link
                  href={slide.primaryButton.href}
                  className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground font-medium rounded-md hover:bg-primary/90 transition-colors"
                  onClick={slide.primaryButton.onClick}
                >
                  {slide.primaryButton.icon && (
                    <span className="mr-2">{slide.primaryButton.icon}</span>
                  )}
                  {slide.primaryButton.text}
                </Link>
              )}
              
              {slide.secondaryButton && (
                <Link
                  href={slide.secondaryButton.href}
                  className="inline-flex items-center px-6 py-3 bg-transparent border border-foreground text-foreground dark:border-foreground dark:text-foreground font-medium rounded-md hover:bg-foreground/10 transition-colors"
                  onClick={slide.secondaryButton.onClick}
                >
                  {slide.secondaryButton.icon && (
                    <span className="mr-2">{slide.secondaryButton.icon}</span>
                  )}
                  {slide.secondaryButton.text}
                </Link>
              )}
            </div>
          </div>
          
          {/* Image Section */}
          {imagePosition !== 'none' && slide.image && (
            <div className={cn(
              'w-full md:w-1/2 flex justify-center items-center mt-6 md:mt-0',
              imagePosition === 'left' ? 'md:order-1' : 'md:order-2'
            )}>
              {typeof slide.image === 'string' ? (
                <img
                  src={slide.image}
                  alt={`${slide.title} image`}
                  className="max-w-full max-h-full object-contain"
                />
              ) : (
                slide.image
              )}
            </div>
          )}
        </div>
        
        {/* Schema.org Structured Data for SEO */}
        {slide.structuredData && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(slide.structuredData)
            }}
          />
        )}
      </section>
    );
  };
  
  // Carousel options
  const carouselProps = {
    variant: 'default' as CarouselVariant,
    items: slides,
    renderItem: renderSlide,
    autoplay,
    autoplayDelay: 5000, // 5 seconds
    navigation: showNavigation,
    pagination: showPagination,
    className: 'hero-carousel',
    customOptions: {
      effect: 'fade',
      fadeEffect: {
        crossFade: true
      },
      ...carouselOptions
    }
  };
  
  return (
    <div className={cn('section-hero-carousel relative', className)}>
      <Carousel {...carouselProps} />
      
      {/* Global Schema.org structured data for all slides */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ItemList",
            "itemListElement": slides.map((slide, index) => ({
              "@type": "ListItem",
              "position": index + 1,
              "name": slide.title,
              "description": slide.description
            }))
          })
        }}
      />
    </div>
  );
};

export default SectionHeroCarousel;