import{r as l}from"./index-Bwql5Dzz.js";var m=Object.defineProperty,n=Object.getOwnPropertySymbols,f=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,o=(r,t,e)=>t in r?m(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,s=(r,t)=>{for(var e in t)f.call(t,e)&&o(r,e,t[e]);if(n)for(var e of n(t))i.call(t,e)&&o(r,e,t[e]);return r},c=(r,t)=>{var e={};for(var a in r)f.call(r,a)&&t.indexOf(a)<0&&(e[a]=r[a]);if(r!=null&&n)for(var a of n(r))t.indexOf(a)<0&&i.call(r,a)&&(e[a]=r[a]);return e};const d=l.forwardRef((r,t)=>{var e=r,{color:a="currentColor"}=e,p=c(e,["color"]);return l.createElement("svg",s({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),l.createElement("g",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},l.createElement("path",{d:"M3.275 5.225 5.55 7.5 3.275 9.775 1 7.5zM7.5 1l2.275 2.275L7.5 5.55 5.225 3.275zM11.725 5.225 14 7.5l-2.275 2.275L9.45 7.5zM7.5 9.45l2.275 2.275L7.5 14l-2.275-2.275z"})),l.createElement("defs",null,l.createElement("clipPath",{id:"a"},l.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});d.displayName="Component";export{d as C};
