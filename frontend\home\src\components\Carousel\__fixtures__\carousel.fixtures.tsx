/**
 * Fixtures for Carousel component testing
 */

// Sample slides for basic testing
export const sampleSlides = [
    {
      id: 1,
      title: 'Slide 1',
      description: 'This is the first slide',
      image: 'https://via.placeholder.com/600x400/3b82f6/ffffff?text=Slide+1',
    },
    {
      id: 2,
      title: 'Slide 2',
      description: 'This is the second slide',
      image: 'https://via.placeholder.com/600x400/22c55e/ffffff?text=Slide+2',
    },
    {
      id: 3,
      title: 'Slide 3',
      description: 'This is the third slide',
      image: 'https://via.placeholder.com/600x400/ef4444/ffffff?text=Slide+3',
    },
  ];
  
  // Sample slides for promotional banners that match the Figma "Brand Slide" design
  export const promotionalSlides = [
    {
      id: 'backup-power',
      title: 'BACKUP POWER SET',
      image: '/images/promotions/backup-power.jpg',
      discount: '599',
      description: 'อุปกรณ์สำรองไฟฟ้าไว้ใช้ในยามฉุกเฉิน',
    },
    {
      id: 'safety-equipment',
      title: 'SAFETY EQUIPMENT',
      image: '/images/promotions/safety-equipment.jpg',
      discount: '40%',
      description: 'อุปกรณ์ความปลอดภัย',
    },
    {
      id: 'lighting-bonus',
      title: 'Lighting Big Bonus 2023',
      image: '/images/promotions/lighting-bonus.jpg',
      discount: '',
      description: '',
    },
    {
      id: 'new-arrival',
      title: 'New Arrival',
      image: '/images/promotions/new-arrival.jpg',
      discount: '',
      description: 'เทคโนโลยีล่าสุดทุกรุ่นพลัง',
    },
    {
      id: 'solar-panel',
      title: 'Solar Panel',
      image: '/images/promotions/solar-panel.jpg',
      discount: '2,999',
      description: '',
    },
  ];
  
  // Sample data for multiple slides layout
  export const productSlides = [
    {
      id: 'product-1',
      title: 'LED Downlight',
      image: '/images/products/led-downlight.jpg',
      price: '฿350',
      rating: 4.5,
    },
    {
      id: 'product-2',
      title: 'Power Strip',
      image: '/images/products/power-strip.jpg',
      price: '฿450',
      rating: 4.8,
    },
    {
      id: 'product-3',
      title: 'Circuit Breaker',
      image: '/images/products/circuit-breaker.jpg',
      price: '฿650',
      rating: 4.2,
    },
    {
      id: 'product-4',
      title: 'LED Bulb',
      image: '/images/products/led-bulb.jpg',
      price: '฿120',
      rating: 4.7,
    },
    {
      id: 'product-5',
      title: 'Extension Cord',
      image: '/images/products/extension-cord.jpg',
      price: '฿280',
      rating: 4.3,
    },
    {
      id: 'product-6',
      title: 'LED Tube Light',
      image: '/images/products/led-tube.jpg',
      price: '฿420',
      rating: 4.6,
    },
  ];
  
  // Render functions for different slide styles
  export const renderFunctions = {
    // Basic slide renderer
    basic: (item: any) => (
      <div className="relative w-full overflow-hidden rounded-lg">
        <img 
          src={item.image} 
          alt={item.title} 
          className="w-full h-64 object-cover"
        />
        <div className="absolute inset-0 flex flex-col justify-end p-6 bg-gradient-to-t from-black/70 to-transparent">
          <h3 className="text-xl font-bold text-white">{item.title}</h3>
          {item.description && (
            <p className="mt-2 text-white/90">{item.description}</p>
          )}
        </div>
      </div>
    ),
    
    // Product card renderer
    product: (item: any) => (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <img 
          src={item.image} 
          alt={item.title} 
          className="w-full h-48 object-cover rounded-md"
        />
        <h3 className="mt-2 text-lg font-medium">{item.title}</h3>
        <div className="flex justify-between items-center mt-1">
          <span className="text-blue-600 font-bold">{item.price}</span>
          <span className="text-yellow-500">{'★'.repeat(Math.floor(item.rating))} {item.rating}</span>
        </div>
      </div>
    ),
    
    // Promotional banner renderer (matches Figma "Brand Slide" design)
    promotional: (item: any) => (
      <div className="promotional-slide relative overflow-hidden rounded-lg">
        <img 
          src={item.image} 
          alt={item.title} 
          className="w-full h-auto"
        />
        {(item.discount || item.title) && (
          <div className="absolute left-0 bottom-0 p-4 w-full">
            {item.discount && (
              <div className="discount-badge bg-yellow-400 text-black font-bold rounded-full px-4 py-2 inline-block mb-2">
                {typeof item.discount === 'string' && item.discount.includes('%') 
                  ? `ลดสูงสุด ${item.discount}` 
                  : `฿${item.discount}`}
              </div>
            )}
            {item.title && (
              <h3 className="text-white text-xl font-bold">{item.title}</h3>
            )}
            {item.description && (
              <p className="text-white/90 mt-1">{item.description}</p>
            )}
          </div>
        )}
      </div>
    ),
  };