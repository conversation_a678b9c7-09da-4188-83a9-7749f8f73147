import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import Link from 'next/link';

// Define types for our data
interface User {
  id: number;
  name: string;
  role: string;
  lastActive: string;
}

interface SystemStatus {
  cpu: string;
  memory: string;
  uptime: string;
}

interface DetailedData {
  users: User[];
  systemStatus: SystemStatus;
  timestamp: string;
}

// This function will be executed at build time and periodically afterward
async function getDetailedRevalidatedData(): Promise<DetailedData> {
  // Artificial delay to simulate data fetching
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    users: [
      { id: 1, name: '<PERSON>', role: 'Developer', lastActive: new Date().toISOString() },
      { id: 2, name: '<PERSON>', role: 'Designer', lastActive: new Date().toISOString() },
      {
        id: 3,
        name: '<PERSON>',
        role: 'Product Manager',
        lastActive: new Date().toISOString(),
      },
    ],
    systemStatus: {
      cpu: Math.floor(Math.random() * 100) + '%',
      memory: Math.floor(Math.random() * 100) + '%',
      uptime: Math.floor(Math.random() * 10000) + ' minutes',
    },
    timestamp: new Date().toISOString(),
  };
}

// Set the revalidation period to 30 seconds
export const revalidate = 30;

export default async function ISRWithDataPage() {
  // Data is refreshed at most every 30 seconds
  const data = await getDetailedRevalidatedData();

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="isr" />
      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">ISR with Detailed Data</h1>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">Incremental Static Regeneration with Data</h2>
          <p className="mb-4">
            This example demonstrates using ISR to periodically refresh data without rebuilding the
            entire page. The data below was generated at build time or during the most recent
            revalidation (every 30 seconds).
          </p>
          <p>
            Unlike client-side rendering, there&apos;s no loading state visible to the user - the
            page shows the latest pre-rendered content immediately.
          </p>
        </div>

        <div className="bg-muted mb-6 rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">User Data</h2>
          <p className="mb-4">
            The table below shows user data that was generated at build time or during revalidation:
          </p>

          <div className="overflow-x-auto">
            <table className="bg-card w-full overflow-hidden rounded-md border">
              <thead className="bg-muted">
                <tr>
                  <th className="px-4 py-2 text-left">ID</th>
                  <th className="px-4 py-2 text-left">Name</th>
                  <th className="px-4 py-2 text-left">Role</th>
                  <th className="px-4 py-2 text-left">Last Active</th>
                </tr>
              </thead>
              <tbody>
                {data.users.map((user) => (
                  <tr key={user.id} className="border-t">
                    <td className="px-4 py-2">{user.id}</td>
                    <td className="px-4 py-2">{user.name}</td>
                    <td className="px-4 py-2">{user.role}</td>
                    <td className="px-4 py-2">{new Date(user.lastActive).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">System Status</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">CPU Usage</h3>
              <p className="text-2xl">{data.systemStatus.cpu}</p>
            </div>
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">Memory Usage</h3>
              <p className="text-2xl">{data.systemStatus.memory}</p>
            </div>
            <div className="bg-card rounded border p-4">
              <h3 className="mb-2 font-medium">Uptime</h3>
              <p className="text-2xl">{data.systemStatus.uptime}</p>
            </div>
          </div>
          <p className="mt-4 text-xs">Generated at: {data.timestamp}</p>
        </div>

        <div className="bg-muted rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">ISR Benefits</h2>
          <ul className="list-disc space-y-2 pl-5">
            <li>Improved performance: Pages are pre-rendered and served from the CDN</li>
            <li>
              Reduced server load: Pages are only revalidated periodically, not on every request
            </li>
            <li>SEO-friendly: Search engines see fully rendered content</li>
            <li>Data freshness: Content updates periodically without manual rebuilds</li>
            <li>
              Fast page loads: Users always see a complete page immediately, no loading states
            </li>
          </ul>
        </div>

        <div className="mt-8 flex justify-center">
          <Link href="/rendering-examples/isr" className="text-primary font-medium hover:underline">
            ← Back to Basic ISR Example
          </Link>
        </div>
      </main>
    </div>
  );
}
