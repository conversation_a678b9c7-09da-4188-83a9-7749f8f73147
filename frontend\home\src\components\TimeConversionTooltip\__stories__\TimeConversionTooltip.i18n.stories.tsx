import type { Meta, StoryObj } from '@storybook/react';
import { TimeConversionTooltip } from '../TimeConversionTooltip';
import {
  defaultTooltipData,
  i18nTooltipData,
} from '../__fixtures__/TimeConversionTooltip.fixtures';
import { formatDateConversion } from '../utils';

/**
 * Internationalization showcase for the TimeConversionTooltip component
 */
const meta = {
  title: 'Components/TimeConversionTooltip/Internationalization',
  component: TimeConversionTooltip,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        story: 'Internationalization features of the TimeConversionTooltip component.',
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof TimeConversionTooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * English language version
 */
export const English: Story = {
  args: {
    data: i18nTooltipData.en,
    children: 'March 15, 2023',
    language: 'en',
  },
  parameters: {
    locale: 'en',
  },
};

/**
 * French language version
 */
export const French: Story = {
  args: {
    data: i18nTooltipData.fr,
    children: '15 mars 2023',
    language: 'fr',
  },
  parameters: {
    locale: 'fr',
  },
};

/**
 * Japanese language version
 */
export const Japanese: Story = {
  args: {
    data: i18nTooltipData.ja,
    children: '2023年3月15日',
    language: 'ja',
  },
  parameters: {
    locale: 'ja',
  },
};

/**
 * Using i18n render function for children
 */
export const TranslationFunction: Story = {
  args: {
    data: defaultTooltipData,
    i18nNamespace: 'common',
    i18nPrefix: 'timeConversion',
    children: (t) => t('batchDetailsCard.createdAt.label'),
  },
  parameters: {
    docs: {
      description: {
        story:
          'Demonstrates passing a translation function as children. Notice how the translation key is properly resolved to "Created at" from the translation files.',
      },
    },
    locale: 'en',
  },
};

/**
 * Comparison of all languages
 */
export const AllLanguages: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Language comparison',
  },
  render: () => (
    <div className="space-y-8">
      <div className="grid grid-cols-1 gap-4">
        <h3 className="text-lg font-semibold">English</h3>
        <TimeConversionTooltip
          data={i18nTooltipData.en}
          language="en"
          i18nPrefix="batchDetailsCard.tooltip"
        >
          March 15, 2023
        </TimeConversionTooltip>

        <h3 className="mt-4 text-lg font-semibold">French</h3>
        <TimeConversionTooltip
          data={i18nTooltipData.fr}
          language="fr"
          i18nPrefix="batchDetailsCard.tooltip"
        >
          15 mars 2023
        </TimeConversionTooltip>

        <h3 className="mt-4 text-lg font-semibold">Japanese</h3>
        <TimeConversionTooltip
          data={i18nTooltipData.ja}
          language="ja"
          i18nPrefix="batchDetailsCard.tooltip"
        >
          2023年3月15日
        </TimeConversionTooltip>
      </div>
    </div>
  ),
};

/**
 * Using the formatDateConversion utility with different languages
 */
export const DateFormattingWithLocale: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Locale-based formatting',
  },
  render: () => {
    // Create a sample timestamp
    const timestamp = '2023-03-15T16:30:00Z';

    // Format with different locales
    const enData = formatDateConversion(timestamp, 'en');
    const frData = formatDateConversion(timestamp, 'fr');
    const jaData = formatDateConversion(timestamp, 'ja');

    return (
      <div className="space-y-8">
        <h3 className="text-base font-medium">Same timestamp, different locales:</h3>
        <p className="text-muted-foreground text-sm">{timestamp}</p>

        <div className="grid grid-cols-1 gap-4">
          <div>
            <h4 className="text-sm font-semibold">English (en)</h4>
            <TimeConversionTooltip data={enData} language="en">
              English Formatting
            </TimeConversionTooltip>
          </div>

          <div>
            <h4 className="text-sm font-semibold">French (fr)</h4>
            <TimeConversionTooltip data={frData} language="fr">
              French Formatting
            </TimeConversionTooltip>
          </div>

          <div>
            <h4 className="text-sm font-semibold">Japanese (ja)</h4>
            <TimeConversionTooltip data={jaData} language="ja">
              Japanese Formatting
            </TimeConversionTooltip>
          </div>
        </div>
      </div>
    );
  },
};

/**
 * Multiple translation function examples
 */
export const MultipleTranslationExamples: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Placeholder text (overridden by render function)',
  },
  render: () => (
    <div className="space-y-8">
      <div className="mb-8 grid grid-cols-1 gap-6">
        <h3 className="text-base font-semibold">Translation Function Examples</h3>

        <div className="rounded-md border p-4">
          <h4 className="mb-3 text-sm font-medium">Default namespace (common)</h4>
          <TimeConversionTooltip data={defaultTooltipData}>
            {(t) => t('batchDetailsCard.createdAt.label')}
          </TimeConversionTooltip>
        </div>

        <div className="rounded-md border p-4">
          <h4 className="mb-3 text-sm font-medium">With tooltip translation</h4>
          <TimeConversionTooltip data={defaultTooltipData} i18nPrefix="batchDetailsCard.tooltip">
            {(t) => t('batchDetailsCard.createdAt.label')}
          </TimeConversionTooltip>
        </div>

        <div className="rounded-md border p-4">
          <h4 className="mb-3 text-sm font-medium">With French locale</h4>
          <TimeConversionTooltip data={i18nTooltipData.fr} language="fr">
            {(t) => t('batchDetailsCard.createdAt.label')}
          </TimeConversionTooltip>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Multiple examples of using translation functions with different configurations.',
      },
    },
  },
};

/**
 * Deeply nested translation keys
 */
export const NestedTranslationKeys: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Placeholder text (overridden by render function)',
  },
  render: () => (
    <div className="space-y-4">
      <h3 className="text-base font-medium">Nested translation key examples:</h3>

      <div className="grid grid-cols-1 gap-4 rounded-md border p-4">
        <div>
          <p className="text-muted-foreground mb-1 text-sm">Simple key:</p>
          <TimeConversionTooltip data={defaultTooltipData}>
            {(t) => t('buttons.submit')}
          </TimeConversionTooltip>
        </div>

        <div>
          <p className="text-muted-foreground mb-1 text-sm">One level deep:</p>
          <TimeConversionTooltip data={defaultTooltipData}>
            {(t) => t('batchDetailsCard.title')}
          </TimeConversionTooltip>
        </div>

        <div>
          <p className="text-muted-foreground mb-1 text-sm">Two levels deep:</p>
          <TimeConversionTooltip data={defaultTooltipData}>
            {(t) => t('batchDetailsCard.status.completed')}
          </TimeConversionTooltip>
        </div>

        <div>
          <p className="text-muted-foreground mb-1 text-sm">With different languages:</p>
          <div className="flex space-x-4">
            <TimeConversionTooltip data={defaultTooltipData} language="en">
              {(t) => t('batchDetailsCard.files.title')}
            </TimeConversionTooltip>

            <TimeConversionTooltip data={defaultTooltipData} language="fr">
              {(t) => t('batchDetailsCard.files.title')}
            </TimeConversionTooltip>

            <TimeConversionTooltip data={defaultTooltipData} language="ja">
              {(t) => t('batchDetailsCard.files.title')}
            </TimeConversionTooltip>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          'Demonstrates that translation functions properly handle deeply nested translation keys from the i18n files.',
      },
    },
  },
};
