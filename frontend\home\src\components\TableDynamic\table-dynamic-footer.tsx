'use client';

import * as React from 'react';
import { useCallback } from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useLanguageChange } from '@/hooks/useLanguageChange';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTableContext } from './table-dynamic-context';
import { FOOTER_HEIGHTS } from './constants';
import type { ColumnMeta } from './types';

/**
 * TableDynamicFooter props
 */
export interface TableDynamicFooterProps {
  /** Whether the footer is sticky */
  sticky?: boolean;
  /** Whether to show column separators */
  showColumnSeparators?: boolean;
  /** Custom footer content */
  footerContent?: React.ReactNode;
  /** Whether to show the summary row */
  showSummary?: boolean;
  /** Whether to show pagination controls */
  showPagination?: boolean;
  /** Custom footer height */
  height?: number | string;
  /** Table size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Translation namespace */
  i18nNamespace?: string;
  /** Translation prefix for keys */
  i18nPrefix?: string;
}

/**
 * TableDynamicFooter component
 */
export const TableDynamicFooter = React.forwardRef<
  HTMLTableSectionElement,
  TableDynamicFooterProps
>(
  (
    {
      sticky = false,
      showColumnSeparators = true,
      footerContent,
      showSummary = false,
      showPagination = true,
      height,
      size = 'md',
      i18nNamespace,
      i18nPrefix = 'tableDynamic.footer',
    },
    ref,
  ) => {
    const { t } = useTranslation(i18nNamespace);
    const currentLang = useLanguageChange();

    const { table } = useTableContext();

    const { pageSize, pageIndex } = table.getState().pagination;
    const totalPages = table.getPageCount();

    const handlePageSizeChange = useCallback(
      (value: string) => {
        table.setPageSize(Number(value));
      },
      [table],
    );

    const isPrevDisabled = !table.getCanPreviousPage();
    const isNextDisabled = !table.getCanNextPage();

    const renderPaginationControls = () => {
      if (!showPagination) return null;

      return (
        <div className="flex items-center justify-between px-4 py-2">
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground text-sm">{t(`${i18nPrefix}.rowsPerPage`)}</span>
            <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
              <SelectTrigger className="h-8 w-16">
                <SelectValue placeholder={pageSize} />
              </SelectTrigger>
              <SelectContent>
                {[10, 20, 30, 50, 100].map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center">
            <div className="text-muted-foreground mr-4 text-sm">
              {t(`${i18nPrefix}.pageInfo`, {
                current: pageIndex + 1,
                total: totalPages || 1,
                from: totalPages ? pageIndex * pageSize + 1 : 0,
                to: Math.min((pageIndex + 1) * pageSize, table.getFilteredRowModel().rows.length),
                totalRows: table.getFilteredRowModel().rows.length,
              })}
            </div>

            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => table.setPageIndex(0)}
                disabled={isPrevDisabled}
              >
                <ChevronsLeft className="h-4 w-4" />
                <span className="sr-only">{t(`${i18nPrefix}.firstPage`)}</span>
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => table.previousPage()}
                disabled={isPrevDisabled}
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="sr-only">{t(`${i18nPrefix}.previousPage`)}</span>
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => table.nextPage()}
                disabled={isNextDisabled}
              >
                <ChevronRight className="h-4 w-4" />
                <span className="sr-only">{t(`${i18nPrefix}.nextPage`)}</span>
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => table.setPageIndex(totalPages - 1)}
                disabled={isNextDisabled}
              >
                <ChevronsRight className="h-4 w-4" />
                <span className="sr-only">{t(`${i18nPrefix}.lastPage`)}</span>
              </Button>
            </div>
          </div>
        </div>
      );
    };

    const renderSummaryRow = () => {
      if (!showSummary) return null;

      return (
        <tr className="bg-muted/50 font-medium">
          {table.getAllColumns().map((column) => {
            const meta = column.columnDef.meta as ColumnMeta;

            // Check if column has an aggregation function
            const hasAggregation = meta?.aggregationFn && meta?.aggregationFormatter;

            return (
              <td
                key={column.id}
                className={cn(
                  'p-2',
                  showColumnSeparators && 'border-border border-r last:border-r-0',
                  meta?.cellClassName,
                )}
              >
                {hasAggregation && meta.aggregationFn && meta.aggregationFormatter
                  ? meta.aggregationFormatter(
                      meta.aggregationFn(
                        table.getRowModel().rows.map((row) => row.getValue(column.id)),
                      ),
                    )
                  : null}
              </td>
            );
          })}
        </tr>
      );
    };

    return (
      <tfoot
        ref={ref}
        className={cn('border-border bg-muted/10 border-t', sticky && 'sticky bottom-0 z-10')}
        style={{ height: height || FOOTER_HEIGHTS[size] }}
      >
        {showSummary && renderSummaryRow()}

        <tr>
          <td colSpan={table.getAllColumns().length}>
            {footerContent || renderPaginationControls()}
          </td>
        </tr>

        <tr className="hidden">
          <td className="text-muted-foreground text-xs">
            {t('language.current')}: {currentLang}
          </td>
        </tr>
      </tfoot>
    );
  },
);

TableDynamicFooter.displayName = 'TableDynamicFooter';
