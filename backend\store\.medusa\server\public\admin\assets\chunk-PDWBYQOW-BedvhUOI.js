import{c as s}from"./chunk-MWVM4TYO-bKUcYSnf.js";var i=r=>{var t;return((t=s[r.toUpperCase()])==null?void 0:t.decimal_digits)??0},u=(r,t)=>new Intl.NumberFormat([],{style:"currency",currencyDisplay:"narrowSymbol",currency:t}).format(r),m=r=>new Intl.NumberFormat([],{style:"currency",currency:r,currencyDisplay:"narrowSymbol"}).format(0).replace(/\d/g,"").replace(/[.,]/g,"").trim(),g=(r,t)=>{const e=m(t),a=i(t),n=c(r,t),o=r.toLocaleString(void 0,{minimumFractionDigits:a,maximumFractionDigits:a,signDisplay:n?"exceptZero":"auto"});return`${e} ${o} ${t.toUpperCase()}`},c=(r,t)=>{const e=i(t);return Math.abs(r)<1/10**e/2};export{g as a,u as g,c as i};
