import{b as r,j as e,aX as n,T as t,B as i,L as c}from"./index-Bwql5Dzz.js";var x=()=>{const{t:s}=r(),a=s("errorBoundary.notFoundTitle"),l=s("errorBoundary.noMatchMessage");return e.jsx("div",{className:"flex size-full min-h-screen items-center justify-center",children:e.jsxs("div",{className:"flex flex-col items-center gap-y-6",children:[e.jsxs("div",{className:"text-ui-fg-subtle flex flex-col items-center gap-y-3",children:[e.jsx(n,{}),e.jsxs("div",{className:"flex flex-col items-center justify-center gap-y-1",children:[e.jsx(t,{size:"small",leading:"compact",weight:"plus",children:a}),e.jsx(t,{size:"small",className:"text-ui-fg-muted text-balance text-center",children:l})]})]}),e.jsx(i,{asChild:!0,size:"small",variant:"secondary",children:e.jsx(c,{to:"/",children:s("errorBoundary.backToDashboard")})})]})})};export{x as Component};
