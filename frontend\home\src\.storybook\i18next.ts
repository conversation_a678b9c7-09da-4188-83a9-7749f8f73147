// Import the unified i18n implementation from src/lib
import { i18n as unifiedI18n, languageCodes, supportedLngs, ns, defaultNS, isRTL } from '../src/lib';

// Get the actual i18next instance from the unified i18n object
const i18n = unifiedI18n.client.i18n;

const resources = languageCodes.reduce((acc, lng) => {
  try {
    acc[lng] = {
      [defaultNS]: require(`../public/locales/${lng}/${defaultNS}.json`),
    };
  } catch (error) {
    console.warn(`Could not load translations for ${lng}:`, error);
  }
  return acc;
}, {});

// Configure i18n for Storybook
i18n.init({
  fallbackLng: 'en',
  ns: [defaultNS],
  defaultNS,
  debug: false,
  interpolation: {
    escapeValue: false,
  },
  react: {
    useSuspense: false, // Important for Storybook compatibility
  },
  supportedLngs: languageCodes,
  resources,
  detection: {
    order: ['localStorage', 'cookie', 'navigator'],
    caches: ['localStorage', 'cookie'],
  },
});

// Add RTL support
i18n.on('languageChanged', (locale) => {
  const direction = isRTL(locale as any) ? 'rtl' : 'ltr';
  document.documentElement.lang = locale;
  document.documentElement.dir = direction;
});

export default i18n; 