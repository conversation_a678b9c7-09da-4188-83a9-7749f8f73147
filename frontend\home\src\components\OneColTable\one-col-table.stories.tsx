import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { expect, within, userEvent } from '@storybook/test';
import { Clock, Check, AlertCircle, FileText } from 'lucide-react';

import OneColTable from './one-col-table';
import OneColTableLayout from './one-col-table-layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { getStatusColorClass } from './utils';
import {
  mockItems,
  largeDatasetItems,
  virtualizedItems,
} from './__fixtures__/one-col-table.fixtures';
import { TableItem } from './types';

const meta = {
  title: 'Components/OneColTable/OneColTable',
  component: OneColTable,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A single-column table component that displays a list of items with support for virtualization, loading states, and custom content rendering. Perfect for transaction lists, notifications, activity feeds, and other vertical list views.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      description: 'The size variant of the table rows',
      control: 'select',
      options: ['sm', 'md', 'lg'],
      defaultValue: 'md',
      table: {
        type: { summary: 'string' },
      },
    },
    variant: {
      description: 'The style variant of the table',
      control: 'select',
      options: ['primary', 'secondary', 'outline'],
      defaultValue: 'primary',
      table: {
        type: { summary: 'string' },
      },
    },
    loading: {
      description: 'Whether the table is in loading state',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
    disabled: {
      description: 'Whether the table is disabled',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
    animate: {
      description: 'Whether to animate the rows when they appear',
      control: 'boolean',
      defaultValue: true,
      table: {
        type: { summary: 'boolean' },
      },
    },
    virtualized: {
      description: 'Whether to use virtualization for large datasets',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
    header: {
      description: 'Optional header component displayed at the top of the table',
      control: 'text',
      table: {
        type: { summary: 'ReactNode' },
      },
    },
    error: {
      description: 'Error message or state',
      control: 'text',
      table: {
        type: { summary: 'ReactNode | string' },
      },
    },
    limit: {
      description: 'Maximum number of items to display',
      control: 'number',
      table: {
        type: { summary: 'number' },
      },
    },
    showLoadMore: {
      description: 'Whether to show a "Load more" button',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
    hasMore: {
      description: 'Whether there are more items to load',
      control: 'boolean',
      defaultValue: false,
      table: {
        type: { summary: 'boolean' },
      },
    },
    height: {
      description: 'Height of the table when virtualized',
      control: 'number',
      table: {
        type: { summary: 'number | string' },
      },
    },
    emptyState: {
      description: 'Custom content to display when there are no items',
      control: 'text',
      table: {
        type: { summary: 'ReactNode' },
      },
    },
  },
} satisfies Meta<typeof OneColTable>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    items: mockItems.slice(0, 5),
    size: 'md',
    variant: 'primary',
    animate: true,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const firstRow = await canvas.findByText('Batch Processing #1');
    expect(firstRow).toBeInTheDocument();
  },
};

// Variants story
export const Variants: Story = {
  parameters: {
    controls: { hideNoControlsWarning: true },
  },
  args: {
    items: mockItems.slice(0, 3),
  },
  render: (args) => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Primary Variant</h3>
        <OneColTable variant="primary" items={args.items} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Secondary Variant</h3>
        <OneColTable variant="secondary" items={args.items} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Outline Variant</h3>
        <OneColTable variant="outline" items={args.items} />
      </div>
    </div>
  ),
};

// Sizes story
export const Sizes: Story = {
  parameters: {
    controls: { hideNoControlsWarning: true },
  },
  args: {
    items: mockItems,
  },
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Small Size</h3>
        <OneColTable size="sm" items={largeDatasetItems.slice(0, 3)} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Medium Size (Default)</h3>
        <OneColTable size="md" items={largeDatasetItems.slice(3, 8)} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Large Size</h3>
        <OneColTable size="lg" items={largeDatasetItems.slice(8, 15)} />
      </div>
    </div>
  ),
};

// Loading state story
export const Loading: Story = {
  parameters: {
    storyGroup: 'States',
  },
  args: {
    loading: true,
    items: largeDatasetItems,
  },
};

// Empty State
export const EmptyState: Story = {
  parameters: {
    storyGroup: 'States',
  },
  args: {
    items: [],
  },
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Default Empty State</h3>
        <OneColTable items={[]} />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Custom Empty State</h3>
        <OneColTable
          items={[]}
          emptyState={
            <div className="flex flex-col items-center justify-center py-8">
              <AlertCircle className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-1 text-lg font-semibold">No items found</h3>
              <p className="text-muted-foreground mb-4">Try adjusting your search criteria</p>
              <Button>Add an item</Button>
            </div>
          }
        />
      </div>
    </div>
  ),
};

// With layout story (for compound components)
export const WithLayout: Story = {
  parameters: {
    storyGroup: 'Layout',
  },
  args: {
    items: mockItems,
  },
  render: () => (
    <OneColTableLayout
      title="Items with Layout"
      items={mockItems}
      showSearch
      showPagination
      pageSize={5}
    />
  ),
};

// Error state story
export const Error: Story = {
  parameters: {
    storyGroup: 'States',
  },
  args: {
    items: largeDatasetItems,
  },
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Default Error State</h3>
        <OneColTable items={largeDatasetItems} error="Failed to load items" />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Customized Error Message</h3>
        <OneColTable
          items={largeDatasetItems}
          error={
            <div className="text-destructive flex items-center">
              <AlertCircle className="mr-2 h-4 w-4" />
              Connection error: Unable to fetch data
            </div>
          }
        />
      </div>
    </div>
  ),
};

// Animations variations story
export const Animations: Story = {
  parameters: {
    storyGroup: 'Interactions',
  },
  args: {
    items: mockItems.slice(0, 5),
  },
  render: () => (
    <div className="space-y-8">
      <h3 className="text-lg font-semibold">With Animation</h3>
      <OneColTable items={mockItems.slice(0, 5)} animate={true} />

      <h3 className="text-lg font-semibold">Without Animation</h3>
      <OneColTable items={mockItems.slice(0, 5)} animate={false} />
    </div>
  ),
};

// Interactive usage with click handlers
export const WithInteractions: Story = {
  parameters: {
    storyGroup: 'Interactions',
  },
  args: {
    items: mockItems.slice(0, 5),
  },
  render: () => {
    return (
      <div className="space-y-4">
        <OneColTable
          items={mockItems.slice(0, 5)}
          onRowClick={(item: TableItem) => {
            console.warn('Row clicked:', item.id);
          }}
        />
      </div>
    );
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const firstRow = await canvas.findByText('Batch Processing #1');

    // Click the first row
    await userEvent.click(firstRow);
  },
};

// Theme variations story
export const ThemeVariations: Story = {
  parameters: {
    storyGroup: 'Backgrounds',
    themes: ['light', 'dark'],
  },
  args: {
    items: mockItems.slice(0, 5),
  },
  render: () => (
    <div className="space-y-4">
      <OneColTable items={mockItems.slice(0, 5)} />
    </div>
  ),
};

// Infinite scrolling with "Load more" button
export const InfiniteLoading: Story = {
  parameters: {
    storyGroup: 'Performance',
  },
  args: {
    items: mockItems.slice(0, 5),
    showLoadMore: true,
    hasMore: true,
  },
  render: () => (
    <OneColTable
      items={mockItems.slice(0, 5)}
      showLoadMore
      hasMore
      onLoadMore={() => console.warn('Load more clicked')}
    />
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const loadMoreButton = await canvas.findByText('Load more');
    expect(loadMoreButton).toBeInTheDocument();

    // Click the load more button
    await userEvent.click(loadMoreButton);
  },
};

// Accessibility demonstration story
export const AccessibilityDemo: Story = {
  parameters: {
    storyGroup: 'Accessibility',
  },
  args: {
    items: mockItems.slice(0, 3),
  },
  render: () => (
    <div className="space-y-8">
      <h3 className="text-lg font-semibold">With Keyboard Navigation Focus</h3>
      <OneColTable
        items={mockItems.slice(0, 3)}
        onRowClick={(item: TableItem) => {
          console.warn('Row clicked:', item.id);
        }}
      />
    </div>
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const firstRow = await canvas.findByText('Batch Processing #1');
    const rowElement = firstRow.closest('[role="row"]');

    if (rowElement) {
      // Just check that the row element exists
      expect(rowElement).toBeInTheDocument();
    }
  },
};

// Disabled examples
export const WithCustomOnClickHandler: Story = {
  parameters: {
    storyGroup: 'Interactions',
  },
  args: {
    items: mockItems.slice(0, 5),
    variant: 'primary',
    size: 'md',
  },
  render: (args) => (
    <div>
      <h2 className="mb-4 text-lg font-medium">Click on a row to see an alert</h2>
      <OneColTable
        {...args}
        onRowClick={(item: TableItem, index: number) => {
          console.warn(`Clicked on item ${item.id} at index ${index}`);
          alert(`Clicked: ${item.content}`);
        }}
      />
    </div>
  ),
};

// Status variations story
export const StatusVariations: Story = {
  parameters: {
    storyGroup: 'Variants',
  },
  args: {
    items: mockItems,
    variant: 'primary',
    size: 'lg',
    animate: true,
    limit: 0,
    header: 'asdfasdf',
  },
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">With Status Indicators</h3>
        <OneColTable
          items={mockItems.map((item) => ({
            ...item,
            content: (
              <div className="flex items-center">
                <div
                  className={cn(
                    'mr-3 flex h-8 w-8 items-center justify-center rounded-full',
                    item.status === 'completed'
                      ? 'bg-green-100'
                      : item.status === 'in-progress'
                        ? 'bg-blue-100'
                        : item.status === 'pending'
                          ? 'bg-amber-100'
                          : item.status === 'warning'
                            ? 'bg-orange-100'
                            : 'bg-red-100',
                  )}
                >
                  {item.status === 'completed' ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : item.status === 'in-progress' ? (
                    <Clock className="h-4 w-4 text-blue-600" />
                  ) : item.status === 'pending' ? (
                    <FileText className="h-4 w-4 text-amber-600" />
                  ) : item.status === 'warning' ? (
                    <AlertCircle className="h-4 w-4 text-orange-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                </div>
                <span>{item.content}</span>
              </div>
            ),
            secondaryContent: (
              <div className="mt-1 flex items-center">
                <Badge variant="outline" className={cn(getStatusColorClass(item.status as string))}>
                  {item.status as string}
                </Badge>
                <span className="ml-2">{item.secondaryContent}</span>
              </div>
            ),
          }))}
          variant="outline"
        />
      </div>
    </div>
  ),
};

// Edge cases story
export const EdgeCases: Story = {
  parameters: {
    storyGroup: 'Variants',
  },
  args: {
    items: [
      {
        id: 'missing-1',
        content: 'Item with no secondary content',
        timestamp: new Date(),
      },
    ],
  },
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">With Missing Data</h3>
        <OneColTable
          items={[
            {
              id: 'missing-1',
              content: 'Item with no secondary content',
              timestamp: new Date(),
            },
            {
              id: 'missing-2',
              content: 'Item with no timestamp',
              secondaryContent: 'Only has secondary content',
            },
            {
              id: 'missing-3',
              content: null as unknown as string,
              secondaryContent: 'Has null content',
              timestamp: new Date(),
            },
            {
              id: 'missing-4',
              content: undefined as unknown as string,
              secondaryContent: 'Has undefined content',
              timestamp: new Date(),
            },
            {
              id: 'missing-5',
              content: '',
              secondaryContent: 'Has empty string content',
              timestamp: new Date(),
            },
          ]}
        />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">With Unusual Values</h3>
        <OneColTable
          items={[
            {
              id: 'unusual-1',
              content: 'Item with very long content '.repeat(10),
              secondaryContent: 'With normal secondary content',
              timestamp: new Date(),
            },
            {
              id: 'unusual-2',
              content: 'Item with very long secondary content',
              secondaryContent:
                'This is a very long secondary content that should wrap to multiple lines '.repeat(
                  5,
                ),
              timestamp: new Date(),
            },
            {
              id: 'unusual-3',
              content: 'Item with invalid date',
              secondaryContent: 'Has an invalid timestamp',
              timestamp: 'not-a-date' as unknown as Date,
            },
          ]}
        />
      </div>
    </div>
  ),
};

// Virtualization story
export const Virtualization: Story = {
  parameters: {
    storyGroup: 'Performance',
  },
  args: {
    items: virtualizedItems,
    virtualized: true,
    height: 400,
  },
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">With Virtualization (1000 items)</h3>
        <div className="h-[400px] w-[600px]">
          <OneColTable items={virtualizedItems} virtualized height={400} />
        </div>
      </div>
    </div>
  ),
};

// Responsive story
export const Responsive: Story = {
  args: {
    items: mockItems.slice(0, 5),
    className: 'w-full',
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
    chromatic: {
      viewports: [320, 768, 1200],
    },
    storyGroup: 'Viewport',
  },
  render: () => (
    <div className="w-full max-w-full">
      <OneColTable className="w-full" items={mockItems.slice(0, 5)} />
    </div>
  ),
};
