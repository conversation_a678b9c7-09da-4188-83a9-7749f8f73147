import { createDateConversionData, ensureISOFormat } from '@/lib/dateFormatter';
import { DateConversionTooltip } from './types';

/**
 * Formats a timestamp into a date conversion tooltip object
 *
 * @param timestamp - ISO 8601 timestamp string or undefined
 * @param language - Optional language code for internationalization (e.g., 'en', 'fr', 'ja')
 * @returns DateConversionTooltip object with formatted date information
 */
export function formatDateConversion(timestamp?: string, language?: string): DateConversionTooltip {
  if (!timestamp) {
    return {
      timezone: '',
      localTime: '',
      utcTime: '',
      date: '',
      localTimezone: '',
    };
  }

  // Ensure the timestamp is in ISO format
  const isoTimestamp = ensureISOFormat(timestamp);

  // Use the utility function to create the date conversion data with language
  return createDateConversionData(isoTimestamp, language);
}

/**
 * Gets the browser's local timezone name
 *
 * @returns String representing the local timezone name
 */
export function getLocalTimezoneName(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone || 'Local';
  } catch {
    return 'Local';
  }
}
