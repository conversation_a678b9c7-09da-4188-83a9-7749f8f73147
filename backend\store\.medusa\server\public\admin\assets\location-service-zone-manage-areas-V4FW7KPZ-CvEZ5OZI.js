import{G as x}from"./chunk-DO73XJPS-ON9hrF-H.js";import{a6 as l,R as j,a_ as v,ar as g,j as e,b as S,a8 as y,cN as w,a9 as F,t as _,H as Z,B as h}from"./index-Bwql5Dzz.js";import{G as b}from"./chunk-PYIO3TDQ-D8Zv8hXV.js";import"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-X5VECN6S-B3ePbuRl.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{c as A}from"./chunk-GVRV2SOJ-pKecjhuj.js";import{K as E}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as n,u as z,S as N}from"./chunk-JGQGO74V-DtHO1ucg.js";import"./chunk-NOAFLTPV-DWvXax-e.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./index-BxZ1678G.js";import"./checkbox-B4pL6X49.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./x-mark-mini-DvSTI7zK.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./chunk-BF3VCHXD-J5OiX7iF.js";import"./prompt-BsR9zKsn.js";var L=l.object({countries:l.array(l.object({iso_2:l.string().min(2),display_name:l.string()})).min(1)});function C({fulfillmentSetId:a,locationId:d,zone:t}){const{t:o}=S(),{handleSuccess:p}=z(),r=y({defaultValues:{countries:t.geo_zones.map(i=>{const s=w.find(m=>m.iso_2===i.country_code);return{iso_2:i.country_code,display_name:(s==null?void 0:s.display_name)||i.country_code.toUpperCase()}})},resolver:F(L)}),{mutateAsync:u,isPending:f}=A(a,t.id),c=r.handleSubmit(async i=>{await u({geo_zones:i.countries.map(({iso_2:s})=>({country_code:s,type:"country"}))},{onSuccess:()=>{_.success(o("stockLocations.serviceZones.manageAreas.successToast",{name:t.name})),p(`/settings/locations/${d}`)},onError:s=>{_.error(s.message)}})});return e.jsx(n.Form,{form:r,children:e.jsxs(E,{className:"flex h-full flex-col overflow-hidden",onSubmit:c,children:[e.jsx(n.Header,{}),e.jsx(n.Body,{className:"flex flex-1 flex-col overflow-auto",children:e.jsxs(N,{id:b,children:[e.jsx("div",{className:"flex flex-col items-center p-16",children:e.jsxs("div",{className:"flex w-full max-w-[720px] flex-col gap-y-8",children:[e.jsx(Z,{children:o("stockLocations.serviceZones.manageAreas.header",{name:t.name})}),e.jsx(x,{form:r})]})}),e.jsx(x.AreaDrawer,{form:r})]})}),e.jsx(n.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(n.Close,{asChild:!0,children:e.jsx(h,{variant:"secondary",size:"small",children:o("actions.cancel")})}),e.jsx(h,{type:"submit",size:"small",isLoading:f,children:o("actions.save")})]})})]})})}var de=()=>{var i,s;const{location_id:a,fset_id:d,zone_id:t}=j(),{stock_location:o,isPending:p,isFetching:r,isError:u,error:f}=v(a,{fields:"*fulfillment_sets.service_zones.geo_zones,fulfillment_sets.service_zones.name"}),c=(s=(i=o==null?void 0:o.fulfillment_sets)==null?void 0:i.find(m=>m.id===d))==null?void 0:s.service_zones.find(m=>m.id===t);if(!p&&!r&&!c)throw g({message:`Service zone with ID ${t} was not found`},404);if(u)throw f;return e.jsx(n,{prev:`/settings/locations/${a}`,children:c&&e.jsx(C,{zone:c,fulfillmentSetId:d,locationId:a})})};export{de as Component};
