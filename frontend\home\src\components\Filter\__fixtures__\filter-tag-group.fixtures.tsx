import { FilterTag } from "../filter-tag-group";

/**
 * Sample filter tags for testing and stories
 */
export const sampleTags: FilterTag[] = [
  { id: '1', type: 'หลอดไฟ', value: 'led', label: 'LED' },
  { id: '2', type: 'แบรนด์', value: 'philips', label: '<PERSON>' },
  { id: '3', type: 'แบรนด์', value: 'eve', label: 'EVE' },
  { id: '4', type: 'แบรนด์', value: 'lamptan', label: 'LAMPTAN' },
];

/**
 * Sample filters categories for the interactive demo
 */
export const availableFilters = {
  lightTypes: [
    { id: '1', value: 'led', label: 'หลอดไฟ LED' },
    { id: '2', value: 'fluorescent', label: 'ไฟหลอดฟลูออเรสเซนต์' },
    { id: '3', value: 'pendant', label: 'หลอดแขวน' },
  ],
  brands: [
    { id: '1', value: 'philips', label: 'Philips' },
    { id: '2', value: 'eve', label: 'EVE' },
    { id: '3', value: 'lamptan', label: 'LAMPTAN' },
    { id: '4', value: 'panasonic', label: 'PANASONIC' },
    { id: '5', value: 'toshiba', label: 'TOSHIBA' },
  ],
  shapes: [
    { id: '1', value: 'round', label: 'กลมแบน' },
    { id: '2', value: 'tube', label: 'ทรงกระบอก' },
    { id: '3', value: 'square', label: 'เหลี่ยม' },
  ],
  colors: [
    { id: '1', value: 'white', label: 'ขาว' },
    { id: '2', value: 'yellow', label: 'เหลือง' },
  ]
};