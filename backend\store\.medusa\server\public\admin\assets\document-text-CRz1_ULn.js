import{r as a}from"./index-Bwql5Dzz.js";var s=Object.defineProperty,n=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,i=(r,t,e)=>t in r?s(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,c=(r,t)=>{for(var e in t)l.call(t,e)&&i(r,e,t[e]);if(n)for(var e of n(t))f.call(t,e)&&i(r,e,t[e]);return r},d=(r,t)=>{var e={};for(var o in r)l.call(r,o)&&t.indexOf(o)<0&&(e[o]=r[o]);if(r!=null&&n)for(var o of n(r))t.indexOf(o)<0&&f.call(r,o)&&(e[o]=r[o]);return e};const v=a.forwardRef((r,t)=>{var e=r,{color:o="currentColor"}=e,p=d(e,["color"]);return a.createElement("svg",c({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),a.createElement("path",{stroke:o,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4.611 5.5H6.39M4.611 8.167h5.778M4.611 10.833h5.778M1.944 12.167V2.833c0-.982.796-1.777 1.778-1.777h4.966c.235 0 .462.093.628.26l3.48 3.48c.166.166.26.392.26.628v6.743c0 .982-.796 1.778-1.778 1.778H3.722a1.777 1.777 0 0 1-1.778-1.778"}),a.createElement("path",{stroke:o,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M12.976 5.056H9.944a.89.89 0 0 1-.888-.89v-3.02"}))});v.displayName="DocumentText";export{v as D};
