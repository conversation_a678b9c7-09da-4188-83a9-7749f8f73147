import { render, screen, fireEvent } from '@testing-library/react';
import { Rating } from '../rating';

describe('Rating', () => {
  test('renders correct number of stars', () => {
    render(<Rating value={3} maxValue={5} />);
    
    // Check that 5 stars are rendered
    const stars = screen.getAllByRole('img');
    expect(stars).toHaveLength(1); // The entire rating component has role="img"
  });

  test('handles interactive mode correctly', () => {
    const mockOnChange = jest.fn();
    render(
      <Rating 
        value={3} 
        maxValue={5} 
        interactive={true} 
        onChange={mockOnChange} 
      />
    );
    
    // Find stars and click the 4th one
    const container = screen.getByRole('img');
    const stars = container.querySelectorAll('div');
    fireEvent.click(stars[3]); // Click 4th star (index 3)
    
    // Check if onChange was called with correct value
    expect(mockOnChange).toHaveBeenCalledWith(4);
  });

  test('handles different sizes correctly', () => {
    const { rerender } = render(<Rating value={3} size="sm" />);
    
    // Check small size
    let star = screen.getByRole('img').querySelector('svg');
    expect(star).toHaveAttribute('width', '16');
    
    // Rerender with large size
    rerender(<Rating value={3} size="lg" />);
    
    // Check large size
    star = screen.getByRole('img').querySelector('svg');
    expect(star).toHaveAttribute('width', '32');
  });

  test('handles vertical direction correctly', () => {
    render(<Rating value={3} direction="vertical" />);
    
    // Check for vertical class
    const container = screen.getByRole('img');
    expect(container).toHaveClass('flex-col');
  });

  test('uses custom colors correctly', () => {
    render(
      <Rating 
        value={3} 
        maxValue={5} 
        filledColor="text-red-500" 
        emptyColor="text-gray-200" 
      />
    );
    
    // Check for custom filled color
    const container = screen.getByRole('img');
    const stars = container.querySelectorAll('svg');
    
    // First 3 stars should have filled color
    expect(stars[0]).toHaveClass('text-red-500');
    
    // Last 2 stars should have empty color
    expect(stars[4]).toHaveClass('text-gray-200');
  });

  test('provides accessible label', () => {
    render(<Rating value={3} label="Product rating: 3 out of 5" />);
    
    // Check for aria-label
    const container = screen.getByRole('img');
    expect(container).toHaveAttribute('aria-label', 'Product rating: 3 out of 5');
  });
});