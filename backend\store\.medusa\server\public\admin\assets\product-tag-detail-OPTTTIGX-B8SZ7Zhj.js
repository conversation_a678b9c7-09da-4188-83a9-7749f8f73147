import{u as h}from"./chunk-LNROU6QR-NOnC9z5g.js";import{eI as p,j as e,q as j,R as T,d as v,a as S,dv as D,eF as _,s as w,b as m,H as g,A as C,e as k}from"./index-Bwql5Dzz.js";import{u as q}from"./chunk-G3QXMPRB-BXa5-FQN.js";import{u as E,_ as N}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import{u as A,a as B}from"./chunk-U6CSGYH6-BpcURsBT.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as F}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{P as I}from"./pencil-square-6wRbnn1C.js";import{T as Q}from"./trash-BBylvTAG.js";import{C as f}from"./container-Dqi2woPF.js";import"./use-prompt-pbDx0Sfe.js";import"./prompt-BsR9zKsn.js";import"./chunk-IQBAUTU5-D_4dFOf0.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./index-BxZ1678G.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";var ve=r=>{const{id:t}=r.params||{},{product_tag:a}=p(t,void 0,{initialData:r.data,enabled:!!t});return a?e.jsx("span",{children:a.value}):null},R=r=>({queryKey:_.detail(r),queryFn:async()=>w.admin.productTag.retrieve(r)}),Se=async({params:r})=>{const t=r.id,a=R(t);return j.ensureQueryData(a)},z=({productTag:r})=>{const{t}=m(),a=h({productTag:r});return e.jsxs(f,{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-x-1.5",children:[e.jsx("span",{className:"text-ui-fg-muted h1-core",children:"#"}),e.jsx(g,{children:r.value})]}),e.jsx(C,{groups:[{actions:[{icon:e.jsx(I,{}),label:t("actions.edit"),to:"edit"}]},{actions:[{icon:e.jsx(Q,{}),label:t("actions.delete"),onClick:a}]}]})]})},u=10,l="pt",L=({productTag:r})=>{const{t}=m(),{searchParams:a,raw:s}=A({pageSize:u,prefix:l}),{products:i,count:o,isPending:n,isError:x,error:y}=k({...a,tag_id:r.id}),b=B(["product_tags"]),c=q(),{table:P}=E({data:i,count:o,columns:c,getRowId:d=>d.id,pageSize:u,prefix:l});if(x)throw y;return e.jsxs(f,{className:"divide-y px-0 py-0",children:[e.jsx("div",{className:"px-6 py-4",children:e.jsx(g,{level:"h2",children:t("products.domain")})}),e.jsx(N,{table:P,filters:b,queryObject:s,isLoading:n,columns:c,pageSize:u,count:o,navigateTo:d=>`/products/${d.original.id}`,search:!0,pagination:!0,orderBy:[{key:"title",label:t("fields.title")},{key:"status",label:t("fields.status")},{key:"created_at",label:t("fields.createdAt")},{key:"updated_at",label:t("fields.updatedAt")}]})]})},De=()=>{const{id:r}=T(),t=v(),{getWidgets:a}=S(),{product_tag:s,isPending:i,isError:o,error:n}=p(r,void 0,{initialData:t});if(i||!s)return e.jsx(D,{showJSON:!0,sections:2});if(o)throw n;return e.jsxs(F,{widgets:{after:a("product_tag.details.after"),before:a("product_tag.details.before")},showJSON:!0,data:s,children:[e.jsx(z,{productTag:s}),e.jsx(L,{productTag:s})]})};export{ve as Breadcrumb,De as Component,Se as loader};
