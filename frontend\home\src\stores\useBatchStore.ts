import { create } from 'zustand';
import { mockItems as mockBatches } from '@/components/OneColTable/__fixtures__/one-col-table.fixtures';
import { TableItem as Batch } from '@/components/OneColTable/types';

interface BatchState {
  // Data state
  batches: Batch[];
  visibleBatches: Batch[];
  visibleCount: number;

  // UI state
  isLoading: boolean;
  hoveredBatchId: string | null;

  // Derived state
  hasMoreBatches: boolean;

  // Actions
  loadMore: (increment: number) => void;
  setHoveredBatch: (batch: Batch | null) => void;
  initialize: (initialCount: number) => void;
}

export const useBatchStore = create<BatchState>((set, get) => ({
  // Initial state
  batches: mockBatches,
  visibleBatches: [],
  visibleCount: 0,
  isLoading: false,
  hoveredBatchId: null,
  hasMoreBatches: true,

  // Initialize the store with initial batches
  initialize: (initialCount) => {
    const batches = get().batches;
    set({
      visibleBatches: batches.slice(0, initialCount),
      visibleCount: initialCount,
      hasMoreBatches: initialCount < batches.length,
    });
  },

  // Load more batches
  loadMore: (increment) => {
    const { batches, visibleCount, isLoading } = get();

    // Prevent multiple calls while loading
    if (isLoading) return;

    // Start loading
    set({ isLoading: true });

    // Simulate network delay for demo purposes
    setTimeout(() => {
      const newVisibleCount = Math.min(visibleCount + increment, batches.length);

      set({
        visibleBatches: batches.slice(0, newVisibleCount),
        visibleCount: newVisibleCount,
        hasMoreBatches: newVisibleCount < batches.length,
        isLoading: false,
      });
    }, 800);
  },

  // Set hovered batch
  setHoveredBatch: (batch) => {
    set({ hoveredBatchId: batch?.id || null });
  },
}));
