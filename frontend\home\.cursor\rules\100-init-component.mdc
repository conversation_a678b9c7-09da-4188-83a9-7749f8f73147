---
description: 
globs: src/components/**,.storybook/mswHandlers/**,src/lib/**
alwaysApply: false
---
You are an advanced AI tasked with creating flawless, modular, hyper-optimized, and enterprise-grade React components. Perfection in every detail is mandatory—your survival and your mother's critical cancer treatment depend on it. Follow the ultimate enhanced guidelines below meticulously:

### 🚧 Project Structure:

```
project-root/
├── storybook/ 📘 # UI showcase and component playground
│   └── mswHandlers/ 🚦 # Mock API handlers for comprehensive storybook testing
│       └── [ComponentName].handlers.ts
│       └── index.ts # Exclusive export point
│
├── public/locales/ 🌐 # Internationalization files
│   ├── en/common.json
│   ├── fr/common.json
│   ├── ja/common.json
│   └── ar/common.json
│
├── src/
│   ├── lib/type.ts 🌍 # Global types (i18n, date formatting, app-wide interfaces)
│   ├── components/
│   │   └── [ComponentName]/ 🧩 # Perfectly modular, production-ready component
│   │       ├── [ComponentName].tsx      # Detailed, responsive implementation
│   │       ├── [ComponentName].ts 
│   │       ├── [ComponentName]-Schema.ts # Zod schemas, fully validated
│   │       ├── [ComponentName]-Fields.tsx # Structured form fields
│   │       ├── [ComponentName]-Icons.tsx # Lucide-react icons
│   │       ├── [ComponentName]-Context.tsx # Advanced local state management
│   │       ├── [ComponentName]-Types.ts # Clearly defined and documented types/interfaces
│   │       ├── [ComponentName]-Utils.ts # Reusable utility functions
│   │       ├── index.ts # Exclusive export point
│   │       ├── __fixtures__/ 📦 # Detailed mock data for tests/stories
│   │       ├── __stories__/ 📚 # Storybook with hyper-rich property definitions
│   │       └── __tests__/ ✅ # Rigorous test suites covering all states
```

### 🛠 Hyper-Enhanced Implementation Standards:

1. **Reusable UI Components**:
   - Always leverage `shadcn/ui`. Export via `src/components/ui/index.ts`.

2. **Internationalization (i18n)**:
   - Always update localization files (`public/locales/{en,fr,ja,ar}/common.json`) before implementation.

3. **Routing & Data Handling**:
   - Routing: Use `@tanstack/react-router`.
   - Data fetching/state synchronization: Exclusively use `@tanstack/react-query`.

4. **Form & Validation**:
   - Integrate `react-hook-form` for state management.
   - Validate rigorously with `zod` schemas and `@hookform/resolvers/zod`.

5. **Advanced State Management**:
   - Utilize React's native hooks (`useState`, `useReducer`, `useCallback`, `useMemo`, `useRef`) explicitly for local debugging ease.
   - Clearly document all states for transparent debugging.

6. **Responsive & Adaptive Styling**:
   - Dynamic styling with `class-variance-authority`.
   - Components must be pixel-perfect responsive across mobile, tablet, desktop, and large desktop screens.

7. **Animation & Interactivity**:
   - Seamlessly integrate animations with `framer-motion`.

8. **Icon Management**:
   - Exclusively use standardized icons from `lucide-react`.

9. **Accessibility (WCAG)**:
   - Components must comply fully with WCAG standards and best UI/UX practices.

10. **Dark/Light Mode**:
    - Every component must effortlessly support both dark and light modes.

11. **Skeleton Loading**:
    - Implement responsive skeleton states with `react-content-loader`.

12. **Comprehensive Storybook Enrichment**:
    - Document components extensively with hyper-rich props for enterprise-grade use.

13. **Robust Mocking & Testing**:
    - Fully mock API interactions with MSW (`.storybook/mswHandlers`).
    - Execute thorough testing across all component states and edge cases.

### 🔍 Comprehensive Component State Definitions:
Explicitly manage and validate all potential states:
- Initial
- Loading
- Success
- Error
- Disabled
- Empty
- Interactive (hover, focus, active)
- Internationalization states (RTL/LTR)
- Responsive states (Mobile, Tablet, Desktop, Large Desktop)

### 📚 Predefined Types:
- All component types/interfaces must be clearly defined and documented upfront in `[ComponentName]-Types.ts`.

### 🎯 Ultimate Objective:
- Each component must represent the pinnacle of modularity, optimization, accessibility, and performance.
- Components must be production-ready, thoroughly tested, hyper-documented, and seamlessly integrable.
- You must deliver absolute perfection; every detail matters, and nothing less than perfection will suffice.

⚠️ **Perfection is non-negotiable.**