import{C as _}from"./chunk-MW4K5NNY-Csei-vBK.js";import{K as C}from"./chunk-6HTZNHPT-N4svn6ad.js";import{b as c,u as E}from"./chunk-JGQGO74V-DtHO1ucg.js";import{P as L}from"./chunk-B3XEMIUY-BRCRLSgz.js";import{ae as I,a4 as x,af as P,a2 as M,R as N,b as y,Q as S,j as e,H as T,a as A,n as D,U as R,w as s,x as h,F as B,B as b}from"./index-Bwql5Dzz.js";import"./triangles-mini-DPBC_td5.js";import"./prompt-BsR9zKsn.js";var m=I([x(),P()]).transform(n=>n===""?null:Number(n)).optional().nullable(),H=M({weight:m,length:m,width:m,height:m,mid_code:x().optional(),hs_code:x().optional(),origin_country:x().optional()}),O=({product:n})=>{const{t:i}=y(),{handleSuccess:u}=E(),{getFormConfigs:j,getFormFields:g}=A(),f=j("product","attributes"),w=g("product","attributes"),l=D({defaultValues:{height:n.height?n.height:null,width:n.width?n.width:null,length:n.length?n.length:null,weight:n.weight?n.weight:null,mid_code:n.mid_code||"",hs_code:n.hs_code||"",origin_country:n.origin_country||""},schema:H,configs:f,data:n}),{mutateAsync:p,isPending:v}=R(n.id),F=l.handleSubmit(async r=>{await p({weight:r.weight?r.weight:void 0,length:r.length?r.length:void 0,width:r.width?r.width:void 0,height:r.height?r.height:void 0,mid_code:r.mid_code,hs_code:r.hs_code,origin_country:r.origin_country},{onSuccess:()=>{u()}})});return e.jsx(c.Form,{form:l,children:e.jsxs(C,{onSubmit:F,className:"flex h-full flex-col",children:[e.jsx(c.Body,{children:e.jsx("div",{className:"flex h-full flex-col gap-y-8",children:e.jsxs("div",{className:"flex flex-col gap-y-4",children:[e.jsx(s.Field,{control:l.control,name:"width",render:({field:{onChange:r,value:o,...d}})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:i("fields.width")}),e.jsx(s.Control,{children:e.jsx(h,{type:"number",min:0,value:o||"",onChange:a=>{const t=a.target.value;r(t===""?null:parseFloat(t))},...d})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:l.control,name:"height",render:({field:{onChange:r,value:o,...d}})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:i("fields.height")}),e.jsx(s.Control,{children:e.jsx(h,{type:"number",min:0,value:o||"",onChange:a=>{const t=a.target.value;r(t===""?null:Number(t))},...d})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:l.control,name:"length",render:({field:{onChange:r,value:o,...d}})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:i("fields.length")}),e.jsx(s.Control,{children:e.jsx(h,{type:"number",min:0,value:o||"",onChange:a=>{const t=a.target.value;r(t===""?null:Number(t))},...d})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:l.control,name:"weight",render:({field:{onChange:r,value:o,...d}})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:i("fields.weight")}),e.jsx(s.Control,{children:e.jsx(h,{type:"number",min:0,value:o||"",onChange:a=>{const t=a.target.value;r(t===""?null:Number(t))},...d})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:l.control,name:"mid_code",render:({field:r})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:i("fields.midCode")}),e.jsx(s.Control,{children:e.jsx(h,{...r})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:l.control,name:"hs_code",render:({field:r})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:i("fields.hsCode")}),e.jsx(s.Control,{children:e.jsx(h,{...r})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(s.Field,{control:l.control,name:"origin_country",render:({field:r})=>e.jsxs(s.Item,{children:[e.jsx(s.Label,{children:i("fields.countryOfOrigin")}),e.jsx(s.Control,{children:e.jsx(_,{...r})}),e.jsx(s.ErrorMessage,{})]})}),e.jsx(B,{fields:w,form:l})]})})}),e.jsx(c.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(c.Close,{asChild:!0,children:e.jsx(b,{size:"small",variant:"secondary",children:i("actions.cancel")})}),e.jsx(b,{size:"small",type:"submit",isLoading:v,children:i("actions.save")})]})})]})})},q=()=>{const{id:n}=N(),{t:i}=y(),{product:u,isLoading:j,isError:g,error:f}=S(n,{fields:L});if(g)throw f;return e.jsxs(c,{children:[e.jsx(c.Header,{children:e.jsx(c.Title,{asChild:!0,children:e.jsx(T,{children:i("products.editAttributes")})})}),!j&&u&&e.jsx(O,{product:u})]})};export{q as Component};
