import { DateConversionTooltip } from '../types';
import { getLocalTimezoneName } from '../utils';

/**
 * Default fixture data for TimeConversionTooltip
 */
export const defaultTooltipData: DateConversionTooltip = {
  timezone: '+00:00',
  localTime: '4:30 PM',
  utcTime: '9:30 PM',
  date: 'Mar 15, 2023',
  localTimezone: getLocalTimezoneName(),
};

/**
 * Multiple time fixtures for testing different scenarios
 */
export const tooltipDataVariants: DateConversionTooltip[] = [
  {
    timezone: '-05:00',
    localTime: '11:45 AM',
    utcTime: '4:45 PM',
    date: 'Jan 10, 2023',
    localTimezone: 'America/New_York',
  },
  {
    timezone: '+09:00',
    localTime: '9:30 PM',
    utcTime: '12:30 PM',
    date: 'Mar 22, 2023',
    localTimezone: 'Asia/Tokyo',
  },
  {
    timezone: '+00:00',
    localTime: '5:15 PM',
    utcTime: '5:15 PM',
    date: 'Sep 5, 2023',
    localTimezone: 'Europe/London',
  },
  {
    timezone: '+11:00',
    localTime: '7:00 AM',
    utcTime: '9:00 PM',
    date: 'Dec 31, 2023',
    localTimezone: 'Australia/Sydney',
  },
];

/**
 * Empty data scenario
 */
export const emptyTooltipData: DateConversionTooltip = {
  timezone: '',
  localTime: '',
  utcTime: '',
  date: '',
  localTimezone: '',
};

/**
 * Long text data scenario
 */
export const longTextTooltipData: DateConversionTooltip = {
  timezone: '-03:00',
  localTime: '11:45 AM',
  utcTime: '2:45 PM',
  date: 'December 25, 2023 (Christmas Day)',
  localTimezone: 'America/Argentina/Buenos_Aires (GMT-03:00)',
};

/**
 * Language-specific fixtures for internationalization testing
 */
export const i18nTooltipData: Record<string, DateConversionTooltip> = {
  // English format
  en: {
    timezone: '+00:00',
    localTime: '4:30 PM',
    utcTime: '9:30 PM',
    date: 'Mar 15, 2023',
    localTimezone: 'Europe/London',
  },
  // French format
  fr: {
    timezone: '+01:00',
    localTime: '16:30',
    utcTime: '15:30',
    date: '15 mars 2023',
    localTimezone: 'Europe/Paris',
  },
  // Japanese format
  ja: {
    timezone: '+09:00',
    localTime: '午後4時30分',
    utcTime: '午前7時30分',
    date: '2023年3月15日',
    localTimezone: 'Asia/Tokyo',
  },
};
