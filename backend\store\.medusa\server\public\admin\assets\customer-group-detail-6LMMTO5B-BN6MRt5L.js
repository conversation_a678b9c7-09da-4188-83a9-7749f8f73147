import{u as N,a as L,b as q}from"./chunk-IDYOGWSG-DuqxOJwc.js";import{dQ as v,j as o,d as M,R as z,a as B,dv as O,q as Q,r as C,b as l,dI as F,dM as b,H as S,L as H,B as $,u as J,dR as K,A as w,T as h,p as U,s as W,t as f}from"./index-Bwql5Dzz.js";import{u as Z,_ as V}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S as X}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{u as d}from"./use-prompt-pbDx0Sfe.js";import{P as G}from"./pencil-square-6wRbnn1C.js";import{T}from"./trash-BBylvTAG.js";import{C as k}from"./container-Dqi2woPF.js";import{C as y}from"./checkbox-B4pL6X49.js";import{c as Y}from"./index-BxZ1678G.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-WYX5PIA3-DoOUp1ge.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";import"./prompt-BsR9zKsn.js";var p="+customers.id",Oe=s=>{const{id:e}=s.params||{},{customer_group:t}=v(e,{fields:p},{initialData:s.data,enabled:!!e});return t?o.jsx("span",{children:t.name}):null},m=10,ee=({group:s})=>{const[e,t]=C.useState({}),{t:r}=l(),i=d(),{searchParams:n,raw:a}=N({pageSize:m}),{customers:g,count:u,isLoading:R,isError:_,error:D}=F({...n,groups:s.id}),x=se(),P=L(["groups"]),{table:A}=Z({data:g??[],columns:x,count:u,getRowId:c=>c.id,enablePagination:!0,enableRowSelection:!0,pageSize:m,rowSelection:{state:e,updater:t},meta:{customerGroupId:s.id}});if(_)throw D;const{mutateAsync:E}=b(s.id),I=async()=>{const c=Object.keys(e);await i({title:r("customerGroups.customers.remove.title",{count:c.length}),description:r("customerGroups.customers.remove.description",{count:c.length}),confirmText:r("actions.continue"),cancelText:r("actions.cancel")})&&await E(c,{onSuccess:()=>{t({})}})};return o.jsxs(k,{className:"divide-y p-0",children:[o.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[o.jsx(S,{level:"h2",children:r("customers.domain")}),o.jsx(H,{to:`/customer-groups/${s.id}/add-customers`,children:o.jsx($,{variant:"secondary",size:"small",children:r("general.add")})})]}),o.jsx(V,{table:A,columns:x,pageSize:m,isLoading:R,count:u,navigateTo:c=>`/customers/${c.id}`,filters:P,search:!0,pagination:!0,orderBy:[{key:"email",label:r("fields.email")},{key:"first_name",label:r("fields.firstName")},{key:"last_name",label:r("fields.lastName")},{key:"has_account",label:r("customers.hasAccount")},{key:"created_at",label:r("fields.createdAt")},{key:"updated_at",label:r("fields.updatedAt")}],queryObject:a,commands:[{action:I,label:r("actions.remove"),shortcut:"r"}],noRecords:{message:r("customerGroups.customers.list.noRecordsMessage")}})]})},te=({customer:s,customerGroupId:e})=>{const{t}=l(),{mutateAsync:r}=b(e),i=d(),n=async()=>{await i({title:t("customerGroups.customers.remove.title",{count:1}),description:t("customerGroups.customers.remove.description",{count:1}),confirmText:t("actions.continue"),cancelText:t("actions.cancel")})&&await r([s.id])};return o.jsx(w,{groups:[{actions:[{icon:o.jsx(G,{}),label:t("actions.edit"),to:`/customers/${s.id}/edit`}]},{actions:[{icon:o.jsx(T,{}),label:t("actions.remove"),onClick:n}]}]})},j=Y(),se=()=>{const s=q();return C.useMemo(()=>[j.display({id:"select",header:({table:e})=>o.jsx(y,{checked:e.getIsSomePageRowsSelected()?"indeterminate":e.getIsAllPageRowsSelected(),onCheckedChange:t=>e.toggleAllPageRowsSelected(!!t)}),cell:({row:e})=>o.jsx(y,{checked:e.getIsSelected(),onCheckedChange:t=>e.toggleSelected(!!t),onClick:t=>{t.stopPropagation()}})}),...s,j.display({id:"actions",cell:({row:e,table:t})=>{const{customerGroupId:r}=t.options.meta;return o.jsx(te,{customer:e.original,customerGroupId:r})}})],[s])},oe=({group:s})=>{var a;const{t:e}=l(),t=d(),r=J(),{mutateAsync:i}=K(s.id),n=async()=>{await t({title:e("customerGroups.delete.title"),description:e("customerGroups.delete.description",{name:s.name}),confirmText:e("actions.delete"),cancelText:e("actions.cancel")})&&await i(void 0,{onSuccess:()=>{f.success(e("customerGroups.delete.successToast",{name:s.name})),r("/customer-groups",{replace:!0})},onError:u=>{f.error(u.message)}})};return o.jsxs(k,{className:"divide-y p-0",children:[o.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[o.jsx(S,{children:s.name}),o.jsx(w,{groups:[{actions:[{icon:o.jsx(G,{}),label:e("actions.edit"),to:`/customer-groups/${s.id}/edit`}]},{actions:[{icon:o.jsx(T,{}),label:e("actions.delete"),onClick:n}]}]})]}),o.jsxs("div",{className:"text-ui-fg-subtle grid grid-cols-2 items-center px-6 py-4",children:[o.jsx(h,{size:"small",leading:"compact",weight:"plus",children:e("customers.domain")}),o.jsx(h,{size:"small",leading:"compact",children:((a=s.customers)==null?void 0:a.length)||"-"})]})]})},Qe=()=>{const s=M(),{id:e}=z(),{customer_group:t,isLoading:r,isError:i,error:n}=v(e,{fields:p},{initialData:s}),{getWidgets:a}=B();if(r||!t)return o.jsx(O,{sections:2,showJSON:!0,showMetadata:!0});if(i)throw n;return o.jsxs(X,{widgets:{before:a("customer_group.details.before"),after:a("customer_group.details.after")},showJSON:!0,showMetadata:!0,data:t,children:[o.jsx(oe,{group:t}),o.jsx(ee,{group:t})]})},re=s=>({queryKey:U.detail(s),queryFn:async()=>W.admin.customerGroup.retrieve(s,{fields:p})}),Fe=async({params:s})=>{const e=s.id,t=re(e);return Q.ensureQueryData(t)};export{Oe as Breadcrumb,Qe as Component,Fe as loader};
