// section-popular-products.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { SectionPopularProducts } from '../section-popular-products';
import { 
  defaultFixture, 
  emptyFixture, 
  loadingFixture, 
  customTitleFixture, 
  featuredFixture 
} from '../__fixtures__/section-popular-products.fixtures';

/**
 * Storybook Meta configuration for SectionPopularProducts component
 */
const meta: Meta<typeof SectionPopularProducts> = {
  title: 'UI/Section/SectionPopularProducts',
  component: SectionPopularProducts,
  parameters: {
    layout: 'fullscreen',
    // Configuring a11y tests
    a11y: {
      config: {
        rules: [
          {
            // Ensure all images have alt text
            id: 'image-alt',
            enabled: true
          },
          {
            // Ensure proper color contrast
            id: 'color-contrast',
            enabled: true
          }
        ]
      }
    },
  },
  // Add component-level argTypes
  argTypes: {
    onAddToCart: { action: 'addToCart' },
    title: {
      control: 'text',
      description: 'Section title',
    },
    products: {
      control: 'object',
      description: 'Array of product data',
    },
    columns: {
      control: 'object',
      description: 'Number of columns at different breakpoints',
    },
    showViewAll: {
      control: 'boolean',
      description: 'Show the "View All" link',
    },
    viewAllUrl: {
      control: 'text',
      description: 'URL for the "View All" link',
    },
    addingToCartIds: {
      control: 'object',
      description: 'IDs of products currently being added to cart',
    },
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="bg-gray-50 min-h-screen">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof SectionPopularProducts>;

/**
 * Default view with all products
 */
export const Default: Story = {
  args: {
    ...defaultFixture
  },
};

/**
 * Empty state when no products are available
 */
export const Empty: Story = {
  args: {
    ...emptyFixture
  },
};

/**
 * State when products are being added to cart
 */
export const Loading: Story = {
  args: {
    ...loadingFixture
  },
};

/**
 * Section with custom title component
 */
export const CustomTitle: Story = {
  args: {
    ...customTitleFixture
  },
};

/**
 * Featured products section with different column layout
 */
export const Featured: Story = {
  args: {
    ...featuredFixture
  },
};

/**
 * Mobile view (2 columns)
 */
export const Mobile: Story = {
  args: {
    ...defaultFixture
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

/**
 * Tablet view (3 columns)
 */
export const Tablet: Story = {
  args: {
    ...defaultFixture
  },
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
};

/**
 * Interactive example 
 */
export const Interactive: Story = {
  args: {
    ...defaultFixture
  },
};