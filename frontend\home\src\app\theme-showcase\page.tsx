"use client"

import { useState, useEffect } from 'react';

export default function ThemeShowcase() {
    const [isDark, setIsDark] = useState(false);

    // ตรวจสอบโหมดธีมเมื่อโหลดหน้า
    useEffect(() => {
        // ตรวจสอบว่า localStorage มีการบันทึกโหมดธีมไว้หรือไม่
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            setIsDark(true);
            document.documentElement.classList.add('dark');
        } else {
            setIsDark(false);
            document.documentElement.classList.remove('dark');
        }
    }, []);

    const toggleDarkMode = () => {
        if (isDark) {
            document.documentElement.classList.remove('dark');
            localStorage.setItem('theme', 'light');
            setIsDark(false);
        } else {
            document.documentElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
            setIsDark(true);
        }
    };

    return (
        <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
            <header className="bg-primary text-primary-foreground p-4 transition-colors duration-300">
                <div className="container mx-auto flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                        <div className="w-10 h-10 bg-secondary rounded-md flex items-center justify-center">
                            <span className="text-secondary-foreground font-bold">อฟ</span>
                        </div>
                        <h1 className="text-xl font-bold">ศูนย์รวมอุปกรณ์ไฟฟ้า</h1>
                    </div>
                    <div className="flex items-center space-x-4">
                        {/* ปุ่มสลับธีม แบบเพิ่มไอคอน */}
                        <button
                            onClick={toggleDarkMode}
                            className="p-2 rounded-md bg-muted text-muted-foreground hover:bg-muted/90 flex items-center space-x-2 transition-colors duration-300"
                            aria-label="สลับธีม"
                        >
                            {isDark ? (
                                <>
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd"></path>
                                    </svg>
                                    <span>โหมดสว่าง</span>
                                </>
                            ) : (
                                <>
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                                    </svg>
                                    <span>โหมดมืด</span>
                                </>
                            )}
                        </button>

                        <button className="p-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90 transition-colors duration-300">
                            เข้าสู่ระบบ
                        </button>
                    </div>
                </div>
            </header>

            <div className="container mx-auto py-6 px-4">
                <div className="mb-8 bg-card p-6 rounded-lg border shadow-sm transition-colors duration-300">
                    <h2 className="text-xl font-bold mb-4">ตัวเลือกธีม</h2>

                    <div className="flex flex-col sm:flex-row gap-4">
                        <button
                            onClick={() => {
                                document.documentElement.classList.remove('dark');
                                localStorage.setItem('theme', 'light');
                                setIsDark(false);
                            }}
                            className={`px-4 py-3 rounded-lg flex items-center space-x-2 ${!isDark
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted text-muted-foreground'
                                } transition-colors duration-300`}
                        >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd"></path>
                            </svg>
                            <span>โหมดสว่าง</span>
                        </button>

                        <button
                            onClick={() => {
                                document.documentElement.classList.add('dark');
                                localStorage.setItem('theme', 'dark');
                                setIsDark(true);
                            }}
                            className={`px-4 py-3 rounded-lg flex items-center space-x-2 ${isDark
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted text-muted-foreground'
                                } transition-colors duration-300`}
                        >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                            <span>โหมดมืด</span>
                        </button>

                        <button
                            onClick={() => {
                                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                                if (prefersDark) {
                                    document.documentElement.classList.add('dark');
                                    setIsDark(true);
                                } else {
                                    document.documentElement.classList.remove('dark');
                                    setIsDark(false);
                                }
                                localStorage.removeItem('theme');
                            }}
                            className="px-4 py-3 rounded-lg flex items-center space-x-2 bg-muted text-muted-foreground hover:bg-muted/90 transition-colors duration-300"
                        >
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd"></path>
                            </svg>
                            <span>ใช้ค่าระบบ</span>
                        </button>
                    </div>

                    <div className="mt-4 text-sm text-muted-foreground">
                        <p>สถานะปัจจุบัน: <span className="font-medium">{isDark ? 'โหมดมืด' : 'โหมดสว่าง'}</span></p>
                    </div>
                </div>

                {/* แสดงตัวอย่างการใช้ background-secondary */}
                <div className="mb-8 bg-[hsl(var(--background-secondary))] p-6 rounded-lg border transition-colors duration-300">
                    <h2 className="text-xl font-bold mb-4 text-[hsl(var(--background-secondary-foreground))]">พื้นหลังรอง (Background Secondary)</h2>
                    <p className="text-[hsl(var(--background-secondary-foreground))]">
                        ส่วนนี้ใช้พื้นหลังรอง (background-secondary) ซึ่งมีความแตกต่างเล็กน้อยจากพื้นหลังหลัก สามารถใช้สำหรับแยกส่วนต่างๆ ของเนื้อหาได้
                    </p>
                </div>

                <section className="mb-12">
                    <h2 className="text-2xl font-bold mb-6">ตัวอย่างธีมและสี</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {/* สีพื้นฐาน */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีพื้นฐาน</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-background border rounded flex items-center justify-center transition-colors duration-300">
                                    background
                                </div>
                                <div className="h-12 bg-foreground text-background rounded flex items-center justify-center transition-colors duration-300">
                                    foreground
                                </div>
                            </div>
                        </div>

                        {/* พื้นหลังรอง */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">พื้นหลังรอง</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-[hsl(var(--background-secondary))] border rounded flex items-center justify-center transition-colors duration-300">
                                    background-secondary
                                </div>
                                <div className="h-12 bg-[hsl(var(--background-secondary-foreground))] text-background rounded flex items-center justify-center transition-colors duration-300">
                                    background-secondary-foreground
                                </div>
                            </div>
                        </div>

                        {/* สีการ์ด */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีการ์ด</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-card border rounded flex items-center justify-center transition-colors duration-300">
                                    card
                                </div>
                                <div className="h-12 bg-card-foreground text-background rounded flex items-center justify-center transition-colors duration-300">
                                    card-foreground
                                </div>
                            </div>
                        </div>

                        {/* สีป็อปโอเวอร์ */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีป็อปโอเวอร์</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-popover border rounded flex items-center justify-center transition-colors duration-300">
                                    popover
                                </div>
                                <div className="h-12 bg-popover-foreground text-background rounded flex items-center justify-center transition-colors duration-300">
                                    popover-foreground
                                </div>
                            </div>
                        </div>

                        {/* สีหลัก */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีหลัก</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-primary text-primary-foreground rounded flex items-center justify-center transition-colors duration-300">
                                    primary
                                </div>
                                <div className="h-12 bg-primary-foreground text-primary rounded flex items-center justify-center transition-colors duration-300">
                                    primary-foreground
                                </div>
                            </div>
                        </div>

                        {/* สีรอง */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีรอง</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-secondary text-secondary-foreground rounded flex items-center justify-center transition-colors duration-300">
                                    secondary
                                </div>
                                <div className="h-12 bg-secondary-foreground text-secondary rounded flex items-center justify-center transition-colors duration-300">
                                    secondary-foreground
                                </div>
                            </div>
                        </div>

                        {/* สีกลาง */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีกลาง</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-muted text-muted-foreground rounded flex items-center justify-center transition-colors duration-300">
                                    muted
                                </div>
                                <div className="h-12 bg-muted-foreground text-muted rounded flex items-center justify-center transition-colors duration-300">
                                    muted-foreground
                                </div>
                            </div>
                        </div>

                        {/* สีเน้น */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีเน้น</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-accent text-accent-foreground rounded flex items-center justify-center transition-colors duration-300">
                                    accent
                                </div>
                                <div className="h-12 bg-accent-foreground text-accent rounded flex items-center justify-center transition-colors duration-300">
                                    accent-foreground
                                </div>
                            </div>
                        </div>

                        {/* สีแจ้งเตือน */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีแจ้งเตือน</h3>
                            <div className="space-y-2">
                                <div className="h-12 bg-destructive text-destructive-foreground rounded flex items-center justify-center transition-colors duration-300">
                                    destructive
                                </div>
                                <div className="h-12 bg-destructive-foreground text-destructive rounded flex items-center justify-center transition-colors duration-300">
                                    destructive-foreground
                                </div>
                            </div>
                        </div>

                        {/* สีอินเทอร์เฟซ */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีอินเทอร์เฟซ</h3>
                            <div className="grid grid-cols-2 gap-2">
                                <div className="h-12 bg-[hsl(var(--border))] rounded flex items-center justify-center transition-colors duration-300">
                                    border
                                </div>
                                <div className="h-12 bg-[hsl(var(--input))] rounded flex items-center justify-center transition-colors duration-300">
                                    input
                                </div>
                                <div className="h-12 bg-[hsl(var(--ring))] text-white rounded flex items-center justify-center transition-colors duration-300">
                                    ring
                                </div>
                                <div className="h-12 rounded border-2 border-[hsl(var(--ring))] flex items-center justify-center">
                                    radius: var(--radius)
                                </div>
                            </div>
                        </div>

                        {/* Sidebar */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">Sidebar</h3>
                            <div className="grid grid-cols-2 gap-2">
                                <div className="h-12 bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))] rounded flex items-center justify-center transition-colors duration-300">
                                    sidebar-background
                                </div>
                                <div className="h-12 bg-[hsl(var(--sidebar-foreground))] text-[hsl(var(--sidebar-background))] rounded flex items-center justify-center transition-colors duration-300">
                                    sidebar-foreground
                                </div>
                                <div className="h-12 bg-[hsl(var(--sidebar-primary))] text-[hsl(var(--sidebar-primary-foreground))] rounded flex items-center justify-center transition-colors duration-300">
                                    sidebar-primary
                                </div>
                                <div className="h-12 bg-[hsl(var(--sidebar-primary-foreground))] text-[hsl(var(--sidebar-primary))] rounded flex items-center justify-center transition-colors duration-300">
                                    sidebar-primary-foreground
                                </div>
                                <div className="h-12 bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))] rounded flex items-center justify-center transition-colors duration-300">
                                    sidebar-accent
                                </div>
                                <div className="h-12 bg-[hsl(var(--sidebar-accent-foreground))] text-[hsl(var(--sidebar-accent))] rounded flex items-center justify-center transition-colors duration-300">
                                    sidebar-accent-foreground
                                </div>
                            </div>
                        </div>

                        {/* Chart Colors */}
                        <div className="p-4 border rounded-lg transition-colors duration-300">
                            <h3 className="font-medium mb-3">สีกราฟ (Chart)</h3>
                            <div className="grid grid-cols-2 gap-2">
                                <div className="h-12 rounded flex items-center justify-center text-white transition-colors duration-300" style={{ background: 'hsl(var(--chart-1))' }}>
                                    chart-1
                                </div>
                                <div className="h-12 rounded flex items-center justify-center text-white transition-colors duration-300" style={{ background: 'hsl(var(--chart-2))' }}>
                                    chart-2
                                </div>
                                <div className="h-12 rounded flex items-center justify-center text-white transition-colors duration-300" style={{ background: 'hsl(var(--chart-3))' }}>
                                    chart-3
                                </div>
                                <div className="h-12 rounded flex items-center justify-center text-white transition-colors duration-300" style={{ background: 'hsl(var(--chart-4))' }}>
                                    chart-4
                                </div>
                                <div className="h-12 rounded flex items-center justify-center text-white transition-colors duration-300" style={{ background: 'hsl(var(--chart-5))' }}>
                                    chart-5
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section className="mb-12">
                    <h2 className="text-2xl font-bold mb-6">ตัวอย่าง UI Components</h2>

                    {/* ปุ่ม */}
                    <div className="mb-8">
                        <h3 className="text-xl font-medium mb-4">ปุ่ม</h3>
                        <div className="flex flex-wrap gap-4">
                            <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors duration-300">
                                ปุ่มหลัก
                            </button>
                            <button className="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90 transition-colors duration-300">
                                ปุ่มรอง
                            </button>
                            <button className="bg-accent text-accent-foreground px-4 py-2 rounded-md hover:bg-accent/90 transition-colors duration-300">
                                ปุ่มเน้น
                            </button>
                            <button className="bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors duration-300">
                                ปุ่มลบ
                            </button>
                            <button className="border bg-background px-4 py-2 rounded-md hover:bg-muted transition-colors duration-300">
                                ปุ่มโปร่งใส
                            </button>
                        </div>
                    </div>

                    {/* Sidebar Example */}
                    <div className="mb-8">
                        <h3 className="text-xl font-medium mb-4">ตัวอย่าง Sidebar</h3>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="bg-[hsl(var(--sidebar-background))] text-[hsl(var(--sidebar-foreground))] p-4 rounded-lg border border-[hsl(var(--sidebar-border))] transition-colors duration-300">
                                <div className="mb-4">
                                    <div className="text-lg font-medium mb-4">เมนูหลัก</div>
                                    <ul className="space-y-2">
                                        <li className="bg-[hsl(var(--sidebar-primary))] text-[hsl(var(--sidebar-primary-foreground))] p-2 rounded-md">หน้าหลัก</li>
                                        <li className="p-2 hover:bg-[hsl(var(--sidebar-accent))] hover:text-[hsl(var(--sidebar-accent-foreground))] rounded-md transition-colors duration-300">สินค้า</li>
                                        <li className="p-2 hover:bg-[hsl(var(--sidebar-accent))] hover:text-[hsl(var(--sidebar-accent-foreground))] rounded-md transition-colors duration-300">โปรโมชั่น</li>
                                        <li className="p-2 hover:bg-[hsl(var(--sidebar-accent))] hover:text-[hsl(var(--sidebar-accent-foreground))] rounded-md transition-colors duration-300">ติดต่อเรา</li>
                                    </ul>
                                </div>
                                <div>
                                    <div className="text-lg font-medium mb-4">หมวดหมู่</div>
                                    <ul className="space-y-2">
                                        <li className="p-2 hover:bg-[hsl(var(--sidebar-accent))] hover:text-[hsl(var(--sidebar-accent-foreground))] rounded-md transition-colors duration-300">หลอดไฟ LED</li>
                                        <li className="p-2 hover:bg-[hsl(var(--sidebar-accent))] hover:text-[hsl(var(--sidebar-accent-foreground))] rounded-md transition-colors duration-300">สายไฟ</li>
                                        <li className="p-2 hover:bg-[hsl(var(--sidebar-accent))] hover:text-[hsl(var(--sidebar-accent-foreground))] rounded-md transition-colors duration-300">ปลั๊กไฟ</li>
                                        <li className="p-2 hover:bg-[hsl(var(--sidebar-accent))] hover:text-[hsl(var(--sidebar-accent-foreground))] rounded-md transition-colors duration-300">อื่นๆ</li>
                                    </ul>
                                </div>
                            </div>

                            <div className="md:col-span-3 bg-card p-4 rounded-lg border transition-colors duration-300">
                                <h4 className="text-lg font-medium mb-4">เนื้อหาหลัก</h4>
                                <p className="text-card-foreground mb-4">ส่วนนี้แสดงตัวอย่างการใช้งาน Sidebar ร่วมกับเนื้อหาหลัก โดยใช้สีต่างๆ ที่กำหนดไว้ใน theme</p>
                                <div className="flex flex-wrap gap-2">
                                    <span className="inline-block px-3 py-1 bg-[hsl(var(--sidebar-primary))] text-[hsl(var(--sidebar-primary-foreground))] rounded-full text-sm">หมวดหมู่ 1</span>
                                    <span className="inline-block px-3 py-1 bg-[hsl(var(--sidebar-accent))] text-[hsl(var(--sidebar-accent-foreground))] rounded-full text-sm">หมวดหมู่ 2</span>
                                    <span className="inline-block px-3 py-1 bg-secondary text-secondary-foreground rounded-full text-sm">หมวดหมู่ 3</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* การ์ด */}
                    <div className="mb-8">
                        <h3 className="text-xl font-medium mb-4">การ์ด</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {/* การ์ดสินค้าปกติ */}
                            <div className="bg-card rounded-lg shadow-sm overflow-hidden transition-colors duration-300">
                                <div className="h-40 bg-muted flex items-center justify-center transition-colors duration-300">
                                    <div className="w-20 h-20 bg-secondary rounded-full"></div>
                                </div>
                                <div className="p-4">
                                    <h3 className="font-medium text-card-foreground">หลอดไฟ LED 12W</h3>
                                    <div className="mt-1 text-muted-foreground text-sm">หลอดไฟประหยัดพลังงาน</div>
                                    <div className="mt-4 flex justify-between items-center">
                                        <span className="text-foreground font-bold">199.00 บาท</span>
                                        <button className="bg-primary text-primary-foreground px-2 py-1 rounded-md text-sm">
                                            เพิ่มลงตะกร้า
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* การ์ดสินค้าที่มีส่วนลด */}
                            <div className="bg-card rounded-lg shadow-sm overflow-hidden relative transition-colors duration-300">
                                <div className="bg-destructive text-destructive-foreground px-2 py-1 absolute right-2 top-2 rounded-md text-sm font-bold">
                                    -20%
                                </div>
                                <div className="h-40 bg-muted flex items-center justify-center transition-colors duration-300">
                                    <div className="w-20 h-20 bg-primary rounded-full"></div>
                                </div>
                                <div className="p-4">
                                    <h3 className="font-medium text-card-foreground">สายไฟ VCT 2x1.5</h3>
                                    <div className="mt-1 text-muted-foreground text-sm">สายไฟคุณภาพสูง</div>
                                    <div className="mt-4 flex justify-between items-center">
                                        <div>
                                            <span className="text-muted-foreground line-through text-sm mr-2">
                                                250.00 บาท
                                            </span>
                                            <span className="text-foreground font-bold">200.00 บาท</span>
                                        </div>
                                        <button className="bg-primary text-primary-foreground px-2 py-1 rounded-md text-sm">
                                            เพิ่มลงตะกร้า
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* การ์ดสินค้า ECO LIFE */}
                            <div className="bg-card rounded-lg shadow-sm overflow-hidden relative transition-colors duration-300">
                                <div className="bg-accent-foreground text-white px-2 py-1 absolute right-2 top-2 rounded-md text-sm font-bold">
                                    ECO
                                </div>
                                <div className="h-40 bg-muted flex items-center justify-center transition-colors duration-300">
                                    <div className="w-20 h-20 bg-accent rounded-full"></div>
                                </div>
                                <div className="p-4">
                                    <h3 className="font-medium text-card-foreground">โซล่าเซลล์ 10W</h3>
                                    <div className="mt-1 text-muted-foreground text-sm">พลังงานสะอาดเพื่อโลก</div>
                                    <div className="mt-4 flex justify-between items-center">
                                        <span className="text-foreground font-bold">499.00 บาท</span>
                                        <button className="bg-primary text-primary-foreground px-2 py-1 rounded-md text-sm">
                                            เพิ่มลงตะกร้า
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* ส่วนโปรโมชั่น */}
                    <div className="mb-8">
                        <h3 className="text-xl font-medium mb-4">ส่วนโปรโมชั่น</h3>

                        {/* Flash Sale */}
                        <div className="mb-4 p-4 rounded-lg bg-destructive text-destructive-foreground transition-colors duration-300">
                            <div className="flex items-center mb-4">
                                <svg className="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"></path>
                                </svg>
                                <h2 className="text-xl font-bold">FLASH SALE</h2>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="bg-background rounded-md p-3 text-foreground transition-colors duration-300">
                                    <div className="font-medium">หลอดไฟ LED 5W</div>
                                    <div className="flex justify-between items-center mt-2">
                                        <div>
                                            <span className="text-muted-foreground line-through text-sm mr-2">129.00</span>
                                            <span className="text-destructive font-bold">99.00</span>
                                        </div>
                                        <button className="bg-primary text-primary-foreground px-2 py-1 rounded-md text-xs">
                                            ซื้อเลย
                                        </button>
                                    </div>
                                </div>
                                <div className="bg-background rounded-md p-3 text-foreground transition-colors duration-300">
                                    <div className="font-medium">เซอร์กิตเบรกเกอร์ 10A</div>
                                    <div className="flex justify-between items-center mt-2">
                                        <div>
                                            <span className="text-muted-foreground line-through text-sm mr-2">250.00</span>
                                            <span className="text-destructive font-bold">189.00</span>
                                        </div>
                                        <button className="bg-primary text-primary-foreground px-2 py-1 rounded-md text-xs">
                                            ซื้อเลย
                                        </button>
                                    </div>
                                </div>
                                <div className="bg-background rounded-md p-3 text-foreground transition-colors duration-300">
                                    <div className="font-medium">ปลั๊กไฟ 4 ช่อง</div>
                                    <div className="flex justify-between items-center mt-2">
                                        <div>
                                            <span className="text-muted-foreground line-through text-sm mr-2">399.00</span>
                                            <span className="text-destructive font-bold">299.00</span>
                                        </div>
                                        <button className="bg-primary text-primary-foreground px-2 py-1 rounded-md text-xs">
                                            ซื้อเลย
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* ECO LIFE */}
                        <div className="p-4 rounded-lg bg-accent transition-colors duration-300">
                            <div className="flex items-center mb-4">
                                <svg className="w-6 h-6 text-accent-foreground mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"></path>
                                </svg>
                                <h2 className="text-xl font-bold text-accent-foreground">ECO LIFE สินค้าเพื่อโลกสีเขียว</h2>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div className="bg-background rounded-md p-3 text-foreground shadow-sm transition-colors duration-300">
                                    <div className="font-medium">หลอด LED ประหยัดไฟ</div>
                                    <div className="text-accent-foreground font-bold mt-2">169.00</div>
                                </div>
                                <div className="bg-background rounded-md p-3 text-foreground shadow-sm transition-colors duration-300">
                                    <div className="font-medium">โคมไฟโซล่าเซลล์</div>
                                    <div className="text-accent-foreground font-bold mt-2">599.00</div>
                                </div>
                                <div className="bg-background rounded-md p-3 text-foreground shadow-sm transition-colors duration-300">
                                    <div className="font-medium">ปลั๊กไฟอัจฉริยะ</div>
                                    <div className="text-accent-foreground font-bold mt-2">799.00</div>
                                </div>
                                <div className="bg-background rounded-md p-3 text-foreground shadow-sm transition-colors duration-300">
                                    <div className="font-medium">รีเลย์ประหยัดพลังงาน</div>
                                    <div className="text-accent-foreground font-bold mt-2">459.00</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* ฟอร์ม */}
                    <div className="mb-8">
                        <h3 className="text-xl font-medium mb-4">ฟอร์ม</h3>
                        <div className="bg-card p-6 rounded-lg border transition-colors duration-300">
                            <h4 className="text-lg font-medium mb-4">ติดต่อเรา</h4>
                            <form className="space-y-4">
                                <div>
                                    <label htmlFor="name" className="block text-sm font-medium mb-1">ชื่อ-นามสกุล</label>
                                    <input
                                        type="text"
                                        id="name"
                                        className="w-full px-3 py-2 border bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))]"
                                        placeholder="ระบุชื่อ-นามสกุล"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="email" className="block text-sm font-medium mb-1">อีเมล</label>
                                    <input
                                        type="email"
                                        id="email"
                                        className="w-full px-3 py-2 border bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))]"
                                        placeholder="<EMAIL>"
                                    />
                                </div>
                                <div>
                                    <label htmlFor="message" className="block text-sm font-medium mb-1">ข้อความ</label>
                                    <textarea
                                        id="message"
                                        rows={4}
                                        className="w-full px-3 py-2 border bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))]"
                                        placeholder="ระบุข้อความที่ต้องการติดต่อ"
                                    ></textarea>
                                </div>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        id="consent"
                                        className="mr-2 h-4 w-4 rounded border-input text-primary focus:ring-[hsl(var(--ring))]"
                                    />
                                    <label htmlFor="consent" className="text-sm">ยินยอมให้เก็บข้อมูลตาม <a href="#" className="text-primary hover:underline">นโยบายความเป็นส่วนตัว</a></label>
                                </div>
                                <div>
                                    <button
                                        type="submit"
                                        className="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors duration-300"
                                    >
                                        ส่งข้อความ
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    {/* แจ้งเตือน */}
                    <div className="mb-8">
                        <h3 className="text-xl font-medium mb-4">แจ้งเตือน</h3>
                        <div className="space-y-4">
                            <div className="bg-primary/10 border-l-4 border-primary p-4 rounded-r-md transition-colors duration-300">
                                <div className="flex">
                                    <svg className="w-5 h-5 text-primary mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                                    </svg>
                                    <div>
                                        <h4 className="text-sm font-medium text-primary">ข้อมูล</h4>
                                        <p className="text-sm">เรามีบริการจัดส่งฟรีเมื่อซื้อสินค้าครบ 1,000 บาท</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-success/10 border-l-4 border-success p-4 rounded-r-md transition-colors duration-300">
                                <div className="flex">
                                    <svg className="w-5 h-5 text-success mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                                    </svg>
                                    <div>
                                        <h4 className="text-sm font-medium text-success">สำเร็จ</h4>
                                        <p className="text-sm">เพิ่มสินค้าลงในตะกร้าเรียบร้อยแล้ว</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-error/10 border-l-4 border-[hsl(var(--error))] p-4 rounded-r-md transition-colors duration-300">
                                <div className="flex">
                                    <svg className="w-5 h-5 text-error mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                                    </svg>
                                    <div>
                                        <h4 className="text-sm font-medium text-error">ข้อผิดพลาด</h4>
                                        <p className="text-sm">ไม่สามารถทำรายการได้ กรุณาลองใหม่อีกครั้ง</p>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-warning/10 border-l-4 border-secondary p-4 rounded-r-md transition-colors duration-300">
                                <div className="flex">
                                    <svg className="w-5 h-5 text-warning mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                                    </svg>
                                    <div>
                                        <h4 className="text-sm font-medium text-warning">คำเตือน</h4>
                                        <p className="text-sm">สินค้าใกล้หมด! รีบสั่งซื้อก่อนสินค้าหมด</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* กราฟตัวอย่าง */}
                    <div className="mb-8">
                        <h3 className="text-xl font-medium mb-4">ตัวอย่างกราฟ</h3>
                        <div className="bg-card p-6 rounded-lg border transition-colors duration-300">
                            <div className="mb-4">
                                <h4 className="text-lg font-medium">ยอดขายรายเดือน</h4>
                                <p className="text-sm text-muted-foreground">แสดงตัวอย่างการใช้สีกราฟที่กำหนดไว้ใน theme</p>
                            </div>
                            <div className="h-60 border rounded-md flex items-end p-4 space-x-4">
                                <div className="h-3/5 w-12 bg-[hsl(var(--chart-1))] rounded-t"></div>
                                <div className="h-4/5 w-12 bg-[hsl(var(--chart-2))] rounded-t"></div>
                                <div className="h-2/5 w-12 bg-[hsl(var(--chart-3))] rounded-t"></div>
                                <div className="h-full w-12 bg-[hsl(var(--chart-4))] rounded-t"></div>
                                <div className="h-3/4 w-12 bg-[hsl(var(--chart-5))] rounded-t"></div>
                                <div className="h-1/2 w-12 bg-[hsl(var(--chart-1))] rounded-t"></div>
                            </div>
                            <div className="flex justify-between mt-2 text-sm text-muted-foreground">
                                <span>ม.ค.</span>
                                <span>ก.พ.</span>
                                <span>มี.ค.</span>
                                <span>เม.ย.</span>
                                <span>พ.ค.</span>
                                <span>มิ.ย.</span>
                            </div>
                            <div className="flex gap-4 mt-4 flex-wrap">
                                <div className="flex items-center">
                                    <div className="w-4 h-4 bg-[hsl(var(--chart-1))] rounded mr-2"></div>
                                    <span className="text-sm">หลอดไฟ</span>
                                </div>
                                <div className="flex items-center">
                                    <div className="w-4 h-4 bg-[hsl(var(--chart-2))] rounded mr-2"></div>
                                    <span className="text-sm">สายไฟ</span>
                                </div>
                                <div className="flex items-center">
                                    <div className="w-4 h-4 bg-[hsl(var(--chart-3))] rounded mr-2"></div>
                                    <span className="text-sm">ปลั๊กไฟ</span>
                                </div>
                                <div className="flex items-center">
                                    <div className="w-4 h-4 bg-[hsl(var(--chart-4))] rounded mr-2"></div>
                                    <span className="text-sm">สวิตช์</span>
                                </div>
                                <div className="flex items-center">
                                    <div className="w-4 h-4 bg-[hsl(var(--chart-5))] rounded mr-2"></div>
                                    <span className="text-sm">อุปกรณ์เสริม</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <footer className="bg-primary text-primary-foreground py-8 transition-colors duration-300">
                <div className="container mx-auto px-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div>
                            <h4 className="text-lg font-bold mb-4">ศูนย์รวมอุปกรณ์ไฟฟ้า</h4>
                            <p className="text-sm">ศูนย์รวมจำหน่ายอุปกรณ์ไฟฟ้า เครื่องใช้ไฟฟ้า และอุปกรณ์ส่องสว่างครบวงจร</p>
                        </div>
                        <div>
                            <h4 className="text-lg font-bold mb-4">บริการลูกค้า</h4>
                            <ul className="space-y-2 text-sm">
                                <li><a href="#" className="hover:underline">วิธีการสั่งซื้อ</a></li>
                                <li><a href="#" className="hover:underline">การชำระเงิน</a></li>
                                <li><a href="#" className="hover:underline">การจัดส่ง</a></li>
                                <li><a href="#" className="hover:underline">เงื่อนไขการรับประกัน</a></li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="text-lg font-bold mb-4">เกี่ยวกับเรา</h4>
                            <ul className="space-y-2 text-sm">
                                <li><a href="#" className="hover:underline">เกี่ยวกับบริษัท</a></li>
                                <li><a href="#" className="hover:underline">ข่าวสาร</a></li>
                                <li><a href="#" className="hover:underline">ร่วมงานกับเรา</a></li>
                                <li><a href="#" className="hover:underline">ติดต่อเรา</a></li>
                            </ul>
                        </div>
                        <div>
                            <h4 className="text-lg font-bold mb-4">ติดตามเรา</h4>
                            <div className="flex space-x-4">
                                <a href="#" className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-300">
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"></path>
                                    </svg>
                                </a>
                                <a href="#" className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-300">
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"></path>
                                    </svg>
                                </a>
                                <a href="#" className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-300">
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                                    </svg>
                                </a>
                                <a href="#" className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-white/20 transition-colors duration-300">
                                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path>
                                    </svg>
                                </a>
                            </div>
                            <div className="mt-4">
                                <p className="text-sm">รับข่าวสารและโปรโมชั่น</p>
                                <div className="flex mt-2">
                                    <input
                                        type="email"
                                        placeholder="อีเมลของคุณ"
                                        className="px-3 py-2 bg-white/10 rounded-l-md text-white placeholder-white/50 focus:outline-none focus:ring-1 focus:ring-[hsl(var(--ring))]"
                                    />
                                    <button className="bg-secondary text-secondary-foreground px-3 py-2 rounded-r-md">
                                        ติดตาม
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="border-t border-white/10 mt-8 pt-6 text-sm text-center text-white/60">
                        &copy; {new Date().getFullYear()} ศูนย์รวมอุปกรณ์ไฟฟ้า. สงวนลิขสิทธิ์.
                    </div>
                </div>
            </footer>
        </div>
    )
}