import type { <PERSON>a, StoryObj } from '@storybook/react';
import SectionHeroCarousel from '../section-hero-carousel';
import { carouselFixtures } from '../__fixtures__/section-hero.fixtures';

const meta: Meta<typeof SectionHeroCarousel> = {
  title: 'UI/Section/SectionHeroCarousel',
  component: SectionHeroCarousel,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
    // เพิ่ม A11y testing
    a11y: {
        config: {
          rules: [
            {
              // ตรวจสอบความคมชัดของสี
              id: 'color-contrast',
              enabled: true
            }
          ]
        }
      }
    },
    argTypes: {
      slides: {
        control: 'object',
        description: 'ข้อมูล slides ทั้งหมด'
      },
      height: {
        control: 'select',
        options: ['sm', 'md', 'lg', 'xl', 'full'],
        description: 'ความสูงของ hero section'
      },
      backgroundStyle: {
        control: 'select',
        options: ['wave', 'flat', 'gradient', 'none'],
        description: 'การแสดงผลพื้นหลัง'
      },
      imagePosition: {
        control: 'select',
        options: ['right', 'left', 'background', 'none'],
        description: 'การแสดงผลรูปภาพ'
      },
      autoplay: {
        control: 'boolean',
        description: 'เปิดใช้ autoplay'
      },
      showNavigation: {
        control: 'boolean',
        description: 'แสดงปุ่มนำทาง'
      },
      showPagination: {
        control: 'boolean',
        description: 'แสดงจุดบอกตำแหน่ง'
      },
      contentPosition: {
        control: 'select',
        options: ['left', 'center', 'right'],
        description: 'ตำแหน่งของเนื้อหา'
      },
      mobileLayout: {
        control: 'select',
        options: ['stack', 'compact'],
        description: 'การแสดงผลบนอุปกรณ์พกพา'
      }
    }
  };
  
  export default meta;
  type Story = StoryObj<typeof SectionHeroCarousel>;
  
  // Default Carousel Story
  export const Default: Story = {
    args: {
      slides: carouselFixtures,
      height: 'lg',
      backgroundStyle: 'wave',
      imagePosition: 'right',
      autoplay: true,
      showNavigation: true,
      showPagination: true,
      contentPosition: 'left',
      mobileLayout: 'stack'
    }
  };
  
  // Gradient Background Carousel
  export const GradientBackground: Story = {
    args: {
      slides: carouselFixtures,
      height: 'lg',
      backgroundStyle: 'gradient',
      imagePosition: 'right',
      autoplay: true,
      showNavigation: true,
      showPagination: true,
      contentPosition: 'left',
      mobileLayout: 'stack'
    }
  };
  
  // Left Image Carousel
  export const LeftImage: Story = {
    args: {
      slides: carouselFixtures,
      height: 'lg',
      backgroundStyle: 'flat',
      imagePosition: 'left',
      autoplay: true,
      showNavigation: true,
      showPagination: true,
      contentPosition: 'right',
      mobileLayout: 'stack'
    }
  };
  
  // Centered Content Carousel
  export const CenteredContent: Story = {
    args: {
      slides: carouselFixtures,
      height: 'lg',
      backgroundStyle: 'gradient',
      imagePosition: 'none',
      autoplay: true,
      showNavigation: true,
      showPagination: true,
      contentPosition: 'center',
      mobileLayout: 'stack'
    }
  };
  
  // Fullscreen Carousel
  export const Fullscreen: Story = {
    args: {
      slides: carouselFixtures,
      height: 'full',
      backgroundStyle: 'wave',
      imagePosition: 'right',
      autoplay: true,
      showNavigation: true,
      showPagination: true,
      contentPosition: 'left',
      mobileLayout: 'stack'
    }
  };
  
  // Mobile Compact Layout
  export const MobileCompact: Story = {
    args: {
      slides: carouselFixtures,
      height: 'md',
      backgroundStyle: 'wave',
      imagePosition: 'right',
      autoplay: true,
      showNavigation: true,
      showPagination: true,
      contentPosition: 'left',
      mobileLayout: 'compact'
    }
  };
  
  // No Autoplay
  export const NoAutoplay: Story = {
    args: {
      slides: carouselFixtures,
      height: 'lg',
      backgroundStyle: 'wave',
      imagePosition: 'right',
      autoplay: false,
      showNavigation: true,
      showPagination: true,
      contentPosition: 'left',
      mobileLayout: 'stack'
    }
  };