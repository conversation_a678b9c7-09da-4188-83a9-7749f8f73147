'use client';

import * as React from 'react';
import { useState, useCallback } from 'react';
import { useTranslation } from '@/components/Providers/i18n-provider';
import { useLanguageChange } from '@/hooks/useLanguageChange';
import { <PERSON><PERSON>, Star, Trash, Eye, Edit, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Row } from '@tanstack/react-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import TableDynamic from './table-dynamic';
import type { ColumnConfig, DataRecord } from './types';

// Sample data for the demo
const generateData = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `item-${i + 1}`,
    name: `Product ${i + 1}`,
    category: ['Electronics', 'Clothing', 'Food', 'Furniture', 'Books'][
      Math.floor(Math.random() * 5)
    ],
    price: Math.floor(Math.random() * 1000) + 10,
    stock: Math.floor(Math.random() * 100),
    rating: (Math.random() * 5).toFixed(1),
    status: ['In Stock', 'Low Stock', 'Out of Stock'][Math.floor(Math.random() * 3)],
    createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
    updatedAt: new Date(),
  }));
};

/**
 * TableDynamicDemo component
 *
 * Demonstrates various features of the TableDynamic component
 */
export const TableDynamicDemo: React.FC = () => {
  const { t } = useTranslation();
  const currentLang = useLanguageChange();

  // States for demo
  const [data, setData] = useState(() => generateData(100));
  const [isLoading, setIsLoading] = useState(false);
  const [isLargeDataset, setIsLargeDataset] = useState(false);
  const [demoError, setDemoError] = useState<string | null>(null);

  // Regenerate data for demo
  const handleRegenerateData = useCallback(() => {
    setIsLoading(true);
    setTimeout(() => {
      setData(generateData(isLargeDataset ? 5000 : 100));
      setIsLoading(false);
    }, 1000);
  }, [isLargeDataset]);

  // Toggle large dataset
  const handleToggleLargeDataset = useCallback(() => {
    setIsLoading(true);
    setIsLargeDataset((prev) => !prev);
    setTimeout(() => {
      setData(generateData(!isLargeDataset ? 5000 : 100));
      setIsLoading(false);
    }, 1000);
  }, [isLargeDataset]);

  // Toggle error state
  const handleToggleError = useCallback(() => {
    setDemoError((prev) => (prev ? null : 'An error occurred while loading data'));
  }, []);

  // Handle row click
  const handleRowClick = useCallback((row: Row<DataRecord>) => {
    console.warn('Row clicked:', row.original);
  }, []);

  // Column definitions
  const columns: ColumnConfig[] = [
    {
      id: 'name',
      header: 'Product Name',
      accessorKey: 'name',
      enableSorting: true,
      enableFiltering: true,
      cell: ({ row }) => <div className="font-medium">{row.getValue('name')}</div>,
    },
    {
      id: 'category',
      header: 'Category',
      accessorKey: 'category',
      enableSorting: true,
      enableFiltering: true,
    },
    {
      id: 'price',
      header: 'Price',
      accessorKey: 'price',
      enableSorting: true,
      enableFiltering: true,
      cell: ({ row }) => <div className="text-right">${row.getValue('price')}</div>,
      aggregationFn: (values: unknown[]) => {
        return values
          .filter((val): val is number => typeof val === 'number')
          .reduce((acc, val) => acc + val, 0);
      },
      aggregationFormatter: (value: unknown) => `$${(value as number).toFixed(2)}`,
      headerTooltip: 'Total sum on hover',
    },
    {
      id: 'stock',
      header: 'Stock',
      accessorKey: 'stock',
      enableSorting: true,
      enableFiltering: true,
      aggregationFn: (values: unknown[]) => {
        return values
          .filter((val): val is number => typeof val === 'number')
          .reduce((acc, val) => acc + val, 0);
      },
      aggregationFormatter: (value: unknown) => `${value as number} units`,
    },
    {
      id: 'rating',
      header: 'Rating',
      accessorKey: 'rating',
      enableSorting: true,
      enableFiltering: true,
      cell: ({ row }) => (
        <div className="flex items-center">
          <Star className="mr-1 h-4 w-4 text-yellow-400" />
          <span>{row.getValue('rating')}</span>
        </div>
      ),
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
      enableSorting: true,
      enableFiltering: true,
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return (
          <Badge
            variant={
              status === 'In Stock'
                ? 'default'
                : status === 'Low Stock'
                  ? 'secondary'
                  : 'destructive'
            }
          >
            {status}
          </Badge>
        );
      },
    },
    {
      id: 'createdAt',
      header: 'Created At',
      accessorKey: 'createdAt',
      enableSorting: true,
      enableFiltering: true,
      cell: ({ row }) => {
        const date = row.getValue('createdAt') as Date;
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      accessorKey: 'id',
      enableSorting: false,
      enableFiltering: false,
      cell: ({ row }) => {
        const id = row.getValue('id') as string;
        return (
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={(e) => {
                e.stopPropagation();
                console.warn('View item:', id);
              }}
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={(e) => {
                e.stopPropagation();
                console.warn('Edit item:', id);
              }}
            >
              <Edit className="h-4 w-4" />
              <span className="sr-only">Edit</span>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">More</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => navigator.clipboard.writeText(id)}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy ID
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-destructive focus:text-destructive"
                  onClick={() => console.warn('Delete item:', id)}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-8 p-6">
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold">TableDynamic Demo</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRegenerateData}>
            Regenerate Data
          </Button>
          <Button
            variant={isLargeDataset ? 'default' : 'outline'}
            onClick={handleToggleLargeDataset}
          >
            {isLargeDataset ? 'Use Small Dataset' : 'Use Large Dataset'}
          </Button>
          <Button variant={demoError ? 'destructive' : 'outline'} onClick={handleToggleError}>
            {demoError ? 'Clear Error' : 'Show Error'}
          </Button>
        </div>
      </div>

      <div>
        <h3 className="mb-4 text-lg font-semibold">Dynamic Table with Full Features</h3>
        <TableDynamic
          data={data}
          columns={columns}
          loading={isLoading}
          error={demoError}
          virtualized={isLargeDataset}
          height={500}
          onRowClick={handleRowClick}
          pagination={{
            enabled: true,
            initialPageSize: 10,
            showPageSizeSelector: true,
            showPageNavigator: true,
            showPageInfo: true,
          }}
          sorting={{
            enabled: true,
            maxSortColumns: 3,
          }}
          filtering={{
            enabled: true,
            showGlobalFilter: true,
            debounce: true,
          }}
          columnVisibility={{
            enabled: true,
            showToggle: true,
          }}
          header={{
            show: true,
            sticky: true,
            showColumnSeparators: true,
          }}
          footer={{
            show: true,
            sticky: false,
            showSummary: true,
          }}
          rowSelection={{
            enabled: true,
            mode: 'multi',
          }}
          highlighting={{
            hoverHighlight: true,
            alternateRowHighlight: false,
          }}
          animate={true}
          striped={false}
          showBorder={true}
        />
      </div>

      <div className="hidden">
        <div className="text-muted-foreground mt-2 text-xs">
          {t('language.current')}: {currentLang}
        </div>
      </div>
    </div>
  );
};

export default TableDynamicDemo;
