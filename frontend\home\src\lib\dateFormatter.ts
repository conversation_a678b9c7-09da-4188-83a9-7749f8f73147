import { DateTime } from 'luxon';

/**
 * Formats an ISO 8601 timestamp to a human-readable format (MMM d, yyyy, h:mm a)
 * @param isoString - ISO 8601 timestamp string
 * @param zone - Time zone (default: 'utc')
 * @returns Formatted date string (e.g., "Mar 6, 2025, 4:11 PM")
 */
export const formatToHumanReadable = (isoString?: string, zone = 'utc'): string => {
  if (!isoString) return '';

  try {
    return DateTime.fromISO(isoString, { zone }).toFormat('MMM d, yyyy, h:mm a');
  } catch (error) {
    console.error('Error formatting date:', error);
    return isoString;
  }
};

/**
 * Extracts and formats only the date part from an ISO 8601 timestamp
 * @param isoString - ISO 8601 timestamp string
 * @param zone - Time zone (default: 'utc')
 * @returns Formatted date string (e.g., "Mar 6, 2025")
 */
export const formatDateOnly = (isoString?: string, zone = 'utc'): string => {
  if (!isoString) return '';

  try {
    return DateTime.fromISO(isoString, { zone }).toFormat('MMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return isoString;
  }
};

/**
 * Extracts and formats only the time part from an ISO 8601 timestamp
 * @param isoString - ISO 8601 timestamp string
 * @param zone - Time zone (default: 'utc')
 * @returns Formatted time string (e.g., "4:11 PM")
 */
export const formatTimeOnly = (isoString?: string, zone = 'utc'): string => {
  if (!isoString) return '';

  try {
    return DateTime.fromISO(isoString, { zone }).toFormat('h:mm a');
  } catch (error) {
    console.error('Error formatting time:', error);
    return isoString;
  }
};

/**
 * Create a DateConversionTooltip object with formatted date and time
 * @param isoString - ISO 8601 timestamp string
 * @param locale - Optional locale for internationalization (e.g., 'en', 'fr', 'ja')
 * @returns Object with formatted date and time values
 */
export const createDateConversionData = (
  isoString?: string,
  locale?: string,
): {
  timezone: string;
  localTime: string;
  utcTime: string;
  date: string;
  localTimezone: string;
} => {
  if (!isoString) {
    return {
      timezone: '',
      localTime: '',
      utcTime: '',
      date: '',
      localTimezone: '',
    };
  }

  try {
    // Set locale if provided, otherwise use system locale
    const effectiveLocale = locale || navigator.language || 'en';

    const utcDateTime = DateTime.fromISO(isoString, { zone: 'utc' }).setLocale(effectiveLocale);
    const localDateTime = utcDateTime.toLocal().setLocale(effectiveLocale);

    // Format the date part with locale awareness
    const formattedDate = utcDateTime.toLocaleString(DateTime.DATE_MED);

    // Format the UTC time with locale awareness
    const utcTime = utcDateTime.toLocaleString(DateTime.TIME_WITH_SECONDS);

    // Format the local time with locale awareness
    const localTime = localDateTime.toLocaleString(DateTime.TIME_WITH_SECONDS);

    // Get timezone name
    const localTimezone = localDateTime.zoneName || '';

    // Get timezone offset with proper formatting
    const offset = localDateTime.offset;
    const offsetHours = Math.abs(Math.floor(offset / 60));
    const offsetMinutes = Math.abs(offset % 60);
    const offsetSign = offset >= 0 ? '+' : '-';
    const timezone = `${offsetSign}${offsetHours.toString().padStart(2, '0')}:${offsetMinutes.toString().padStart(2, '0')}`;

    return {
      timezone,
      localTime,
      utcTime,
      date: formattedDate,
      localTimezone,
    };
  } catch (error) {
    console.error('Error creating date conversion data:', error);
    return {
      timezone: '',
      localTime: '',
      utcTime: '',
      date: '',
      localTimezone: '',
    };
  }
};

/**
 * Ensure a date string is in ISO 8601 format
 * @param dateString - Date string to convert
 * @returns ISO 8601 formatted date string
 */
export const ensureISOFormat = (dateString?: string): string => {
  if (!dateString) return '';

  try {
    // If already in ISO format, return as is
    if (dateString.includes('T') && (dateString.includes('Z') || dateString.includes('+'))) {
      return dateString;
    }

    // Convert to ISO format
    const iso = DateTime.fromISO(dateString).toISO();
    return iso ?? '';
  } catch {
    // Try parsing as a JavaScript Date if not valid ISO
    try {
      const iso = DateTime.fromJSDate(new Date(dateString)).toISO();
      return iso ?? '';
    } catch (innerError) {
      console.error('Error ensuring ISO format:', innerError);
      return dateString;
    }
  }
};
