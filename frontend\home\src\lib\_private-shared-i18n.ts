/**
 * @private
 * SHARED i18n CONSTANTS AND TYPES
 * This file contains types and constants shared between client and server i18n implementations
 */

// Define supported languages with proper typing
export const supportedLngs = {
  en: { rtl: false },
  fr: { rtl: false },
  ja: { rtl: false },
  ar: { rtl: true },
} as const;

// Create a type for supported language codes
export type SupportedLanguage = keyof typeof supportedLngs;

// Get language codes as an array
export const languageCodes = Object.keys(supportedLngs) as SupportedLanguage[];

// Define namespaces
export const ns = ['common'];
export const defaultNS = 'common';

// Define a type for nested translation objects
export type TranslationObject = {
  [key: string]: string | TranslationObject;
};

/**
 * Helper function to check if a language is RTL
 */
export function isRTL(language: SupportedLanguage): boolean {
  return supportedLngs[language]?.rtl || false;
} 