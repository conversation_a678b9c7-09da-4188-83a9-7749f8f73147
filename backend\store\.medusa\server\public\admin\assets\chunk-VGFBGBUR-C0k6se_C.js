import{b as n,r as u,j as t,Y as c}from"./index-Bwql5Dzz.js";import{g as f}from"./chunk-6LRPF7MX-BV-FSPta.js";import{T as i,a as p}from"./chunk-MSDRGCRR-BLk8RuFZ.js";import{S as x}from"./chunk-ADOCJB6L-fVr5Yqi0.js";import{u as h}from"./chunk-C76H5USB-ByRPKhW7.js";import{c as j}from"./index-BxZ1678G.js";var w=()=>{const{t:e}=n();return[{label:e("fields.createdAt"),key:"created_at",type:"date"},{label:e("fields.updatedAt"),key:"updated_at",type:"date"}]},v=({code:e})=>t.jsx("div",{className:"flex h-full w-full items-center gap-x-3 overflow-hidden",children:t.jsx(c,{size:"2xsmall",className:"truncate",children:e})}),y=({text:e})=>t.jsx("div",{className:" flex h-full w-full items-center ",children:t.jsx("span",{children:e})}),b=({promotion:e})=>{const[a,r]=f(e);return t.jsx(x,{color:a,children:r})},s=j(),H=()=>{const{t:e}=n();return u.useMemo(()=>[s.display({id:"code",header:()=>t.jsx(y,{text:e("fields.code")}),cell:({row:a})=>t.jsx(v,{code:a.original.code})}),s.display({id:"method",header:()=>t.jsx(i,{text:e("promotions.fields.method")}),cell:({row:a})=>{const r=a.original.is_automatic?e("promotions.form.method.automatic.title"):e("promotions.form.method.code.title");return t.jsx(p,{text:r})}}),s.display({id:"status",header:()=>t.jsx(i,{text:e("fields.status")}),cell:({row:a})=>t.jsx(b,{promotion:a.original})})],[e])},q=({prefix:e,pageSize:a=20})=>{const r=h(["offset","q","created_at","updated_at"],e),{offset:o,q:m,created_at:l,updated_at:d}=r;return{searchParams:{limit:a,created_at:l?JSON.parse(l):void 0,updated_at:d?JSON.parse(d):void 0,offset:o?Number(o):0,q:m},raw:r}};export{w as a,H as b,q as u};
