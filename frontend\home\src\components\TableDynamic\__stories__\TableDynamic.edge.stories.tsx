'use client';

import * as React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, within } from '@storybook/test';
import TableDynamic from '../table-dynamic';
import { edgeCaseData, simpleColumns } from '../__fixtures__/TableDynamic.fixtures';
import type { ColumnConfig } from '../types';

// Enhanced columns for stories
const getEnhancedColumns = (): ColumnConfig[] => {
  return simpleColumns.map((col) => {
    if (col.id === 'price') {
      return {
        ...col,
        cell: ({ row }) => {
          const price = row.getValue('price');
          // Handle special cases
          if (price === 0) return <div className="text-muted">Free</div>;
          if (typeof price === 'number' && price < 0)
            return <div className="text-destructive">${Math.abs(price)} (Discount)</div>;

          // Normal case
          return <div className="text-right">${String(price)}</div>;
        },
      } as ColumnConfig;
    }
    if (col.id === 'name') {
      return {
        ...col,
        cell: ({ row }) => {
          const name = row.getValue('name');
          // Handle edge cases
          if (!name) return <div className="text-muted italic">Unnamed product</div>;
          if (typeof name === 'string' && name.length > 50) {
            return (
              <div className="truncate font-medium" title={String(name)}>
                {name.substring(0, 50)}...
              </div>
            );
          }

          // Normal case
          return <div className="font-medium">{String(name)}</div>;
        },
      } as ColumnConfig;
    }
    if (col.id === 'category') {
      return {
        ...col,
        cell: ({ row }) => {
          const category = row.getValue('category');
          // Handle edge cases
          if (!category) return <div className="text-muted italic">Uncategorized</div>;

          // Normal case
          return <div>{String(category)}</div>;
        },
      } as ColumnConfig;
    }
    return col as ColumnConfig;
  });
};

const meta = {
  title: 'Components/TableDynamic/EdgeCases',
  component: TableDynamic,
  parameters: {
    docs: {
      description: {
        story:
          'Demonstrations of how the TableDynamic component handles edge cases and boundary conditions.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="max-w-full p-4">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof TableDynamic>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shows how the table handles edge cases like empty values, long content, and special characters.
 */
export const EdgeCases: Story = {
  args: {
    data: edgeCaseData,
    columns: getEnhancedColumns(),
    variant: 'primary',
    size: 'md',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check that the table renders despite edge cases
    const table = canvas.getByRole('table');
    await expect(table).toBeInTheDocument();
  },
};

/**
 * Shows the table with very long content that might break layout.
 */
export const LongContent: Story = {
  args: {
    data: [
      {
        id: '1',
        name: 'This is a product with an extremely long name that might cause layout issues if not handled properly with truncation or text wrapping',
        category:
          'This is also a very long category name that exceeds normal expectations and tests the layout capabilities of the table component',
        price: 1200,
        stock: 45,
      },
    ],
    columns: getEnhancedColumns(),
    variant: 'primary',
    size: 'md',
  },
};

/**
 * Shows the table with a mix of data types.
 */
export const MixedDataTypes: Story = {
  args: {
    data: [
      { id: '1', name: 'Normal Product', category: 'Electronics', price: 100, stock: 10 },
      { id: '2', name: null, category: 'Food', price: '50', stock: '5' }, // Mixed types
      { id: '3', name: undefined, category: undefined, price: undefined, stock: undefined },
      { id: '4', name: 0, category: 0, price: 0, stock: 0 }, // Zeros
      { id: '5', name: false, category: true, price: -10, stock: -5 }, // Booleans and negatives
    ],
    columns: getEnhancedColumns(),
    variant: 'primary',
    size: 'md',
  },
};

/**
 * Shows the table with content that might be used for XSS attacks.
 */
export const PotentialXSS: Story = {
  args: {
    data: [
      {
        id: '1',
        name: '<script>alert("XSS")</script>',
        category: 'Books',
        price: 15,
        stock: 100,
      },
      {
        id: '2',
        name: '<img src="x" onerror="alert(\'XSS\')">',
        category: 'Electronics',
        price: 50,
        stock: 20,
      },
    ],
    columns: getEnhancedColumns(),
    variant: 'primary',
    size: 'md',
  },
};

/**
 * Shows the table with a huge number of columns that would overflow horizontally.
 */
export const ManyColumns: Story = {
  args: {
    data: [
      {
        id: '1',
        name: 'Product',
        col1: 'Value 1',
        col2: 'Value 2',
        col3: 'Value 3',
        col4: 'Value 4',
        col5: 'Value 5',
        col6: 'Value 6',
        col7: 'Value 7',
        col8: 'Value 8',
        col9: 'Value 9',
        col10: 'Value 10',
      },
    ],
    columns: [
      ...getEnhancedColumns(),
      ...Array(10)
        .fill(0)
        .map((_, i) => ({
          id: `col${i + 1}`,
          header: `Column ${i + 1}`,
          accessorKey: `col${i + 1}`,
        })),
    ] as ColumnConfig[],
    variant: 'primary',
    size: 'md',
  },
};

/**
 * Shows the table with a single row and column (minimum data).
 */
export const MinimalData: Story = {
  args: {
    data: [{ id: '1', name: 'Single Item' }],
    columns: [
      {
        id: 'name',
        header: 'Name',
        accessorKey: 'name',
      },
    ] as ColumnConfig[],
    variant: 'primary',
    size: 'md',
  },
};
