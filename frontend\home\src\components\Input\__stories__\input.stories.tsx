// Input.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { expect, userEvent, within } from '@storybook/test';
import { Input } from '../input';

const meta: Meta<typeof Input> = {
  title: 'UI/Input/Input',
  component: Input,
  parameters: {
    layout: 'centered',
    // a11y testing parameters
    a11y: {
      config: {
        rules: [
          {
            // ตัวอย่างการปรับแต่งกฎ a11y เฉพาะ component นี้
            id: 'label',
            enabled: true,
          },
        ],
      },
    },
  },
  // ค่าเริ่มต้นที่ใช้กับทุก Story
  args: {
    id: 'input-field',
    placeholder: 'Enter text here',
  },
  // Mock functions สำหรับใช้ในการทดสอบ
  argTypes: {
    onChange: { action: 'changed' },
    onValueChange: { action: 'valueChanged' },
    onButtonClick: { action: 'buttonClicked' },
    variant: {
      control: 'select',
      options: ['default', 'search', 'withButton', 'password'],
    },
    size: {
      control: 'select',
      options: ['default', 'sm', 'lg'],
    },
  },
  // สร้าง documentation อัตโนมัติ
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Input>;

// Story พื้นฐาน
export const Default: Story = {
  args: {},
};

// Story ที่มี label
export const WithLabel: Story = {
  args: {
    label: 'Your Name',
    placeholder: 'Enter your name',
  },
};

// Story ขนาดต่างๆ
export const SizeVariants: Story = {
  render: () => (
    <div className="flex flex-col space-y-4">
      <Input size="sm" placeholder="Small input field" />
      <Input size="default" placeholder="Default input field" />
      <Input size="lg" placeholder="Large input field" />
    </div>
  ),
};

// Story ที่มีข้อความช่วยเหลือ
export const WithHelperText: Story = {
  args: {
    label: 'Email Address',
    placeholder: '<EMAIL>',
    helperText: 'We\'ll never share your email with anyone else',
  },
};

// Story ที่มีการกำหนดค่าเริ่มต้น
export const WithDefaultValue: Story = {
  args: {
    label: 'Company',
    defaultValue: 'Acme Corporation',
  },
};

// Story แสดงสถานะ disabled
export const Disabled: Story = {
  args: {
    label: 'Username',
    placeholder: 'Enter username',
    disabled: true,
    defaultValue: 'Unavailable',
  },
};

// Story Input แบบ ReadOnly
export const ReadOnly: Story = {
  args: {
    label: 'User ID',
    defaultValue: 'USR-12345',
    readOnly: true,
    helperText: 'This value cannot be changed',
  },
};

// Story ที่มีการทดสอบ interaction และ state
export const InteractionTest: Story = {
  args: {
    label: 'Test Input',
    placeholder: 'Type here...',
    onChange: fn(),
    onValueChange: fn(),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // ค้นหา input element
    const input = canvas.getByRole('textbox');
    
    // ตรวจสอบว่า input มีอยู่จริง
    expect(input).toBeInTheDocument();
    
    // จำลองการ focus
    await userEvent.click(input);
    
    // จำลองการพิมพ์ข้อความ
    await userEvent.type(input, 'Hello, world!');
    
    // ตรวจสอบว่าข้อความที่พิมพ์ถูกต้อง
    expect(input).toHaveValue('Hello, world!');
    
    // ตรวจสอบว่ามีการเรียกใช้ onChange
    expect(args.onChange).toHaveBeenCalled();
    
    // ตรวจสอบว่ามีการเรียกใช้ onValueChange
    expect(args.onValueChange).toHaveBeenCalled();
  },
};

// ----------- Custom Variant Stories -----------

// Story Input แบบมีปุ่มค้นหา (Search)
export const SearchInputVariant: Story = {
  args: {
    variant: 'search',
    placeholder: 'ค้นหาสินค้าที่ต้องการ',
    onButtonClick: fn(),
    searchButtonClassName: 'bg-[#1E3A8A]',
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // ค้นหา input element
    const input = canvas.getByRole('textbox');
    expect(input).toBeInTheDocument();
    
    // พิมพ์ข้อความในช่องค้นหา
    await userEvent.type(input, 'สินค้าทดสอบ');
    expect(input).toHaveValue('สินค้าทดสอบ');
    
    // คลิกปุ่มค้นหา
    const searchButton = canvas.getByTestId('search-button') || canvas.getByRole('button');
    await userEvent.click(searchButton);
    
    // ตรวจสอบว่ามีการเรียกใช้ onButtonClick
    expect(args.onButtonClick).toHaveBeenCalled();
  },
};

// Story Input แบบมีปุ่มด้านข้าง (Discount Code)
export const WithButtonVariant: Story = {
  args: {
    variant: 'withButton',
    placeholder: 'กรอกรหัสส่วนลด',
    buttonText: 'ใช้',
    onButtonClick: fn(),
  },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // ค้นหา input element
    const input = canvas.getByRole('textbox');
    expect(input).toBeInTheDocument();
    
    // พิมพ์รหัสส่วนลด
    await userEvent.type(input, 'DISCOUNT50');
    expect(input).toHaveValue('DISCOUNT50');
    
    // คลิกปุ่มใช้
    const applyButton = canvas.getByRole('button', { name: 'ใช้' });
    await userEvent.click(applyButton);
    
    // ตรวจสอบว่ามีการเรียกใช้ onButtonClick
    expect(args.onButtonClick).toHaveBeenCalled();
  },
};
