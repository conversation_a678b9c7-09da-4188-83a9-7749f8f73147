{"compilerOptions": {"target": "ES2021", "esModuleInterop": true, "module": "Node16", "moduleResolution": "Node16", "emitDecoratorMetadata": true, "experimentalDecorators": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "declaration": false, "sourceMap": false, "inlineSourceMap": true, "outDir": "./.medusa/server", "rootDir": "./", "jsx": "react-jsx", "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "checkJs": false, "strictNullChecks": true}, "ts-node": {"swc": true}, "include": ["**/*", ".medusa/types/*"], "exclude": ["node_modules", ".medusa/server", ".medusa/admin", ".cache"]}