'use client';

import * as React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, within } from '@storybook/test';
import TableDynamic from '../table-dynamic';
import { simpleData, simpleColumns, variantColumns } from '../__fixtures__/TableDynamic.fixtures';
import { ActionCell } from './TableDynamic.stories';
import { TooltipProvider } from '@/components/ui/tooltip';
import type { ColumnConfig } from '../types';

// Enhanced columns for stories with cell renderers
const getEnhancedColumns = (columnType: keyof typeof variantColumns): ColumnConfig[] => {
  const baseColumns = variantColumns[columnType] || [];

  return baseColumns.map((col) => {
    const enhancedCol = { ...col } as ColumnConfig;

    if (col.id === 'price') {
      enhancedCol.cell = ({ row }) => <div className="text-right">${row.getValue('price')}</div>;
      enhancedCol.aggregationFn = (values: unknown[]) =>
        values.reduce((acc: number, val) => acc + (typeof val === 'number' ? val : 0), 0);
      enhancedCol.aggregationFormatter = (value: unknown) =>
        `$${typeof value === 'number' ? value.toFixed(2) : '0.00'}`;
      enhancedCol.headerTooltip = 'Total sum on hover';
    }

    if (col.id === 'name') {
      enhancedCol.cell = ({ row }) => <div className="font-medium">{row.getValue('name')}</div>;
    }

    if (col.id === 'actions') {
      enhancedCol.cell = ActionCell;
    }

    return enhancedCol;
  });
};

// Enhanced simple columns for basic stories
const getEnhancedSimpleColumns = (): ColumnConfig[] => {
  return simpleColumns.map((col) => {
    if (col.id === 'price') {
      return {
        ...col,
        cell: ({ row }) => <div className="text-right">${row.getValue('price')}</div>,
      } as ColumnConfig;
    }
    if (col.id === 'name') {
      return {
        ...col,
        cell: ({ row }) => <div className="font-medium">{row.getValue('name')}</div>,
      } as ColumnConfig;
    }
    return col as ColumnConfig;
  });
};

const meta = {
  title: 'Components/TableDynamic/Variants',
  component: TableDynamic,
  parameters: {
    docs: {
      description: {
        story:
          'Explore different visual variants and configurations of the TableDynamic component.',
      },
    },
  },
  decorators: [
    (Story) => (
      <TooltipProvider>
        <div className="max-w-full p-4">
          <Story />
        </div>
      </TooltipProvider>
    ),
  ],
} satisfies Meta<typeof TableDynamic>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Shows the primary variant of the table with default styling.
 */
export const Primary: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const table = canvas.getByRole('table');
    await expect(table).toBeInTheDocument();
  },
};

/**
 * Shows the secondary variant of the table with a muted background.
 */
export const Secondary: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'secondary',
    size: 'md',
  },
};

/**
 * Shows the outline variant of the table with a border.
 */
export const Outline: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'outline',
    size: 'md',
  },
};

/**
 * Shows all size variants side by side.
 */
export const AllSizes: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
  },
  render: () => (
    <div className="space-y-8">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Small Size</h3>
        <TableDynamic data={simpleData} columns={getEnhancedSimpleColumns()} size="sm" />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Medium Size (Default)</h3>
        <TableDynamic data={simpleData} columns={getEnhancedSimpleColumns()} size="md" />
      </div>

      <div>
        <h3 className="mb-2 text-lg font-semibold">Large Size</h3>
        <TableDynamic data={simpleData} columns={getEnhancedSimpleColumns()} size="lg" />
      </div>
    </div>
  ),
};

/**
 * Shows the table with sticky columns configuration.
 */
export const StickyColumns: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedColumns('sticky'),
    variant: 'primary',
    size: 'md',
  },
};

/**
 * Shows the table with a hidden column configuration.
 */
export const HiddenColumns: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedColumns('hidden'),
    variant: 'primary',
    size: 'md',
    columnVisibility: {
      enabled: true,
      showToggle: true,
    },
  },
};

/**
 * Shows the table with truncated cell content.
 */
export const TruncatedContent: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedColumns('truncated'),
    variant: 'primary',
    size: 'md',
  },
};

/**
 * Shows the table with striped rows.
 */
export const StripedRows: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    striped: true,
  },
};

/**
 * Shows the table with compact styling.
 */
export const Compact: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'sm',
    compact: true,
  },
};
