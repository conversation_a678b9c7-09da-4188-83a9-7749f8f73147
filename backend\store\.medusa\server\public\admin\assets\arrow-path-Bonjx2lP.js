import{r as l}from"./index-Bwql5Dzz.js";var c=Object.defineProperty,n=Object.getOwnPropertySymbols,f=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable,o=(r,t,e)=>t in r?c(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,m=(r,t)=>{for(var e in t)f.call(t,e)&&o(r,e,t[e]);if(n)for(var e of n(t))i.call(t,e)&&o(r,e,t[e]);return r},d=(r,t)=>{var e={};for(var a in r)f.call(r,a)&&t.indexOf(a)<0&&(e[a]=r[a]);if(r!=null&&n)for(var a of n(r))t.indexOf(a)<0&&i.call(r,a)&&(e[a]=r[a]);return e};const s=l.forwardRef((r,t)=>{var e=r,{color:a="currentColor"}=e,p=d(e,["color"]);return l.createElement("svg",m({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),l.createElement("g",{stroke:a,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,clipPath:"url(#a)"},l.createElement("path",{d:"M4.752 9.616 1.935 8.86l-.755 2.817"}),l.createElement("path",{d:"M13.136 8.53a5.729 5.729 0 0 1-11.196.357M10.248 5.384l2.817.755.755-2.817"}),l.createElement("path",{d:"M1.864 6.469a5.729 5.729 0 0 1 11.184-.403"})),l.createElement("defs",null,l.createElement("clipPath",{id:"a"},l.createElement("path",{fill:"#fff",d:"M0 0h15v15H0z"}))))});s.displayName="ArrowPath";export{s as A};
