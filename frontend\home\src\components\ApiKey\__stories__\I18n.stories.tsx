import type { Meta, StoryObj } from '@storybook/react';
import { expect, fn, within } from '@storybook/test';
import I18nCreate from '../I18nCreate';
import I18nEdit from '../I18nEdit';
import { sampleProjects, sampleApiKeyRestricted } from '../__fixtures__/ApiKey.fixtures';
import { I18nextProvider } from 'react-i18next';
import { i18n } from '@/lib';

// Ensure i18n is initialized
const i18nInstance = i18n.client.i18n;
i18nInstance.changeLanguage('en');

// Common wrapper for I18n stories to ensure proper provider setup
const I18nWrapper = ({ children, lang = 'en' }: { children: React.ReactNode; lang?: string }) => {
  // Set the language
  i18nInstance.changeLanguage(lang);

  return <I18nextProvider i18n={i18nInstance}>{children}</I18nextProvider>;
};

const meta = {
  title: 'Components/ApiKey/Internationalization',
  parameters: {
    docs: {
      description: {
        story: 'Internationalization examples for API key components',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="flex min-h-[600px] items-center justify-center bg-gray-100 p-8">
        <div className="w-full max-w-md">
          <Story />
        </div>
      </div>
    ),
  ],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Create API Key form in English
 */
export const CreateEnglish: Story = {
  render: () => (
    <I18nWrapper lang="en">
      <I18nCreate onSubmit={fn()} onCancel={fn()} availableProjects={sampleProjects} />
    </I18nWrapper>
  ),
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check for the presence of the form and basic structure
    const form = canvas.getByTestId('i18n-create-form');
    await expect(form).toBeInTheDocument();

    // Check for title element
    const heading = canvas.getByTestId('form-title');
    await expect(heading).toBeInTheDocument();
  },
};

/**
 * Internationalized Create API Key form with French language.
 */
export const CreateFrench: Story = {
  render: () => (
    <I18nWrapper lang="fr">
      <I18nCreate
        onSubmit={fn()}
        onCancel={fn()}
        isSubmitting={false}
        availableProjects={sampleProjects}
      />
    </I18nWrapper>
  ),
  parameters: {
    backgrounds: { default: 'light' },
    locale: 'fr',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check for the presence of the form and basic structure
    const form = canvas.getByTestId('i18n-create-form');
    await expect(form).toBeInTheDocument();

    // Check for title element
    const heading = canvas.getByTestId('form-title');
    await expect(heading).toBeInTheDocument();
  },
};

/**
 * Internationalized Create API Key form with Japanese language.
 */
export const CreateJapanese: Story = {
  render: () => (
    <I18nWrapper lang="ja">
      <I18nCreate
        onSubmit={fn()}
        onCancel={fn()}
        isSubmitting={false}
        availableProjects={sampleProjects}
      />
    </I18nWrapper>
  ),
  parameters: {
    backgrounds: { default: 'light' },
    locale: 'ja',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check for the presence of the form and basic structure
    const form = canvas.getByTestId('i18n-create-form');
    await expect(form).toBeInTheDocument();

    // Check for title element
    const heading = canvas.getByTestId('form-title');
    await expect(heading).toBeInTheDocument();
  },
};

/**
 * Internationalized Edit API Key form with English language.
 */
export const EditEnglish: Story = {
  render: () => (
    <I18nWrapper lang="en">
      <I18nEdit
        apiKey={sampleApiKeyRestricted}
        onSubmit={fn()}
        onCancel={fn()}
        isSubmitting={false}
      />
    </I18nWrapper>
  ),
  parameters: {
    backgrounds: { default: 'light' },
    locale: 'en',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check for the presence of the form and basic structure
    const form = canvas.getByTestId('i18n-edit-form');
    await expect(form).toBeInTheDocument();

    // Check for title element
    const heading = canvas.getByTestId('form-title');
    await expect(heading).toBeInTheDocument();
  },
};

/**
 * Internationalized Edit API Key form with French language.
 */
export const EditFrench: Story = {
  render: () => (
    <I18nWrapper lang="fr">
      <I18nEdit
        apiKey={sampleApiKeyRestricted}
        onSubmit={fn()}
        onCancel={fn()}
        isSubmitting={false}
      />
    </I18nWrapper>
  ),
  parameters: {
    backgrounds: { default: 'light' },
    locale: 'fr',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check for the presence of the form and basic structure
    const form = canvas.getByTestId('i18n-edit-form');
    await expect(form).toBeInTheDocument();

    // Check for title element
    const heading = canvas.getByTestId('form-title');
    await expect(heading).toBeInTheDocument();
  },
};

/**
 * Internationalized Edit API Key form with Japanese language.
 */
export const EditJapanese: Story = {
  render: () => (
    <I18nWrapper lang="ja">
      <I18nEdit
        apiKey={sampleApiKeyRestricted}
        onSubmit={fn()}
        onCancel={fn()}
        isSubmitting={false}
      />
    </I18nWrapper>
  ),
  parameters: {
    backgrounds: { default: 'light' },
    locale: 'ja',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check for the presence of the form and basic structure
    const form = canvas.getByTestId('i18n-edit-form');
    await expect(form).toBeInTheDocument();

    // Check for title element
    const heading = canvas.getByTestId('form-title');
    await expect(heading).toBeInTheDocument();
  },
};
