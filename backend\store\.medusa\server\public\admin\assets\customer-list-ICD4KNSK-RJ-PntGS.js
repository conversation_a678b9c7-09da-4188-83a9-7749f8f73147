import{u as f,a as g,b as x}from"./chunk-IDYOGWSG-DuqxOJwc.js";import{a as j,j as s,b as m,dI as y,H as C,L as h,B as v,r as k,k as T,A as _}from"./index-Bwql5Dzz.js";import{u as A,_ as P}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{S}from"./chunk-2RQLKDBF-ChA0h8vf.js";import{P as E}from"./pencil-square-6wRbnn1C.js";import{C as w}from"./container-Dqi2woPF.js";import{c as D}from"./index-BxZ1678G.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-WYX5PIA3-DoOUp1ge.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./chunk-ADOCJB6L-fVr5Yqi0.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./Trans-VWqfqpAH.js";import"./check-BGSYwiWc.js";var a=20,L=()=>{const{t:e}=m(),{searchParams:t,raw:n}=f({pageSize:a}),{customers:l,count:o,isLoading:c,isError:u,error:p}=y({...t},{placeholderData:T}),d=g(),i=N(),{table:b}=A({data:l??[],columns:i,count:o,enablePagination:!0,getRowId:r=>r.id,pageSize:a});if(u)throw p;return s.jsxs(w,{className:"divide-y p-0",children:[s.jsxs("div",{className:"flex items-center justify-between px-6 py-4",children:[s.jsx(C,{children:e("customers.domain")}),s.jsx(h,{to:"/customers/create",children:s.jsx(v,{size:"small",variant:"secondary",children:e("actions.create")})})]}),s.jsx(P,{table:b,columns:i,pageSize:a,count:o,filters:d,orderBy:[{key:"email",label:e("fields.email")},{key:"first_name",label:e("fields.firstName")},{key:"last_name",label:e("fields.lastName")},{key:"has_account",label:e("customers.hasAccount")},{key:"created_at",label:e("fields.createdAt")},{key:"updated_at",label:e("fields.updatedAt")}],isLoading:c,navigateTo:r=>r.original.id,search:!0,queryObject:n,noRecords:{message:e("customers.list.noRecordsMessage")}})]})},z=({customer:e})=>{const{t}=m();return s.jsx(_,{groups:[{actions:[{icon:s.jsx(E,{}),label:t("actions.edit"),to:`/customers/${e.id}/edit`}]}]})},H=D(),N=()=>{const e=x();return k.useMemo(()=>[...e,H.display({id:"actions",cell:({row:t})=>s.jsx(z,{customer:t.original})})],[e])},de=()=>{const{getWidgets:e}=j();return s.jsx(S,{widgets:{after:e("customer.list.after"),before:e("customer.list.before")},children:s.jsx(L,{})})};export{de as Component};
