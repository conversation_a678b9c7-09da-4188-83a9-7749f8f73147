import type { Meta, StoryObj } from '@storybook/react';
import { TimeConversionTooltip } from '../TimeConversionTooltip';
import {
  defaultTooltipData,
  emptyTooltipData,
  longTextTooltipData,
} from '../__fixtures__/TimeConversionTooltip.fixtures';
import type { DateConversionTooltip } from '../types';

/**
 * Edge cases for TimeConversionTooltip component
 */
const meta = {
  title: 'Components/TimeConversionTooltip/EdgeCases',
  component: TimeConversionTooltip,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        story: 'Edge cases and extreme scenarios for the TimeConversionTooltip component.',
      },
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof TimeConversionTooltip>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Empty content case (just shows the tooltip trigger without any text)
 */
export const EmptyContent: Story = {
  args: {
    data: defaultTooltipData,
    children: '',
  },
};

/**
 * Empty tooltip data (shows the tooltip with empty timezone/time information)
 */
export const EmptyData: Story = {
  args: {
    data: emptyTooltipData,
    children: 'Empty tooltip data',
  },
};

/**
 * Very long content in the tooltip trigger text
 */
export const LongTriggerContent: Story = {
  args: {
    data: defaultTooltipData,
    children:
      'This is an extremely long text that would typically cause layout issues in components without proper overflow handling. It contains many characters and should test edge cases of the component.',
  },
};

/**
 * Long tooltip data with very long timezone and times
 */
export const LongTooltipData: Story = {
  args: {
    data: longTextTooltipData,
    children: 'Long tooltip data',
  },
  render: () => (
    <div className="space-y-4">
      <h3 className="text-base font-medium">Problem</h3>
      <div>
        <TimeConversionTooltip data={longTextTooltipData}>
          This is a very long date and time string that might cause layout issues if not handled
          properly
        </TimeConversionTooltip>
      </div>

      <h3 className="mt-8 text-base font-medium">Solution: With max-width and truncation</h3>
      <div>
        <TimeConversionTooltip
          data={longTextTooltipData}
          className="inline-block max-w-[300px] truncate"
        >
          This is a very long date and time string that might cause layout issues if not handled
          properly
        </TimeConversionTooltip>
      </div>

      <h3 className="mt-8 text-base font-medium">Solution: With wrapper container</h3>
      <div className="max-w-[300px]">
        <TimeConversionTooltip data={longTextTooltipData}>
          This is a very long date and time string that might cause layout issues if not handled
          properly
        </TimeConversionTooltip>
      </div>
    </div>
  ),
};

/**
 * Special characters in tooltip content
 */
export const SpecialCharacters: Story = {
  args: {
    data: {
      timezone: 'America/New_York (🇺🇸)',
      localTime: '12:00 PM',
      utcTime: '16:00 PM',
      date: 'Mar 15, 2023 📅',
    },
    children: 'Special characters → 特殊文字 → 特殊字符 → كاراكترهای ویژه',
  },
};

/**
 * Rich HTML content in tooltip trigger
 */
export const RichContent: Story = {
  args: {
    data: defaultTooltipData,
    children: (
      <div className="flex items-center space-x-2">
        <span className="font-bold">Date:</span>
        <span className="text-primary">March 15, 2023</span>
        <span className="bg-muted rounded px-2 py-1 text-xs">Important</span>
      </div>
    ),
  },
};

/**
 * Multiple tooltips in a tight space
 */
export const MultipleTooltipsInTightSpace: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Multiple tooltips',
  },
  render: () => (
    <div className="flex max-w-[400px] flex-wrap gap-2">
      {Array.from({ length: 10 }).map((_, i) => (
        <TimeConversionTooltip
          key={i}
          data={defaultTooltipData}
          className="bg-muted rounded px-2 py-1"
        >
          Date {i + 1}
        </TimeConversionTooltip>
      ))}
    </div>
  ),
};

/**
 * Nested tooltips (tooltip inside tooltip)
 */
export const NestedTooltips: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Nested tooltips',
  },
  render: () => (
    <TimeConversionTooltip data={defaultTooltipData}>
      Outer tooltip
      <span className="mx-2">with</span>
      <TimeConversionTooltip
        data={{
          timezone: 'Europe/London',
          localTime: '5:00 PM',
          utcTime: '5:00 PM',
          date: 'Apr 20, 2023',
        }}
        className="text-primary"
      >
        nested tooltip
      </TimeConversionTooltip>
    </TimeConversionTooltip>
  ),
};

/**
 * Multiple extreme cases in one view
 */
export const ExtremeCases: Story = {
  args: {
    data: defaultTooltipData,
    children: 'Multiple extreme cases',
  },
  render: () => {
    // Define our extended type for test cases
    type TestCase = DateConversionTooltip & {
      label?: string;
    };

    // Array of increasingly challenging test cases
    const testCases: TestCase[] = [
      {
        timezone: 'America/New_York',
        localTime: '10:00 AM',
        utcTime: '3:00 PM',
        date: 'Jan 1, 2023',
        label: 'Normal case',
      },
      {
        timezone: 'America/Argentina/Buenos_Aires',
        localTime: '11:45 AM',
        utcTime: '2:45 PM',
        date: 'December 25, 2023',
        label: 'Long timezone',
      },
      {
        ...longTextTooltipData,
        label: 'Very long case',
      },
      {
        timezone: 'America/Argentina/Buenos_Aires (GMT-03:00) (Daylight Saving Time)',
        localTime: '11:45:32.456 AM',
        utcTime: '2:45:32.456 PM',
        date: 'Wednesday, December 25, 2023 (Christmas Day)',
        label: 'Extreme case',
      },
    ];

    return (
      <div className="space-y-8">
        {testCases.map((testCase, index) => (
          <div key={index} className="space-y-2">
            <h3 className="text-base font-medium">{testCase.label || `Test Case ${index + 1}`}</h3>
            <TimeConversionTooltip data={testCase}>
              {testCase.label || `Tooltip ${index + 1}`}
            </TimeConversionTooltip>
          </div>
        ))}
      </div>
    );
  },
};
