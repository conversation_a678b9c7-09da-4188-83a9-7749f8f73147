import type { Meta, StoryObj } from '@storybook/react';
import { StorybookI18nButton } from './storybook-wrapper';

const meta: Meta<typeof StorybookI18nButton> = {
  title: 'Components/LanguageButton/I18nButton',
  component: StorybookI18nButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A button that allows users to switch between different languages. Uses our unified language change hook that works consistently in both application and Storybook environments.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof StorybookI18nButton>;

// In Storybook, the component will render a simplified version
export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'The default language button that shows actual dropdown with languages in both Storybook and production environments.',
      },
    },
  },
};

export const French: Story = {
  parameters: {
    locale: 'fr',
    docs: {
      description: {
        story: 'Language button with French locale set. Uses the unified language change hook.',
      },
    },
  },
};

export const Japanese: Story = {
  parameters: {
    locale: 'ja',
    docs: {
      description: {
        story: 'Language button with Japanese locale set. Uses the unified language change hook.',
      },
    },
  },
};

export const Arabic: Story = {
  parameters: {
    locale: 'ar',
    docs: {
      description: {
        story: 'Language button with Arabic locale set (RTL language). Uses the unified language change hook with proper RTL support.',
      },
    },
  },
};
