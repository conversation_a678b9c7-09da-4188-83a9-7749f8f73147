import { CardContentLinkProps } from '../card-content-link';

// Sample fixture data for testing and Storybook demos
export const lightingFixture: CardContentLinkProps = {
  title: 'โคมไฟและหลอดไฟ',
  description: 'Convallis vitae in consetetur euismod dapibus. Placerat nulla vitae eget sit consetetur. Nisi non risus eget morbi nisi commodo. Convallis ac praesent magna ante non. Non id massa eget.',
  links: [
    { label: 'หลอดไฟ', href: '/lighting/bulbs' },
    { label: 'รางน้อยและดาวน์ไลท์', href: '/lighting/tracks' },
    { label: 'โคมไฟเสาสูง', href: '/lighting/pole' },
    { label: 'โคมไฟภายใน', href: '/lighting/indoor' },
    { label: 'โคมไฟภายนอก', href: '/lighting/outdoor' },
    { label: 'โคมไฟ', href: '/lighting/lamps' },
    { label: 'อุปกรณ์หลอดไฟ', href: '/lighting/accessories' },
  ],
  image: {
    src: '/images/lighting-fixture.png',
    alt: 'โคมไฟและหลอดไฟ',
    width: 250,
    height: 250,
  },
  secondaryImage: {
    src: '/images/lighting-products.png',
    alt: 'ผลิตภัณฑ์หลอดไฟและโคมไฟ',
    width: 400,
    height: 120,
  }
};

export const electricalSafetyFixture: CardContentLinkProps = {
  title: 'ระบบไฟฟ้าและความปลอดภัย',
  description: 'Convallis vitae in consetetur euismod dapibus. Placerat nulla vitae eget sit consetetur. Nisi non risus eget morbi nisi commodo.',
  links: [
    { label: 'สายไฟ', href: '/electrical/cables' },
    { label: 'รางสี่เหลี่ยมและแผงเมอร์', href: '/electrical/panels' },
    { label: 'เบรกเกอร์และตู้ไฟ', href: '/electrical/breakers' },
    { label: 'ปลั๊กและสวิตช์ไฟฟ้า', href: '/electrical/sockets' },
    { label: 'อุปกรณ์เดินสายไฟ', href: '/electrical/accessories' },
    { label: 'เครื่องสำรองไฟฟ้า', href: '/electrical/backup' },
  ],
  image: {
    src: '/images/electrical-safety.png',
    alt: 'ระบบไฟฟ้าและความปลอดภัย',
    width: 250,
    height: 250,
  },
};

export const electricalEquipmentFixture: CardContentLinkProps = {
  title: 'อุปกรณ์ไฟฟ้า',
  description: 'Convallis vitae in consetetur euismod dapibus. Placerat nulla vitae eget sit consetetur.',
  links: [
    { label: 'กริ่ง', href: '/equipment/bells' },
    { label: 'ระบบไฟฉุกเฉิน', href: '/equipment/emergency' },
    { label: 'อุปกรณ์ IoT', href: '/equipment/iot' },
    { label: 'อุปกรณ์ความปลอดภัย', href: '/equipment/safety' },
  ],
  image: {
    src: '/images/electrical-equipment.png',
    alt: 'อุปกรณ์ไฟฟ้า',
    width: 250,
    height: 250,
  },
};

export const noImageFixture: CardContentLinkProps = {
  ...lightingFixture,
  image: undefined,
  secondaryImage: undefined,
};

export const noDescriptionFixture: CardContentLinkProps = {
  ...lightingFixture,
  description: undefined,
};

export const primaryImageOnlyFixture: CardContentLinkProps = {
  ...lightingFixture,
  secondaryImage: undefined,
};

export const secondaryImageOnlyFixture: CardContentLinkProps = {
  ...lightingFixture,
  image: undefined,
};

// Minimal fixture for testing
export const minimalFixture: CardContentLinkProps = {
  title: 'Test Title',
  links: [
    { label: 'Link 1', href: '/link1' },
    { label: 'Link 2', href: '/link2' },
  ],
};