import React, { ReactNode, useState, useRef, useEffect } from 'react';
import { Swiper as SwiperClass } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay, EffectCoverflow } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-coverflow';

export type CarouselVariant = 'default' | 'multiple' | 'coverflow';

export interface CarouselProps {
  /**
   * Carousel style variant
   * @default 'default'
   */
  variant?: CarouselVariant;
  
  /**
   * Array of items to display in carousel
   */
  items: any[];
  
  /**
   * Function to render each carousel item
   */
  renderItem: (item: any, index: number) => ReactNode;
  
  /**
   * Enable autoplay
   * @default false
   */
  autoplay?: boolean;
  
  /**
   * Autoplay delay in milliseconds
   * @default 3000
   */
  autoplayDelay?: number;
  
  /**
   * Show navigation arrows
   * @default true
   */
  navigation?: boolean;
  
  /**
   * Show pagination dots
   * @default true
   */
  pagination?: boolean;
  
  /**
   * Custom class name for the carousel container
   */
  className?: string;
  
  /**
   * Custom options to override variant defaults
   */
  customOptions?: any;
  
  /**
   * Called when active slide changes
   */
  onSlideChange?: (swiper: SwiperClass) => void;
}

/**
 * Reusable Carousel component built on Swiper
 */
const Carousel: React.FC<CarouselProps> = ({
  variant = 'default',
  items = [],
  renderItem,
  autoplay = false,
  autoplayDelay = 3000,
  navigation = true,
  pagination = true,
  className = '',
  customOptions = {},
  onSlideChange,
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<SwiperClass | null>(null);

  // Variant-specific configurations
  const variantConfigs = {
    default: {
      slidesPerView: 1,
      spaceBetween: 16,
    },
    multiple: {
      slidesPerView: 1,
      spaceBetween: 16,
      breakpoints: {
        640: {
          slidesPerView: 2,
          spaceBetween: 20,
        },
        1024: {
          slidesPerView: 3,
          spaceBetween: 24,
        },
      },
    },
    coverflow: {
      effect: 'coverflow',
      grabCursor: true,
      centeredSlides: true,
      slidesPerView: 'auto',
      coverflowEffect: {
        rotate: 0,
        stretch: 0,
        depth: 100,
        modifier: 2.5,
        slideShadows: false,
      },
      initialSlide: 1,
    },
  };

  // Merge variant-specific config with custom options
  const variantConfig = variantConfigs[variant] || variantConfigs.default;
  
  // Construct Swiper options
  const swiperOptions = {
    ...variantConfig,
    ...customOptions,
    modules: [
      Navigation,
      Pagination,
      Autoplay,
      ...(variant === 'coverflow' ? [EffectCoverflow] : []),
    ],
    ...(navigation && { navigation: true }),
    ...(pagination && { pagination: { clickable: true } }),
    ...(autoplay && { 
      autoplay: { 
        delay: autoplayDelay,
        disableOnInteraction: false,
      } 
    }),
    onSlideChange: (swiper: SwiperClass) => {
      setActiveIndex(swiper.activeIndex);
      onSlideChange?.(swiper);
    },
  };

  // Handle external navigation
  const goToSlide = (index: number) => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(index);
    }
  };

  return (
    <div className={`carousel carousel-${variant} ${className}`}>
      <Swiper
        {...swiperOptions}
        onSwiper={(swiper) => {
          swiperRef.current = swiper;
        }}
      >
        {items.map((item, index) => (
          <SwiperSlide key={index} className={`carousel-slide-${variant}`}>
            {renderItem(item, index)}
          </SwiperSlide>
        ))}
      </Swiper>
      
      {/* External controls can be added here if needed */}
    </div>
  );
};

export default Carousel;