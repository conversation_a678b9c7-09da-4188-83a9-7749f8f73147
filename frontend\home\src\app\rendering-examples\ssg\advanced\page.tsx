import { SharedHeader } from '@/components/rendering-examples/SharedHeader';
import Link from 'next/link';

// Function to generate advanced analytics data at build time
async function generateAdvancedAnalytics() {
  // No need for artificial delays in build-time data fetching
  return {
    pageViews: {
      today: Math.floor(Math.random() * 10000),
      weekly: Math.floor(Math.random() * 50000),
      monthly: Math.floor(Math.random() * 200000),
    },
    userSessions: {
      averageDuration: Math.floor(Math.random() * 500) + ' seconds',
      bounceRate: Math.floor(Math.random() * 100) + '%',
      newUsers: Math.floor(Math.random() * 5000),
    },
    serverLoad: {
      current: Math.floor(Math.random() * 100) + '%',
      average: Math.floor(Math.random() * 100) + '%',
      peak: Math.floor(Math.random() * 100) + '%',
    },
    generatedAt: new Date().toISOString(),
  };
}

export default async function SSGAdvancedPage() {
  // Fetch static data at build time
  const advancedData = await generateAdvancedAnalytics();

  return (
    <div className="container mx-auto px-4 py-8">
      <SharedHeader currentExample="ssg" />
      <main className="container mx-auto px-4 pb-12">
        <h1 className="mb-6 text-3xl font-bold">Advanced SSG Analytics</h1>

        <div className="bg-primary/5 border-primary/20 mb-6 rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">Static Analytics Dashboard</h2>
          <p className="mb-6">
            This advanced example demonstrates how SSG can be used to generate complex, data-heavy
            dashboards at build time. Unlike real-time analytics, this data is frozen at the time of
            build.
          </p>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="bg-card rounded-lg border p-6">
              <h3 className="mb-4 font-semibold">Page Views</h3>
              <div className="space-y-4">
                <div>
                  <div className="mb-1 flex justify-between">
                    <span className="text-sm">Today</span>
                    <span className="text-sm font-medium">
                      {advancedData.pageViews.today.toLocaleString()}
                    </span>
                  </div>
                  <div className="bg-muted h-2 w-full rounded-full">
                    <div
                      className="h-2 rounded-full bg-blue-500"
                      style={{ width: `${(advancedData.pageViews.today / 10000) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="mb-1 flex justify-between">
                    <span className="text-sm">Weekly</span>
                    <span className="text-sm font-medium">
                      {advancedData.pageViews.weekly.toLocaleString()}
                    </span>
                  </div>
                  <div className="bg-muted h-2 w-full rounded-full">
                    <div
                      className="h-2 rounded-full bg-green-500"
                      style={{ width: `${(advancedData.pageViews.weekly / 50000) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="mb-1 flex justify-between">
                    <span className="text-sm">Monthly</span>
                    <span className="text-sm font-medium">
                      {advancedData.pageViews.monthly.toLocaleString()}
                    </span>
                  </div>
                  <div className="bg-muted h-2 w-full rounded-full">
                    <div
                      className="h-2 rounded-full bg-purple-500"
                      style={{ width: `${(advancedData.pageViews.monthly / 200000) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg border p-6">
              <h3 className="mb-4 font-semibold">User Sessions</h3>
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium">Average Duration</h4>
                  <p className="text-2xl font-semibold">
                    {advancedData.userSessions.averageDuration}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Bounce Rate</h4>
                  <p className="text-2xl font-semibold">{advancedData.userSessions.bounceRate}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">New Users</h4>
                  <p className="text-2xl font-semibold">
                    {advancedData.userSessions.newUsers.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-card rounded-lg border p-6">
              <h3 className="mb-4 font-semibold">Server Load</h3>
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium">Current</h4>
                  <p className="text-2xl font-semibold">{advancedData.serverLoad.current}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Average</h4>
                  <p className="text-2xl font-semibold">{advancedData.serverLoad.average}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Peak</h4>
                  <p className="text-2xl font-semibold">{advancedData.serverLoad.peak}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-4 text-right text-xs">Generated at: {advancedData.generatedAt}</div>
        </div>

        <div className="bg-muted rounded-lg p-6">
          <h2 className="mb-4 text-xl font-semibold">When to Use Advanced SSG</h2>
          <p className="mb-4">
            This pattern is suitable for dashboards or analytics pages where instant page loads are
            a priority and the displayed data doesn&apos;t need to be real-time. The data is
            computed once during build time.
          </p>
          <p>
            Examples include historical analytics reports, performance benchmarks, or any complex
            data visualization that doesn&apos;t require up-to-the-minute data.
          </p>
        </div>

        <div className="mt-8 flex justify-center">
          <Link href="/rendering-examples/ssg" className="text-primary font-medium hover:underline">
            ← Back to Basic SSG Example
          </Link>
        </div>
      </main>
    </div>
  );
}
