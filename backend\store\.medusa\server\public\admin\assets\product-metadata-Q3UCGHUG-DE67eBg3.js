import{M as m}from"./chunk-HE7M25F2-CIT0st-p.js";import{R as n,Q as d,U as p,j as u}from"./index-Bwql5Dzz.js";import"./chunk-6HTZNHPT-N4svn6ad.js";import"./chunk-JGQGO74V-DtHO1ucg.js";import"./arrow-up-mini-D5bOKjDW.js";import"./trash-BBylvTAG.js";import"./prompt-BsR9zKsn.js";var h=()=>{const{id:a}=n(),{product:t,isPending:r,isError:o,error:s}=d(a),{mutateAsync:i,isPending:e}=p(t==null?void 0:t.id);if(o)throw s;return u.jsx(m,{metadata:t==null?void 0:t.metadata,hook:i,isPending:r,isMutating:e})};export{h as Component};
