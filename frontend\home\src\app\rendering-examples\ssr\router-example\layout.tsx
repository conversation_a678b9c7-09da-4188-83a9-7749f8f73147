import { Fragment } from 'react';
import { ExampleNavigation } from '@/components/rendering-examples/ExampleNavigation';
import { ThemeToggle } from '@/components/theme-toggle';
import { I18nButton } from '@/components/I18nButton';
import Link from 'next/link';
import { createTranslation } from '@/components/Providers/server-i18n';

// Define the routes for this example
const routes = [
  { path: 'home', labelKey: 'rendering.routes.home', defaultLabel: 'Home' },
  { path: 'about', labelKey: 'rendering.routes.about', defaultLabel: 'About' },
  { path: 'products', labelKey: 'rendering.routes.products', defaultLabel: 'Products' },
  { path: 'contact', labelKey: 'rendering.routes.contact', defaultLabel: 'Contact' },
];

export default async function SSRRouterLayout({ children }: { children: React.ReactNode }) {
  // For server components, we use server-side translation utility
  const { t } = await createTranslation('common');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <Link href="/rendering-examples/ssr" className="text-primary font-semibold hover:underline">
          ← {t('rendering.backToSSR')}
        </Link>

        <div className="flex items-center gap-2">
          <ThemeToggle />
          <I18nButton />
        </div>
      </div>

      <h1 className="mb-6 text-3xl font-bold">{t('rendering.ssrRouterTitle')}</h1>

      <div className="bg-muted mb-6 rounded-lg p-6">
        <h2 className="mb-4 text-xl font-semibold">{t('rendering.ssrRouterHowWorks')}</h2>
        <p className="mb-4">{t('rendering.ssrRouterDescription')}</p>
        <p>{t('rendering.ssrRouterNote')}</p>
      </div>

      <ExampleNavigation basePath="/rendering-examples/ssr/router-example" routes={routes} />

      {children}
    </div>
  );
}
