import{a as s}from"./chunk-MSDRGCRR-BLk8RuFZ.js";import{b as c,r as n,j as r}from"./index-Bwql5Dzz.js";import{c as u}from"./index-BxZ1678G.js";var l=u(),x=()=>{const{t}=c();return n.useMemo(()=>[l.accessor("title",{header:t("fields.title"),cell:({getValue:e})=>r.jsx(s,{text:e()})}),l.accessor("handle",{header:t("fields.handle"),cell:({getValue:e})=>r.jsx(s,{text:`/${e()}`})}),l.accessor("products",{header:t("fields.products"),cell:({getValue:e})=>{var o;const a=((o=e())==null?void 0:o.length)||void 0;return r.jsx(s,{text:a})}})],[t])};export{x as u};
