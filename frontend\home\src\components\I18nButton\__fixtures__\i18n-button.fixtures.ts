import { Languages, Flag } from 'lucide-react';

export const languageFixtures = {
  languageCodes: ['en', 'fr', 'ja', 'ar'],
  translations: {
    'en': {
      'language.en': 'English',
      'language.fr': 'French',
      'language.ja': 'Japanese',
      'language.ar': 'Arabic',
      'language.switchLanguage': 'Switch Language',
      'language.current': 'Current language',
      'language.skeleton.ariaLabel': 'Loading language selector'
    },
    'fr': {
      'language.en': 'Anglais',
      'language.fr': 'Français',
      'language.ja': 'Japonais',
      'language.ar': 'Arabe',
      'language.switchLanguage': 'Changer de Langue',
      'language.current': 'Langue actuelle',
      'language.skeleton.ariaLabel': 'Chargement du sélecteur de langue'
    },
    'ja': {
      'language.en': '英語',
      'language.fr': 'フランス語',
      'language.ja': '日本語',
      'language.ar': 'アラビア語',
      'language.switchLanguage': '言語を切り替える',
      'language.current': '現在の言語',
      'language.skeleton.ariaLabel': '言語セレクタのロード中'
    },
    'ar': {
      'language.en': 'الإنجليزية',
      'language.fr': 'الفرنسية',
      'language.ja': 'اليابانية',
      'language.ar': 'العربية',
      'language.switchLanguage': 'تغيير اللغة',
      'language.current': 'اللغة الحالية',
      'language.skeleton.ariaLabel': 'تحميل منتقي اللغة'
    }
  }
};

export const i18nMock = {
  language: 'en',
  isInitialized: true,
  changeLanguage: jest.fn((lng: string) => {
    i18nMock.language = lng;
    return Promise.resolve();
  }),
  getFixedT: jest.fn((lng: string) => {
    return (key: string, fallback?: string) => {
      const translations = languageFixtures.translations[lng as keyof typeof languageFixtures.translations] as Record<string, string>;
      return translations[key] || fallback || key;
    };
  }),
  on: jest.fn(),
  off: jest.fn(),
  t: jest.fn((key: string, fallback?: string) => {
    // Return the translation or fallback
    const translations = languageFixtures.translations.en as Record<string, string>;
    return translations[key] || fallback || key;
  })
};

export const customLanguages = [
  { 
    code: 'en', 
    label: 'English', 
    icon: <Flag className="h-4 w-4" />,
    rtl: false 
  },
  { 
    code: 'fr', 
    label: 'French', 
    icon: <Flag className="h-4 w-4" />,
    rtl: false 
  },
  { 
    code: 'es', 
    label: 'Spanish', 
    icon: <Flag className="h-4 w-4" />,
    rtl: false 
  },
  { 
    code: 'ar', 
    label: 'Arabic', 
    icon: <Flag className="h-4 w-4" />,
    rtl: true 
  }
];

export const customIcons = {
  default: <Languages className="h-[1.2rem] w-[1.2rem]" />,
  flag: <Flag className="h-[1.2rem] w-[1.2rem]" />
};

export const storybookEnvironments = {
  standard: {
    window: {
      parent: window,
      location: {
        href: 'https://example.com'
      }
    }
  },
  storybook: {
    window: {
      parent: {}, // Different from window
      location: {
        href: 'https://example.com/?viewMode=story'
      }
    }
  },
  iframe: {
    window: {
      parent: window,
      location: {
        href: 'https://example.com/iframe.html'
      }
    }
  }
}; 