import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';

const modalVariants = cva(
  'fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out',
  {
    variants: {
      size: {
        default: 'sm:max-w-lg',
        sm: 'sm:max-w-sm',
        lg: 'sm:max-w-xl',
        xl: 'sm:max-w-2xl',
        full: 'sm:max-w-full'
      },
      position: {
        default: 'data-[state=open]:fade-in-90 data-[state=closed]:fade-out-90 data-[state=closed]:slide-out-to-bottom-[2%] data-[state=open]:slide-in-from-bottom-[2%]',
        top: 'data-[state=open]:fade-in-90 data-[state=closed]:fade-out-90 data-[state=closed]:slide-out-to-top-[2%] data-[state=open]:slide-in-from-top-[2%]',
        center: 'data-[state=open]:zoom-in-90 data-[state=closed]:zoom-out-90 start-50 top-50 translate-x-[-50%] translate-y-[-50%]'
      }
    },
    defaultVariants: {
      size: 'default',
      position: 'default'
    }
  }
);

export interface ModalProps extends VariantProps<typeof modalVariants> {
  children?: React.ReactNode;
  title?: string;
  description?: string;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
  showCloseButton?: boolean;
  footer?: React.ReactNode;
}

export const Modal = React.forwardRef<HTMLDivElement, ModalProps>(
  ({ 
    children, 
    title, 
    description, 
    isOpen, 
    onClose, 
    size, 
    position, 
    className, 
    showCloseButton = true,
    footer
  }, ref) => {
    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <DialogContent 
          ref={ref}
          className={cn(modalVariants({ size, position }), className)}
          data-testid="modal-content"
        >
          {showCloseButton && (
            <DialogClose 
              className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
              onClick={onClose}
              data-testid="modal-close-button"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          )}
          
          {(title || description) && (
            <DialogHeader>
              {title && <DialogTitle data-testid="modal-title">{title}</DialogTitle>}
              {description && <DialogDescription data-testid="modal-description">{description}</DialogDescription>}
            </DialogHeader>
          )}
          
          <div data-testid="modal-body">{children}</div>
          
          {footer && (
            <DialogFooter data-testid="modal-footer">{footer}</DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    );
  }
);

Modal.displayName = 'Modal';

export default Modal;