import{j as e,m as a,T as r}from"./index-Bwql5Dzz.js";var p=({title:l,value:s,actions:i})=>{const t=typeof s=="string"||!s;return e.jsxs("div",{className:a("text-ui-fg-subtle grid w-full grid-cols-2 items-center gap-4 px-6 py-4",{"grid-cols-[1fr_1fr_28px]":!!i}),children:[e.jsx(r,{size:"small",weight:"plus",leading:"compact",children:l}),t?e.jsx(r,{size:"small",leading:"compact",className:"whitespace-pre-line text-pretty",children:s??"-"}):e.jsx("div",{className:"flex flex-wrap gap-1",children:s}),i&&e.jsx("div",{children:i})]})};export{p as S};
