import React from 'react';
import Carousel from './carousel';
import { cn } from '@/lib/utils'; // Utility for merging class names

interface BrandSlide {
  id: string | number;
  image: string;
  title?: string;
  link?: string;
}

export interface CoverflowCarouselProps {
  /**
   * Array of brand slides
   */
  items: BrandSlide[];
  
  /**
   * Enable autoplay
   * @default true
   */
  autoplay?: boolean;
  
  /**
   * Autoplay delay in milliseconds
   * @default 3000
   */
  autoplayDelay?: number;
  
  /**
   * Custom class name
   */
  className?: string;
  
  /**
   * Additional options to pass to Swiper
   */
  swiperOptions?: Record<string, any>;
}

/**
 * Coverflow carousel specifically for brand slides
 * Implements the design from the Figma "Brand Slide" frame
 */
const CoverflowCarousel: React.FC<CoverflowCarouselProps> = ({
  items,
  autoplay = true,
  autoplayDelay = 3000,
  className,
  swiperOptions = {},
}) => {
  // Enhanced coverflow effect settings to match the design
  const coverflowOptions = {
    effect: 'coverflow',
    grabCursor: true,
    centeredSlides: true,
    slidesPerView: 'auto',
    coverflowEffect: {
      rotate: 0,
      stretch: 0,
      depth: 150,
      modifier: 1.5,
      slideShadows: false,
    },
    initialSlide: 1,
    loop: true,
    ...swiperOptions,
  };

  // Render function for brand slides
  const renderBrandSlide = (item: BrandSlide, index: number) => {
    return (
      <div className="brand-slide-container">
        <div className="brand-slide relative cursor-pointer overflow-hidden rounded-lg">
          {/* Image with conditional link */}
          {item.link ? (
            <a href={item.link} target="_blank" rel="noopener noreferrer">
              <img 
                src={item.image} 
                alt={item.title || `Brand slide ${index + 1}`} 
                className="w-full h-auto object-cover transition-transform duration-300 hover:scale-105"
              />
            </a>
          ) : (
            <img 
              src={item.image} 
              alt={item.title || `Brand slide ${index + 1}`} 
              className="w-full h-auto object-cover"
            />
          )}
          
          {/* Optional title overlay */}
          {item.title && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
              <h3 className="text-white font-medium">{item.title}</h3>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Carousel
      variant="coverflow"
      items={items}
      renderItem={renderBrandSlide}
      autoplay={autoplay}
      autoplayDelay={autoplayDelay}
      className={cn("brand-coverflow-carousel", className)}
      customOptions={coverflowOptions}
    />
  );
};

export default CoverflowCarousel;