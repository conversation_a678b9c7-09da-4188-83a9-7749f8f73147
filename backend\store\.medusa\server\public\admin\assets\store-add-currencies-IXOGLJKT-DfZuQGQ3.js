import{u as M,a as Q,b as L}from"./chunk-NEZX6265-Dnnail08.js";import{a2 as O,ad as U,a4 as G,ao as X,a5 as Z,g as $,l as J,j as e,b as I,a8 as W,a9 as Y,r as j,ed as ee,t as k,q as re,ee as se,E as te,B as T,V as oe,ab as ce,k as ie}from"./index-Bwql5Dzz.js";import{u as ae,_ as ne}from"./chunk-X3LH6P65-BtKDvzuz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{K as le}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as p,u as de}from"./chunk-JGQGO74V-DtHO1ucg.js";import{C as E}from"./checkbox-B4pL6X49.js";import{c as ue}from"./index-BxZ1678G.js";import"./chunk-MSDRGCRR-BLk8RuFZ.js";import"./chunk-P3UUX2T6-CnJzifYv.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./prompt-BsR9zKsn.js";var me=O({currencies:U(G()).min(1),pricePreferences:X(Z())}),R=50,S="ac",pe=({store:t,pricePreferences:n})=>{var P;const{t:c}=I(),{handleSuccess:m}=de(),{raw:s,searchParams:i}=M({pageSize:50,prefix:S}),{currencies:l,count:a,isPending:d,isError:x,error:y}=Q(i,{placeholderData:ie}),f=W({defaultValues:{currencies:[],pricePreferences:n==null?void 0:n.reduce((r,o)=>(o.value&&(r[o.value]=o.is_tax_inclusive),r),{})},resolver:Y(me)}),[b,F]=j.useState({}),{setValue:g,watch:N}=f,z=N("pricePreferences"),D=r=>{const o=typeof r=="function"?r(b):r,h=Object.keys(o);g("currencies",h,{shouldDirty:!0,shouldTouch:!0}),F(o)},v=((P=t.supported_currencies)==null?void 0:P.map(r=>r.currency_code))??[],q=j.useCallback(r=>{g("pricePreferences",r)},[g]),C=fe(z,q),{table:B}=ae({data:l??[],columns:C,count:a,getRowId:r=>r.code,enableRowSelection:r=>!v.includes(r.original.code),enablePagination:!0,pageSize:R,prefix:S,rowSelection:{state:b,updater:D}}),{mutateAsync:H,isPending:K}=ee(t.id),V=f.handleSubmit(async r=>{var _,w;const o=Array.from(new Set([...r.currencies,...v]));let h=(w=(_=t.supported_currencies)==null?void 0:_.find(u=>u.is_default))==null?void 0:w.currency_code;o.includes(h??"")||(h=o==null?void 0:o[0]),await H({supported_currencies:o.map(u=>({currency_code:u,is_default:u===h,is_tax_inclusive:r.pricePreferences[u]}))},{onSuccess:()=>{k.success(c("store.toast.currenciesUpdated")),re.invalidateQueries({queryKey:se.all}),m()},onError:u=>{k.error(u.message)}})});if(x)throw y;return e.jsx(p.Form,{form:f,children:e.jsxs(le,{onSubmit:V,className:"flex h-full flex-col overflow-hidden",children:[e.jsx(p.Header,{children:e.jsx("div",{className:"flex flex-1 items-center justify-between",children:e.jsx("div",{className:"flex items-center",children:f.formState.errors.currencies&&e.jsx(te,{variant:"error",children:f.formState.errors.currencies.message})})})}),e.jsx(p.Body,{className:"flex flex-1 flex-col overflow-hidden",children:e.jsx(ne,{table:B,pageSize:R,count:a,columns:C,layout:"fill",pagination:!0,search:"autofocus",prefix:S,orderBy:[{key:"name",label:c("fields.name")},{key:"code",label:c("fields.code")}],isLoading:d,queryObject:s})}),e.jsx(p.Footer,{children:e.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[e.jsx(p.Close,{asChild:!0,children:e.jsx(T,{size:"small",variant:"secondary",children:c("actions.cancel")})}),e.jsx(T,{size:"small",type:"submit",isLoading:K,children:c("actions.save")})]})})]})})},A=ue(),fe=(t,n)=>{const{t:c}=I(),m=L();return j.useMemo(()=>[A.display({id:"select",header:({table:s})=>e.jsx(E,{checked:s.getIsSomePageRowsSelected()?"indeterminate":s.getIsAllPageRowsSelected(),onCheckedChange:i=>s.toggleAllPageRowsSelected(!!i)}),cell:({row:s})=>{const i=!s.getCanSelect(),l=s.getIsSelected()||i,a=e.jsx(E,{checked:l,disabled:i,onCheckedChange:d=>s.toggleSelected(!!d),onClick:d=>{d.stopPropagation()}});return i?e.jsx(oe,{content:c("store.currencyAlreadyAdded"),side:"right",children:a}):a}}),...m,A.display({id:"select",header:()=>e.jsx("div",{className:"whitespace-nowrap",children:c("fields.taxInclusivePricing")}),cell:({row:s})=>{const i=!s.getCanSelect(),l=t[s.original.code];return e.jsx("div",{className:"flex items-center justify-end",children:e.jsx(ce,{disabled:i,checked:l??!1,onCheckedChange:a=>{n({...t,[s.original.code]:a})}})})}})],[c,m,t,n])},Le=()=>{var x;const{store:t,isPending:n,isError:c,error:m}=$(),{price_preferences:s,isPending:i,isError:l,error:a}=J({attribute:"currency_code",value:(x=t==null?void 0:t.supported_currencies)==null?void 0:x.map(y=>y.currency_code)},{enabled:!!t}),d=!!t&&!n&&!!s&&!i;if(c)throw m;if(l)throw a;return e.jsx(p,{children:d&&e.jsx(pe,{store:t,pricePreferences:s})})};export{Le as Component};
