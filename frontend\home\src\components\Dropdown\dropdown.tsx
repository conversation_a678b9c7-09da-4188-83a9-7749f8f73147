import React, { forwardRef, useState, ReactNode } from 'react';
import { Check, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';

export interface DropdownOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: ReactNode;
}

export type DropdownVariant = 'default' | 'blue' | 'white';

export interface DropdownProps {
  id?: string;
  value?: string;
  defaultValue?: string;
  options: DropdownOption[];
  placeholder?: string;
  label?: string;
  helperText?: string;
  error?: boolean;
  errorMessage?: string;
  disabled?: boolean;
  required?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  width?: string;
  className?: string;
  noOptionsMessage?: string;
  onValueChange?: (value: string) => void;
  onChange?: (event: { target: { value: string; name?: string } }) => void;
  name?: string;
  // Added props
  variant?: DropdownVariant;
  size?: 'sm' | 'default' | 'lg';
  prefixIcon?: ReactNode;
  showIcon?: boolean; // Show option icons in dropdown button
  popoverWidth?: string;
  triggerClassName?: string;
  contentClassName?: string;
}

const Dropdown = forwardRef<HTMLButtonElement, DropdownProps>(
  ({
    id,
    value,
    defaultValue,
    options,
    placeholder = 'Select an option',
    label,
    helperText,
    error = false,
    errorMessage,
    disabled = false,
    required = false,
    searchable = false,
    clearable = false,
    width = '100%',
    className,
    noOptionsMessage = 'No options found',
    onValueChange,
    onChange,
    name,
    // Added props with default values
    variant = 'default',
    size = 'default',
    prefixIcon,
    showIcon = false,
    popoverWidth,
    triggerClassName,
    contentClassName,
  }, ref) => {
    const [open, setOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState<string | undefined>(value || defaultValue);
    
    // Get the selected option
    const selectedOption = options.find(option => option.value === selectedValue);

    // Determine height based on size
    const sizeClasses = {
      sm: "h-8 text-sm py-1",
      default: "h-10 py-2",
      lg: "h-12 text-lg py-3"
    }[size];

    // Determine variant classes
    const variantClasses = {
      'default': "bg-transparent hover:bg-accent",
      'blue': "bg-[#f8fafc] hover:bg-[#f1f5f9]",
      'white': "bg-white hover:bg-[#f8fafc]"
    }[variant];

    const handleSelect = (currentValue: string) => {
      // If clicking on the same value and clearable is true, clear the selection
      if (clearable && currentValue === selectedValue) {
        setSelectedValue(undefined);
        
        if (onValueChange) {
          onValueChange('');
        }
        
        if (onChange) {
          onChange({ target: { value: '', name } });
        }
      } else {
        setSelectedValue(currentValue);
        
        if (onValueChange) {
          onValueChange(currentValue);
        }
        
        if (onChange) {
          onChange({ target: { value: currentValue, name } });
        }
      }
      
      setOpen(false);
    };

    // Determine which icon to show for the selected option
    const getOptionIcon = (option: DropdownOption | undefined) => {
      if (!showIcon) return null;
      return option?.icon;
    };

    return (
      <div className="w-full space-y-2" data-testid="dropdown-container" style={{ width }}>
        {label && (
          <Label 
            htmlFor={id} 
            className={cn(
              error && "text-destructive",
              required && "after:content-['*'] after:ml-0.5 after:text-destructive"
            )}
          >
            {label}
          </Label>
        )}
        
        <Popover open={open && !disabled} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              ref={ref}
              id={id}
              variant="outline"
              role="combobox"
              aria-expanded={open}
              aria-required={required}
              aria-invalid={error}
              aria-describedby={
                error ? `${id}-error` : helperText ? `${id}-helper` : undefined
              }
              className={cn(
                "w-full justify-between border-input",
                variantClasses,
                sizeClasses,
                error && "border-destructive text-destructive",
                "text-left font-normal",
                !selectedValue && "text-muted-foreground",
                triggerClassName,
                className
              )}
              disabled={disabled}
              onClick={() => !disabled && setOpen(!open)}
            >
              <div className="flex items-center gap-2 overflow-hidden">
                {prefixIcon && <span className="flex-shrink-0">{prefixIcon}</span>}
                {showIcon && selectedOption?.icon && (
                  <span className="flex-shrink-0 mr-2">{getOptionIcon(selectedOption)}</span>
                )}
                <span className="truncate">
                  {selectedOption ? selectedOption.label : placeholder}
                </span>
              </div>
              <ChevronDown className={cn("ml-2 h-4 w-4 shrink-0 opacity-50", open && "hidden")} />
              <ChevronUp className={cn("ml-2 h-4 w-4 shrink-0 opacity-50", !open && "hidden")} />
            </Button>
          </PopoverTrigger>
          <PopoverContent 
            className={cn("w-full p-0", contentClassName)} 
            style={{ width: popoverWidth || width }}
          >
            <Command>
              {searchable && (
                <CommandInput placeholder="Search options..." className="h-9" />
              )}
              <CommandEmpty>{noOptionsMessage}</CommandEmpty>
              <CommandGroup className="max-h-60 overflow-auto">
                {options.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    disabled={option.disabled}
                    onSelect={() => handleSelect(option.value)}
                    className={option.disabled ? "cursor-not-allowed opacity-50" : ""}
                  >
                    <div className="flex items-center gap-2">
                      {option.icon && (
                        <span className="flex-shrink-0">{option.icon}</span>
                      )}
                      <span>{option.label}</span>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedValue === option.value ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </Command>
          </PopoverContent>
        </Popover>
        
        {helperText && !error && (
          <p id={`${id}-helper`} className="text-sm text-muted-foreground">
            {helperText}
          </p>
        )}
        
        {error && errorMessage && (
          <p id={`${id}-error`} className="text-sm font-medium text-destructive">
            {errorMessage}
          </p>
        )}
      </div>
    );
  }
);

Dropdown.displayName = 'Dropdown';

export { Dropdown };