import{u as k,a as T,b as E}from"./chunk-NOAFLTPV-DWvXax-e.js";import{u as A,_ as I}from"./chunk-X3LH6P65-BtKDvzuz.js";import{a2 as F,ad as z,a4 as B,R as N,e8 as D,j as o,b as H,r as g,a8 as M,a9 as K,cN as L,ea as O,t as x,B as S}from"./index-Bwql5Dzz.js";import"./lodash-CPCX-RQp.js";import"./chunk-TMAS4ILY-DPw6o7wu.js";import{K as V}from"./chunk-6HTZNHPT-N4svn6ad.js";import{R as n,u as q}from"./chunk-JGQGO74V-DtHO1ucg.js";import{C as b}from"./checkbox-B4pL6X49.js";import{c as G}from"./index-BxZ1678G.js";import"./chunk-C76H5USB-ByRPKhW7.js";import"./chunk-YEDAFXMB-BvYBZbFD.js";import"./chunk-AOFGTNG6-D6NUsOHO.js";import"./table-BDZtqXjX.js";import"./chunk-WX2SMNCD-B6fFhFh4.js";import"./plus-mini-C5sDHkH8.js";import"./command-bar-Cyd2ymXA.js";import"./index-DP5bcQyU.js";import"./chunk-DV5RB7II-B2VrP-dr.js";import"./format-Cpg7FCX8.js";import"./_isIndex-bB1kTSVv.js";import"./x-mark-mini-DvSTI7zK.js";import"./date-picker-C167G6yz.js";import"./clsx-B-dksMZM.js";import"./popover-B2TSwh5F.js";import"./triangle-left-mini-Bu6679Aa.js";import"./index-DX0YxfHa.js";import"./prompt-BsR9zKsn.js";var Q=F({countries:z(B()).min(1)}),m=50,p="ac",U=({region:r})=>{const{t:e}=H(),{handleSuccess:a}=q(),[t,l]=g.useState({}),u=M({defaultValues:{countries:[]},resolver:K(Q)}),{setValue:h}=u;g.useEffect(()=>{const s=Object.keys(t).filter(i=>t[i]);h("countries",s,{shouldDirty:!0,shouldTouch:!0})},[t,h]);const{searchParams:C,raw:j}=k({pageSize:m,prefix:p}),{countries:_,count:f}=T({countries:L.map((s,i)=>({display_name:s.display_name,name:s.name,id:i,iso_2:s.iso_2,iso_3:s.iso_3,num_code:s.num_code,region_id:null,region:{}})),...C}),y=Z(),{table:R}=A({data:_||[],columns:y,count:f,enablePagination:!0,enableRowSelection:s=>{var i;return((i=r.countries)==null?void 0:i.findIndex(c=>c.iso_2===s.original.iso_2))===-1},getRowId:s=>s.iso_2,pageSize:m,rowSelection:{state:t,updater:l},prefix:p}),{mutateAsync:v,isPending:P}=O(r.id),w=u.handleSubmit(async s=>{var c;const i=[...((c=r.countries)==null?void 0:c.map(d=>d.iso_2))??[],...s.countries];await v({countries:i},{onSuccess:()=>{x.success(e("regions.toast.countries")),a()},onError:d=>{x.error(d.message)}})});return o.jsx(n.Form,{form:u,children:o.jsxs(V,{onSubmit:w,className:"flex h-full flex-col overflow-hidden",children:[o.jsx(n.Header,{children:o.jsxs("div",{className:"flex items-center justify-end gap-x-2",children:[o.jsx(n.Close,{asChild:!0,children:o.jsx(S,{size:"small",variant:"secondary",children:e("actions.cancel")})}),o.jsx(S,{size:"small",isLoading:P,type:"submit",children:e("actions.add")})]})}),o.jsx(n.Body,{className:"overflow-hidden",children:o.jsx(I,{table:R,columns:y,pageSize:m,count:f,search:"autofocus",pagination:!0,layout:"fill",orderBy:[{key:"display_name",label:e("fields.name")},{key:"iso_2",label:e("fields.code")}],queryObject:j,prefix:p})})]})})},X=G(),Z=()=>{const r=E();return g.useMemo(()=>[X.display({id:"select",header:({table:e})=>o.jsx(b,{checked:e.getIsSomePageRowsSelected()?"indeterminate":e.getIsAllPageRowsSelected(),onCheckedChange:a=>e.toggleAllPageRowsSelected(!!a)}),cell:({row:e})=>{const a=!e.getCanSelect();return o.jsx(b,{checked:e.getIsSelected()||a,disabled:a,onCheckedChange:t=>e.toggleSelected(!!t),onClick:t=>{t.stopPropagation()}})}}),...r],[r])},_e=()=>{const{id:r}=N(),{region:e,isPending:a,isError:t,error:l}=D(r,{fields:"*payment_providers"});if(t)throw l;return o.jsx(n,{children:!a&&e&&o.jsx(U,{region:e})})};export{_e as Component};
