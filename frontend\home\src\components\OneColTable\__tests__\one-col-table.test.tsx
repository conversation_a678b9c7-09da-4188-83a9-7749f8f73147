import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import OneColTable from '../one-col-table';
import { mockItems } from '../__fixtures__/one-col-table.fixtures';

describe('OneColTable', () => {
  // Rendering tests
  describe('rendering', () => {
    it('renders correctly with default props', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} />);
      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(screen.getByText('Batch Processing #1')).toBeInTheDocument();
      expect(screen.getByText('Batch Processing #2')).toBeInTheDocument();
      expect(screen.getByText('Batch Processing #3')).toBeInTheDocument();
    });

    it('renders skeleton when loading is true', () => {
      render(<OneColTable items={mockItems} loading />);
      expect(screen.getByRole('status')).toBeInTheDocument();
      expect(screen.queryByText('Batch Processing #1')).not.toBeInTheDocument();
    });

    it('renders empty state when no items are provided', () => {
      render(<OneColTable items={[]} />);
      expect(screen.getByText('No items found')).toBeInTheDocument();
    });

    it('renders custom empty state when provided', () => {
      const customEmptyState = <div>Custom empty state</div>;
      render(<OneColTable items={[]} emptyState={customEmptyState} />);
      expect(screen.getByText('Custom empty state')).toBeInTheDocument();
    });

    it('renders header when provided', () => {
      const header = <div>Custom Header</div>;
      render(<OneColTable items={mockItems.slice(0, 3)} header={header} />);
      expect(screen.getByText('Custom Header')).toBeInTheDocument();
    });

    it('renders "Load more" button when showLoadMore and hasMore are true', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} showLoadMore hasMore />);
      expect(screen.getByRole('button', { name: /load more/i })).toBeInTheDocument();
    });

    it('does not render "Load more" button when hasMore is false', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} showLoadMore hasMore={false} />);
      expect(screen.queryByRole('button', { name: /load more/i })).not.toBeInTheDocument();
    });
  });

  // Interaction tests
  describe('interactions', () => {
    it('calls onRowClick when a row is clicked', async () => {
      const handleRowClick = jest.fn();
      render(<OneColTable items={mockItems.slice(0, 3)} onRowClick={handleRowClick} />);

      const firstRow = screen.getByText('Batch Processing #1').closest('[role="row"]');
      if (firstRow) {
        await userEvent.click(firstRow);
        expect(handleRowClick).toHaveBeenCalledWith(mockItems[0], 0);
      }
    });

    it('calls onLoadMore when "Load more" button is clicked', async () => {
      const handleLoadMore = jest.fn();
      render(
        <OneColTable
          items={mockItems.slice(0, 3)}
          showLoadMore
          hasMore
          onLoadMore={handleLoadMore}
        />,
      );

      const loadMoreButton = screen.getByRole('button', { name: /load more/i });
      await userEvent.click(loadMoreButton);
      expect(handleLoadMore).toHaveBeenCalled();
    });

    it('does not call onRowClick when disabled', async () => {
      const handleRowClick = jest.fn();
      render(<OneColTable items={mockItems.slice(0, 3)} onRowClick={handleRowClick} disabled />);

      const firstRow = screen.getByText('Batch Processing #1').closest('[role="row"]');
      if (firstRow) {
        await userEvent.click(firstRow);
        expect(handleRowClick).not.toHaveBeenCalled();
      }
    });
  });

  // Props and variants tests
  describe('props and variants', () => {
    it('applies the correct size class', () => {
      const { rerender } = render(<OneColTable items={mockItems.slice(0, 3)} size="sm" />);
      expect(screen.getByRole('table')).toHaveClass('text-sm');

      rerender(<OneColTable items={mockItems.slice(0, 3)} size="lg" />);
      expect(screen.getByRole('table')).toHaveClass('text-lg');
    });

    it('applies the correct variant class', () => {
      const { rerender } = render(
        <OneColTable items={mockItems.slice(0, 3)} variant="secondary" />,
      );
      expect(screen.getByRole('table')).toHaveClass('bg-muted');

      rerender(<OneColTable items={mockItems.slice(0, 3)} variant="outline" />);
      expect(screen.getByRole('table')).toHaveClass('border');
    });

    it('applies the correct state class when disabled', () => {
      render(<OneColTable items={mockItems.slice(0, 3)} disabled />);
      expect(screen.getByRole('table')).toHaveClass('opacity-50');
      expect(screen.getByRole('table')).toHaveAttribute('aria-disabled', 'true');
    });

    it('limits the number of items when limit prop is provided', () => {
      render(<OneColTable items={mockItems} limit={2} />);
      expect(screen.getByText('Batch Processing #1')).toBeInTheDocument();
      expect(screen.getByText('Batch Processing #2')).toBeInTheDocument();
      expect(screen.queryByText('Batch Processing #3')).not.toBeInTheDocument();
    });
  });
});
