{"app": {"title": "タイムライン", "description": "時系列イベントを表示するためのカスタマイズ可能なタイムラインコンポーネント。"}, "nav": {"home": "ホーム", "about": "概要", "settings": "設定", "installation": "インストール", "usage": "使用方法", "examples": "サンプル", "rendering_examples": "レンダリング例"}, "buttons": {"submit": "送信", "cancel": "キャンセル", "save": "保存", "delete": "削除", "clickMe": "クリックしてください"}, "language": {"en": "英語", "fr": "フランス語", "ja": "日本語", "ar": "アラビア語", "switchLanguage": "言語を切り替える", "current": "現在の言語"}, "theme": {"current": "現在のテーマ"}, "rendering": {"backToExamples": "例に戻る", "backToCSR": "CSR例に戻る", "backToSPA": "SPA例に戻る", "backToSSR": "SSR例に戻る", "viewRouterExample": "テーマと言語サポート付きのルーター例を表示", "viewFullSPAExample": "テーマと言語サポート付きの完全なSPAルーター例を表示", "basicExample": "基本例", "withDataExample": "データ付き例", "advancedExample": "高度な例", "ssr": "SSR", "ssg": "SSG", "csr": "CSR", "isr": "ISR", "spa": "SPA", "routes": {"basic": "基本", "withData": "データ付き", "advanced": "高度", "home": "ホーム", "about": "概要", "products": "製品", "contact": "お問い合わせ"}, "ssrTitle": "サーバーサイドレンダリング (SSR)", "ssrHowWorks": "SSRの仕組み", "ssrDescription": "サーバーサイドレンダリングでは、このページのHTMLはリクエストごとにサーバー上で生成されます。これはユーザーがページをリクエストしたときに生成されるため、コンテンツは常に最新の状態です。", "ssrNote": "これはコンテンツが頻繁に変更されるページや、各ユーザーにパーソナライズする必要があるページに役立ちます。SSRは検索エンジンが完全にレンダリングされたコンテンツを見ることができるため、SEOにも最適です。", "ssrServerData": "サーバー生成データ", "ssrServerDataDescription": "このデータはリクエスト時にサーバー上で生成されました：", "ssrRefreshNote": "ページを更新すると、これらの値が変化し、ページがリクエストごとにレンダリングされていることを確認できます。", "ssrImplementation": "実装", "ssrImplementationDescription": "Next.js 15では、AppRouterのページのデフォルトはSSRです。データを取得するサーバーコンポーネントをエクスポートするだけです。", "ssrWithData": "詳細データ付きSSR", "ssrDataFetch": "動的ユーザーデータ", "ssrDataFetchDescription": "この例では、各リクエストごとにサーバー上でより複雑なデータを取得する方法を示しています。以下では、ページが読み込まれるたびに新たに生成されるユーザーデータを見ることができます。", "userData": "ユーザーデータ", "userId": "ID", "userName": "名前", "userRole": "役割", "userLastActive": "最終アクティブ", "systemStatus": "システム状態", "cpuUsage": "CPU使用率", "memoryUsage": "メモリ使用率", "uptime": "稼働時間", "generatedAt": "生成時刻", "ssrAdvanced": "高度なSSR分析", "realTimeAnalytics": "リアルタイム分析ダッシュボード", "ssrAdvancedDescription": "この高度な例では、SSRを使用して、リクエストごとに最新情報を含む複雑なデータ豊富なダッシュボードを生成する方法を示しています。", "pageViews": "ページビュー", "today": "今日", "weekly": "週間", "monthly": "月間", "userSessions": "ユーザーセッション", "avgDuration": "平均時間", "bounceRate": "直帰率", "newUsers": "新規ユーザー", "serverLoad": "サーバー負荷", "current": "現在", "average": "平均", "peak": "ピーク", "ssrAdvantages": "SSRの利点", "ssrAdvantage1": "リクエストごとに常に最新のデータ", "ssrAdvantage2": "検索エンジンが完全なHTMLを見ることができる優れたSEO", "ssrAdvantage3": "ユーザーにとってのコンテンツ表示時間の短縮", "ssrAdvantage4": "JavaScriptが無効でも正常に動作", "ssrRouterTitle": "サーバーサイドレンダリング (SSR) ルーター例", "ssrRouterHowWorks": "SSRルーティングの仕組み", "ssrRouterDescription": "サーバーサイドレンダリングでは、各ルート変更はサーバーへの新しいリクエストをトリガーし、そのルートのHTMLが生成されます。これは各ページが最新のデータでサーバー上で新たにレンダリングされることを意味します。", "ssrRouterNote": "これにより常に最新のコンテンツが得られますが、ナビゲーション中に完全なページの再読み込みが発生することに気づくでしょう。テーマと言語の設定はCookieまたはローカルストレージを通じて維持されます。", "csrTitle": "クライアントサイドレンダリング (CSR)", "csrHowWorks": "CSRの仕組み", "csrDescription": "クライアントサイドレンダリングでは、サーバーは最小限のHTMLページを送信し、JavaScriptがブラウザ内でページを構築します。これはJavaScriptが読み込まれると最小限のコンテンツから始まり、完全なUIで「ハイドレート」される様子に見られます。", "csrNote": "CSRはほとんどのコンテンツがユーザー固有でSEOがそれほど重要でない高度にインタラクティブなページに適しています。", "clientState": "クライアントサイドの状態", "clientStateDescription": "これらの値はブラウザでJavaScriptが実行された後にのみ利用可能です：", "clientStateLoading": "クライアントサイドデータを読み込み中...", "clientTime": "ブラウザ時間", "windowSize": "ウィンドウサイズ", "refreshNote": "ページを更新して、CSRがスタイルのない/最小限のコンテンツのフラッシュを引き起こす様子を確認してください。", "csrWithData": "データ取得付きCSR", "csrDataFetch": "クライアントサイドデータ取得", "csrDataFetchDescription": "CSRでは、JavaScriptが読み込まれた後にブラウザによってデータが取得されます。これは、データが表示されるまでに常に遅延があることを意味します。", "fetchedData": "取得したデータ", "loadingData": "データを読み込み中...", "dataMessage": "メッセージ", "dataTimestamp": "タイムスタンプ", "csrAdvanced": "高度なCSRテクニック", "csrAdvancedTips": "CSRアプリケーションの最適化", "csrAdvancedDescription": "高度なCSRアプリケーションは、コード分割、遅延読み込み、クライアントサイドキャッシュなどのテクニックを使用してパフォーマンスを向上させます。", "csrBestPractices": "ベストプラクティス", "csrBestPractice1": "コンテンツが取得中であることを示すためのローディング状態を使用する", "csrBestPractice2": "エラーを適切に処理するためのエラー境界を実装する", "csrBestPractice3": "オフライン機能のためのサービスワーカーの使用を検討する", "csrBestPractice4": "可能な場合は重要なデータを事前に読み込む", "csrRouterTitle": "CSRルーティングの仕組み", "csrRouterDescription": "クライアントサイドレンダリングでは、初期HTMLは最小限であり、JavaScriptがブラウザ内でページを構築します。ルート間のナビゲーションは、ページの完全な再読み込みなしにブラウザ内で完全に行われます。", "csrRouterNote": "このアプローチは、ルート変更間で状態が保持される滑らかなアプリのような体験を提供します。ページが完全に再読み込みされないため、テーマと言語の設定はナビゲーション中に自然に保持されます。", "spaTitle": "シングルページアプリケーション（SPA）", "spaHowWorks": "SPAの仕組み", "spaDescription": "シングルページアプリケーションは、1つのHTMLページを読み込み、ユーザーがアプリとインタラクションするにつれて動的にコンテンツを更新します。「ページ」間のナビゲーションは、ブラウザを更新せずにクライアントサイドで完全に行われます。", "spaNote": "SPAは、ページの再読み込みを必要としないためより滑らかなユーザー体験を提供し、アプリケーションの状態を維持し、よりアプリに近いインタラクションを可能にします。現在のURLはブラウザのHistory APIを使用して管理されます。", "currentUrl": "現在のURL", "loadedAt": "読み込み時刻", "spaFeatures": "SPAの主な特徴", "spaFeature1": "ページ再読み込みなしのクライアントサイドルーティング", "spaFeature2": "「ページ」遷移間のアプリケーション状態の永続性", "spaFeature3": "サービスワーカーによるオフライン動作能力", "spaFeature4": "ビュー間のスムーズな遷移とアニメーション", "spaRouterTitle": "SPAルーティングの仕組み", "spaRouterDescription": "シングルページアプリケーションでは、すべてのルーティングはページを更新せずにクライアントサイドで行われます。アプリはURL変更をインターセプトし、サーバーから新しいHTMLをリクエストすることなく適切なコンテンツをレンダリングします。", "spaRouterNote": "これは永続的な状態とページのフラッシュなしで最もスムーズなユーザー体験を提供します。現在のURL："}, "timeConversion": {"title": "時間変換", "utc": "世界標準時", "local": "現地時間", "timezone": "タイムゾーン", "date": "日付", "time": "時間"}, "tableDynamic": {"empty": {"title": "データがありません", "description": "表示するアイテムはありません。"}, "error": {"generic": "データの読み込み中にエラーが発生しました。"}, "filter": {"searchPlaceholder": "検索...", "clearSearch": "検索をクリア", "filters": "フィルター", "filterColumns": "列をフィルタリング", "clearAllFilters": "すべてのフィルターをクリア"}, "footer": {"rowsPerPage": "1ページあたりの行数：", "pageInfo": "ページ {{current}}/{{total}} ({{from}}-{{to}}/全{{totalRows}}アイテム)", "firstPage": "最初のページ", "previousPage": "前のページ", "nextPage": "次のページ", "lastPage": "最後のページ"}, "skeleton": {"ariaLabel": "テーブルコンテンツを読み込み中"}}, "userTable": {"headers": {"name": "名前", "email": "メール", "role": "役割", "status": "状態", "lastLogin": "最終ログイン"}, "status": {"active": "アクティブ", "inactive": "非アクティブ", "pending": "保留中"}, "empty": {"title": "ユーザーが見つかりません", "description": "表示するユーザーはありません。"}, "error": {"title": "エラー", "retry": "再試行"}, "footer": {"total": "合計：{{count}}人のユーザー"}}, "apiKey": {"name": {"label": "名前", "placeholder": "任意", "serviceKeyLabel": "サービスキー名", "serviceKeyPlaceholder": "マイサービスアカウントキー", "userKeyPlaceholder": "マイテストキー"}, "ownerType": {"label": "所有者", "you": "あなた", "serviceAccount": "サービスアカウント"}, "permissionType": {"label": "権限", "all": "すべて", "restricted": "制限付き", "readOnly": "読み取り専用"}, "project": {"label": "プロジェクト", "placeholder": "プロジェクトを選択"}, "resources": {"title": "リソース", "permissions": "権限", "models": "モデル", "modelsPath": "/v1/models", "modelCapabilities": "モデル機能", "modelCapabilitiesPath": "/v1/audio, /v1/chat/completions, /v1/embeddings, /v1/images, /v1/moderations", "assistants": "アシスタント", "assistantsPath": "/v1/assistants", "assistantsModelsPath": "/v1/models (アシスタントに必要)", "threads": "スレッド", "threadsPath": "/v1/threads", "threadsModelsPath": "/v1/models (スレッドに必要)", "evals": "評価", "evalsPath": "/v1/evals", "fineTuning": "ファインチューニング", "fineTuningPath": "/v1/fine_tuning", "files": "ファイル", "filesPath": "/v1/files"}, "resourcePermissions": {"none": "なし", "read": "読み取り", "write": "書き込み"}, "descriptions": {"serviceAccount": "新しいボットメンバー（サービスアカウント）がプロジェクトに追加され、APIキーが作成されます。", "userAccount": "このAPIキーはあなたのユーザーに紐づけられ、選択したプロジェクトに対してリクエストを行うことができます。組織やプロジェクトから削除された場合、このキーは無効になります。", "personal": "このAPIキーはあなたのユーザーに紐づけられ、選択したプロジェクトに対してリクエストを行うことができます。組織やプロジェクトから削除された場合、このキーは無効になります。", "permissionChange": "権限の変更が反映されるまで数分かかる場合があります。"}, "actions": {"cancel": "キャンセル", "save": "保存", "createKey": "シークレットキーを作成", "create": "シークレットキーを作成", "submitting": "送信中..."}, "titles": {"create": "新しいシークレットキーを作成", "edit": "シークレットキーを編集"}, "validation": {"required": "このフィールドは必須です", "name": "名前には文字、数字、ハイフン、アンダースコアのみを含める必要があります"}}, "batchDetailsCard": {"title": "バッチ", "status": {"label": "ステータス", "completed": "完了", "failed": "失敗", "pending": "保留中"}, "createdAt": {"label": "作成日時"}, "endpoint": {"label": "エンドポイント"}, "completionWindow": {"label": "完了ウィンドウ"}, "completionTime": {"label": "完了時間"}, "requestCounts": {"label": "リクエスト数", "info": "合計{{total}}件中、完了{{completed}}件、失敗{{failed}}件"}, "files": {"title": "ファイル", "input": {"label": "入力ファイル"}, "output": {"label": "出力ファイル"}, "error": {"label": "エラーファイル"}, "download": "{{file}}をダウンロード"}, "error": {"title": "バッチ読み込みエラー", "generic": "バッチ詳細の読み込みに失敗しました"}, "tooltip": {"timeConversion": "時間変換", "utc": "UTC", "local": "ローカル", "relative": "相対"}, "skeleton": {"ariaLabel": "バッチ詳細を読み込み中"}, "demo": {"title": "バッチ詳細カード", "description": "バッチプロセスに関する詳細情報を表示する総合的なカード", "showLoading": "読み込み状態を表示", "hideLoading": "読み込み状態を非表示", "sizes": "サイズバリエーション", "smallSize": "小サイズ", "mediumSize": "中サイズ", "largeSize": "大サイズ", "variants": "スタイルバリエーション", "primaryVariant": "プライマリバリアント", "secondaryVariant": "セカンダリバリアント", "outlineVariant": "アウトラインバリアント", "states": "コンポーネント状態", "disabledState": "無効状態", "errorState": "エラー状態", "customization": "カスタム設定", "noTimeline": "タイムラインなし", "customTimeline": "カスタムタイムライン", "fullWidth": "全幅", "languages": "言語バリエーション"}}, "rtlDemo": {"title": "RTLデモンストレーション", "currentLanguage": "現在の言語", "direction": "テキスト方向", "paragraph1": "このコンポーネントは、選択された言語に基づいてテキストの方向がどのように変化するかを示しています。日本語は左から右（LTR）の言語です。", "paragraph2": "下部のボタンがテキストの方向に対して相対的な位置を維持していることに注目してください。LTRモードでは「前へ」は左側にありますが、RTLモードでは右側に表示されます。", "previousButton": "前へ", "nextButton": "次へ", "formTitle": "フォーム要素デモ", "nameLabel": "名前", "namePlaceholder": "名前を入力してください", "emailLabel": "メールアドレス", "emailPlaceholder": "<EMAIL>", "subscribeLabel": "ニュースレターに登録する", "submitButton": "送信", "textAlignmentTitle": "テキスト配置デモ", "textAlignStart": "このテキストは先頭に揃えられています（LTRでは左、RTLでは右）。", "textAlignEnd": "このテキストは末尾に揃えられています（LTRでは右、RTLでは左）。", "textAlignCenter": "このテキストは方向に関係なく中央に配置されています。"}}