// components/Pagination/_fixtures_/pagination.fixtures.tsx
import { PaginationProps } from "../pagination";

export const paginationFixtures: Record<string, PaginationProps> = {
  // Numbered pagination fixtures (Variant 1)
  numberedBasic: {
    totalPages: 12,
    currentPage: 1,
    onPageChange: () => {},
    variant: "numbered",
  },
  
  numberedMiddlePage: {
    totalPages: 12,
    currentPage: 5,
    onPageChange: () => {},
    variant: "numbered",
  },
  
  numberedLastPage: {
    totalPages: 12,
    currentPage: 12,
    onPageChange: () => {},
    variant: "numbered",
  },
  
  numberedFewPages: {
    totalPages: 3,
    currentPage: 2,
    onPageChange: () => {},
    variant: "numbered",
  },
  
  numberedDisabled: {
    totalPages: 12,
    currentPage: 5,
    onPageChange: () => {},
    variant: "numbered",
    disabled: true,
  },
  
  numberedNoEllipsis: {
    totalPages: 12,
    currentPage: 5,
    onPageChange: () => {},
    variant: "numbered",
    showEllipsis: false,
  },
  
  // Simple pagination fixtures (Variant 2)
  simpleFirstPage: {
    totalPages: 24,
    currentPage: 1,
    onPageChange: () => {},
    variant: "simple",
  },
  
  simpleMiddlePage: {
    totalPages: 24,
    currentPage: 12,
    onPageChange: () => {},
    variant: "simple",
  },
  
  simpleLastPage: {
    totalPages: 24,
    currentPage: 24,
    onPageChange: () => {},
    variant: "simple",
  },
  
  simpleCustomText: {
    totalPages: 24,
    currentPage: 1,
    onPageChange: () => {},
    variant: "simple",
    pageText: "Page",
  },
  
  simpleDisabled: {
    totalPages: 24,
    currentPage: 12,
    onPageChange: () => {},
    variant: "simple",
    disabled: true,
  },
  
  // Single page (should not render)
  singlePage: {
    totalPages: 1,
    currentPage: 1,
    onPageChange: () => {},
  },
};