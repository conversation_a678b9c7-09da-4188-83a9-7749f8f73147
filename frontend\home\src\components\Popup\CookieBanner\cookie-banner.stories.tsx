// components/CookieBanner/CookieBanner.stories.tsx
import { Meta, StoryObj } from '@storybook/react';
import { userEvent, within, expect } from '@storybook/test';
import { CookieBanner, CookiePreferences } from './cookie-banner';

const meta: Meta<typeof CookieBanner> = {
  title: 'UI/Popup/CookieBanner',
  component: CookieBanner,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A customizable cookie consent banner with accessibility features.',
      },
    },
    a11y: {
      config: {
        rules: [
          {
            // All interactive elements should be focusable
            id: 'button-name',
            enabled: true,
          },
          {
            // Ensure proper contrast
            id: 'color-contrast',
            enabled: true,
          },
        ],
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    position: {
      control: 'radio',
      options: ['bottom', 'top'],
      description: 'Position of the banner on the screen',
    },
    title: {
      control: 'text',
      description: 'The title of the cookie banner',
    },
    description: {
      control: 'text',
      description: 'The description text for the cookie banner',
    },
    onAcceptAll: {
      action: 'acceptAll',
      description: 'Triggered when user accepts all cookies',
    },
    onDeclineAll: {
      action: 'declineAll',
      description: 'Triggered when user declines all non-essential cookies',
    },
    onSavePreferences: {
      action: 'savePreferences',
      description: 'Triggered when user saves custom preferences',
    },
    initialPreferences: {
      control: 'object',
      description: 'Initial cookie preferences',
    },
  },
  args: {
    position: 'bottom',
    title: 'เว็บไซต์นี้ใช้คุกกี้',
    description:
      'เราใช้คุกกี้เพื่อเพิ่มประสิทธิภาพ และประสบการณ์ที่ดีในการใช้งานเว็บไซต์เมื่อคุณกดยอมรับเราจะสามารถเลือกแสดงสิ่งที่น่าสนใจสำหรับคุณได้โดยเฉพาะ',
    initialPreferences: {
      necessary: true,
      analytics: false,
      marketing: false,
    },
  },
};

export default meta;
type Story = StoryObj<typeof CookieBanner>;

export const Default: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // Verify banner renders with title and description
    expect(canvas.getByText('Cookies & Privacy')).toBeInTheDocument();
    expect(
      canvas.getByText(/We use cookies to enhance your browsing experience/)
    ).toBeInTheDocument();
    
    // Verify action buttons
    expect(canvas.getByText('Decline All')).toBeInTheDocument();
    expect(canvas.getByText('Customize')).toBeInTheDocument();
    expect(canvas.getByText('Accept All')).toBeInTheDocument();
  },
};

export const TopPosition: Story = {
  args: {
    position: 'top',
  },
};

export const AcceptAllFlow: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // Find and click "Accept All" button
    const acceptButton = canvas.getByText('Accept All');
    await userEvent.click(acceptButton);
    
    // Verify onAcceptAll callback was called with correct preferences
    await expect(args.onAcceptAll).toHaveBeenCalledWith({
      necessary: true,
      analytics: true,
      marketing: true,
    });
  },
};

export const DeclineAllFlow: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // Find and click "Decline All" button
    const declineButton = canvas.getByText('Decline All');
    await userEvent.click(declineButton);
    
    // Verify onDeclineAll callback was called
    await expect(args.onDeclineAll).toHaveBeenCalled();
  },
};

export const CustomizeFlow: Story = {
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement);
    
    // Find and click "Customize" button
    const customizeButton = canvas.getByText('Customize');
    await userEvent.click(customizeButton);
    
    // Verify customize view appears
    expect(canvas.getByText('Cookie Preferences')).toBeInTheDocument();
    expect(canvas.getByText('Necessary Cookies')).toBeInTheDocument();
    expect(canvas.getByText('Analytics Cookies')).toBeInTheDocument();
    expect(canvas.getByText('Marketing Cookies')).toBeInTheDocument();
    
    // Toggle Analytics consent on
    const analyticsCheckbox = canvas.getByLabelText('Analytics Cookies');
    await userEvent.click(analyticsCheckbox);
    
    // Verify necessary cookies checkbox is disabled
    const necessaryCheckbox = canvas.getByLabelText('Necessary Cookies');
    expect(necessaryCheckbox).toBeDisabled();
    
    // Save preferences
    const saveButton = canvas.getByText('Save Preferences');
    await userEvent.click(saveButton);
    
    // Verify onSavePreferences callback was called with correct preferences
    await expect(args.onSavePreferences).toHaveBeenCalledWith({
      necessary: true,
      analytics: true,
      marketing: false,
    });
  },
};

export const CustomContent: Story = {
  args: {
    title: 'Our Cookie Policy',
    description:
      'This website uses cookies to improve user experience. Please choose your preferences below.',
  },
};

export const WithInitialPreferences: Story = {
  args: {
    initialPreferences: {
      necessary: true,
      analytics: true,
      marketing: false,
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Click customize to see preferences
    const customizeButton = canvas.getByText('Customize');
    await userEvent.click(customizeButton);
    
    // Verify analytics is pre-checked
    const analyticsCheckbox = canvas.getByLabelText('Analytics Cookies');
    expect(analyticsCheckbox).toBeChecked();
    
    // Verify marketing is not checked
    const marketingCheckbox = canvas.getByLabelText('Marketing Cookies');
    expect(marketingCheckbox).not.toBeChecked();
  },
};

// Accessibility focused story
export const Accessibility: Story = {
  parameters: {
    a11y: {
      // Test focused on accessibility
      disable: false,
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    
    // Test keyboard navigation
    await userEvent.tab(); // Focus first button (Decline All)
    expect(canvas.getByText('Decline All')).toHaveFocus();
    
    await userEvent.tab(); // Focus second button (Customize)
    expect(canvas.getByText('Customize')).toHaveFocus();
    
    await userEvent.tab(); // Focus third button (Accept All)
    expect(canvas.getByText('Accept All')).toHaveFocus();
    
    // Press enter on focused button (Accept All)
    await userEvent.keyboard('{Enter}');
    
    // Banner should now be closed with preferences set
  },
};