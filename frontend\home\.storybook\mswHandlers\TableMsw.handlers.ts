// @ts-ignore - Using any type for MSW to work around type errors
import { http, HttpResponse, delay } from 'msw';
import { mockUsers, mockValidationErrors, NETWORK_SPEEDS } from '../../src/components/tablemswtesting/__fixtures__/TableMsw.mockData';

export const TableMswHandlers = [
  // Debug logging handler for all requests
  // @ts-ignore - Using any type for MSW
  http.all('*', async ({ request }) => {
    console.log(`🔍 MSW DEBUG: Intercepted ${request.method} ${request.url}`);
    // Don't handle the request here, just log it
    return undefined;
  }),
  
  // GET all users
  // @ts-ignore - Using any type for MSW
  http.get('/api/users', async ({ request }) => {
    const url = new URL(request.url);
    const simulateParam = url.searchParams.get('simulate');
    
    // Optional delay to simulate network conditions
    await delay(NETWORK_SPEEDS.normal);
    
    // Simulation conditions
    if (simulateParam === 'error') {
      return new HttpResponse(
        JSON.stringify({ error: 'Internal server error' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (simulateParam === 'empty') {
      return HttpResponse.json({ data: [] });
    }
    
    if (simulateParam === 'slow') {
      await delay(NETWORK_SPEEDS.verySlow);
    }
    
    // Return all users
    return HttpResponse.json({
      data: Object.values(mockUsers),
      total: Object.values(mockUsers).length,
    });
  }),
  
  // GET a specific user by ID
  // @ts-ignore - Using any type for MSW
  http.get('/api/users/:id', async ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const simulateParam = url.searchParams.get('simulate');
    
    // Optional delay to simulate network conditions
    await delay(NETWORK_SPEEDS.normal);
    
    if (simulateParam === 'error') {
      return new HttpResponse(
        JSON.stringify({ error: 'Failed to fetch user' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (simulateParam === 'not-found') {
      return new HttpResponse(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    const user = mockUsers[id as string];
    
    if (!user) {
      return new HttpResponse(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    return HttpResponse.json({ data: user });
  }),
  
  // POST create new user
  // @ts-ignore - Using any type for MSW
  http.post('/api/users', async ({ request }) => {
    const data = await request.json();
    const url = new URL(request.url);
    const simulateParam = url.searchParams.get('simulate');
    
    // Optional delay
    await delay(NETWORK_SPEEDS.normal);
    
    // Simulation conditions
    if (simulateParam === 'validation-error') {
      return new HttpResponse(
        JSON.stringify({
          error: 'Validation failed',
          fields: mockValidationErrors
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (simulateParam === 'server-error') {
      return new HttpResponse(
        JSON.stringify({ error: 'Failed to create user' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Create a new user with a generated ID
    const newUser = {
      id: `user-${Date.now()}`,
      ...(typeof data === 'object' ? data : {}),
      createdAt: new Date().toISOString()
    };
    
    return new HttpResponse(
      JSON.stringify({ data: newUser }),
      { 
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }),
  
  // PUT update a user
  // @ts-ignore - Using any type for MSW
  http.put('/api/users/:id', async ({ params, request }) => {
    const { id } = params;
    const data = await request.json();
    const url = new URL(request.url);
    const simulateParam = url.searchParams.get('simulate');
    
    // Optional delay
    await delay(NETWORK_SPEEDS.normal);
    
    if (simulateParam === 'validation-error') {
      return new HttpResponse(
        JSON.stringify({
          error: 'Validation failed',
          fields: mockValidationErrors
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (simulateParam === 'not-found') {
      return new HttpResponse(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    const user = mockUsers[id as string];
    
    if (!user) {
      return new HttpResponse(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Update the user
    const updatedUser = {
      ...user,
      ...(typeof data === 'object' ? data : {}),
      id: user.id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };
    
    return HttpResponse.json({ data: updatedUser });
  }),
  
  // DELETE a user
  // @ts-ignore - Using any type for MSW
  http.delete('/api/users/:id', async ({ params, request }) => {
    const { id } = params;
    const url = new URL(request.url);
    const simulateParam = url.searchParams.get('simulate');
    
    // Optional delay
    await delay(NETWORK_SPEEDS.normal);
    
    if (simulateParam === 'server-error') {
      return new HttpResponse(
        JSON.stringify({ error: 'Failed to delete user' }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (simulateParam === 'not-found' || !mockUsers[id as string]) {
      return new HttpResponse(
        JSON.stringify({ error: 'User not found' }),
        { 
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    return new HttpResponse(null, { status: 204 });
  }),
]; 