import { ApiKeyFormValues } from './ApiKey-Schema';

/**
 * ApiKey interface representing an API key entity
 */
export interface ApiKey {
  /** Unique identifier for the API key */
  id: string;
  /** Optional human-readable name */
  name?: string;
  /** Type of permissions granted to this key */
  permissionType: 'All' | 'Restricted' | 'Read only';
  /** Resource-specific permissions if permissionType is 'Restricted' */
  resourcePermissions?: {
    models?: 'None' | 'Read' | 'Write';
    modelCapabilities?: 'None' | 'Read' | 'Write';
    assistants?: 'None' | 'Read' | 'Write';
    threads?: 'None' | 'Read' | 'Write';
    evals?: 'None' | 'Read' | 'Write';
    fineTuning?: 'None' | 'Read' | 'Write';
    files?: 'None' | 'Read' | 'Write';
    [key: string]: 'None' | 'Read' | 'Write' | undefined;
  };
  /** ISO timestamp when the key was created */
  createdAt: string;
  /** ISO timestamp when the key was last used */
  lastUsed?: string;
  /** Value of the API key (only visible once upon creation) */
  keyValue?: string;
  /** Status of the API key */
  status?: 'active' | 'revoked' | 'expired';
  /** ISO timestamp when the key expires */
  expiresAt?: string;
  /** Number of requests made with this key */
  requestCount?: number;
  /** Tags associated with this API key */
  tags?: string[];
  /** Environment where this key can be used */
  environment?: 'development' | 'staging' | 'production';
  /** Project ID this key is associated with */
  projectId?: string;
  /** User who created this key */
  createdBy?: string;
  /** IP restrictions for this key */
  ipRestrictions?: string[];
  /** Domain restrictions for this key */
  domainRestrictions?: string[];
  /** Maximum requests per minute allowed */
  rateLimit?: number;
  /** Audit log of key usage */
  usageLog?: {
    timestamp: string;
    ipAddress: string;
    endpoint: string;
    status: number;
  }[];
}

/**
 * Props for the CreateApiKey component
 */
export interface CreateApiKeyProps {
  /** Callback when form is submitted */
  onSubmit: (data: ApiKeyFormValues) => void;
  /** Callback when creation is cancelled */
  onCancel: () => void;
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** List of available projects */
  availableProjects: { value: string; label: string }[];
  /** Optional class name for styling */
  className?: string;
  /** Available environments for the key */
  availableEnvironments?: { value: string; label: string }[];
  /** Default expiration time in days */
  defaultExpirationDays?: number;
  /** Whether to show advanced options */
  showAdvancedOptions?: boolean;
  /** Whether to show tags input */
  showTags?: boolean;
  /** Maximum number of tags allowed */
  maxTags?: number;
  /** Whether to show IP restrictions */
  showIpRestrictions?: boolean;
  /** Whether to show domain restrictions */
  showDomainRestrictions?: boolean;
  /** Whether to show rate limiting options */
  showRateLimit?: boolean;
  /** Theme variant */
  variant?: 'default' | 'compact' | 'expanded';
  /** Whether to auto-focus the name field */
  autoFocusNameField?: boolean;
  /** Animation speed for transitions */
  animationSpeed?: 'slow' | 'normal' | 'fast';
  /** Custom validation function */
  validateForm?: (data: Partial<ApiKeyFormValues>) => Record<string, string> | null;
}

/**
 * Props for the EditApiKey component
 */
export interface EditApiKeyProps {
  /** API Key being edited */
  apiKey: ApiKey;
  /** Callback when form is submitted */
  onSubmit: (data: ApiKeyFormValues) => void;
  /** Callback when editing is cancelled */
  onCancel: () => void;
  /** Whether the form is currently submitting */
  isSubmitting?: boolean;
  /** Optional class name for styling */
  className?: string;
  /** Available environments for the key */
  availableEnvironments?: { value: string; label: string }[];
  /** Whether to show advanced options */
  showAdvancedOptions?: boolean;
  /** Whether to show tags input */
  showTags?: boolean;
  /** Maximum number of tags allowed */
  maxTags?: number;
  /** Whether to show IP restrictions */
  showIpRestrictions?: boolean;
  /** Whether to show domain restrictions */
  showDomainRestrictions?: boolean;
  /** Whether to show rate limiting options */
  showRateLimit?: boolean;
  /** Theme variant */
  variant?: 'default' | 'compact' | 'expanded';
  /** Animation speed for transitions */
  animationSpeed?: 'slow' | 'normal' | 'fast';
  /** Whether to show usage log */
  showUsageLog?: boolean;
  /** Whether key can be revoked */
  allowRevoke?: boolean;
  /** Whether key expiration can be extended */
  allowExtendExpiration?: boolean;
  /** Custom validation function */
  validateForm?: (data: Partial<ApiKeyFormValues>) => Record<string, string> | null;
}
