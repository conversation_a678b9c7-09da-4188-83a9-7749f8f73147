'use client';

import * as React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { expect, userEvent, within } from '@storybook/test';
import TableDynamic from '../table-dynamic';
import { simpleData, simpleColumns } from '../__fixtures__/TableDynamic.fixtures';
import type { ColumnConfig } from '../types';

// Enhanced simple columns for stories
const getEnhancedSimpleColumns = (): ColumnConfig[] => {
  return simpleColumns.map((col) => {
    if (col.id === 'price') {
      return {
        ...col,
        cell: ({ row }) => (
          <div className="text-right" aria-label={`Price: $${row.getValue('price')}`}>
            ${row.getValue('price')}
          </div>
        ),
      } as ColumnConfig;
    }
    if (col.id === 'name') {
      return {
        ...col,
        cell: ({ row }) => <div className="font-medium">{row.getValue('name')}</div>,
      } as ColumnConfig;
    }
    return col as ColumnConfig;
  });
};

const meta = {
  title: 'Components/TableDynamic/Accessibility',
  component: TableDynamic,
  parameters: {
    a11y: {
      config: {
        rules: [
          { id: 'color-contrast', enabled: true },
          { id: 'heading-order', enabled: true },
          { id: 'label', enabled: true },
        ],
      },
    },
    docs: {
      description: {
        story: 'Demonstration of accessibility features in the TableDynamic component.',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="max-w-full p-4">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof TableDynamic>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Basic accessible table that passes WCAG 2.1 AA requirements.
 */
export const Accessible: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    // Check that the table has appropriate semantic structure
    const table = canvas.getByRole('table');
    await expect(table).toBeInTheDocument();

    // Should have table headers for column names
    const headers = canvas.getAllByRole('columnheader');
    await expect(headers.length).toBeGreaterThan(0);

    // Should have rows
    const rows = canvas.getAllByRole('row');
    await expect(rows.length).toBeGreaterThan(1); // Header + data rows
  },
};

/**
 * Demonstrates keyboard navigation through the table.
 */
export const KeyboardNavigation: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    onRowClick: () => {}, // Add a handler to make rows clickable
  },
  play: async () => {
    // Start by focusing an element in the canvas
    await userEvent.tab();

    // Tab through to reach the table
    await userEvent.tab();

    // Tab to reach the first row (after header)
    await userEvent.tab();

    // Press space to "click" the current row
    await userEvent.keyboard(' ');

    // Tab and keyboard navigation can continue from here
  },
};

/**
 * Table with high contrast that ensures readability.
 */
export const HighContrast: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
  },
  parameters: {
    backgrounds: {
      default: 'dark',
    },
  },
};

/**
 * Table with ARIA labels and screen reader-friendly content.
 */
// Create a component to use for the story
const ScreenReaderTable = () => (
  <div>
    <h2 id="product-table-title">Product Inventory</h2>
    <p id="product-table-description">
      This table shows the current inventory of products with their names, categories, prices, and
      stock levels.
    </p>
    <TableDynamic
      data={simpleData}
      columns={getEnhancedSimpleColumns()}
      variant="primary"
      size="md"
      aria-labelledby="product-table-title"
      aria-describedby="product-table-description"
    />
  </div>
);

export const ScreenReaderFriendly: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
  },
  parameters: {
    docs: {
      story: {
        inline: false,
      },
    },
  },
  render: () => <ScreenReaderTable />,
};

/**
 * Table with reduced motion for users who prefer minimal animations.
 */
export const ReducedMotion: Story = {
  args: {
    data: simpleData,
    columns: getEnhancedSimpleColumns(),
    variant: 'primary',
    size: 'md',
    animate: false, // Disable animations for users who prefer reduced motion
  },
};

/**
 * Table with mobile accessibility considerations.
 */
export const MobileAccessible: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile',
    },
  },
  args: {
    data: simpleData.slice(0, 3), // Fewer items for better mobile experience
    columns: getEnhancedSimpleColumns().slice(0, 2), // Fewer columns for mobile
    variant: 'primary',
    size: 'sm', // Smaller size for mobile
  },
};
