import{r as n}from"./index-Bwql5Dzz.js";var _=Object.defineProperty,l=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,o=(r,t,e)=>t in r?_(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,v=(r,t)=>{for(var e in t)i.call(t,e)&&o(r,e,t[e]);if(l)for(var e of l(t))f.call(t,e)&&o(r,e,t[e]);return r},m=(r,t)=>{var e={};for(var a in r)i.call(r,a)&&t.indexOf(a)<0&&(e[a]=r[a]);if(r!=null&&l)for(var a of l(r))t.indexOf(a)<0&&f.call(r,a)&&(e[a]=r[a]);return e};const s=n.forwardRef((r,t)=>{var e=r,{color:a="currentColor"}=e,p=m(e,["color"]);return n.createElement("svg",v({xmlns:"http://www.w3.org/2000/svg",width:15,height:15,fill:"none",ref:t},p),n.createElement("path",{fill:a,d:"M10 10.09c0 .163-.037.323-.108.463a.85.85 0 0 1-.293.335.7.7 0 0 1-.397.111.7.7 0 0 1-.39-.141l-3.454-2.59a.9.9 0 0 1-.263-.33 1.04 1.04 0 0 1 0-.876.9.9 0 0 1 .263-.33l3.455-2.59A.7.7 0 0 1 9.203 4a.7.7 0 0 1 .396.112.85.85 0 0 1 .293.335c.07.14.108.3.108.463z"}))});s.displayName="TriangleLeftMini";export{s as T};
